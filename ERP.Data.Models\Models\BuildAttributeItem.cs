﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class BuildAttributeItem
{
    public int Id { get; set; }

    public int BuildOptionId { get; set; }

    public int? OpAttrGroupItemId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public string? JobNumber { get; set; }

    public int? PlanOptionId { get; set; }

    public bool? Sold { get; set; }

    public int? AttrGroupAssignmentId { get; set; }

    public virtual AttrGroupAssignment? AttrGroupAssignment { get; set; }

    public virtual TbBuiltOption BuildOption { get; set; } = null!;

    public virtual OptionAttributeGroupItem? OpAttrGroupItem { get; set; }
}
