﻿@page "/schedulecondensed"
@inject SubdivisionService SubdivisionService
@inject ScheduleService ScheduleService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject PoService PoService
@inject BudgetService BudgetService
@implements IDisposable
@using ERP.Data.Models.Dto
@using ERP.Data.Models
@using ERP.Data.Models.ExtensionMethods
@using Telerik.DataSource.Extensions
@using System.Collections.ObjectModel
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector, ConstructionManager")]
<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }
    /*.mytreeclass .k-drag-cell {
            display:none;
        }*/
/*    .mytreeclass .k-table-th.k-header.k-drag-cell{
        visibility: collapse;
    }
    .mytreeclass .k-drag-cell.k-touch-action-none{
        visibility: collapse;

    }*/
    .mytreeclass .k-drag-col{
        visibility: collapse;
    }
    .mytreeclass .k-header {
        min-width: 200px;
    }

    .mytreeclass  {
       overflow-x: auto;
    }
    .k-drag-clue {
        color: white;
    }

    .k-icon.k-icon-with-modifier {
        display: none;
    }

    .SubdivisionTable .k-hierarchy-cell {
        display: none;
    }
    
    .k-splitbar {
        width: 15px;
        color: black;
        padding-left:5px;
        padding-right:5px;
    }

    .pane {
        padding: 8px;
        overflow-x: auto;
    }

    .k-splitter .k-pane {
        overflow-x: auto !important;
    }

    .k-splitter-horizontal{
        overflow-x: auto !important;
    }
    .page-wrapper .page-content{     
       /* padding-right: 100px !important;
        margin-right: 100px !important;*/ 
/*        width: 95% !important;*/
    }
    .page-wrapper{
       /* padding-right: 100px !important;
        margin-right: 100px !important;
            width: 80% !important;*/
    }
    .k-table-td{
        padding: 4px !important;
        font-size: 11px;
    }

    .k-table-td { /*these settings are to prevent the 0 width columns from affecting the column height*/
        padding: 4px !important;
        font-size: 11px;
        height: 30px !important;
        white-space: nowrap !important; 
        overflow: visible;
    }

    .important-appointment {
        background-color: red;
        color: white;
        font-weight: 900;
    }

    .actual-style {
        background-color: #a7e6c0;
        color: black;
      
    }

    .scheduled-style {
        background-color: #f4cd64;
        color: black;
        
    }

    .baseline-style {
        background-color: #2e5771;
        color: white;
       
    }
    .all-day-style {
        background-color: blue;
        color: white;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
    .k-dialog-wrapper{
        z-index: 50000 !important;
    }
    .k-button-solid-base.k-selected {
        border-color: #f4cd64;
        color: black;
        background-color: #f4cd64;
    }

    .k-panelbar > .k-item > .k-link.k-selected, .k-panelbar > .k-panelbar-header > .k-link.k-selected {
        color: black;
        background-color: #f4cd64;
    }

    .k-panelbar > .k-item > .k-link, .k-panelbar > .k-panelbar-header > .k-link {
        color: black;
    }

        .k-panelbar > .k-panelbar-header > .k-link.k-selected:hover{
            background-color: #f4cd64;
        }



</style>
<TelerikMediaQuery Media="@SmallScreenMediaQuery" OnChange="@((doesMatch) => IsSmallScreen = doesMatch)"></TelerikMediaQuery>
<TelerikMediaQuery Media="@LargeScreenMediaQuery" OnChange="@((doesMatch) => IsLargeScreen = doesMatch)"></TelerikMediaQuery>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="false" ShowJob="true" ShowSubdivision="true"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
   <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Schedule</h7>
                </div>
            </div>
<TelerikTooltip TargetSelector=".tooltip-target" />
@if (IsLargeScreen)
{
    <div class="row">
        @if (JobSelected == null)
        {
            
            <p><em>Select a job to see schedule details</em></p>
            <div style="width:200px">
                <TelerikButton OnClick="@NewScheduleFromTemplate" Title="New schedule from template" Icon="@FontIcon.Plus" Class=" tooltip-target k-button-add">Create schedule</TelerikButton>
            </div>
        }
        else
        {
            @if (IsLoading)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />
            }
            else
            {
                <TelerikTabStrip>
                    <TabStripTab Title="Schedule">
                        <div>
                            <TelerikTreeList Data="@SelectedScheduleData"
                                             SelectionMode="@TreeListSelectionMode.Multiple"
                                             OnStateInit="@((TreeListStateEventArgs<ScheduleTemplateTreeModel> args) => OnStateInitHandler(args))"
                                             IdField="Id"
                                             Id="WorksheetTree"
                                             Class="mytreeclass"
                                             ItemsField="Children"
                                             Resizable="true"
                                             Width="100%"
                                             Height="1000px"
                                             OnExpand="@OnExpand"
                                             OnUpdate="@UpdateItem"
                                             OnEdit="@EditItem"
                                             EditMode="@TreeListEditMode.Incell"
                                             @ref="@ScheduleTreeList">
                                <TreeListColumns>
                                    <TreeListColumn Field="Id" Title="Milestone/Activity" Width="300px" Editable="false" Expandable="true" Locked="true">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.FolderName != null)
                                                {
                                                    @($"{item.FolderName}")
                                                }
                                                else if (item != null && item.Sactivity != null)
                                                {
                                                    <TelerikButton Title="View/Edit" OnClick="() => EditActivity(item.ScheduleSactivity)" Class="k-button-success">Edit</TelerikButton>
                                                    @($" - {item.Sactivity.ActivityName}")

                                                }
                                                else if (item != null && item.Milestone != null)
                                                {
                                                    @($"{item.Milestone.MilestoneName}")
                                                }

                                            }
                                        </Template>
                                    </TreeListColumn>
                                    @*  <TreeListColumn Field="ScheduleSactivity.Complete" Title="Complete" Width="50px" /> *@
                                    <TreeListColumn Field="ScheduleSactivity.BoolStarted" Title="Started" Visible="true" Editable="false" Width="50px">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null && item.ScheduleSactivity != null)
                                                {
                                                    <TelerikCheckBox @bind-Value="item.ScheduleSactivity.BoolStarted" OnChange="()=> UpdateBoolStarted(item)"></TelerikCheckBox>
                                                }

                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null && ItemToEdit.ScheduleSactivity != null)
                                                {
                                                    <TelerikCheckBox @bind-Value="ItemToEdit.ScheduleSactivity.BoolStarted"></TelerikCheckBox>
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.BoolComplete" Title="Complete" Visible="true" Editable="false" Width="50px">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null && item.ScheduleSactivity != null)
                                                {
                                                    <TelerikCheckBox @bind-Value="item.ScheduleSactivity.BoolComplete" OnChange="()=>UpdateBoolCompleted(item)"></TelerikCheckBox>
                                                }

                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivity != null && ItemToEdit.ScheduleSactivity != null)
                                                {
                                                    <TelerikCheckBox @bind-Value="ItemToEdit.ScheduleSactivity.BoolComplete"></TelerikCheckBox>
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>

                                    <TreeListColumn Field="ScheduleSactivity.BoolIgnoreNoWorkDays" Title="Ignore No Work Days" Width="50px">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null && item.ScheduleSactivity != null)
                                                {
                                                    <TelerikCheckBox @bind-Value="item.ScheduleSactivity.BoolIgnoreNoWorkDays"></TelerikCheckBox>
                                                }

                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null && ItemToEdit.ScheduleSactivity != null)
                                                {
                                                    <TelerikCheckBox @bind-Value="ItemToEdit.ScheduleSactivity.BoolIgnoreNoWorkDays"></TelerikCheckBox>
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.VarianceCode" Title="Variance Code" Width="100px">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    <span>@item.ScheduleSactivity.VarianceCode</span>
                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivity != null)
                                                {
                                                    <TelerikDropDownList Data="@VarianceCodes"
                                                                         TextField="VarianceDesc"
                                                                         ValueField="VarianceCode"
                                                                         @bind-Value="ItemToEdit.ScheduleSactivity.VarianceCode">
                                                    </TelerikDropDownList>
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="Predecessors" Title="Predecessor" Width="100px">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivity != null)
                                                {
                                                    <span>@item.Predecessors</span>
                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                var priorActivities = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivityId != null && x.ScheduleSactivity.Seq < ItemToEdit.ScheduleSactivity.Seq)).Select(x => x.Sactivity);
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null)
                                                {
                                                    <TelerikMultiSelect Data="@priorActivities"
                                                                        TextField="ActivityName"
                                                                        ValueField="SactivityId"
                                                                        Filterable="true"
                                                                        FilterOperator="StringFilterOperator.Contains"
                                                                        @bind-Value="ItemToEdit.PredecessorIds">
                                                    </TelerikMultiSelect>
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.SubNumberNavigation.SubName" Title="Supplier" Width="50px">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivity != null)
                                                {
                                                    <span>@item.ScheduleSactivity.SubNumberNavigation?.SubName</span>
                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivity != null && ItemToEdit.SupplierEditable == true)
                                                {
                                                    <TelerikDropDownList Data="@AllSuppliers"
                                                                         TextField="SubName"
                                                                         ValueField="SubNumber"
                                                                         FilterOperator="@StringFilterOperator.Contains"
                                                                         Filterable="true"
                                                                         @bind-Value="ItemToEdit.ScheduleSactivity.SubNumber">
                                                    </TelerikDropDownList>
                                                }
                                                else
                                                {
                                                    <span>@ItemToEdit?.ScheduleSactivity?.SubNumberNavigation?.SubName</span>
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.Duration" Title="Duration" Width="60px">
                                        <Template>
                                            @{
                                                //TODO: probably should not be editable if there is an actual start and end
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{item.ScheduleSactivity.Duration}")
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {
                                                    @($"{item.ScheduleMilestone.Duration}")
                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null && isScheduledEndEditable == true)
                                                {
                                                    <TelerikNumericTextBox @bind-Value="@ItemToEdit.ScheduleSactivity.Duration"></TelerikNumericTextBox>
                                                }
                                                else
                                                {
                                                    if (ItemToEdit.ScheduleSactivity != null)
                                                    {
                                                        <span>@($"{ItemToEdit?.ScheduleSactivity.Duration}")</span>
                                                    }
                                                    else if (ItemToEdit.ScheduleMilestone != null)
                                                    {
                                                        <span>@($"{ItemToEdit?.ScheduleMilestone.Duration}")</span>
                                                    }
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.LagTime" Title="Lag Time" Width="50px">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{item.ScheduleSactivity.LagTime}")
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {

                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null)
                                                {
                                                    <TelerikNumericTextBox @bind-Value="@ItemToEdit.ScheduleSactivity.LagTime"></TelerikNumericTextBox>
                                                }
                                                else
                                                {
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.PlusminusDays" Title="Plus Minus" Editable="false" Width="50px">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{item.ScheduleSactivity.PlusminusDays}")
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {
                                                    @($"{item.ScheduleMilestone.PlusminusDays}")
                                                }
                                            }
                                        </Template>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.SchStartDate" DisplayFormat="{0:MM/dd/yyyy}" Title="Scheduled Start" Width="80px">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{item.ScheduleSactivity.SchStartDate:MM/dd/yyy}")
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {
                                                    @($"{item.ScheduleMilestone.SchStartDate:MM/dd/yyy}")
                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null && isScheduledStartEditable == true && ItemToEdit.ScheduleSactivity.VarianceCode != null)
                                                {

                                                    <TelerikDatePicker @bind-Value="@ItemToEdit.ScheduleSactivity.SchStartDate"></TelerikDatePicker>
                                                }
                                                else
                                                {

                                                    if (ItemToEdit.ScheduleSactivityId != null)
                                                    {
                                                        if (ItemToEdit.ScheduleSactivity.ActualStartDate != null)
                                                        {
                                                            <span style="color:red">Already started</span>
                                                        }
                                                        else if (ItemToEdit.ScheduleSactivity.VarianceCode == null)
                                                        {
                                                            <span style="color:red">Variance Required</span>
                                                        }
                                                        
                                                        <span>@($"{ItemToEdit?.ScheduleSactivity.SchStartDate:MM/dd/yyy}")</span>
                                                    }
                                                    else if (ItemToEdit.ScheduleMilestone != null)
                                                    {
                                                        <span>@($"{ItemToEdit?.ScheduleMilestone.SchStartDate:MM/dd/yyy}")</span>
                                                    }
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.SchEndDate" DisplayFormat="{0:MM/dd/yyyy}" Title="Scheduled Finish" Width="80px">
                                        <Template>
                                            @{
                                                //TODO: scheduled dates should be locked if there is an actual date, and if there is no variance code
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{item.ScheduleSactivity.SchEndDate:MM/dd/yyy}")
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {
                                                    @($"{item.ScheduleMilestone.SchEndDate:MM/dd/yyy}")
                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null && isScheduledEndEditable == true && ItemToEdit.ScheduleSactivity.VarianceCode != null)
                                                {
                                                    <TelerikDatePicker @bind-Value="@ItemToEdit.ScheduleSactivity.SchEndDate"></TelerikDatePicker>
                                                }
                                                else
                                                {
                                                    if (ItemToEdit.ScheduleSactivityId != null)
                                                    {
                                                        if (ItemToEdit.ScheduleSactivity.ActualEndDate != null)
                                                        {
                                                            <span style="color:red">Already completed</span>
                                                        }
                                                        if (ItemToEdit.ScheduleSactivity.VarianceCode == null)
                                                        {
                                                            <span style="color:red">Variance Required</span>
                                                        }
                                                        <span>@($"{ItemToEdit?.ScheduleSactivity.SchEndDate:MM/dd/yyy}")</span>
                                                    }
                                                    else if (ItemToEdit.ScheduleMilestone != null)
                                                    {
                                                        <span>@($"{ItemToEdit?.ScheduleMilestone.SchEndDate:MM/dd/yyy}")</span>
                                                    }
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                </TreeListColumns>
                                <TreeListToolBarTemplate>
                                    <TreeListSearchBox DebounceDelay="200"></TreeListSearchBox>
                                    <TreeListCommandButton Command="Save" OnClick="@Save" Title="Save" Icon="@FontIcon.Save" Class=" tooltip-target k-button-success"></TreeListCommandButton>
                                    <TreeListCommandButton Command="Cancel" OnClick="@CancelChanges" Title="Cancel Changes" Icon="@FontIcon.Cancel" Class=" tooltip-target k-button-danger"></TreeListCommandButton>
                                    @if (userIsDirectorOrAdmin)
                                    {
                                        <TreeListCommandButton Command="CreateSchedule" OnClick="@NewScheduleFromTemplate" Title="New schedule from template" Icon="@FontIcon.Plus" Class=" tooltip-target k-button-add"></TreeListCommandButton>
                                        <TreeListCommandButton Command="UpdateFromTemplate" OnClick="@UpdateScheduleFromTemplate" Title="Update schedule from template" Icon="@FontIcon.Redo" Class=" tooltip-target k-button-add"></TreeListCommandButton>
                                    }

                                    @if (SelectedSchedule != null)
                                    {
                                        <label for="publishCheckbox">Publish Schedule</label>
                                        <TelerikCheckBox Id="publishCheckbox" @bind-Value="@SelectedSchedule.BoolPublished" OnChange="@UpdatePublishSchedule"></TelerikCheckBox>
                                        if (SelectedSchedule.IniSchApproved == true)
                                        {
                                            <span><strong>Release Date: &nbsp;</strong> @SelectedSchedule.IniSchStartDate?.ToString("MM/dd/yyyy")</span>
                                        }
                                        else
                                        {
                                            <span style="color:red"><strong>Not Released</strong></span>
                                        }
                                        <span> | </span>

                                        <br />
                                        <span><strong>Template: &nbsp;</strong> @SelectedSchedule.Template?.TemplateName</span>
                                        <span><strong>Base Start: &nbsp;</strong> @SelectedSchedule.BaseStartDate?.ToString("MM/dd/yyyy")</span>
                                        <span><strong>Base End: &nbsp;</strong> @SelectedSchedule.BaseEndDate?.ToString("MM/dd/yyyy")</span>
                                        <span><strong>Sch Start: &nbsp;</strong> @SelectedSchedule.DateToStart?.ToString("MM/dd/yyyy")</span>
                                        <span><strong>Sch End: &nbsp;</strong> @SelectedSchedule.DateToEnd?.ToString("MM/dd/yyyy")</span>
                                    }
                                    <TelerikToggleButton Class="tooltip-target" Title="Filter My Schedules" @bind-Selected="@FilterSchedules" OnClick="ToggleFilter">All Activities/Incomplete Only: <strong>@ToggleText</strong></TelerikToggleButton>
                                </TreeListToolBarTemplate>

                            </TelerikTreeList>
                        </div>
                    </TabStripTab>
                    <TabStripTab Title="CalendarView">
                        <div>
                            <div>
                                <TelerikCheckBox @bind-Value="@ShowScheduled" Id="scheduledCheckBox" OnChange="@FilterAppointments" />
                                <label for="scheduledCheckBox">Scheduled</label>
                                <TelerikCheckBox @bind-Value="@ShowActual" Id="actualCheckBox" OnChange="@FilterAppointments" />
                                <label for="actualCheckBox">Actual</label>
                                <TelerikCheckBox @bind-Value="@ShowBaseline" Id="baselineCheckBox" OnChange="@FilterAppointments" />
                                <label for="baselineCheckBox">Baseline</label>
                            </div>
                        </div>
                        <TelerikScheduler Data="@SelectedCalendarData"
                                          @bind-Date="@StartDate"
                                          OnItemRender="@OnItemRenderHandler"
                                          Height="600px"
                                          StartField="StartDate"
                                          EndField="EndDate"
                                          IsAllDayField="IsAllDay"
                                          TitleField="@(nameof(ScheduleSactivityDto.ActivityName))"
                                          DescriptionField="@(nameof(ScheduleSactivityDto.ActivityName))"
                                          @bind-View="@CurrView">
                            <SchedulerViews>
                                <SchedulerDayView StartTime="@DayStart" />
                                <SchedulerWeekView StartTime="@DayStart" />
                                <SchedulerMultiDayView StartTime="@DayStart" NumberOfDays="10" />
                                <SchedulerMonthView />
                            </SchedulerViews>
                        </TelerikScheduler>

                    </TabStripTab>
                </TelerikTabStrip>
            }
            
        }
        <br />
        <br />
    </div>
}
else
{
    @if(Milestones != null)
    {
        <TelerikPanelBar Data="Milestones">
            <PanelBarBindings>
                <PanelBarBinding>
                    <HeaderTemplate>
                        @{
                            var item = context as ScheduleMilestoneDto;
                            <div class="row" style="display: flex; align-items: center; justify-content: center; width:100%">
                                <div class="col-1" style="align-items: center; justify-content: center;">
                                    @if (item.ActualEndDate != null)
                                    {
                                        <div style="display: inline-block; margin-right: 10px; background-color: green; width: 25px; height: 25px; border-radius: 50%;"></div>
                                    }
                                    else if (DateTime.Today > item.SchEndDate)
                                    {
                                        <div style="display: inline-block; margin-right: 10px; background-color: red; width: 25px; height: 25px; border-radius: 50%;"></div>
                                    }
                                    else
                                    {
                                        <div style="display: inline-block; margin-right: 10px; background-color: yellow; width: 25px; height: 25px; border-radius: 50%;"></div>
                                    }
                                    
                                </div>
                                <div class="col" style="text-align:center; width:100%"><h2>@item.Milestone.MilestoneName</h2></div>
                            </div>

                        }
                    </HeaderTemplate>
                    <ContentTemplate>
                        @{
                            var item = context as ScheduleMilestoneDto;
                            <div class="row k-card-body justify-space-between">
                                <div class="col-md-4 col-4">
                                    <h6>Base Start</h6>
                                    <p style="font-size:14px">@(item.BaseStartDate?.ToString("MM/dd/yyyy") ?? "-")</p>
                                    <h6>Base End</h6>
                                    <p style="font-size:14px">@(item.BaseEndDate?.ToString("MM/dd/yyyy") ?? "-")</p>
                                </div>
                                <div class="col-md-4 col-4">
                                    <h6>Sch. Start</h6>
                                    <p style="font-size:14px">@(item.SchStartDate?.ToString("MM/dd/yyyy") ?? "-")</p>
                                    <h6>Sch. End</h6>
                                    <p style="font-size:14px">@(item.SchEndDate?.ToString("MM/dd/yyyy") ?? "-")</p>
                                </div>
                                <div class="col-md-4 col-4">
                                    <h6>Actual Start</h6>
                                    <p style="font-size:14px">@(item.ActualStartDate?.ToString("MM/dd/yyyy") ?? "-")</p>
                                    <h6>Actual End</h6>
                                    <p style="font-size:14px">@(item.ActualEndDate?.ToString("MM/dd/yyyy") ?? "-")</p>
                                </div>
                            </div>
                            <CardActions Class="justify-space-between">
                            <div class="row" style="width:100%">
                                <div class="col-8" style="text-align:left; font-size:12px">
                                   Created: @item.CreatedDateTime.ToString("MM/dd/yyyy") / Updated: @item.UpdatedDateTime?.ToString("MM/dd/yyyy") / Updated By: @item.UpdatedBy
                                </div>
                                <div class="col-4" style="text-align:right; padding-right:10px">
                                    <TelerikButton Class="k-button-success mr-2" @onclick="() => NavigateToActivity(item.ScheduleMid)">View / Edit Activity</TelerikButton>
                                </div>
                                </div>
                            </CardActions>
                        }
                    </ContentTemplate>
                </PanelBarBinding>
            </PanelBarBindings>
        </TelerikPanelBar>
    }   
}
<NavigationLock ConfirmExternalNavigation="@UnsavedChanges" OnBeforeInternalNavigation="BeforeInternalNavigation" />
<ERP.Web.Components.EditScheduleActivity @ref="EditScheduleActivity" SelectedActivity="@SelectedActivity" HandleAddSubmit="@HandleValidEditActivitySubmit"></ERP.Web.Components.EditScheduleActivity>
<ERP.Web.Components.CreateScheduleFromTemplate @ref="AddScheduleFromTemplateModal" SelectedTemplate="@SelectedTemplate.TemplateId" HandleAddSubmit="@HandleValidAddScheduleFromTemplateSubmit"></ERP.Web.Components.CreateScheduleFromTemplate> 
@code {

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    private string? SelectedJob { get; set; }
    private string? JobSelected { get; set; }
    public ScheduleSactivityDto? SelectedActivity { get; set; }
    private TelerikTreeList<ScheduleTemplateTreeModel>? ScheduleTreeList { get; set; }
    public ObservableCollection<ScheduleTemplateTreeModel>? SelectedScheduleData { get; set; } 
    public List<ScheduleTemplateTreeModel>? AllSelectedScheduleData { get; set; }
    public List<ScheduleTemplateTreeModel>? UnfilteredListSelectedScheduleData { get; set; }
    public ScheduleDto? SelectedSchedule { get; set; }
    public TemplateDto? SelectedTemplate { get; set; } = new TemplateDto();
    public int? PreviousSelectedTemplateId { get; set; }
    protected ERP.Web.Components.CreateTemplate? AddTemplateModal { get; set; }
    public ScheduleTemplateTreeModel? ItemToEdit { get; set; }
    protected ERP.Web.Components.CreateScheduleFromTemplate? AddScheduleFromTemplateModal { get; set; }
    protected ERP.Web.Components.EditScheduleActivity? EditScheduleActivity { get; set; }
    public List<VarianceDto>? VarianceCodes { get; set; }
    public List<SupplierDto>? AllSuppliers { get; set; }
    public bool PublishSchedule { get; set; } = false;
    private bool isScheduledStartEditable { get; set; } = true;
    private bool isScheduledEndEditable { get; set; } = true;
    private bool isSupplierEditable { get; set; } = true;
    private bool userIsDirectorOrAdmin { get; set; } = false;
    private bool IsSmallScreen { get; set; }
    private bool IsLargeScreen { get; set; }
    private string SmallScreenMediaQuery { get; set; } = "(max-width: 767px)";
    private string LargeScreenMediaQuery { get; set; } = "(min-width: 1199px)";
    private bool UnsavedChanges { get; set; } = false;


    private string ToggleText { get; set; } = "Incomplete Only";
    private bool FilterSchedules { get; set; } = false;

    //for mobile
    public List<ScheduleMilestoneDto>? Milestones { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    //calendar
    public List<ScheduleSactivityDto>? SelectedScheduleCalendarData { get; set; }
    public List<ScheduleSactivitySchedulerModel>? SelectedCalendarData { get; set; }
    public SchedulerView CurrView { get; set; } = SchedulerView.Month;
    public List<CalendarsDayDto>? Holidays { get; set; }
    public DateTime StartDate { get; set; } = DateTime.Now;
    public DateTime DayStart { get; set; } = new DateTime(2000, 1, 1, 8, 0, 0); //the time portion is important

    public bool ShowScheduled { get; set; } = true;
    public bool ShowActual { get; set; } = false;
    public bool ShowBaseline { get; set; } = false;
    public GanttView SelectedView { get; set; } = GanttView.Week;

    public bool IsLoading { get; set; }


    private async Task BeforeInternalNavigation(LocationChangingContext context)
    {
        if (UnsavedChanges)
        {
            var proceed = await Dialogs.ConfirmAsync("You have unsaved changes, are you sure you want to leave this page?");

            if (!proceed)
            {
                context.PreventNavigation();
            }            
        }        
    }

    protected override async Task OnInitializedAsync()
    {


        SubdivisionJobPickService.OnChanged += JobChangedHandler;
        var suppliersTask = PoService.GetSuppliersAsync();
        var varianceCodesTask =  ScheduleService.GetVarianceCodesAsync();
        await Task.WhenAll(new Task[] { suppliersTask, varianceCodesTask });
        AllSuppliers = suppliersTask.Result.Value;
        VarianceCodes = varianceCodesTask.Result.Value;
        if (SubdivisionJobPickService.JobNumber != null)
        {
            IsLoading = true;//Load Spinner
            JobSelected = SubdivisionJobPickService.JobNumber;
            StateHasChanged();
            var getScheduleTask = ScheduleService.GetScheduleAsync(JobSelected);
            var getprescheduleTask = ScheduleService.GetPreScheduleAsync(JobSelected);
            var getMilestonesTask = ScheduleService.GetMilestonesForScheduleAsync(JobSelected);
            await Task.WhenAll(new Task[] { getMilestonesTask, getScheduleTask, getprescheduleTask });
            AllSelectedScheduleData = getScheduleTask.Result.Value;
            SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(AllSelectedScheduleData);
            SelectedScheduleCalendarData = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();
            FilterAppointments();
            PublishSchedule = AllSelectedScheduleData.Any() ? AllSelectedScheduleData.First().SchedulePublished : false;
            SelectedSchedule = getprescheduleTask.Result.Value;
            Milestones = getMilestonesTask.Result.Value;
            foreach (var milestone in SelectedScheduleData)
            {               
                milestone.CompletedDate = milestone.ScheduleMilestone.ActualEndDate;
                foreach(var activity in milestone.Children)
                {
                    activity.CompletedDate = activity.ScheduleSactivity.ActualEndDate;
                }
            }

            StateHasChanged();
            IsLoading = false; // Hide loading
        }

        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userroleConstructionDirector = user.User.IsInRole("ConstructionDirector");
        userIsDirectorOrAdmin = userRoleAdmin || userroleConstructionDirector;
    }
    async Task ToggleFilter()
    {
        if (FilterSchedules == true)
        {
            ToggleText = "Incomplete only";

            var filteredState = new TreeListState<ScheduleTemplateTreeModel>()
                {
                    FilterDescriptors = new List<IFilterDescriptor>()
            {
                new CompositeFilterDescriptor(){
                    FilterDescriptors = new FilterDescriptorCollection()
                    {                     
                        new FilterDescriptor()
                        {
                            Member = nameof(ScheduleTemplateTreeModel.CompletedDate),
                            MemberType = typeof(DateTime?),
                            Operator = FilterOperator.IsNull,
                           // Value = "Release"
                        },                        
                    }
                },

            }

                };

            await ScheduleTreeList.SetStateAsync(filteredState);

        }
        else
        {
            var filteredState = new TreeListState<ScheduleTemplateTreeModel>()
                {
                    FilterDescriptors = new List<IFilterDescriptor>()
            {
                new CompositeFilterDescriptor(){
                    FilterDescriptors = new FilterDescriptorCollection()
                    {

                    }
                }
            }

                };
            await ScheduleTreeList.SetStateAsync(filteredState);
            ToggleText = "All";
        }
    }
    private void FilterAppointments()
    {

        SelectedScheduleCalendarData = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();
        SelectedCalendarData = new List<ScheduleSactivitySchedulerModel>();
        if (ShowActual)
        {
            var actualData = SelectedScheduleCalendarData.Select(x => new ScheduleSactivitySchedulerModel()
                {
                    StartDate = x.ActualStartDate,
                    EndDate = x.ActualEndDate,
                    ActivityName = x.ActivityName,
                    DateType = "actual"
                }).ToList();
            SelectedCalendarData.AddRange(actualData);
        }
        if (ShowScheduled)
        {
            var scheduledData = SelectedScheduleCalendarData.Select(x => new ScheduleSactivitySchedulerModel()
                {
                    StartDate = x.SchStartDate,
                    EndDate = x.SchEndDate,
                    ActivityName = x.ActivityName,
                    DateType = "scheduled"
                }).ToList();
            SelectedCalendarData.AddRange(scheduledData);
        }
        if(ShowBaseline)
        {
            var baselineData = SelectedScheduleCalendarData.Select(x => new ScheduleSactivitySchedulerModel()
                {
                    StartDate = x.BaseStartDate,
                    EndDate = x.BaseEndDate,
                    ActivityName = x.ActivityName,
                    DateType = "baseline"
                }).ToList();
            SelectedCalendarData.AddRange(baselineData);
        }

    }
    void OnItemRenderHandler(SchedulerItemRenderEventArgs args)
    {
        ScheduleSactivitySchedulerModel appt = args.Item as ScheduleSactivitySchedulerModel;
        if(appt.DateType == "scheduled")
        {
            args.Class = "scheduled-style";
        }
        else if(appt.DateType == "actual")
        {
            args.Class = "actual-style";
        }
        else if (appt.DateType == "baseline")
        {
            args.Class = "baseline-style";
        }        
    }
    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= JobChangedHandler;
    }
    async Task JobChangedHandler()
    {
        var selected = SubdivisionJobPickService.JobNumber;
        if (JobSelected != selected)//check if actual change, so not calling this multiple times
        {
            IsLoading = true;//Loader show
            JobSelected = selected;
            StateHasChanged();

            //TODO: this is slow to load. show spinner at least
            var getScheduleTask = ScheduleService.GetScheduleAsync(JobSelected);
            var getprescheduleTask = ScheduleService.GetPreScheduleAsync(JobSelected);
            var getMilestonesTask = ScheduleService.GetMilestonesForScheduleAsync(JobSelected);
            await Task.WhenAll(new Task[] { getMilestonesTask, getScheduleTask, getprescheduleTask });
            AllSelectedScheduleData = getScheduleTask.Result.Value;
            SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(AllSelectedScheduleData);
            SelectedScheduleCalendarData = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();
            FilterAppointments();
            PublishSchedule = AllSelectedScheduleData.Any() ? AllSelectedScheduleData.First().SchedulePublished : false;
            SelectedSchedule = getprescheduleTask.Result.Value;
            Milestones = getMilestonesTask.Result.Value;
            foreach (var milestone in SelectedScheduleData)
            {
                milestone.CompletedDate = milestone.ScheduleMilestone.ActualEndDate;
                foreach (var activity in milestone.Children)
                {
                    activity.CompletedDate = activity.ScheduleSactivity.ActualEndDate;
                }
            }
            IsLoading = false; // Hide load spinner
            StateHasChanged();
        }

    }
    async Task OnStateInitHandler(TreeListStateEventArgs<ScheduleTemplateTreeModel> args)
    {
        // var collapsedItemsState = new TreeListState<ScheduleTemplateTreeModel>()
        // {
        //     //collapse all items in the TreeList upon initialization of the state
        //         ExpandedItems = new List<ScheduleTemplateTreeModel>()
        // };
        // args.TreeListState = collapsedItemsState;
        var filteredState = new TreeListState<ScheduleTemplateTreeModel>()
            {
                FilterDescriptors = new List<IFilterDescriptor>()
            {
                new CompositeFilterDescriptor(){
                    FilterDescriptors = new FilterDescriptorCollection()
                    {
                        new FilterDescriptor()
                        {
                            Member = nameof(ScheduleTemplateTreeModel.CompletedDate),
                            MemberType = typeof(DateTime?),
                            Operator = FilterOperator.IsNull,
                           // Value = "Release"
                        },
                    }
                },

            }

            };
        args.TreeListState = filteredState;
    }
    private async Task OnExpand(TreeListExpandEventArgs args)
    {
        var test = args.Item;
    }
    private async Task EditItem(TreeListCommandEventArgs args)
    {
        var item = args.Item as ScheduleTemplateTreeModel;
        //TODO: add check for supplier can be changed, ie it has po
        // if(item != null && item.ScheduleSactivity != null)
        // {
        //     isScheduledEndEditable = item.ScheduleSactivity.ActualEndDate == null;
        //     isScheduledStartEditable = item.ScheduleSactivity.ActualStartDate == null;
        //     isSupplierEditable = item.SupplierEditable;
        // }
        // else
        // {
        //     isScheduledEndEditable = false;
        //     isScheduledStartEditable = false;
        // }
    }
    private async Task UpdatePublishSchedule()
    {
        var response = await ScheduleService.UpdateSchedulePublishedAsync(SelectedSchedule);
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
    }
    private async Task UpdateItem(TreeListCommandEventArgs args)
    {
        //TODO: get the checkboxes out of the update/edit, do them independently so the grid doesn't have to go into edit mode and then save
        //TODO: clean up duplication below
        var item = args.Item as ScheduleTemplateTreeModel;
        var editedField = args.Field;
        if (item.ScheduleSactivityId != null)
        {
            UnsavedChanges = true; //TODO: check something actually changed
            var getCalendar = (await ScheduleService.GetHolidaysAsync()).Value;
            var holidays = getCalendar.Select(x => x.WorkDate).ToList();
            bool updateBaseDates = false;
            //update/complete the activity
            var allActivitiesInSchedule = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();

            //adjust other fields based on what changed
            var updateItem1 = allActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == item.ScheduleSactivity.ScheduleAid);

            if (updateItem1 != null)
            {
                if (editedField == "ScheduleSactivity.BaseStartDate")
                {
                    //base start date chagned, need to change end date based on duration
                    var checkHoliday = (await ScheduleService.CheckHolidayAsync(item.ScheduleSactivity.BaseStartDate.Value)).Value;
                    if (checkHoliday && !item.ScheduleSactivity.BoolIgnoreNoWorkDays)
                    {

                        var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");
                        if (!confirm)
                        {
                            return;
                        }
                        updateItem1.IsLocked = "T";
                        updateItem1.BoolIgnoreNoWorkDays = true;//set ignore no work days if they choose to ignore the holiday
                    }
                    var findPredecessors = item.PredecessorIds.ToList();
                    if (findPredecessors.Any())
                    {
                        var findPreds = allActivitiesInSchedule.Where(x => x.SactivityId != null && findPredecessors.Contains((int)x.SactivityId)).ToList();
                        var findMaxPredecessorEndDate = findPreds.Max(x => x.BaseEndDate);
                        if (item.ScheduleSactivity.BaseStartDate.Value < findMaxPredecessorEndDate.Value.AddDays((double)item.ScheduleSactivity.LagTime + 1))
                        {
                            Dialogs.AlertAsync("Cannot schedule before predecessor");
                            return;
                        }
                    }
                    updateItem1.BaseStartDate = item.ScheduleSactivity.BaseStartDate;
                    updateItem1.BaseEndDate = updateItem1.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)updateItem1.BaseStartDate, (int)updateItem1.Duration - 1, holidays) : updateItem1.BaseStartDate.Value.AddDays((int)updateItem1.Duration - 1);
                    var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
                    updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);
                    updateBaseDates = true;
                }
                else if (editedField == "ScheduleSactivity.BaseEndDate")
                {
                    updateItem1.BaseEndDate = item.ScheduleSactivity.BaseEndDate;
                    var checkHoliday = (await ScheduleService.CheckHolidayAsync(item.ScheduleSactivity.BaseEndDate.Value)).Value;
                    if (checkHoliday && !item.ScheduleSactivity.BoolIgnoreNoWorkDays)
                    {

                        var confirm = await Dialogs.ConfirmAsync("Selected End Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");
                        if (!confirm)
                        {
                            return;
                        }
                        updateItem1.IsLocked = "T";
                        updateItem1.BoolIgnoreNoWorkDays = true;//set ignore no work days if they choose to ignore the holiday
                    }
                    var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
                    updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);
                    updateBaseDates = true;
                }
                else if (editedField == "ScheduleSactivity.SchStartDate")
                {
                    //sch start date chagned, change end date based on duration
                    if (item.ScheduleSactivity.VarianceCode == null && item.ScheduleSactivity.SchStartDate > updateItem1.SchStartDate)
                    {
                        //Won't happen because of logic in column editor template
                        Dialogs.AlertAsync("You must enter a variance!");
                        return;
                    }
                    //TODO: find pred. be sure not to schedule before pred is complete
                    var findPredecessors = item.PredecessorIds.ToList();
                    if (findPredecessors.Any())
                    {
                        var findPreds = allActivitiesInSchedule.Where(x => x.SactivityId != null && findPredecessors.Contains((int)x.SactivityId)).ToList();
                        var findMaxPredecessorEndDate = findPreds.Max(x => x.SchEndDate);
                        if (item.ScheduleSactivity.SchStartDate.Value < findMaxPredecessorEndDate.Value.AddDays((double)item.ScheduleSactivity.LagTime + 1))
                        {
                            Dialogs.AlertAsync("Cannot schedule before predecessor");
                            return;
                        }
                    }

                    var checkHoliday = (await ScheduleService.CheckHolidayAsync(item.ScheduleSactivity.SchStartDate.Value)).Value;
                    if (checkHoliday && !item.ScheduleSactivity.BoolIgnoreNoWorkDays)
                    {

                        var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");
                        if (!confirm)
                        {
                            return;
                        }
                        updateItem1.IsLocked = "T";
                        updateItem1.BoolIgnoreNoWorkDays = true;//set ignore no work days if they choose to ignore the holiday
                    }
                    updateItem1.SchStartDate = item.ScheduleSactivity.SchStartDate;
                    updateItem1.SchEndDate = updateItem1.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)updateItem1.SchStartDate, (int)updateItem1.Duration - 1, holidays) : updateItem1.SchStartDate.Value.AddDays((int)updateItem1.Duration - 1);
                    var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
                    updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);
                }
                else if (editedField == "ScheduleSactivity.SchEndDate")
                {
                    if (item.ScheduleSactivity.VarianceCode == null && item.ScheduleSactivity.SchEndDate > updateItem1.SchEndDate)
                    {
                        //Won't happen because of logic in column editor template
                        Dialogs.AlertAsync("You must enter a variance!");
                        return;
                    }
                    if (item.ScheduleSactivity.SchEndDate < updateItem1.SchStartDate)
                    {
                        Dialogs.AlertAsync("End date cannot be before start date");
                        return;
                    }
                    updateItem1.SchEndDate = item.ScheduleSactivity.SchEndDate;
                }
                else if (editedField == "ScheduleSactivity.Duration")
                {
                    //duration changed, update sch end date
                    updateItem1.Duration = item.ScheduleSactivity.Duration;
                    updateItem1.SchEndDate = updateItem1.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)updateItem1.SchStartDate, (int)updateItem1.Duration - 1, holidays) : updateItem1.SchStartDate.Value.AddDays((int)updateItem1.Duration - 1);
                    var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
                    updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);
                }
                else if (editedField == "ScheduleSactivity.BoolIgnoreNoWorkDays")
                {
                    //ignore no work days change, update scheduled start or end date
                    updateItem1.IsLocked = item.ScheduleSactivity.BoolIgnoreNoWorkDays ? "T" : "F";
                    updateItem1.BoolIgnoreNoWorkDays = item.ScheduleSactivity.BoolIgnoreNoWorkDays;
                    var findPredActivities = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Where(x => item.PredecessorIds.Contains((int)x.ScheduleSactivity.SactivityId)).ToList();
                    var predEndDate = findPredActivities.Max(x => x.ScheduleSactivity.ActualEndDate != null ? x.ScheduleSactivity.ActualEndDate : x.ScheduleSactivity.SchEndDate);
                    var newStartDate = !item.ScheduleSactivity.BoolIgnoreNoWorkDays ? predEndDate == null ? item.ScheduleSactivity.SchStartDate.Value : CalendarExtension.AddWorkingDays(predEndDate.Value, (int)item.ScheduleSactivity.LagTime + 1, holidays) : predEndDate == null ? item.ScheduleSactivity.SchStartDate.Value : predEndDate.Value.AddDays((int)item.ScheduleSactivity.LagTime + 1);
                    item.ScheduleSactivity.SchStartDate = newStartDate;//If no preds, this is the existing start date
                    updateItem1.SchStartDate = newStartDate;
                    var newEndDate = !item.ScheduleSactivity.BoolIgnoreNoWorkDays ? CalendarExtension.AddWorkingDays(newStartDate, (int)item.ScheduleSactivity.Duration - 1, holidays) : newStartDate.AddDays((int)updateItem1.Duration - 1);
                    updateItem1.SchEndDate = newEndDate;
                    var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
                    updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);
                }
                else if (editedField == "ScheduleSactivity.LagTime")
                {
                    //lag time changed, update start and end dates
                    updateItem1.LagTime = item.ScheduleSactivity.LagTime;
                    var findPredecessors = item.PredecessorIds.ToList();
                    if (findPredecessors.Any())
                    {
                        var findPreds = allActivitiesInSchedule.Where(x => x.SactivityId != null && findPredecessors.Contains((int)x.SactivityId)).ToList();
                        var findMaxPredecessorEndDate = findPreds.Max(x => x.SchEndDate);
                        //TODO: don't update these if actual date is entered
                        //TODO: what if they are trying to mark it as completed early??
                        updateItem1.SchStartDate = updateItem1.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findMaxPredecessorEndDate, (int)updateItem1.LagTime + 1, holidays) : findMaxPredecessorEndDate.Value.AddDays((int)updateItem1.LagTime + 1);
                        updateItem1.SchEndDate = updateItem1.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)updateItem1.SchStartDate, (int)updateItem1.Duration - 1, holidays) : updateItem1.SchStartDate.Value.AddDays((int)updateItem1.Duration - 1);
                    }
                    var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
                    updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);
                }
                else if (editedField == "Predecessors")
                {
                    //predecessor Id should be the list of ints, predecessors should be the strings
                    item.ScheduleSactivity.PredIds = item.PredecessorIds;//TODO: fix
                    item.ScheduleSactivityPreds = item.PredecessorIds.Select(x => new ScheduleSactivityPredDto()
                        {
                            ScheduleAid = (int)item.ScheduleSactivityId,
                            PredSactivityId = x,
                        }).ToList();
                    updateItem1.Predecessors = item.ScheduleSactivityPreds;
                    updateItem1.PredIds = item.PredecessorIds;
                    //todo: don't adjust sch dates if there's actul date
                    var priorActivities = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivityId != null && x.ScheduleSactivity.Seq < ItemToEdit.ScheduleSactivity.Seq)).Select(x => x.Sactivity);
                    var selectPreds = priorActivities.Where(x => item.PredecessorIds.Contains(x.SactivityId)).ToList();
                    item.Predecessors = string.Join(",", selectPreds.Select(x => x.ActivityName));
                    item.PredecessorIds = selectPreds.Select(x => x.SactivityId).ToList();
                    var allActivitiesInSchedule2 = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).ToList();
                    var updateItem2 = allActivitiesInSchedule2.FirstOrDefault(x => x.ScheduleSactivity.ScheduleAid == item.ScheduleSactivity.ScheduleAid);
                    updateItem2.Predecessors = item.Predecessors;
                    updateItem2.PredecessorIds = item.PredecessorIds;
                    var findNewPredActivities = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Where(x => item.PredecessorIds.Contains((int)x.ScheduleSactivity.SactivityId)).ToList();
                    if (findNewPredActivities.Any())
                    {
                        var predEndDate = findNewPredActivities.Max(x => x.ScheduleSactivity.ActualEndDate != null ? x.ScheduleSactivity.ActualEndDate : x.ScheduleSactivity.SchEndDate);
                        var newStartDate = !item.ScheduleSactivity.BoolIgnoreNoWorkDays ? CalendarExtension.AddWorkingDays(predEndDate.Value, (int)item.ScheduleSactivity.LagTime + 1, holidays) : predEndDate.Value.AddDays((int)item.ScheduleSactivity.LagTime + 1);
                        item.ScheduleSactivity.SchStartDate = newStartDate;
                        updateItem1.SchStartDate = newStartDate;
                        var newEndDate = !item.ScheduleSactivity.BoolIgnoreNoWorkDays ? CalendarExtension.AddWorkingDays(newStartDate, (int)item.ScheduleSactivity.Duration - 1, holidays) : newStartDate.AddDays((int)updateItem1.Duration - 1);
                        updateItem1.SchEndDate = newEndDate;
                    }
                    else
                    {
                        updateItem1.SchStartDate = item.ScheduleSactivity.SchStartDate;
                        updateItem1.SchEndDate = item.ScheduleSactivity.SchEndDate;
                    }
                    var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
                    updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);

                }

                else
                {
                    //variance code updated
                    // updateItem1.SchEndDate = item.ScheduleSactivity.SchEndDate;


                    // updateItem1.ActualStartDate = item.ScheduleSactivity.ActualStartDate;
                    updateItem1.VarianceCode = item.ScheduleSactivity.VarianceCode;
                    return;
                }

                try
                {
                    //updates front end, does not save to db
                    var response = await ScheduleService.UpdateScheduleSActivityAsync(updateItem1, allActivitiesInSchedule, updateBaseDates);
                    var activitiesToUpdate = response.Value;
                    ScheduleTreeList.Rebind();

                    foreach (var milestone in SelectedScheduleData.Where(x => x.ScheduleMilestone != null))
                    {
                        milestone.ScheduleMilestone.SchStartDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).Min(x => x.SchStartDate);
                        milestone.ScheduleMilestone.SchEndDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).Max(x => x.SchEndDate);
                        milestone.ScheduleMilestone.BaseStartDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).Min(x => x.BaseStartDate);
                        milestone.ScheduleMilestone.BaseEndDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).Max(x => x.BaseEndDate);
                        milestone.ScheduleMilestone.ActualStartDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).Min(x => x.ActualStartDate);
                        milestone.ScheduleMilestone.ActualEndDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).All(x => x.ActualEndDate != null) ? allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).Max(x => x.ActualEndDate) : null;
                        milestone.ScheduleMilestone.ActualDuration = (milestone.ScheduleMilestone.ActualStartDate != null && milestone.ScheduleMilestone.ActualEndDate != null) ? CalendarExtension.WorkingDaysDuration(milestone.ScheduleMilestone.ActualStartDate.Value, milestone.ScheduleMilestone.ActualEndDate.Value, holidays) : null;
                        milestone.ScheduleMilestone.Duration = CalendarExtension.WorkingDaysDuration(milestone.ScheduleMilestone.SchStartDate.Value, milestone.ScheduleMilestone.SchEndDate.Value, holidays);
                        milestone.ScheduleMilestone.Calduration = (milestone.ScheduleMilestone.BaseEndDate.Value - milestone.ScheduleMilestone.BaseStartDate.Value).Days;//should this be based on base dates or sch dates or actual dates?
                        var plusminusdaysend1 = milestone.ScheduleMilestone.ActualEndDate != null ? milestone.ScheduleMilestone.ActualEndDate.Value : milestone.ScheduleMilestone.SchEndDate.Value;
                        milestone.ScheduleMilestone.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(milestone.ScheduleMilestone.BaseEndDate.Value, plusminusdaysend1, holidays);
                    }
                    SelectedSchedule.BaseStartDate = SelectedScheduleData.Where(x => x.ScheduleMilestone != null).Min(x => x.ScheduleMilestone.BaseStartDate);
                    SelectedSchedule.BaseEndDate = SelectedScheduleData.Where(x => x.ScheduleMilestone != null).Max(x => x.ScheduleMilestone.BaseEndDate);
                    SelectedSchedule.DateToStart = SelectedScheduleData.Where(x => x.ScheduleMilestone != null).Min(x => x.ScheduleMilestone.SchStartDate);
                    SelectedSchedule.DateToEnd = SelectedScheduleData.Where(x => x.ScheduleMilestone != null).Max(x => x.ScheduleMilestone.SchEndDate);
                    SelectedSchedule.ActualStartDate = SelectedScheduleData.Where(x => x.ScheduleMilestone != null).Min(x => x.ScheduleMilestone.ActualStartDate);
                    SelectedSchedule.ActualEndDate = SelectedScheduleData.Where(x => x.ScheduleMilestone != null).All(x => x.ScheduleMilestone.ActualEndDate != null) ? SelectedScheduleData.Where(x => x.ScheduleMilestone != null).Max(x => x.ScheduleMilestone.ActualEndDate) : null;
                    SelectedSchedule.BaseDuration = CalendarExtension.WorkingDaysDuration(SelectedSchedule.BaseStartDate.Value, SelectedSchedule.BaseEndDate.Value, holidays);
                    SelectedSchedule.BaseCalduration = (SelectedSchedule.BaseEndDate.Value - SelectedSchedule.BaseStartDate.Value).Days;
                }
                catch (Exception ex)
                {
                    var debug = ex.Message;
                }
            }
            
        }       
    }
    private void EditActivity(ScheduleSactivityDto activity)
    {
        if(activity != null)
        {
            SelectedActivity = activity;
        }
        EditScheduleActivity?.Show();
        StateHasChanged();
    }
    private async void HandleValidEditActivitySubmit(ResponseModel<ScheduleSactivityDto> responseActivity)
    {
        EditScheduleActivity.Hide();
        //update the tree
        var refreshedScheduleData = (await ScheduleService.GetScheduleAsync(JobSelected)).Value;
        foreach (var milestone in SelectedScheduleData.Where(x => x.ScheduleMilestone != null))
        {
            milestone.ScheduleMilestone = refreshedScheduleData.FirstOrDefault(x => x.ScheduleMilestone.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).ScheduleMilestone;
        }
        foreach (var activity in SelectedScheduleData.SelectMany(x => x.Children).Where(x => x.ScheduleSactivity != null))
        {
            var findUpdateItem = refreshedScheduleData.SelectMany(x => x.Children).FirstOrDefault(x => x.ScheduleSactivity.ScheduleAid == activity.ScheduleSactivity.ScheduleAid);
            activity.ScheduleSactivity = findUpdateItem.ScheduleSactivity;
            activity.Predecessors = findUpdateItem.Predecessors;
            activity.PredecessorIds = findUpdateItem.PredecessorIds;
        }
        ShowSuccessOrErrorNotification(responseActivity.Message, responseActivity.IsSuccess);
        StateHasChanged();
    }
    private void NewScheduleFromTemplate()
    {
        AddScheduleFromTemplateModal.Show();
        StateHasChanged();
    }
    private async void UpdateScheduleFromTemplate()
    {
        //AddScheduleFromTemplateModal.Show();
        var response = await ScheduleService.UpdateScheduleFromTemplateAsync(SelectedSchedule);
        if (response.IsSuccess)
        {
            //refresh, since it's a whole new schedule
            var getScheduleTask = ScheduleService.GetScheduleAsync(JobSelected);
            var getprescheduleTask = ScheduleService.GetPreScheduleAsync(JobSelected);
            var getMilestonesTask = ScheduleService.GetMilestonesForScheduleAsync(JobSelected);
            await Task.WhenAll(new Task[] { getMilestonesTask, getScheduleTask, getprescheduleTask });
            AllSelectedScheduleData = getScheduleTask.Result.Value;
            SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(AllSelectedScheduleData);
            SelectedScheduleCalendarData = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();
            FilterAppointments();
            PublishSchedule = AllSelectedScheduleData.Any() ? AllSelectedScheduleData.First().SchedulePublished : false;
            SelectedSchedule = getprescheduleTask.Result.Value;
            Milestones = getMilestonesTask.Result.Value;
            foreach (var milestone in SelectedScheduleData)
            {
                milestone.CompletedDate = milestone.ScheduleMilestone.ActualEndDate;
                foreach (var activity in milestone.Children)
                {
                    activity.CompletedDate = activity.ScheduleSactivity.ActualEndDate;
                }
            }
            StateHasChanged();
        }
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        StateHasChanged();
    }
    private async Task UpdateBoolCompleted(ScheduleTemplateTreeModel item)
    {
        UnsavedChanges = true; //TODO: check something actually changed
        var getCalendar = (await ScheduleService.GetHolidaysAsync()).Value;
        var holidays = getCalendar.Select(x => x.WorkDate).ToList();
        //update/complete the activity
        var allActivitiesInSchedule = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();
        var updateItem1 = allActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == item.ScheduleSactivity.ScheduleAid);
        
        if (item.ScheduleSactivity.BoolComplete == true)//temp to allow reapprove for testing
        {
            if (item.ScheduleSactivity.ActualStartDate == null)
            {
                Dialogs.AlertAsync("Activity must be started before being completed.");
                item.ScheduleSactivity.BoolComplete = false;
                updateItem1.BoolComplete = false;
                return;
            }
            else if (!userIsDirectorOrAdmin && (CalendarExtension.AddWorkingDays(item.ScheduleSactivity.SchEndDate.Value.Date, 1, holidays) < DateTime.Now.Date || (CalendarExtension.AddWorkingDays(item.ScheduleSactivity.SchEndDate.Value.Date, 1, holidays) == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                //basedateeditable is user is in construction dir role
                Dialogs.AlertAsync("Activity is late! You must select a variance and then adjust the scheduled dates. Contact Construction Director to override this if needed");
                item.ScheduleSactivity.BoolComplete = false;
                updateItem1.BoolComplete = false;
                return;
            }
            else if (userIsDirectorOrAdmin && (CalendarExtension.AddWorkingDays(item.ScheduleSactivity.SchEndDate.Value.Date, 1, holidays) < DateTime.Now.Date || (CalendarExtension.AddWorkingDays(item.ScheduleSactivity.SchEndDate.Value.Date, 1, holidays) == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                //TODO: Holiday needs to be included here
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    item.ScheduleSactivity.BoolComplete = false;
                    updateItem1.BoolComplete = false;
                    return;
                }
            }
            updateItem1.ActualStartDate = item.ScheduleSactivity.SchStartDate;
            updateItem1.ActualEndDate = item.ScheduleSactivity.SchEndDate;
            updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, updateItem1.ActualEndDate.Value, holidays);
            updateItem1.ActualDuration = CalendarExtension.WorkingDaysDuration(updateItem1.ActualStartDate.Value, updateItem1.ActualEndDate.Value);
            updateItem1.Calduration = (updateItem1.ActualEndDate.Value - updateItem1.ActualStartDate.Value).Days;
        }
    }
    private async Task UpdateBoolStarted(ScheduleTemplateTreeModel item)
    {
        UnsavedChanges = true; //TODO: check something actually changed
        var getCalendar = (await ScheduleService.GetHolidaysAsync()).Value;
        var holidays = getCalendar.Select(x => x.WorkDate).ToList();
        //update/complete the activity
        var allActivitiesInSchedule = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();
        var updateItem1 = allActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == item.ScheduleSactivity.ScheduleAid);
        
        if (item.ScheduleSactivity.BoolStarted == true)//temp to allow reapprove for testing
        {
            if (!userIsDirectorOrAdmin && (item.ScheduleSactivity.SchStartDate.Value.Date < DateTime.Now.Date || (item.ScheduleSactivity.SchStartDate.Value.Date == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                //TODO: if it's construction director role, allow check complete after the deadline, maybe just warning dialog
                Dialogs.AlertAsync("You are late! You must select a variance and then adjust the scheduled dates! If the scheduled activity was started on time, but you forgot to mark it, you must contact Construction Director");
                item.ScheduleSactivity.BoolStarted = false;
                updateItem1.BoolStarted = false;
                return;
            }
            else if (userIsDirectorOrAdmin && (item.ScheduleSactivity.SchStartDate.Value.Date < DateTime.Now.Date || (item.ScheduleSactivity.SchStartDate.Value.Date == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    item.ScheduleSactivity.BoolStarted = false;
                    updateItem1.BoolStarted = false;
                    return;
                }
            }
            updateItem1.ActualStartDate = item.ScheduleSactivity.SchStartDate;
            updateItem1.BoolStarted = true;
            //TODO: require variance and adjust schedule if it's not started by 10 am on the day it's schedued
        }
        else
        {
            updateItem1.ActualStartDate = null;//reset it
        }
    }
    private async void HandleValidAddScheduleFromTemplateSubmit(ScheduleDto responseSchedule)
    {
        var test = responseSchedule;
        AddScheduleFromTemplateModal.Hide();
        //TODO: maybe the addschedulefrom template modal should default to have the current job selected, and then if they add a schedule, show it on response
        var selectedScheduleData = (await ScheduleService.GetScheduleAsync(JobSelected)).Value;
        SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(selectedScheduleData);            
        PublishSchedule = selectedScheduleData.Any() ? selectedScheduleData.First().SchedulePublished : false;
        SelectedSchedule = (await ScheduleService.GetPreScheduleAsync(JobSelected)).Value;
        StateHasChanged();
    }
    public async void Save()
    {
        //TODO: fix so if update schedule activity fails it doesn't update the milestones
        var activitiesToUpdate = SelectedScheduleData.SelectMany(x => x.Children.Where(y => y.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();        
        var response = await ScheduleService.UpdateScheduleSactivitysAsync(activitiesToUpdate);       
        var milestonesToUpdate = SelectedScheduleData.Where(x => x.ScheduleMilestone != null).Select(x => x.ScheduleMilestone).ToList();
        var response2 = await ScheduleService.UpdateScheduleMilestonesAsync(milestonesToUpdate);
        var response3 = await ScheduleService.UpdateScheduleAsync(SelectedSchedule);
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        if (response.IsSuccess)
        {
            UnsavedChanges = false;
        }       
        StateHasChanged();
    }
    public async void CancelChanges()
    {
        AllSelectedScheduleData = (await ScheduleService.GetScheduleAsync(JobSelected)).Value;
        SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(AllSelectedScheduleData);
        SelectedScheduleCalendarData = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();
        FilterAppointments();

        PublishSchedule = AllSelectedScheduleData.Any() ? AllSelectedScheduleData.First().SchedulePublished : false;
        SelectedSchedule = (await ScheduleService.GetPreScheduleAsync(JobSelected)).Value;
        Milestones = (await ScheduleService.GetMilestonesForScheduleAsync(JobSelected)).Value;
        foreach (var milestone in SelectedScheduleData)
        {
            milestone.CompletedDate = milestone.ScheduleMilestone.ActualEndDate;
            foreach (var activity in milestone.Children)
            {
                activity.CompletedDate = activity.ScheduleSactivity.ActualEndDate;
            }
        }
        ShowSuccessOrErrorNotification("Cleared Changes", true);
        //TODO: keep expanded state
        UnsavedChanges = false;
        StateHasChanged();
    }
    private async void ChangeApproved()
    {
        if (SelectedSchedule.IniSchApproved == true)
        {
            SelectedSchedule.IniSchApproveDate = DateTime.Now;
            SelectedSchedule.BoolPublished = true;
            if(SelectedSchedule.IniSchStartDate == null)
            {
                SelectedSchedule.IniSchStartDate = DateTime.Now;
                //TODO: show spinner
                await UpdateInitialStart();
            }
            var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userName = user.User.Identity.Name.Split('@')[0];
            SelectedSchedule.IniSchApprovedby = userName;
            StateHasChanged();
        }
        else
        {
            SelectedSchedule.IniSchApproveDate = null;
            SelectedSchedule.IniSchApprovedby = null;
            SelectedSchedule.BoolPublished = false;
        }
    }
    private async Task UpdateInitialStart()
    {
        //TODO: if selected date is a holiday and they choose to use it anyway, how to adjust schedule?
        var checkHoliday = (await ScheduleService.CheckHolidayAsync(SelectedSchedule.IniSchStartDate.Value.Date)).Value;
        if (checkHoliday)
        {
            var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday, cancel to select new date");
            if (!confirm)
            {
                return;
            }            
        }
        var response = await ScheduleService.CalculateScheduleEndFromStartAsync(SelectedSchedule);
        SelectedSchedule.IniSchEndDate = response.Value.IniSchEndDate;
        SelectedSchedule.IniSchDuration = response.Value.IniSchDuration;
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        StateHasChanged();
    }
    private async Task HandleValidSubmit()
    {
        var schedule = SelectedSchedule;
        var result = await ScheduleService.UpdatePrescheduleAsync(SelectedSchedule);
        var selectedScheduleData = (await ScheduleService.GetScheduleAsync(JobSelected)).Value;
        SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(selectedScheduleData);        
        PublishSchedule = selectedScheduleData.Any() ? selectedScheduleData.First().SchedulePublished : false;        
        SelectedSchedule = (await ScheduleService.GetPreScheduleAsync(JobSelected)).Value;
        ShowSuccessOrErrorNotification(result.Message, result.IsSuccess);//TODO: fix response success
        StateHasChanged();
    }
    
    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
    
    List<Milestone> milestones = new List<Milestone>();

    void NavigateToActivity(int scheduleMid)
    {
        string url = $"MobileScheduleActivity/{scheduleMid}";

        NavManager.NavigateTo(url);
    }

}
