﻿using Azure.Core;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using NLog;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class ItemService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public ItemService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }
              
        public async Task<ResponseModel<List<AsmHeaderModel>>> GetAssembliesInPlanAsync(int planId)
        {
            var groups = new ResponseModel<List<AsmHeaderModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/item/GetAssemblyForPlan/{planId}");
                var responseString = await response.Content.ReadAsStringAsync();
                groups = JsonConvert.DeserializeObject<ResponseModel<List<AsmHeaderModel>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return groups;
        }
        public async Task<ResponseModel<List<AsmHeaderModel>>> GetAssembliesInPlanWithOtherMasterPlanAsync(int planId)
        {
            var groups = new ResponseModel<List<AsmHeaderModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/item/GetAssemblyForPlanWithOtherMasterPlan/{planId}");
                var responseString = await response.Content.ReadAsStringAsync();
                groups = JsonConvert.DeserializeObject<ResponseModel<List<AsmHeaderModel>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return groups;
        }
        public async Task<ResponseModel<List<AsmHeaderModel>>> GetAssembliesInPlanIncludeInactiveAsync(int planId)
        {
            //They may need to add options to budget that are inactive, but were active when customer selected thme
            var groups = new ResponseModel<List<AsmHeaderModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/item/GetAssemblyForPlanIncludeInactive/{planId}");
                var responseString = await response.Content.ReadAsStringAsync();
                groups = JsonConvert.DeserializeObject<ResponseModel<List<AsmHeaderModel>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return groups;
        }
        public async Task<ResponseModel<List<AsmHeaderModel>>> GetAllAssembliesForPlansAsync(List<int> selectedMasterPlans)
        {
            var asmHeaders = new ResponseModel<List<AsmHeaderModel>>();
            try
            {
                asmHeaders = await _downstreamAPI.PutForUserAsync<List<int>, ResponseModel<List<AsmHeaderModel>>>(
                   "DownstreamApi", selectedMasterPlans,
                   options => {
                       options.RelativePath = "api/item/GetAllAssembliesForPlans/";
                   });

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return asmHeaders;
        }
        public async Task<ResponseModel<List<ModelManagerItemModel>>> GetItemsInMasterOptionAsync(int optionId)
        {
            var items = new ResponseModel<List<ModelManagerItemModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => {
                       options.RelativePath = $"api/item/getitemsinmasteroption/{optionId}";
                   });
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                    items = JsonConvert.DeserializeObject<ResponseModel<List<ModelManagerItemModel>>>(responseString);
                }

                else
                {
                    var test = response.ReasonPhrase;
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return items;
        }

        public async Task<ResponseModel<List<ModelManagerItemModel>>> GetItemsInAssemblyAsync(int assemblyId)
        {
            var items = new ResponseModel<List<ModelManagerItemModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => { 
                       options.RelativePath = $"api/item/GetItemsInAssembly/{assemblyId}"; 
                   });
                if(response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                    items = JsonConvert.DeserializeObject<ResponseModel<List<ModelManagerItemModel>>>(responseString);
                }

                else
                {
                    var test = response.ReasonPhrase;
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return items;
        }
        
        public async Task<ResponseModel<List<ModelManagerItemModel>>> GetItemsInActivityAsync(int pactivityId, bool optionSpecific = false)
        {
            var items = new ResponseModel<List<ModelManagerItemModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/item/GetMasterItemsByTradeAndActivity/{pactivityId}?optionSpecific={optionSpecific}");
                var responseString = await response.Content.ReadAsStringAsync();
                items = JsonConvert.DeserializeObject<ResponseModel<List<ModelManagerItemModel>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return items;
        }
        public async Task<ResponseModel<List<ModelManagerItemModel>>> GetItemsInActivityToAddToPlanAsync(int pactivityId)
        {
            var items = new ResponseModel<List<ModelManagerItemModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/item/GetMasterItemsToAddByTradeAndActivity/{pactivityId}");
                var responseString = await response.Content.ReadAsStringAsync();
                items = JsonConvert.DeserializeObject<ResponseModel<List<ModelManagerItemModel>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return items;
        }
        public async Task<List<AsmHeaderDto>> GetPlanOptionsForMasterItemAsync(int masterItemId)
        {
            var items = new List<AsmHeaderDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/item/PlanOptionsForMasterItem/{masterItemId}");
                var responseString = await response.Content.ReadAsStringAsync();
                items = JsonConvert.DeserializeObject<List<AsmHeaderDto>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return items;
        }

        public async Task<ResponseModel<List<TradeDto>>> GetTradesAsync()
        {
            var trades = new ResponseModel<List<TradeDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/item/gettrades");
                var responseString = await response.Content.ReadAsStringAsync();
                trades = JsonConvert.DeserializeObject<ResponseModel<List<TradeDto>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return trades;
        }
        public async Task<ResponseModel<List<PactivityModel>>> GetPurchasingActivitiesAsync()
        {
            var activities = new ResponseModel<List<PactivityModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/item/getpurchasingactivities");
                var responseString = await response.Content.ReadAsStringAsync();
                activities = JsonConvert.DeserializeObject<ResponseModel<List<PactivityModel>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return activities;
        }
        public async Task<ResponseModel<List<PactivityModel>>> GetActivityByTradeAsync(int tradeId)
        {
            var activities = new ResponseModel<List<PactivityModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/item/getpurchasingactivitybytrade/{tradeId}");
                var responseString = await response.Content.ReadAsStringAsync();
                activities = JsonConvert.DeserializeObject<ResponseModel<List<PactivityModel>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return activities;
        }

        public async Task<ResponseModel<bool>> DeleteAsmHeaderAsync(int optionId)
        {
            //Don't delete, mark inactive
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<int, ResponseModel<bool>>(
                            "DownstreamApi", optionId,
                             options => {
                                 options.RelativePath = "api/item/DeleteAsmHeader/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<bool>() { IsSuccess = false, Message = "Failed to delete." };

        }
        public async Task<bool> DeleteMasterItemAsync(int itemId)
        {
            //Don't delete, mark inactive
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<int, object>(
                            "DownstreamApi", itemId,
                             options => {
                                 options.RelativePath = "api/item/DeleteMasterItem/";
                             });
                return true;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return true;//TODO: return something useful
        }

        public async Task<bool> DeleteActivityAsync(int activityId)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<int, object>(
                            "DownstreamApi", activityId,
                             options => {
                                 options.RelativePath = "api/item/deleteactivity/";
                             });
                return true;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return true;//TODO: return something useful
        }
        public async Task<ResponseModel<AsmHeaderModel>> UpdateAssemblyAsync(AsmHeaderModel assemblyToUpdate)
        {
            var responseItem = new ResponseModel<AsmHeaderModel>();
            assemblyToUpdate.IsActive = true;
            try
            {
                responseItem = await _downstreamAPI.PutForUserAsync<AsmHeaderModel, ResponseModel<AsmHeaderModel>>(
                    "DownstreamApi", assemblyToUpdate,
                    options => {
                        options.RelativePath = "api/item/updateassembly/";
                    });

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<AsmHeaderModel>() { Value = assemblyToUpdate, IsSuccess = true, Message = responseItem.Message };
        }
        public async Task<ResponseModel<ModelManagerItemModel>> UpdateItemAsync(ModelManagerItemModel item)
        {
            var responseItem = new MasterItemModel();

            var updateItem = new MasterItemModel()
            {
                MasterItemId = item.MasterItemId,
                IsActive = true,
                ItemDesc = item.ItemDesc,
                ItemNotes = item.ItemNotes,
                ItemNumber = item.ItemNumber,
                BomClassId = (int)item.BomClassId,
                CalcBasis = item.CalcBasis,
                CnvFctr = item.CnvFctr,
                JcCategory = item.JcCategory,
                PeCategoryCode = item.PeCategoryCode,
                Taxable = item.IsTaxable ? "T" : "F",
                ExcludeFromPo = item.IsExcludeFromPO ? "T" : "F",
                TakeoffUnit = item.TakeoffUnit,
                OrderUnit = item.OrderUnit,
                RndDir = item.RndDir,
                RndUnit = item.RndUnit,
                Multdiv = item.Multdiv == "Multiply" ? "M" : "D",
                PlanSpecific = item.IsPlanSpecific ? "T" : "F",//This actually means lump sum                 
            };

            try
            {
                responseItem = await _downstreamAPI.PutForUserAsync<MasterItemModel, MasterItemModel>(
                    "DownstreamApi", updateItem,
                    options => {
                        options.RelativePath = "api/item/updateitem/";
                    });
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ModelManagerItemModel>() { Value = item, IsSuccess = true };
        }
        public async Task<ResponseModel<AsmDetailDto>> UpdateAsmDetailAsync(AsmDetailDto item)
        {
            
            try
            {               
                var responseItem = await _downstreamAPI.PutForUserAsync<AsmDetailDto, ResponseModel<AsmDetailDto>>(
                    "DownstreamApi", item,
                    options => {
                        options.RelativePath = "api/item/updateasmdetail/";
                    });
                return responseItem;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<AsmDetailDto>() { Value = item, IsSuccess = false };
        }
        public async Task<ResponseModel<AsmDetailDto>> UpdateAsmDetailsAsync(AsmDetailDto item)
        {

            try
            {
                var responseItem = await _downstreamAPI.PutForUserAsync<AsmDetailDto, ResponseModel<AsmDetailDto>>(
                    "DownstreamApi", item,
                    options => {
                        options.RelativePath = "api/item/updateasmdetails/";
                    });
                return responseItem;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<AsmDetailDto>() { Value = item, IsSuccess = false };
        }
        public async Task<ResponseModel<AsmDetailDto>> DeleteAsmDetailAsync(AsmDetailDto item)
        {

            try
            {
                
                var responseItem = await _downstreamAPI.PutForUserAsync<AsmDetailDto, ResponseModel<AsmDetailDto>>(
                    "DownstreamApi", item,
                    options => {
                        options.RelativePath = "api/item/deleteasmdetail/";
                    });
                return responseItem;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<AsmDetailDto>() { Value = item, IsSuccess = false };
        }
        public async Task<ResponseModel<ModelManagerItemModel>> AddMasterItemAsync(ModelManagerItemModel item)
        {
            var responseItem = new ResponseModel<ModelManagerItemModel>();
            try
            {

                responseItem = await _downstreamAPI.PostForUserAsync<ModelManagerItemModel, ResponseModel<ModelManagerItemModel>>(
                    "DownstreamApi", item,
                    options => {
                        options.RelativePath = "api/item/addmasteritem/";
                    });
                return new ResponseModel<ModelManagerItemModel>() { Value = responseItem.Value ?? item, IsSuccess = true };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<ModelManagerItemModel>() { Value = item, IsSuccess = false };
        }        

        public async Task<ResponseModel<List<ModelManagerItemModel>>> AddMasterOptionItems(List<ModelManagerItemModel> items)
        {
            var responseItem = new ResponseModel<List<ModelManagerItemModel>>();

            try
            {

                responseItem = await _downstreamAPI.PostForUserAsync<List<ModelManagerItemModel>, ResponseModel<List<ModelManagerItemModel>>>(
                    "DownstreamApi", items,
                    options => {
                        options.RelativePath = "api/item/AddItemsToMasterPlanOption/";
                    });
                return new ResponseModel<List<ModelManagerItemModel>>() { Value = responseItem.Value ?? items, IsSuccess = true, Message = responseItem.Message };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<ModelManagerItemModel>>() { Value = items, IsSuccess = false };
        }
       
        public async Task<ResponseModel<PactivityModel>> AddActivityAsync(PactivityModel activity)
        {
          
            try
            {

                var response = await _downstreamAPI.PostForUserAsync<PactivityModel, ResponseModel<PactivityModel>>(
                    "DownstreamApi", activity,
                    options => {
                        options.RelativePath = "api/item/addactivity/";
                    });
               
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<PactivityModel>() { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<PactivityModel>(){ IsSuccess = false, Message = "Sorry. Fail."};
            }
           
        }

        public async Task<ResponseModel<PactivityModel>> UpdateActivityAsync(PactivityModel activity)
        {

            try
            {

                var responseItem = await _downstreamAPI.PutForUserAsync<PactivityModel, ResponseModel<PactivityModel>>(
                    "DownstreamApi", activity,
                    options => {
                        options.RelativePath = "api/item/updateactivity/";
                    });
                
                return responseItem;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<PactivityModel>();
        }

        public async Task<ResponseModel<TradeDto>> AddTradeAsync(TradeDto tradeToAdd)
        {
            
            try
            {

                var response = await _downstreamAPI.PostForUserAsync<TradeDto, ResponseModel<TradeDto>>(
                    "DownstreamApi", tradeToAdd,
                    options => {
                        options.RelativePath = "api/item/addtrade/";
                    });
                
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<TradeDto>() { IsSuccess = false, Value = tradeToAdd };
        }

        public async Task<ResponseModel<TradeDto>> UpdateTradeAsync(TradeDto tradeToUpdate)
        {

            try
            {

                var response = await _downstreamAPI.PutForUserAsync<TradeDto, ResponseModel<TradeDto>>(
                    "DownstreamApi", tradeToUpdate,
                    options => {
                        options.RelativePath = "api/item/updatetrade/";
                    });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<TradeDto>() { IsSuccess = false, Value = tradeToUpdate };
        }
        public async Task<ResponseModel<TradeDto>> DeleteTradeAsync(TradeDto tradeToUpdate)
        {

            try
            {

                var response = await _downstreamAPI.PutForUserAsync<TradeDto, ResponseModel<TradeDto>>(
                    "DownstreamApi", tradeToUpdate,
                    options => {
                        options.RelativePath = "api/item/deletetrade/";
                    });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<TradeDto> { IsSuccess = false, Message = "Fail!!!!"};
        }
    }
}
