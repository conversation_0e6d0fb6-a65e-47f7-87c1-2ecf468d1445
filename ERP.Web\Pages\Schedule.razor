﻿@page "/schedule"
@inject SubdivisionService SubdivisionService
@inject ScheduleService ScheduleService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject PoService PoService
@inject BudgetService BudgetService
@inject SubdivisionService SubdivisionService
@inject IJSRuntime JSRuntimeService
@implements IDisposable
@using ERP.Data.Models.Dto
@using ERP.Data.Models
@using ERP.Data.Models.ExtensionMethods
@using Telerik.DataSource.Extensions
@using System.Collections.ObjectModel
@inject LocalStorage LocalStorage
@inject IJSRuntime JSRuntime
@using System.Text.Json
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector, ConstructionManager, Operations, DesignBuild")]

<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }
    /*.mytreeclass .k-drag-cell {
            display:none;
        }*/
/*    .mytreeclass .k-table-th.k-header.k-drag-cell{
        visibility: collapse;
    }
    .mytreeclass .k-drag-cell.k-touch-action-none{
        visibility: collapse;

    }*/
    .mytreeclass .k-drag-col{
        visibility: collapse;
    }
    .mytreeclass .k-header {
        min-width: 200px;
    }

    .mytreeclass  {
       overflow-x: auto;
    }
    .k-drag-clue {
        color: white;
    }

    .k-icon.k-icon-with-modifier {
        display: none;
    }

    .SubdivisionTable .k-hierarchy-cell {
        display: none;
    }

    .k-splitbar {
        width: 15px;
        color: black;
        padding-left:5px;
        padding-right:5px;
    }

    .pane {
        padding: 8px;
        overflow-x: auto;
    }

    .k-splitter .k-pane {
        overflow-x: auto !important;
    }

    .k-splitter-horizontal{
        overflow-x: auto !important;
    }
    .page-wrapper .page-content{     
       /* padding-right: 100px !important;
        margin-right: 100px !important;*/ 
/*        width: 95% !important;*/
    }
    .page-wrapper{
       /* padding-right: 100px !important;
        margin-right: 100px !important;
            width: 80% !important;*/
    }

    .k-table-td { /*these settings are to prevent the 0 width columns from affecting the column height*/
        padding: 4px !important;
        font-size: 11px;
        height: 30px !important;
        white-space: nowrap !important; 
        overflow: visible;
    }

    .important-appointment {
        background-color: red;
        color: white;
        font-weight: 900;
        margin-top:.25rem;
    }

    .actual-style {
        background-color: #a7e6c0;
        color: black;
        margin-top:.25rem;

    }

    .scheduled-style {
        background-color: #f4cd64;
        color: black;
        margin-top:.25rem;

    }

    .baseline-style {
        background-color: #2e5771;
        color: white;
        margin-top:.25rem;

    }
    .all-day-style {
        background-color: blue;
        color: white;
        margin-top: .25rem;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
    .k-dialog-wrapper{
        z-index: 50000 !important;
    }
    .k-button-solid-base.k-selected {
        border-color: #f4cd64;
        color: black;
        background-color: #f4cd64;
    }

    .k-panelbar > .k-item > .k-link.k-selected, .k-panelbar > .k-panelbar-header > .k-link.k-selected {
        color: black;
        background-color: #f4cd64;
    }

    .k-panelbar > .k-item > .k-link, .k-panelbar > .k-panelbar-header > .k-link {
        color: black;
    }

        .k-panelbar > .k-panelbar-header > .k-link.k-selected:hover{
            background-color: #f4cd64;
        }

    .k-animation-container {
        z-index:10010 !important;
    }

    k-dropdownlist {
        z-index:10010 !important;
    }

    .k-window {
        z-index: 10002;
    }

    .k-filter-menu {
        height: 200px;
        overflow: auto;
    }

    .k-grid .center-cell {
        text-align: center;
    }

    .k-grid th.center-me {
        text-align: center;
    }

    .k-grid th.center-wrap {
        justify-content: center;
        text-align: center;
        white-space: normal;
        vertical-align: middle;
    }

        .k-grid th.center-wrap .k-column-title {
            white-space: normal;
        }

   /*  .k-dropdownlist-popup {
         width: 150px;
     } */

    .custom-dropdown {
        width: 250px;
    }

</style>
<TelerikMediaQuery Media="@SmallScreenMediaQuery" OnChange="@((doesMatch) => IsSmallScreen = doesMatch)"></TelerikMediaQuery>
<TelerikMediaQuery Media="@LargeScreenMediaQuery" OnChange="@((doesMatch) => IsLargeScreen = doesMatch)"></TelerikMediaQuery>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="false" ShowJob="true" ShowSubdivision="true"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
   <div class="card" style="background-color:#2e5771">
       <div class="card-body" style="padding:0.5rem">
            <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Schedule</h7>
       </div>
   </div>


<TelerikTooltip TargetSelector=".tooltip-target" />
@if (IsLargeScreen)
{
    <div class="row">
        @if (JobSelected == null)
        {

            <p><em>Select a job to see schedule details</em></p>
            <div style="width:200px">
                <TelerikButton OnClick="@NewScheduleFromTemplate" Title="New schedule from template" Icon="@FontIcon.Plus" Class=" tooltip-target k-button-add">Create schedule</TelerikButton>
            </div>
        }
        else
        {
            @if (IsLoading)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />
            }
            else
            {
                <TelerikTabStrip>
                    <TabStripTab Title="Schedule">
                        <div>
                            <div style="margin-bottom: 8px">
                                <TelerikButton OnClick="@SetState">Save Column Order</TelerikButton>
                                <TelerikButton OnClick="@ResetState">Reset Column Order</TelerikButton>
                                <TelerikButton OnClick="@ReloadPage">Reload Page</TelerikButton>
                            </div>
                            <TelerikTreeList Data="@SelectedScheduleData"
                            SelectionMode="@TreeListSelectionMode.Multiple"
                            OnStateInit="@((TreeListStateEventArgs<ScheduleTemplateTreeModel> args) => OnStateInitHandler(args))"
                            IdField="Id"
                            Id="WorksheetTree"
                            Class="mytreeclass"
                            ItemsField="Children"
                            Resizable="true"
                            Width="100%"
                            Height="1000px"
                            OnExpand="@OnExpand"
                            OnUpdate="@UpdateItem"
                            OnEdit="@EditItem"
                            EditMode="@TreeListEditMode.Incell"
                            @ref="@ScheduleTreeList"
                            FilterMode="@TreeListFilterMode.FilterRow"
                            Sortable="true" Reorderable="true"
                            OnStateChanged="@((TreeListStateEventArgs<ScheduleTemplateTreeModel> args) => OnStateChangedHandler(args))">
                                <TreeListColumns>
                                    <TreeListColumn Field="@nameof(ScheduleTemplateTreeModel.SactivityIds)" HeaderClass="center-me" Sortable="false" Title="Milestone/Activity" Width="300px" Editable="false" Expandable="true" Locked="true">
                                        @* <FilterMenuTemplate>
                                            @{
                                                theFilterContext = context;
                                            }
                                            <ul>
                                            @foreach (var activity in Activities)
                                            {
                                                <div>
                                                    <TelerikCheckBox Value="@(IsCheckboxInCurrentFilter(context.FilterDescriptor, activity))"
                                                                     TValue="bool"
                                                                     ValueChanged="@((value) => UpdateCheckedActivities(value, activity))"
                                                                     Id="@($"{activity}")">
                                                    </TelerikCheckBox>
                                                    <label for="@($"{activity}")">
                                                        @activity
                                                    </label>
                                                </div>
                                            }
                                            </ul>
                                        </FilterMenuTemplate> *@
                                        <FilterCellTemplate>
                                            <TelerikMultiSelect Data="@SActivitiesData"
                                            @bind-Value="@RowFilteredSActivityIds"
                                            ValueField="@nameof(SactivityDto.SactivityId)"
                                            TextField="@nameof(SactivityDto.ActivityName)"
                                            AutoClose="false"
                                            Filterable="true"
                                            FilterOperator="@StringFilterOperator.Contains"
                                            OnChange="@( (object newValue) => OnSActivityRowFilterChange(newValue, context) )" />
                                        </FilterCellTemplate>
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.FolderName != null)
                                                {
                                                    @($"{item.FolderName}")
                                                }
                                                else if (item != null && item.Sactivity != null)
                                                {
                                                    <TelerikButton Title="View/Edit Notes" OnClick="() => EditActivity(item.ScheduleSactivity)" Class="k-button-success">Notes</TelerikButton>
                                                    @($" - {item.Sactivity.ActivityName}")
                                                    if(item.TotalPOAmount > 0)
                                                    {
                                                        if (userIsDesignBuild)
                                                        {
                                                            <TelerikButton Title="Purchase Order" OnClick="() => ViewPo(item.ScheduleSactivity)">@item.TotalPOAmount.ToString("C0")</TelerikButton>
                                                        }
                                                        else
                                                        {
                                                            <TelerikButton Title="Purchase Order" Icon="FontIcon.Dollar" OnClick="() => ViewPo(item.ScheduleSactivity)"></TelerikButton>
                                                        }
                                                    }
                                                }
                                                else if (item != null && item.Milestone != null)
                                                {
                                                    @($"{item.Milestone.MilestoneName}")
                                                }

                                            }
                                        </Template>
                                    </TreeListColumn>
                                    @*  <TreeListColumn Field="ScheduleSactivity.Complete" Title="Complete" Width="50px" /> *@
                                    <TreeListColumn Field="ScheduleSactivity.BoolStarted" HeaderClass="center-me" Title="Started" Visible="true" Editable="false" Width="50px" Filterable="false" OnCellRender="@( (TreeListCellRenderEventArgs args) => args.Class = "center-cell" )">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null && item.ScheduleSactivity != null)
                                                {
                                                    <TelerikCheckBox  Enabled = "AllowEdit" @bind-Value="item.ScheduleSactivity.BoolStarted" OnChange="()=> UpdateBoolStarted(item)"></TelerikCheckBox>
                                                }

                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null && ItemToEdit.ScheduleSactivity != null)
                                                {
                                                    <TelerikCheckBox Enabled = "AllowEdit" @bind-Value="ItemToEdit.ScheduleSactivity.BoolStarted"></TelerikCheckBox>
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.BoolComplete" HeaderClass="center-me" Title="Complete" Visible="true" Editable="false" Width="50px" Filterable="false" OnCellRender="@( (TreeListCellRenderEventArgs args) => args.Class = "center-cell" )">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null && item.ScheduleSactivity != null)
                                                {
                                                    <TelerikCheckBox Enabled = "AllowEdit"  @bind-Value="item.ScheduleSactivity.BoolComplete" OnChange="()=>UpdateBoolCompleted(item)"></TelerikCheckBox>
                                                }

                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivity != null && ItemToEdit.ScheduleSactivity != null)
                                                {
                                                    <TelerikCheckBox  Enabled = "AllowEdit" @bind-Value="ItemToEdit.ScheduleSactivity.BoolComplete"></TelerikCheckBox>
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.BoolIgnoreNoWorkDays" HeaderClass="center-wrap" Title="Ignore No Work Days" Width="50px" Filterable="false" OnCellRender="@( (TreeListCellRenderEventArgs args) => args.Class = "center-cell" )">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null && item.ScheduleSactivity != null)
                                                {
                                                    <TelerikCheckBox @bind-Value="item.ScheduleSactivity.BoolIgnoreNoWorkDays"></TelerikCheckBox>
                                                }

                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null && ItemToEdit.ScheduleSactivity != null)
                                                {
                                                    <TelerikCheckBox @bind-Value="ItemToEdit.ScheduleSactivity.BoolIgnoreNoWorkDays"></TelerikCheckBox>
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.VarianceCode" HeaderClass="center-wrap" Title="Variance Code" Width="50px" Filterable="false" OnCellRender="@( (TreeListCellRenderEventArgs args) => args.Class = "center-cell" )">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    <span>@item.ScheduleSactivity.VarianceCode</span>
                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivity != null)
                                                {
                                                    <TelerikDropDownList Data="@VarianceCodes"
                                                    TextField="VarianceDesc"
                                                    ValueField="VarianceCode"
                                                    @bind-Value="ItemToEdit.ScheduleSactivity.VarianceCode"
                                                    Class="custom-dropdown">
                                                    </TelerikDropDownList>
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="Predecessors" HeaderClass="center-me" Title="Predecessor" Width="100px" Filterable="false" OnCellRender="@( (TreeListCellRenderEventArgs args) => args.Class = "center-cell" )">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivity != null)
                                                {
                                                    <span>@item.Predecessors</span>
                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                                                                
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null && ItemToEdit.ScheduleSactivity.ActualStartDate == null)
                                                {
                                    
                                                    <TelerikMultiSelect Data="@AvailablePredecessors"
                                                    Context="multiSelectContext"
                                                    TextField="ActivityName"
                                                    ValueField="SactivityId"
                                                    Filterable="true"
                                                    DebounceDelay="0"
                                                    AutoClose="false"
                                                    FilterOperator="StringFilterOperator.Contains"
                                                    @bind-Value="ItemToEdit.PredecessorIds">
                                                    </TelerikMultiSelect>
                                                }
                                                else if (ItemToEdit.ScheduleSactivity.ActualStartDate != null)
                                                {
                                                    <div class="text-danger mt-1">Cannot change predecssor. Activity already started.</div>
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.SubNumber" HeaderClass="center-me" Title="Supplier" Width="100px" Filterable="false" OnCellRender="@( (TreeListCellRenderEventArgs args) => args.Class = "center-cell" )">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivity != null)
                                                {
                                                    <span>@item.ScheduleSactivity.SubNumberNavigation?.SubName</span>
                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivity != null) //3/3/25 removed condition for IsSupplierEditable = true, per Julie request ok to update supplier on schedule even if PO is issued
                                                {
                                                    <TelerikDropDownList @ref="SupplierDropdownRef"
                                                    Data="@AllSuppliers"
                                                    DefaultText="None"
                                                    TextField="SubName"
                                                    ValueField="SubNumber"
                                                    FilterOperator="@StringFilterOperator.Contains"
                                                    Filterable="true"
                                                    @bind-Value="ItemToEdit.ScheduleSactivity.SubNumber"
                                                    OnChange="@OnSupplierChange"
                                                    Enabled="@(ItemToEdit?.ScheduleSactivity?.ActualStartDate == null)"
                                                    Class="custom-dropdown">
                                                    </TelerikDropDownList>

                                                    @if (ItemToEdit?.ScheduleSactivity?.ActualStartDate != null)
                                                    {
                                                        <div class="text-danger mt-1">Already started.</div>
                                                    }
                                                }
                                                else
                                                {
                                                    <span>@ItemToEdit?.ScheduleSactivity?.SubNumberNavigation?.SubName</span>
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    @if (userIsDirectorOrAdmin)
                                    {
                                        <TreeListColumn Field="ScheduleSactivity.Duration" Title="Duration" Width="60px" Filterable="false">
                                            <Template>
                                                @{
                                                    //TODO: probably should not be editable if there is an actual start and end
                                                    var item = context as ScheduleTemplateTreeModel;
                                                    if (item != null && item.ScheduleSactivityId != null)
                                                    {
                                                        @($"{item.ScheduleSactivity.Duration}")
                                                    }
                                                    else if (item != null && item.ScheduleMilestone != null)
                                                    {
                                                        @($"{item.ScheduleMilestone.Duration}")
                                                    }
                                                }
                                            </Template>
                                            <EditorTemplate>
                                                @{
                                                    ItemToEdit = context as ScheduleTemplateTreeModel;
                                                    if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null && isScheduledEndEditable == true)
                                                    {
                                                        <TelerikNumericTextBox @bind-Value="@ItemToEdit.ScheduleSactivity.Duration"></TelerikNumericTextBox>
                                                    }
                                                    else
                                                    {
                                                        if (ItemToEdit.ScheduleSactivity != null)
                                                        {
                                                            <span>@($"{ItemToEdit?.ScheduleSactivity.Duration}")</span>
                                                        }
                                                        else if (ItemToEdit.ScheduleMilestone != null)
                                                        {
                                                            <span>@($"{ItemToEdit?.ScheduleMilestone.Duration}")</span>
                                                        }
                                                    }
                                                }
                                            </EditorTemplate>
                                        </TreeListColumn>
                                        <TreeListColumn Field="ScheduleSactivity.LagTime" HeaderClass="center-me" Title="Lag" Width="40px" Filterable="false">
                                            <Template>
                                                @{
                                                    var item = context as ScheduleTemplateTreeModel;
                                                    if (item != null && item.ScheduleSactivityId != null)
                                                    {
                                                        @($"{item.ScheduleSactivity.LagTime}")
                                                    }
                                                    else if (item != null && item.ScheduleMilestone != null)
                                                    {

                                                    }
                                                }
                                            </Template>
                                            <EditorTemplate>
                                                @{
                                                    ItemToEdit = context as ScheduleTemplateTreeModel;
                                                    if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null)
                                                    {
                                                        <TelerikNumericTextBox @bind-Value="@ItemToEdit.ScheduleSactivity.LagTime" ReadOnly="false"></TelerikNumericTextBox>
                                                    }
                                                    else
                                                    {
                                                    }
                                                }
                                            </EditorTemplate>
                                        </TreeListColumn> 
                                    }                                   
                                    <TreeListColumn Field="ScheduleSactivity.PlusminusDays" Title="+ / -" Editable="false" Width="40px" Filterable="false" HeaderClass="center-me">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{-item.ScheduleSactivity.PlusminusDays}")//Displaying as negative so negative means behind schedule
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {
                                                    @($"{-item.ScheduleMilestone.PlusminusDays}")
                                                    //Displaying as negative so - means behind schedule, db stores it so - is ahead of schedule
                                                }
                                            }
                                        </Template>
                                    </TreeListColumn> 
                                    @*  <TreeListColumn Field="ScheduleSactivity.BaseStartDate" DisplayFormat="{0:MM/dd/yyyy}" Title="Baseline Start" Width="80px">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{item.ScheduleSactivity.BaseStartDate:MM/dd/yyy}")
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {
                                                    @($"{item.ScheduleMilestone.BaseStartDate:MM/dd/yyy}")
                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null && userIsDirectorOrAdmin == true)
                                                {
                                                    <TelerikDatePicker @bind-Value="@ItemToEdit.ScheduleSactivity.BaseStartDate"></TelerikDatePicker>
                                                }
                                                else
                                                {
                                                    if (ItemToEdit.ScheduleSactivityId != null)
                                                    {
                                                        <span>@($"{ItemToEdit?.ScheduleSactivity.BaseStartDate:MM/dd/yyy}")</span>
                                                    }
                                                    else if (ItemToEdit.ScheduleMilestone != null)
                                                    {
                                                        <span>@($"{ItemToEdit?.ScheduleMilestone.BaseStartDate:MM/dd/yyy}")</span>
                                                    }
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>  *@
                                    @* <TreeListColumn Field="ScheduleSactivity.BaseEndDate" DisplayFormat="{0:MM/dd/yyyy}" Title="Baseline Finish" Width="80px">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{item.ScheduleSactivity.BaseEndDate:MM/dd/yyy}")
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {
                                                    @($"{item.ScheduleMilestone.BaseEndDate:MM/dd/yyy}")
                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null && userIsDirectorOrAdmin == true)
                                                {
                                                    <TelerikDatePicker @bind-Value="@ItemToEdit.ScheduleSactivity.BaseEndDate"></TelerikDatePicker>
                                                }
                                                else
                                                {
                                                    if (ItemToEdit.ScheduleSactivityId != null)
                                                    {
                                                        <span>@($"{ItemToEdit?.ScheduleSactivity.BaseEndDate:MM/dd/yyy}")</span>
                                                    }
                                                    else if (ItemToEdit.ScheduleMilestone != null)
                                                    {
                                                        <span>@($"{ItemToEdit?.ScheduleMilestone.BaseEndDate:MM/dd/yyy}")</span>
                                                    }
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>  *@
                                    <TreeListColumn Field="ScheduleSactivity.SchStartDate" HeaderClass="center-me" DisplayFormat="{0:MM/dd/yyyy}" Title="Sch. Start" Width="70px" Filterable="false" OnCellRender="@( (TreeListCellRenderEventArgs args) => args.Class = "center-cell" )">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{item.ScheduleSactivity.SchStartDate:MM/dd/yyy}")
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {
                                                    @($"{item.ScheduleMilestone.SchStartDate:MM/dd/yyy}")
                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null && isScheduledStartEditable == true && ItemToEdit.ScheduleSactivity.ActualStartDate == null && ItemToEdit.ScheduleSactivity.VarianceCode != null)
                                                {

                                                    <TelerikDatePicker DisabledDates = "@Sundays" @bind-Value="@ItemToEdit.ScheduleSactivity.SchStartDate"></TelerikDatePicker>
                                                }
                                                else
                                                {

                                                    if (ItemToEdit.ScheduleSactivityId != null)
                                                    {
                                                        if (ItemToEdit.ScheduleSactivity.ActualStartDate != null)
                                                        {
                                                            <span style="color:red">Already started</span>
                                                        }
                                                        else if (ItemToEdit.ScheduleSactivity.VarianceCode == null)
                                                        {
                                                            <span style="color:red">Variance Required</span>
                                                        }

                                                        <span>@($"{ItemToEdit?.ScheduleSactivity.SchStartDate:MM/dd/yyy}")</span>
                                                    }
                                                    else if (ItemToEdit.ScheduleMilestone != null)
                                                    {
                                                        <span>@($"{ItemToEdit?.ScheduleMilestone.SchStartDate:MM/dd/yyy}")</span>
                                                    }
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.SchEndDate" HeaderClass="center-me" DisplayFormat="{0:MM/dd/yyyy}" Title="Sch. Finish" Width="70px" Filterable="false" OnCellRender="@( (TreeListCellRenderEventArgs args) => args.Class = "center-cell" )">
                                        <Template>
                                            @{
                                                //TODO: scheduled dates should be locked if there is an actual date, and if there is no variance code
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{item.ScheduleSactivity.SchEndDate:MM/dd/yyy}")
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {
                                                    @($"{item.ScheduleMilestone.SchEndDate:MM/dd/yyy}")
                                                }
                                            }
                                        </Template>
                                        <EditorTemplate>
                                            @{
                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                if (ItemToEdit != null && ItemToEdit.ScheduleSactivityId != null && isScheduledEndEditable == true && ItemToEdit.ScheduleSactivity.ActualStartDate == null && ItemToEdit.ScheduleSactivity.VarianceCode != null)
                                                {
                                                    <TelerikDatePicker DisabledDates="@Sundays" @bind-Value="@ItemToEdit.ScheduleSactivity.SchEndDate"></TelerikDatePicker>
                                                }
                                                else
                                                {
                                                    if (ItemToEdit.ScheduleSactivityId != null)
                                                    {
                                                        if (ItemToEdit.ScheduleSactivity.ActualEndDate != null)
                                                        {
                                                            <span style="color:red">Already completed</span>
                                                        }
                                                        if (ItemToEdit.ScheduleSactivity.VarianceCode == null)
                                                        {
                                                            <span style="color:red">Variance Required</span>
                                                        }
                                                        <span>@($"{ItemToEdit?.ScheduleSactivity.SchEndDate:MM/dd/yyy}")</span>
                                                    }
                                                    else if (ItemToEdit.ScheduleMilestone != null)
                                                    {
                                                        <span>@($"{ItemToEdit?.ScheduleMilestone.SchEndDate:MM/dd/yyy}")</span>
                                                    }
                                                }
                                            }
                                        </EditorTemplate>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.ActualStartDate" HeaderClass="center-me" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Title="Actual Start" Width="70px" Filterable="false" OnCellRender="@( (TreeListCellRenderEventArgs args) => args.Class = "center-cell" )">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{item.ScheduleSactivity.ActualStartDate:MM/dd/yyy}")
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {
                                                    @($"{item.ScheduleMilestone.ActualStartDate:MM/dd/yyy}")
                                                }
                                            }
                                        </Template>
                                    </TreeListColumn>
                                    <TreeListColumn Field="ScheduleSactivity.ActualEndDate" HeaderClass="center-me" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Title="Actual Finish" Width="70px" Filterable="false" OnCellRender="@( (TreeListCellRenderEventArgs args) => args.Class = "center-cell" )">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{item.ScheduleSactivity.ActualEndDate:MM/dd/yyy}")
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {
                                                    @($"{item.ScheduleMilestone.ActualEndDate:MM/dd/yyy}")
                                                }
                                            }
                                        </Template>
                                    </TreeListColumn>
                                    @* <TreeListColumn Field="ScheduleSactivity.Calduration" Title="Cal Duration" Editable="false" Width="80px">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{item.ScheduleSactivity.Calduration}")
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {
                                                    @($"{item.ScheduleMilestone.Calduration}")
                                                }
                                            }
                                        </Template>
                                    </TreeListColumn> *@
                                    @* <TreeListColumn Field="ScheduleSactivity.ActualDuration" Title="Actual Duration" Editable="false" Width="80px">
                                        <Template>
                                            @{
                                                var item = context as ScheduleTemplateTreeModel;
                                                if (item != null && item.ScheduleSactivityId != null)
                                                {
                                                    @($"{item.ScheduleSactivity.ActualDuration}")
                                                }
                                                else if (item != null && item.ScheduleMilestone != null)
                                                {
                                                    @($"{item.ScheduleMilestone.ActualDuration}")
                                                }
                                            }
                                        </Template>
                                    </TreeListColumn> *@
                                </TreeListColumns>
                                <TreeListToolBarTemplate>
                                    <div style="float:left; width:200px">
                                        <table>
                                            <tr>
                                                <td colspan="4">
                                                    <TreeListSearchBox DebounceDelay="200" Width="163px"></TreeListSearchBox>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="5">
                                                    <TreeListCommandButton Title="Export To Excel" Command="ExcelExport" OnClick="@ExportSchedule" Icon="@FontIcon.FileExcel" Class="tooltip-target k-button-add"></TreeListCommandButton>
                                                    @if(AllowEdit)
                                                    {
                                                        <TreeListCommandButton Command="Save" OnClick="@Save" Title="Save" Icon="@FontIcon.Save" Class=" tooltip-target k-button-success"></TreeListCommandButton>
                                                        <TreeListCommandButton Command="Cancel" OnClick="@CancelChanges" Title="Cancel Changes" Icon="@FontIcon.Cancel" Class=" tooltip-target k-button-danger"></TreeListCommandButton>
                                                    }

                                                    @if (userIsDirectorOrAdmin && AllowEdit)
                                                    {
                                                        <TreeListCommandButton Command="CreateSchedule" OnClick="@NewScheduleFromTemplate" Title="New schedule from template" Icon="@FontIcon.Plus" Class=" tooltip-target k-button-add"></TreeListCommandButton>
                                                        <TreeListCommandButton Command="UpdateFromTemplate" OnClick="@UpdateScheduleFromTemplate" Title="Update schedule from template" Icon="@FontIcon.Redo" Class=" tooltip-target k-button-add"></TreeListCommandButton>
                                                    }
                                                </td>
                                            </tr>
                                        </table>
                                    </div>

                                    @if (SelectedSchedule != null)
                                    {
                                        <div style="float:left; font-size:.688rem; font-family:'Roboto', sans-serif; width:400px">
                                            <table>
                                                <tr>
                                                    <td><strong>Publish Schedule</strong></td>
                                                    <td><TelerikCheckBox Id="publishCheckbox" @bind-Value="@SelectedSchedule.BoolPublished" OnChange="@UpdatePublishSchedule"></TelerikCheckBox></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Show Complete/Incomplete</strong></td>
                                                    <td>
                                                        <TelerikToggleButton Class="tooltip-target" Title="Filter My Schedules" @bind-Selected="@FilterSchedules" OnClick="ToggleFilter"><strong>@ToggleText</strong></TelerikToggleButton>
                                                    </td>
                                                </tr>
                                                @if (SelectedSchedule.IniSchApproved == true)
                                                {
                                                    <tr>
                                                        <td><strong>Release Date:</strong></td>
                                                        <td>@SelectedSchedule.IniSchStartDate?.ToString("MM/dd/yyyy")</td>
                                                    </tr>
                                                }
                                                else
                                                {
                                                    <tr>
                                                        <td><strong>Release Date:</strong></td>
                                                        <td><span style="color:red"><strong>Not Released</strong></span></td>
                                                    </tr>
                                                }

                                                <tr>
                                                    <td><strong>Template:</strong></td>
                                                    <td>@SelectedSchedule.Template?.TemplateName</td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div style="float:right; font-size:.688rem; font-family:'Roboto', sans-serif; width:300px">
                                            <table>
                                                <tr>
                                                    <td><strong>Permit Number:</strong></td>
                                                    <td>@SelectedSchedule.PermitNumber</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Permit Submit Date:</strong></td>
                                                    <td>@SelectedSchedule.PermitSubmitDate?.ToString("MM/dd/yyyy")</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Permit Received Date:</strong></td>
                                                    <td>@SelectedSchedule.PermitReceivedDate?.ToString("MM/dd/yyyy")</td>
                                                </tr>
                                                <tr>                                                                                                                                                          
                                                    <td><strong>House Type:</strong></td>                                                                                                    
                                                    <td>@HouseType</td>                                                    
                                                </tr>                                                                    
                                            </table>
                                        </div>
                                        <div style="float:right; font-size:.688rem; font-family:'Roboto', sans-serif; width:300px">
                                            <table>
                                                <tr>
                                                    <td><strong>Base Start:</strong></td>
                                                    <td>@SelectedSchedule.BaseStartDate?.ToString("MM/dd/yyyy")</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Base End:</strong></td>
                                                    <td>@SelectedSchedule.BaseEndDate?.ToString("MM/dd/yyyy")</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Schedule Start:</strong></td>
                                                    <td>@SelectedSchedule.DateToStart?.ToString("MM/dd/yyyy")</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Schedule End:</strong></td>
                                                    <td>@SelectedSchedule.DateToEnd?.ToString("MM/dd/yyyy")</td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div style="float:right; font-size:.688rem; font-family:'Roboto', sans-serif; width:300px">
                                            @{
                                                var startDate = Convert.ToDateTime(SelectedSchedule.BaseEndDate);
                                                var endDate = Convert.ToDateTime(SelectedSchedule.DateToEnd);
                                                var delta = startDate.Subtract(endDate);
                                            }

                                            <TelerikCard Width="150px">
                                                <div>
                                                    <CardTitle>
                                                        <div style="text-align: center; padding: 4px; font-weight:bold; margin: 0 auto">
                                                            Days +/-
                                                        </div>
                                                    </CardTitle>
                                                    <CardSeparator></CardSeparator>
                                                    <CardBody>
                                                        <div style="text-align: center; padding: 4px; margin: 0 auto; font-size: 24px">
                                                            @{
                                                                if (delta.TotalDays <= 0)
                                                                {
                                                                    <span style="color: red">
                                                                        @(Math.Floor(delta.TotalDays))
                                                                    </span>
                                                                }
                                                                else
                                                                {
                                                                    <span style="color: green">
                                                                        @(Math.Floor(delta.TotalDays))
                                                                    </span>
                                                                }
                                                            }
                                                        </div>
                                                    </CardBody>
                                                </div>
                                            </TelerikCard>
                                        </div>
                                    }

                                </TreeListToolBarTemplate>

                            </TelerikTreeList>
                        </div>
                    </TabStripTab>
                    <TabStripTab Title="CalendarView">
                        <div>
                            <div>
                                <TelerikCheckBox @bind-Value="@ShowScheduled" Id="scheduledCheckBox" OnChange="@FilterAppointments" />
                                <label for="scheduledCheckBox">Scheduled</label>
                                <TelerikCheckBox @bind-Value="@ShowActual" Id="actualCheckBox" OnChange="@FilterAppointments" />
                                <label for="actualCheckBox">Actual</label>
                                <TelerikCheckBox @bind-Value="@ShowBaseline" Id="baselineCheckBox" OnChange="@FilterAppointments" />
                                <label for="baselineCheckBox">Baseline</label>
                            </div>
                        </div>
                        <TelerikScheduler Data="@SelectedCalendarData"
                        @bind-Date="@StartDate"
                        OnItemRender="@OnItemRenderHandler"
                        Height="600px"
                        StartField="StartDate"
                        EndField="EndDate"
                        IsAllDayField="IsAllDay"
                        TitleField="@(nameof(ScheduleSactivityDto.ActivityName))"
                        DescriptionField="@(nameof(ScheduleSactivityDto.ActivityName))"
                        @bind-View="@CurrView">
                            <SchedulerViews>
                                <SchedulerDayView StartTime="@DayStart" />
                                <SchedulerWeekView StartTime="@DayStart" />
                                <SchedulerMultiDayView StartTime="@DayStart" NumberOfDays="10" />
                                <SchedulerMonthView />
                            </SchedulerViews>
                        </TelerikScheduler>

                    </TabStripTab>
                </TelerikTabStrip>
            }

        }
        <br />
        <br />
    </div>
}
else
{
    @if(Milestones != null)
    {

        var startDate = Convert.ToDateTime(SelectedSchedule.BaseEndDate);
        var endDate = Convert.ToDateTime(SelectedSchedule.DateToEnd);
        var delta = startDate.Subtract(endDate);

        <TelerikCard>
            <div style="background-color:aliceblue">
                <CardTitle>
                    <div style="text-align: center; padding: 4px; font-weight:bold; margin: 0 auto">
                        Days +/- :  @{
                            if (delta.TotalDays <= 0)
                            {
                                <span style="color: red">
                                    @(Math.Floor(delta.TotalDays))
                                </span>
                            }
                            else
                            {
                                <span style="color: green">
                                    @(Math.Floor(delta.TotalDays))
                                </span>
                            }
                        }
                    </div>
                </CardTitle>
            </div>
        </TelerikCard>


        <TelerikPanelBar Data="Milestones">
            <PanelBarBindings>
                <PanelBarBinding>
                    <HeaderTemplate>
                        @{
                            var item = context as ScheduleMilestoneDto;
                            <div class="row" style="display: flex; align-items: center; justify-content: center; width:100%">
                                <div class="col-1" style="align-items: center; justify-content: center;">
                                    @if (item.ActualEndDate != null)
                                    {
                                        <div style="display: inline-block; margin-right: 10px; background-color: green; width: 25px; height: 25px; border-radius: 50%;"></div>
                                    }
                                    else if (DateTime.Today > item.SchEndDate)
                                    {
                                        <div style="display: inline-block; margin-right: 10px; background-color: red; width: 25px; height: 25px; border-radius: 50%;"></div>
                                    }
                                    else
                                    {
                                        <div style="display: inline-block; margin-right: 10px; background-color: yellow; width: 25px; height: 25px; border-radius: 50%;"></div>
                                    }

                                </div>
                                <div class="col" style="text-align:center; width:100%"><h2>@item.Milestone.MilestoneName</h2></div>
                            </div>

                        }
                    </HeaderTemplate>
                    <ContentTemplate>
                        @{
                            var item = context as ScheduleMilestoneDto;
                            <div class="row k-card-body justify-space-between">
                                <div class="col-md-4 col-4">
                                    <h6>Base Start</h6>
                                    <p style="font-size:14px">@(item.BaseStartDate?.ToString("MM/dd/yyyy") ?? "-")</p>
                                    <h6>Base End</h6>
                                    <p style="font-size:14px">@(item.BaseEndDate?.ToString("MM/dd/yyyy") ?? "-")</p>
                                </div>
                                <div class="col-md-4 col-4">
                                    <h6>Sch. Start</h6>
                                    <p style="font-size:14px">@(item.SchStartDate?.ToString("MM/dd/yyyy") ?? "-")</p>
                                    <h6>Sch. End</h6>
                                    <p style="font-size:14px">@(item.SchEndDate?.ToString("MM/dd/yyyy") ?? "-")</p>
                                </div>
                                <div class="col-md-4 col-4">
                                    <h6>Actual Start</h6>
                                    <p style="font-size:14px">@(item.ActualStartDate?.ToString("MM/dd/yyyy") ?? "-")</p>
                                    <h6>Actual End</h6>
                                    <p style="font-size:14px">@(item.ActualEndDate?.ToString("MM/dd/yyyy") ?? "-")</p>
                                </div>
                            </div>
                            <CardActions Class="justify-space-between">
                                <div class="row" style="width:100%">
                                    <div class="col-8" style="text-align:left; font-size:12px">
                                        Created: @item.CreatedDateTime.ToString("MM/dd/yyyy") / Updated: @item.UpdatedDateTime?.ToString("MM/dd/yyyy") / Updated By: @item.UpdatedBy
                                    </div>
                                    <div class="col-4" style="text-align:right; padding-right:10px">
                                        <TelerikButton Class="k-button-success mr-2" @onclick="() => NavigateToActivity(item.ScheduleMid)">View / Edit Activity</TelerikButton>
                                    </div>
                                </div>
                            </CardActions>
                        }
                    </ContentTemplate>
                </PanelBarBinding>
            </PanelBarBindings>
        </TelerikPanelBar>
    }   
}
<NavigationLock ConfirmExternalNavigation="@UnsavedChanges" OnBeforeInternalNavigation="BeforeInternalNavigation" />
<ERP.Web.Components.EditScheduleActivityNotes @ref="EditScheduleActivity" SelectedActivity="@SelectedActivity" HandleAddSubmit="@HandleValidEditActivitySubmit"></ERP.Web.Components.EditScheduleActivityNotes>
<ERP.Web.Components.CreateScheduleFromTemplate @ref="AddScheduleFromTemplateModal" SelectedTemplate="@SelectedTemplate.TemplateId" HandleAddSubmit="@HandleValidAddScheduleFromTemplateSubmit"></ERP.Web.Components.CreateScheduleFromTemplate> 
@code {

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    private string? SelectedJob { get; set; }
    private string? JobSelected { get; set; }
    public ScheduleSactivityDto? SelectedActivity { get; set; }
    private TelerikTreeList<ScheduleTemplateTreeModel>? ScheduleTreeList { get; set; }
    public ObservableCollection<ScheduleTemplateTreeModel>? SelectedScheduleData { get; set; } 
    public List<ScheduleTemplateTreeModel>? AllSelectedScheduleData { get; set; }
    public List<ScheduleTemplateTreeModel>? UnfilteredListSelectedScheduleData { get; set; }
    public ScheduleDto? SelectedSchedule { get; set; }
    public TemplateDto? SelectedTemplate { get; set; } = new TemplateDto();
    public int? PreviousSelectedTemplateId { get; set; }
    protected ERP.Web.Components.CreateTemplate? AddTemplateModal { get; set; }
    public ScheduleTemplateTreeModel? ItemToEdit { get; set; }
    protected ERP.Web.Components.CreateScheduleFromTemplate? AddScheduleFromTemplateModal { get; set; }
    protected ERP.Web.Components.EditScheduleActivityNotes? EditScheduleActivity { get; set; }
    public List<VarianceDto>? VarianceCodes { get; set; }
    public List<SupplierDto>? AllSuppliers { get; set; }
    public bool PublishSchedule { get; set; } = false;
    private bool isScheduledStartEditable { get; set; } = true;
    private bool isScheduledEndEditable { get; set; } = true;
    private bool isSupplierEditable { get; set; } = true;
    private bool userIsDirectorOrAdmin { get; set; } = false;
    private bool userIsDesignBuild { get; set; } = false;
    private bool IsSmallScreen { get; set; }
    private bool IsLargeScreen { get; set; }
    private string SmallScreenMediaQuery { get; set; } = "(max-width: 767px)";
    private string LargeScreenMediaQuery { get; set; } = "(min-width: 1024.5px)";//Previous 1199px
    private bool UnsavedChanges { get; set; } = false;
    private List<DateTime>? Sundays { get; set; }
    private bool AllowEdit { get; set; } = true;
    private string ToggleText { get; set; } = "Incomplete Only";
    private bool FilterSchedules { get; set; } = false;
    private ScheduleTemplateTreeModel EditedItem { get; set; }
    private int? OldSupplierNumber;


    private IEnumerable<SactivityDto> AvailablePredecessors { get; set; } = Enumerable.Empty<SactivityDto>();
    private IEnumerable<SactivityDto> AllPredecessors { get; set; } = Enumerable.Empty<SactivityDto>();

    //for mobile
    public List<ScheduleMilestoneDto>? Milestones { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    private TelerikDropDownList<SupplierDto, int?> SupplierDropdownRef;
    private bool ShowWarning = false;

    //calendar
    public List<ScheduleSactivityDto>? SelectedScheduleCalendarData { get; set; }
    public List<ScheduleSactivitySchedulerModel>? SelectedCalendarData { get; set; }
    public SchedulerView CurrView { get; set; } = SchedulerView.Month;
    public List<CalendarsDayDto>? Holidays { get; set; }
    public DateTime StartDate { get; set; } = DateTime.Now;
    public DateTime DayStart { get; set; } = new DateTime(2000, 1, 1, 8, 0, 0); //the time portion is important

    public bool ShowScheduled { get; set; } = true;
    public bool ShowActual { get; set; } = false;
    public bool ShowBaseline { get; set; } = false;
    public GanttView SelectedView { get; set; } = GanttView.Week;

    public bool IsLoading { get; set; }

    string UniqueStorageKey = "erp-schedule";

    private string HouseType = string.Empty;

    // Filter
    private List<SactivityDto> SActivitiesData { get; set; }

    private List<int> RowFilteredSActivityIds { get; set; } = new List<int>();

    private async Task OnSActivityRowFilterChange(object newValue, FilterCellTemplateContext context)
    {
        if (RowFilteredSActivityIds.Any())
        {
            var build = new List<ScheduleTemplateTreeModel>();

            if (AllSelectedScheduleData != null && AllSelectedScheduleData.Any())
            {
                foreach (var item in AllSelectedScheduleData)
                {
                    if (item.Children != null && item.Children.Any())
                    {
                        foreach (var child in item.Children)
                        {
                            foreach (var id in RowFilteredSActivityIds)
                            {
                                if (child.Sactivity != null)
                                {
                                    if (id == child.Sactivity.SactivityId)
                                    {
                                        build.Add(child);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(build);
        }
        else
        {
            var getScheduleTask = ScheduleService.GetScheduleAsync(JobSelected);
            await Task.WhenAll(new Task[] { getScheduleTask });
            SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(AllSelectedScheduleData);
        }

        await context.FilterAsync();
    }
    // End of Filter

    private async Task BeforeInternalNavigation(LocationChangingContext context)
    {
        if (UnsavedChanges)
        {
            var proceed = await Dialogs.ConfirmAsync("You have unsaved changes, are you sure you want to leave this page?");

            if (!proceed)
            {
                context.PreventNavigation();
            }            
        }        
    }

    protected override async Task OnInitializedAsync()
    {
        Sundays = CalendarExtension.GetSundays(DateTime.Now.AddYears(-1), DateTime.Now.AddYears(1));
        SubdivisionJobPickService.OnChanged += JobChangedHandler;
        var getHolidaysTask = ScheduleService.GetHolidaysAsync();
        var suppliersTask = PoService.GetSuppliersAsync();
        var varianceCodesTask =  ScheduleService.GetVarianceCodesAsync();

        await Task.WhenAll(new Task[] { suppliersTask, varianceCodesTask, getHolidaysTask });
        Holidays = getHolidaysTask.Result.Value;
        AllSuppliers = suppliersTask.Result.Value;
        VarianceCodes = varianceCodesTask.Result.Value;

        if (SubdivisionJobPickService.JobNumber != null)
        {
            IsLoading = true;//Load Spinner
            JobSelected = SubdivisionJobPickService.JobNumber;

            StateHasChanged();

            var getScheduleTask = ScheduleService.GetScheduleAsync(JobSelected);
            var getprescheduleTask = ScheduleService.GetPreScheduleAsync(JobSelected);
            var getMilestonesTask = ScheduleService.GetMilestonesForScheduleAsync(JobSelected);
            var getJobSActivityTask = ScheduleService.GetJobSActivityAsync(JobSelected);


            await Task.WhenAll(new Task[] { getMilestonesTask, getScheduleTask, getprescheduleTask, getJobSActivityTask });

            AllSelectedScheduleData = getScheduleTask.Result.Value;
            SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(AllSelectedScheduleData);
            SelectedScheduleCalendarData = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();
            SActivitiesData = getJobSActivityTask.Result.Value;
            AvailablePredecessors = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivityId != null)).Select(x => x.Sactivity);
            AllPredecessors = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivityId != null)).Select(x => x.Sactivity);
            FilterAppointments();

            PublishSchedule = AllSelectedScheduleData.Any() ? AllSelectedScheduleData.First().SchedulePublished : false;
            SelectedSchedule = getprescheduleTask.Result.Value;
            Milestones = getMilestonesTask.Result.Value;

            foreach (var milestone in SelectedScheduleData)
            {               
                milestone.CompletedDate = milestone.ScheduleMilestone.ActualEndDate;

                foreach (var activity in milestone.Children)
                {
                    activity.CompletedDate = activity.ScheduleSactivity.ActualEndDate;
                }
            }

            StateHasChanged();
            IsLoading = false; // Hide loading
        }

        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userroleConstructionDirector = user.User.IsInRole("ConstructionDirector");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        userIsDesignBuild  = user.User.IsInRole("DesignBuild");
        AllowEdit = !userReadOnly;
        userIsDirectorOrAdmin = userRoleAdmin || userroleConstructionDirector;
    }

    private async Task ExportSchedule()
    {
        if (AllSelectedScheduleData == null)
        {
            //ShowMessage(false, "No Schedule Selected");
            return;
        }
        if (await JSRuntimeService.InvokeAsync<bool>("confirm", $"Do you want to Export?"))
        {
            StateHasChanged();
            var responseBytes = await ScheduleService.DownloadExcelSchedulesAsync(AllSelectedScheduleData);
            var fileName = $"{JobSelected}-{DateTime.Now.ToString("yyyy-MM-dd", System.Globalization.CultureInfo.InvariantCulture)}.xlsx";
            await JSRuntimeService.InvokeAsync<object>("saveAsFile", fileName, Convert.ToBase64String(responseBytes.Value));

        }
    }

    async Task ToggleFilter()
    {
        if (FilterSchedules == true)
        {
            ToggleText = "Incomplete only";

            var filteredState = new TreeListState<ScheduleTemplateTreeModel>()
                {
                    FilterDescriptors = new List<IFilterDescriptor>()
                    {
                        new CompositeFilterDescriptor(){
                            FilterDescriptors = new FilterDescriptorCollection()
                            {                     
                                new FilterDescriptor()
                                {
                                    Member = nameof(ScheduleTemplateTreeModel.CompletedDate),
                                    MemberType = typeof(DateTime?),
                                    Operator = FilterOperator.IsNull,
                                   // Value = "Release"
                                },                        
                            }
                        },

                    }
                };

            await ScheduleTreeList.SetStateAsync(filteredState);
        }
        else
        {
            var filteredState = new TreeListState<ScheduleTemplateTreeModel>()
            {
                FilterDescriptors = new List<IFilterDescriptor>()
                {
                    new CompositeFilterDescriptor(){
                        FilterDescriptors = new FilterDescriptorCollection()
                        {

                        }
                    }
                }
            };

            await ScheduleTreeList.SetStateAsync(filteredState);
            ToggleText = "All";
        }
    }

    private void FilterAppointments()
    {
        SelectedScheduleCalendarData = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();

        SelectedCalendarData = new List<ScheduleSactivitySchedulerModel>();

        if (ShowActual)
        {
            var actualData = SelectedScheduleCalendarData.Select(x => new ScheduleSactivitySchedulerModel()
            {
                StartDate = x.ActualStartDate,
                EndDate = x.ActualEndDate,
                ActivityName = x.ActivityName,
                DateType = "actual"
            }).ToList();

            SelectedCalendarData.AddRange(actualData);
        }

        if (ShowScheduled)
        {
            var scheduledData = SelectedScheduleCalendarData.Select(x => new ScheduleSactivitySchedulerModel()
            {
                StartDate = x.SchStartDate,
                EndDate = x.SchEndDate,
                ActivityName = x.ActivityName,
                DateType = "scheduled"
            }).ToList();

            SelectedCalendarData.AddRange(scheduledData);
        }

        if (ShowBaseline)
        {
            var baselineData = SelectedScheduleCalendarData.Select(x => new ScheduleSactivitySchedulerModel()
            {
                StartDate = x.BaseStartDate,
                EndDate = x.BaseEndDate,
                ActivityName = x.ActivityName,
                DateType = "baseline"
            }).ToList();

            SelectedCalendarData.AddRange(baselineData);
        }
    }

    void OnItemRenderHandler(SchedulerItemRenderEventArgs args)
    {
        ScheduleSactivitySchedulerModel appt = args.Item as ScheduleSactivitySchedulerModel;

        if (appt.DateType == "scheduled")
        {
            args.Class = "scheduled-style";
        }
        else if (appt.DateType == "actual")
        {
            args.Class = "actual-style";
        }
        else if (appt.DateType == "baseline")
        {
            args.Class = "baseline-style";
        }        
    }

    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= JobChangedHandler;
    }

    async Task JobChangedHandler()
    {
        var selected = SubdivisionJobPickService.JobNumber;

        if (JobSelected != selected)//check if actual change, so not calling this multiple times
        {
            IsLoading = true;//Loader show
            JobSelected = selected;

            StateHasChanged();

            //TODO: this is slow to load. show spinner at least
            var getScheduleTask = ScheduleService.GetScheduleAsync(JobSelected);
            var getprescheduleTask = ScheduleService.GetPreScheduleAsync(JobSelected);
            var getMilestonesTask = ScheduleService.GetMilestonesForScheduleAsync(JobSelected);
            var getJobSActivityTask = ScheduleService.GetJobSActivityAsync(JobSelected);
            var getJobTask = SubdivisionService.GetLotAsync(selected);

            await Task.WhenAll(new Task[] { getMilestonesTask, getScheduleTask, getprescheduleTask, getJobSActivityTask, getJobTask });

            AllSelectedScheduleData = getScheduleTask.Result.Value;
            SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(AllSelectedScheduleData);
            SelectedScheduleCalendarData = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();
            SActivitiesData = getJobSActivityTask.Result.Value;
            AvailablePredecessors = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivityId != null)).Select(x => x.Sactivity);
            AllPredecessors = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivityId != null)).Select(x => x.Sactivity);
            var job = getJobTask.Result.Value;
            HouseType = job.PlanName;

            FilterAppointments();

            PublishSchedule = AllSelectedScheduleData.Any() ? AllSelectedScheduleData.First().SchedulePublished : false;
            SelectedSchedule = getprescheduleTask.Result.Value;
            Milestones = getMilestonesTask.Result.Value;

            foreach (var milestone in SelectedScheduleData)
            {
                milestone.CompletedDate = milestone.ScheduleMilestone.ActualEndDate;
                foreach (var activity in milestone.Children)
                {
                    activity.CompletedDate = activity.ScheduleSactivity.ActualEndDate;
                }
            }

            IsLoading = false; // Hide load spinner
            StateHasChanged();
        }
    }

    async Task OnStateInitHandler(TreeListStateEventArgs<ScheduleTemplateTreeModel> args)
    {
        try
        {
            // var collapsedItemsState = new TreeListState<ScheduleTemplateTreeModel>()
            // {
            //     //collapse all items in the TreeList upon initialization of the state
            //         ExpandedItems = new List<ScheduleTemplateTreeModel>()
            // };
            // args.TreeListState = collapsedItemsState;

            // var filters = new CompositeFilterDescriptor()
            //     {
            //         FilterDescriptors = new FilterDescriptorCollection() {
            //              new FilterDescriptor()
            //              {
            //                 Member = nameof(ScheduleTemplateTreeModel.CompletedDate),
            //                 MemberType = typeof(DateTime?),
            //                 Operator = FilterOperator.IsNull,
            //              }
            //         }
            //     };

            var state = await LocalStorage.GetItem<TreeListState<ScheduleTemplateTreeModel>>(UniqueStorageKey);

            if (state != null)
            {
                // state.FilterDescriptors = new FilterDescriptorCollection();
                // state.ColumnStates = null;
                // state.EditItem = null;
                // state.EditField = null;
                state.SortDescriptors = null;//This was causing errors, probably a sort was saved on a column that no longer exists or something
                args.TreeListState = state;
            }

            //args.TreeListState.FilterDescriptors.Add(filters);
        }
        catch (InvalidOperationException ex)
        {
            // JSInterop will fail on 1st render, so doing this will prevent it after re-render
            var test = ex.Message;
        }
    }

    private async void OnStateChangedHandler(TreeListStateEventArgs<ScheduleTemplateTreeModel> args)
    {
        var state = args.TreeListState;
        state.ExpandedItems = null;
        await LocalStorage.SetItem(UniqueStorageKey, state);
    }

    private async Task OnExpand(TreeListExpandEventArgs args)
    {
        var test = args.Item;
    }

    private async Task EditItem(TreeListCommandEventArgs args)
    {
        var item = args.Item as ScheduleTemplateTreeModel;
        OldSupplierNumber = item.ScheduleSactivity.SubNumber;
        EditedItem = item;
        var priorActivities = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivityId != null && x.ScheduleSactivity.Seq < item.ScheduleSactivity.Seq)).Select(x => x.Sactivity);
        AvailablePredecessors = AllPredecessors.Where(x => priorActivities.Select(y => y.SactivityId).Contains(x.SactivityId)).ToList();
        //TODO: add check for supplier can be changed, ie it has po
        // if(item != null && item.ScheduleSactivity != null)
        // {
        //     isScheduledEndEditable = item.ScheduleSactivity.ActualEndDate == null;
        //     isScheduledStartEditable = item.ScheduleSactivity.ActualStartDate == null;
        //     isSupplierEditable = item.SupplierEditable;
        // }
        // else
        // {
        //     isScheduledEndEditable = false;
        //     isScheduledStartEditable = false;
        // }
    }

    private async Task UpdatePublishSchedule()
    {
        var response = await ScheduleService.UpdateSchedulePublishedAsync(SelectedSchedule);
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
    }

    private async Task UpdateItem(TreeListCommandEventArgs args)
    {
        //TODO: clean up duplication below
        var item = args.Item as ScheduleTemplateTreeModel;
        var editedField = args.Field;
        if (item == null)
        {
            await ScheduleService.LogException(new ArgumentNullException("update item null"));
        }
        if (item.ScheduleSactivityId != null)
        {
            UnsavedChanges = true; //TODO: check something actually changed

            var holidays = Holidays.Select(x => x.WorkDate).ToList();
            bool updateBaseDates = false;//TODO: allow Regist to update Base dates

            //update/complete the activity
            List<ScheduleSactivityDto?> allActivitiesInSchedule;

            if (SelectedScheduleData.Where(x => x.Children != null).Any())
            {
                allActivitiesInSchedule = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();
            }
            else
            {
                allActivitiesInSchedule = SelectedScheduleData.Select(x => x.ScheduleSactivity).ToList();
            }

            //adjust other fields based on what changed
            var updateItem = allActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == item.ScheduleSactivity.ScheduleAid);

            if (updateItem != null)
            {
                if (editedField == "ScheduleSactivity.BaseStartDate")
                {
                    //base start date chagned, need to change end date based on duration
                    var checkHoliday = holidays.Contains(item.ScheduleSactivity.BaseStartDate.Value) || item.ScheduleSactivity.BaseStartDate.Value.DayOfWeek == DayOfWeek.Sunday || item.ScheduleSactivity.BaseStartDate.Value.DayOfWeek == DayOfWeek.Saturday;
                    if (checkHoliday && !item.ScheduleSactivity.BoolIgnoreNoWorkDays)
                    {

                        var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");

                        if (!confirm)
                        {
                            return;
                        }

                        updateItem.IsLocked = "T";
                        updateItem.BoolIgnoreNoWorkDays = true;//set ignore no work days if they choose to ignore the holiday
                    }

                    var findPredecessors = item.PredecessorIds.ToList();

                    if (findPredecessors.Any())
                    {
                        var findPreds = allActivitiesInSchedule.Where(x => x.SactivityId != null && findPredecessors.Contains((int)x.SactivityId)).ToList();

                        var findMaxPredecessorEndDate = findPreds.Max(x => x.BaseEndDate);

                        if (item.ScheduleSactivity.BaseStartDate.Value < findMaxPredecessorEndDate.Value.AddDays((double)item.ScheduleSactivity.LagTime + 1))
                        {
                            Dialogs.AlertAsync("Cannot schedule before predecessor");
                            return;
                        }
                    }

                    updateItem.BaseStartDate = item.ScheduleSactivity.BaseStartDate;
                    var addDays = updateItem.Duration == null || updateItem.Duration <= 0 ? 0 : (int)updateItem.Duration - 1;//If duration = 0 or null or 1, make end same day as start
                    updateItem.BaseEndDate = updateItem.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)updateItem.BaseStartDate, addDays, holidays) : updateItem.BaseStartDate.Value.AddDays(addDays);

                    var plusminusdaysend = updateItem.ActualEndDate != null ? updateItem.ActualEndDate.Value : updateItem.SchEndDate.Value;

                    updateItem.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem.BaseEndDate.Value, plusminusdaysend, holidays);
                    updateBaseDates = true;
                }
                else if (editedField == "ScheduleSactivity.BaseEndDate")
                {
                    updateItem.BaseEndDate = item.ScheduleSactivity.BaseEndDate;
                    var checkHoliday = holidays.Contains(item.ScheduleSactivity.BaseEndDate.Value) || item.ScheduleSactivity.BaseEndDate.Value.DayOfWeek == DayOfWeek.Sunday || item.ScheduleSactivity.BaseEndDate.Value.DayOfWeek == DayOfWeek.Saturday;
                    if (checkHoliday && !item.ScheduleSactivity.BoolIgnoreNoWorkDays)
                    {
                        var confirm = await Dialogs.ConfirmAsync("Selected End Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");

                        if (!confirm)
                        {
                            return;
                        }

                        updateItem.IsLocked = "T";
                        updateItem.BoolIgnoreNoWorkDays = true;//set ignore no work days if they choose to ignore the holiday
                    }

                    var plusminusdaysend = updateItem.ActualEndDate != null ? updateItem.ActualEndDate.Value : updateItem.SchEndDate.Value;

                    updateItem.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem.BaseEndDate.Value, plusminusdaysend, holidays);
                    updateBaseDates = true;
                }
                else if (editedField == "ScheduleSactivity.SchStartDate")
                {
                    //sch start date chagned, change end date based on duration
                    if (item.ScheduleSactivity.VarianceCode == null && item.ScheduleSactivity.SchStartDate > updateItem.SchStartDate)
                    {
                        //Won't happen because of logic in column editor template
                        Dialogs.AlertAsync("You must enter a variance!");
                        return;
                    }

                    //TODO: find pred. be sure not to schedule before pred is complete
                    var findPredecessors = item.PredecessorIds.ToList();

                    if (findPredecessors.Any())
                    {
                        var findPreds = allActivitiesInSchedule.Where(x => x.SactivityId != null && findPredecessors.Contains((int)x.SactivityId)).ToList();

                        var findMaxPredecessorEndDate = findPreds.Max(x => x.SchEndDate);

                        if (item.ScheduleSactivity.SchStartDate.Value < findMaxPredecessorEndDate.Value.AddDays((double)item.ScheduleSactivity.LagTime + 1))
                        {
                            Dialogs.AlertAsync("Cannot schedule before predecessor");
                            return;
                        }
                    }
                    var checkHoliday = holidays.Contains(item.ScheduleSactivity.SchStartDate.Value) || item.ScheduleSactivity.SchStartDate.Value.DayOfWeek == DayOfWeek.Sunday || item.ScheduleSactivity.SchStartDate.Value.DayOfWeek == DayOfWeek.Saturday;
                    if (checkHoliday && !item.ScheduleSactivity.BoolIgnoreNoWorkDays)
                    {
                        var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");
                        if (!confirm)
                        {
                            return;
                        }
                        updateItem.IsLocked = "T";
                        updateItem.BoolIgnoreNoWorkDays = true;//set ignore no work days if they choose to ignore the holiday
                    }

                    updateItem.SchStartDate = item.ScheduleSactivity.SchStartDate;
                    var addDays = updateItem.Duration == null || updateItem.Duration <= 0 ? 0 : (int)updateItem.Duration - 1;//If duration = 0 or null or 1, make end same day as start
                    updateItem.SchEndDate = updateItem.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)updateItem.SchStartDate, addDays, holidays) : updateItem.SchStartDate.Value.AddDays(addDays);

                    var plusminusdaysend = updateItem.ActualEndDate != null ? updateItem.ActualEndDate.Value : updateItem.SchEndDate.Value;

                    updateItem.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem.BaseEndDate.Value, plusminusdaysend, holidays);
                }
                else if (editedField == "ScheduleSactivity.SchEndDate")
                {
                    if (item.ScheduleSactivity.VarianceCode == null && item.ScheduleSactivity.SchEndDate > updateItem.SchEndDate)
                    {
                        //Won't happen because of logic in column editor template
                        await Dialogs.AlertAsync("You must enter a variance!");
                        return;
                    }

                    if (item.ScheduleSactivity.SchEndDate < updateItem.SchStartDate)
                    {
                        await Dialogs.AlertAsync("End date cannot be before start date");
                        return;
                    }
                    var checkHoliday = holidays.Contains(item.ScheduleSactivity.SchEndDate.Value) || item.ScheduleSactivity.SchEndDate.Value.DayOfWeek == DayOfWeek.Sunday || item.ScheduleSactivity.SchEndDate.Value.DayOfWeek == DayOfWeek.Saturday;
                    if (checkHoliday && !item.ScheduleSactivity.BoolIgnoreNoWorkDays)
                    {
                        var confirm = await Dialogs.ConfirmAsync("Selected End Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");
                        if (!confirm)
                        {
                            return;
                        }
                        updateItem.IsLocked = "T";
                        updateItem.BoolIgnoreNoWorkDays = true;//set ignore no work days if they choose to ignore the holiday
                    }
                    updateItem.SchEndDate = item.ScheduleSactivity.SchEndDate;
                    var plusminusdaysend = updateItem.ActualEndDate != null ? updateItem.ActualEndDate.Value : updateItem.SchEndDate.Value;
                    updateItem.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem.BaseEndDate.Value, plusminusdaysend, holidays);
                }
                else if (editedField == "ScheduleSactivity.Duration")
                {
                    //duration changed, update sch end date
                    updateItem.Duration = item.ScheduleSactivity.Duration;
                    var addDays = updateItem.Duration == null || updateItem.Duration <= 0 ? 0 : (int)updateItem.Duration - 1;//If duration = 0 or null or 1, make end same day as start
                    updateItem.SchEndDate = updateItem.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)updateItem.SchStartDate, addDays, holidays) : updateItem.SchStartDate.Value.AddDays(addDays);
                    var plusminusdaysend = updateItem.ActualEndDate != null ? updateItem.ActualEndDate.Value : updateItem.SchEndDate.Value;
                    updateItem.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem.BaseEndDate.Value, plusminusdaysend, holidays);
                }
                else if (editedField == "ScheduleSactivity.BoolIgnoreNoWorkDays")
                {
                    //ignore no work days change, update scheduled start or end date
                    updateItem.IsLocked = item.ScheduleSactivity.BoolIgnoreNoWorkDays ? "T" : "F";
                    updateItem.BoolIgnoreNoWorkDays = item.ScheduleSactivity.BoolIgnoreNoWorkDays;

                    var findPredActivities = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Where(x => item.PredecessorIds.Contains((int)x.ScheduleSactivity.SactivityId)).ToList();

                    var predEndDate = findPredActivities.Max(x => x.ScheduleSactivity.ActualEndDate != null ? x.ScheduleSactivity.ActualEndDate : x.ScheduleSactivity.SchEndDate);

                    var newStartDate = !item.ScheduleSactivity.BoolIgnoreNoWorkDays ? predEndDate == null ? item.ScheduleSactivity.SchStartDate.Value : CalendarExtension.AddWorkingDays(predEndDate.Value, (int)item.ScheduleSactivity.LagTime + 1, holidays) : predEndDate == null ? item.ScheduleSactivity.SchStartDate.Value : predEndDate.Value.AddDays((int)item.ScheduleSactivity.LagTime + 1);

                    item.ScheduleSactivity.SchStartDate = newStartDate;//If no preds, this is the existing start date
                    updateItem.SchStartDate = newStartDate;

                    var addDays = updateItem.Duration == null || updateItem.Duration <= 0 ? 0 : (int)updateItem.Duration - 1;//If duration = 0 or null or 1, make end same day as start
                    var newEndDate = !item.ScheduleSactivity.BoolIgnoreNoWorkDays ? CalendarExtension.AddWorkingDays(newStartDate, addDays, holidays) : newStartDate.AddDays(addDays);
                    updateItem.SchEndDate = newEndDate;

                    var plusminusdaysend = updateItem.ActualEndDate != null ? updateItem.ActualEndDate.Value : updateItem.SchEndDate.Value;
                    updateItem.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem.BaseEndDate.Value, plusminusdaysend, holidays);
                }
                else if (editedField == "ScheduleSactivity.LagTime")
                {
                    //lag time changed, update start and end dates
                    updateItem.LagTime = item.ScheduleSactivity.LagTime;
                    var findPredecessors = item.PredecessorIds.ToList();

                    if (findPredecessors.Any())
                    {
                        var findPreds = allActivitiesInSchedule.Where(x => x.SactivityId != null && findPredecessors.Contains((int)x.SactivityId)).ToList();
                        var findMaxPredecessorEndDate = findPreds.Max(x => x.SchEndDate);
                        //TODO: don't update these if actual date is entered
                        //TODO: what if they are trying to mark it as completed early??
                        updateItem.SchStartDate = updateItem.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findMaxPredecessorEndDate, (int)updateItem.LagTime + 1, holidays) : findMaxPredecessorEndDate.Value.AddDays((int)updateItem.LagTime + 1);
                        var addDays = updateItem.Duration == null || updateItem.Duration <= 0 ? 0 : (int)updateItem.Duration - 1;//If duration = 0 or null or 1, make end same day as start
                        updateItem.SchEndDate = updateItem.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)updateItem.SchStartDate, addDays, holidays) : updateItem.SchStartDate.Value.AddDays(addDays);
                    }

                    var plusminusdaysend = updateItem.ActualEndDate != null ? updateItem.ActualEndDate.Value : updateItem.SchEndDate.Value;
                    updateItem.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem.BaseEndDate.Value, plusminusdaysend, holidays);
                }
                else if (editedField == "Predecessors")
                {
                    //predecessor Id should be the list of ints, predecessors should be the strings
                    item.ScheduleSactivity.PredIds = item.PredecessorIds;//TODO: fix

                    item.ScheduleSactivityPreds = item.PredecessorIds.Select(x => new ScheduleSactivityPredDto()
                        {
                            ScheduleAid = (int)item.ScheduleSactivityId,
                            PredSactivityId = x,
                            ScheduleA = item.ScheduleSactivity,
                            IsActive = true,
                            PredScheduleAid = SelectedScheduleData.SelectMany(x => x.Children.Where(y => y.ScheduleSactivityId != null)).FirstOrDefault(y => y.ScheduleSactivity.SactivityId == x).ScheduleSactivity.ScheduleAid,                            
                            }).ToList(); 

                    updateItem.Predecessors = item.ScheduleSactivityPreds;
                    updateItem.PredIds = item.PredecessorIds;

                    //todo: don't adjust sch dates if there's actul date
                    if (ItemToEdit != null)
                    {
                        var priorActivities = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivityId != null && x.ScheduleSactivity.Seq < ItemToEdit.ScheduleSactivity.Seq)).Select(x => x.Sactivity);

                        var selectPreds = priorActivities.Where(x => item.PredecessorIds.Contains(x.SactivityId)).ToList();

                        item.Predecessors = string.Join(",", selectPreds.Select(x => x.ActivityName));
                        item.PredecessorIds = selectPreds.Select(x => x.SactivityId).ToList();

                        var allActivitiesInSchedule2 = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).ToList();

                        var updateItem2 = allActivitiesInSchedule2.FirstOrDefault(x => x.ScheduleSactivity.ScheduleAid == item.ScheduleSactivity.ScheduleAid);

                        updateItem2.Predecessors = item.Predecessors;
                        updateItem2.PredecessorIds = item.PredecessorIds;

                        var findNewPredActivities = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Where(x => item.PredecessorIds.Contains((int)x.ScheduleSactivity.SactivityId)).ToList();

                        if (findNewPredActivities.Any())
                        {
                            var predEndDate = findNewPredActivities.Max(x => x.ScheduleSactivity.ActualEndDate != null ? x.ScheduleSactivity.ActualEndDate : x.ScheduleSactivity.SchEndDate);

                            var newStartDate = !item.ScheduleSactivity.BoolIgnoreNoWorkDays ? CalendarExtension.AddWorkingDays(predEndDate.Value, (int)item.ScheduleSactivity.LagTime + 1, holidays) : predEndDate.Value.AddDays((int)item.ScheduleSactivity.LagTime + 1);

                            item.ScheduleSactivity.SchStartDate = newStartDate;
                            updateItem.SchStartDate = newStartDate;
                            var addDays = updateItem.Duration == null || updateItem.Duration <= 0 ? 0 : (int)updateItem.Duration - 1;//If duration = 0 or null or 1, make end same day as start
                            var newEndDate = !item.ScheduleSactivity.BoolIgnoreNoWorkDays ? CalendarExtension.AddWorkingDays(newStartDate, addDays, holidays) : newStartDate.AddDays(addDays);

                            updateItem.SchEndDate = newEndDate;
                        }
                        else
                        {
                            updateItem.SchStartDate = item.ScheduleSactivity.SchStartDate;
                            updateItem.SchEndDate = item.ScheduleSactivity.SchEndDate;
                        }

                        var plusminusdaysend = updateItem.ActualEndDate != null ? updateItem.ActualEndDate.Value : updateItem.SchEndDate.Value;
                        updateItem.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem.BaseEndDate.Value, plusminusdaysend, holidays);
                    }
                }
                else if (editedField == "ScheduleSactivity.SubNumber")
                {
                    //TODO: allow change supplier if not complete and no issued po.  this does not save
                    if (item.ScheduleSactivity.SubNumberNavigation == null)
                    {
                        item.ScheduleSactivity.SubNumberNavigation = new SupplierDto();
                    }
                    if (updateItem.SubNumberNavigation == null)
                    {
                        updateItem.SubNumberNavigation = new SupplierDto();
                    }

                    item.ScheduleSactivity.SubNumberNavigation.SubName = AllSuppliers.FirstOrDefault(x => x.SubNumber == item.ScheduleSactivity.SubNumber)?.SubName;
                    if (item.ScheduleSactivity.SubNumber != null)
                    {
                        item.ScheduleSactivity.SubNumberNavigation.SubNumber = (int)item.ScheduleSactivity.SubNumber;
                        updateItem.SubNumber = item.ScheduleSactivity.SubNumber;
                        updateItem.SubNumberNavigation.SubNumber = (int)item.ScheduleSactivity.SubNumber;
                        updateItem.SubNumberNavigation.SubName = AllSuppliers.FirstOrDefault(x => x.SubNumber == item.ScheduleSactivity.SubNumber)?.SubName;
                        return;
                    }
                    else
                    {
                        updateItem.SubNumber = null;
                        updateItem.SubNumberNavigation = new SupplierDto();
                    }

                }
                else
                {
                    //variance code updated
                    // updateItem1.SchEndDate = item.ScheduleSactivity.SchEndDate;

                    // updateItem1.ActualStartDate = item.ScheduleSactivity.ActualStartDate;
                    updateItem.VarianceCode = item.ScheduleSactivity.VarianceCode;
                    return;
                }

                try
                {
                    //updates front end, does not save to db
                    var response = await ScheduleService.UpdateScheduleSActivityAsync(updateItem, allActivitiesInSchedule, updateBaseDates);
                    var activitiesToUpdate = response.Value;

                    ScheduleTreeList.Rebind();

                    foreach (var milestone in SelectedScheduleData.Where(x => x.ScheduleMilestone != null))
                    {
                        milestone.ScheduleMilestone.SchStartDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).Min(x => x.SchStartDate);
                        milestone.ScheduleMilestone.SchEndDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).Max(x => x.SchEndDate);
                        milestone.ScheduleMilestone.BaseStartDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).Min(x => x.BaseStartDate);
                        milestone.ScheduleMilestone.BaseEndDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).Max(x => x.BaseEndDate);
                        milestone.ScheduleMilestone.ActualStartDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).Min(x => x.ActualStartDate);
                        milestone.ScheduleMilestone.ActualEndDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).All(x => x.ActualEndDate != null) ? allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).Max(x => x.ActualEndDate) : null;
                        milestone.ScheduleMilestone.ActualDuration = (milestone.ScheduleMilestone.ActualStartDate != null && milestone.ScheduleMilestone.ActualEndDate != null) ? CalendarExtension.WorkingDaysDuration(milestone.ScheduleMilestone.ActualStartDate.Value, milestone.ScheduleMilestone.ActualEndDate.Value, holidays) : null;
                        milestone.ScheduleMilestone.Duration = CalendarExtension.WorkingDaysDuration(milestone.ScheduleMilestone.SchStartDate.Value, milestone.ScheduleMilestone.SchEndDate.Value, holidays);
                        milestone.ScheduleMilestone.Calduration = (milestone.ScheduleMilestone.BaseEndDate.Value - milestone.ScheduleMilestone.BaseStartDate.Value).Days;//should this be based on base dates or sch dates or actual dates?
                        var plusminusdaysend1 = milestone.ScheduleMilestone.ActualEndDate != null ? milestone.ScheduleMilestone.ActualEndDate.Value : milestone.ScheduleMilestone.SchEndDate.Value;
                        milestone.ScheduleMilestone.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(milestone.ScheduleMilestone.BaseEndDate.Value, plusminusdaysend1, holidays);
                    }

                    SelectedSchedule.BaseStartDate = SelectedScheduleData.Where(x => x.ScheduleMilestone != null).Min(x => x.ScheduleMilestone.BaseStartDate);
                    SelectedSchedule.BaseEndDate = SelectedScheduleData.Where(x => x.ScheduleMilestone != null).Max(x => x.ScheduleMilestone.BaseEndDate);
                    SelectedSchedule.DateToStart = SelectedScheduleData.Where(x => x.ScheduleMilestone != null).Min(x => x.ScheduleMilestone.SchStartDate);
                    SelectedSchedule.DateToEnd = SelectedScheduleData.Where(x => x.ScheduleMilestone != null).Max(x => x.ScheduleMilestone.SchEndDate);
                    SelectedSchedule.ActualStartDate = SelectedScheduleData.Where(x => x.ScheduleMilestone != null).Min(x => x.ScheduleMilestone.ActualStartDate);
                    SelectedSchedule.ActualEndDate = SelectedScheduleData.Where(x => x.ScheduleMilestone != null).All(x => x.ScheduleMilestone.ActualEndDate != null) ? SelectedScheduleData.Where(x => x.ScheduleMilestone != null).Max(x => x.ScheduleMilestone.ActualEndDate) : null;
                    SelectedSchedule.BaseDuration = CalendarExtension.WorkingDaysDuration(SelectedSchedule.BaseStartDate.Value, SelectedSchedule.BaseEndDate.Value, holidays);
                    SelectedSchedule.BaseCalduration = (SelectedSchedule.BaseEndDate.Value - SelectedSchedule.BaseStartDate.Value).Days;
                }
                catch (Exception ex)
                {
                    var debug = ex.Message;
                }
            } 
        }       
    }

    private async Task OnSupplierChange(object userInput)
    {
        int? newValue = userInput as int?;
        int safeValue = newValue ?? 0; // or use -1 if 0 is a valid supplier
        var supplierName = AllSuppliers.FirstOrDefault(x => x.SubNumber == newValue)?.SubName;

        var confirm = await Dialogs.ConfirmAsync($"Apply new supplier '{supplierName ?? "Empty"}' to all activities with same original supplier?");
        if (confirm)
        {
            var matchingItems = SelectedScheduleData
                .SelectMany(x => x.Children)
                .Where(x => x.ScheduleSactivity.SubNumber == OldSupplierNumber);

            foreach (var item in matchingItems)
            {
                if (item.ScheduleSactivity.ActualStartDate == null)
                {
                    item.ScheduleSactivity.SubNumber = newValue;

                    item.ScheduleSactivity.SubNumberNavigation = new SupplierDto
                        {
                            SubNumber = safeValue,
                            SubName = supplierName ?? string.Empty
                        };
                }

            }

            ScheduleTreeList.Rebind();
        }
    }


    private async Task EditActivity(ScheduleSactivityDto activity)
    {
        if (activity != null)
        {
            SelectedActivity = activity;
        }

        await EditScheduleActivity?.Show();
        StateHasChanged();
    }

    private async void HandleValidEditActivitySubmit(ResponseModel<ScheduleSactivityDto> responseActivity)
    {
        EditScheduleActivity.Hide();
        //12/19 Don't refresh - if they had unsaved edits those get lost

        //update the tree
        // var refreshedScheduleData = (await ScheduleService.GetScheduleAsync(JobSelected)).Value;

        // foreach (var milestone in SelectedScheduleData.Where(x => x.ScheduleMilestone != null))
        // {
        //     milestone.ScheduleMilestone = refreshedScheduleData.FirstOrDefault(x => x.ScheduleMilestone.ScheduleMid == milestone.ScheduleMilestone.ScheduleMid).ScheduleMilestone;
        // }

        // foreach (var activity in SelectedScheduleData.SelectMany(x => x.Children).Where(x => x.ScheduleSactivity != null))
        // {
        //     var findUpdateItem = refreshedScheduleData.SelectMany(x => x.Children).FirstOrDefault(x => x.ScheduleSactivity.ScheduleAid == activity.ScheduleSactivity.ScheduleAid);
        //     activity.ScheduleSactivity = findUpdateItem.ScheduleSactivity;
        //     activity.Predecessors = findUpdateItem.Predecessors;
        //     activity.PredecessorIds = findUpdateItem.PredecessorIds;
        // }

        ShowSuccessOrErrorNotification(responseActivity.Message, responseActivity.IsSuccess);

        StateHasChanged();
    }

    private void NewScheduleFromTemplate()
    {
        AddScheduleFromTemplateModal.Show();
        StateHasChanged();
    }

    private async void UpdateScheduleFromTemplate()
    {
        //AddScheduleFromTemplateModal.Show();
        var response = await ScheduleService.UpdateScheduleFromTemplateAsync(SelectedSchedule);

        if (response.IsSuccess)
        {
            //refresh, since it's a whole new schedule
            var getScheduleTask = ScheduleService.GetScheduleAsync(JobSelected);
            var getprescheduleTask = ScheduleService.GetPreScheduleAsync(JobSelected);
            var getMilestonesTask = ScheduleService.GetMilestonesForScheduleAsync(JobSelected);

            await Task.WhenAll(new Task[] { getMilestonesTask, getScheduleTask, getprescheduleTask });

            AllSelectedScheduleData = getScheduleTask.Result.Value;
            SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(AllSelectedScheduleData);
            SelectedScheduleCalendarData = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();

            FilterAppointments();

            PublishSchedule = AllSelectedScheduleData.Any() ? AllSelectedScheduleData.First().SchedulePublished : false;
            SelectedSchedule = getprescheduleTask.Result.Value;
            Milestones = getMilestonesTask.Result.Value;

            foreach (var milestone in SelectedScheduleData)
            {
                milestone.CompletedDate = milestone.ScheduleMilestone.ActualEndDate;
                foreach (var activity in milestone.Children)
                {
                    activity.CompletedDate = activity.ScheduleSactivity.ActualEndDate;
                }
            }

            StateHasChanged();
        }

        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        StateHasChanged();
    }

    private async Task UpdateBoolCompleted(ScheduleTemplateTreeModel item)
    {
        UnsavedChanges = true; //TODO: check something actually changed

        var getCalendar = (await ScheduleService.GetHolidaysAsync()).Value;
        var holidays = getCalendar.Select(x => x.WorkDate).ToList();

        //update/complete the activity
        var allActivitiesInSchedule = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();
        var updateItem1 = allActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == item.ScheduleSactivity.ScheduleAid);

        if (item.ScheduleSactivity.BoolComplete == true)//temp to allow reapprove for testing
        {
            if (item.ScheduleSactivity.ActualStartDate == null)
            {
                Dialogs.AlertAsync("Activity must be started before being completed.");
                item.ScheduleSactivity.BoolComplete = false;
                updateItem1.BoolComplete = false;
                return;
            }
            else if (!userIsDirectorOrAdmin && (CalendarExtension.AddWorkingDays(item.ScheduleSactivity.SchEndDate.Value.Date, 1, holidays) < DateTime.Now.Date || (CalendarExtension.AddWorkingDays(item.ScheduleSactivity.SchEndDate.Value.Date, 1, holidays) == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                //Regis had requested not allow them to mark it started after 10 am on scheduled date
                //for now just a warning that it's late. 
                //basedateeditable is user is in construction dir role
                // Dialogs.AlertAsync("Activity is late! You must select a variance and then adjust the scheduled dates. Contact Construction Director to override this if needed");
                // item.ScheduleSactivity.BoolComplete = false;
                // updateItem1.BoolComplete = false;
                // return;
                //for now, just warn that it's after scheduled date. They probably should enter a variance.
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    item.ScheduleSactivity.BoolComplete = false;
                    updateItem1.BoolComplete = false;
                    return;
                }
            }
            else if (userIsDirectorOrAdmin && (CalendarExtension.AddWorkingDays(item.ScheduleSactivity.SchEndDate.Value.Date, 1, holidays) < DateTime.Now.Date || (CalendarExtension.AddWorkingDays(item.ScheduleSactivity.SchEndDate.Value.Date, 1, holidays) == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    item.ScheduleSactivity.BoolComplete = false;
                    updateItem1.BoolComplete = false;
                    return;
                }
            }
            updateItem1.ActualStartDate = item.ScheduleSactivity.SchStartDate;
            updateItem1.ActualEndDate = item.ScheduleSactivity.SchEndDate;
            updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, updateItem1.ActualEndDate.Value, holidays);
            updateItem1.ActualDuration = CalendarExtension.WorkingDaysDuration(updateItem1.ActualStartDate.Value, updateItem1.ActualEndDate.Value);
            updateItem1.Calduration = (updateItem1.ActualEndDate.Value - updateItem1.ActualStartDate.Value).Days;
        }
        else
        {
            updateItem1.ActualEndDate = null;//reset it
        }
    }

    private async Task UpdateBoolStarted(ScheduleTemplateTreeModel item)
    {
        UnsavedChanges = true;

        var getCalendar = (await ScheduleService.GetHolidaysAsync()).Value;
        var holidays = getCalendar.Select(x => x.WorkDate).ToList();

        //update/complete the activity
        var allActivitiesInSchedule = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();

        var updateItem1 = allActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == item.ScheduleSactivity.ScheduleAid);

        if (item.ScheduleSactivity.BoolStarted == true)//temp to allow reapprove for testing
        {
            if (!userIsDirectorOrAdmin && (item.ScheduleSactivity.SchStartDate.Value.Date < DateTime.Now.Date || (item.ScheduleSactivity.SchStartDate.Value.Date == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                //TODO: require variance and adjust schedule if it's not started by 10 am on the day it's schedued
                //TODO: if it's construction director role, allow check complete after the deadline, maybe just warning dialog
                // Dialogs.AlertAsync("You are late! You must select a variance and then adjust the scheduled dates! If the scheduled activity was started on time, but you forgot to mark it, you must contact Construction Director");
                // item.ScheduleSactivity.BoolStarted = false;
                // updateItem1.BoolStarted = false;
                // return;
                //Regis asked to prevent construction managers from starting late, for now just showing a warning
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    item.ScheduleSactivity.BoolStarted = false;
                    updateItem1.BoolStarted = false;
                    return;
                }
            }
            //else if (userIsDirectorOrAdmin && (item.ScheduleSactivity.SchStartDate.Value.Date < DateTime.Now.Date || (item.ScheduleSactivity.SchStartDate.Value.Date == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            else if (userIsDirectorOrAdmin && (item.ScheduleSactivity.SchStartDate.Value.Date < DateTime.Now.Date))
            {
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    item.ScheduleSactivity.BoolStarted = false;
                    updateItem1.BoolStarted = false;
                    return;
                }
            }

            updateItem1.ActualStartDate = item.ScheduleSactivity.SchStartDate;
            updateItem1.BoolStarted = true;           
        }
        else
        {
            updateItem1.ActualStartDate = null;//reset it
        }
    }

    private async void HandleValidAddScheduleFromTemplateSubmit(ScheduleDto responseSchedule)
    {
        var test = responseSchedule;

        AddScheduleFromTemplateModal.Hide();

        //TODO: maybe the addschedulefrom template modal should default to have the current job selected, and then if they add a schedule, show it on response
        var selectedScheduleData = (await ScheduleService.GetScheduleAsync(JobSelected)).Value;

        SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(selectedScheduleData);            
        PublishSchedule = selectedScheduleData.Any() ? selectedScheduleData.First().SchedulePublished : false;
        SelectedSchedule = (await ScheduleService.GetPreScheduleAsync(JobSelected)).Value;

        StateHasChanged();
    }

    public async void Save()
    {
        var activitiesToUpdate = SelectedScheduleData.Where(x => x.Children != null).SelectMany(x => x.Children.Where(y => y.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();  

        var response = await ScheduleService.UpdateScheduleSactivitysAsync(activitiesToUpdate); //saves update to db, including updating milestones and schedule dates

        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);

        if (response.IsSuccess)
        {
            UnsavedChanges = false;
        } 
        StateHasChanged();
    }

    public async void CancelChanges()
    {
        AllSelectedScheduleData = (await ScheduleService.GetScheduleAsync(JobSelected)).Value;
        SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(AllSelectedScheduleData);
        SelectedScheduleCalendarData = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();

        FilterAppointments();

        PublishSchedule = AllSelectedScheduleData.Any() ? AllSelectedScheduleData.First().SchedulePublished : false;
        SelectedSchedule = (await ScheduleService.GetPreScheduleAsync(JobSelected)).Value;
        Milestones = (await ScheduleService.GetMilestonesForScheduleAsync(JobSelected)).Value;

        foreach (var milestone in SelectedScheduleData)
        {
            milestone.CompletedDate = milestone.ScheduleMilestone.ActualEndDate;
            foreach (var activity in milestone.Children)
            {
                activity.CompletedDate = activity.ScheduleSactivity.ActualEndDate;
            }
        }

        ShowSuccessOrErrorNotification("Cleared Changes", true);

        //TODO: keep expanded state 
        UnsavedChanges = false;
        StateHasChanged();
    }

    private async void ChangeApproved()
    {
        if (SelectedSchedule.IniSchApproved == true)
        {
            SelectedSchedule.IniSchApproveDate = DateTime.Now;
            SelectedSchedule.BoolPublished = true;

            if(SelectedSchedule.IniSchStartDate == null)
            {
                SelectedSchedule.IniSchStartDate = DateTime.Now;
                //TODO: show spinner
                await UpdateInitialStart();
            }

            var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userName = user.User.Identity.Name.Split('@')[0];
            SelectedSchedule.IniSchApprovedby = userName;

            StateHasChanged();
        }
        else
        {
            SelectedSchedule.IniSchApproveDate = null;
            SelectedSchedule.IniSchApprovedby = null;
            SelectedSchedule.BoolPublished = false;
        }
    }

    private async Task UpdateInitialStart()
    {
        //TODO: if selected date is a holiday and they choose to use it anyway, how to adjust schedule?
        var checkHoliday = (await ScheduleService.CheckHolidayAsync(SelectedSchedule.IniSchStartDate.Value.Date)).Value;

        if (checkHoliday)
        {
            var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday, cancel to select new date");
            if (!confirm)
            {
                return;
            }            
        }

        var response = await ScheduleService.CalculateScheduleEndFromStartAsync(SelectedSchedule);

        SelectedSchedule.IniSchEndDate = response.Value.IniSchEndDate;
        SelectedSchedule.IniSchDuration = response.Value.IniSchDuration;

        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);

        StateHasChanged();
    }

    private async Task HandleValidSubmit()
    {
        var schedule = SelectedSchedule;
        var result = await ScheduleService.UpdatePrescheduleAsync(SelectedSchedule);
        var selectedScheduleData = (await ScheduleService.GetScheduleAsync(JobSelected)).Value;

        SelectedScheduleData = new ObservableCollection<ScheduleTemplateTreeModel>(selectedScheduleData);        
        PublishSchedule = selectedScheduleData.Any() ? selectedScheduleData.First().SchedulePublished : false;        
        SelectedSchedule = (await ScheduleService.GetPreScheduleAsync(JobSelected)).Value;

        ShowSuccessOrErrorNotification(result.Message, result.IsSuccess);//TODO: fix response success

        StateHasChanged();
    }

    async Task ViewPo(ScheduleSactivityDto activity)
    {
        if (activity != null)
        {
            var getPos = await PoService.GetPOForScheduleActivityAsync(activity.ScheduleAid);
            if (getPos?.Value != null && getPos.Value.Count != 0)
            {
                var details = getPos.Value;
                var groupedByHeader = details.GroupBy(x => x.PoheaderId).ToList();
                foreach (var po in groupedByHeader)
                {
                    var header = po.First().Poheader;
                    await GenerateDocumentAndDownload(header, po.Select(x => x).ToList());
                }
            }
        }
    }
    async Task GenerateDocumentAndDownload(PoheaderDto poHeader, List<PodetailDto> poDetails)
    {
        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(poHeader, poDetails);
        DemoFileExporter.Save(JSRuntime, fileData, "application/pdf", $"PO-{poHeader.Ponumber}.pdf");
    }
    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
        {
            Text = message,
            ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
        });
    }

    List<Milestone> milestones = new List<Milestone>();

    void NavigateToActivity(int scheduleMid)
    {
        string url = $"MobileScheduleActivity/{scheduleMid}";

        NavManager.NavigateTo(url);
    }

    private async Task ResetState()
    {
        // clean up the storage
        await LocalStorage.RemoveItem(UniqueStorageKey);
        await ScheduleTreeList.SetStateAsync(null); // pass null to reset the state
    }

    private void ReloadPage()
    {
        JSRuntime.InvokeVoidAsync("window.location.reload");
    }

    private async Task SetState()
    {
        var state = ScheduleTreeList.GetState();
        state.ExpandedItems = null; // Set not to expand, otherwise it will overload localstorage and exceed quota
        await LocalStorage.SetItem(UniqueStorageKey, state);
    }
}
