﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class SchePackageItem
{
    public int PackageItemId { get; set; }

    public string ItemName { get; set; } = null!;

    public int PackageId { get; set; }

    public string? WebLink { get; set; }

    public string? Notes { get; set; }

    public DateTime? FinishDate { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public string? Depart { get; set; }

    public string? Mg { get; set; }

    public virtual ScheStartPackage Package { get; set; } = null!;

    public virtual ICollection<SchePackageAssignment> SchePackageAssignments { get; set; } = new List<SchePackageAssignment>();
}
