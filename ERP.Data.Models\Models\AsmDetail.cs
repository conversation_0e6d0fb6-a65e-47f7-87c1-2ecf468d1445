﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class AsmDetail
{
    public int AsmDetailId { get; set; }

    public int AsmHeaderId { get; set; }

    public int? PeHeader { get; set; }

    public int? AsbDetailNumber { get; set; }

    public int MasterItemId { get; set; }

    public int? Ordinality { get; set; }

    public double? Factor { get; set; }

    public int? SessionId { get; set; }

    public string? UseItemFormula { get; set; }

    public int? CalculationCode { get; set; }

    public string? Formula { get; set; }

    public string? Calculation { get; set; }

    public int? DetailType { get; set; }

    public int? ItemTableHeaderId { get; set; }

    public string? SelectAtTakeoffDesc { get; set; }

    public string? SelectAtTakeoffUnit { get; set; }

    public int? BomClassId { get; set; }

    public string? OptionItemNotes { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual AsmHeader AsmHeader { get; set; } = null!;

    public virtual BomClass? BomClass { get; set; }

    public virtual MasterItem MasterItem { get; set; } = null!;
}
