using Azure.Identity;
using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using ERP.Data.Models;
using ERP.Web.Data;
using ERP.Web.DocusignServices;
using ERP.Web.ExtensionMethods;
using ERP.Web.Models;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Graph.Models.ExternalConnectors;
using Microsoft.Identity.Web;
using Microsoft.Identity.Web.UI;

using NLog;

LogManager.LoadConfiguration(string.Concat(Directory.GetCurrentDirectory(), "/nlog.config"));
var builder = WebApplication.CreateBuilder(args);



// Add services to the container.
var initialScopes = builder.Configuration["DownstreamApi:Scopes"]?.Split(' ') ?? builder.Configuration["MicrosoftGraph:Scopes"]?.Split(' ');
builder.Services.AddAuthentication(OpenIdConnectDefaults.AuthenticationScheme)
    .AddMicrosoftIdentityWebApp(builder.Configuration.GetSection("AzureAd"))
    .EnableTokenAcquisitionToCallDownstreamApi(initialScopes)
.AddDownstreamApi("DownstreamApi", builder.Configuration.GetSection("DownstreamApi")).UseHttpClientTimeout("DownstreamApi", TimeSpan.FromMinutes(10))
.AddDownstreamApi("DownstreamApi2", builder.Configuration.GetSection("DownstreamApi2"))
.AddDownstreamApi("BuisinessCentral", builder.Configuration.GetSection("BusinessCentral"))
.AddMicrosoftGraph(builder.Configuration.GetSection("GraphApi"))
            .AddInMemoryTokenCaches();


//var initialScopes = builder.Configuration["DownstreamApi:Scopes"]?.Split(' ') ?? builder.Configuration["MicrosoftGraph:Scopes"]?.Split(' ');
//builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
//    .AddMicrosoftIdentityWebApp(builder.Configuration.GetSection("AzureAd"))
//    .EnableTokenAcquisitionToCallDownstreamApi(initialScopes)
//.AddDownstreamApi("DownstreamApi", builder.Configuration.GetSection("DownstreamApi"))
//.AddMicrosoftGraph(builder.Configuration.GetSection("GraphApi"))
//            .AddInMemoryTokenCaches();


//builder.Services.AddAuthentication(OpenIdConnectDefaults.AuthenticationScheme)
//    .AddMicrosoftIdentityWebApp(builder.Configuration.GetSection("AzureAd"))
//    .EnableTokenAcquisitionToCallDownstreamApi(initialScopes)
//.AddDownstreamWebApi("DownstreamApi", builder.Configuration.GetSection("DownstreamApi"))
//.AddMicrosoftGraph(builder.Configuration.GetSection("GraphApi"))
//            .AddInMemoryTokenCaches();


builder.Services.AddControllersWithViews()
    .AddMicrosoftIdentityUI();

builder.Services.Configure<OpenIdConnectOptions>(options =>
{
    options.TokenValidationParameters.RoleClaimType = "roles";
    options.TokenValidationParameters.NameClaimType = "name";
});

builder.Services.AddAuthorization(options =>
{
    // By default, all incoming requests will be authorized according to the default policy
    options.FallbackPolicy = options.DefaultPolicy;
});

//Telerik
builder.Services.AddTelerikBlazor();

builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor()
    .AddMicrosoftIdentityConsentHandler();

builder.Services.Configure<HubOptions>(options =>
{
    options.MaximumReceiveMessageSize = null;// 10 * 1024 * 1024; // 10MB or use null 
});

//builder.Services.AddSingleton<SubdivisionService>();
//builder.Services.AddSingleton<OptionService>();
builder.Services.AddScoped<BCAPILogService>();
builder.Services.AddScoped<SubdivisionJobPickService>();
builder.Services.AddScoped<SubdivisionService>();
builder.Services.AddScoped<OptionService>();
builder.Services.AddScoped<ItemService>();
builder.Services.AddScoped<PlanService>();
builder.Services.AddScoped<TradeService>();
builder.Services.AddScoped<SupplierMessagesService>();
builder.Services.AddScoped<SalesPriceService>();
builder.Services.AddScoped<CostService>();
builder.Services.AddScoped<SalesConfigService>();
builder.Services.AddScoped<JobDocumentService>();
builder.Services.AddScoped<BudgetService>();
builder.Services.AddScoped<PoService>();
builder.Services.AddScoped<PaymentService>();
builder.Services.AddScoped<ScheduleService>();
builder.Services.AddScoped<ColorSchemeService>();
builder.Services.AddScoped<AttributeService>();
builder.Services.AddScoped<SelectedOptionsService>();
builder.Services.AddScoped<AttributeItemPickService>();
builder.Services.AddScoped<ColorSchemePickService>();
builder.Services.AddScoped<AddendaService>();
builder.Services.AddScoped<HOAService>();
builder.Services.AddScoped<ScheduleStartPackageService>();
builder.Services.AddScoped<StartPackageDocumentService>();
builder.Services.AddScoped<MaterialColorDocumentService>();
builder.Services.AddScoped<SupplierService>();
builder.Services.AddScoped<JobService>();
builder.Services.AddScoped<ReleaseScheduleService>();
builder.Services.AddSingleton<IDocusignAuthenticator, DocusignAuthenticator>();
builder.Services.AddSingleton<IDocusignService, DocusignService>();
builder.Services.AddScoped<LocalStorage>();
builder.Services.AddScoped<VPOItemPickService>();
builder.Services.AddScoped(sp => new HttpClient
{
    BaseAddress = new Uri("https://localhost:44386")
});


builder.Services.AddHttpClient<ItemService>(client => client.BaseAddress = new Uri("https://localhost:7292/"));
builder.Services.AddHttpClient<BudgetService>(client => client.BaseAddress = new Uri("https://localhost:7292/"));
builder.Services.AddHttpClient<OptionService>(client => client.BaseAddress = new Uri("https://localhost:7292/"));
builder.Services.AddHttpClient<SubdivisionService>(client => client.BaseAddress = new Uri("https://localhost:7292/"));
builder.Services.AddHttpClient<PlanService>(client => client.BaseAddress = new Uri("https://localhost:7292/"));
builder.Services.AddHttpClient<TradeService>(client => client.BaseAddress = new Uri("https://localhost:7292/"));
builder.Services.AddHttpClient<SupplierMessagesService>(client => client.BaseAddress = new Uri("https://localhost:7292/"));
builder.Services.AddHttpClient<SalesPriceService>(client => client.BaseAddress = new Uri("https://localhost:7292/"));
//builder.Services.AddHttpClient<CostService>(client => client.BaseAddress = new Uri("https://localhost:44386/"));
builder.Services.AddHttpClient<CostService>(client => client.BaseAddress = new Uri("https://vmerpservices.azurewebsites.net/"));
builder.Services.AddHttpClient<SalesConfigService>(client => client.BaseAddress = new Uri("https://vmerpservices.azurewebsites.net/"));
//builder.Services.AddHttpClient<HOAService>(client => client.BaseAddress = new Uri("https://vmerpservices.azurewebsites.net/"));
var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.MapFallbackToPage("/_Host");

app.UseEndpoints(endpoints =>
{
    endpoints.MapRazorPages();
    endpoints.MapBlazorHub();
});

app.Run();
