﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class TbIndividual
{
    public int Id { get; set; }

    public int BuilderId { get; set; }

    public string FirstName { get; set; } = null!;

    public string? Mi { get; set; }

    public string LastName { get; set; } = null!;

    public string? Title { get; set; }

    public string? JobPosition { get; set; }

    public string Address1 { get; set; } = null!;

    public string? Address2 { get; set; }

    public string? City { get; set; }

    public string? State { get; set; }

    public string? Country { get; set; }

    public string? Zip { get; set; }

    public string? HomePhone { get; set; }

    public string? Fax { get; set; }

    public string? WorkPhone { get; set; }

    public string? WorkExt { get; set; }

    public string? MobilePhone { get; set; }

    public string? Pager { get; set; }

    public string? Email { get; set; }

    public int? LastUser { get; set; }

    public DateTime? LastChanged { get; set; }

    public string? Entity { get; set; }

    public bool DoNotSendEmail { get; set; }

    public bool DoNotSendLetter { get; set; }

    public bool DoNotCall { get; set; }

    public DateTime? DoNotSendEmailLastChanged { get; set; }

    public DateTime? DoNotSendLetterLastChanged { get; set; }

    public DateTime? DoNotCallLastChanged { get; set; }

    public int BounceStatus { get; set; }

    public bool DoNotSendText { get; set; }

    public DateTime? DoNotSendTextLastChanged { get; set; }

    public int? StateId { get; set; }

    public int? CountryId { get; set; }

    public string? ImageUrl { get; set; }

    public string? FacebookId { get; set; }

    public DateTime? FacebookLastLookedFor { get; set; }

    public string? TwitterId { get; set; }

    public string? TwitterName { get; set; }

    public DateTime? TwitterLastLookedFor { get; set; }

    public string? FoursquareId { get; set; }

    public DateTime? FoursquareLastLookedFor { get; set; }

    public bool? OldDoNotEmail { get; set; }

    public DateTime? OldDoNotEmailLastChanged { get; set; }

    public bool? EmailOptIn { get; set; }

    public bool? DoNotEmail { get; set; }

    public DateTime? DoNotEmailLastChanged { get; set; }

    public DateTime? DateOfBirth { get; set; }

    public DateTime Createddatetime { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? Dwuserid { get; set; }

    public bool? IsActive { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public int? Vid { get; set; }

    public virtual ICollection<TbMember> TbMembers { get; set; } = new List<TbMember>();
}
