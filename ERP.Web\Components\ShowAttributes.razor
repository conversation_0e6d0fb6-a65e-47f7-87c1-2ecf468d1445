﻿@using ERP.Data.Models.Dto;
@inject ColorSchemeService ColorSchemeService
@inject OptionService OptionService
@inject PlanService PlanService
@inject SubdivisionService SubdivisionService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="400px"
               Height="400px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Attributes for Selected Option: @SelectedOption.OptionCode - @SelectedOption.OptionDesc
    </WindowTitle>
    <WindowContent>
            <TelerikGrid Data=@SelectedOption.BuildAttributeItems
                       >
                <GridColumns>
                <GridColumn Field="AttrGroupAssignment.AttributeGroup.Description" Title="Group Desc">
                </GridColumn>
                <GridColumn Field="AttrGroupAssignment.AttributeItem.Description" Title="Item Desc">
                </GridColumn>                    
                </GridColumns>
                <GridToolBarTemplate>
                    @{
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>                        
                    }
                </GridToolBarTemplate>
            </TelerikGrid>
           
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    [Parameter]    
    public bool IsModalVisible { get; set; }
    [Parameter]
    public TbBuiltOptionDto? SelectedOption { get; set; }
   
    public async Task Show()
    {
        IsModalVisible = true;
        if(SelectedOption != null)
        {
            SelectedOption.BuildAttributeItems = SelectedOption.BuildAttributeItems.Where(x => !string.IsNullOrWhiteSpace(x.AttrGroupAssignment.AttributeItem.Description) && x.AttrGroupAssignment.AttributeItem.Description != "Selection Needed" && x.AttrGroupAssignment.AttributeItem.Description != "Not Applicable").ToList();//exclude blank selections
        }
        StateHasChanged();
    }
    protected override async Task OnInitializedAsync()
    {
    }
   
}
