﻿@using ERP.Data.Models.Dto;
@inject SelectedOptionsService SelectedOptionsService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JsRuntime
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>


@if (BuyerData != null)
{
    <p><strong>Status: </strong>@LotStatus</p>
    foreach(var buyer in BuyerData)
    {
        //var firstBuyer = BuyerData.First(x => x.ListOrder == 0);//ListOrder 0 = Primary Buyer
        
        <p><strong>Handshake Buyer: </strong>@($"{buyer.FirstName} {buyer.LastName}") </p>
        <p><strong>Handshake Buyer Email: </strong> @buyer.Email</p>
        <p><strong>Handshake Buyer Address: </strong> @buyer.Address</p>
        <p><strong>Handshake Buyer Phone: </strong> @buyer.Phone</p>
        <br/>
        @* if (BuyerData.Count > 1)
        {
            <p>Co-Buyers</p>
            foreach (var coBuyer in BuyerData.Where(x => x.ListOrder != 0))//ListOrder > 0 = CoBuyer
            {
                <p><strong>Co Buyer: </strong>@($"{coBuyer.FirstName} {coBuyer.LastName}") </p>
                <p><strong>Co Buyer Email: </strong> @coBuyer.Email</p>
                <p><strong>Co Buyer Address: </strong> @coBuyer.Address</p>
                <p><strong>Co Buyer Phone: </strong> @coBuyer.Phone</p>
            }
        } *@
        <p><strong>ERP Customer: </strong>@($"{ERPCustomerData.CustomerName}") </p>
        <p><strong>ERP Customer Email: </strong> @ERPCustomerData.Email</p>
        <p><strong>ERP Customer Address: </strong> @ERPCustomerData.Address1</p>
        <p><strong>ERP Customer Phone: </strong> @ERPCustomerData.HomePhone</p>
        if (buyer.IsCancelled == true)
        {
            <TelerikButton Title="Push Cancellation Data" Icon="@FontIcon.Check" OnClick="PushCustomerData" Class="k-button-success">Push Cancellation Data</TelerikButton>
        }
        else
        {
            <TelerikButton Title="Send Customer Data to BC" Icon="@FontIcon.Check" OnClick="PushCustomerData" Class="k-button-success">Send Customer Data to BC</TelerikButton>
        }
    }
}

@code {


    [Parameter]
    public string? JobNumber { get; set; }
    private string? SpecOrBuyer { get; set; } = "";
    public List<BuyerDto>? BuyerData { get; set; }
    public CustomerDto? ERPCustomerData { get; set; } = new CustomerDto();
    public string? LotStatus { get; set; }//spec, sold, tbb
    private bool loading { get; set; } = false;

    [Parameter]
    public EventCallback<ResponseModel<byte[]>> HandlePushCustomerNavSubmit { get; set; }
    [Parameter]
    public EventCallback<ResponseModel<string>> HandlePushCustomerSubmit { get; set; }
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        if (JobNumber != null)
        {
            loading = true;
            var getBuyerTask = SelectedOptionsService.GetBuyersByJobAsync(JobNumber);
            var getExistingERPCustomerTask = SelectedOptionsService.GetJobCustomerAsync(JobNumber);
            await Task.WhenAll(new Task[] { getBuyerTask, getExistingERPCustomerTask });
            if (!getBuyerTask.Result.IsSuccess || !getExistingERPCustomerTask.Result.IsSuccess)
            {
                ShowSuccessOrErrorNotification(getExistingERPCustomerTask.Result.Message, getExistingERPCustomerTask.Result.IsSuccess);
                ShowSuccessOrErrorNotification(getBuyerTask.Result.Message, getBuyerTask.Result.IsSuccess);
            }
            SpecOrBuyer = getBuyerTask.Result.Message;//TODO: cleaner way to do this
            BuyerData = getBuyerTask.Result.Value;
            LotStatus = getBuyerTask.Result.Message;//TODO: put this someplace cleaner
            ERPCustomerData = getExistingERPCustomerTask.Result.Value;
            loading = false;
            StateHasChanged();
        }
    }

    protected async Task PushCustomerData()
    {
        //TODO: show indication if customer data already pushed
        if (JobNumber != null)
        {
            var response = await SelectedOptionsService.PushCustomerDataAsync(JobNumber);
            if (response.IsSuccess)
            {
                var getExistingERPCustomerTask = await SelectedOptionsService.GetJobCustomerAsync(JobNumber);//refresh
                ERPCustomerData = getExistingERPCustomerTask.Value;
               
            }
            await HandlePushCustomerSubmit.InvokeAsync(response);
        }
    }
    protected async Task PushCustomerDataNav()
    {
        //TODO: show indication if customer data already pushed
        if (JobNumber != null)
        {
            var response = await SelectedOptionsService.PushCustomerDataNavAsync(JobNumber);
            if (response.IsSuccess)
            {
                var getExistingERPCustomerTask = await SelectedOptionsService.GetJobCustomerAsync(JobNumber);//refresh
                ERPCustomerData = getExistingERPCustomerTask.Value;

            }
            if (response.Value != null)
            {
                var fileData = response.Value;
                DemoFileExporter.Save(JsRuntime, fileData, "application/xml", $"Customer-Job-{DateTime.Now.ToString("MM-dd-yyyy")}.xml");
            }
            await HandlePushCustomerNavSubmit.InvokeAsync(response);
        }
    }
    private void ShowSuccessOrErrorNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
