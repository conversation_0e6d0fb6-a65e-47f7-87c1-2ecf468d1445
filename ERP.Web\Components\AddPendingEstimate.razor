﻿@using ERP.Data.Models;
@inject SubdivisionService SubdivisionService
@inject BudgetService BudgetService
@inject PlanService PlanService
@inject OptionService OptionService
@inject ItemService ItemService
@using ERP.Data.Models.Dto;

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Estimate
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@EstimateToAdd" OnValidSubmit="@HandleValidAddSubmit">            
            <div class="mb-3">
                <label class="form-label">Reference Number</label><br />
                <TelerikTextBox @bind-Value="@EstimateToAdd.Estoption.Estheader.ReferenceNumber"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Reference Description</label><br />
                <TelerikTextBox @bind-Value="@EstimateToAdd.Estoption.Estheader.ReferenceDesc"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Estimate Description</label><br />
                <TelerikTextBox Enabled="false" @bind-Value="@EstimateToAdd.Estoption.Estheader.EstimateDescPe"></TelerikTextBox>
            </div>
            <p>
                <label>List Items by:</label>
                <TelerikDropDownList @bind-Value="@SelectedItemDisplayCategory"
                                     Data="@ItemDisplayCategoriesList"
                                     TextField="DisplayCategory"
                                     ValueField="DisplayCategoryId"
                                     DefaultText="Select Category to list Items"
                                     Width="100%">
                </TelerikDropDownList>
            </p>

            @if (SelectedItemDisplayCategory == 1)
            {
                <p>
                    <label>Master Plan</label>
                    @if (AllMasterPlans != null && AllMasterPlans.Count != 0)
                    {
                        <TelerikDropDownList @bind-Value="@SelectedMasterPlanId"
                                             Data="@AllMasterPlans"
                                             TextField="PlanName"
                                             ValueField="MasterPlanId"
                                             DefaultText="Select Master Plan"
                                             Filterable="true"
                                             OnChange="@CascadePlanOptions"
                                             ScrollMode="@DropDownScrollMode.Virtual"
                                             ItemHeight="30"
                                             PageSize="20"
                                             Width="100%" Context="planDropdownContext">
                            <ItemTemplate>@planDropdownContext.PlanNum - @planDropdownContext.PlanName </ItemTemplate>
                        </TelerikDropDownList>
                    }
                </p>
                @if (SelectedMasterPlanId != 0)
                {
                    <p>
                        <label>Master Plan Options</label>
                        <TelerikDropDownList @bind-Value="@SelectedOptionId"
                                             Data="@AllOptionsInMasterPlan"
                                             TextField="AssemblyDesc"
                                             ValueField="AsmHeaderId"
                                             DefaultText="Select Option"
                                             OnChange="@CascadeItems"
                                             Filterable="true"
                                             Width="100%" Context="optinDropdownContext">
                            <ItemTemplate>@optinDropdownContext.AssemblyCode - @optinDropdownContext.AssemblyDesc </ItemTemplate>
                        </TelerikDropDownList>
                    </p>
                }
                @if (SelectedOptionId != 0)
                {
                    <p>
                        <label>Item</label>
                        <TelerikDropDownList @bind-Value="@EstimateToAdd.MasterItemsId"
                                             TextField="ItemDesc"
                                             ValueField="MasterItemId"
                                             Filterable="true"
                                             FilterOperator="StringFilterOperator.Contains"
                                             Data="@AllItems"
                                             DefaultText="Select Item"
                                             Width="100%" Context="itemDropdownContext">
                            <ItemTemplate>@itemDropdownContext.ItemNumber - @itemDropdownContext.ItemDesc </ItemTemplate>
                        </TelerikDropDownList>
                    </p>
                }
            }
            @if (SelectedItemDisplayCategory == 2)
            {
                <p>
                    <label>Trade</label>

                    @if (AllTrades != null && AllTrades.Count != 0)
                    {
                        <TelerikDropDownList @bind-Value="@SelectedTradeId"
                                             Data="@AllTrades"
                                             TextField="TradeName"
                                             ValueField="TradeId"
                                             DefaultText="Select Trade"
                                             Filterable="true"
                                             OnChange="@CascadeActivities"
                                             ScrollMode="@DropDownScrollMode.Virtual"
                                             ItemHeight="30"
                                             PageSize="20"
                                             Width="100%">
                        </TelerikDropDownList>
                    }
                </p>
                @if (SelectedTradeId != 0)
                {
                    <p>
                        <label>Activity</label>
                        <TelerikDropDownList @bind-Value="@SelectedActivityId"
                                             Data="@AllActivitiesInTrade"
                                             TextField="Activity"
                                             ValueField="PactivityId"
                                             DefaultText="Select Activity"
                                             OnChange="@CascadeItems"
                                             Filterable="true"
                                             Width="100%">
                        </TelerikDropDownList>
                    </p>
                }
                @if (SelectedActivityId != 0)
                {
                    <p>
                        <label>Item</label>
                        <TelerikDropDownList @bind-Value="@EstimateToAdd.MasterItemsId"
                                             TextField="ItemDesc"
                                             ValueField="MasterItemId"
                                             Filterable="true"
                                             Data="@AllItems"
                                             DefaultText="Select Item"
                                             Width="100%">
                        </TelerikDropDownList>
                    </p>
                }
            }
            <button type="submit" class="btn btn-primary">Add</button>
            <button type="button" @onclick="CancelAddItem" class="btn btn-secondary">Cancel</button>
            <div style=@submittingStyle>Adding. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public EstdetailDto EstimateToAdd { get; set; } = new EstdetailDto() { Estoption = new EstoptionDto() { Estheader = new EstheaderDto() } };
    public List<SubdivisionDto>? AllSubdivisions { get; set; }
    private string submittingStyle = "display:none";
    public CombinedPOBudgetTreeModel? SelectedBudgetEstimate { get; set; } = new CombinedPOBudgetTreeModel() { Estoption = new EstoptionDto(), Estheader = new EstheaderDto() };
    public ERP.Web.Components.AddItemToBudget? AddItemToBudgetModal { get; set; }
    public EstoptionDto? ResponseOption { get; set; }
    [Parameter]
    public EventCallback<ResponseModel<CombinedPOBudgetTreeModel>> HandleAddSubmit { get; set; }
    [Parameter]
    public string? JobNumber { get; set; }
    private List<ReferenceTypeDto>? ReferenceTypes { get; set; }
    private List<EstimateSourceDto>? EstimateSources { get; set; }
    public List<string>? AllVarianceCategories { get; set; }
    public string? SelectedVarianceCategory { get; set; }
    private List<string>? TakeoffTypes { get; set; }
    private string? SelectedTakeoffType { get; set; }
    public string VarianceCategory { get; set; } = "CO";
    public List<TradeDto>? AllTrades { get; set; }
    public List<MasterPlanDto>? AllMasterPlans { get; set; }
    public List<MasterOptionHeaderModel>? AllMasterOptions { get; set; }
    public List<AsmHeaderModel>? AllOptionsInMasterPlan { get; set; }
    public int SelectedTradeId;
    public List<PactivityModel>? AllActivitiesInTrade { get; set; }
    public int SelectedActivityId;
    public List<ModelManagerItemModel>? AllItems { get; set; }
    public int SelectedItemId;
    public int SelectedOptionId;
    public int SelectedMasterPlanId;
    public List<ItemDisplayCategory> ItemDisplayCategoriesList { get; set; } = new List<ItemDisplayCategory>()
    {
        new ItemDisplayCategory()
        {
            DisplayCategory = "Plan / Option / Item",
            DisplayCategoryId = 1
        },
        new ItemDisplayCategory()
        {
            DisplayCategory = "Trade / Activity / Item",
            DisplayCategoryId = 2
        }
    };
    public int SelectedItemDisplayCategory { get; set; } = 2;

    protected override async Task OnInitializedAsync()
    {
        var tradeTask = ItemService.GetTradesAsync();
        var varianceCategoriesTask = BudgetService.GetVarianceCategoriesAsync();
        var plansTask = PlanService.GetMasterPlansAsync();
        var refTypesTask = BudgetService.GetReferenceTypesAsync();
        await Task.WhenAll(tradeTask, varianceCategoriesTask, plansTask, refTypesTask);
        AllVarianceCategories = varianceCategoriesTask.Result.Value;
        AllMasterPlans = plansTask.Result.Value;
        AllTrades = tradeTask.Result.Value;
        ReferenceTypes = refTypesTask.Result.Value;
        TakeoffTypes = new List<string>() { "Item Takeoff", "Option Takeoff", "Pending/Custom Estimate" };
    }
    public async Task Show()
    {
        IsModalVisible = true;
        EstimateToAdd = new EstdetailDto() 
        { 
            Estoption = new EstoptionDto() 
            { 
                Estheader = new EstheaderDto()   
                { 
                    JobNumber = JobNumber, 
                    ReferenceNumber = "Custom",
                    EstimateDescPe = "Pending/Custom Estimate" 
                } 
            } 
        };
        StateHasChanged();
    }
    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        var addEstResponse = await BudgetService.AddEstheaderAsync(EstimateToAdd.Estoption.Estheader);
        if (addEstResponse.IsSuccess)
        {
            //add option
            var selectedOption = SelectedItemDisplayCategory == 1 ? AllOptionsInMasterPlan.FirstOrDefault(x => x.AsmHeaderId == SelectedOptionId) : null;
            EstimateToAdd.Estoption.OptionDesc = SelectedItemDisplayCategory == 2 ? "Base House" : selectedOption.AssemblyDesc;
            EstimateToAdd.Estoption.OptionNumber = SelectedItemDisplayCategory == 2 ? "Base House" : selectedOption.AssemblyCode;
            EstimateToAdd.Estoption.EstheaderId = addEstResponse.Value.EstheaderId;
            var responseOption = await BudgetService.AddEstoptionAsync(EstimateToAdd.Estoption);
            if (responseOption.IsSuccess)
            {
                //add item
                EstimateToAdd.EstoptionId = responseOption.Value.EstoptionId;
                EstimateToAdd.Estoption = responseOption.Value;
                var responseItem = await BudgetService.AddEstdetaiAsync(EstimateToAdd);
                if (responseItem.IsSuccess)
                {
                    var responseEst = CreateTreeItemToReturn(responseItem.Value);
                    await HandleAddSubmit.InvokeAsync(new ResponseModel<CombinedPOBudgetTreeModel> { Value = responseEst, IsSuccess = true, Message = responseItem.Message });
                }
                else
                {
                    await HandleAddSubmit.InvokeAsync(new ResponseModel<CombinedPOBudgetTreeModel> { Value = null, IsSuccess = false, Message = responseItem.Message });
                }
            }
            else
            {
                await HandleAddSubmit.InvokeAsync(new ResponseModel<CombinedPOBudgetTreeModel> { Value = null, IsSuccess = false, Message = responseOption.Message });
            }
        }
        else
        {            
            await HandleAddSubmit.InvokeAsync(new ResponseModel<CombinedPOBudgetTreeModel> { Value = null, IsSuccess = false, Message = addEstResponse.Message });
        }
        submittingStyle = "display:none";
        SelectedActivityId = 0;
        SelectedOptionId = 0;
        SelectedTradeId = 0;
        SelectedMasterPlanId = 0;
        SelectedItemDisplayCategory = 2;
        StateHasChanged();
        IsModalVisible = false;

    }
    public void CancelAddItem()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;
    }
   
    private async Task LoadAllMasterOptionsAsync()
    {
        AllMasterOptions = (await OptionService.GetAllMasterOptionsAsync()).Value;
    }
    private async Task LoadAllMasterPlansAsync()
    {
        var allMasterPlansResponse = (await PlanService.GetMasterPlansAsync()).Value;
        AllMasterPlans = allMasterPlansResponse;
    }
    private async Task LoadTradesAsync()
    {
        AllTrades = (await ItemService.GetTradesAsync()).Value;
    }
    private async Task CascadePlanOptions()
    {
        AllOptionsInMasterPlan = (await ItemService.GetAssembliesInPlanAsync(SelectedMasterPlanId)).Value;
        SelectedOptionId = 0;
    }
    private async Task CascadeActivities(object newVal)
    {
        AllActivitiesInTrade = (await ItemService.GetActivityByTradeAsync(SelectedTradeId)).Value;
        SelectedActivityId = 0;
    }
    private async Task CascadeItems(object newVal)
    {
        switch (SelectedItemDisplayCategory)
        {
            case 1:
                AllItems = (await ItemService.GetItemsInAssemblyAsync(SelectedOptionId)).Value;//OptionId here is asmheader id, not master optionid
                break;
            case 2:
                AllItems = (await ItemService.GetItemsInActivityAsync(SelectedActivityId)).Value;
                break;
            default:
                break;
        }
    }
    public class ItemDisplayCategory
    {
        public string DisplayCategory { get; set; }
        public int DisplayCategoryId { get; set; }
    }
    private CombinedPOBudgetTreeModel CreateTreeItemToReturn(EstdetailDto responseItem)
    {
        var responseEstimate = new CombinedPOBudgetTreeModel()
            {
                Estheader = responseItem.Estoption.Estheader,
                Id = Guid.NewGuid(),
                JobNumber = JobNumber,
                EstimateNumber = responseItem.Estoption.Estheader.EstimateNumber,
                HasChildren = true,
                IsIssued = false,
                IssueEnabled = true,
                IsPendingCustomEstimate = true,
                Children = new List<CombinedPOBudgetTreeModel>()
            {
                new CombinedPOBudgetTreeModel
                {

                    Id = Guid.NewGuid(),
                    JobNumber = JobNumber,
                    ReleaseCode = responseItem.Estactivity.Releasecode,
                    Estoption = responseItem.Estoption,
                    EstimateNumber = responseItem.Estoption.Estheader.EstimateNumber,
                    HasChildren = true,
                    IsIssued = false,
                    IssueEnabled = true,
                    IsPendingCustomEstimate = false,
                    PoIsIssued = false,
                    PoIssueEnabled = true,
                    Children = new List<CombinedPOBudgetTreeModel>()
                    {
                        new CombinedPOBudgetTreeModel()
                        {
                             Id = Guid.NewGuid(),
                             JobNumber = JobNumber,
                             Estactivity = responseItem.Estactivity,
                             ActivityTotal = responseItem.Amount,
                             EstimateNumber = responseItem.Estoption.Estheader.EstimateNumber,
                             HasChildren = true,
                             IsIssued = false,
                             IssueEnabled = true,
                             IsPendingCustomEstimate = false,
                             PoIsIssued = false,
                             PoIssueEnabled = true,
                             Children = new List<CombinedPOBudgetTreeModel>(){
                                new CombinedPOBudgetTreeModel()
                                {
                                    Id = Guid.NewGuid(),
                                    JobNumber = JobNumber,
                                    EstimateNumber = responseItem.Estoption.Estheader.EstimateNumber,
                                    Estdetail = responseItem,
                                    Estactivity = responseItem.Estactivity,
                                    HasChildren = false,
                                    IsIssued = false,
                                    IssueEnabled = true,
                                    IsPendingCustomEstimate = false,
                                    PoIsIssued = false,
                                    PoIssueEnabled = true,
                                }
                            }
                        }
                    }
                }
            }
            };
        //Set the parent ids
        foreach (var estOption in responseEstimate.Children)
        {
            estOption.ParentId = responseEstimate.Id;
            foreach (var estActivity in estOption.Children)
            {
                estActivity.ParentId = estOption.Id;
                foreach (var estDetail in estActivity.Children)
                {
                    estDetail.ParentId = estActivity.Id;
                }
            }
        }
        return responseEstimate;
    }
}
