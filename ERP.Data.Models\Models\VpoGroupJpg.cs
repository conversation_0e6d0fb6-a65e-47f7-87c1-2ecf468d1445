﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class VpoGroupJpg
{
    public int VpoGroupId { get; set; }

    public string JobPostingGroupCode { get; set; } = null!;

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual JobPostingGroup JobPostingGroupCodeNavigation { get; set; } = null!;

    public virtual VpoGroup VpoGroup { get; set; } = null!;
}
