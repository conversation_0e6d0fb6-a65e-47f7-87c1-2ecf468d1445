﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class SalesConfigCombinedTreeModel
    {
        //TODO: make an interface instead
        public bool? IsDummyForSearch { get; set; }//trying hack to keep treelevels expandable on search
        public Guid Id { get; set; }
        public Guid? ParentId { get; set; }
        public bool HasChildren { get; set; }
        public int DisplayRowIndex { get; set; }
        public string? DragClue { get; set; }
        public string? JobsString { get; set; }//to make search on jobs work when only subdivision is showing in the tree
        public string? AttributeSelectionsString { get; set; }
        public SalesconfigcoDto? Salesconfigco { get; set; }
        public SalesconfigDto? Salesconfig { get; set; }
        public SalesconfigoptionDto? Salesconfigoption { get; set; }
        public SalesconfigcooptionDto? Salesconfigcooption { get; set; }

        public int? SalesConfigId { get; set; }
        public int? SalesConfigCoId { get; set; }
        public int? SalesConfigOptionId { get; set; }
        public int? SalesConfigCoOptionId { get; set; }
        public int? SSConfigurationId { get; set; }
        public bool? CustomOption { get; set; }
        public bool Approvable { get; set; } //can't reapprove, can't approve if there is custom option that still needs estimate, can't approve out of order, etc.
        public DateTime? Saledate { get; set; }

        public DateTime? Ratificationdate { get; set; }

        public DateTime? Estimatedsettlementdate { get; set; }

        public DateTime? Closingdate { get; set; }

        public DateTime? Canceldate { get; set; }

        public string? Status { get; set; }

        public string? SalesContact { get; set; }

        public int? PhasePlanId { get; set; }

        public double? Baseprice { get; set; }

        public string? LotSwing { get; set; }

        public string? Ownername { get; set; }
        public string? JobNumber { get; set; }
        public bool? IsActive { get; set; }
        public string? LotNumber { get; set; }
        public string? LotAddress { get; set; } 
        public string? LotCity { get; set; }
        public int? CoNumber { get; set; }

        public string? CoStatus { get; set; }

        public DateTime? CoStatusdate { get; set; }

        public string? SentToPurchasing { get; set; }

        public double? LotPremium { get; set; }

        public string? IsApproved { get; set; }
        public bool BoolIsApproved { get; set; }

        public string? SalesconfigcoAction { get; set; }

        public double? SalesconfigcoPrice { get; set; }

        public double? SalesconfigcoQuantityChange { get; set; }

        public string? SalesconfigcoNotes { get; set; }

        public string? AssociatedEstimate { get; set; }

        public string? SsOptioncode { get; set; }

        public string? SalesconfigcoSelections { get; set; }

        public string? ScDescription { get; set; }
        public int? PlanOptionId { get; set; }

        public double? OptionPrice { get; set; }

        public double? OptionQuantity { get; set; }

        public string? OptionNotes { get; set; }

        public string? OptionSelections { get; set; }

        public double? SellingPrice { get; set; }
        public double? Quantity { get; set; }
        public double? ListPrice { get; set; }
        public double? UnitCost { get; set; }
        public string? OptionDesc { get; set; }
        public string? OptionName { get; set; }
        public string? OptionCode { get; set; }
        public string? PlanName { get; set; }
        public string? PlanNum { get; set; }
        public string? SubdivisionName { get; set; }
        public int? SubdivisionId { get; set; }
        public double? SumOptionPrice { get; set; }
        public double? SumBaseHouseAndOptionPrice { get; set; }
        public double? PriceIncentive { get; set; }

        public double? OptionIncentive { get; set; }

        public double? UpfrontIncentive { get; set; }

        public double? LotPremiumIncentive { get; set; }
        public string? OwnerNamePrefix { get; set; }

        public string? OwnerFirstName { get; set; }

        public string? OwnerMiddleName { get; set; }

        public string? OwnerLastName { get; set; }

        public string? OwnerNameSuffix { get; set; }

        public string? SsAction { get; set; }

        public string? Owneraddress1 { get; set; }

        public string? Owneraddress2 { get; set; }

        public string? Ownersuburb { get; set; }

        public string? Ownerstate { get; set; }

        public string? Ownerpostcode { get; set; }

        public string? Ownerphone1 { get; set; }

        public string? Ownerphone2 { get; set; }

        public string? Ownerfax { get; set; }

        public string? Ownermobile { get; set; }

        public string? Owneremail { get; set; }

        public string? UserContact1 { get; set; }

        public string? UserContact2 { get; set; }

        public int? PeeHeaderId { get; set; }

        public string? UserContact1NamePrefix { get; set; }

        public string? UserContact1FirstName { get; set; }

        public string? UserContact1MiddleName { get; set; }

        public string? UserContact1LastName { get; set; }

        public string? UserContact1NameSuffix { get; set; }

        public string? UserContact2NamePrefix { get; set; }

        public string? UserContact2FirstName { get; set; }

        public string? UserContact2MiddleName { get; set; }

        public string? UserContact2LastName { get; set; }

        public string? UserContact2NameSuffix { get; set; }

        public string? IsDeleted { get; set; }

        public string? ConfigSource { get; set; }

        public double? SalesIncentives { get; set; }

        public string? Prevstatus { get; set; }


        public int? SsClientId { get; set; }

        public int? SsSalesagentId { get; set; }

        public string? SalesagentName { get; set; }

        public string? SalesagentEmail { get; set; }

        public string? SalesagentPhone { get; set; }

        public int? SsLotid { get; set; }
        public bool? IsBaseHouseRow { get; set; }
        public bool? IsExpandOptionsRow { get; set; }
        public double? SellingPriceMargin { get; set; }
        public double? SellingPriceMarginPercent { get; set; }
        public double? SalesVariance { get; set; }
        public double? SumOptionCost { get; set; }
        public bool? SalesConcessionWarning { get; set; }
        public bool? RequiresEstimate { get; set; }
 
    }
}
