﻿using AutoMapper;
using DocumentFormat.OpenXml.EMMA;
using ERP.API.Data;
using ERP.API.Utilities;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Data.Models.ExtensionMethods;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Graph.Models.TermStore;
using Microsoft.IdentityModel.Tokens;
using NLog;
using System.Data;
using System.Diagnostics;
using System.Text;

namespace ERP.API.Controllers
{


    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class ScheduleController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly Email _email;
        private readonly IMapper _mapper;
        private readonly IWebHostEnvironment _env;
        private readonly ExportService _service;
        private string contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        private string fileName = "schedules.xlsx";

        public ScheduleController(IConfiguration configuration, ErpDevContext context, IMapper mapper, IWebHostEnvironment env, ExportService service)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _email = new Email(configuration);
            _env = env;
            _service = service;
        }

        [HttpGet("{openOnly}")]
        public async Task<IActionResult> SchedulesAsync(bool openOnly = false)
        {
            try
            {
                //TODO: openonly would also maybe include only released ones? Or published ones?
                var schedules = openOnly ? await _context.Schedules.Include(x => x.JobNumberNavigation.Subdivision).Include(x => x.JobNumberNavigation.LotStatusNavigation).Where(x => x.IsActive == true && x.ActualEndDate == null && x.JobNumberNavigation.LotStatusNavigation.StatusName != "Closed").OrderBy(x => x.JobNumber).ToListAsync() : await _context.Schedules.Where(x => x.IsActive == true).OrderBy(x => x.JobNumber).ToListAsync();
                var schedulesDto = _mapper.Map<List<ScheduleDto>>(schedules);
                return Ok(new ResponseModel<List<ScheduleDto>>() { Value = schedulesDto, IsSuccess = true, Message = "Success getting schedules" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleDto>> { IsSuccess = false, Message = "Failed to get Schedules", Value = new List<ScheduleDto>() });
            }
        }

        [HttpGet("{userId}/{openOnly}")]
        public async Task<IActionResult> SchedulesByContactAsync(string userId, bool openOnly = false)
        {
            try
            {
                //TODO: openonly would also maybe include only released ones? Or published ones?
                var myJobs = _context.JobContacts.Include(x => x.JobNumberNavigation.LotStatusNavigation).Where(x => x.UserId == userId && x.IsActive == true && x.JobNumberNavigation.LotStatusNavigation.StatusName != "Closed").Select(x => x.JobNumber);
                var schedules = openOnly ? await _context.Schedules.Include(x => x.JobNumberNavigation.Subdivision).Where(x => x.IsActive == true && myJobs.Contains(x.JobNumber) && x.ActualEndDate == null).OrderBy(x => x.JobNumber).ToListAsync() : await _context.Schedules.Include(x => x.JobNumberNavigation.Subdivision).Where(x => x.IsActive == true && myJobs.Contains(x.JobNumber)).OrderBy(x => x.JobNumber).ToListAsync();
                var schedulesDto = _mapper.Map<List<ScheduleDto>>(schedules);
                return Ok(new ResponseModel<List<ScheduleDto>>() { Value = schedulesDto, IsSuccess = true, Message = "Success getting schedules" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleDto>> { IsSuccess = false, Message = "Failed to get Schedules", Value = new List<ScheduleDto>() });
            }
        }

        [HttpGet("{subNumber}")]
        public async Task<IActionResult> ScheduleActivityForSupplierAsync(int subNumber)
        {
            try
            {

                var schedules = await _context.ScheduleSactivities.Include(x => x.Sactivity).Include(x => x.ScheduleM.Schedule.JobNumberNavigation).Where(x => x.IsActive == true && x.SubNumber == subNumber && x.ActualEndDate == null).OrderBy(x => x.ScheduleM.Schedule.JobNumber).ToListAsync();
                var schedulesDto = _mapper.Map<List<ScheduleSactivityDto>>(schedules);
                return Ok(new ResponseModel<List<ScheduleSactivityDto>>() { Value = schedulesDto, IsSuccess = true, Message = "Success getting schedules" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return Ok(new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Message = "Failed getting schedules" });
            }
        }

        [HttpGet("{tradeId}/{subdivisionId}")]
        public async Task<IActionResult> OpenScheduleActivitiesForTradeSubdivisionAsync(int tradeId, int subdivisionId)
        {
            try
            {
                var jobs = subdivisionId != 1 ? // division default = 1 
                                    await _context.Jobs.Where(x => x.SubdivisionId == subdivisionId).ToListAsync() :
                                    await _context.Jobs.ToListAsync();
                var jobNumbers = jobs.Select(x => x.JobNumber).ToList();
                var openScheduleSactivities = await _context.ScheduleSactivities.Include("Sactivity").Include("Schedule").Include("SubNumberNavigation").Where(x => x.Sactivity.TradeId == tradeId && jobNumbers.Contains(x.Schedule.JobNumber) && x.ActualStartDate == null && x.IsActive == true).ToListAsync();
                var openScheduleSactivitiesDto = _mapper.Map<List<ScheduleSactivityDto>>(openScheduleSactivities);

                return new OkObjectResult(new ResponseModel<List<ScheduleSactivityDto>> { Value = openScheduleSactivitiesDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleSactivityDto>> { IsSuccess = false, Message = "Failed to fetch Open Schedule Activities for Trade Subdivision" });
            }
        }

        [HttpGet("{userName}")]
        public async Task<IActionResult> ScheduleActivityForSuperAsync(string userName)
        {
            try
            {
                //var myJobs = await _context.JobContacts.Include(x => x.JobNumberNavigation.LotStatusNavigation).Where(x => x.UserId == userName && x.JobNumberNavigation.LotStatusNavigation.StatusName != "Closed" && x.IsActive == true).Select(x => x.JobNumber).Distinct().ToListAsync();
                //var schedules = await _context.ScheduleSactivities.Include(x => x.Sactivity).Include(x => x.ScheduleSactivityLinks).Include(x => x.ScheduleSactivityPreds).Include(x => x.ScheduleM.Schedule.JobNumberNavigation.Subdivision).Where(x => x.IsActive == true && myJobs.Contains(x.ScheduleM.Schedule.JobNumber) && x.ActualEndDate == null).OrderBy(x => x.ScheduleM.Schedule.JobNumber).ToListAsync();
                var schedules = new List<ScheduleSactivityDto>();
                var schedulesCollection = new List<ScheduleSactivityDto>();

                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT ss.SCHEDULE_AID, ss.SCHEDULE_ID, ss.SEQ, ss.DURATION, ss.LAG_TIME, sm.BASE_START_DATE, sm.BASE_END_DATE, sm.SCHEDULE_MID, sm.INI_START_DATE, sm.INI_END_DATE, ss.SCH_START_DATE, ss.SCH_END_DATE, sa.ACTIVITY_NAME, sa.SACTIVITY_ID, ss.NOTE, ss.SUPPLIER_NOTE, ss.SUB_NUMBER, ss.UPDATE_DATE, ss.UpdatedDateTime, ss.UpdatedBy, ss.PLUSMINUS_DAYS, ss.VARIANCE_CODE, ss.COMPLETED_BY, ss.COMPLETED_DATE, ss.IS_LOCKED, ssp.PRED_SACTIVITY_ID, v.VARIANCE_CODE, CASE WHEN ssl.SCHEDULE_AID IS NOT NULL THEN 'Yes' ELSE 'No' END 'HasPO', CASE WHEN ssl.SCHEDULE_AID IS NOT NULL AND ss.ACTUAL_END_DATE IS NULL THEN 'Yes' ELSE 'No' END 'SupplierEditable', su.SUB_NAME, sub.SUBDIVISION_NAME, j.JOB_ADDRESS1, j.Lot_Number, ss.ACTUAL_START_DATE, ss.ACTUAL_END_DATE, j.JOB_NUMBER, sub.SUBDIVISION_NUM FROM dbo.SCHEDULE_SACTIVITY ss JOIN dbo.SACTIVITY sa ON ss.SACTIVITY_ID = sa.SACTIVITY_ID LEFT JOIN dbo.SCHEDULE_SACTIVITY_LINK ssl ON ss.SCHEDULE_AID = ssl.SCHEDULE_AID JOIN dbo.SCHEDULE sc ON ss.SCHEDULE_ID = sc.SCHEDULE_ID JOIN dbo.JOB j ON sc.JOB_NUMBER = j.JOB_NUMBER JOIN dbo.JOB_CONTACT jc ON j.JOB_NUMBER = jc.JOB_NUMBER JOIN dbo.SCHEDULE_MILESTONE sm ON ss.SCHEDULE_MID = sm.SCHEDULE_MID LEFT JOIN dbo.SCHEDULE_SACTIVITY_PRED ssp ON ss.SCHEDULE_AID = ssp.SCHEDULE_AID LEFT JOIN dbo.VARIANCE v ON ss.VARIANCE_CODE = v.VARIANCE_CODE LEFT JOIN dbo.SUPPLIER su ON TRY_CAST(ss.SUB_NUMBER AS INT) = TRY_CAST(su.SUB_NUMBER AS INT) JOIN dbo.SUBDIVISION sub ON j.SUBDIVISION_ID = sub.SUBDIVISION_ID WHERE ss.ACTUAL_END_DATE IS NULL AND ss.IsActive = 1 AND jc.IsActive = 1 AND sa.IsActive = 1  AND sc.IsActive = 1 AND j.IsActive = 1 AND sm.IsActive = 1 AND ssp.IsActive = 1 AND j.LOT_STATUS <> 6 and (j.BLOCKED is  NULL OR j.BLOCKED = 0) AND (j.BLOCKED is  NULL OR j.BLOCKED = 0) AND sub.COMMUNITY_STATUS = 'Construction' AND sc.INI_SCH_APPROVED = 1 AND ss.SCH_START_DATE is not null AND jc.USER_ID = @userName";
                    var command = new SqlCommand(query, connection);

                    command.Parameters.Add("@userName", SqlDbType.VarChar).Value = userName;
                    // command.Parameters.Add("@updatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    var reader = command.ExecuteReader();

                    //TODO: if multiple preds the activity gets there twice, need to group

                    while (reader.Read())
                    {
                        var scheduleSActivity = new ScheduleSactivityDto
                        {
                            ScheduleAid = Convert.ToInt32(reader.GetValue(0).ToString()),
                            ScheduleId = Convert.ToInt32(reader.GetValue(1).ToString()),
                            ScheduleMid = Convert.ToInt32(reader.GetValue(7).ToString()),
                            Seq = (reader.GetValue(2) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(2).ToString()) : 0,
                            Duration = (reader.GetValue(3) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(3).ToString()) : 0,
                            LagTime = (reader.GetValue(4) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(4).ToString()) : 0,
                            BaseStartDate = (reader.GetValue(5) != DBNull.Value) ? Convert.ToDateTime(reader.GetValue(5).ToString()) : (DateTime?)null,
                            BaseEndDate = (reader.GetValue(6) != DBNull.Value) ? Convert.ToDateTime(reader.GetValue(6).ToString()) : (DateTime?)null,
                            IniStartDate = (reader.GetValue(8) != DBNull.Value) ? Convert.ToDateTime(reader.GetValue(8).ToString()) : (DateTime?)null,
                            IniEndDate = (reader.GetValue(9) != DBNull.Value) ? Convert.ToDateTime(reader.GetValue(9).ToString()) : (DateTime?)null,
                            SchStartDate = (reader.GetValue(10) != DBNull.Value) ? Convert.ToDateTime(reader.GetValue(10).ToString()) : (DateTime?)null,
                            SchEndDate = (reader.GetValue(11) != DBNull.Value) ? Convert.ToDateTime(reader.GetValue(11).ToString()) : (DateTime?)null,
                            ActivityName = reader.GetValue(12) != DBNull.Value ? reader.GetValue(12).ToString() : null,
                            SactivityId = reader.GetValue(13) != DBNull.Value ? Convert.ToInt32(reader.GetValue(13).ToString()) : null,
                            Note = reader.GetValue(14) != DBNull.Value ? reader.GetValue(14).ToString() : null,
                            SupplierNote = reader.GetValue(15) != DBNull.Value ? reader.GetValue(15).ToString() : null,
                            SubNumber = reader.GetValue(16) != DBNull.Value ? Convert.ToInt32(reader.GetValue(16).ToString()) : null,
                            UpdateDate = (reader.GetValue(17) != DBNull.Value) ? Convert.ToDateTime(reader.GetValue(17).ToString()) : (DateTime?)null,
                            UpdatedDateTime = (reader.GetValue(18) != DBNull.Value) ? Convert.ToDateTime(reader.GetValue(18).ToString()) : (DateTime?)null,
                            UpdatedBy = reader.GetValue(19) != DBNull.Value ? reader.GetValue(19).ToString() : null,
                            PlusminusDays = reader.GetValue(20) != DBNull.Value ? Convert.ToInt32(reader.GetValue(20).ToString()) : null,
                            VarianceCode = reader.GetValue(21) != DBNull.Value ? reader.GetValue(21).ToString() : null,
                            CompletedBy = reader.GetValue(22) != DBNull.Value ? reader.GetValue(22).ToString() : null,
                            CompletedDate = (reader.GetValue(23) != DBNull.Value) ? Convert.ToDateTime(reader.GetValue(23).ToString()) : (DateTime?)null,
                            IsLocked = reader.GetValue(24) != DBNull.Value ? reader.GetValue(24).ToString() : null,
                            ActualStartDate = (reader.GetValue(33) != DBNull.Value) ? Convert.ToDateTime(reader.GetValue(33).ToString()) : (DateTime?)null,
                            ActualEndDate = (reader.GetValue(34) != DBNull.Value) ? Convert.ToDateTime(reader.GetValue(34).ToString()) : (DateTime?)null,
                            HasPo = reader.GetValue(27) != DBNull.Value ? reader.GetValue(27).ToString() == "Yes" ? true : false : false,
                            SupplierEditable = reader.GetValue(28) != DBNull.Value ? reader.GetValue(28).ToString() == "Yes" ? true : false : false,
                            BoolStarted = reader.GetValue(33) != DBNull.Value,
                            BoolComplete = reader.GetValue(34) != DBNull.Value,
                            Predecessors = reader.GetValue(25) != DBNull.Value ? new List<ScheduleSactivityPredDto>()
                            {
                                new ScheduleSactivityPredDto()
                                {
                                    PredSactivityId =  Convert.ToInt32(reader.GetValue(25).ToString())
                                }
                            } : null,
                            PredIds = reader.GetValue(25) != DBNull.Value ? new List<int>() { Convert.ToInt32(reader.GetValue(25).ToString()) } : null,
                        };

                        scheduleSActivity.ScheduleM = new ScheduleMilestoneDto
                        {
                            Schedule = new ScheduleDto
                            {
                                JobNumber = reader.GetValue(35).ToString(),
                                JobNumberNavigation = new JobDto
                                {
                                    Subdivision = new SubdivisionDto
                                    {
                                        SubdivisionName = reader.GetValue(30).ToString(),
                                        SubdivisionNum = reader.GetValue(36).ToString(),
                                    },
                                    JobNumber = reader.GetValue(35).ToString(),
                                    LotNumber = reader.GetValue(32).ToString(),
                                    JobAddress1 = reader.GetValue(31).ToString()
                                }
                            }
                        };

                        scheduleSActivity.Sactivity = new SactivityDto
                        {
                            ActivityName = reader.GetValue(12).ToString()
                        };

                        schedules.Add(scheduleSActivity);
                    }
                }

                schedulesCollection.AddRange(schedules);

                return Ok(new ResponseModel<List<ScheduleSactivityDto>>() { Value = schedulesCollection, IsSuccess = true, Message = "Success getting schedules" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return Ok(new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Message = "Failed getting schedules" });
            }
        }


        [HttpGet("{userName}")]
        public async Task<IActionResult> ScheduleActivityForSuperAsync_Archive(string userName)
        {
            try
            {
                var myJobs = await _context.JobContacts.Include(x => x.JobNumberNavigation.LotStatusNavigation).Where(x => x.UserId == userName && x.JobNumberNavigation.LotStatusNavigation.StatusName != "Closed" && x.IsActive == true).Select(x => x.JobNumber).Distinct().ToListAsync();
                var schedules = await _context.ScheduleSactivities.Include(x => x.Sactivity).Include(x => x.ScheduleSactivityLinks).Include(x => x.ScheduleSactivityPreds).Include(x => x.ScheduleM.Schedule.JobNumberNavigation.Subdivision).Where(x => x.IsActive == true && myJobs.Contains(x.ScheduleM.Schedule.JobNumber) && x.ActualEndDate == null).OrderBy(x => x.ScheduleM.Schedule.JobNumber).ToListAsync();
                var schedulesDto = schedules.Select(x => new ScheduleSactivityDto()
                {
                    ScheduleAid = x.ScheduleAid,
                    ScheduleM = _mapper.Map<ScheduleMilestoneDto>(x.ScheduleM),
                    ScheduleMid = x.ScheduleMid,
                    ScheduleId = x.ScheduleId,
                    Schedule = _mapper.Map<ScheduleDto>(x.Schedule),
                    Seq = x.Seq,
                    Duration = x.Duration,
                    LagTime = x.LagTime,
                    BaseEndDate = x.BaseEndDate,
                    BaseStartDate = x.BaseStartDate,
                    IniEndDate = x.IniEndDate,
                    IniStartDate = x.IniStartDate,
                    SchEndDate = x.SchEndDate,
                    SchStartDate = x.SchStartDate,
                    ActivityName = x.Sactivity.ActivityName,
                    Sactivity = _mapper.Map<SactivityDto>(x.Sactivity),
                    SactivityId = x.SactivityId,
                    Note = x.Note,
                    SupplierNote = x.SupplierNote,
                    SubNumber = x.SubNumber,
                    SubNumberNavigation = _mapper.Map<SupplierDto>(x.SubNumberNavigation),
                    UpdateDate = x.UpdateDate,
                    UpdatedBy = x.UpdatedBy,
                    UpdatedDateTime = x.UpdatedDateTime,
                    PlusminusDays = x.PlusminusDays,
                    VarianceCode = x.VarianceCode,
                    VarianceCodeNavigation = _mapper.Map<VarianceDto>(x.VarianceCodeNavigation),
                    CompletedBy = x.CompletedBy,
                    CompletedDate = x.CompletedDate,
                    IsLocked = x.IsLocked,
                    HasPo = x.ScheduleSactivityLinks.Any(),
                    SupplierEditable = !x.ScheduleSactivityLinks.Any() && x.ActualEndDate == null,
                    BoolStarted = x.ActualStartDate != null,
                    PredIds = x.ScheduleSactivityPreds != null ? x.ScheduleSactivityPreds.Select(x => x.PredSactivityId).ToList() : new List<int>(),
                    Predecessors = _mapper.Map<List<ScheduleSactivityPredDto>>(x.ScheduleSactivityPreds),
                }).ToList();

                //var schedulesDto = _mapper.Map<List<ScheduleSactivityDto>>(schedules);//this won't work to get predecessors
                schedulesDto = schedulesDto.OrderBy(x => x.ScheduleM.Schedule.IniSchEndDate).ThenBy(x => x.Seq).ToList();
                return Ok(new ResponseModel<List<ScheduleSactivityDto>>() { Value = schedulesDto, IsSuccess = true, Message = "Success getting schedules" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return Ok(new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Message = "Failed getting schedules" });
            }
        }

        [HttpGet("{userName}/{jobNumber}")]
        public async Task<IActionResult> ScheduleActivityForSuperByJobNumberAsync(string userName, string jobNumber)
        {
            try
            {
                var myJobs = await _context.JobContacts.Where(x => x.UserId == userName && x.IsActive == true).Select(x => x.JobNumber).Distinct().ToListAsync();
                var schedules = await _context.ScheduleSactivities.Include(x => x.ScheduleSactivityPreds).Include(x => x.Sactivity).Include(x => x.ScheduleSactivityLinks).Include(x => x.ScheduleM.Schedule.JobNumberNavigation.Subdivision).Where(x => x.IsActive == true && myJobs.Contains(x.ScheduleM.Schedule.JobNumber) && x.ScheduleM.Schedule.JobNumber.Equals(jobNumber) && x.ActualEndDate == null).OrderBy(x => x.ScheduleM.Schedule.JobNumber).ToListAsync();
                var schedulesDto = schedules.Select(x => new ScheduleSactivityDto()
                {
                    ScheduleAid = x.ScheduleAid,
                    ScheduleM = _mapper.Map<ScheduleMilestoneDto>(x.ScheduleM),
                    ScheduleMid = x.ScheduleMid,
                    ScheduleId = x.ScheduleId,
                    Schedule = _mapper.Map<ScheduleDto>(x.Schedule),
                    Seq = x.Seq,
                    Duration = x.Duration,
                    LagTime = x.LagTime,
                    BaseEndDate = x.BaseEndDate,
                    BaseStartDate = x.BaseStartDate,
                    IniEndDate = x.IniEndDate,
                    IniStartDate = x.IniStartDate,
                    SchEndDate = x.SchEndDate,
                    SchStartDate = x.SchStartDate,
                    ActivityName = x.Sactivity.ActivityName,
                    Sactivity = _mapper.Map<SactivityDto>(x.Sactivity),
                    SactivityId = x.SactivityId,
                    Note = x.Note,
                    SupplierNote = x.SupplierNote,
                    SubNumber = x.SubNumber,
                    SubNumberNavigation = _mapper.Map<SupplierDto>(x.SubNumberNavigation),
                    UpdateDate = x.UpdateDate,
                    UpdatedBy = x.UpdatedBy,
                    UpdatedDateTime = x.UpdatedDateTime,
                    PlusminusDays = x.PlusminusDays,
                    VarianceCode = x.VarianceCode,
                    VarianceCodeNavigation = _mapper.Map<VarianceDto>(x.VarianceCodeNavigation),
                    CompletedBy = x.CompletedBy,
                    CompletedDate = x.CompletedDate,
                    IsLocked = x.IsLocked,
                    HasPo = x.ScheduleSactivityLinks.Any(),
                    SupplierEditable = !x.ScheduleSactivityLinks.Any() && x.ActualEndDate == null,
                    ActualStartDate = x.ActualStartDate,
                    ActualEndDate = x.ActualEndDate,
                    BoolStarted = x.ActualStartDate != null,
                    BoolComplete = x.ActualEndDate != null,
                    PredIds = x.ScheduleSactivityPreds != null ? x.ScheduleSactivityPreds.Where(x => x.IsActive == true).Select(x => x.PredSactivityId).ToList() : new List<int>(),
                }).ToList();

                //var schedulesDto = _mapper.Map<List<ScheduleSactivityDto>>(schedules);
                schedulesDto = schedulesDto.OrderBy(x => x.ScheduleM.Schedule.IniSchEndDate).ThenBy(x => x.Seq).ToList();
                return Ok(new ResponseModel<List<ScheduleSactivityDto>>() { Value = schedulesDto, IsSuccess = true, Message = "Success getting schedules" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return Ok(new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Message = "Failed getting schedules" });
            }
        }
        [HttpGet]
        public async Task<IActionResult> JobsNoSchedulesAsync()
        {
            //Get all jobs that have no schedule
            try
            {
                var constructionJobPostingGroups = new List<string>() { "ADUCONST", "CCONST", "SFCONST", "CSHELL", "THCONST", "TECONST", "TICONST", "VMDB", "VMDBHB" };
                var getJobs = _context.Jobs.AsNoTracking().Include(x => x.Schedule.Template).Include(x => x.Schedule.JobNumberNavigation).Include(x => x.Subdivision).Where(x => (x.Schedule == null || x.Schedule.IsActive == false || x.Schedule.IniSchApproved != true || x.Schedule.JobNumber.Contains("TEST"))).Select(x => new JobDto()
                {
                    JobNumber = x.JobNumber,
                    Subdivision = _mapper.Map<SubdivisionDto>(x.Subdivision),
                    SubdivisionId = x.SubdivisionId,
                    JobPostingGroup = x.JobPostingGroup,
                    JobSchedule = _mapper.Map<ScheduleDto>(x.Schedule) ?? new ScheduleDto() { JobNumber = x.JobNumber },
                    StickBuilingNum = x.StickBuilingNum
                }).OrderBy(x => x.JobNumber).ToList();

                return Ok(new ResponseModel<List<JobDto>>() { Value = getJobs, Message = "Got schedules", IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobDto>> { IsSuccess = false, Message = "Failed to get job status list", Value = null });
            }
        }
        [HttpGet("{templateId}")]
        public async Task<IActionResult> SchedulesForTemplateAsync(int templateId)
        {
            try
            {
                var schedules = await _context.Schedules.Include(x => x.JobNumberNavigation.Subdivision).Where(x => x.IsActive == true && x.TemplateId == templateId).OrderBy(x => x.JobNumber).ToListAsync();
                var schedulesDto = _mapper.Map<List<ScheduleDto>>(schedules);
                return new OkObjectResult(new ResponseModel<List<ScheduleDto>> { Value = schedulesDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleDto>> { IsSuccess = false, Message = "Failed to get Schedules for Template", Value = new List<ScheduleDto>() });
            }
        }

        [HttpGet("{scheduleId}")]
        public async Task<IActionResult> PredsForScheduleAsync(int scheduleId)
        {
            try
            {
                var findActivities = await _context.ScheduleSactivities.Where(x => x.ScheduleM.Schedule.ScheduleId == scheduleId && x.IsActive == true).ToListAsync();
                var findSuccessorActivities = _context.ScheduleSactivityPreds.Include("ScheduleA").Where(x => findActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                var schedulesDto = _mapper.Map<List<ScheduleSactivityPredDto>>(findSuccessorActivities);
                return new OkObjectResult(new ResponseModel<List<ScheduleSactivityPredDto>> { Value = schedulesDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleSactivityPredDto>> { IsSuccess = false, Message = "Failed to get Schedules preds", Value = new List<ScheduleSactivityPredDto>() });
            }
        }

        [HttpGet]
        public async Task<IActionResult> TemplatesAsync()
        {
            try
            {
                var templates = await _context.Templates.Where(x => x.IsActive == true).OrderBy(x => x.TemplateName).ToListAsync();
                var templatesDto = _mapper.Map<List<TemplateDto>>(templates);
                return new OkObjectResult(new ResponseModel<List<TemplateDto>> { Value = templatesDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<TemplateDto>> { IsSuccess = false, Message = "Failed to get Templates", Value = new List<TemplateDto>() });
            }
        }

        [HttpGet("{templateId}")]
        public async Task<IActionResult> TemplateAsync(int templateId)
        {
            try
            {
                // var templates = await _context.Templates.SingleOrDefaultAsync(x => x.TemplateId == templateId);
                // var templatesDto = _mapper.Map<TemplateDto>(templates);
                var treeData = new List<ScheduleTemplateTreeModel>();
                var findMilestones = _context.TemplateMilestones.Where(x => x.TemplateId == templateId && x.IsActive == true).OrderBy(x => x.Seq).ToList();
                foreach (var milestone in findMilestones)
                {
                    var findMilestone = _context.Milestones.SingleOrDefault(x => x.MilestoneId == milestone.MilestoneId);
                    var findActivities = _context.TemplateSactivities.Include(x => x.Sactivity).ThenInclude(x => x.Pactivities).Include(x => x.TemplateSactivityPreds).ThenInclude(x => x.PredSactivity).Where(x => x.TemplateMid == milestone.TemplateMid && x.IsActive == true).OrderBy(x => x.Seq).ToList();
                    treeData.Add(new ScheduleTemplateTreeModel()
                    {
                        Id = Guid.NewGuid(),
                        TemplateMilestone = _mapper.Map<TemplateMilestoneDto>(milestone),
                        Milestone = _mapper.Map<MilestoneDto>(findMilestone),
                        MilestoneName = findMilestone.MilestoneName,
                        HasChildren = findActivities.Any(),
                        Children = findActivities.Select(x => new ScheduleTemplateTreeModel()
                        {
                            Id = Guid.NewGuid(),
                            Sactivity = _mapper.Map<SactivityDto>(x.Sactivity),
                            ActivityName = x.Sactivity.ActivityName,
                            TemplateSactivity = _mapper.Map<TemplateSactivityDto>(x),
                            PurchasingActivities = string.Join(", ", x.Sactivity.Pactivities.Where(y => y.IsActive == true && y.DivId == 1).OrderBy(y => y.Activity).Select(x => x.Activity).ToList().Distinct()),
                            Predecessors = string.Join(", ", x.TemplateSactivityPreds.Where(x => x.PredSactivity != null && x.IsActive == true).Select(y => y.PredSactivity.ActivityName)),
                            PredecessorIds = x.TemplateSactivityPreds.Where(x => x.PredSactivity != null && x.IsActive == true).Select(y => y.PredSactivity.SactivityId).ToList(),
                            HasChildren = false
                        }).ToList(),
                    });
                }

                foreach (var milestone in treeData)
                {
                    foreach (var activity in milestone.Children)
                    {
                        activity.SearchTags = $"{milestone.Milestone.MilestoneName}|{activity.Sactivity.ActivityName}";
                        milestone.SearchTags = activity.SearchTags;
                    }
                }

                return new OkObjectResult(new ResponseModel<List<ScheduleTemplateTreeModel>> { Value = treeData, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleTemplateTreeModel>> { IsSuccess = false, Message = "Failed to get Templates Data", Value = new List<ScheduleTemplateTreeModel>() });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddTemplateAsync([FromBody] TemplateDto addTemplate)
        {
            try
            {
                var newTemplate = new ERP.Data.Models.Template()
                {
                    TemplateName = addTemplate.TemplateName,
                    Description = addTemplate.Description,
                    DivId = 1,
                    CreatedBy = User.Identity.Name.Split("@")[0],
                    UserCreated = User.Identity.Name.Split("@")[0]
                };
                _context.Templates.Add(newTemplate);
                await _context.SaveChangesAsync();
                var templateDto = _mapper.Map<TemplateDto>(newTemplate);
                return new OkObjectResult(new ResponseModel<TemplateDto> { Value = templateDto, IsSuccess = true, Message = "Template added successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TemplateDto> { IsSuccess = false, Message = "Failed to add Template" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteTemplateAsync([FromBody] TemplateDto updateTemplate)
        {
            try
            {
                var updateBy = User.Identity.Name.Split("@")[0];
                var findTemplate = _context.Templates.SingleOrDefault(x => x.TemplateId == updateTemplate.TemplateId);
                findTemplate.IsActive = false;
                findTemplate.UpdatedBy = updateBy;
                findTemplate.Updateddatetime = DateTime.Now;
                _context.Templates.Update(findTemplate);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<TemplateDto> { Value = updateTemplate, IsSuccess = true, Message = "Template deleted successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TemplateDto> { IsSuccess = false, Message = "Failed to delete Template" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> UpdateTemplateActivityAsync([FromBody] TemplateSactivityDto updateTemplate)
        {
            try
            {
                var updateBy = User.Identity.Name.Split("@")[0];
                var findTemplateActivity = _context.TemplateSactivities.Include(x => x.TemplateSactivityPreds).SingleOrDefault(x => x.TemplateAid == updateTemplate.TemplateAid);
                var findPreds = findTemplateActivity.TemplateSactivityPreds.Select(x => x.PredSactivityId).ToList();
                if (updateTemplate.PredIds != null)
                {
                    bool changed = !findTemplateActivity.TemplateSactivityPreds.Where(x => x.IsActive == true).Select(x => x.PredSactivityId).Order().ToList().SequenceEqual(updateTemplate.PredIds.Order());
                    if (changed)
                    {
                        //deactivate any old preds
                        foreach (var findPred in findTemplateActivity.TemplateSactivityPreds.ToList())
                        {
                            findPred.IsActive = false;
                            findPred.UpdatedBy = User.Identity.Name.Split('@')[0];
                            findPred.Updateddatetime = DateTime.Now;
                        }
                        await _context.TemplateSactivityPreds.BulkUpdateAsync(findTemplateActivity.TemplateSactivityPreds);

                        //if any new preds existed already, reactivate them
                        var reactivatePreds = findTemplateActivity.TemplateSactivityPreds.Where(x => updateTemplate.PredIds.Contains(x.PredSactivityId)).ToList();
                        foreach (var reactivatePred in reactivatePreds)
                        {
                            reactivatePred.IsActive = true;
                            reactivatePred.UpdatedBy = User.Identity.Name.Split('@')[0];
                            reactivatePred.Updateddatetime = DateTime.Now;
                        }
                        await _context.TemplateSactivityPreds.BulkUpdateAsync(reactivatePreds);
                        //insert new preds
                        var newPreds = updateTemplate.PredIds.Where(x => !findTemplateActivity.TemplateSactivityPreds.Select(y => y.PredSactivityId).Contains(x)).Select(x => new TemplateSactivityPred()
                        {
                            TemplateAid = updateTemplate.TemplateAid,
                            PredSactivityId = x,
                            CreatedBy = updateBy,
                            UpdatedBy = updateBy,
                            IsActive = true
                        }).ToList();
                        await _context.TemplateSactivityPreds.BulkInsertAsync(newPreds);
                    }
                    //findTemplateActivity.TemplateSactivityPreds = newPreds;
                }

                findTemplateActivity.Duration = updateTemplate.Duration;
                findTemplateActivity.LagTime = updateTemplate.LagTime;
                findTemplateActivity.GrossLag = updateTemplate.GrossLag;
                findTemplateActivity.Seq = updateTemplate.Seq;
                findTemplateActivity.UpdatedBy = updateBy;
                findTemplateActivity.Updateddatetime = DateTime.Now;
                await _context.TemplateSactivities.BulkUpdateAsync(new List<TemplateSactivity> { findTemplateActivity });
                //await _context.SaveChangesAsync();
                var findActivity = _context.TemplateSactivities.Include("TemplateSactivityPreds.PredSactivity").SingleOrDefault(x => x.TemplateAid == updateTemplate.TemplateAid);
                var templateSactivityDto = _mapper.Map<TemplateSactivityDto>(findActivity);
                templateSactivityDto.PredIds = findActivity.TemplateSactivityPreds.Where(x => x.IsActive == true && x.PredSactivity != null).Select(x => x.PredSactivityId).ToList();
                templateSactivityDto.Predecessors = string.Join(", ", findActivity.TemplateSactivityPreds.Where(x => x.IsActive == true && x.PredSactivity != null).Select(y => y.PredSactivity.ActivityName));
                return new OkObjectResult(new ResponseModel<TemplateSactivityDto> { Value = templateSactivityDto, IsSuccess = true, Message = "Template activity updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TemplateSactivityDto> { IsSuccess = false, Message = "Failed to update Template Activity" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> DeleteTemplateMilestoneAsync([FromBody] TemplateMilestoneDto templateMilestone)
        {
            try
            {
                //Todo: find template milestone is null if drop then delete
                var updateBy = User.Identity.Name.Split("@")[0];
                var findTemplateMilestone = _context.TemplateMilestones.SingleOrDefault(x => x.TemplateMid == templateMilestone.TemplateMid);
                findTemplateMilestone.IsActive = false;
                findTemplateMilestone.UpdatedBy = updateBy;
                findTemplateMilestone.Updateddatetime = DateTime.Now;
                _context.TemplateMilestones.Update(findTemplateMilestone);
                await _context.SaveChangesAsync();
                var findActivitiesInMilestone = _context.TemplateSactivities.Where(x => x.TemplateMid == templateMilestone.TemplateMid);
                await findActivitiesInMilestone.ExecuteUpdateAsync(s => s.SetProperty(x => x.IsActive, false).SetProperty(x => x.UpdatedBy, updateBy).SetProperty(x => x.Updateddatetime, DateTime.Now));
                //set any activities that have these as predecessors to deactivate that predecessor
                var activityIds = findActivitiesInMilestone.Select(x => x.SactivityId).ToList();
                var findSuccessors = _context.TemplateSactivityPreds.Where(x => activityIds.Contains(x.PredSactivityId) && x.TemplateA.TemplateM.TemplateId == findTemplateMilestone.TemplateId);
                await findSuccessors.ExecuteUpdateAsync(s => s.SetProperty(x => x.IsActive, false).SetProperty(x => x.UpdatedBy, updateBy).SetProperty(x => x.Updateddatetime, DateTime.Now));
                return new OkObjectResult(new ResponseModel<TemplateMilestoneDto> { Value = templateMilestone, IsSuccess = true, Message = "Template milestone deleted successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TemplateMilestoneDto> { IsSuccess = false, Message = "Failed to delete Template milestone" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> DeleteTemplateActivityAsync([FromBody] TemplateSactivityDto templateSactivity)
        {
            try
            {
                var updateBy = User.Identity.Name.Split("@")[0];
                var findTemplateActivity = _context.TemplateSactivities.Include(x => x.TemplateM).Include(x => x.TemplateSactivityPreds).SingleOrDefault(x => x.TemplateAid == templateSactivity.TemplateAid);
                findTemplateActivity.IsActive = false;
                findTemplateActivity.UpdatedBy = updateBy;
                findTemplateActivity.Updateddatetime = DateTime.Now;
                _context.TemplateSactivities.Update(findTemplateActivity);
                await _context.SaveChangesAsync();

                //deactivate any that have this as a pred
                var findSuccessors = _context.TemplateSactivityPreds.Where(x => x.PredSactivityId == templateSactivity.SactivityId && x.TemplateA.TemplateM.TemplateId == findTemplateActivity.TemplateM.TemplateId);
                await findSuccessors.ExecuteUpdateAsync(s => s.SetProperty(x => x.IsActive, false).SetProperty(x => x.UpdatedBy, updateBy).SetProperty(x => x.Updateddatetime, DateTime.Now));
                return new OkObjectResult(new ResponseModel<TemplateSactivityDto> { Value = templateSactivity, IsSuccess = true, Message = "Template activity deleted successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TemplateSactivityDto> { IsSuccess = false, Message = "Failed to delete Template Activity" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateTemplateActivitiesSeqAsync([FromBody] List<TemplateSactivityDto> updateTemplateActivites)
        {
            try
            {
                //assuming new sequence is saved in the update milestones seq
                var findActivities = _context.TemplateSactivities.Where(x => updateTemplateActivites.Select(y => y.TemplateAid).Contains(x.TemplateAid)).ToList();
                var notFoundActivities = updateTemplateActivites.Where(x => !findActivities.Select(y => y.TemplateAid).Contains(x.TemplateAid)).ToList();
                var FoundActivities = updateTemplateActivites.Where(x => findActivities.Select(y => y.TemplateAid).Contains(x.TemplateAid)).ToList();
                var findDuplicate = updateTemplateActivites.GroupBy(x => x.TemplateAid).Where(x => x.Count() > 1).Select(x => x.Key).ToList();
                foreach (var activity in findActivities)
                {
                    var updateActivity = updateTemplateActivites.SingleOrDefault(x => x.TemplateAid == activity.TemplateAid);
                    activity.Seq = updateActivity.Seq;//TODO: check null? could there be more than one?
                    activity.TemplateMid = updateActivity.TemplateMid;//Item could have moved to a different milestone
                }
                _context.TemplateSactivities.UpdateRange(findActivities);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<List<TemplateSactivityDto>> { Value = updateTemplateActivites, IsSuccess = true, Message = "Template activities updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<TemplateSactivityDto>> { IsSuccess = false, Message = "Failed to update Template Activities" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateTemplateMilestonesSeqAsync([FromBody] List<TemplateMilestoneDto> updateTemplateMilestones)
        {
            try
            {
                var testNew = updateTemplateMilestones.Where(x => x.TemplateMid == null || x.TemplateMid == 0).ToList();
                //assuming new sequence is saved in the update milestones seq
                var findMilestones = _context.TemplateMilestones.Where(x => updateTemplateMilestones.Select(y => y.TemplateMid).Contains(x.TemplateMid)).ToList();
                var notFoundActivities = updateTemplateMilestones.Where(x => !findMilestones.Select(y => y.TemplateMid).Contains(x.TemplateMid)).ToList();
                var FoundActivities = updateTemplateMilestones.Where(x => findMilestones.Select(y => y.TemplateMid).Contains(x.TemplateMid)).ToList();
                var findDuplicate = updateTemplateMilestones.GroupBy(x => x.TemplateMid).Where(x => x.Count() > 1).Select(x => x.Key).ToList();
                foreach (var milestones in findMilestones)
                {
                    var updateActivity = updateTemplateMilestones.SingleOrDefault(x => x.TemplateMid == milestones.TemplateMid);
                    milestones.Seq = updateActivity.Seq;//TODO: check null? could there be more than one?
                    milestones.TemplateMid = updateActivity.TemplateMid;//Item could have moved to a different milestone
                }
                _context.TemplateMilestones.UpdateRange(findMilestones);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<List<TemplateMilestoneDto>> { Value = updateTemplateMilestones, IsSuccess = true, Message = "Template milestones updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<TemplateMilestoneDto>> { IsSuccess = false, Message = "Failed to update Template milestones" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CopyTemplateAsync([FromBody] TemplateDto addTemplate)
        {
            try
            {
                var createdBy = User.Identity.Name.Split('@')[0];
                var getTemplateToCopy = _context.Templates.SingleOrDefault(x => x.TemplateId == addTemplate.TemplateId);

                var newTemplate = new Template()
                {
                    TemplateName = addTemplate.TemplateName,
                    Description = addTemplate.Description,
                    DivId = 1,
                    CreatedBy = User.Identity.Name.Split("@")[0],
                    UserCreated = User.Identity.Name.Split("@")[0]
                };
                _context.Templates.Add(newTemplate);
                await _context.SaveChangesAsync();
                var getTemplateMilestones = _context.TemplateMilestones.Where(x => x.TemplateId == addTemplate.TemplateId && x.IsActive == true).ToList();
                var newTemplateMilestones = getTemplateMilestones.Select(x => new TemplateMilestone()
                {
                    TemplateId = newTemplate.TemplateId,
                    MilestoneId = x.MilestoneId,
                    Seq = x.Seq,
                    CreatedBy = createdBy,
                }).ToList();
                _context.TemplateMilestones.AddRange(newTemplateMilestones);
                await _context.SaveChangesAsync();

                var getTemplateSactivities = _context.TemplateSactivities.Where(x => getTemplateMilestones.Select(y => y.TemplateMid).Contains(x.TemplateMid) && x.IsActive == true).ToList();
                var newTemplateSactivites = getTemplateSactivities.Select(x => new TemplateSactivity()
                {
                    // TemplateId = newTemplate.TemplateId,
                    TemplateMid = newTemplateMilestones.SingleOrDefault(y => y.MilestoneId == x.TemplateM.MilestoneId).TemplateMid,//TODO: this could be null, assumes milestones are not in the template twice
                    SactivityId = x.SactivityId,
                    Seq = x.Seq,
                    Duration = x.Duration,
                    LagTime = x.LagTime,
                    Note = x.Note,
                    ChecklistId = x.ChecklistId,
                    GrossLag = x.GrossLag,
                    GenPitBudget = x.GenPitBudget,
                    CreatedBy = createdBy
                }).ToList();
                _context.TemplateSactivities.AddRange(newTemplateSactivites);
                await _context.SaveChangesAsync();

                var findTemplateActivityPreds = _context.TemplateSactivityPreds.AsNoTracking().Include(x => x.TemplateA).Where(x => getTemplateSactivities.Select(y => y.TemplateAid).Contains(x.TemplateAid) && x.IsActive == true).ToList();
                var newTemplatePreds = findTemplateActivityPreds.Select(x => new TemplateSactivityPred()
                {
                    TemplateAid = newTemplateSactivites.FirstOrDefault(y => x.TemplateA.SactivityId == y.SactivityId).TemplateAid,
                    PredSactivityId = x.PredSactivityId,
                    CreatedBy = createdBy
                }).ToList();
                _context.TemplateSactivityPreds.AddRange(newTemplatePreds);
                await _context.SaveChangesAsync();

                var templateDto = _mapper.Map<TemplateDto>(newTemplate);
                return new OkObjectResult(new ResponseModel<TemplateDto> { Value = templateDto, IsSuccess = true, Message = "Template copied successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TemplateDto> { IsSuccess = false, Message = "Failed to copy Template" });
            }
        }

        //NOT USED. SchedulesFromTemplate used instead
        [HttpPost]
        public async Task<IActionResult> ScheduleFromTemplateAsync([FromBody] ScheduleDto scheduleDto)
        {
            try
            {

                string createBy = User.Identity.Name.Split('@')[0];
                var getTemplate = _context.Templates.Include("TemplateMilestones.TemplateSactivities").SingleOrDefault(x => x.TemplateId == scheduleDto.TemplateId);
                var newSchedule = new Schedule()
                {
                    TemplateId = scheduleDto.TemplateId,
                    Published = scheduleDto.Published,
                    JobNumber = scheduleDto.JobNumber,
                    CreatedBy = createBy
                };
                _context.Schedules.Add(newSchedule);
                await _context.SaveChangesAsync();
                var addMilestones = getTemplate.TemplateMilestones.Select(x => new ScheduleMilestone()
                {
                    ScheduleId = newSchedule.ScheduleId,
                    MilestoneId = x.MilestoneId,
                    Seq = x.Seq,
                    Duration = x.TemplateSactivities.Sum(y => y.Duration),
                    BaseStartDate = DateTime.Now.Date,
                    CreatedBy = createBy
                }).ToList();
                _context.ScheduleMilestones.AddRange(addMilestones);
                await _context.SaveChangesAsync();
                var subdivisionId = _context.Jobs.FirstOrDefault(x => x.JobNumber == scheduleDto.JobNumber).SubdivisionId;
                var templateMilestones = getTemplate.TemplateMilestones.ToList();
                var getTemplateActivities = _context.TemplateSactivities.Include("TemplateM").Include("Sactivity").Include(x => x.TemplateSactivityPreds).Where(x => templateMilestones.Select(y => y.TemplateMid).Contains(x.TemplateMid)).ToList();
                var test = getTemplateActivities.ToList();
                var addActivities = getTemplateActivities.Select(x => new ScheduleSactivity()
                {
                    ScheduleId = newSchedule.ScheduleId,
                    ScheduleMid = addMilestones.FirstOrDefault(y => y.MilestoneId == x.TemplateM.MilestoneId)?.ScheduleMid,
                    SactivityId = x.SactivityId,
                    SubNumber = _context.TradeSuppliers.FirstOrDefault(y => y.SubdivisionId == subdivisionId && y.TradeId == x.Sactivity.TradeId && y.DefaultSupplier == "T")?.SubNumber ?? _context.TradeSuppliers.FirstOrDefault(y => y.SubdivisionId == 1 && y.TradeId == x.Sactivity.TradeId && y.DefaultSupplier == "T")?.SubNumber ?? null,//TODO: division default if no subnumber? Should this be pactivieyt supplier???
                    Seq = x.Seq,
                    Duration = x.Duration,
                    ScheduleSactivityPreds = x.TemplateSactivityPreds.Select(y => new ScheduleSactivityPred()
                    {
                        PredSactivityId = y.PredSactivityId,
                        CreatedBy = createBy
                    }).ToList(),
                    //start dates to be calculated after prerelease schedule
                    // BaseStartDate = DateTime.Now.Date,
                    // BaseEndDate = x.Duration != null ? DateTime.Now.Date.AddDays((double)x.Duration) : DateTime.Now.Date,
                    CreatedBy = createBy
                }).ToList();
                //TODO: predecessors, all the fields, etc
                await _context.ScheduleSactivities.BulkInsertAsync(addActivities, options => options.IncludeGraph = true);
                //await _context.SaveChangesAsync();

                return new OkObjectResult(new ResponseModel<Schedule> { Value = newSchedule, IsSuccess = true, Message = "Generated Schedule from Template successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<Schedule> { IsSuccess = false, Message = "Failed to generate Schedule from Template" });
            }
        }

        //Not used - use update schedules from template
        //        [HttpPost]
        //        public async Task<IActionResult> UpdateScheduleFromTemplateAsync([FromBody] ScheduleDto scheduleDto)
        //        {
        //            try
        //            {
        //                string createBy = User.Identity.Name.Split('@')[0];
        //                var holidays = _context.CalendarsDays.Where(x => x.CalendarId == 1 && x.IsActive == true).Select(x => x.WorkDate).ToList();

        //                //get the current activities data
        //                var findCurrentActivities = _context.ScheduleSactivities.Include(x => x.Sactivity).Where(x => x.ScheduleId == scheduleDto.ScheduleId && x.IsActive == true).ToList();
        //                var currentActivitiesDto = _mapper.Map<List<ScheduleSactivityDto>>(findCurrentActivities);
        //                var findCurrentMilestones = _context.ScheduleMilestones.Where(x => x.ScheduleId == scheduleDto.ScheduleId && x.IsActive == true).ToList();
        //                var findCurrentLinks = _context.ScheduleSactivityLinks.Where(x => findCurrentActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid) && x.IsActive == true).ToList();
        //                var findCurrentPreds = _context.ScheduleSactivityPreds.Where(x => findCurrentActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid) && x.IsActive == true).ToList();
        //                var findCurrentSchedule = await _context.Schedules.SingleOrDefaultAsync(x => x.ScheduleId == scheduleDto.ScheduleId);

        //                //create new schedule from template
        //                var getTemplate = _context.Templates.Include("TemplateMilestones.TemplateSactivities").SingleOrDefault(x => x.TemplateId == scheduleDto.TemplateId);
        //                var subdivisionId = _context.Jobs.FirstOrDefault(x => x.JobNumber == scheduleDto.JobNumber).SubdivisionId;
        //                var templateMilestones = getTemplate.TemplateMilestones.ToList();
        //                var getTemplateActivities = _context.TemplateSactivities.Include("TemplateM").Include("Sactivity").Where(x => templateMilestones.Select(y => y.TemplateMid).Contains(x.TemplateMid) && x.IsActive == true).ToList();
        //                var getTradeSuppliers = _context.TradeSuppliers.Where(x => (x.SubdivisionId == subdivisionId || x.SubdivisionId == 1) && x.IsActive == true && x.DefaultSupplier == "T").ToList();

        //                //Add any new milestones and activities
        //                var addMilestones = templateMilestones.Where(x => !findCurrentMilestones.Select(y => y.MilestoneId).Contains(x.MilestoneId) && x.IsActive == true).Select(x => new ScheduleMilestone()
        //                {
        //                    ScheduleId = scheduleDto.ScheduleId,
        //                    MilestoneId = x.MilestoneId,
        //                    Seq = x.Seq,
        //                    CreatedBy = createBy

        //                });
        //                await _context.ScheduleMilestones.BulkInsertAsync(addMilestones);
        //                //update existing ones from template
        //                foreach(var milestone in findCurrentMilestones)
        //                {
        //                    milestone.Seq = templateMilestones.FirstOrDefault(x => x.MilestoneId == milestone.MilestoneId)?.Seq;
        //                    milestone.UpdatedBy = createBy;
        //                    milestone.UpdatedDateTime = DateTime.Now;
        //                }
        //                await _context.ScheduleMilestones.BulkUpdateAsync(findCurrentMilestones);

        //                var allMilestones = _context.ScheduleMilestones.Where(x => x.ScheduleId == scheduleDto.ScheduleId && x.IsActive == true);//this should be the old ones and newly added ones
        //                var addActivities = getTemplateActivities.Where(x => !findCurrentActivities.Select(y => y.SactivityId).Contains(x.SactivityId) && x.IsActive == true).Select(x => new ScheduleSactivity()
        //                {
        //                    ScheduleId = scheduleDto.ScheduleId,
        //                    ScheduleMid = allMilestones.FirstOrDefault(y => y.MilestoneId == x.TemplateM.MilestoneId)?.ScheduleMid,
        //                    SactivityId = x.SactivityId,
        //                    SubNumber = getTradeSuppliers.FirstOrDefault(y => y.SubdivisionId == subdivisionId && y.TradeId == x.Sactivity.TradeId)?.SubNumber ?? getTradeSuppliers.FirstOrDefault(y => y.SubdivisionId == 1 && y.TradeId == x.Sactivity.TradeId)?.SubNumber ?? null,
        //                    Seq = x.Seq,
        //                    Duration = x.Duration,
        //                    LagTime = x.LagTime,
        //                    CreatedBy = createBy
        //                }).ToList();
        //                await _context.ScheduleSactivities.BulkInsertAsync(addActivities);

        //                foreach(var activity in findCurrentActivities)
        //                {
        //                    var findTemplateActiity = getTemplateActivities.FirstOrDefault(x => x.SactivityId == activity.SactivityId);
        //                    activity.UpdateDate = DateTime.Now;
        //                    activity.UpdatedBy = createBy;
        //                    activity.LagTime = activity.ActualEndDate != null ? activity.LagTime : findTemplateActiity?.LagTime;
        //                    activity.Duration = activity.ActualEndDate != null ? activity.LagTime : findTemplateActiity?.Duration;
        //                    activity.Seq = findTemplateActiity.Seq;
        //                    activity.SubNumber = getTradeSuppliers.FirstOrDefault(y => y.SubdivisionId == subdivisionId && y.TradeId == activity.Sactivity.TradeId)?.SubNumber ?? getTradeSuppliers.FirstOrDefault(y => y.SubdivisionId == 1 && y.TradeId == activity.Sactivity.TradeId)?.SubNumber ?? null;
        //                }
        //                await _context.ScheduleSactivities.BulkUpdateAsync(findCurrentActivities);

        //                //delete ones not in new template
        //                //TODO: for now, deleting all the preds and readding, really should just delete the ones that aren't needed aand update the ones that changed
        //                var deleteAllPreds = _context.ScheduleSactivityPreds.Where(x => findCurrentActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
        //                await _context.ScheduleSactivityPreds.BulkDeleteAsync(deleteAllPreds);

        //               // var deleteAllLinks = _context.ScheduleSactivityLinks.Where(x => findCurrentActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
        //                //await _context.ScheduleSactivityLinks.BulkDeleteAsync(deleteAllLinks);

        //                //TODO: mark inactive insted
        //                var activitiesToDelete = findCurrentActivities.Where(x => !(getTemplateActivities.Select(y => y.SactivityId).ToList()).Contains((int)x.SactivityId)).ToList();
        //                await _context.ScheduleSactivities.BulkDeleteAsync(activitiesToDelete);
        //                var milestonesToDelete = findCurrentMilestones.Where(x => !templateMilestones.Select(y => y.MilestoneId).ToList().Contains(x.MilestoneId)).ToList();
        //                await _context.ScheduleMilestones.BulkDeleteAsync(milestonesToDelete);

        //                var getTemplatePreds = await _context.TemplateSactivityPreds.Include(x => x.TemplateA).Where(x => getTemplateActivities.Select(y => y.TemplateAid).Contains(x.TemplateAid) && x.IsActive == true).ToListAsync();
        //                var getScheduleActivities = await _context.ScheduleSactivities.Where(x => x.ScheduleId != null && x.ScheduleId == scheduleDto.ScheduleId && x.IsActive == true).ToListAsync();
        //                var predsToAdd = (
        //                                   from b in getTemplatePreds
        //                                   join c in getScheduleActivities on new { activityId = b.TemplateA.SactivityId } equals new { activityId = c.SactivityId ?? 0 }
        //                                   join d in getScheduleActivities on new { activityId = b.PredSactivityId } equals new { activityId = d.SactivityId ?? 0 }
        //                                   select new ScheduleSactivityPred()
        //                                   {
        //                                       ScheduleAid = c.ScheduleAid,
        //                                       PredSactivityId = b.PredSactivityId,
        //                                       PredScheduleAid = d.ScheduleAid,
        //                                       CreatedBy = createBy
        //                                   }).ToList();
        //                InsertScheduleActivityPreds(createBy, predsToAdd);
        //                var getAddedPreds = _context.ScheduleSactivityPreds.Where(x => getScheduleActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();

        //                //add links to est
        //               // var findSactivities = await _context.ScheduleSactivities.Include(x => x.ScheduleSactivityLinks).Where(x => x.ScheduleId == scheduleDto.ScheduleId && x.IsActive == true).ToListAsync();
        //                var selectLinks = (from a in addActivities
        //                                   join b in _context.Estactivities.Where(x => x.JobNumber == scheduleDto.JobNumber && x.IsActive == true) on a.SactivityId equals b.SactivityId
        //                                   select new
        //                                   {
        //                                       SubNumber = b.SubNumber,
        //                                       ScheduleSactivityId = a.ScheduleAid,
        //                                       ScheduleSactivityLink = new ScheduleSactivityLink()
        //                                       {
        //                                           ScheduleAid = a.ScheduleAid,
        //                                           EstactivityId = b.EstactivityId,
        //                                           UpdatedBy = createBy,
        //                                           CreatedBy = createBy,
        //                                       }
        //                                   }).ToList();
        //                //Insert the new ones
        //                InsertScheduleActivityLinks(createBy, selectLinks.Select(x => x.ScheduleSactivityLink).ToList());

        //                //TODO: check the logic, especially on items with no predecessor or multiple preds
        //                //then save the updated dates

        //                if (scheduleDto.IniSchApproved == true)
        //                {
        //                    //if released, update all the dates, if not released, there would be no dates anyway
        //                    //start at beginning and recalculate the whole schedule
        //                    var firstActivity = getScheduleActivities.Where(x => x.Seq == 1).FirstOrDefault();
        //                    //if firstActivity not already started, it won't hve sch dates so set it's sch start date values
        //                    firstActivity.SchStartDate = firstActivity.SchStartDate ?? (scheduleDto.IniSchApproved == true ? scheduleDto.IniSchStartDate : null);
        //                    firstActivity.SchEndDate = firstActivity.SchEndDate ?? (scheduleDto.IniSchApproved == true ? firstActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)firstActivity.SchStartDate, (int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0, holidays) : firstActivity.SchStartDate.Value.AddDays((int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0) : null);
        //                    firstActivity.BaseStartDate = firstActivity.BaseStartDate ?? (scheduleDto.IniSchApproved == true ? firstActivity.SchStartDate : null);
        //                    firstActivity.IniStartDate = firstActivity.IniStartDate ?? scheduleDto.IniSchStartDate;
        //                    firstActivity.IniEndDate = firstActivity.IniEndDate ?? (firstActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)firstActivity.SchStartDate, (int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0, holidays) : firstActivity.SchStartDate.Value.AddDays((int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0));
        //                    firstActivity.BaseEndDate = firstActivity.BaseEndDate ?? (scheduleDto.IniSchApproved == true ? firstActivity.SchEndDate : null);

        //                    var returnList = new List<ScheduleSactivity>();
        //                    //CalculateAdjustDates(firstActivity, getAddedPreds, holidays, returnList);
        //                    //returnList.Add(firstActivity);
        //                    //var test = returnList.Where(x => x.SchStartDate == null).ToList();

        //                    RecalculateDates(getAddedPreds, firstActivity, holidays, false);
        //                    returnList = visited;


        //                    var updateActivities = _context.ScheduleSactivities.Where(x => returnList.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
        //                    foreach (var activity in updateActivities)
        //                    {
        //                        //TODO: no need to update the completed ones again
        //                        activity.SchStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
        //                        activity.SchEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
        //                        activity.BaseStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
        //                        activity.BaseEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
        //                        activity.IniStartDate = activity.IniStartDate ?? returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
        //                        activity.IniEndDate = activity.IniStartDate ?? returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
        //                        activity.Calduration = (activity.SchEndDate.Value - activity.SchStartDate.Value).Days;//TODO: check null-- calduration is straigt diff in days
        //                        activity.UpdatedBy = createBy;
        //                        activity.UpdateDate = DateTime.Now;
        //                        activity.UpdatedDateTime = DateTime.Now;
        //                    }

        //                    //save the update to temp table
        //                    var sessionId = Guid.NewGuid();
        //                    var result = await InsertTempSactivites(updateActivities, sessionId);

        //                    //update from the temp table
        //                    var conn1 = _configuration.GetConnectionString("ERPConnection");
        //                    using (var connection = new SqlConnection(conn1))
        //                    {
        //                        connection.Open();
        //                        var query = $"Update  c SET c.ACTUAL_START_DATE = c2.ACTUAL_START_DATE, c.ACTUAL_END_DATE = c2.ACTUAL_END_DATE, c.SCH_START_DATE = c2.SCH_START_DATE, c.SCH_END_DATE = c2.SCH_END_DATE, c.PLUSMINUS_DAYS = c2.PLUSMINUS_DAYS, c.BASE_START_DATE = c2.BASE_START_DATE, c.BASE_END_DATE = c2.BASE_END_DATE, c.INI_START_DATE = c2.INI_START_DATE, c.INI_END_DATE = c2.INI_END_DATE, c.CALDURATION = c2.CALDURATION, c.SUB_NUMBER = c2.SUB_NUMBER, c.UpdatedBy = c2.UpdatedBy, c.UpdatedDateTime = c2.UpdatedDateTime FROM xxxSCHEDULE_SACTIVITY c2 INNER JOIN SCHEDULE_SACTIVITY c on c.SCHEDULE_AID = c2.SCHEDULE_AID where c2.SessionId = '{sessionId}'";

        //                        var command = new SqlCommand(query, connection);
        //                        await command.ExecuteNonQueryAsync();
        //                        //truncate the temp table
        //                        // var deleteQuery = $"DELETE FROM xxxSCHEDULE_SACTIVITY where SessionId = '{sessionId}'";
        //                        //  var deleteCommand = new SqlCommand(deleteQuery, connection);
        //                        // await deleteCommand.ExecuteNonQueryAsync();
        //                    }

        //                    //_context.ScheduleSactivities.BulkUpdate(findNewActivities);

        //                    //then update the milestones and schedule as a whole dates
        //                    //update the milestones dates and durations
        //                    var findAllMilestones = _context.ScheduleMilestones.Include(x => x.ScheduleSactivities).Where(x => x.ScheduleId == findCurrentSchedule.ScheduleId).ToList();
        //                    foreach (var milestone in findAllMilestones)
        //                    {
        //                        milestone.SchStartDate = milestone.ScheduleSactivities.Min(x => x.SchStartDate);
        //                        milestone.SchEndDate = milestone.ScheduleSactivities.Max(x => x.SchEndDate);
        //                        milestone.ActualStartDate = milestone.ScheduleSactivities.Min(x => x.ActualStartDate);
        //                        milestone.ActualEndDate = milestone.ScheduleSactivities.All(x => x.ActualEndDate != null) ? milestone.ScheduleSactivities.Max(x => x.ActualEndDate) : null;
        //                        milestone.IniStartDate = milestone.ScheduleSactivities.Min(x => x.SchStartDate);
        //                        milestone.IniEndDate = milestone.ScheduleSactivities.Max(x => x.SchEndDate);
        //                        milestone.BaseStartDate = milestone.ScheduleSactivities.Min(x => x.SchStartDate);
        //                        milestone.BaseEndDate = milestone.ScheduleSactivities.Max(x => x.SchEndDate);
        //                        milestone.Duration = CalendarExtension.WorkingDaysDuration(milestone.SchStartDate.Value, milestone.SchEndDate.Value, holidays);//TODO: check null
        //                        milestone.Calduration = (milestone.SchEndDate.Value - milestone.SchStartDate.Value).Days;//TODO: check null-- calduration is straigt diff in days 
        //                        milestone.UpdatedBy = createBy;
        //                        milestone.UpdatedDateTime = DateTime.Now;
        //                    }
        //                    await _context.ScheduleMilestones.BulkUpdateAsync(findAllMilestones);

        //                    //update the base and proj dates and duration for the new schedule as a whole
        //                    var findNewMilestones = _context.ScheduleMilestones.Include(x => x.ScheduleSactivities).Where(x => x.ScheduleId == findCurrentSchedule.ScheduleId).ToList();
        //                    findCurrentSchedule.DateToStart = findNewMilestones.Min(x => x.BaseStartDate);
        //                    findCurrentSchedule.DateToEnd = findNewMilestones.Min(x => x.IniStartDate);
        //                    findCurrentSchedule.ActualStartDate = findNewMilestones.Min(x => x.ActualStartDate);
        //                    findCurrentSchedule.ActualEndDate = findNewMilestones.All(x => x.ActualEndDate != null) ? findAllMilestones.Max(x => x.ActualEndDate) : null;
        //                    findCurrentSchedule.BaseEndDate = findNewMilestones.Max(x => x.BaseEndDate);
        //                    findCurrentSchedule.BaseStartDate = findNewMilestones.Min(x => x.BaseStartDate);
        //                    findCurrentSchedule.ProjEndDate = findNewMilestones.Max(x => x.IniEndDate);
        //                    findCurrentSchedule.BaseDuration = findCurrentSchedule.BaseStartDate != null && findCurrentSchedule.BaseEndDate != null ? CalendarExtension.WorkingDaysDuration(findCurrentSchedule.BaseStartDate.Value, findCurrentSchedule.BaseEndDate.Value, holidays) : null;
        //                    findCurrentSchedule.ProjDuration = findCurrentSchedule.BaseDuration;
        //                    findCurrentSchedule.BaseCalduration = findCurrentSchedule.BaseStartDate != null && findCurrentSchedule.BaseEndDate != null ? (findCurrentSchedule.BaseEndDate.Value - findCurrentSchedule.BaseStartDate.Value).Days : null;
        //                    findCurrentSchedule.ProjCalduration = findCurrentSchedule.BaseCalduration;
        //                    await _context.Schedules.BulkUpdateAsync(new List<Schedule>() { findCurrentSchedule });
        //                }



        //                //TODO: return the updated one?

        //                return new OkObjectResult(new ResponseModel<ScheduleDto> { Value = scheduleDto, IsSuccess = true, Message = "Updated Schedule from Template successfully" });
        //            }
        //            catch (Exception ex)
        //            {
        //                var username = User.Identity?.Name?.Split('@')[0];
        //#if DEBUG
        //                _logger.Debug(ex);
        //#else
        //                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
        //#endif
        //                return StatusCode(500, new ResponseModel<ScheduleDto> { IsSuccess = false, Message = "Failed to update Schedule from Template" });
        //            }
        //        }

        [HttpPost]
        public async Task<IActionResult> UpdateSchedulesFromTemplateAsync([FromBody] List<ScheduleDto> scheduleDtos)
        {
            try
            {
                string createBy = User.Identity.Name.Split('@')[0];
                var holidays = _context.CalendarsDays.Where(x => x.CalendarId == 1 && x.IsActive == true).Select(x => x.WorkDate).ToList();

                //TODO: bulk instead of this loop
                foreach (var scheduleDto in scheduleDtos)
                {
                    //get the current activities data
                    var findCurrentActivities = _context.ScheduleSactivities.Include(x => x.Sactivity).Where(x => x.ScheduleId == scheduleDto.ScheduleId && x.IsActive == true).ToList();
                    var currentActivitiesDto = _mapper.Map<List<ScheduleSactivityDto>>(findCurrentActivities);
                    var findCurrentMilestones = _context.ScheduleMilestones.Where(x => x.ScheduleId == scheduleDto.ScheduleId && x.IsActive == true).ToList();
                    var findCurrentLinks = _context.ScheduleSactivityLinks.Where(x => findCurrentActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid) && x.IsActive == true).ToList();
                    var findCurrentPreds = _context.ScheduleSactivityPreds.Where(x => findCurrentActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid) && x.IsActive == true).ToList();
                    var findCurrentSchedule = await _context.Schedules.SingleOrDefaultAsync(x => x.ScheduleId == scheduleDto.ScheduleId);

                    //create new schedule from template
                    var getTemplate = _context.Templates.Include("TemplateMilestones.TemplateSactivities").SingleOrDefault(x => x.TemplateId == scheduleDto.TemplateId);
                    var subdivisionId = _context.Jobs.FirstOrDefault(x => x.JobNumber == scheduleDto.JobNumber).SubdivisionId;
                    var templateMilestones = getTemplate.TemplateMilestones.ToList();
                    var getTemplateActivities = _context.TemplateSactivities.Include("TemplateM").Include("Sactivity").Where(x => templateMilestones.Select(y => y.TemplateMid).Contains(x.TemplateMid) && x.IsActive == true).ToList();
                    var getTradeSuppliers = _context.TradeSuppliers.Where(x => (x.SubdivisionId == subdivisionId || x.SubdivisionId == 1) && x.IsActive == true && x.DefaultSupplier == "T").ToList();

                    //Add any new milestones and activities
                    var addMilestones = templateMilestones.Where(x => !findCurrentMilestones.Select(y => y.MilestoneId).Contains(x.MilestoneId) && x.IsActive == true).Select(x => new ScheduleMilestone()
                    {
                        ScheduleId = scheduleDto.ScheduleId,
                        MilestoneId = x.MilestoneId,
                        Seq = x.Seq,
                        CreatedBy = createBy
                    });
                    await _context.ScheduleMilestones.BulkInsertAsync(addMilestones);
                    //update existing ones from template
                    foreach (var milestone in findCurrentMilestones)
                    {
                        milestone.Seq = templateMilestones.FirstOrDefault(x => x.MilestoneId == milestone.MilestoneId)?.Seq;
                        milestone.UpdatedBy = createBy;
                        milestone.UpdatedDateTime = DateTime.Now;
                    }
                    await _context.ScheduleMilestones.BulkUpdateAsync(findCurrentMilestones);

                    var allMilestones = _context.ScheduleMilestones.Where(x => x.ScheduleId == scheduleDto.ScheduleId && x.IsActive == true);//this should be the old ones and newly added ones
                    var addActivities = getTemplateActivities.Where(x => !findCurrentActivities.Select(y => y.SactivityId).Contains(x.SactivityId) && x.IsActive == true).Select(x => new ScheduleSactivity()
                    {
                        ScheduleId = scheduleDto.ScheduleId,
                        ScheduleMid = allMilestones.FirstOrDefault(y => y.MilestoneId == x.TemplateM.MilestoneId)?.ScheduleMid,
                        SactivityId = x.SactivityId,
                        SubNumber = getTradeSuppliers.FirstOrDefault(y => y.SubdivisionId == subdivisionId && y.TradeId == x.Sactivity.TradeId)?.SubNumber ?? getTradeSuppliers.FirstOrDefault(y => y.SubdivisionId == 1 && y.TradeId == x.Sactivity.TradeId)?.SubNumber ?? null,
                        Seq = x.Seq,
                        Duration = x.Duration,
                        LagTime = x.LagTime,
                        CreatedBy = createBy
                    }).ToList();
                    await _context.ScheduleSactivities.BulkInsertAsync(addActivities);


                    //update activities
                    var templateSactivityIds = getTemplateActivities.Select(x => x.SactivityId).ToList();
                    foreach (var activity in findCurrentActivities.Where(x => x.SactivityId != null && templateSactivityIds.Contains((int)x.SactivityId)))
                    {
                        var findTemplateActivity = getTemplateActivities.FirstOrDefault(x => x.SactivityId == activity.SactivityId);
                        activity.UpdateDate = DateTime.Now;
                        activity.UpdatedBy = createBy;
                        activity.LagTime = findTemplateActivity?.LagTime;
                        activity.Duration = findTemplateActivity?.Duration;
                        activity.Seq = findTemplateActivity?.Seq;
                        activity.ScheduleMid = allMilestones.FirstOrDefault(y => y.MilestoneId == findTemplateActivity.TemplateM.MilestoneId)?.ScheduleMid;
                        activity.SubNumber = getTradeSuppliers.FirstOrDefault(y => y.SubdivisionId == subdivisionId && y.TradeId == activity.Sactivity?.TradeId)?.SubNumber ?? getTradeSuppliers.FirstOrDefault(y => y.SubdivisionId == 1 && y.TradeId == activity.Sactivity?.TradeId)?.SubNumber ?? null;
                    }
                    await _context.ScheduleSactivities.BulkUpdateAsync(findCurrentActivities);

                    //delete ones not in new template
                    //TODO: for now, deleting all the preds and readding, really should just delete the ones that aren't needed aand update the ones that changed
                    var deleteAllPreds = _context.ScheduleSactivityPreds.Where(x => findCurrentActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                    await _context.ScheduleSactivityPreds.BulkDeleteAsync(deleteAllPreds);

                    // var deleteAllLinks = _context.ScheduleSactivityLinks.Where(x => findCurrentActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                    //await _context.ScheduleSactivityLinks.BulkDeleteAsync(deleteAllLinks);

                    var activitiesToDelete = findCurrentActivities.Where(x => x.SactivityId != null && !templateSactivityIds.Contains((int)x.SactivityId)).ToList();
                    foreach (var deleteAct in activitiesToDelete)
                    {
                        deleteAct.IsActive = false;
                        deleteAct.UpdateDate = DateTime.Now;
                        deleteAct.UpdatedBy = createBy;
                    }
                    await _context.ScheduleSactivities.BulkUpdateAsync(activitiesToDelete, options => options.ColumnInputExpression = x => new { x.IsActive, x.UpdatedBy, x.UpdatedDateTime });

                    var milestonesToDelete = findCurrentMilestones.Where(x => !templateMilestones.Select(y => y.MilestoneId).ToList().Contains(x.MilestoneId)).ToList();
                    foreach (var deleteMilestone in milestonesToDelete)
                    {
                        deleteMilestone.IsActive = false;
                        deleteMilestone.UpdatedDateTime = DateTime.Now;
                        deleteMilestone.UpdatedBy = createBy;
                    }
                    await _context.ScheduleMilestones.BulkUpdateAsync(milestonesToDelete, options => options.ColumnInputExpression = x => new { x.IsActive, x.UpdatedBy, x.UpdatedDateTime });


                    var getTemplatePreds = await _context.TemplateSactivityPreds.Include(x => x.TemplateA).Where(x => getTemplateActivities.Select(y => y.TemplateAid).Contains(x.TemplateAid) && x.IsActive == true).ToListAsync();
                    var getScheduleActivities = await _context.ScheduleSactivities.Where(x => x.ScheduleId != null && x.ScheduleId == scheduleDto.ScheduleId && x.IsActive == true).ToListAsync();
                    var predsToAdd = (
                                       from b in getTemplatePreds
                                       join c in getScheduleActivities on new { activityId = b.TemplateA.SactivityId } equals new { activityId = c.SactivityId ?? 0 }
                                       join d in getScheduleActivities on new { activityId = b.PredSactivityId } equals new { activityId = d.SactivityId ?? 0 }
                                       select new ScheduleSactivityPred()
                                       {
                                           ScheduleAid = c.ScheduleAid,
                                           PredSactivityId = b.PredSactivityId,
                                           PredScheduleAid = d.ScheduleAid,
                                           CreatedBy = createBy
                                       }).DistinctBy(x => new { x.ScheduleAid, x.PredSactivityId }).ToList();

                    InsertScheduleActivityPreds(createBy, predsToAdd);
                    var getAddedPreds = _context.ScheduleSactivityPreds.Where(x => getScheduleActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();

                    //add links to est
                    // var findSactivities = await _context.ScheduleSactivities.Include(x => x.ScheduleSactivityLinks).Where(x => x.ScheduleId == scheduleDto.ScheduleId && x.IsActive == true).ToListAsync();
                    var selectLinks = (from a in addActivities
                                       join b in _context.Estactivities.Where(x => x.JobNumber == scheduleDto.JobNumber && x.IsActive == true) on a.SactivityId equals b.SactivityId
                                       select new
                                       {
                                           SubNumber = b.SubNumber,
                                           ScheduleSactivityId = a.ScheduleAid,
                                           ScheduleSactivityLink = new ScheduleSactivityLink()
                                           {
                                               ScheduleAid = a.ScheduleAid,
                                               EstactivityId = b.EstactivityId,
                                               UpdatedBy = createBy,
                                               CreatedBy = createBy,
                                           }
                                       }).ToList();
                    //Insert the new ones
                    InsertScheduleActivityLinks(createBy, selectLinks.Select(x => x.ScheduleSactivityLink).ToList());


                    //then save the updated dates
                    //if released, update all the dates, if not released, there would be no dates anyway
                    //start at beginning and recalculate the whole schedule
                    var firstActivity = getScheduleActivities.Where(x => x.Seq == 1).FirstOrDefault();
                    //if firstActivity not already started, it won't hve sch dates so set it's sch start date values
                    if (firstActivity != null && firstActivity.SchStartDate != null)
                    {
                        firstActivity.SchStartDate = firstActivity.SchStartDate;
                        firstActivity.SchEndDate = firstActivity.SchEndDate ?? (firstActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)firstActivity.SchStartDate, (int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0, holidays) : firstActivity.SchStartDate.Value.AddDays((int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0));
                        firstActivity.BaseStartDate = firstActivity.BaseStartDate ?? firstActivity.SchStartDate;
                        firstActivity.BaseEndDate = firstActivity.BaseEndDate ?? firstActivity.SchEndDate;
                        firstActivity.IniStartDate = firstActivity.IniStartDate ?? scheduleDto.IniSchStartDate;
                        firstActivity.IniEndDate = firstActivity.IniEndDate ?? (firstActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)firstActivity.SchStartDate, (int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0, holidays) : firstActivity.SchStartDate.Value.AddDays((int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0));
                        var firstActivityplusminusdaysEnd = firstActivity.ActualEndDate != null ? firstActivity.ActualEndDate.Value : firstActivity.SchEndDate.Value;
                        firstActivity.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(firstActivity.BaseEndDate.Value, firstActivityplusminusdaysEnd, holidays);

                        var returnList = RecalculateDates(getAddedPreds, firstActivity, holidays, false);

                        var updateActivities = _context.ScheduleSactivities.Where(x => returnList.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                        foreach (var activity in updateActivities)
                        {
                            activity.SchStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
                            activity.SchEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
                            activity.BaseStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
                            activity.BaseEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
                            activity.IniStartDate = activity.IniStartDate ?? returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
                            activity.IniEndDate = activity.IniEndDate ?? returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
                            activity.Calduration = activity.SchStartDate != null && activity.SchEndDate != null ? (activity.SchEndDate.Value - activity.SchStartDate.Value).Days : null;
                            var plusminusdaysend = activity.ActualEndDate != null ? activity.ActualEndDate.Value : activity.SchEndDate.Value;
                            activity.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(activity.BaseEndDate.Value, plusminusdaysend, holidays);
                            activity.UpdatedBy = createBy;
                            activity.UpdateDate = DateTime.Now;
                            activity.UpdatedDateTime = DateTime.Now;
                        }
                        _context.ScheduleSactivities.BulkUpdate(updateActivities);

                        //then update the milestones and schedule as a whole dates
                        //update the milestones dates and durations
                        var findAllMilestones = _context.ScheduleMilestones.Include(x => x.ScheduleSactivities).Where(x => x.ScheduleId == findCurrentSchedule.ScheduleId && x.IsActive == true).ToList();
                        foreach (var milestone in findAllMilestones)
                        {
                            milestone.SchStartDate = milestone.ScheduleSactivities.Min(x => x.SchStartDate);
                            milestone.SchEndDate = milestone.ScheduleSactivities.Max(x => x.SchEndDate);
                            milestone.ActualStartDate = milestone.ScheduleSactivities.Min(x => x.ActualStartDate);
                            milestone.ActualEndDate = milestone.ScheduleSactivities.All(x => x.ActualEndDate != null) ? milestone.ScheduleSactivities.Max(x => x.ActualEndDate) : null;
                            milestone.IniStartDate = milestone.ScheduleSactivities.Min(x => x.IniStartDate);
                            milestone.IniEndDate = milestone.ScheduleSactivities.Max(x => x.IniEndDate);
                            milestone.BaseStartDate = milestone.ScheduleSactivities.Min(x => x.BaseStartDate);
                            milestone.BaseEndDate = milestone.ScheduleSactivities.Max(x => x.BaseEndDate);
                            milestone.Duration = milestone.SchEndDate != null && milestone.SchStartDate != null ? CalendarExtension.WorkingDaysDuration(milestone.SchStartDate.Value, milestone.SchEndDate.Value, holidays) : null;
                            milestone.Calduration = milestone.SchEndDate != null && milestone.SchStartDate != null ? (milestone.SchEndDate.Value - milestone.SchStartDate.Value).Days : null;
                            var milestoneplusminusdaysend = milestone.ActualEndDate != null ? milestone.ActualEndDate.Value : milestone.SchEndDate.Value;
                            milestone.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(milestone.BaseEndDate.Value, milestoneplusminusdaysend, holidays);
                            milestone.UpdatedBy = createBy;
                            milestone.UpdatedDateTime = DateTime.Now;
                        }
                        await _context.ScheduleMilestones.BulkUpdateAsync(findAllMilestones);

                        //update the base and proj dates and duration for the new schedule as a whole
                        var findNewMilestones = _context.ScheduleMilestones.Include(x => x.ScheduleSactivities).Where(x => x.ScheduleId == findCurrentSchedule.ScheduleId && x.IsActive == true).ToList();
                        findCurrentSchedule.DateToStart = findNewMilestones.Min(x => x.SchStartDate);
                        findCurrentSchedule.DateToEnd = findNewMilestones.Max(x => x.SchEndDate);
                        findCurrentSchedule.ActualStartDate = findNewMilestones.Min(x => x.ActualStartDate);
                        findCurrentSchedule.ActualEndDate = findNewMilestones.All(x => x.ActualEndDate != null) ? findAllMilestones.Max(x => x.ActualEndDate) : null;
                        findCurrentSchedule.BaseEndDate = findNewMilestones.Max(x => x.BaseEndDate);
                        findCurrentSchedule.BaseStartDate = findNewMilestones.Min(x => x.BaseStartDate);
                        findCurrentSchedule.IniSchStartDate = findNewMilestones.Min(x => x.IniStartDate);
                        findCurrentSchedule.IniSchEndDate = findNewMilestones.Max(x => x.IniEndDate);
                        findCurrentSchedule.ProjStartDate = findNewMilestones.Min(x => x.IniStartDate);
                        findCurrentSchedule.ProjEndDate = findNewMilestones.Max(x => x.IniEndDate);
                        findCurrentSchedule.BaseDuration = findCurrentSchedule.BaseStartDate != null && findCurrentSchedule.BaseEndDate != null ? CalendarExtension.WorkingDaysDuration(findCurrentSchedule.BaseStartDate.Value, findCurrentSchedule.BaseEndDate.Value, holidays) : null;
                        findCurrentSchedule.ProjDuration = findCurrentSchedule.BaseDuration;
                        findCurrentSchedule.BaseCalduration = findCurrentSchedule.BaseStartDate != null && findCurrentSchedule.BaseEndDate != null ? (findCurrentSchedule.BaseEndDate.Value - findCurrentSchedule.BaseStartDate.Value).Days : null;
                        findCurrentSchedule.ProjCalduration = findCurrentSchedule.BaseCalduration;
                        var scheduleplusminusdaysend = findCurrentSchedule.ActualEndDate != null ? findCurrentSchedule.ActualEndDate.Value : findCurrentSchedule.DateToEnd.Value;
                        findCurrentSchedule.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(findCurrentSchedule.BaseEndDate.Value, scheduleplusminusdaysend, holidays);
                        await _context.Schedules.BulkUpdateAsync(new List<Schedule>() { findCurrentSchedule });
                    }
                }
                return new OkObjectResult(new ResponseModel<List<ScheduleDto>> { Value = scheduleDtos, IsSuccess = true, Message = "Updated Schedule from Template successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleDto>> { IsSuccess = false, Message = "Failed to update Schedule from Template" });
            }
        }
        [HttpPost]
        public async Task<IActionResult> DeleteScheduleAsync([FromBody] ScheduleDto deleteSchedule)
        {
            try
            {
                               
                var findSchedule = await _context.Schedules.SingleOrDefaultAsync(x => x.ScheduleId == deleteSchedule.ScheduleId);
                {
                    if (findSchedule == null)
                    {
                        return NotFound(new ResponseModel<ScheduleDto> { IsSuccess = false, Message = "Schedule not found" });
                    }
                    if(findSchedule.IniSchApproved == true && !findSchedule.JobNumber.Contains("TEST"))
                    {
                        return Ok(new ResponseModel<ScheduleDto> { IsSuccess = false, Message = "Cannot delete released schedule" });
                    }
                    findSchedule.IsActive = false;
                    findSchedule.UpdatedBy = User.Identity.Name.Split('@')[0];
                    findSchedule.UpdatedDateTime = DateTime.Now;
                    await _context.Schedules.BulkUpdateAsync(new List<Schedule>() { findSchedule });
                }

                return Ok(new ResponseModel<ScheduleDto> { Value = deleteSchedule, IsSuccess = true, Message = "Deleted schedule" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheduleDto> { IsSuccess = false, Message = "Failed to delete schedule" });

            }
        }
        [HttpPost]
        public async Task<IActionResult> SchedulesFromTemplateAsync([FromBody] List<ScheduleDto> scheduleDtos)
        {
            try
            {
                //TODO: warn if there is already a schedule for the job?  - current WMS logic is delete (hard delete) and add new
                //TODO: require pick template and jobs
                //TODO: too slow
                //TODO: this is assuming all one template and all one subdivision
                //TODO: check scheduleDtos not null and not empty
                string createBy = User.Identity.Name.Split('@')[0];
                if (scheduleDtos.First().TemplateId == null || scheduleDtos.First().TemplateId == 0)
                {
                    return StatusCode(500, new ResponseModel<List<ScheduleDto>> { IsSuccess = false, Message = "No template selected" });
                }
                


                var getTemplate = _context.Templates.Include("TemplateMilestones").SingleOrDefault(x => x.TemplateId == scheduleDtos.First().TemplateId && x.IsActive == true);
                var templateMilestones = getTemplate.TemplateMilestones.ToList();
                var getTemplateActivities = _context.TemplateSactivities.Include("TemplateM").Include("Sactivity").Where(x => templateMilestones.Select(y => y.TemplateMid).Contains(x.TemplateMid) && x.IsActive == true).ToList();
                var getTemplatePreds = await _context.TemplateSactivityPreds.Include(x => x.TemplateA).Where(x => getTemplateActivities.Select(y => y.TemplateAid).Contains(x.TemplateAid) && x.IsActive == true).ToListAsync();
                var allJobs = _context.Jobs.Where(x => x.IsActive == true).ToList();
                var subdivisionId = allJobs.FirstOrDefault(x => x.JobNumber == scheduleDtos.First().JobNumber).SubdivisionId;
                var allTradeSuppliersThisSubdivisionOrDefault = _context.TradeSuppliers.Where(x => x.IsActive == true && x.DefaultSupplier == "T" && (x.SubdivisionId == subdivisionId || x.SubdivisionId == 1)).ToList();
                var activitiesToAdd = new List<ScheduleSactivity>();
                var milestonesToAdd = new List<ScheduleMilestone>();


                var findExisting = _context.Schedules.Include(x => x.ScheduleMilestones).ThenInclude(x => x.ScheduleSactivities).ThenInclude(x => x.ScheduleSactivityPreds).Include(x => x.ScheduleMilestones).ThenInclude(x => x.ScheduleSactivities).ThenInclude(x => x.ScheduleSactivityLinks).Where(x => scheduleDtos.Select(y => y.JobNumber).Contains(x.JobNumber)).ToList();

                if(findExisting.Any(x => x.IniSchStartDate != null && x.IniSchApproved == true && !x.JobNumber.Contains("TEST")))
                {
                   //TODO: don't allow update released 
                }
                


                // var findExistingMilestones = findExisting.SelectMany(x => x.ScheduleMilestones).ToList();
                // var findExisting = _context.Schedules.Include(x => x.ScheduleMilestones).ThenInclude(x => x.ScheduleSactivities).ThenInclude(x => x.ScheduleSactivityPreds).Include(x => x.ScheduleMilestones).ThenInclude(x => x.ScheduleSactivities).ThenInclude(x => x.ScheduleSactivityLinks).Where(x => scheduleDtos.Select(y => y.JobNumber).Contains(x.JobNumber)).ToList();
                var findExistingSchedules = _context.Schedules.Where(x => scheduleDtos.Select(y => y.JobNumber).Contains(x.JobNumber)).ToList();
                var findExistingMilestones = _context.ScheduleMilestones.Where(x => findExistingSchedules.Select(y => y.ScheduleId).Contains(x.ScheduleId)).ToList();
                // var findExistingMilestones = findExisting.SelectMany(x => x.ScheduleMilestones).ToList();

                // var findExistingActivities = findExisting.SelectMany(x => x.ScheduleMilestones.SelectMany(y => y.ScheduleSactivities)).ToList();
                var findExistingActivities = _context.ScheduleSactivities.Where(x => findExistingMilestones.Select(y => y.ScheduleMid).ToList().Contains((int)x.ScheduleMid)).ToList();

                var findExistingLinks = _context.ScheduleSactivityLinks.Where(x => findExistingActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                var findExistingPreds = _context.ScheduleSactivityPreds.Where(x => findExistingActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                //var findExistingLinks = findExisting.SelectMany(x => x.ScheduleMilestones.SelectMany(y => y.ScheduleSactivities.SelectMany(z => z.ScheduleSactivityLinks))).ToList();
                //var findExistingPreds = findExisting.SelectMany(x => x.ScheduleMilestones.SelectMany(y => y.ScheduleSactivities.SelectMany(z => z.ScheduleSactivityPreds))).ToList();

                //deactivate the old ones, but not the schedule
                foreach (var milestone in findExistingMilestones)
                {
                    milestone.IsActive = false;
                    milestone.UpdatedBy = createBy;
                    milestone.UpdatedDateTime = DateTime.Now;
                }
                foreach (var activity in findExistingActivities)
                {
                    activity.IsActive = false;
                    activity.UpdatedBy = createBy;
                    activity.UpdatedDateTime = DateTime.Now;
                }
                foreach (var link in findExistingLinks)
                {
                    link.IsActive = false;
                    link.UpdatedBy = createBy;
                    link.UpdatedDateTime = DateTime.Now;
                }
                foreach (var milestone in findExistingPreds)
                {
                    milestone.IsActive = false;
                    milestone.UpdatedBy = createBy;
                    milestone.UpdatedDateTime = DateTime.Now;
                }
                await _context.ScheduleSactivityLinks.BulkUpdateAsync(findExistingLinks);
                await _context.ScheduleSactivityPreds.BulkUpdateAsync(findExistingPreds);
                await _context.ScheduleSactivities.BulkUpdateAsync(findExistingActivities);
                await _context.ScheduleMilestones.BulkUpdateAsync(findExistingMilestones);
                //await _context.ScheduleSactivityLinks.BulkDeleteAsync(findExistingLinks);
                //await _context.ScheduleSactivityPreds.BulkDeleteAsync(findExistingPreds);
                //await _context.ScheduleSactivities.BulkDeleteAsync(findExistingActivities);
                //await _context.ScheduleMilestones.BulkDeleteAsync(findExistingMilestones);//Why does this take so long??

                //await _context.Schedules.BulkDeleteAsync(findExistingSchedules);
                foreach (var schedule in findExisting)
                {
                    schedule.TemplateId = getTemplate.TemplateId;
                    schedule.UpdatedBy = createBy;
                    schedule.DateCreated = DateTime.Now;
                    schedule.IsActive = true;
                }
                await _context.Schedules.BulkUpdateAsync(findExisting);
                var newSchedules = scheduleDtos.Where(x => !findExisting.Select(y => y.JobNumber).Contains(x.JobNumber)).Select(x => new Schedule()
                {
                    TemplateId = x.TemplateId,
                    Published = x.Published,
                    JobNumber = x.JobNumber,
                    CreatedBy = createBy,
                    DateCreated = DateTime.Now,
                }).ToList();
                await _context.Schedules.BulkInsertAsync(newSchedules);

                var allSchedules = new List<Schedule>();
                allSchedules.AddRange(newSchedules);
                allSchedules.AddRange(findExisting);
                foreach (var schedule in allSchedules)
                {
                    var addMilestones = getTemplate.TemplateMilestones.Select(x => new ScheduleMilestone()
                    {
                        ScheduleId = schedule.ScheduleId,
                        MilestoneId = x.MilestoneId,
                        Seq = x.Seq,
                        CreatedBy = createBy
                    }).ToList();
                    milestonesToAdd.AddRange(addMilestones);
                }
                string? result = InsertScheduleMilestones(createBy, milestonesToAdd);

                var newScheduleIds = allSchedules.Select(s => s.ScheduleId).ToList();
                var findNewAddedMilestones = await _context.ScheduleMilestones.Where(x => newScheduleIds.Contains(x.ScheduleId) && x.IsActive == true).ToListAsync();

                var addActivities1 = (from a in allSchedules
                                      from b in getTemplateActivities
                                      join c in findNewAddedMilestones on new { scheduleId = a.ScheduleId, milestoneId = b.TemplateM.MilestoneId } equals new { scheduleId = c.ScheduleId, milestoneId = c.MilestoneId }
                                      from d in allTradeSuppliersThisSubdivisionOrDefault.Where(x => x.SubdivisionId == subdivisionId && x.TradeId == b.Sactivity.TradeId && x.DefaultSupplier == "T").DefaultIfEmpty()
                                      from e in allTradeSuppliersThisSubdivisionOrDefault.Where(x => x.SubdivisionId == 1 && x.TradeId == b.Sactivity.TradeId && x.DefaultSupplier == "T").DefaultIfEmpty()
                                      select new ScheduleSactivity()
                                      {
                                          ScheduleId = a.ScheduleId,
                                          ScheduleMid = c.ScheduleMid,
                                          SactivityId = b.SactivityId,
                                          //TODO: should this subNumber come from an ESTACTIVITY instead?? When the template is created it probably can't
                                          SubNumber = d?.SubNumber != 0 && d?.SubNumber != null ? d.SubNumber : e?.SubNumber,
                                          Seq = b.Seq,
                                          Duration = b.Duration,
                                          LagTime = b.LagTime,
                                          //DATES to be calculated after preschedule checked
                                          CreatedBy = createBy
                                      }).ToList();

                //foreach (var schedule in newSchedules)
                //{
                //    var addActivities = getTemplateActivities.Select(x => new ScheduleSactivity()
                //    {
                //        ScheduleId = schedule.ScheduleId,
                //        ScheduleMid = findNewAddedMilestones.FirstOrDefault(y => y.MilestoneId == x.TemplateM.MilestoneId && y.ScheduleId == schedule.ScheduleId)?.ScheduleMid,
                //        SactivityId = x.SactivityId,
                //        //TODO: should this subNumber come from an ESTACTIVITY instead?? When the template is created it probably can't
                //        SubNumber = allTradeSuppliersThisSubdivisionOrDefault.FirstOrDefault(y => y.SubdivisionId == subdivisionId && y.TradeId == x.Sactivity.TradeId)?.SubNumber ?? allTradeSuppliersThisSubdivisionOrDefault.FirstOrDefault(y => y.SubdivisionId == 1 && y.TradeId == x.Sactivity.TradeId)?.SubNumber ?? null,//TODO: division default if no subnumber? //
                //        Seq = x.Seq,
                //        Duration = x.Duration,
                //        LagTime = x.LagTime,
                //        //DATES to be calculated after preschedule checked
                //        CreatedBy = createBy
                //    }).ToList();
                //    //TODO: predecessors, all the fields, etc
                //    activitiesToAdd.AddRange(addActivities);
                //}

                //EF extenstions bulk insert - 1.3 sec on 10,000 records
                //await _context.ScheduleSactivities.BulkInsertAsync(addActivities1);

                InsertScheduleActivities(createBy, addActivities1);

                var getAddedActivities = await _context.ScheduleSactivities.Where(x => x.ScheduleId != null && newScheduleIds.Contains((int)x.ScheduleId) && x.IsActive == true).ToListAsync();
                var predsToAdd = new List<ScheduleSactivityPred>();

                var predsToAdd1 = (from a in allSchedules
                                   from b in getTemplatePreds
                                   join c in getAddedActivities on new { scheduleId = a.ScheduleId, activityId = b.TemplateA.SactivityId } equals new { scheduleId = c.ScheduleId ?? 0, activityId = c.SactivityId ?? 0 }
                                   join d in getAddedActivities on new { scheduleId = a.ScheduleId, activityId = b.PredSactivityId } equals new { scheduleId = d.ScheduleId ?? 0, activityId = d.SactivityId ?? 0 }
                                   select new ScheduleSactivityPred()
                                   {
                                       ScheduleAid = c.ScheduleAid,
                                       PredSactivityId = b.PredSactivityId,
                                       PredScheduleAid = d.ScheduleAid,
                                       CreatedBy = createBy
                                   }).DistinctBy(x => new { x.ScheduleAid, x.PredScheduleAid }).ToList();
                //foreach (var schedule in newSchedules)
                //{
                //    //TODO: there can be bad data where the predecessor activity is not actually in the template. 
                //    var newPreds = getTemplatePreds.Select(x => new ScheduleSactivityPred()
                //    {
                //        ScheduleAid = getAddedActivities.FirstOrDefault(y => y.ScheduleId == schedule.ScheduleId && y.SactivityId == x.TemplateA.SactivityId).ScheduleAid,
                //        PredSactivityId = x.PredSactivityId,
                //        PredScheduleAid = getAddedActivities.FirstOrDefault(y => y.ScheduleId == schedule.ScheduleId && y.SactivityId == x.PredSactivityId)?.ScheduleAid//This fails if there are activities in the preds that are not actully in the template, we could just set null, but let's exclude those because they are wrong
                //    }).ToList();
                //    predsToAdd.AddRange(newPreds.Where(x => x.PredScheduleAid != null));
                //}

                InsertScheduleActivityPreds(createBy, predsToAdd1);


                //for TEST jobs, create dates so don't have to "release"
                foreach(var jobSchedule in scheduleDtos.Where(x => x.JobNumber.Contains("TEST")))
                {
                    jobSchedule.IniSchApproved = true;
                    jobSchedule.IniSchStartDate = DateTime.Now;
                    jobSchedule.ScheduleId = _context.Schedules.FirstOrDefault(x => x.JobNumber == jobSchedule.JobNumber && x.IsActive == true).ScheduleId;
                    await UpdatePrescheduleAsync(jobSchedule);
                }

                return new OkObjectResult(new ResponseModel<List<ScheduleDto>> { Value = scheduleDtos, IsSuccess = true, Message = "Generated Schedules from Template successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0]; 
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleDto>> { IsSuccess = false, Message = "Failed to generate Schedules from Template" });
            }
        }

        private void InsertScheduleActivityPreds(string createBy, List<ScheduleSactivityPred> predsToAdd)
        {
            //insert activities                
            DataTable PredsDataTable = new DataTable("ScheduleSactivityPreds");
            DataColumn ScheduleAid = new DataColumn("ScheduleAid", typeof(int));
            PredsDataTable.Columns.Add(ScheduleAid);
            DataColumn PredSactivityId = new DataColumn("PredSactivityId", typeof(int));
            PredsDataTable.Columns.Add(PredSactivityId);
            DataColumn PredScheduleAid = new DataColumn("PredScheduleAid", typeof(int));
            PredsDataTable.Columns.Add(PredScheduleAid);
            DataColumn CreatedBy = new DataColumn("CreatedBy", typeof(string));
            PredsDataTable.Columns.Add(CreatedBy);

            foreach (var pred in predsToAdd)
            {
                PredsDataTable.Rows.Add(pred.ScheduleAid, pred.PredSactivityId, pred.PredScheduleAid, createBy);
            }

            var conn = _configuration.GetConnectionString("ERPConnection");
            using (var connection = new SqlConnection(conn))
            {
                connection.Open();
                using (var bulkCopy = new SqlBulkCopy(connection))
                {
                    bulkCopy.DestinationTableName = "SCHEDULE_SACTIVITY_PRED";
                    bulkCopy.ColumnMappings.Add("ScheduleAid", "SCHEDULE_AID");
                    bulkCopy.ColumnMappings.Add("PredScheduleAid", "PRED_SCHEDULE_AID");
                    bulkCopy.ColumnMappings.Add("PredSactivityId", "PRED_SACTIVITY_ID");
                    bulkCopy.ColumnMappings.Add("CreatedBy", "CreatedBy");
                    try
                    {
                        bulkCopy.WriteToServer(PredsDataTable);
                    }
                    catch (Exception ex)
                    {
#if DEBUG
                        _logger.Debug(ex);
#else
                    _logger.Error(ex);
#endif
                    }
                }
            }
        }

        private void InsertScheduleActivityLinks(string createBy, List<ScheduleSactivityLink> linksToAdd)
        {
            //insert activities                
            DataTable LinksSactivityTable = new DataTable("ScheduleSactivityLinkss");
            DataColumn ScheduleAid = new DataColumn("ScheduleAid", typeof(int));
            LinksSactivityTable.Columns.Add(ScheduleAid);
            DataColumn EstActivityId = new DataColumn("EstActivityId", typeof(int));
            LinksSactivityTable.Columns.Add(EstActivityId);
            DataColumn CreatedBy = new DataColumn("CreatedBy", typeof(string));
            LinksSactivityTable.Columns.Add(CreatedBy);
            DataColumn UpdatedBy = new DataColumn("UpdatedBy", typeof(string));
            LinksSactivityTable.Columns.Add(UpdatedBy);
            foreach (var link in linksToAdd)
            {
                LinksSactivityTable.Rows.Add(link.ScheduleAid, link.EstactivityId, createBy, createBy);
            }

            var conn = _configuration.GetConnectionString("ERPConnection");
            using (var connection = new SqlConnection(conn))
            {
                connection.Open();
                using (var bulkCopy = new SqlBulkCopy(connection))
                {
                    bulkCopy.DestinationTableName = "SCHEDULE_SACTIVITY_LINK";
                    bulkCopy.ColumnMappings.Add("ScheduleAid", "SCHEDULE_AID");
                    bulkCopy.ColumnMappings.Add("EstActivityId", "ESTACTIVITY_ID");
                    bulkCopy.ColumnMappings.Add("CreatedBy", "CreatedBy");
                    bulkCopy.ColumnMappings.Add("UpdatedBy", "UpdatedBy");
                    try
                    {
                        bulkCopy.WriteToServer(LinksSactivityTable);
                    }
                    catch (Exception ex)
                    {
                        var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                        _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                    }
                }
            }
        }

        private void InsertScheduleActivities(string createBy, List<ScheduleSactivity> activitiesToAdd)
        {
            //insert activities                
            DataTable ActivitiesDataTable = new DataTable("ScheduleSActivities");
            DataColumn ScheduleMid = new DataColumn("ScheduleMid", typeof(int));
            ActivitiesDataTable.Columns.Add(ScheduleMid);
            DataColumn ScheduleId2 = new DataColumn("ScheduleId2", typeof(int));
            ActivitiesDataTable.Columns.Add(ScheduleId2);
            DataColumn SactivityId = new DataColumn("SactivityId", typeof(int));
            ActivitiesDataTable.Columns.Add(SactivityId);
            DataColumn SubNumber = new DataColumn("SubNumber", typeof(int));
            ActivitiesDataTable.Columns.Add(SubNumber);
            DataColumn Seq2 = new DataColumn("Seq2", typeof(int));
            ActivitiesDataTable.Columns.Add(Seq2);
            DataColumn Duration2 = new DataColumn("Duration2", typeof(int));
            ActivitiesDataTable.Columns.Add(Duration2);
            DataColumn LagTime = new DataColumn("LagTime", typeof(int));
            ActivitiesDataTable.Columns.Add(LagTime);
            DataColumn CreatedBy2 = new DataColumn("CreatedBy2", typeof(string));
            ActivitiesDataTable.Columns.Add(CreatedBy2);

            foreach (var activity in activitiesToAdd)
            {
                ActivitiesDataTable.Rows.Add(activity.ScheduleMid, activity.ScheduleId, activity.SactivityId, activity.SubNumber, activity.Seq, activity.Duration, activity.LagTime, createBy);
            }

            var conn = _configuration.GetConnectionString("ERPConnection");
            using (var connection = new SqlConnection(conn))
            {
                connection.Open();
                using (var bulkCopy = new SqlBulkCopy(connection))
                {
                    bulkCopy.DestinationTableName = "SCHEDULE_SACTIVITY";
                    bulkCopy.ColumnMappings.Add("ScheduleMid", "SCHEDULE_MID");
                    bulkCopy.ColumnMappings.Add("ScheduleId2", "SCHEDULE_ID");
                    bulkCopy.ColumnMappings.Add("SactivityId", "SACTIVITY_ID");
                    bulkCopy.ColumnMappings.Add("SubNumber", "SUB_NUMBER");
                    bulkCopy.ColumnMappings.Add("Seq2", "SEQ");
                    bulkCopy.ColumnMappings.Add("Duration2", "DURATION");
                    bulkCopy.ColumnMappings.Add("LagTime", "LAG_TIME");
                    bulkCopy.ColumnMappings.Add("CreatedBy2", "CreatedBy");
                    try
                    {
                        bulkCopy.WriteToServer(ActivitiesDataTable);
                    }
                    catch (Exception ex)
                    {
                        var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                        _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                    }
                }
            }
        }

        private string? InsertScheduleMilestones(string createBy, List<ScheduleMilestone> milestonesToAdd)
        {
            //insert milestones                
            DataTable MilestonesDataTable = new DataTable("ScheduleMilestones");
            DataColumn ScheduleId = new DataColumn("ScheduleId", typeof(int));
            MilestonesDataTable.Columns.Add(ScheduleId);
            DataColumn MilestoneId = new DataColumn("MilestoneId", typeof(int));
            MilestonesDataTable.Columns.Add(MilestoneId);
            DataColumn Seq = new DataColumn("Seq", typeof(int));
            MilestonesDataTable.Columns.Add(Seq);
            DataColumn Duration = new DataColumn("Duration", typeof(int));
            MilestonesDataTable.Columns.Add(Duration);
            DataColumn CreatedBy = new DataColumn("CreatedBy", typeof(string));
            MilestonesDataTable.Columns.Add(CreatedBy);

            foreach (var milestone in milestonesToAdd)
            {
                MilestonesDataTable.Rows.Add(milestone.ScheduleId, milestone.MilestoneId, milestone.Seq, milestone.Duration, createBy);
            }

            var conn = _configuration.GetConnectionString("ERPConnection");
            using (var connection = new SqlConnection(conn))
            {
                connection.Open();
                using (var bulkCopy = new SqlBulkCopy(connection))
                {
                    bulkCopy.DestinationTableName = "SCHEDULE_MILESTONE";
                    bulkCopy.ColumnMappings.Add("ScheduleId", "SCHEDULE_ID");
                    bulkCopy.ColumnMappings.Add("MilestoneId", "MILESTONE_ID");
                    bulkCopy.ColumnMappings.Add("Seq", "SEQ");
                    bulkCopy.ColumnMappings.Add("Duration", "DURATION");
                    bulkCopy.ColumnMappings.Add("CreatedBy", "CreatedBy");
                    try
                    {
                        bulkCopy.WriteToServer(MilestonesDataTable);
                    }
                    catch (Exception ex)
                    {
                        var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                        _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                    }
                }
            }

            return "succeeded";
        }

        [HttpGet]
        public async Task<IActionResult> MilestonesAsync()
        {
            try
            {
                var milestones = await _context.Milestones.Where(x => x.IsActive == true && x.DivId == 1).OrderBy(x => x.Seq).ToListAsync();
                var milestonesDto = _mapper.Map<List<MilestoneDto>>(milestones);
                return new OkObjectResult(new ResponseModel<List<MilestoneDto>> { Value = milestonesDto, IsSuccess = true, Message = "Fetched Milestones successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MilestoneDto>> { IsSuccess = false, Message = "Failed to get Milestones" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddMilestoneToTemplateAsync([FromBody] ScheduleTemplateTreeModel addMilestone)
        {
            try
            {                
                //prevent add duplicate
                var findExisting = _context.TemplateMilestones.Include(x => x.Milestone).Where(x => x.TemplateId == addMilestone.TemplateMilestone.TemplateId && x.Milestone.MilestoneId == addMilestone.Milestone.MilestoneId).ToList();
                if (findExisting.Any())
                {

                    //if inactive reactivate
                    var foundInactiveMilestones = findExisting.Where(x => x.IsActive == false).ToList();
                    if(foundInactiveMilestones.Count != 0)
                    {
                        var reactivateMilestone = foundInactiveMilestones.First();
                        reactivateMilestone.IsActive = true;
                        reactivateMilestone.Updateddatetime = DateTime.Now;
                        reactivateMilestone.UpdatedBy = User.Identity.Name.Split('@')[0];
                        reactivateMilestone.Seq = addMilestone.TemplateMilestone.Seq;
                        _context.TemplateMilestones.Update(reactivateMilestone);
                        await _context.SaveChangesAsync();
                        return new OkObjectResult(new ResponseModel<ScheduleTemplateTreeModel> { Value = addMilestone, IsSuccess = true, Message = " Milestone existed and was re-activated" });
                    }

                    return new OkObjectResult(new ResponseModel<ScheduleTemplateTreeModel> { Value = addMilestone, IsSuccess = false, Message = "Milestone already exists in template" });
                }

                
                var newMilestone = new TemplateMilestone()
                {
                    MilestoneId = addMilestone.Milestone.MilestoneId,
                    TemplateId = addMilestone.TemplateMilestone.TemplateId,
                    CreatedBy = User.Identity.Name.Split('@')[0],
                    Seq = addMilestone.TemplateMilestone.Seq
                };
                _context.TemplateMilestones.Add(newMilestone);
                await _context.SaveChangesAsync();
                var returnMilestone = _mapper.Map<TemplateMilestoneDto>(newMilestone);
                addMilestone.TemplateMilestone = returnMilestone;
                return new OkObjectResult(new ResponseModel<ScheduleTemplateTreeModel> { Value = addMilestone, IsSuccess = true, Message = "Added Milestone to Template successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheduleTemplateTreeModel> { IsSuccess = false, Message = "Failed to add Milestone to Template" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddActivityToTemplateAsync([FromBody] ScheduleTemplateTreeModel addSactivity)
        {
            try
            {
                //TODO: default the predecessor to the item before it in the seq

                //prevent add duplicate
                var findExisting = _context.TemplateSactivities.Include(x => x.TemplateM).Where(x => x.TemplateM.TemplateId == addSactivity.TemplateMilestone.TemplateId && x.Sactivity.SactivityId == addSactivity.Sactivity.SactivityId).ToList();
                if (findExisting.Any())
                {
                    //reactivate if existing and inactive
                    var foundInactiveActivities = findExisting.Where(x => x.IsActive == false).ToList();
                    if (foundInactiveActivities.Count != 0)
                    {
                        var reactivateActivity = foundInactiveActivities.First();
                        reactivateActivity.IsActive = true;
                        reactivateActivity.TemplateMid = addSactivity.TemplateMilestone.TemplateMid;
                        reactivateActivity.Duration = addSactivity.Sactivity.DefaultDuration;
                        reactivateActivity.LagTime = addSactivity.Sactivity.DefaultLagtime;
                        reactivateActivity.GrossLag = addSactivity.Sactivity.GrossLag;
                        reactivateActivity.CreatedBy = User.Identity.Name.Split('@')[0];
                        reactivateActivity.Seq = addSactivity.Sactivity.Seq;
                        reactivateActivity.Updateddatetime = DateTime.Now;
                        reactivateActivity.UpdatedBy = User.Identity.Name.Split('@')[0];
                        _context.TemplateSactivities.Update(reactivateActivity);
                        await _context.SaveChangesAsync();
                        return new OkObjectResult(new ResponseModel<ScheduleTemplateTreeModel> { Value = addSactivity, IsSuccess = true, Message = "Activity existed and was re-activated" });
                    }
                    return new OkObjectResult(new ResponseModel<ScheduleTemplateTreeModel> { Value = addSactivity, IsSuccess = false, Message = "Activity already exists in template" });
                }

                var addActivity = new TemplateSactivity()
                {
                    // MilestoneId = addSactivity.Milestone.MilestoneId,
                    //TemplateId = addSactivity.TemplateMilestone.TemplateId,
                    SactivityId = addSactivity.Sactivity.SactivityId,
                    TemplateMid = addSactivity.TemplateMilestone.TemplateMid,
                    Duration = addSactivity.Sactivity.DefaultDuration,
                    LagTime = addSactivity.Sactivity.DefaultLagtime,
                    GrossLag = addSactivity.Sactivity.GrossLag,
                    CreatedBy = User.Identity.Name.Split('@')[0],
                    Seq = addSactivity.Sactivity.Seq,
                    // TemplateSactivityPreds = new List<TemplateSactivityPred>() { newPredecessor }
                };
                _context.TemplateSactivities.Add(addActivity);
                await _context.SaveChangesAsync();
                if (addSactivity.TemplateSactivity.PredIds != null && addSactivity.TemplateSactivity.PredIds.Count != 0)
                {
                    var newPredecessor = new TemplateSactivityPred()
                    {
                        PredSactivityId = addSactivity.TemplateSactivity.PredIds.First(),//TODO: fix //TODO: heather says this is actually supposed to be sactivity id, not template sactivity id. This is bad design. 
                        TemplateAid = addActivity.TemplateAid,
                    };
                    //strange, there are already predecessors for items newly added? 
                    _context.TemplateSactivityPreds.Add(newPredecessor);
                    await _context.SaveChangesAsync();
                }

                //TODO: delete old predecessor?? there shouldn't be any 
                //TODO: find the template sactivity again so it gets the predecessors 
                var findActivity = _context.TemplateSactivities.Include("TemplateSactivityPreds.PredSactivity").SingleOrDefault(x => x.TemplateAid == addActivity.TemplateAid);
                var returnTemplateSactivity = _mapper.Map<TemplateSactivityDto>(findActivity);
                addSactivity.TemplateSactivity = returnTemplateSactivity;
                addSactivity.PredecessorIds = findActivity.TemplateSactivityPreds.Select(x => x.PredSactivityId).ToList();
                addSactivity.Predecessors = string.Join(", ", findActivity.TemplateSactivityPreds.Where(x => x.PredSactivity != null).Select(y => y.PredSactivity.ActivityName));
                return new OkObjectResult(new ResponseModel<ScheduleTemplateTreeModel> { Value = addSactivity, IsSuccess = true, Message = "Added activity to Template successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheduleTemplateTreeModel> { IsSuccess = false, Message = "Failed to add Activity to Template" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddMilestoneAsync([FromBody] MilestoneDto addMilestone)
        {
            try
            {
                int maxSeqNumber = _context.Milestones.Where(x => x.IsActive == true).Max(x => x.Seq) ?? 0;
                var newMilestone = new Milestone()
                {
                    MilestoneName = addMilestone.MilestoneName,
                    CreatedBy = User.Identity.Name.Split('@')[0],
                    Seq = maxSeqNumber + 1,
                    DivId = 1,
                };
                _context.Milestones.Add(newMilestone);
                await _context.SaveChangesAsync();
                var returnMilestone = _mapper.Map<MilestoneDto>(newMilestone);
                return new OkObjectResult(new ResponseModel<MilestoneDto> { Value = returnMilestone, IsSuccess = true, Message = "Added milestone successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MilestoneDto> { IsSuccess = false, Message = "Failed to add milestone" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateMilestonesSeqAsync([FromBody] List<MilestoneDto> updateMilestones)
        {
            try
            {
                //assuming new sequence is saved in the update milestones seq
                var milestonesToUpdate = new List<Milestone>();
                foreach (var item in updateMilestones)
                {
                    var findMilestone = _context.Milestones.SingleOrDefault(x => x.MilestoneId == item.MilestoneId);
                    if (findMilestone != null)
                    {
                        findMilestone.Seq = item.Seq;
                        milestonesToUpdate.Add(findMilestone);
                    }
                }
                _context.Milestones.UpdateRange(milestonesToUpdate);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<List<MilestoneDto>> { Value = updateMilestones, IsSuccess = true, Message = "Updated Milestones successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MilestoneDto>> { IsSuccess = false, Message = "Failed to update Milestones" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateMilestoneAsync([FromBody] MilestoneDto addMilestone)
        {
            try
            {
                var findMilestone = _context.Milestones.Where(x => x.MilestoneId == addMilestone.MilestoneId).SingleOrDefault();
                findMilestone.MilestoneName = addMilestone.MilestoneName;
                //findMilestone.Seq = addMilestone.Seq;
                findMilestone.UpdatedBy = User.Identity.Name.Split('@')[0];
                findMilestone.UpdatedDateTime = DateTime.Now;
                _context.Milestones.Update(findMilestone);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<MilestoneDto> { Value = addMilestone, IsSuccess = true, Message = "Updated milestone successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MilestoneDto> { IsSuccess = false, Message = "Failed to update milestone" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteMilestoneAsync([FromBody] MilestoneDto addMilestone)
        {
            try
            {
                //TODO: milestones in use in a template cannot be deleted

                var findMilestone = _context.Milestones.Where(x => x.MilestoneId == addMilestone.MilestoneId).SingleOrDefault();
                findMilestone.IsActive = false;
                findMilestone.UpdatedBy = User.Identity.Name.Split('@')[0];
                findMilestone.UpdatedDateTime = DateTime.Now;
                _context.Milestones.Update(findMilestone);

                int maxSeqNumber = _context.Milestones.Where(x => x.IsActive == true).Max(x => x.Seq) ?? 0;
                if (maxSeqNumber > findMilestone.Seq)
                {
                    for (int seqIndex = findMilestone.Seq ?? 0; seqIndex < maxSeqNumber; seqIndex++)
                    {
                        var milestone = _context.Milestones.Where(x => x.Seq == seqIndex + 1 && x.IsActive == true).FirstOrDefault();
                        if (milestone != null)
                        {
                            milestone.Seq = seqIndex;
                            milestone.UpdatedBy = User.Identity.Name.Split('@')[0];
                            milestone.UpdatedDateTime = DateTime.Now;
                            _context.Milestones.Update(milestone);
                        }
                    }
                }

                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<MilestoneDto> { Value = addMilestone, IsSuccess = true, Message = "Deleted milestone successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MilestoneDto> { IsSuccess = false, Message = "Failed to delete milestone" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> SActivitiesAsync()
        {
            try
            {
                var sactivites = await _context.Sactivities.Include("Trade").Where(x => x.IsActive == true).OrderBy(x => x.Seq).ToListAsync();
                var sactivitiesDto = _mapper.Map<List<SactivityDto>>(sactivites);
                return new OkObjectResult(new ResponseModel<List<SactivityDto>> { Value = sactivitiesDto, IsSuccess = true, Message = "Fetched SActivities successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SactivityDto>> { IsSuccess = false, Message = "Failed to get Sactivities" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateActivityAsync([FromBody] SactivityDto updateActivity)
        {
            try
            {
                //TODO: adjust sequence number of remainin milestones?
                var findActivity = _context.Sactivities.Where(x => x.SactivityId == updateActivity.SactivityId).SingleOrDefault();
                findActivity.ActivityName = updateActivity.ActivityName;
                findActivity.Seq = updateActivity.Seq;
                findActivity.TradeId = updateActivity.TradeId;
                findActivity.DefaultDuration = updateActivity.DefaultDuration;
                findActivity.DefaultLagtime = updateActivity.DefaultLagtime;
                findActivity.DefaultNote = updateActivity.DefaultNote;
                findActivity.UpdateSchedules = updateActivity.UpdateSchedules;
                findActivity.UpdateTemplates = updateActivity.UpdateTemplates;
                findActivity.ReportingId = updateActivity.ReportingId;
                findActivity.Workdays = updateActivity.Workdays;
                findActivity.UpdatedBy = User.Identity.Name.Split('@')[0];

                findActivity.UpdatedDateTime = DateTime.Now;

                _context.Sactivities.Update(findActivity);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<SactivityDto> { Value = updateActivity, IsSuccess = true, Message = "Updated activity successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SactivityDto> { IsSuccess = false, Message = "Failed to update activity" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteActivityAsync([FromBody] SactivityDto deleteActivity)
        {
            try
            {
                //TODO: don't allow delete on activities that are in use in a schedule??
                var findActivity = _context.Sactivities.Where(x => x.SactivityId == deleteActivity.SactivityId).SingleOrDefault();
                findActivity.IsActive = false;
                findActivity.UpdatedBy = User.Identity.Name.Split('@')[0];
                findActivity.UpdatedDateTime = DateTime.Now;
                _context.Sactivities.Update(findActivity);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<SactivityDto> { Value = deleteActivity, IsSuccess = true, Message = "Deleted activity successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SactivityDto> { IsSuccess = false, Message = "Failed to delete activity" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddActivityAsync([FromBody] SactivityDto addActivity)
        {
            try
            {
                int maxSeqNumber = _context.Sactivities.Max(x => x.Seq) ?? 0;
                var activity = new Sactivity()
                {
                    ActivityName = addActivity.ActivityName,
                    TradeId = addActivity.TradeId,
                    Seq = addActivity.Seq ?? maxSeqNumber + 1,//TODO: if allow them to enter a sequence, need to adjust all the others
                    DefaultDuration = addActivity.DefaultDuration,
                    DefaultLagtime = addActivity.DefaultLagtime,
                    DefaultNote = addActivity.DefaultNote,
                    UpdateSchedules = addActivity.UpdateSchedules,
                    UpdateTemplates = addActivity.UpdateTemplates,
                    Workdays = addActivity.Workdays,
                    DivId = 1,
                    GrossLag = addActivity.GrossLag,
                    CreatedBy = User.Identity.Name.Split('@')[0],
                };
                _context.Sactivities.Add(activity);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<SactivityDto> { Value = addActivity, IsSuccess = true, Message = "Added activity successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SactivityDto> { IsSuccess = false, Message = "Failed to add activity" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateActivitiesSeqAsync([FromBody] List<SactivityDto> updateActivities)
        {
            try
            {
                //assuming new sequence is saved in the update milestones seq
                //TODO: is this slow? - yep. too slow. 
                var sactvitiesToUpdate = new List<Sactivity>();
                var findActivities = _context.Sactivities.Where(x => updateActivities.Select(y => y.SactivityId).Contains(x.SactivityId)).ToList();
                foreach (var activity in findActivities)
                {
                    activity.Seq = updateActivities.SingleOrDefault(x => x.SactivityId == activity.SactivityId).Seq;
                }
                _context.Sactivities.UpdateRange(findActivities);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<List<SactivityDto>> { Value = updateActivities, IsSuccess = true, Message = "Updated activities successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SactivityDto>> { IsSuccess = false, Message = "Failed to update activities" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> TradesAsync()
        {
            try
            {
                var trades = await _context.Trades.Where(x => x.IsActive == true).OrderBy(x => x.TradeName).ToListAsync();
                var tradeDtos = _mapper.Map<List<TradeDto>>(trades);
                return new OkObjectResult(new ResponseModel<List<TradeDto>> { Value = tradeDtos, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<TradeDto>> { IsSuccess = false, Message = "Failed to fetch trades" });
            }
        }

        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> PreScheduleAsync(string jobNumber)
        {
            try
            {
                var schedule = _context.Schedules.Include(x => x.Template).Include(x => x.JobNumberNavigation).Where(x => x.JobNumber == jobNumber && x.IsActive == true).OrderByDescending(x => x.CreatedDateTime).FirstOrDefault();
                var scheduleDto = _mapper.Map<ScheduleDto>(schedule);
                if (schedule != null)
                {
                    scheduleDto.BoolPublished = schedule.Published == "T";
                }
                return new OkObjectResult(new ResponseModel<ScheduleDto> { Value = scheduleDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheduleDto> { IsSuccess = false, Message = "Failed to get PreSchedule activities" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdatePrescheduleAsync([FromBody] ScheduleDto updatePreschedule)
        {
            try
            {
                //edit start date and approve, used to project datas and release schedule
                //if preschedule start date changes, it recalculates, preschedule base end, etc. 
                //if approved, create and lock in baseline dates, and lock initial schedule dates. 

                //if more than one schedule for a job - currently showing the most recent one
                //what if they approve the pre schedule, then need to completely change the schedule?? -- new schedule from template


                var updateBy = User.Identity.Name.Split('@')[0];
                bool wasApproved = false;
                var findSchedule = _context.Schedules.Include(x => x.JobNumberNavigation.Subdivision).Include(x => x.Template).SingleOrDefault(x => x.ScheduleId == updatePreschedule.ScheduleId);
                if (findSchedule == null)
                {
                    var result = await SchedulesFromTemplateAsync(new List<ScheduleDto>() { updatePreschedule });
                    findSchedule = await _context.Schedules.FirstOrDefaultAsync(x => x.JobNumber == updatePreschedule.JobNumber);
                    //todo: what if that failed
                }
                if (updatePreschedule.JobNumberNavigation?.StickBuilingNum != null && updatePreschedule.JobNumberNavigation?.StickBuilingNum != findSchedule.JobNumberNavigation?.StickBuilingNum)
                {
                    //update stick, but it might be building or phase
                    var jobToUpdate = findSchedule.JobNumberNavigation;
                    jobToUpdate.StickBuilingNum = updatePreschedule.JobNumberNavigation?.StickBuilingNum;
                    //_context.Jobs.Update(jobToUpdate);
                    //await _context.SaveChangesAsync();

                    await _context.Jobs.BulkUpdateAsync(new List<Job>() { jobToUpdate });
                }
                //TODO: don't update all the dates if just updating stick number or building permit number


                //if schedule not found, add one with the selected template
                if (findSchedule == null && updatePreschedule.TemplateId != null && updatePreschedule.TemplateId != 0)
                {
                    //TODO: add new schedule with the selected template

                    var result = await SchedulesFromTemplateAsync(new List<ScheduleDto>() { updatePreschedule });
                    findSchedule = await _context.Schedules.Include(x => x.JobNumberNavigation).FirstOrDefaultAsync(x => x.JobNumber == updatePreschedule.JobNumber);
                    //todo: what if that failed

                }


                //TODO: prevent repprove the same schedule?
                //if (findSchedule.IniSchApproved == true)
                //{
                //    //maybe return a message saying already apprvoed
                //    return Ok(new ResponseModel<ScheduleDto>() { IsSuccess = false, Message = "Schedule already released", Value = updatePreschedule });
                //}
                if (updatePreschedule.IniSchStartDate == null)
                {
                    //return Ok(new ResponseModel<ScheduleDto>() { IsSuccess = false, Message = "Missing Start Date", Value = updatePreschedule }); 
                }
                if (updatePreschedule.IniSchApproved != true)
                {

                    //2/20 allowing save dates before "release"
                    //return Ok(new ResponseModel<ScheduleDto>() { IsSuccess = false, Message = "Release Not Approved. Approve Release to Save Schedule.", Value = updatePreschedule });
                }

                //disable for test
                //if (updatePreschedule.ActualStartDate != null && updatePreschedule.IniSchStartDate != findSchedule?.IniSchStartDate)
                //{
                //    //TODO: still allow update some fields... 
                //    return Ok(new ResponseModel<ScheduleDto>() { IsSuccess = false, Message = "Schedule is already started. Cannot update release dates", Value = updatePreschedule });
                //}
                wasApproved = findSchedule.IniSchApproved == true;
                var iniStartDate = findSchedule.IniSchStartDate;
                findSchedule.TemplateId = updatePreschedule.TemplateId != 0 ? updatePreschedule.TemplateId : null;//it could change. be careful about overwriting completed schedules. maybe only allow change if not released or not started?
                findSchedule.PermitNumber = updatePreschedule.PermitNumber;
                findSchedule.PermitSubmitDate = updatePreschedule.PermitSubmitDate;
                findSchedule.PermitReceivedDate = updatePreschedule.PermitReceivedDate;
                //findSchedule.StickBuildingNum = updatePreschedule.JobNumberNavigation?.StickBuilingNum;
                findSchedule.IniSchApproved = updatePreschedule.IniSchApproved;
                findSchedule.IniSchApproveDate = wasApproved ? findSchedule.IniSchApproveDate : updatePreschedule.IniSchApproved == true ? DateTime.Now : null; //if already approved, keep original approved date, else fill with current date
                findSchedule.IniSchApprovedby = wasApproved ? findSchedule.IniSchApprovedby : updatePreschedule.IniSchApproved == true ? updateBy : null;
                findSchedule.IniSchStartDate = updatePreschedule.IniSchStartDate;
                findSchedule.IniSchEndDate = updatePreschedule.IniSchEndDate;
                findSchedule.IniSchBaseStartDate = updatePreschedule.IniSchStartDate;
                findSchedule.IniSchBaseEndDate = updatePreschedule.IniSchEndDate;
                findSchedule.IniSchModifiedby = User.Identity.Name.Split('@')[0];
                findSchedule.IniSchModifieddate = DateTime.Now;
                findSchedule.IniSchDuration = updatePreschedule.IniSchDuration;
                findSchedule.Published = updatePreschedule.BoolPublished ? "T" : "F";
                findSchedule.LockPreSch = findSchedule.IniSchApproved;
                findSchedule.UpdatedBy = updateBy;
                findSchedule.UpdatedDateTime = DateTime.Now;
                //findSchedule.BaseStartDate =  findSchedule.IniSchStartDate;
                //findSchedule.ProjStartDate =  findSchedule.IniSchStartDate;
                //_context.Schedules.Update(findSchedule);
                //await _context.SaveChangesAsync();
                await _context.Schedules.BulkUpdateAsync(new List<Schedule>() { findSchedule });

                if(!wasApproved && updatePreschedule.IniSchApproved == true)
                {
                    //if job is newly released, release the whole stick
                    var findSameStick = !string.IsNullOrWhiteSpace(updatePreschedule.JobNumberNavigation?.StickBuilingNum) ? _context.Jobs.AsNoTracking().Where(x => x.Schedule != null).Include(x => x.Schedule!.Template).Include(x => x.Subdivision).Where(x => x.StickBuilingNum == updatePreschedule.JobNumberNavigation.StickBuilingNum && x.IsActive == true && x.SubdivisionId == updatePreschedule.JobNumberNavigation.SubdivisionId).ToList() : _context.Jobs.AsNoTracking().Include(x => x.Subdivision).Where(x => x.Schedule != null).Include(x => x.Schedule!.Template).Where(x => x.JobNumber == updatePreschedule.JobNumber).ToList();//if no stick num, just this job
                    var schedulesSameStick = findSameStick.Where(x => x.Schedule != null).Select(x => x.Schedule!).ToList();
                    foreach (var schedule in schedulesSameStick)
                    {
                        schedule.IniSchApproved = updatePreschedule.IniSchApproved;
                        schedule.IniSchApproveDate = DateTime.Now;
                        schedule.IniSchApprovedby = updateBy;
                        schedule.UpdatedBy = updateBy;
                        schedule.UpdatedDateTime = DateTime.Now;
                    }
                    await _context.Schedules.BulkUpdateAsync(schedulesSameStick, options => options.ColumnInputExpression = x => new { x.IniSchApproved, x.IniSchApproveDate, x.IniSchApprovedby, x.UpdatedBy, x.UpdatedDateTime });

                    var findSchedulesDto = _mapper.Map<List<ScheduleDto>>(schedulesSameStick);
                    foreach (var scheduleDto in findSchedulesDto)
                    {
                        scheduleDto.OldIniSchStartDate = iniStartDate;
                        scheduleDto.BoolPublished = scheduleDto.Published == "T";
                        scheduleDto.UsingDefaultTemplate = scheduleDto.TemplateId == null;
                        scheduleDto.TemplateId = scheduleDto.TemplateId;
                        scheduleDto.Template = scheduleDto.Template;
                        scheduleDto.JobNumberNavigation = new JobDto() { JobNumber = findSameStick.FirstOrDefault(x => x.JobNumber == scheduleDto.JobNumber)?.JobNumber, SubdivisionId = findSameStick.FirstOrDefault(x => x.JobNumber == scheduleDto.JobNumber)?.SubdivisionId };
                        scheduleDto.OldIniSchStartDate = scheduleDto.IniSchStartDate;
                        scheduleDto.IniSchStartDateInputRaw = scheduleDto.IniSchStartDate?.ToString("MM/dd/yyyy") ?? "";
                    }
                    foreach (var scheduleDto in findSchedulesDto)
                    {
                        scheduleDto.OldIniSchStartDate = iniStartDate;
                    }
                   
                   // return Ok(new ResponseModel<List<ScheduleDto>>() { IsSuccess = true, Message = "Updated Initial Schedule", Value = findSchedulesDto });
                }
               

                //if schedule is approved, populate the baseline and projected dates in the schedule
                //TODO: require approved to save date, or just date exists?
                //below shouldn't happen because if the date is changing it calls the update schedule stick method
                if (updatePreschedule.IniSchStartDate != null && updatePreschedule.IniSchStartDate != iniStartDate)//update dates if changed?
                {
                    // Sactivity_link
                    var findSactivities = await _context.ScheduleSactivities.Include(x => x.ScheduleSactivityLinks).Include(x => x.Sactivity).Where(x => x.ScheduleId == findSchedule.ScheduleId && x.IsActive == true).ToListAsync();
                    var selectLinks = (from a in findSactivities
                                       join b in _context.Estactivities.Where(x => x.JobNumber == findSchedule.JobNumber && x.IsActive == true) on a.SactivityId equals b.SactivityId
                                       select new
                                       {
                                           SubNumber = b.SubNumber,
                                           ScheduleSactivityId = a.ScheduleAid,
                                           ScheduleSactivityLink = new ScheduleSactivityLink()
                                           {
                                               ScheduleAid = a.ScheduleAid,
                                               EstactivityId = b.EstactivityId,
                                               CreatedBy = updateBy,
                                           }
                                       }).ToList();
                    //TODO: would need to maek sure there weren't already schedule activity links -- ie update vs add new
                    //remove previous Schedule_Sactivity --
                    //TODO: don't delete, mark inactive - 
                    var conn = _configuration.GetConnectionString("ERPConnection");
                    using (var connection = new SqlConnection(conn))
                    {
                        connection.Open();
                        var deleteQuery = $"DELETE FROM SCHEDULE_SACTIVITY_LINK where SCHEDULE_AID in ('{string.Join("','", selectLinks.Select(x => x.ScheduleSactivityId).ToList())}')";
                        var deleteCommand = new SqlCommand(deleteQuery, connection);
                        await deleteCommand.ExecuteNonQueryAsync();
                    }
                    //Insert the new ones
                    var newLinks = selectLinks.Select(x => x.ScheduleSactivityLink).DistinctBy(x => new { x.ScheduleAid, x.EstactivityId, x.CreatedBy }).ToList();
                    await _context.ScheduleSactivityLinks.BulkInsertAsync(newLinks);
                    // InsertScheduleActivityLinks(updateBy, selectLinks.Select(x => x.ScheduleSactivityLink).ToList());

                    //TODO: what if start date is a holiday - need to warn, and then mark start date as ignore no work days
                    //populate all the other dates in the schedule
                    var firstActivity = _context.ScheduleSactivities.Where(x => x.ScheduleId == findSchedule.ScheduleId && x.Seq == 1 && x.IsActive == true).FirstOrDefault();
                    if (firstActivity != null)
                    {
                        var holidays = _context.CalendarsDays.Where(x => x.CalendarId == 1 && x.IsActive == true).Select(x => x.WorkDate).ToList();
                        firstActivity.SchStartDate = findSchedule.IniSchStartDate;
                        firstActivity.SchEndDate = firstActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)firstActivity.SchStartDate, (int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0, holidays) : firstActivity.SchStartDate.Value.AddDays((int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0);
                        firstActivity.BaseStartDate = firstActivity.SchStartDate;
                        firstActivity.IniStartDate = findSchedule.IniSchStartDate;
                        firstActivity.IniEndDate = firstActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)firstActivity.SchStartDate, (int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0, holidays) : firstActivity.SchStartDate.Value.AddDays((int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0);
                        firstActivity.BaseEndDate = firstActivity.SchEndDate;
                        var listActivities = new List<ScheduleSactivity>();
                        // var findSactivities = _context.ScheduleSactivities.Where(x => x.ScheduleId == findSchedule.ScheduleId).ToList();//TODO: go through mid
                        var findSuccessorActivities = _context.ScheduleSactivityPreds.Include("ScheduleA").Where(x => findSactivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();

                        //if (findSchedule.IniSchApproved == true)//2/20/25 save update dates even before "release"
                        var returnList = RecalculateDates(findSuccessorActivities, firstActivity, holidays, false);

                        //if something was not in the predecessor chain, need to set that date to current start date, and adjust again 
                        var activitiesNotAdjusted = findSactivities.Where(x => x != firstActivity && !returnList.Contains(x)).ToList();
                        listActivities = new List<ScheduleSactivity>();
                        //var returnList2 = new List<ScheduleSactivity>();
                        //while (activitiesNotAdjusted.Any())
                        //{
                        //    var firstActivity1 = activitiesNotAdjusted.OrderBy(x => x.Seq).FirstOrDefault();
                        //    if (firstActivity1 != null)
                        //    {
                        //        firstActivity1.SchStartDate = findSchedule.IniSchApproved == true ? findSchedule.IniSchStartDate : null;
                        //        firstActivity1.SchEndDate = findSchedule.IniSchApproved == true ? firstActivity1.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)firstActivity1.SchStartDate, (int)firstActivity1.Duration > 0 ? (int)firstActivity1.Duration - 1 : 0, holidays) : firstActivity1.SchStartDate.Value.AddDays((int)firstActivity1.Duration > 0 ? (int)firstActivity1.Duration - 1 : 0) : null;
                        //        var additems = AdjustDatesBasedOnPreSchedule(firstActivity1, listActivities, findSuccessorActivities, holidays);
                        //        returnList2.AddRange(additems);
                        //        returnList2.Add(firstActivity1);
                        //        activitiesNotAdjusted = activitiesNotAdjusted.Where(x => x != firstActivity1 && !returnList2.Contains(x)).ToList();
                        //    }
                        //}//add the not adjusted return list to the other return list to update all the dates
                        //returnList.AddRange(returnList2);
                        //TODO: check the logic, especially on items with no predecessor or multiple preds
                        //then save the updated projected dates - if it's approved, set the base dates and sch dates, else just set the ini dates
                        var updateActivities = _context.ScheduleSactivities.Include(x => x.SubNumberNavigation).Where(x => returnList.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                        var supplierCommunicationsDto = new List<SupplierCommunicationDto>();
                        var updatedActivitesForEmail = new List<UpdateActivity>();
                        var getTradeSuppliers = _context.TradeSuppliers.Where(x => (x.SubdivisionId == findSchedule.JobNumberNavigation.SubdivisionId || x.SubdivisionId == 1) && x.IsActive == true && x.DefaultSupplier == "T").ToList();
                        foreach (var activity in updateActivities)
                        {
                            activity.SchStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
                            activity.SchEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
                            activity.BaseStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
                            activity.BaseEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);

                            //2/20/25 allow updates before release
                            //activity.SchStartDate = findSchedule.IniSchApproved == true ? returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate) : null;
                            //activity.SchEndDate = findSchedule.IniSchApproved == true ? returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate) : null;
                            //activity.BaseStartDate = findSchedule.IniSchApproved == true ? returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate) : null;
                            //activity.BaseEndDate = findSchedule.IniSchApproved == true ? returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate) : null;

                            activity.IniStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
                            activity.IniEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
                            activity.Calduration = (activity.SchEndDate.Value - activity.SchStartDate.Value).Days;//TODO: check null-- calduration is straigt diff in days 
                            //activity.SubNumber = selectLinks.FirstOrDefault(x => x.ScheduleSactivityId == activity.ScheduleAid)?.SubNumber ?? activity.SubNumber;//TODO: check
                            activity.SubNumber = getTradeSuppliers.FirstOrDefault(y => y.SubdivisionId == findSchedule.JobNumberNavigation.SubdivisionId && y.TradeId == activity.Sactivity?.TradeId)?.SubNumber ?? getTradeSuppliers.FirstOrDefault(y => y.SubdivisionId == 1 && y.TradeId == activity.Sactivity?.TradeId)?.SubNumber ?? null;
                            activity.UpdatedBy = updateBy;
                            activity.UpdateDate = DateTime.Now;
                            activity.UpdatedDateTime = DateTime.Now;

                            if (activity.SubNumber != null && activity.Schedule != null && activity.Sactivity != null && updatePreschedule.BoolPublished == true)
                            {
                                updatedActivitesForEmail.Add(new UpdateActivity
                                {
                                    SubNumber = activity.SubNumber,
                                    SubName = activity.SubNumberNavigation?.SubName,
                                    JobNumber = activity.Schedule.JobNumber,
                                    ActivityName = activity.Sactivity.ActivityName,
                                    StartDate = activity.SchStartDate?.ToString("MM/dd/yyyy"),
                                    EndDate = activity.SchEndDate?.ToString("MM/dd/yyyy")
                                });
                            }
                        }
                        //save the update
                        await _context.ScheduleSactivities.BulkUpdateAsync(updateActivities, options => options.ColumnInputExpression = x => new { x.SchStartDate, x.SchEndDate, x.BaseEndDate, x.BaseStartDate, x.PlusminusDays, x.IniEndDate, x.IniStartDate, x.Calduration, x.SubNumber, x.UpdatedBy, x.UpdatedDateTime });

                        //update the milestones dates and durations
                        var findAllMilestones = _context.ScheduleMilestones.Include(x => x.ScheduleSactivities).Where(x => x.ScheduleId == findSchedule.ScheduleId && x.IsActive == true).ToList();
                        foreach (var milestone in findAllMilestones)
                        {
                            milestone.SchStartDate = milestone.ScheduleSactivities.Min(x => x.SchStartDate);
                            milestone.SchEndDate = milestone.ScheduleSactivities.Max(x => x.SchEndDate);
                            milestone.IniStartDate = milestone.ScheduleSactivities.Min(x => x.SchStartDate);
                            milestone.IniEndDate = milestone.ScheduleSactivities.Max(x => x.SchEndDate);
                            milestone.BaseStartDate = milestone.ScheduleSactivities.Min(x => x.SchStartDate);
                            milestone.BaseEndDate = milestone.ScheduleSactivities.Max(x => x.SchEndDate);

                            if (milestone.SchStartDate != null && milestone.SchEndDate != null)
                            {
                                milestone.Duration = CalendarExtension.WorkingDaysDuration(milestone.SchStartDate.Value, milestone.SchEndDate.Value, holidays);
                                milestone.Calduration = (milestone.SchEndDate.Value - milestone.SchStartDate.Value).Days;
                            }

                            milestone.UpdatedBy = updateBy;
                            milestone.UpdatedDateTime = DateTime.Now;
                        }
                        await _context.ScheduleMilestones.BulkUpdateAsync(findAllMilestones);

                        //update the base and proj dates and duration for the schedule as a whole                        
                        findSchedule.DateToStart = findAllMilestones.Min(x => x.BaseStartDate);
                        findSchedule.DateToEnd = findAllMilestones.Max(x => x.BaseEndDate);
                        findSchedule.BaseStartDate = findAllMilestones.Min(x => x.BaseStartDate);
                        findSchedule.BaseEndDate = findAllMilestones.Max(x => x.BaseEndDate);
                        findSchedule.ProjStartDate = findAllMilestones.Min(x => x.BaseStartDate);
                        findSchedule.ProjEndDate = findAllMilestones.Max(x => x.BaseEndDate);
                        findSchedule.IniSchEndDate = findAllMilestones.Max(x => x.BaseEndDate);
                        findSchedule.IniSchDuration = findSchedule.IniSchStartDate != null && findSchedule.IniSchEndDate != null ? CalendarExtension.WorkingDaysDuration(findSchedule.IniSchStartDate.Value, findSchedule.IniSchEndDate.Value, holidays) : null;
                        findSchedule.BaseDuration = findSchedule.BaseStartDate != null && findSchedule.BaseEndDate != null ? CalendarExtension.WorkingDaysDuration(findSchedule.BaseStartDate.Value, findSchedule.BaseEndDate.Value, holidays) : null;
                        findSchedule.ProjDuration = findSchedule.BaseDuration;
                        findSchedule.BaseCalduration = findSchedule.BaseStartDate != null && findSchedule.BaseEndDate != null ? (findSchedule.BaseEndDate.Value - findSchedule.BaseStartDate.Value).Days : null;
                        findSchedule.ProjCalduration = findSchedule.BaseCalduration;
                        await _context.Schedules.BulkUpdateAsync(new List<Schedule>() { findSchedule });

                        //Grouping the messages by supplier, so each supplier only gets one message with a list of activities that are changed
                        if (findSchedule.Published == "T" && findSchedule.IniSchApproved == true)
                        {
                            var supplierCommunications = new List<SupplierCommunication>();
                            var emailSuppliers = new List<SendGridEmailModel>();
                            foreach (var activitesBySupplier in updatedActivitesForEmail.GroupBy(x => x.SubNumber))
                            {
                                var getJob = _context.Jobs.AsNoTracking().Include(x => x.Subdivision).FirstOrDefault(x => x.JobNumber == activitesBySupplier.First().JobNumber);
                                var getSuppliersEmails = _context.SupplierContacts.AsNoTracking().Include(x => x.Contact).Where(x => x.SubNumber == activitesBySupplier.Key && (x.SupplierContactSched == "T") && x.IsActive == true).ToList();//scheduling contacts
                                foreach (var contact in getSuppliersEmails)
                                {
                                    emailSuppliers.Add(new SendGridEmailModel
                                    {
                                        //Email = "<EMAIL>",
                                        SubName = activitesBySupplier.FirstOrDefault()?.SubName,
                                        JobNumber = activitesBySupplier.First().JobNumber,
                                        JobAddress = getJob?.JobAddress1,
                                        LotNumber = getJob?.LotNumber,
                                        SubdivisionName = getJob?.Subdivision?.SubdivisionName,
                                        Email = contact.Contact.Email,
                                        MessageSubject = $"Schedule Change for Van Metre Job {activitesBySupplier.First().JobNumber}",
                                        Activities = new Activities() { UpdateActivities = activitesBySupplier.Select(x => x).ToList() },
                                        MessageBody = "here is a test",//won't be used
                                    });
                                }
                                var stringMessage = new StringBuilder();
                                stringMessage.Append("<p><strong>The following activities are updated</strong><p>");
                                foreach (var activity in activitesBySupplier)
                                {
                                    stringMessage.Append($"<span style=\"font-size:14px;color:#3B3B39;line-height:1.2em;;font-family:Arial, Helvetica, sans-serif\"><strong> Activity:</strong> {activity.ActivityName} |<strong> Start:</strong> {activity.StartDate} |<strong> End:</strong> {activity.EndDate}</span><br>");
                                }
                                if (getSuppliersEmails.Any())
                                {
                                    supplierCommunications.Add(new SupplierCommunication
                                    {
                                        //SendTo = "<EMAIL>",
                                        SendTo = getSuppliersEmails.First()?.Contact.Email,
                                        SendFrom = "<EMAIL>",
                                        SubNumber = (int)activitesBySupplier.First().SubNumber,
                                        JobNumber = activitesBySupplier.First().JobNumber,
                                        MessageSubject = $"Schedule Change for Van Metre Job {activitesBySupplier.First().JobNumber}",
                                        Message = stringMessage.ToString(), //TODO: html here?
                                        IsActive = true,
                                        CreatedBy = User.Identity.Name.Split('@')[0],
                                        CreatedDateTime = DateTime.Now
                                    });
                                }

                            }
                            if (emailSuppliers.Count > 0 && !findSchedule.JobNumber.Contains("TEST"))
                            {
                                if (_env.IsDevelopment())
                                {
                                    foreach (var supplier in emailSuppliers)
                                    {
                                        supplier.Email = "<EMAIL>";//send only to me in test
                                        supplier.MessageSubject = $"TEST {supplier.MessageSubject}";
                                    }
                                }
                                await _email.SendMultipleEmail(emailSuppliers);
                            }
                            if (supplierCommunications.Count >  0 && !findSchedule.JobNumber.Contains("TEST"))
                            {
                                await _context.SupplierCommunications.BulkInsertAsync(supplierCommunications);
                            }
                        }

                    }
                }
                
                var findScheduleDto = _mapper.Map<ScheduleDto>(findSchedule);
                findScheduleDto.OldIniSchStartDate = iniStartDate;
                findScheduleDto.OldIniSchStartDate = iniStartDate;
                findScheduleDto.BoolPublished = findScheduleDto.Published == "T";
                findScheduleDto.UsingDefaultTemplate = findScheduleDto.TemplateId == null;
                findScheduleDto.TemplateId = findScheduleDto.TemplateId;
                findScheduleDto.Template = findScheduleDto.Template;
                findScheduleDto.JobNumberNavigation = new JobDto() { JobNumber = findSchedule.JobNumber, SubdivisionId = findSchedule.JobNumberNavigation.SubdivisionId };
                findScheduleDto.OldIniSchStartDate = findScheduleDto.IniSchStartDate;
                findScheduleDto.IniSchStartDateInputRaw = findScheduleDto.IniSchStartDate?.ToString("MM/dd/yyyy") ?? "";
                return Ok(new ResponseModel<List<ScheduleDto>>() { IsSuccess = true, Message = "Updated Initial Schedule", Value = new List<ScheduleDto> { findScheduleDto } });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return Ok(new ResponseModel<List<ScheduleDto>>() { IsSuccess = false, Message = "Failed to update Initial Schedule" });
            }
        }


        [HttpPut]
        public async Task<IActionResult> UpdatePrescheduleStickAsync([FromBody] ScheduleDto updatePreschedule)
        {
            try
            {
                //if preschedule start date changes, it recalculates, preschedule base end, etc. 
                //if approved, create and lock in baseline dates, and lock initial schedule dates. 

                var sw1 = new Stopwatch();
                var sw2 = new Stopwatch();
                var sw3 = new Stopwatch();
                var sw4 = new Stopwatch();
                var sw5 = new Stopwatch();//getting data from db, pulled out of foreach loop
                var sw6 = new Stopwatch();//save data after foreach
                var sw7 = new Stopwatch();//in foreach
                var sw8 = new Stopwatch();//total
                sw1.Start();
                sw8.Start();
                var updateBy = User.Identity.Name.Split('@')[0];
                bool wasApproved = false;

                var findSchedule = _context.Schedules.Include(x => x.JobNumberNavigation).SingleOrDefault(x => x.ScheduleId == updatePreschedule.ScheduleId);              
                
                if ( findSchedule != null && updatePreschedule.JobNumberNavigation?.StickBuilingNum != null && updatePreschedule.JobNumberNavigation?.StickBuilingNum != findSchedule.JobNumberNavigation?.StickBuilingNum)
                {
                    //update stick
                    var jobToUpdate = findSchedule.JobNumberNavigation;
                    jobToUpdate.StickBuilingNum = updatePreschedule.JobNumberNavigation?.StickBuilingNum;
                    _context.Jobs.Update(jobToUpdate);
                    await _context.SaveChangesAsync();
                }

                var findSameStick = updatePreschedule.JobNumberNavigation.StickBuilingNum != null ? _context.Jobs.AsNoTracking().Include(x => x.Schedule).Include(x => x.Subdivision).Where(x => x.StickBuilingNum == updatePreschedule.JobNumberNavigation.StickBuilingNum && x.IsActive == true && x.SubdivisionId == updatePreschedule.JobNumberNavigation.SubdivisionId).ToList() : _context.Jobs.AsNoTracking().Include(x => x.Subdivision).Include(x => x.Schedule).Where(x => x.JobNumber == updatePreschedule.JobNumber).ToList();
                //TODO: don't update all the dates if just updating stick number or building permit number
                //if schedule not found for jobs in the stick, add one with the selected template
                var scheduleMissing = findSameStick.Any(x => x.Schedule == null);
                if (scheduleMissing && updatePreschedule.TemplateId != null && updatePreschedule.TemplateId != 0)
                {
                    //add new schedule with the selected template
                    var schedulesToBeCreated = findSameStick.Where(x => x.Schedule == null).Select(x=> new ScheduleDto { JobNumber = x.JobNumber, TemplateId = updatePreschedule.TemplateId }).ToList();
                    var result = await SchedulesFromTemplateAsync(schedulesToBeCreated);
                    findSchedule = await _context.Schedules.Include(x => x.JobNumberNavigation).FirstOrDefaultAsync(x => x.JobNumber == updatePreschedule.JobNumber);
                    findSameStick = updatePreschedule.JobNumberNavigation.StickBuilingNum != null ? _context.Jobs.AsNoTracking().Include(x => x.Schedule).Include(x => x.Subdivision).Where(x => x.StickBuilingNum == updatePreschedule.JobNumberNavigation.StickBuilingNum && x.IsActive == true && x.SubdivisionId == updatePreschedule.JobNumberNavigation.SubdivisionId).ToList() : _context.Jobs.AsNoTracking().Include(x => x.Subdivision).Include(x => x.Schedule).Where(x => x.JobNumber == updatePreschedule.JobNumber).ToList();//get the stick again now that schedules are added, else schedules are null below
                }
                //disable for test
                //if (updatePreschedule.ActualStartDate != null && updatePreschedule.IniSchStartDate != findSchedule?.IniSchStartDate)
                //{
                //    //TODO: still allow update some fields... 
                //    return Ok(new ResponseModel<ScheduleDto>() { IsSuccess = false, Message = "Schedule is already started. Cannot update release dates", Value = updatePreschedule });
                //}
                wasApproved = findSchedule.IniSchApproved == true;
                var iniStartDate = findSchedule.IniSchStartDate;
                findSchedule.TemplateId = updatePreschedule.TemplateId;//it could change. be careful about overwriting completed schedules. maybe only allow change if not released or not started?
                findSchedule.PermitNumber = updatePreschedule.PermitNumber;
                findSchedule.PermitSubmitDate = updatePreschedule.PermitSubmitDate;
                findSchedule.PermitReceivedDate = updatePreschedule.PermitReceivedDate;
                //findSchedule.StickBuildingNum = updatePreschedule.JobNumberNavigation?.StickBuilingNum;
                findSchedule.IniSchApproved = updatePreschedule.IniSchApproved;
                findSchedule.IniSchApproveDate = updatePreschedule.IniSchApproved == true ? DateTime.Now : null;
                findSchedule.IniSchApprovedby = updatePreschedule.IniSchApproved == true ? updateBy : null;
                findSchedule.IniSchStartDate = updatePreschedule.IniSchStartDate;
                findSchedule.IniSchEndDate = updatePreschedule.IniSchEndDate;
                findSchedule.IniSchBaseStartDate = updatePreschedule.IniSchStartDate;
                findSchedule.IniSchBaseEndDate = updatePreschedule.IniSchEndDate;
                findSchedule.IniSchModifiedby = User.Identity.Name.Split('@')[0];
                findSchedule.IniSchModifieddate = DateTime.Now;
                findSchedule.IniSchDuration = updatePreschedule.IniSchDuration;
                findSchedule.Published = updatePreschedule.BoolPublished ? "T" : "F";
                findSchedule.LockPreSch = findSchedule.IniSchApproved;
                _context.Schedules.Update(findSchedule);
                await _context.SaveChangesAsync();

                sw1.Stop();

                sw2.Start();

                var holidays = _context.CalendarsDays.AsNoTracking().Where(x => x.CalendarId == 1 && x.IsActive == true).Select(x => x.WorkDate).ToList();

                var stickWithNonNullSchedules = findSameStick.Where(x => x.Schedule != null).ToList();
                var scheduleSActivities = await _context.ScheduleSactivities.Include(x => x.Schedule).Include(x => x.ScheduleSactivityLinks).Include(x => x.Sactivity).Where(x => stickWithNonNullSchedules.Select(y => y.Schedule.ScheduleId).Contains((int)x.ScheduleId) && x.IsActive == true).ToListAsync();
                var groupedActivitiesByJob = scheduleSActivities.GroupBy(x => x.Schedule.JobNumber).ToDictionary(g => g.Key, g => g.ToList());

                var activitiesToBeUpdatedForJobSchedule = new Dictionary<string, List<ScheduleSactivity>>();
                var successorActivitiesForJobSchedule = new Dictionary<string, List<ScheduleSactivityPred>>();

                foreach (var job in findSameStick)
                {
                    var scheduleSactivitiesForJob = groupedActivitiesByJob.ContainsKey(job.Schedule.JobNumber) ? groupedActivitiesByJob[job.Schedule.JobNumber] : new List<ScheduleSactivity>();
                    var findSuccessorActivities = _context.ScheduleSactivityPreds.Include("ScheduleA").Where(x => scheduleSactivitiesForJob.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                    successorActivitiesForJobSchedule.Add(job.Schedule.JobNumber, findSuccessorActivities);
                }

                Parallel.ForEach(findSameStick, job =>
                {
                    var scheduleSactivitiesForJob = groupedActivitiesByJob.ContainsKey(job.Schedule.JobNumber) ? groupedActivitiesByJob[job.Schedule.JobNumber] : new List<ScheduleSactivity>();
                    var firstActivity = scheduleSactivitiesForJob.Where(x => x.Seq == 1).FirstOrDefault();
                    if (firstActivity != null)
                    {
                        firstActivity.SchStartDate = updatePreschedule.IniSchStartDate;
                        firstActivity.SchEndDate = firstActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)firstActivity.SchStartDate, (int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0, holidays) : firstActivity.SchStartDate.Value.AddDays((int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0);
                        firstActivity.BaseStartDate = firstActivity.SchStartDate;
                        firstActivity.IniStartDate = updatePreschedule.IniSchStartDate;
                        firstActivity.IniEndDate = firstActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)firstActivity.SchStartDate, (int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0, holidays) : firstActivity.SchStartDate.Value.AddDays((int)firstActivity.Duration > 0 ? (int)firstActivity.Duration - 1 : 0);
                        firstActivity.BaseEndDate = firstActivity.SchEndDate;

                        var successorActivities = successorActivitiesForJobSchedule.ContainsKey(job.Schedule.JobNumber) ? successorActivitiesForJobSchedule[job.Schedule.JobNumber] : new List<ScheduleSactivityPred>();
                        var activitiesToBeUpdated = RecalculateDates(successorActivities, firstActivity, holidays, false);
                        activitiesToBeUpdatedForJobSchedule.Add(job.Schedule.JobNumber, activitiesToBeUpdated);
                    }
                });
                sw2.Stop();

                sw3.Start();
                sw5.Start();
                List<int> ScheduleLinksToDelete = new List<int>();
                List<ScheduleSactivityLink> scheduleSactivityLinksToInsert = new List<ScheduleSactivityLink>();
                var stickJobNumbers = findSameStick.Select(x => x.JobNumber).ToList();
                var stickSubdivisions = findSameStick.Select(x => x.SubdivisionId).ToList();
                var GetTradeSuppliers = _context.TradeSuppliers.AsNoTracking().Where(x => (stickSubdivisions.Contains(x.SubdivisionId) || x.SubdivisionId == 1) && x.IsActive == true && x.DefaultSupplier == "T").ToList();
                var AllActivitiesStick = _context.ScheduleSactivities.Include(x => x.Schedule).Include(x => x.SubNumberNavigation).Where(x => stickJobNumbers.Contains(x.Schedule.JobNumber)).ToList();
                var allSubNumbers = AllActivitiesStick.Select(x => x.SubNumber).Distinct().ToList();
                var getSuppliersContacts = _context.SupplierContacts.AsNoTracking().Include(x => x.Contact).Where(x => allSubNumbers.Contains(x.SubNumber) && (x.SupplierContactSched == "T") && x.IsActive == true).ToList();//scheduling contacts
                var getEstActivities = _context.Estactivities.AsNoTracking().Where(x => stickJobNumbers.Contains(x.JobNumber) && x.IsActive == true).ToList();
                List<ScheduleSactivity> activitiesToBeUpdated = new List<ScheduleSactivity>();
                List<SupplierCommunication> supplierCommunicationsToAdd = new List<SupplierCommunication>();
                List<SendGridEmailModel> sendGridEmailSuppliersToAdd = new List<SendGridEmailModel>();
                sw5.Stop();
                sw7.Start();
                foreach (var jobs in findSameStick)
                {
                    //var findJobSchedule = _context.Schedules.Where(x => x.JobNumber == jobs.JobNumber && x.IsActive == true).FirstOrDefault();
                    var findJobSchedule = jobs.Schedule;
                    if (findJobSchedule != null && findJobSchedule.IsActive == true)
                    {
                        //TODO: if schedule didn't exist, add one with the default template
                        //TODO: if schedule released, mark all in same stick as released
                        if ((updatePreschedule.IniSchStartDate != null && findJobSchedule.IniSchStartDate != updatePreschedule.IniSchStartDate) || findSchedule.IniSchStartDate == updatePreschedule.IniSchStartDate)//update dates if changed?
                        {
                            findJobSchedule.IniSchStartDate = updatePreschedule.IniSchStartDate;
                            // Sactivity_link
                            var findSactivities = groupedActivitiesByJob.ContainsKey(findJobSchedule.JobNumber) ? groupedActivitiesByJob[findJobSchedule.JobNumber] : new List<ScheduleSactivity>();

                            var selectLinks = (from a in findSactivities.ToList()
                                               join b in getEstActivities on new { a.SactivityId, a.Schedule.JobNumber } equals new { b.SactivityId, b.JobNumber } 
                                               select new
                                               {
                                                   SubNumber = b.SubNumber,
                                                   ScheduleSactivityId = a.ScheduleAid,
                                                   ScheduleSactivityLink = new ScheduleSactivityLink()
                                                   {
                                                       ScheduleAid = a.ScheduleAid,
                                                       EstactivityId = b.EstactivityId,
                                                       CreatedBy = updateBy,
                                                   }
                                               }).ToList();
                            ScheduleLinksToDelete.AddRange(selectLinks.Select(x => x.ScheduleSactivityId).ToList());
                            //TODO: would need to maek sure there weren't already schedule activity links -- ie update vs add new
                            //remove previous Schedule_Sactivity --
                            //TODO: don't delete, mark inactive - //out of loop
                            //var conn = _configuration.GetConnectionString("ERPConnection");
                            //using (var connection = new SqlConnection(conn))
                            //{
                            //    connection.Open();
                            //    var deleteQuery = $"DELETE FROM SCHEDULE_SACTIVITY_LINK where SCHEDULE_AID in ('{string.Join("','", selectLinks.Select(x => x.ScheduleSactivityId).ToList())}')";
                            //    var deleteCommand = new SqlCommand(deleteQuery, connection);
                            //    await deleteCommand.ExecuteNonQueryAsync();
                            //}
                            ////Insert the new ones

                            var newLinks = selectLinks.Select(x => x.ScheduleSactivityLink).DistinctBy(x => new { x.ScheduleAid, x.EstactivityId, x.CreatedBy }).ToList();
                            
                            scheduleSactivityLinksToInsert.AddRange(newLinks);

                            //await _context.ScheduleSactivityLinks.BulkInsertAsync(newLinks);//out of loop

                            var returnList = activitiesToBeUpdatedForJobSchedule.ContainsKey(findJobSchedule.JobNumber) ? activitiesToBeUpdatedForJobSchedule[findJobSchedule.JobNumber] : new List<ScheduleSactivity>();

                            if (!returnList.IsNullOrEmpty())
                            {
                                var updateActivities = AllActivitiesStick.Where(x => returnList.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                                var supplierCommunicationsDto = new List<SupplierCommunicationDto>();
                                var updatedActivitesForEmail = new List<UpdateActivity>();
                                var getTradeSuppliers = GetTradeSuppliers.Where(x => (x.SubdivisionId == findJobSchedule.JobNumberNavigation.SubdivisionId || x.SubdivisionId == 1) && x.IsActive == true && x.DefaultSupplier == "T").ToList();


                                foreach(var activity in updateActivities)
                                {
                                    activity.SchStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
                                    activity.SchEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
                                    activity.BaseStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
                                    activity.BaseEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
                                    activity.IniStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
                                    activity.IniEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
                                    activity.Calduration = (activity.SchEndDate.Value - activity.SchStartDate.Value).Days;
                                    activity.SubNumber = getTradeSuppliers.FirstOrDefault(y => y.SubdivisionId == findJobSchedule.JobNumberNavigation.SubdivisionId && y.TradeId == activity.Sactivity?.TradeId)?.SubNumber ?? getTradeSuppliers.FirstOrDefault(y => y.SubdivisionId == 1 && y.TradeId == activity.Sactivity?.TradeId)?.SubNumber ?? null;
                                    activity.UpdatedBy = updateBy;
                                    activity.UpdateDate = DateTime.Now;
                                    activity.UpdatedDateTime = DateTime.Now;

                                    if (activity.SubNumber != null && activity.Schedule != null && activity.Sactivity != null && updatePreschedule.BoolPublished == true)
                                    {
                                        updatedActivitesForEmail.Add(new UpdateActivity
                                        {
                                            SubNumber = activity.SubNumber,
                                            SubName = activity.SubNumberNavigation?.SubName,
                                            JobNumber = activity.Schedule.JobNumber,
                                            ActivityName = activity.Sactivity.ActivityName,
                                            StartDate = activity.SchStartDate?.ToString("MM/dd/yyyy"),
                                            EndDate = activity.SchEndDate?.ToString("MM/dd/yyyy")
                                        });
                                    }
                                }

                                activitiesToBeUpdated.AddRange(updateActivities);
                                //save the update - pulled out of loop
                                //await _context.ScheduleSactivities.BulkUpdateAsync(updateActivities, options => options.ColumnInputExpression = x => new { x.SchStartDate, x.SchEndDate, x.BaseEndDate, x.BaseStartDate, x.PlusminusDays, x.IniEndDate, x.IniStartDate, x.Calduration, x.SubNumber, x.UpdatedBy, x.UpdatedDateTime });

                                //Grouping the messages by supplier, so each supplier only gets one message with a list of activities that are changed
                                if (findJobSchedule.Published == "T" && findJobSchedule.IniSchApproved == true)
                                {
                                    var supplierCommunications = new List<SupplierCommunication>();
                                    var emailSuppliers = new List<SendGridEmailModel>();
                                    foreach (var activitesBySupplier in updatedActivitesForEmail.GroupBy(x => x.SubNumber))
                                    {
                                        var getJob = findSameStick.FirstOrDefault(x => x.JobNumber == activitesBySupplier.First().JobNumber);
                                        var getSuppliersEmails = getSuppliersContacts.Where(x => x.SubNumber == activitesBySupplier.Key && (x.SupplierContactSched == "T") && x.IsActive == true).ToList();//scheduling contacts
                                        
                                        //parallel not helpful here, this isn't doing anything much
                                        Parallel.ForEach(getSuppliersEmails, contact =>
                                        {
                                            emailSuppliers.Add(new SendGridEmailModel
                                            {
                                                //Email = "<EMAIL>",
                                                SubName = activitesBySupplier.FirstOrDefault()?.SubName,
                                                JobNumber = activitesBySupplier.First().JobNumber,
                                                JobAddress = getJob?.JobAddress1,
                                                LotNumber = getJob?.LotNumber,
                                                SubdivisionName = getJob?.Subdivision?.SubdivisionName,
                                                Email = contact.Contact.Email,
                                                MessageSubject = $"Schedule Change for Van Metre Job {activitesBySupplier.First().JobNumber}",
                                                Activities = new Activities() { UpdateActivities = activitesBySupplier.Select(x => x).ToList() },
                                                MessageBody = "here is a test",//won't be used
                                            });
                                        });

                                        var stringMessage = new StringBuilder();
                                        stringMessage.Append("<p><strong>The following activities are updated</strong><p>");
                                        foreach (var activity in activitesBySupplier)
                                        {
                                            stringMessage.Append($"<span style=\"font-size:14px;color:#3B3B39;line-height:1.2em;;font-family:Arial, Helvetica, sans-serif\"><strong> Activity:</strong> {activity.ActivityName} |<strong> Start:</strong> {activity.StartDate} |<strong> End:</strong> {activity.EndDate}</span><br>");
                                        }
                                        if (getSuppliersEmails.Any())
                                        {
                                            supplierCommunications.Add(new SupplierCommunication
                                            {
                                                //SendTo = "<EMAIL>",
                                                SendTo = getSuppliersEmails.First()?.Contact.Email,
                                                SendFrom = "<EMAIL>",
                                                SubNumber = (int)activitesBySupplier.First().SubNumber,
                                                JobNumber = activitesBySupplier.First().JobNumber,
                                                MessageSubject = $"Schedule Change for Van Metre Job {activitesBySupplier.First().JobNumber}",
                                                Message = stringMessage.ToString(), //TODO: html here?
                                                IsActive = true,
                                                CreatedBy = User.Identity.Name.Split('@')[0],
                                                CreatedDateTime = DateTime.Now
                                            });
                                        }
                                    }
                                    if (emailSuppliers.Count > 0 && !findSchedule.JobNumber.Contains("TEST"))
                                    {
                                        if (_env.IsDevelopment())
                                        {
                                            foreach (var supplier in emailSuppliers)
                                            {
                                                supplier.Email = "<EMAIL>";//send only to me in test
                                                supplier.MessageSubject = $"TEST {supplier.MessageSubject}";
                                            }
                                        }
                                        sendGridEmailSuppliersToAdd.AddRange(emailSuppliers);
                                       // await _email.SendMultipleEmail(emailSuppliers);
                                    }
                                    if (supplierCommunications.Count > 0 && !findSchedule.JobNumber.Contains("TEST"))
                                    {
                                        supplierCommunicationsToAdd.AddRange(supplierCommunications);
                                       // await _context.SupplierCommunications.BulkInsertAsync(supplierCommunications);
                                    }
                                }
                            }
                        }
                    }
                }
                sw7.Stop();
                sw3.Stop();
                //end of the foreach loop
                sw6.Start();
                //delete old links
                //TODO: would need to maek sure there weren't already schedule activity links -- ie update vs add new
                //remove previous Schedule_Sactivity --
                //TODO: don't delete, mark inactive - 
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    var deleteQuery = $"DELETE FROM SCHEDULE_SACTIVITY_LINK where SCHEDULE_AID in ('{string.Join("','", ScheduleLinksToDelete)}')";
                    var deleteCommand = new SqlCommand(deleteQuery, connection);
                    await deleteCommand.ExecuteNonQueryAsync();
                }
                //Insert the new ones
                await _context.ScheduleSactivityLinks.BulkInsertAsync(scheduleSactivityLinksToInsert);

                //update the activities
                await _context.ScheduleSactivities.BulkUpdateAsync(activitiesToBeUpdated, options => options.ColumnInputExpression = x => new { x.SchStartDate, x.SchEndDate, x.BaseEndDate, x.BaseStartDate, x.PlusminusDays, x.IniEndDate, x.IniStartDate, x.Calduration, x.SubNumber, x.UpdatedBy, x.UpdatedDateTime });

                //send all the emails
                await _email.SendMultipleEmail(sendGridEmailSuppliersToAdd);

                //save the sent communications
                await _context.SupplierCommunications.BulkInsertAsync(supplierCommunicationsToAdd);


                //update the milestones and schedules 
                sw4.Start();
                var jobNubmersSameStick = findSameStick.Select(x => x.JobNumber).ToList();
                var findJobSchedules = _context.Schedules.Include(x => x.Template).Include(x => x.ScheduleMilestones).ThenInclude(x => x.ScheduleSactivities).Where(x => jobNubmersSameStick.Contains(x.JobNumber) && x.IsActive == true).ToList();
                var allMilestones = findJobSchedules.SelectMany(x => x.ScheduleMilestones.Where(x => x.IsActive == true)).ToList();

                foreach (var milestone in allMilestones)
                {
                    milestone.SchStartDate = milestone.ScheduleSactivities.Where(x => x.IsActive == true).Min(x => x.SchStartDate);
                    milestone.SchEndDate = milestone.ScheduleSactivities.Where(x => x.IsActive == true).Max(x => x.SchEndDate);
                    milestone.IniStartDate = milestone.ScheduleSactivities.Where(x => x.IsActive == true).Min(x => x.SchStartDate);
                    milestone.IniEndDate = milestone.ScheduleSactivities.Where(x => x.IsActive == true).Max(x => x.SchEndDate);
                    milestone.BaseStartDate = milestone.ScheduleSactivities.Where(x => x.IsActive == true).Min(x => x.SchStartDate);
                    milestone.BaseEndDate = milestone.ScheduleSactivities.Where(x => x.IsActive == true).Max(x => x.SchEndDate);

                    if (milestone.SchStartDate != null && milestone.SchEndDate != null)
                    {
                        milestone.Duration = CalendarExtension.WorkingDaysDuration(milestone.SchStartDate.Value, milestone.SchEndDate.Value, holidays);
                        milestone.Calduration = (milestone.SchEndDate.Value - milestone.SchStartDate.Value).Days;
                    }

                    milestone.UpdatedBy = updateBy;
                    milestone.UpdatedDateTime = DateTime.Now;
                }

                await _context.ScheduleMilestones.BulkUpdateAsync(allMilestones, options => options.ColumnInputExpression = x => new { x.SchStartDate, x.SchEndDate, x.BaseEndDate, x.BaseStartDate,  x.IniEndDate, x.IniStartDate, x.Calduration, x.Duration, x.UpdatedBy, x.UpdatedDateTime });

                foreach (var schedule in findJobSchedules)
                {
                    //update the base and proj dates and duration for the schedule as a whole                        
                    schedule.DateToStart = schedule.ScheduleMilestones.Where(x => x.IsActive == true).Min(x => x.BaseStartDate);
                    schedule.DateToEnd = schedule.ScheduleMilestones.Where(x => x.IsActive == true).Max(x => x.BaseEndDate);
                    schedule.BaseStartDate = schedule.ScheduleMilestones.Where(x => x.IsActive == true).Min(x => x.BaseStartDate);
                    schedule.BaseEndDate = schedule.ScheduleMilestones.Where(x => x.IsActive == true).Max(x => x.BaseEndDate);
                    schedule.ProjStartDate = schedule.ScheduleMilestones.Where(x => x.IsActive == true).Min(x => x.BaseStartDate);
                    schedule.ProjEndDate = schedule.ScheduleMilestones.Where(x => x.IsActive == true).Max(x => x.BaseEndDate);
                    schedule.IniSchStartDate = schedule.ScheduleMilestones.Where(x => x.IsActive == true).Min(x => x.BaseStartDate);
                    schedule.IniSchEndDate = schedule.ScheduleMilestones.Where(x => x.IsActive == true).Max(x => x.BaseEndDate);
                    schedule.IniSchApproved = updatePreschedule.IniSchApproved;
                    schedule.IniSchApprovedby = updatePreschedule.IniSchApproved == true ? updateBy : schedule.IniSchApprovedby;
                    schedule.IniSchApproveDate = updatePreschedule.IniSchApproved == true ? DateTime.Now : schedule.IniSchApproveDate;
                    schedule.IniSchDuration = schedule.IniSchStartDate != null && schedule.IniSchEndDate != null ? CalendarExtension.WorkingDaysDuration(schedule.IniSchStartDate.Value, schedule.IniSchEndDate.Value, holidays) : null;
                    schedule.BaseDuration = schedule.BaseStartDate != null && schedule.BaseEndDate != null ? CalendarExtension.WorkingDaysDuration(schedule.BaseStartDate.Value, schedule.BaseEndDate.Value, holidays) : null;
                    schedule.ProjDuration = schedule.BaseDuration;
                    schedule.BaseCalduration = schedule.BaseStartDate != null && schedule.BaseEndDate != null ? (schedule.BaseEndDate.Value - schedule.BaseStartDate.Value).Days : null;
                    schedule.ProjCalduration = schedule.BaseCalduration;
                    schedule.UpdatedBy = updateBy;
                    schedule.UpdatedDateTime = DateTime.Now;
                }
                
                await _context.Schedules.BulkUpdateAsync(findJobSchedules, options => options.ColumnInputExpression = x => new { x.DateToStart, x.DateToEnd, x.BaseEndDate, x.BaseStartDate, x.ProjStartDate, x.ProjEndDate, x.IniSchStartDate, x.IniSchEndDate, x.IniSchApproved, x.IniSchDuration, x.BaseDuration, x.ProjDuration, x.ProjCalduration, x.UpdatedBy, x.UpdatedDateTime });


                var findScheduleDto = _mapper.Map<ScheduleDto>(findSchedule);
                var findStickSchedulesDto = _mapper.Map<List<ScheduleDto>>(findJobSchedules);
                foreach (var scheduleDto in findStickSchedulesDto)
                {
                    scheduleDto.OldIniSchStartDate = iniStartDate;
                    scheduleDto.BoolPublished = scheduleDto.Published == "T";
                    scheduleDto.UsingDefaultTemplate = scheduleDto.TemplateId == null;
                    scheduleDto.TemplateId = scheduleDto.TemplateId;
                    scheduleDto.Template = scheduleDto.Template;
                    scheduleDto.JobNumberNavigation = new JobDto() { JobNumber = findSameStick.FirstOrDefault(x => x.JobNumber == scheduleDto.JobNumber)?.JobNumber, SubdivisionId = findSameStick.FirstOrDefault(x => x.JobNumber == scheduleDto.JobNumber)?.SubdivisionId };
                    scheduleDto.OldIniSchStartDate = scheduleDto.IniSchStartDate;
                    scheduleDto.IniSchStartDateInputRaw = scheduleDto.IniSchStartDate?.ToString("MM/dd/yyyy") ?? "";
                }
                
                sw6.Stop();
                sw4.Stop();
                sw8.Stop();
                Console.WriteLine($"Initial code:{sw1.Elapsed}");
                Console.WriteLine($"parallel calclation:{sw2.Elapsed}");
                Console.WriteLine($"foreach loop:{sw3.Elapsed}");
                Console.WriteLine($"milestones and schedules:{sw4.Elapsed}");
                Console.WriteLine($"fetch data from db before foreach:{sw5.Elapsed}");
                Console.WriteLine($"foreach:{sw7.Elapsed}");
                Console.WriteLine($"save data after foreach:{sw6.Elapsed}");
                Console.WriteLine($"total:{sw8.Elapsed}");
                return Ok(new ResponseModel<List<ScheduleDto>>() { IsSuccess = true, Message = "Updated Initial Schedule", Value = findStickSchedulesDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return Ok(new ResponseModel<List<ScheduleDto>>() { IsSuccess = false, Message = "Failed to update Initial Schedule" });
            }
        }

        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> ScheduleAsync(string jobNumber)
        {
            try
            {
                var holidays = _context.CalendarsDays.Where(x => x.CalendarId == 1 && x.IsActive == true).Select(x => x.WorkDate).ToList();
                var treeData = new List<ScheduleTemplateTreeModel>();
                var schedule = _context.Schedules.Where(x => x.JobNumber == jobNumber && x.IsActive == true).OrderByDescending(x => x.CreatedDateTime).FirstOrDefault();

                if (schedule != null)
                {
                    var milestones = _context.ScheduleMilestones.Where(x => x.ScheduleId == schedule.ScheduleId && x.IsActive == true).OrderBy(x => x.Seq).ToList();
                    foreach (var milestone in milestones)
                    {
                        var findMilestone = _context.Milestones.SingleOrDefault(x => x.MilestoneId == milestone.MilestoneId);
                        var findActivities = _context.ScheduleSactivities.Include("Sactivity").Include("SubNumberNavigation").Include("ScheduleSactivityPreds.PredSactivity").Include("ScheduleSactivityLinks.Estactivity.Estdetails.Podetail.Poheader").Where(x => x.ScheduleMid == milestone.ScheduleMid && x.IsActive == true).ToList();
                        //var activitiesWithLinks = findActivities.Where(x => x.ScheduleSactivityLinks != null && x.ScheduleSactivityLinks.Count > 0).ToList();
                        //var activitiesLinksPO = activitiesWithLinks.SelectMany(x => x.ScheduleSactivityLinks.SelectMany(y => y.Estactivity.Estdetails.Select(z => z.Podetail?.Poheader))).ToList();

                        treeData.Add(new ScheduleTemplateTreeModel()
                        {
                            Id = Guid.NewGuid(),
                            ScheduleMilestone = _mapper.Map<ScheduleMilestoneDto>(milestone),
                            ScheduleSactivity = new ScheduleSactivityDto() { SubNumberNavigation = new SupplierDto() },
                            Milestone = _mapper.Map<MilestoneDto>(findMilestone),
                            HasChildren = findActivities.Any(),
                            Children = findActivities.OrderBy(x => x.Seq).Select(x => new ScheduleTemplateTreeModel()
                            {
                                Id = Guid.NewGuid(),
                                Sactivity = _mapper.Map<SactivityDto>(x.Sactivity),
                                ScheduleSactivityId = x.ScheduleAid,
                                ScheduleSactivity = _mapper.Map<ScheduleSactivityDto>(x),
                                ScheduleSactivityPreds = _mapper.Map<List<ScheduleSactivityPredDto>>(x.ScheduleSactivityPreds),
                                Predecessors = string.Join(",", x.ScheduleSactivityPreds.Where(y => y.IsActive == true).Select(y => y.PredSactivity.ActivityName)),
                                SchedSactivityLinkIds = x.ScheduleSactivityLinks.Where(x => x.IsActive == true).Select(y => y.EstactivityId).ToList(),
                                PredecessorIds = x.ScheduleSactivityPreds.Where(y => y.IsActive == true).Select(y => y.PredSactivityId).ToList(),//TODO: fix bad data there are nulls that shoulnd't be there. For now, using Pred_sactivity_id instead of Pred_schedule_aid for the predeciessor id
                                HasChildren = false,
                                TotalPOAmount = Math.Round((double)x.ScheduleSactivityLinks.SelectMany(y => y.Estactivity.Estdetails).Where(z => z.Podetail?.IsActive == true && z.Podetail.Poheader.Postatus != 5).Sum(x => x.Podetail.Poamount)),
                                SupplierEditable = !x.ScheduleSactivityLinks.Any(y => y.Estactivity.Estdetails.Any(z => z.Podetail?.IsActive == true && z.Podetail.Poheader.Postatus != 5)) && x.ActualEndDate == null//Supplier can be edited if there is no issued PO, and the activity is not completed.
                            }).ToList(),
                            SactivityIds = findActivities.Select(x => x.SactivityId).ToList(),
                        });
                    }
                    foreach (var item in treeData.SelectMany(x => x.Children).Where(x => x.ScheduleSactivity != null))
                    {
                        item.ScheduleSactivity.BoolComplete = item.ScheduleSactivity.Complete == "T" ? true : false;
                        item.ScheduleSactivity.BoolStarted = item.ScheduleSactivity.ActualStartDate != null;
                        item.ScheduleSactivity.BoolIgnoreNoWorkDays = item.ScheduleSactivity.IsLocked == "T" ? true : false;
                        item.ScheduleSactivity.SupplierEditable = item.SupplierEditable;
                        item.ScheduleSactivity.ActivityName = item.ScheduleSactivity.Sactivity.ActivityName;//need to move the name up for the calendar view
                                                                                                            // item.ScheduleSactivity.FirstPredId = item.PredecessorIds.Any() ?  treeData.SelectMany(x => x.Children).FirstOrDefault(x => x.ScheduleSactivity.SactivityId == item.PredecessorIds.FirstOrDefault()).ScheduleSactivityId : null;//TODO: wrong, trying with just one pred to make gant work
                        item.ScheduleSactivity.FirstPredId = null;//TODO: wrong, trying with just one pred to make gant work
                        //item.ScheduleSactivity.PercentComplete = Math.Round(random.NextDouble(), 2);
                        item.ScheduleSactivity.Title = "Test";
                        item.ScheduleSactivity.Start = DateTime.Now;
                        item.ScheduleSactivity.End = DateTime.Now.AddDays(1);
                        item.ScheduleSactivity.PredIds = item.ScheduleSactivityPreds.Select(x => x.PredSactivityId).ToList();
                        item.ScheduleSactivity.Predecessors = item.ScheduleSactivityPreds;//TODO: include properly. for now, leaving out to test changing preds
                    }
                    foreach (var milestone in treeData)
                    {
                        foreach (var activity in milestone.Children)
                        {
                            activity.SearchTags = $"{milestone.Milestone.MilestoneName}|{activity.Sactivity.ActivityName}";
                            milestone.SearchTags = activity.SearchTags;
                        }
                    }
                }
                //if (treeData.Any())
                //{
                //    treeData.First().SchedulePublished = schedule.Published == "T";
                //    treeData.First().ReleaseDate = schedule.IniSchStartDate;
                //}              
                return new OkObjectResult(new ResponseModel<List<ScheduleTemplateTreeModel>> { Value = treeData, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleTemplateTreeModel>> { IsSuccess = false, Message = "Failed to fetch schedules" });
            }
        }

        private void CalculateAdjustDates(ScheduleSactivity activity, List<ScheduleSactivityPred> preds, List<DateTime>? holidays, List<ScheduleSactivity> returnList)
        {
            var currentActivity = activity;
            var findSuccessorActivities = preds.Where(x => x.PredSactivityId == currentActivity.SactivityId).ToList();//PredScheduleAid has become null. Ask heather to fix
                                                                                                                      //  var findSuccessorActivities = preds.Where(x => x.PredScheduleAid == currentActivity.ScheduleAid).ToList();
            if (findSuccessorActivities.Count > 0)
            {
                //Parallel.ForEach(findSuccessorActivities, succ =>
                //{
                //    //if actual end date is not null, adjust based on that rather than scheduled end date
                //    if (succ.ScheduleA.ActualStartDate == null || succ.ScheduleA.ActualEndDate == null)
                //    {
                //        succ.ScheduleA.SchStartDate = succ.ScheduleA.IsLocked != "T" ? currentActivity.ActualEndDate != null ? CalendarExtension.AddWorkingDays((DateTime)currentActivity.ActualEndDate, (int)succ.ScheduleA.LagTime + 1, holidays) : CalendarExtension.AddWorkingDays((DateTime)currentActivity.SchEndDate, (int)succ.ScheduleA.LagTime + 1, holidays) : currentActivity.ActualEndDate != null ? currentActivity.ActualEndDate.Value.AddDays((int)succ.ScheduleA.LagTime + 1) : currentActivity.SchEndDate.Value.AddDays((int)succ.ScheduleA.LagTime + 1);
                //        succ.ScheduleA.SchEndDate = succ.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)succ.ScheduleA.SchStartDate, (int)succ.ScheduleA.Duration > 0 ? (int)succ.ScheduleA.Duration - 1 : 0, holidays) : succ.ScheduleA.SchStartDate.Value.AddDays((int)succ.ScheduleA.Duration > 0 ? (int)succ.ScheduleA.Duration - 1 : 0);
                //    }

                //    updateActivities.Add(succ.ScheduleA);
                //    CalculateAdjustDates(succ.ScheduleA, updateActivities, preds, holidays);
                //});
                foreach (var dependentActivity in findSuccessorActivities)
                {
                    //if actual end date is not null, adjust based on that rather than scheduled end date
                    if (dependentActivity.ScheduleA.ActualStartDate == null || dependentActivity.ScheduleA.ActualEndDate == null)
                    {
                        dependentActivity.ScheduleA.SchStartDate = dependentActivity.ScheduleA.IsLocked != "T" ? currentActivity.ActualEndDate != null ? CalendarExtension.AddWorkingDays((DateTime)currentActivity.ActualEndDate, (int)dependentActivity.ScheduleA.LagTime + 1, holidays) : CalendarExtension.AddWorkingDays((DateTime)currentActivity.SchEndDate, (int)dependentActivity.ScheduleA.LagTime + 1, holidays) : currentActivity.ActualEndDate != null ? currentActivity.ActualEndDate.Value.AddDays((int)dependentActivity.ScheduleA.LagTime + 1) : currentActivity.SchEndDate.Value.AddDays((int)dependentActivity.ScheduleA.LagTime + 1);
                        dependentActivity.ScheduleA.SchEndDate = dependentActivity.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)dependentActivity.ScheduleA.SchStartDate, (int)dependentActivity.ScheduleA.Duration > 0 ? (int)dependentActivity.ScheduleA.Duration - 1 : 0, holidays) : dependentActivity.ScheduleA.SchStartDate.Value.AddDays((int)dependentActivity.ScheduleA.Duration > 0 ? (int)dependentActivity.ScheduleA.Duration - 1 : 0);
                    }
                    returnList.Add(dependentActivity.ScheduleA);
                    CalculateAdjustDates(dependentActivity.ScheduleA, preds, holidays, returnList);
                }
                ;
            }
            return;
        }

        private List<ScheduleSactivity> RecalculateDates(List<ScheduleSactivityPred> preds, ScheduleSactivity topActivity, List<DateTime>? holidays, bool? adjustBaseDates = false)
        {
            //using a Breadth-first tree traversal, adding nodes to a Queue, to update scheduled or baseline dates based on predecessor

            Dictionary<int, List<ActivityNode>> children = new Dictionary<int, List<ActivityNode>>();
            Queue<ActivityNode> q = new Queue<ActivityNode>();
            
            var countNodes1 = 0;

            //construct the dictionary 
            var visited = new List<ScheduleSactivity>();
            foreach (var n in preds.Where(x => x.IsActive == true))
            {
                if (!children.ContainsKey((int)n.PredScheduleAid))
                {
                    children[(int)n.PredScheduleAid] = new List<ActivityNode>();
                }
                children[(int)n.PredScheduleAid].Add(new ActivityNode() { CurrentActivity = n.ScheduleA, ParentActivityId = n.PredScheduleAid });
            }

            //add the first node to the queue
            q.Enqueue(new ActivityNode() { CurrentActivity = topActivity, ParentActivityId = null });
            while (q.Count > 0)
            {
                var currentNode = q.Dequeue();
                var currentParent = currentNode.ParentActivityId != null ? currentNode.ParentActivity : null;//current parent null for top of tree
                DateTime newStart;
                DateTime newEnd;
                if (visited.Contains(currentNode.CurrentActivity) && currentParent != null)
                {
                    //if this activity is already visited, calculate new dates bases on current parent, compare with the visited one to see which branch should stay, to avoid recalculating multiple times for case when actiity is in tree multiple times due to multiple predecessor
                    //calculate new dates

                    if (adjustBaseDates == true)
                    {
                        newStart = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.BaseEndDate, (int)(currentNode.CurrentActivity.LagTime ?? 0) + 1, holidays) : currentParent.CurrentActivity.BaseEndDate.Value.AddDays((int)(currentNode.CurrentActivity.LagTime ?? 0) + 1);
                        newEnd = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays(newStart, (int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0, holidays) : newStart.AddDays((int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0);
                    }
                    else
                    {
                        newStart = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.SchEndDate, (int)(currentNode.CurrentActivity.LagTime ?? 0) + 1, holidays) : currentParent.CurrentActivity.SchEndDate.Value.AddDays((int)(currentNode.CurrentActivity.LagTime ?? 0) + 1);//2/25/25 calculating based on schedule dates, even ifactual is different
                        //newStart = currentNode.CurrentActivity.IsLocked != "T" ? currentParent.CurrentActivity.ActualEndDate != null ? CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.ActualEndDate, (int)currentNode.CurrentActivity.LagTime + 1, holidays) : CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.SchEndDate, (int)currentNode.CurrentActivity.LagTime + 1, holidays) : currentParent.CurrentActivity.ActualEndDate != null ? currentParent.CurrentActivity.ActualEndDate.Value.AddDays((int)currentNode.CurrentActivity.LagTime + 1) : currentParent.CurrentActivity.SchEndDate.Value.AddDays((int)currentNode.CurrentActivity.LagTime + 1);
                        newEnd = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)newStart, (int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0, holidays) : newStart.AddDays((int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0);
                    }

                    //if previously calculated value is later date than or equal to new calculated value, just keep going, don't enque the current children, don't add current to visited, because the previous one will be used, otherwise, remove visited ones, add new
                    if ((adjustBaseDates != true && (currentNode.CurrentActivity.ActualStartDate == null && currentNode.CurrentActivity.ActualEndDate == null) && visited.Where(x => x.ScheduleAid == currentNode.CurrentActivity.ScheduleAid).FirstOrDefault().SchStartDate < newStart) || (adjustBaseDates == true && (currentNode.CurrentActivity.ActualStartDate == null && currentNode.CurrentActivity.ActualEndDate == null) && visited.Where(x => x.ScheduleAid == currentNode.CurrentActivity.ScheduleAid).FirstOrDefault().BaseStartDate < newStart))
                    {
                        // if current value is greater, remove previous children from q, enque current children
                        visited.RemoveAll(x => x.ScheduleAid == currentNode.CurrentActivity.ScheduleAid);
                        if (adjustBaseDates == true)
                        {
                            currentNode.CurrentActivity.BaseStartDate = newStart;
                            currentNode.CurrentActivity.BaseEndDate = newEnd;
                        }
                        else
                        {
                            currentNode.CurrentActivity.SchStartDate = newStart;
                            currentNode.CurrentActivity.SchEndDate = newEnd;
                        }


                        visited.Add(currentNode.CurrentActivity);
                        q = new Queue<ActivityNode>(q.Where(x => x.ParentActivityId != currentNode.CurrentActivity.ScheduleAid));//remove any old children from queue -- this creates new queue, probably poor performance but should not happen often
                        if (children.ContainsKey(currentNode.CurrentActivity.ScheduleAid))
                        {
                            foreach (var n in children[currentNode.CurrentActivity.ScheduleAid])
                            {
                                q.Enqueue(new ActivityNode() { CurrentActivity = n.CurrentActivity, ParentActivityId = currentNode.CurrentActivity.ScheduleAid, ParentActivity = currentNode });
                                countNodes1++;
                            }
                        }
                    }
                }
                else
                {
                    //activity was not already visited, add to visited collection and process dates
                    visited.Add(currentNode.CurrentActivity);
                    if (adjustBaseDates != true && currentParent != null && (currentNode.CurrentActivity.ActualStartDate == null && currentNode.CurrentActivity.ActualEndDate == null))
                    {
                        currentNode.CurrentActivity.SchStartDate = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.SchEndDate, (int)(currentNode.CurrentActivity.LagTime ?? 0) + 1, holidays) : currentParent.CurrentActivity.SchEndDate.Value.AddDays((int)currentNode.CurrentActivity.LagTime + 1);
                        //currentNode.CurrentActivity.SchStartDate = currentNode.CurrentActivity.IsLocked != "T" ? currentParent.CurrentActivity.ActualEndDate != null ? CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.ActualEndDate, (int)currentNode.CurrentActivity.LagTime + 1, holidays) : CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.SchEndDate, (int)currentNode.CurrentActivity.LagTime + 1, holidays) : currentParent.CurrentActivity.ActualEndDate != null ? currentParent.CurrentActivity.ActualEndDate.Value.AddDays((int)currentNode.CurrentActivity.LagTime + 1) : currentParent.CurrentActivity.SchEndDate.Value.AddDays((int)currentNode.CurrentActivity.LagTime + 1);
                        currentNode.CurrentActivity.SchEndDate = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentNode.CurrentActivity.SchStartDate, (int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0, holidays) : currentNode.CurrentActivity.SchStartDate.Value.AddDays((int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0);
                    }
                    else if (adjustBaseDates == true && currentParent != null)
                    {
                        currentNode.CurrentActivity.BaseStartDate = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.BaseEndDate, (int)(currentNode.CurrentActivity.LagTime ?? 0) + 1, holidays) : currentParent.CurrentActivity.BaseEndDate.Value.AddDays((int)(currentNode.CurrentActivity.LagTime ?? 0) + 1);
                        currentNode.CurrentActivity.BaseEndDate = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentNode.CurrentActivity.BaseStartDate, (int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0, holidays) : currentNode.CurrentActivity.BaseStartDate.Value.AddDays((int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0);
                    }
                    if (children.ContainsKey(currentNode.CurrentActivity.ScheduleAid))
                    {
                        foreach (var n in children[currentNode.CurrentActivity.ScheduleAid])
                        {
                            q.Enqueue(new ActivityNode() { CurrentActivity = n.CurrentActivity, ParentActivityId = currentNode.CurrentActivity.ScheduleAid, ParentActivity = currentNode });
                            countNodes1++;
                        }
                    }
                }
            }
            return visited;
        }

        private void CalculateAdjustBaseDates(ScheduleSactivity activity, List<ScheduleSactivityPred> preds, List<DateTime>? holidays, List<ScheduleSactivity> returnList)
        {
            var currentActivity = activity;
            var findSuccessorActivities = preds.Where(x => x.PredSactivityId == currentActivity.SactivityId).ToList();
            if (findSuccessorActivities.Count > 0)
            {
                //Parallel.ForEach(findSuccessorActivities, succ =>
                //{
                //    succ.ScheduleA.BaseStartDate = succ.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentActivity.BaseEndDate, (int)succ.ScheduleA.LagTime + 1, holidays) : currentActivity.BaseEndDate.Value.AddDays((int)succ.ScheduleA.LagTime + 1);
                //    succ.ScheduleA.BaseEndDate = succ.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)succ.ScheduleA.BaseStartDate, (int)succ.ScheduleA.Duration > 0 ? (int)succ.ScheduleA.Duration - 1 : 0, holidays) : succ.ScheduleA.BaseStartDate.Value.AddDays((int)succ.ScheduleA.Duration > 0 ? (int)succ.ScheduleA.Duration - 1 : 0);
                //    updateActivities.Add(succ.ScheduleA);
                //    CalculateAdjustBaseDates(succ.ScheduleA, updateActivities, preds, holidays);
                //});
                foreach (var dependentActivity in findSuccessorActivities)
                {
                    dependentActivity.ScheduleA.BaseStartDate = dependentActivity.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentActivity.BaseEndDate, (int)(dependentActivity.ScheduleA.LagTime ?? 0) + 1, holidays) : currentActivity.BaseEndDate.Value.AddDays((int)(dependentActivity.ScheduleA.LagTime ?? 0) + 1);
                    dependentActivity.ScheduleA.BaseEndDate = dependentActivity.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)dependentActivity.ScheduleA.BaseStartDate, (int)dependentActivity.ScheduleA.Duration > 0 ? (int)dependentActivity.ScheduleA.Duration - 1 : 0, holidays) : dependentActivity.ScheduleA.BaseStartDate.Value.AddDays((int)dependentActivity.ScheduleA.Duration > 0 ? (int)dependentActivity.ScheduleA.Duration - 1 : 0);
                    returnList.Add(dependentActivity.ScheduleA);
                    CalculateAdjustBaseDates(dependentActivity.ScheduleA, preds, holidays, returnList);
                }
                ;
            }
            return;
        }

        private void AdjustDatesBasedOnPreSchedule(ScheduleSactivity activity, List<ScheduleSactivityPred> preds, List<DateTime>? holidays, List<ScheduleSactivity> returnActivities)
        {
            //This is for calculating end date based on start date for Denise's release/initial schedule. It should only be used when shchedule is not released and there are no actual dates yet
            //TODO: need to prevent use if schedule is started
            //TODO: how to reset schedule if something is started by mistake?
            //updates baseline, proj, and scheduled dates
            var currentActivity = activity;
            var findDependentActivities = preds.Where(x => x.PredSactivityId == currentActivity.SactivityId).ToList();
            if (findDependentActivities.Count > 0)
            {
                //Parallel.ForEach(findDependentActivities, dependentActivity =>
                //{
                //    //if ignore no workdays, add days, not working days
                //    //TODO: fix null here, seem to sometimes get error
                //    dependentActivity.ScheduleA.SchStartDate = dependentActivity.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentActivity.SchEndDate, (int)dependentActivity.ScheduleA.LagTime + 1, holidays) : currentActivity.SchEndDate.Value.AddDays((int)dependentActivity.ScheduleA.LagTime + 1);
                //    dependentActivity.ScheduleA.SchEndDate = dependentActivity.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)dependentActivity.ScheduleA.SchStartDate, (int)dependentActivity.ScheduleA.Duration > 0 ? (int)dependentActivity.ScheduleA.Duration - 1 : 0, holidays) : dependentActivity.ScheduleA.SchStartDate.Value.AddDays((int)dependentActivity.ScheduleA.Duration > 0 ? (int)dependentActivity.ScheduleA.Duration - 1 : 0);
                //    returnActivities.Add(dependentActivity.ScheduleA);// why adding? It's already on the list? it doesn't need to be added again, or returned, since this method modifies the list -- ws adding because could be it has multiple preds and need to pick max -- need to fix this logic
                //    AdjustDatesBasedOnPreSchedule(dependentActivity.ScheduleA, updateActivities, preds, holidays, returnActivities);
                //});
                foreach (var dependentActivity in findDependentActivities)
                {
                    //if ignore no workdays, add days, not working days
                    //TODO: fix null here, seem to sometimes get error
                    dependentActivity.ScheduleA.SchStartDate = dependentActivity.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentActivity.SchEndDate, (int)dependentActivity.ScheduleA.LagTime + 1, holidays) : currentActivity.SchEndDate.Value.AddDays((int)dependentActivity.ScheduleA.LagTime + 1);
                    dependentActivity.ScheduleA.SchEndDate = dependentActivity.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)dependentActivity.ScheduleA.SchStartDate, (int)dependentActivity.ScheduleA.Duration > 0 ? (int)dependentActivity.ScheduleA.Duration - 1 : 0, holidays) : dependentActivity.ScheduleA.SchStartDate.Value.AddDays((int)dependentActivity.ScheduleA.Duration > 0 ? (int)dependentActivity.ScheduleA.Duration - 1 : 0);
                    returnActivities.Add(dependentActivity.ScheduleA);// why adding? It's already on the list? it doesn't need to be added again, or returned, since this method modifies the list -- ws adding because could be it has multiple preds and need to pick max -- need to fix this logic
                    AdjustDatesBasedOnPreSchedule(dependentActivity.ScheduleA, preds, holidays, returnActivities);
                }
            }
            //returnActivities.Add(activity); 
            return;
        }

        [HttpPut]
        public async Task<IActionResult> UpdatePublishScheduleAsync([FromBody] ScheduleDto schedule)
        {
            try
            {
                var findSchedule = await _context.Schedules.SingleOrDefaultAsync(x => x.ScheduleId == schedule.ScheduleId);
                findSchedule.Published = schedule.BoolPublished ? "T" : "F";
                findSchedule.UpdatedBy = User.Identity.Name.Split('@')[0];
                findSchedule.UpdatedDateTime = DateTime.Now;
                _context.Schedules.Update(findSchedule);
                await _context.SaveChangesAsync();
                var scheduleDto = _mapper.Map<ScheduleDto>(schedule);
                return new OkObjectResult(new ResponseModel<ScheduleDto> { Value = scheduleDto, IsSuccess = true, Message = "Published Schedule updated" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheduleDto> { IsSuccess = false, Message = "Failed to update publish schedule" });
            }
        }

        [HttpGet()]
        public async Task<IActionResult> GetHolidaysAsync()
        {
            try
            {
                var holidays = await _context.CalendarsDays.Where(x => x.CalendarId == 1 && x.IsActive == true).ToListAsync();
                var holidaysDto = _mapper.Map<List<CalendarsDayDto>>(holidays);
                return Ok(new ResponseModel<List<CalendarsDayDto>>() { IsSuccess = true, Value = holidaysDto, Message = "Retrieved calendar" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return Ok(new ResponseModel<List<CalendarsDayDto>>() { IsSuccess = false, Value = null, Message = "Failed to get holidays" });
            }
        }

        [HttpGet("{checkDate}")]
        public async Task<IActionResult> CheckHolidayAsync(string checkDate)
        {
            try
            {
                var dateToCheck = DateTime.Parse(checkDate);
                var holidays = await _context.CalendarsDays.Where(x => x.CalendarId == 1 && x.IsActive == true).Select(x => x.WorkDate).ToListAsync();
                bool enteredDateHoliday = dateToCheck.DayOfWeek == DayOfWeek.Sunday || dateToCheck.DayOfWeek == DayOfWeek.Saturday || holidays.Contains(dateToCheck);
                return Ok(new ResponseModel<bool>() { IsSuccess = true, Value = enteredDateHoliday });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return Ok(new ResponseModel<bool>() { IsSuccess = false, Message = "Failed to check holiday" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> CalculateScheduleBasedOnStartDateAsync([FromBody] ScheduleDto schedule)
        {
            try
            {
                //Get the updated IniEndDate and Duration based on updated ini start date -- this does not save the changes
                var findSchedule = await _context.Schedules.SingleOrDefaultAsync(x => x.ScheduleId == schedule.ScheduleId);
                if (findSchedule == null)
                {
                    var result = await SchedulesFromTemplateAsync(new List<ScheduleDto>() { schedule });
                    findSchedule = await _context.Schedules.FirstOrDefaultAsync(x => x.JobNumber == schedule.JobNumber);
                    if (findSchedule == null)
                    {
                        return new OkObjectResult(new ResponseModel<ScheduleDto> {  IsSuccess = false, Message = "Failed to create schedule from template" });
                    }
                }
                var holidays = _context.CalendarsDays.Where(x => x.CalendarId == 1 && x.IsActive == true).Select(x => x.WorkDate).ToList();
                var firstActivity = _context.ScheduleSactivities.Where(x => x.ScheduleId == findSchedule.ScheduleId && x.Seq == 1 && x.IsActive == true).FirstOrDefault();
                DateTime? maxEndDate = null;
                if (firstActivity != null)
                {
                    firstActivity.SchStartDate = schedule.IniSchStartDate;
                    if (firstActivity.SchStartDate == null)
                    {
                        return new OkObjectResult(new ResponseModel<ScheduleDto> { IsSuccess = false, Message = "Missing start date" });
                    }
                    firstActivity.SchEndDate = firstActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)firstActivity.SchStartDate, (firstActivity.Duration ?? 0) > 0 ? (firstActivity.Duration ?? 0) - 1 : 0, holidays) : firstActivity.SchStartDate.Value.AddDays((firstActivity.Duration ?? 0) > 0 ? (firstActivity.Duration ?? 0) - 1 : 0);

                    firstActivity.BaseStartDate = firstActivity.SchStartDate;
                    firstActivity.IniStartDate = firstActivity.SchStartDate;
                    firstActivity.IniEndDate = firstActivity.SchEndDate;
                    firstActivity.BaseEndDate = firstActivity.SchEndDate;
                    var listActivities = new List<ScheduleSactivity>();
                    var findSactivities = _context.ScheduleSactivities.Where(x => x.ScheduleId == findSchedule.ScheduleId && x.IsActive == true).ToList();//TODO: go through mid
                    var findSuccessorActivities = _context.ScheduleSactivityPreds.Include("ScheduleA").Where(x => findSactivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid) && x.IsActive == true).ToList();
                    var returnList = RecalculateDates(findSuccessorActivities, firstActivity, holidays, false);

                    //if something was not in the predecessor chain, set that date to current start date, and adjust again, maybe there should be a warning??
                    var activitiesNotAdjusted = findSactivities.Where(x => x != firstActivity && !returnList.Contains(x)).ToList();
                    listActivities = new List<ScheduleSactivity>();
                    var returnList2 = new List<ScheduleSactivity>();
                    //while (activitiesNotAdjusted.Any())
                    //{
                    //    var firstActivity1 = activitiesNotAdjusted.OrderBy(x => x.Seq).FirstOrDefault();
                    //    if (firstActivity1 != null)
                    //    {
                    //       // firstActivity1.SchStartDate = findSchedule.IniSchApproved == true ? findSchedule.IniSchStartDate : null;
                    //        firstActivity1.SchStartDate = schedule.IniSchStartDate;//TODO: check this logic
                    //        firstActivity1.SchEndDate = firstActivity1.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)firstActivity1.SchStartDate, (int)firstActivity1.Duration > 0 ? (int)firstActivity1.Duration - 1 : 0, holidays) : firstActivity1.SchStartDate.Value.AddDays((int)firstActivity1.Duration > 0 ? (int)firstActivity1.Duration - 1 : 0);
                    //        //AdjustDatesBasedOnPreSchedule(firstActivity1, findSuccessorActivities, holidays, returnList2);
                    //        //returnList2.Add(firstActivity1);

                    //        RecalculateDates(findSuccessorActivities, firstActivity1, holidays, false);
                    //        returnList2 = visited;

                    //        //returnList2.AddRange(additems);
                    //        //returnList2.Add(firstActivity1);
                    //        activitiesNotAdjusted = activitiesNotAdjusted.Where(x => x != firstActivity1 && !returnList2.Contains(x)).ToList();
                    //    }
                    //}//add the not adjusted return list to the other return list to update all the dates
                    //returnList.AddRange(returnList2);


                    var updateActivities = _context.ScheduleSactivities.Where(x => returnList.Select(y => y.ScheduleAid).Contains(x.ScheduleAid) && x.IsActive == true).ToList();
                    foreach (var activity in updateActivities)
                    {
                        activity.SchEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
                    }
                    maxEndDate = updateActivities.Max(x => x.SchEndDate);
                }
                var findEndDate = maxEndDate;
                schedule.IniSchEndDate = maxEndDate;
                int? duration = schedule.IniSchStartDate != null && schedule.IniSchEndDate != null ? CalendarExtension.WorkingDaysDuration(schedule.IniSchStartDate.Value, schedule.IniSchEndDate.Value, holidays) : null;
                schedule.IniSchDuration = duration;
                schedule.ScheduleId = findSchedule?.ScheduleId ?? 0;
                return new OkObjectResult(new ResponseModel<ScheduleDto> { Value = schedule, IsSuccess = true, Message = "Dates Calculated." });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheduleDto> { IsSuccess = false, Message = "Failed to update PreSchedule Start Date" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateScheduleSActivityNotesAsync([FromBody] ScheduleSactivityDto updateActivity)
        {
            try
            {
                //update notes on the schedule activity
                var updateBy = User.Identity.Name.Split('@')[0];
                var findActivity = _context.ScheduleSactivities.Include(x => x.ScheduleSactivityPreds).Where(x => x.ScheduleAid == updateActivity.ScheduleAid).SingleOrDefault();
                findActivity.Note = updateActivity.Note;
                findActivity.SupplierNote = updateActivity.SupplierNote;
                findActivity.UpdatedBy = User.Identity.Name.Split('@')[0];
                findActivity.UpdatedDateTime = DateTime.Now;
                _context.ScheduleSactivities.Update(findActivity);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<ScheduleSactivityDto>() { Value = updateActivity, IsSuccess = true, Message = "Schedule updated" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheduleSactivityDto> { IsSuccess = false, Message = "Failed to update Schedule SActivity" });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdateScheduleSActivityAsync([FromBody] ScheduleSactivityDto updateActivity)
        {
            try
            {
                //NOT USED?
                //This is used on several pages, like construction daily tasks, to update a single activity, need to fix it
                //if the date is changed, it needs to updat the whole schedule 

                //TODO: add message to supplier 
                //scheduled should not be editable if actual is already entered,
                //baseline only changes if start date change, else baseline fixed. -- baseline to be editable only by Regis

                bool updateBaseDates = false; //will set this to whether base dates changing or start and whether it's regis or not
                bool updateSchedDates = true;//TODO: baseline can be changed by Director - Regis only 

                //changeing scheduled end changes Plus minus days, but not duration
                //TODO: supplier is only editable if no issued purchase order. Cancelling a po and reissuing with another supplier should update the schedule
                //what if they enter something inconsistent in duration / schedule start / scheduled end 
                var updateBy = User.Identity.Name.Split('@')[0];
                var holidays = _context.CalendarsDays.Where(x => x.CalendarId == 1 && x.IsActive == true).Select(x => x.WorkDate).ToList();
                var findActivity = _context.ScheduleSactivities.Include(x => x.ScheduleSactivityPreds).Where(x => x.ScheduleAid == updateActivity.ScheduleAid).SingleOrDefault();

                var findSchedule = _context.Schedules.Where(x => x.ScheduleId == findActivity.ScheduleId).FirstOrDefault();//TODO: uses ScheduleM, 
                //if (findSchedule.IniSchApproved != true)
                //{
                //    return Ok(new ResponseModel<ScheduleSactivityDto>() { Value = updateActivity, IsSuccess = false, Message = "Cannot adjust schedule before it is released" });
                //    //TODO: should some parts of the schedule but not the dates be editable before Denise releases
                //}
                var findSactivities = _context.ScheduleSactivities.Where(x => x.ScheduleId == updateActivity.ScheduleM.ScheduleId && x.IsActive == true).ToList();
                var findStart = findSactivities.Where(x => x.SactivityId == 22).FirstOrDefault();//22 is id for start date, I think -- TODO: That won't be true in PROD. Fix this..

                //"IsLocked" = IgnoreNoWorkDays
                var findPreds = findActivity.ScheduleSactivityPreds.ToList();
                if (updateActivity.PredIds != null && !findPreds.Select(x => x.PredSactivityId).SequenceEqual(updateActivity.PredIds))
                {
                    var newPreds = updateActivity.PredIds.Select(x => new ScheduleSactivityPred()
                    {
                        ScheduleAid = updateActivity.ScheduleAid,
                        PredSactivityId = x,
                        //PredScheduleAid = ??,
                        CreatedBy = updateBy,
                        UpdatedBy = updateBy,
                    }).ToList();
                    findActivity.ScheduleSactivityPreds = newPreds;
                }//TODO: what happens to old preds if this is done. 

                //TODO: determine if the change in preds changes the dates, if so, need to update schedule
                var newlyComplete = updateActivity.ActualEndDate == null && findActivity.ActualEndDate != null;
                findActivity.Complete = updateActivity.BoolComplete == true || updateActivity.ActualEndDate != null ? "T" : "F";

                //only update the whole schedule if one of the dates is changed. TODO: it should also change if a pred changed
                var datechanged = findActivity.Duration != updateActivity.Duration || findActivity.LagTime != updateActivity.LagTime || findActivity.BaseStartDate != updateActivity.BaseStartDate || findActivity.SchStartDate != updateActivity.SchStartDate || findActivity.SchEndDate != updateActivity.SchEndDate || findActivity.ActualStartDate != updateActivity.ActualStartDate || findActivity.ActualEndDate != updateActivity.ActualEndDate;
                if (datechanged)
                {
                    var updateActivities = new List<ScheduleSactivity>();
                    if ((findActivity.BaseStartDate != updateActivity.BaseStartDate || findActivity.BaseEndDate != updateActivity.BaseEndDate))
                    {
                        //findActivity.Duration = updateActivity.ActualEndDate != null && updateActivity.ActualStartDate != null ? CalendarExtension.WorkingDaysDuration(updateActivity.ActualStartDate.Value, updateActivity.ActualEndDate.Value, holidays) : null;//TODO: not if ignore no work days //Todo: does changeing base dates update duration 

                        findActivity.BaseStartDate = updateActivity.BaseStartDate;
                        findActivity.BaseEndDate = updateActivity.BaseEndDate != findActivity.BaseEndDate ? updateActivity.BaseEndDate : findActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findActivity.BaseStartDate, (int)updateActivity.Duration - 1, holidays) : findActivity.BaseStartDate.Value.AddDays((int)updateActivity.Duration - 1);
                        updateBaseDates = true;
                        updateSchedDates = false;

                        var plusminusdaysend = findActivity.ActualEndDate != null ? findActivity.ActualEndDate.Value : findActivity.SchEndDate.Value;
                        findActivity.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(findActivity.BaseEndDate.Value, plusminusdaysend, holidays);
                        //update the whole schedule
                        var listActivities = new List<ScheduleSactivity>();
                        var findSuccessorActivities = _context.ScheduleSactivityPreds.Include("ScheduleA").Where(x => findSactivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();

                        //CalculateAdjustBaseDates(findActivity, findSuccessorActivities, holidays, returnList);
                        //returnList.Add(findActivity);

                        var returnList = RecalculateDates(findSuccessorActivities, findActivity, holidays, true);

                        updateActivities = _context.ScheduleSactivities.Where(x => returnList.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();

                        foreach (var activity in updateActivities)
                        {
                            if (updateBaseDates == true)
                            {
                                activity.BaseStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.BaseStartDate);
                                activity.BaseEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.BaseEndDate);
                            }
                            var plusminusdaysend1 = activity.ActualEndDate != null ? activity.ActualEndDate.Value : activity.SchEndDate.Value;
                            activity.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(activity.BaseEndDate.Value, plusminusdaysend, holidays);
                            activity.UpdatedBy = updateBy;
                            activity.UpdateDate = DateTime.Now;
                            activity.UpdatedDateTime = DateTime.Now;
                        }
                    }
                    if ((findActivity.ActualStartDate != updateActivity.ActualStartDate || findActivity.ActualEndDate != updateActivity.ActualEndDate))
                    {
                        findActivity.ActualDuration = updateActivity.ActualEndDate != null && updateActivity.ActualStartDate != null ? CalendarExtension.WorkingDaysDuration(updateActivity.ActualStartDate.Value, updateActivity.ActualEndDate.Value, holidays) : null;//TODO: not if ignore no work days
                        findActivity.Calduration = updateActivity.ActualEndDate != null && updateActivity.ActualStartDate != null ? (updateActivity.ActualEndDate.Value - updateActivity.ActualStartDate.Value).Days : null;//TODO: check if calduration is based on actual start and end or scheduled start and end
                        findActivity.ActualEndDate = updateActivity.ActualEndDate;
                        findActivity.ActualStartDate = updateActivity.ActualStartDate;
                        var plusminusdaysend = findActivity.ActualEndDate != null ? findActivity.ActualEndDate.Value : findActivity.SchEndDate.Value;
                        findActivity.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(findActivity.BaseEndDate.Value, plusminusdaysend, holidays);
                    }
                    if ((findActivity.SchStartDate != updateActivity.SchStartDate || findActivity.SchEndDate != updateActivity.SchEndDate))
                    {
                        findActivity.SchStartDate = updateActivity.SchStartDate;
                        findActivity.SchEndDate = updateActivity.SchEndDate != findActivity.SchEndDate ? updateActivity.SchEndDate : findActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findActivity.SchStartDate, (int)updateActivity.Duration - 1, holidays) : findActivity.SchStartDate.Value.AddDays((int)updateActivity.Duration - 1);
                        var plusminusdaysend = findActivity.ActualEndDate != null ? findActivity.ActualEndDate.Value : findActivity.SchEndDate.Value;
                        findActivity.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(findActivity.BaseEndDate.Value, plusminusdaysend, holidays);

                        //update the whole schedule
                        var listActivities = new List<ScheduleSactivity>();
                        var findSuccessorActivities = _context.ScheduleSactivityPreds.Include("ScheduleA").Where(x => findSactivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();

                        //CalculateAdjustDates(findActivity, findSuccessorActivities, holidays, returnList);//trying new version
                        //returnList.Add(findActivity);

                        var returnList = RecalculateDates(findSuccessorActivities, findActivity, holidays, false);

                        updateActivities = _context.ScheduleSactivities.Where(x => returnList.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                        foreach (var activity in updateActivities)
                        {
                            activity.SchStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
                            activity.SchEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
                            var plusminusdaysend1 = activity.ActualEndDate != null ? activity.ActualEndDate.Value : activity.SchEndDate.Value;
                            activity.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(activity.BaseEndDate.Value, plusminusdaysend1, holidays);
                            activity.UpdatedBy = updateBy;
                            activity.UpdateDate = DateTime.Now;
                            activity.UpdatedDateTime = DateTime.Now;
                        }
                    }
                    if (findActivity.Duration != updateActivity.Duration && findActivity.SchStartDate != null && updateActivity.Duration != null)
                    {
                        findActivity.Duration = updateActivity.Duration;
                        findActivity.SchEndDate = findActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findActivity.SchStartDate, (int)updateActivity.Duration - 1, holidays) : findActivity.SchStartDate.Value.AddDays((int)updateActivity.Duration - 1);
                        var plusminusdaysend = findActivity.ActualEndDate != null ? findActivity.ActualEndDate.Value : findActivity.SchEndDate.Value;
                        findActivity.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(findActivity.BaseEndDate.Value, plusminusdaysend, holidays);

                        //update the whole schedule
                        var listActivities = new List<ScheduleSactivity>();
                        var findSuccessorActivities = _context.ScheduleSactivityPreds.Include("ScheduleA").Where(x => findSactivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                        //CalculateAdjustDates(findActivity, findSuccessorActivities, holidays, returnList);//trying new version 

                        var returnList = RecalculateDates(findSuccessorActivities, findActivity, holidays, false);

                        updateActivities = _context.ScheduleSactivities.Where(x => returnList.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                        foreach (var activity in updateActivities)
                        {
                            activity.SchStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
                            activity.SchEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
                            var plusminusdaysend1 = activity.ActualEndDate != null ? activity.ActualEndDate.Value : activity.SchEndDate.Value;
                            activity.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(activity.BaseEndDate.Value, plusminusdaysend1, holidays);
                            activity.UpdatedBy = updateBy;
                            activity.UpdateDate = DateTime.Now;
                            activity.UpdatedDateTime = DateTime.Now;
                        }
                    }
                    if (findActivity.LagTime != updateActivity.LagTime)
                    {
                        findActivity.LagTime = updateActivity.LagTime;
                        var findPredecessors = findActivity.ScheduleSactivityPreds.ToList();
                        if (findPredecessors.Any())
                        {
                            var findMaxPredecessorEndDate = _context.ScheduleSactivities.Where(x => findPredecessors.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).Max(x => x.SchEndDate);
                            //TODO: don't update these if actual date is entered
                            findActivity.SchStartDate = findActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findMaxPredecessorEndDate, (int)findActivity.LagTime + 1, holidays) : findMaxPredecessorEndDate.Value.AddDays((int)findActivity.LagTime + 1);
                            findActivity.SchEndDate = findActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findActivity.SchStartDate, (int)updateActivity.Duration - 1, holidays) : findActivity.SchStartDate.Value.AddDays((int)updateActivity.Duration - 1);
                        }

                        var plusminusdaysend = findActivity.ActualEndDate != null ? findActivity.ActualEndDate.Value : findActivity.SchEndDate.Value;
                        findActivity.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(findActivity.BaseEndDate.Value, plusminusdaysend, holidays);
                        //update the whole schedule
                        var listActivities = new List<ScheduleSactivity>();
                        var findSuccessorActivities = _context.ScheduleSactivityPreds.Include("ScheduleA").Where(x => findSactivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                        //CalculateAdjustDates(findActivity, findSuccessorActivities, holidays, returnList);

                        var returnList = RecalculateDates(findSuccessorActivities, findActivity, holidays, false);

                        updateActivities = _context.ScheduleSactivities.Where(x => returnList.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                        foreach (var activity in updateActivities)
                        {
                            activity.SchStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
                            activity.SchEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
                            var plusminusdaysend1 = activity.ActualEndDate != null ? activity.ActualEndDate.Value : activity.SchEndDate.Value;
                            activity.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(activity.BaseEndDate.Value, plusminusdaysend1, holidays);
                            activity.UpdatedBy = updateBy;
                            activity.UpdateDate = DateTime.Now;
                            activity.UpdatedDateTime = DateTime.Now;
                        }
                    }

                    var sessionId = Guid.NewGuid();
                    var result = await InsertTempSactivites(updateActivities, sessionId);
                    //update the real ones from the temp table
                    var conn = _configuration.GetConnectionString("ERPConnection");
                    using (var connection = new SqlConnection(conn))
                    {
                        connection.Open();
                        var query = $"Update  c SET  c.SCH_START_DATE = c2.SCH_START_DATE, c.SCH_END_DATE = c2.SCH_END_DATE, c.BASE_START_DATE = c2.BASE_START_DATE, c.BASE_END_DATE = c2.BASE_END_DATE, c.PLUSMINUS_DAYS = c2.PLUSMINUS_DAYS, c.UpdatedBy = c2.CreatedBy, c.UpdatedDateTime = c2.UpdatedDateTime FROM xxxSCHEDULE_SACTIVITY c2 INNER JOIN SCHEDULE_SACTIVITY c on c.SCHEDULE_AID = c2.SCHEDULE_AID where c2.SessionId = '{sessionId}'";

                        var command = new SqlCommand(query, connection);
                        await command.ExecuteNonQueryAsync();
                        //truncate the temp table
                        var deleteQuery = $"DELETE FROM xxxSCHEDULE_SACTIVITY where SessionId = '{sessionId}'";
                        var deleteCommand = new SqlCommand(deleteQuery, connection);
                        await deleteCommand.ExecuteNonQueryAsync();
                    }

                    //update the milestones dates and durations
                    var findAllMilestones = _context.ScheduleMilestones.Include(x => x.ScheduleSactivities).Where(x => x.ScheduleId == findActivity.ScheduleId && x.IsActive == true).ToList();
                    foreach (var milestone in findAllMilestones)
                    {
                        var actualstart = milestone.ScheduleSactivities.Min(x => x.ActualStartDate);
                        var actualend = milestone.ScheduleSactivities.Any(x => x.ActualEndDate == null) ? null : milestone.ScheduleSactivities.Max(x => x.ActualEndDate);
                        var schstart = milestone.ScheduleSactivities.Min(x => x.SchStartDate);
                        var schend = milestone.ScheduleSactivities.Max(x => x.SchEndDate);

                        milestone.ActualDuration = (actualstart != null && actualend != null) ? CalendarExtension.WorkingDaysDuration(actualstart.Value, actualend.Value, holidays) : null;
                        milestone.Duration = CalendarExtension.WorkingDaysDuration(schstart.Value, schend.Value, holidays);
                        milestone.SchStartDate = schstart;
                        milestone.SchEndDate = schend;
                        milestone.BaseStartDate = milestone.ScheduleSactivities.Min(x => x.BaseStartDate);
                        milestone.BaseEndDate = milestone.ScheduleSactivities.Max(x => x.BaseEndDate);
                        milestone.ActualStartDate = actualstart;
                        milestone.ActualEndDate = actualend;
                        milestone.UpdatedBy = updateBy;
                        milestone.UpdatedDateTime = DateTime.Now;
                    }
                    _context.ScheduleMilestones.UpdateRange(findAllMilestones);
                    await _context.SaveChangesAsync();

                    var schedule = _context.Schedules.SingleOrDefault(x => x.ScheduleId == updateActivity.ScheduleM.ScheduleId);
                    schedule.BaseStartDate = findAllMilestones.Min(x => x.BaseStartDate);
                    schedule.BaseEndDate = findAllMilestones.Max(x => x.BaseEndDate);
                    schedule.BaseDuration = CalendarExtension.WorkingDaysDuration(schedule.BaseStartDate.Value, schedule.BaseEndDate.Value, holidays);
                    schedule.BaseCalduration = (schedule.BaseEndDate.Value - schedule.BaseStartDate.Value).Days;
                    schedule.DateToStart = findAllMilestones.Min(x => x.SchStartDate);
                    schedule.DateToEnd = findAllMilestones.Max(x => x.SchEndDate);
                    schedule.UpdatedBy = updateBy;
                    schedule.UpdatedDateTime = DateTime.Now;
                    _context.Schedules.Update(schedule);
                    await _context.SaveChangesAsync();
                }


                //update the other non-date related fields
                findActivity.SubNumber = updateActivity.SubNumber;
                findActivity.Note = updateActivity.Note;
                findActivity.SupplierNote = updateActivity.SupplierNote;
                findActivity.VarianceCode = updateActivity.VarianceCode;
                findActivity.IsLocked = updateActivity.BoolIgnoreNoWorkDays ? "T" : "F";
                findActivity.UpdatedBy = User.Identity.Name.Split('@')[0];
                findActivity.UpdatedDateTime = DateTime.Now;
                _context.ScheduleSactivities.Update(findActivity);
                await _context.SaveChangesAsync();

                if (newlyComplete)
                {
                    //mark po approved for payment
                    var findLink = _context.ScheduleSactivityLinks.Where(x => x.ScheduleAid == updateActivity.ScheduleAid && x.IsActive == true).FirstOrDefault();
                    if (findLink != null)
                    {
                        // var findEstActivity = _context.Estactivities.FirstOrDefault(x => x.EstactivityId == findLink.EstactivityId);
                        var findHeaders = _context.Estdetails.Include(x => x.Podetail).ThenInclude(x => x.Poheader.PojobnumberNavigation).Where(x => x.EstactivityId == findLink.EstactivityId).Select(x => x.Podetail.Poheader).Distinct().ToList();
                        foreach (var updateHeader in findHeaders)
                        {
                            if (updateHeader != null && updateHeader.Postatus != 4 && updateHeader.Podatecancelled == null && updateHeader.Postatus != 5)
                            {
                                //don't approve if already approved
                                var findApproval = _context.Poapprovals.Where(x => x.PoheaderId == updateHeader.PoheaderId);
                                if (!findApproval.Any())
                                {
                                    // var getNextApprovalSeq = _context.Poapprovals.Where(x => x.PoheaderId == updateHeader.PoheaderId).Max(x => x.Approvalseq) == null ? 1 : _context.Poapprovals.Where(x => x.PoheaderId == updateHeader.PoheaderId).Max(x => x.Approvalseq) + 1;
                                    var addApproval = new Poapproval()
                                    {
                                        PoheaderId = updateHeader.PoheaderId,
                                        Invdate = DateTime.Now,
                                        Approvedby = updateBy,
                                        CreatedBy = updateBy,
                                        Invnetamount = updateHeader.Pototal,
                                        Invtaxamount = updateHeader.Taxamount,
                                        Invdescription = $"{updateHeader.Podescription} - {updateHeader.PojobnumberNavigation.JobAddress1}",
                                        Invnumber = updateHeader.Ponumber,
                                        Approvalseq = 1
                                    };
                                    _context.Poapprovals.Add(addApproval);

                                    //TODO: this needs to update the jcc?
                                    updateHeader.Postatus = 4;
                                    updateHeader.UpdatedBy = updateBy;
                                    updateHeader.Approvedby = updateBy;
                                    updateHeader.Podateapproved = DateTime.Now;
                                    updateHeader.TaskCompleteBy = updateBy;
                                    updateHeader.TaskCompleteDate = DateTime.Now;
                                    updateHeader.PendInvDescrip = $"{updateHeader.Podescription} - {updateHeader.PojobnumberNavigation.JobAddress1}";
                                    updateHeader.PendInvDate = DateTime.Now;
                                    updateHeader.PendInvAmount = updateHeader.Pototal;
                                    updateHeader.PendInvNumber = addApproval.Invnumber;
                                    _context.Poheaders.Update(updateHeader);
                                    await _context.SaveChangesAsync();
                                }
                            }
                        }
                    }
                }
                return Ok(new ResponseModel<ScheduleSactivityDto>() { Value = updateActivity, IsSuccess = true, Message = "Schedule updated" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheduleSactivityDto> { IsSuccess = false, Message = "Failed to update Schedule SActivity" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateScheduleSActivitysAsync([FromBody] List<ScheduleSactivityDto> updateActivities)
        {
            try
            {
                //NOTE: this is assuming all the update Activities come from the same schedul
                //This is for a Save function that saves all the updates in the schedule at once 
                //TODO: if they changed predecessors, update that? or is that not allowed
                //TODO: only update the ones that changed, don't allow update something lready complete
                if (updateActivities != null && updateActivities.Count > 0)
                {
                    string message = "Updated schedule.";
                    var updateBy = User.Identity.Name.Split('@')[0];
                    var newPres = updateActivities.Where(x => x.Predecessors != null).ToList();
                    // var findActivities = await _context.ScheduleSactivities.Include(x => x.SubNumberNavigation.SupplierContacts).ThenInclude(x => x.Contact).Include(x => x.ScheduleSactivityPreds).Include(x => x.Schedule).Where(x => updateActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid) && x.IsActive == true).ToListAsync();
                    var findActivities = await _context.ScheduleSactivities.Include(x => x.Sactivity).Include(x => x.SubNumberNavigation).Include(x => x.ScheduleSactivityPreds).Include(x => x.Schedule).Where(x => updateActivities.Select(y => y.ScheduleAid).Contains(x.ScheduleAid) && x.IsActive == true).ToListAsync();
                    var updatedActivitesForEmail = new List<UpdateActivity>();
                    foreach (var findActivity in findActivities)
                    {
                        var updateActivity = updateActivities.FirstOrDefault(x => x.ScheduleAid == findActivity.ScheduleAid);
                        var subName = findActivity.SubNumberNavigation?.SubName;
                        if (findActivity.SubNumber != null && findActivity.Schedule.Published == "T" && (findActivity.SchStartDate != updateActivity.SchStartDate || findActivity.SchEndDate != updateActivity.SchEndDate || findActivity.SubNumber != updateActivity.SubNumber))
                        {
                            if (findActivity.SubNumber != updateActivity.SubNumber)
                            {
                                subName = _context.Suppliers.FirstOrDefault(x => x.SubNumber == updateActivity.SubNumber)?.SubName;
                            }

                            updatedActivitesForEmail.Add(new UpdateActivity
                            {
                                SubNumber = updateActivity.SubNumber != null ? (int)updateActivity.SubNumber : null,
                                SubName = subName,
                                StartDate = updateActivity.SchStartDate?.ToString("MM/dd/yyyy"),
                                EndDate = updateActivity.SchEndDate?.ToString("MM/dd/yyyy"),
                                ActivityName = findActivity.Sactivity.ActivityName,
                                JobNumber = findActivity.Schedule.JobNumber
                            });
                        }

                        updateActivity.NewlyComplete = updateActivity.BoolComplete == true && findActivity.ActualEndDate == null;
                        findActivity.Complete = updateActivity.BoolComplete ? "T" : "F";
                        findActivity.CompletedBy = findActivity.CompletedBy == null ? updateActivity.BoolComplete ? updateBy : null : findActivity.CompletedBy;
                        findActivity.IsLocked = updateActivity.BoolIgnoreNoWorkDays ? "T" : "F";
                        findActivity.SchStartDate = updateActivity.SchStartDate;
                        findActivity.SchEndDate = updateActivity.SchEndDate;
                        findActivity.BaseStartDate = updateActivity.BaseStartDate;
                        findActivity.BaseEndDate = updateActivity.BaseEndDate;
                        findActivity.ActualStartDate = updateActivity.ActualStartDate;
                        findActivity.ActualEndDate = updateActivity.ActualEndDate;
                        findActivity.ActualDuration = updateActivity.ActualDuration;
                        findActivity.Duration = updateActivity.Duration;
                        findActivity.LagTime = updateActivity.LagTime;
                        findActivity.Note = updateActivity.Note;
                        findActivity.SupplierNote = updateActivity.SupplierNote;
                        findActivity.VarianceCode = updateActivity.VarianceCode;
                        findActivity.PlusminusDays = updateActivity.PlusminusDays;
                        findActivity.SubNumber = updateActivity.SubNumber;//TODO: check this. Be careful
                        findActivity.SubNumberNavigation = _mapper.Map<Supplier>(updateActivity.SubNumberNavigation);
                        findActivity.UpdatedBy = User.Identity.Name.Split('@')[0];
                        findActivity.UpdatedDateTime = DateTime.Now;

                        bool changed = updateActivity.Predecessors != null ? !findActivity.ScheduleSactivityPreds.Select(x => x.PredSactivityId).Order().ToList().SequenceEqual(updateActivity.Predecessors.Select(x => x.PredSactivityId).Order()) : false;
                        if (changed)
                        {
                            //deactivate any old preds
                            foreach (var findPred in findActivity.ScheduleSactivityPreds.ToList())
                            {
                                findPred.IsActive = false;
                                findPred.UpdatedBy = User.Identity.Name.Split('@')[0];
                                findPred.UpdatedDateTime = DateTime.Now;
                            }
                            await _context.ScheduleSactivityPreds.BulkUpdateAsync(findActivity.ScheduleSactivityPreds);

                            //if any new preds existed already, reactivate them
                            var reactivatePreds = findActivity.ScheduleSactivityPreds.Where(x => updateActivity.Predecessors.Select(y => y.PredSactivityId).Contains(x.PredSactivityId)).ToList();
                            foreach (var reactivatePred in reactivatePreds)
                            {
                                reactivatePred.IsActive = true;
                                reactivatePred.UpdatedBy = User.Identity.Name.Split('@')[0];
                                reactivatePred.UpdatedDateTime = DateTime.Now;
                            }
                            await _context.ScheduleSactivityPreds.BulkUpdateAsync(reactivatePreds);
                            //insert new preds
                            var newPreds = updateActivity.Predecessors.Where(x => !findActivity.ScheduleSactivityPreds.Select(y => y.PredSactivityId).Contains(x.PredSactivityId)).Select(x => new ScheduleSactivityPred()
                            {
                                ScheduleAid = x.ScheduleAid,
                                PredSactivityId = x.PredSactivityId,
                                PredScheduleAid = x.PredScheduleAid,
                                CreatedBy = User.Identity.Name.Split('@')[0],
                                CreatedDateTime = DateTime.Now,
                                IsActive = true
                            }).ToList();
                            await _context.ScheduleSactivityPreds.BulkInsertAsync(newPreds);
                        }
                    }

                    //approve for payment
                    var newlyComplete = updateActivities.Where(x => x.NewlyComplete == true);
                    if (newlyComplete.Any())
                    {
                        //var findBlockedVendors = _context.Suppliers.Where(x => x.Blocked == true).Select(x => x.SubNumber).Distinct().ToList();
                        //var findInsuranceExpiredVendors = _context.SupplierInsurances.Where(x => x.InsuranceRequired == 1 && x.PolicyExpirationDate.Date < DateTime.Now.Date && x.IsActive == true).Select(x => x.SubNumber).Distinct().ToList();
                        foreach (var updateActivity in newlyComplete)
                        {

                            var findLinks = _context.ScheduleSactivityLinks.Where(x => x.ScheduleAid == updateActivity.ScheduleAid && x.IsActive == true).ToList();
                            if (findLinks != null && findLinks.Count != 0)
                            {
                                foreach (var findLink in findLinks)
                                {
                                    //var findEstActivity = _context.Estactivities.FirstOrDefault(x => x.EstactivityId == findLink.EstactivityId);
                                    var findHeaders = _context.Estdetails.Include(x => x.Podetail).ThenInclude(x => x.Poheader.PojobnumberNavigation).Where(x => x.EstactivityId == findLink.EstactivityId && x.PodetailId != null).Select(x => x.Podetail.Poheader).Distinct().ToList();

                                    foreach (var updateHeader in findHeaders.Where(x => x != null))
                                    {
                                        //Per Kevin 11/14 (see Tara's email) allow mark po complete and approve for payment even if insurance expired
                                        //if (updateActivity.SubNumber == null || findBlockedVendors.Contains((int)updateActivity.SubNumber) || findInsuranceExpiredVendors.Contains(updateActivity.SubNumber))
                                        //{
                                        //    if (updateActivity.SubNumber != null)
                                        //    {
                                        //        message += $"PO for supplier: {updateActivity.SubNumberNavigation.SubName} could not be approved due to supplier blocked or insurance expired.";
                                        //        string emailMessage = $"Construction activity was completed, but the PO {updateHeader.Ponumber} for supplier: {updateActivity.SubNumberNavigation.SubName} could not be approved due to supplier blocked or insurance expired. Please fix insurance or blocked in NAV/BC, then approve the PO in ERP.";
                                        //        _email.SendEmail("Supplier insurance expired or supplier blocked", emailMessage, new List<string>() { "<EMAIL>", "<EMAIL>" });//Send Julie an email so she can fix supplier issue and approve payment. This should not happen often
                                        //        continue;
                                        //    }
                                        //}
                                        if (updateHeader.Pototal == 0)
                                        {
                                            continue;//no need to approve a 0
                                        }
                                        if (updateHeader != null && updateHeader.Postatus != 4 && updateHeader.Postatus != 5 && updateHeader.Podatecancelled == null)
                                        {
                                            //don't approve if already approved
                                            var findApproval = _context.Poapprovals.Where(x => x.PoheaderId == updateHeader.PoheaderId);
                                            //var getNextApprovalSeq = _context.Poapprovals.Where(x => x.PoheaderId == updateHeader.PoheaderId).Max(x => x.Approvalseq) == null ? 1 : _context.Poapprovals.Where(x => x.PoheaderId == updateHeader.PoheaderId).Max(x => x.Approvalseq) + 1;
                                            if (!findApproval.Any())
                                            {
                                                var addApproval = new Poapproval()
                                                {
                                                    PoheaderId = updateHeader.PoheaderId,
                                                    Invdate = DateTime.Now,
                                                    Approvedby = updateBy,
                                                    CreatedBy = updateBy,
                                                    Invnetamount = updateHeader.Pototal,
                                                    Invtaxamount = updateHeader.Taxamount,
                                                    Invdescription = $"{updateHeader.Podescription} - {updateHeader.PojobnumberNavigation.JobAddress1}",
                                                    Invnumber = updateHeader.Ponumber,
                                                    Approvalseq = 1
                                                };
                                                await _context.Poapprovals.BulkInsertAsync(new List<Poapproval> { addApproval });
                                                // _context.Poapprovals.Add(addApproval);

                                                //TODO: this needs to update the jcc?

                                                updateHeader.Postatus = 4;
                                                updateHeader.UpdatedBy = updateBy;
                                                updateHeader.Approvedby = updateBy;
                                                updateHeader.Podateapproved = DateTime.Now;
                                                updateHeader.TaskCompleteBy = updateBy;
                                                updateHeader.TaskCompleteDate = DateTime.Now;
                                                updateHeader.PendInvDescrip = $"{updateHeader.Podescription} - {updateHeader.PojobnumberNavigation.JobAddress1}";
                                                updateHeader.PendInvDate = DateTime.Now;
                                                updateHeader.PendInvAmount = updateHeader.Pototal;
                                                updateHeader.PendInvNumber = addApproval.Invnumber;
                                                //_context.Poheaders.Update(updateHeader);
                                                await _context.Poheaders.BulkUpdateAsync(new List<Poheader> { updateHeader });
                                                //await _context.SaveChangesAsync();
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    //save the update
                    await _context.ScheduleSactivities.BulkUpdateAsync(findActivities);

                    var milestonesInSchedele = _context.ScheduleMilestones.Where(x => x.ScheduleId == findActivities.FirstOrDefault().ScheduleId && x.IsActive == true).ToList();
                    var allActivitiesInSchedule = _context.ScheduleSactivities.Where(x => x.ScheduleId == findActivities.FirstOrDefault().ScheduleId).ToList();
                    var findSchedule = _context.Schedules.FirstOrDefault(x => x.ScheduleId == findActivities.FirstOrDefault().ScheduleId);
                    var holidays = _context.CalendarsDays.Where(x => x.IsActive == true && x.CalendarId == 1).Select(x => x.WorkDate).ToList();
                    foreach (var milestone in milestonesInSchedele)
                    {
                        milestone.SchStartDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMid).Min(x => x.SchStartDate);
                        milestone.SchEndDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMid).Max(x => x.SchEndDate);
                        milestone.BaseStartDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMid).Min(x => x.BaseStartDate);
                        milestone.BaseEndDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMid).Max(x => x.BaseEndDate);
                        milestone.ActualStartDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMid).Min(x => x.ActualStartDate);
                        milestone.ActualEndDate = allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMid).All(x => x.ActualEndDate != null) ? allActivitiesInSchedule.Where(x => x.ScheduleMid == milestone.ScheduleMid).Max(x => x.ActualEndDate) : null;
                        milestone.ActualDuration = (milestone.ActualStartDate != null && milestone.ActualEndDate != null) ? CalendarExtension.WorkingDaysDuration(milestone.ActualStartDate.Value, milestone.ActualEndDate.Value, holidays) : null;
                        milestone.Duration = milestone.SchStartDate != null && milestone.SchEndDate != null ? CalendarExtension.WorkingDaysDuration(milestone.SchStartDate.Value, milestone.SchEndDate.Value, holidays) : null;
                        milestone.Calduration = milestone.BaseStartDate != null && milestone.BaseEndDate != null ? (milestone.BaseEndDate.Value - milestone.BaseStartDate.Value).Days : null ;//should this be based on base dates or sch dates or actual dates?
                        DateTime? plusminusdaysend1 = milestone.ActualEndDate != null ? milestone.ActualEndDate.Value : milestone.SchEndDate != null ? milestone.SchEndDate.Value : null;
                        milestone.PlusminusDays = milestone.BaseEndDate != null && plusminusdaysend1 != null ? CalendarExtension.PlusMinusDaysDuration(milestone.BaseEndDate.Value, plusminusdaysend1.Value, holidays) : null;
                    }
                    await _context.ScheduleMilestones.BulkUpdateAsync(milestonesInSchedele, options => options.IncludeGraph = false);

                    findSchedule.BaseStartDate = milestonesInSchedele.Min(x => x.BaseStartDate);
                    findSchedule.BaseEndDate = milestonesInSchedele.Max(x => x.BaseEndDate);
                    findSchedule.DateToStart = milestonesInSchedele.Min(x => x.SchStartDate);
                    findSchedule.DateToEnd = milestonesInSchedele.Max(x => x.SchEndDate);
                    findSchedule.ActualStartDate = milestonesInSchedele.Min(x => x.ActualStartDate);
                    findSchedule.ActualEndDate = milestonesInSchedele.All(x => x.ActualEndDate != null) ? milestonesInSchedele.Max(x => x.ActualEndDate) : null;
                    findSchedule.BaseDuration = findSchedule.BaseStartDate != null && findSchedule.BaseEndDate != null ? CalendarExtension.WorkingDaysDuration(findSchedule.BaseStartDate.Value, findSchedule.BaseEndDate.Value, holidays) : null;
                    findSchedule.BaseCalduration = findSchedule.BaseStartDate != null && findSchedule.BaseEndDate != null ? (findSchedule.BaseEndDate.Value - findSchedule.BaseStartDate.Value).Days : null;
                    DateTime? plusminusdaysendBase = findSchedule.ActualEndDate != null   ? findSchedule.ActualEndDate.Value :  findSchedule.DateToEnd != null? findSchedule.DateToEnd.Value : null;
                    findSchedule.PlusminusDays = findSchedule.BaseEndDate != null && plusminusdaysendBase != null ?  CalendarExtension.PlusMinusDaysDuration(findSchedule.BaseEndDate.Value, plusminusdaysendBase.Value, holidays) : null;
                    await _context.Schedules.BulkUpdateAsync(new List<Schedule>() { findSchedule });

                    //Grouping the messages by supplier, so each supplier only gets one message with a list of activities that are changed
                    var supplierCommunications = new List<SupplierCommunication>();
                    var emailSuppliers = new List<SendGridEmailModel>();
                    foreach (var activitesBySupplier in updatedActivitesForEmail.GroupBy(x => x.SubNumber))
                    {
                        if (activitesBySupplier.First().SubNumber != null)
                        {
                            var getJob = _context.Jobs.AsNoTracking().Include(x => x.Subdivision).FirstOrDefault(x => x.JobNumber == activitesBySupplier.First().JobNumber);
                            var getSuppliersEmails = _context.SupplierContacts.AsNoTracking().Include(x => x.Contact).Where(x => x.SubNumber == activitesBySupplier.Key && (x.SupplierContactSched == "T") && x.IsActive == true).ToList();//admin and scheduling contacts
                            var stringMessage = new StringBuilder();
                            stringMessage.Append("<p><strong>The following activities are updated</strong><p>");
                            foreach (var activity in activitesBySupplier)
                            {
                                stringMessage.Append($"<span style=\"font-size:14px;color:#3B3B39;line-height:1.2em;;font-family:Arial, Helvetica, sans-serif\"><strong> Activity:</strong> {activity.ActivityName} |<strong> Start:</strong> {activity.StartDate} |<strong> End:</strong> {activity.EndDate}</span><br>");
                            }
                            foreach (var contact in getSuppliersEmails)
                            {
                                emailSuppliers.Add(new SendGridEmailModel
                                {
                                    //Email = "<EMAIL>",
                                    SubName = activitesBySupplier.FirstOrDefault()?.SubName,
                                    JobNumber = activitesBySupplier.First().JobNumber,
                                    JobAddress = getJob?.JobAddress1,
                                    LotNumber = getJob?.LotNumber,
                                    SubdivisionName = getJob.Subdivision.SubdivisionName,
                                    Email = contact.Contact.Email,
                                    MessageSubject = $"Schedule Change for Van Metre Job {activitesBySupplier.First().JobNumber}",
                                    Activities = new Activities() { UpdateActivities = activitesBySupplier.Select(x => x).ToList() },
                                    MessageBody = "here is a test",//won't be used
                                });
                            }
                            supplierCommunications.Add(new SupplierCommunication
                            {
                                //SendTo = "<EMAIL>",
                                SendTo = getSuppliersEmails.FirstOrDefault()?.Contact.Email,
                                SendFrom = "<EMAIL>",
                                SubNumber = (int)activitesBySupplier.First().SubNumber,
                                JobNumber = activitesBySupplier.First().JobNumber,
                                MessageSubject = $"Schedule Change for Van Metre Job {activitesBySupplier.First().JobNumber}",
                                Message = stringMessage.ToString(), //TODO: html here?
                                IsActive = true,
                                CreatedBy = User.Identity.Name.Split('@')[0],
                                CreatedDateTime = DateTime.Now
                            });
                        }
                    }
                    if (emailSuppliers.Count > 0 && !findSchedule.JobNumber.Contains("TEST"))
                    {
                        if (_env.IsDevelopment())
                        {
                            foreach (var supplier in emailSuppliers)
                            {
                                supplier.Email = "<EMAIL>";//send only to me in test
                                supplier.MessageSubject = $"TEST {supplier.MessageSubject}";
                            }
                        }
                        await _email.SendMultipleEmail(emailSuppliers);
                    }
                    if (supplierCommunications.Count > 0 && !findSchedule.JobNumber.Contains("TEST"))
                    {
                        await _context.SupplierCommunications.BulkInsertAsync(supplierCommunications);
                    }
                    return new OkObjectResult(new ResponseModel<List<ScheduleSactivityDto>> { Value = updateActivities, IsSuccess = true, Message = message });
                }
                return new OkObjectResult(new ResponseModel<List<ScheduleSactivityDto>> { Value = updateActivities, IsSuccess = false, Message = "No activites to update" });

            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleSactivityDto>> { IsSuccess = false, Message = "Failed to update Schedule SActivities" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateScheduleMilestonesAsync([FromBody] List<ScheduleMilestoneDto> updateMilestones)
        {
            try
            {
                //This would be for a Save function that saves all the updates in the schedule at once 
                var updateBy = User.Identity.Name.Split('@')[0];
                var findMilestones = await _context.ScheduleMilestones.Where(x => updateMilestones.Select(y => y.ScheduleMid).Contains(x.ScheduleMid) && x.IsActive == true).ToListAsync();
                foreach (var findMilestone in findMilestones)
                {
                    var updateMilestone = updateMilestones.FirstOrDefault(x => x.ScheduleMid == findMilestone.ScheduleMid);
                    findMilestone.SchStartDate = updateMilestone.SchStartDate;
                    findMilestone.SchEndDate = updateMilestone.SchEndDate;
                    findMilestone.IniStartDate = updateMilestone.IniStartDate;
                    findMilestone.IniEndDate = updateMilestone.IniEndDate;
                    findMilestone.BaseStartDate = updateMilestone.BaseStartDate;
                    findMilestone.BaseEndDate = updateMilestone.BaseEndDate;
                    findMilestone.ActualStartDate = updateMilestone.ActualStartDate;
                    findMilestone.ActualEndDate = updateMilestone.ActualEndDate;
                    findMilestone.ActualDuration = updateMilestone.ActualDuration;
                    findMilestone.Calduration = updateMilestone.Calduration;
                    findMilestone.PlusminusDays = updateMilestone.PlusminusDays;
                    findMilestone.UpdatedBy = User.Identity.Name.Split('@')[0];
                    findMilestone.UpdatedDateTime = DateTime.Now;
                }
                await _context.ScheduleMilestones.BulkUpdateAsync(findMilestones);
                return new OkObjectResult(new ResponseModel<List<ScheduleMilestoneDto>> { Value = updateMilestones, IsSuccess = true, Message = "Updated Schedule Milestones successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleMilestoneDto>> { IsSuccess = false, Message = "Failed to update Schedule SActivities" });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdateScheduleAsync([FromBody] ScheduleDto updateSchedule)
        {
            try
            {
                //This would be for a Save function that saves all the updates in the schedule at once 
                var updateBy = User.Identity.Name.Split('@')[0];
                var findSchedule = _context.Schedules.FirstOrDefault(x => x.ScheduleId == updateSchedule.ScheduleId);
                findSchedule.PlusminusDays = updateSchedule.PlusminusDays;
                findSchedule.DateToStart = updateSchedule.DateToStart;
                findSchedule.DateToEnd = updateSchedule.DateToEnd;
                findSchedule.BaseStartDate = updateSchedule.BaseStartDate;
                findSchedule.BaseEndDate = updateSchedule.BaseEndDate;
                findSchedule.ActualStartDate = updateSchedule.ActualStartDate;
                findSchedule.ActualEndDate = updateSchedule.ActualEndDate;
                findSchedule.ActualDuration = updateSchedule.ActualDuration;
                findSchedule.UpdatedBy = User.Identity.Name.Split('@')[0];
                findSchedule.UpdatedDateTime = DateTime.Now;
                _context.Schedules.Update(findSchedule);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<ScheduleDto> { Value = updateSchedule, IsSuccess = true, Message = "Updated Schedule successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheduleDto> { IsSuccess = false, Message = "Failed to update Schedule" });
            }
        }

        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> MilestonesForScheduleAsync(string jobNumber)
        {
            try
            {
                var getSchedule = await _context.Schedules.Where(x => x.JobNumber == jobNumber).OrderByDescending(x => x.DateCreated).FirstOrDefaultAsync();
                if (getSchedule != null)
                {
                    var getMilestones = await _context.ScheduleMilestones.Include("Milestone").Where(x => x.ScheduleId == getSchedule.ScheduleId && x.IsActive == true).ToListAsync();
                    var milestonesDto = _mapper.Map<List<ScheduleMilestoneDto>>(getMilestones);
                    var response = new ResponseModel<List<ScheduleMilestoneDto>>() { Value = milestonesDto, IsSuccess = true, Message = "got milestones" };
                    return Ok(response);
                }
                return Ok(new ResponseModel<List<ScheduleMilestoneDto>>() { Value = new List<ScheduleMilestoneDto>(), IsSuccess = true, Message = "got milestones" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleMilestoneDto>> { IsSuccess = false, Message = "Failed to fetch Milestones for Schedule" });
            }
        }

        [HttpGet("{scheduleMid}")]
        public async Task<IActionResult> ActivitiesForScheduleMilestoneAsync(int scheduleMid)
        {
            try
            {
                var getActivities = await _context.ScheduleSactivities.Include(x => x.SubNumberNavigation).Include(x => x.Sactivity).Include(x => x.ScheduleM.Schedule).Include(x => x.ScheduleM.Milestone).Include(x => x.ScheduleSactivityPreds).ThenInclude(x => x.PredSactivity).Where(x => x.ScheduleMid == scheduleMid && x.IsActive == true).Include(x => x.VarianceCodeNavigation).ToListAsync();
                //var activitiesDto = _mapper.Map<List<ScheduleSactivityDto>>(getActivities); 
                var activitiesDto = getActivities.Select(x => new ScheduleSactivityDto()
                {
                    ScheduleMid = x.ScheduleMid,
                    ScheduleAid = x.ScheduleAid,
                    SactivityId = x.SactivityId,
                    Sactivity = _mapper.Map<SactivityDto>(x.Sactivity),
                    ScheduleM = _mapper.Map<ScheduleMilestoneDto>(x.ScheduleM),
                    SubNumber = x.SubNumber,
                    Seq = x.Seq,
                    Duration = x.Duration,
                    LagTime = x.LagTime,
                    BaseStartDate = x.BaseStartDate,
                    BaseEndDate = x.BaseEndDate,
                    SchStartDate = x.SchStartDate,
                    SchEndDate = x.SchEndDate,
                    ActualStartDate = x.ActualStartDate,
                    ActualEndDate = x.ActualEndDate,
                    ActualDuration = x.ActualDuration,
                    Complete = x.Complete,
                    BoolComplete = x.Complete == "T",
                    BoolStarted = x.ActualStartDate != null,
                    IsLocked = x.IsLocked,
                    BoolIgnoreNoWorkDays = x.IsLocked == "T",
                    PlusminusDays = x.PlusminusDays,
                    UpdatedBy = x.UpdatedBy,
                    CreatedBy = x.CreatedBy,
                    Note = x.Note,
                    SupplierNote = x.SupplierNote,
                    VarianceCode = x.VarianceCode,
                    VarianceCodeNavigation = _mapper.Map<VarianceDto>(x.VarianceCodeNavigation),
                    UpdatedDateTime = x.UpdatedDateTime,
                    CreatedDateTime = x.CreatedDateTime,
                    SubNumberNavigation = _mapper.Map<SupplierDto>(x.SubNumberNavigation),
                    Predecessors = _mapper.Map<List<ScheduleSactivityPredDto>>(x.ScheduleSactivityPreds.Where(y => y.IsActive == true)) ?? new List<ScheduleSactivityPredDto>(),
                    PredIds = x.ScheduleSactivityPreds.Select(y => y.PredSactivityId).ToList(),

                }).ToList();
                return Ok(new ResponseModel<List<ScheduleSactivityDto>>() { Value = activitiesDto, IsSuccess = true, Message = "got activities" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleSactivityDto>> { IsSuccess = false, Message = "Failed to fetch activities for Schedule Milestone" });
            }
        }

        [HttpGet("{scheduleId}")]
        public async Task<IActionResult> ActivitiesForScheduleAsync(int scheduleId)
        {
            try
            {
                var getActivities = await _context.ScheduleSactivities.Include(x => x.SubNumberNavigation).Include(x => x.Sactivity).Include(x => x.ScheduleM.Schedule).Include(x => x.ScheduleM.Milestone).Include(x => x.ScheduleSactivityPreds).ThenInclude(x => x.PredSactivity).Where(x => x.ScheduleM.ScheduleId == scheduleId && x.IsActive == true).Include(x => x.VarianceCodeNavigation).ToListAsync();
                //var activitiesDto = _mapper.Map<List<ScheduleSactivityDto>>(getActivities); 
                var activitiesDto = getActivities.Select(x => new ScheduleSactivityDto()
                {
                    ScheduleMid = x.ScheduleMid,
                    ScheduleAid = x.ScheduleAid,
                    SactivityId = x.SactivityId,
                    Sactivity = _mapper.Map<SactivityDto>(x.Sactivity),
                    ScheduleM = _mapper.Map<ScheduleMilestoneDto>(x.ScheduleM),
                    SubNumber = x.SubNumber,
                    Seq = x.Seq,
                    Duration = x.Duration,
                    LagTime = x.LagTime,
                    BaseStartDate = x.BaseStartDate,
                    BaseEndDate = x.BaseEndDate,
                    SchStartDate = x.SchStartDate,
                    SchEndDate = x.SchEndDate,
                    ActualStartDate = x.ActualStartDate,
                    ActualEndDate = x.ActualEndDate,
                    ActualDuration = x.ActualDuration,
                    Complete = x.Complete,
                    BoolComplete = x.Complete == "T",
                    BoolStarted = x.ActualStartDate != null,
                    IsLocked = x.IsLocked,
                    BoolIgnoreNoWorkDays = x.IsLocked == "T",
                    PlusminusDays = x.PlusminusDays,
                    UpdatedBy = x.UpdatedBy,
                    CreatedBy = x.CreatedBy,
                    Note = x.Note,
                    SupplierNote = x.SupplierNote,
                    VarianceCode = x.VarianceCode,
                    VarianceCodeNavigation = _mapper.Map<VarianceDto>(x.VarianceCodeNavigation),
                    UpdatedDateTime = x.UpdatedDateTime,
                    CreatedDateTime = x.CreatedDateTime,
                    SubNumberNavigation = _mapper.Map<SupplierDto>(x.SubNumberNavigation),
                    Predecessors = _mapper.Map<List<ScheduleSactivityPredDto>>(x.ScheduleSactivityPreds.Where(y => y.IsActive == true)) ?? new List<ScheduleSactivityPredDto>(),

                }).ToList();
                return Ok(new ResponseModel<List<ScheduleSactivityDto>>() { Value = activitiesDto, IsSuccess = true, Message = "got activities" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheduleSactivityDto>> { IsSuccess = false, Message = "Failed to fetch activities for Schedule Milestone" });
            }
        }

        private async Task<bool> InsertTempSactivites(IEnumerable<ScheduleSactivity> sactivitiesToInsert, Guid sessionId)
        {
            try
            {

                DataTable ActivitiesDataTable = new DataTable("ScheduleSActivities");
                DataColumn ScheduleAid = new DataColumn("ScheduleAid", typeof(int));
                ActivitiesDataTable.Columns.Add(ScheduleAid);
                DataColumn ScheduleMid = new DataColumn("ScheduleMid", typeof(int));
                ActivitiesDataTable.Columns.Add(ScheduleMid);
                DataColumn ScheduleId = new DataColumn("ScheduleId", typeof(int));
                ActivitiesDataTable.Columns.Add(ScheduleId);
                DataColumn SactivityId = new DataColumn("SactivityId", typeof(int));
                ActivitiesDataTable.Columns.Add(SactivityId);
                DataColumn SubNumber = new DataColumn("SubNumber", typeof(int));
                ActivitiesDataTable.Columns.Add(SubNumber);
                DataColumn Seq = new DataColumn("Seq", typeof(int));
                ActivitiesDataTable.Columns.Add(Seq);
                DataColumn Duration = new DataColumn("Duration", typeof(int));
                ActivitiesDataTable.Columns.Add(Duration);
                DataColumn CalDuration = new DataColumn("CalDuration", typeof(int));
                ActivitiesDataTable.Columns.Add(CalDuration);
                DataColumn LagTime = new DataColumn("LagTime", typeof(int));
                ActivitiesDataTable.Columns.Add(LagTime);
                DataColumn BaseStartDate = new DataColumn("BaseStartDate", typeof(DateTime));
                ActivitiesDataTable.Columns.Add(BaseStartDate);
                DataColumn BaseEndDate = new DataColumn("BaseEndDate", typeof(DateTime));
                ActivitiesDataTable.Columns.Add(BaseEndDate);
                DataColumn SchStartDate = new DataColumn("SchStartDate", typeof(DateTime));
                ActivitiesDataTable.Columns.Add(SchStartDate);
                DataColumn SchEndDate = new DataColumn("SchEndDate", typeof(DateTime));
                ActivitiesDataTable.Columns.Add(SchEndDate);
                DataColumn IniStartDate = new DataColumn("IniStartDate", typeof(DateTime));
                ActivitiesDataTable.Columns.Add(IniStartDate);
                DataColumn IniEndDate = new DataColumn("IniEndDate", typeof(DateTime));
                ActivitiesDataTable.Columns.Add(IniEndDate);
                DataColumn ActualStartDate = new DataColumn("ActualStartDate", typeof(DateTime));
                ActivitiesDataTable.Columns.Add(ActualStartDate);
                DataColumn ActualEndDate = new DataColumn("ActualEndDate", typeof(DateTime));
                ActivitiesDataTable.Columns.Add(ActualEndDate);
                DataColumn Complete = new DataColumn("Complete", typeof(string));
                ActivitiesDataTable.Columns.Add(Complete);
                DataColumn PlusMinusDays = new DataColumn("PlusMinusDays", typeof(int));
                ActivitiesDataTable.Columns.Add(PlusMinusDays);
                DataColumn UpdatedBy = new DataColumn("UpdatedBy", typeof(string));
                ActivitiesDataTable.Columns.Add(UpdatedBy);
                DataColumn UpdatedDateTime = new DataColumn("UpdatedDateTime", typeof(DateTime));
                ActivitiesDataTable.Columns.Add(UpdatedDateTime);
                DataColumn CreatedDateTime = new DataColumn("CreatedDateTime", typeof(DateTime));
                ActivitiesDataTable.Columns.Add(CreatedDateTime);
                DataColumn IsActive = new DataColumn("IsActive", typeof(bool));
                ActivitiesDataTable.Columns.Add(IsActive);
                DataColumn SessionId = new DataColumn("SessionId", typeof(string));
                ActivitiesDataTable.Columns.Add(SessionId);
                string createBy = User.Identity.Name.Split('@')[0];
                foreach (var activity in sactivitiesToInsert)
                {
                    ActivitiesDataTable.Rows.Add(activity.ScheduleAid, activity.ScheduleMid, activity.ScheduleId, activity.SactivityId, activity.SubNumber, activity.Seq, activity.Duration, activity.Calduration, activity.LagTime, activity.BaseStartDate, activity.BaseEndDate, activity.SchStartDate, activity.SchEndDate, activity.IniStartDate, activity.IniEndDate, activity.ActualStartDate, activity.ActualEndDate, activity.Complete, activity.PlusminusDays, createBy, DateTime.Now, DateTime.Now, true, sessionId.ToString());
                }

                //do the bulk insert 
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    using (var bulkCopy = new SqlBulkCopy(connection))
                    {
                        bulkCopy.DestinationTableName = "xxxSCHEDULE_SACTIVITY";
                        bulkCopy.ColumnMappings.Add("ScheduleAid", "SCHEDULE_AID");
                        bulkCopy.ColumnMappings.Add("ScheduleMid", "SCHEDULE_MID");
                        bulkCopy.ColumnMappings.Add("ScheduleId", "SCHEDULE_ID");
                        bulkCopy.ColumnMappings.Add("SactivityId", "SACTIVITY_ID");
                        bulkCopy.ColumnMappings.Add("SubNumber", "SUB_NUMBER");
                        bulkCopy.ColumnMappings.Add("Seq", "SEQ");
                        bulkCopy.ColumnMappings.Add("Duration", "DURATION");
                        bulkCopy.ColumnMappings.Add("CalDuration", "CALDURATION");
                        bulkCopy.ColumnMappings.Add("LagTime", "LAG_TIME");
                        bulkCopy.ColumnMappings.Add("BaseStartDate", "BASE_START_DATE");
                        bulkCopy.ColumnMappings.Add("BaseEndDate", "BASE_END_DATE");
                        bulkCopy.ColumnMappings.Add("IniStartDate", "INI_START_DATE");
                        bulkCopy.ColumnMappings.Add("IniEndDate", "INI_END_DATE");
                        bulkCopy.ColumnMappings.Add("SchStartDate", "SCH_START_DATE");
                        bulkCopy.ColumnMappings.Add("SchEndDate", "SCH_END_DATE");
                        bulkCopy.ColumnMappings.Add("ActualStartDate", "ACTUAL_START_DATE");
                        bulkCopy.ColumnMappings.Add("ActualEndDate", "ACTUAL_END_DATE");
                        bulkCopy.ColumnMappings.Add("PlusMinusDays", "PLUSMINUS_DAYS");
                        bulkCopy.ColumnMappings.Add("Complete", "COMPLETE");
                        bulkCopy.ColumnMappings.Add("UpdatedBy", "UpdatedBy");
                        bulkCopy.ColumnMappings.Add("UpdatedDateTime", "UpdatedDateTime");
                        bulkCopy.ColumnMappings.Add("CreatedDateTime", "CreatedDateTime");
                        bulkCopy.ColumnMappings.Add("IsActive", "IsActive");
                        bulkCopy.ColumnMappings.Add("SessionId", "SessionId");
                        try
                        {
                            bulkCopy.WriteToServer(ActivitiesDataTable);
                        }
                        catch (Exception ex)
                        {
                            var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                            _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                var test = ex.Message;
                return false;
            }
        }

        [HttpGet]
        public async Task<IActionResult> VarianceCodesAsync()
        {
            try
            {
                var varianceCodes = await _context.Variances.Where(x => x.IsActive == true).OrderBy(x => x.VarianceCode).ToListAsync();
                var codesDto = _mapper.Map<List<VarianceDto>>(varianceCodes);
                return new OkObjectResult(new ResponseModel<List<VarianceDto>> { Value = codesDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<VarianceDto>> { IsSuccess = false, Message = "Failed to get Variance Codes", Value = new List<VarianceDto>() });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddVarianceCodeAsync([FromBody] VarianceDto codeToAdd)
        {
            try
            {
                var checkIfExisting = await _context.Variances.SingleOrDefaultAsync(x => x.VarianceCode == codeToAdd.VarianceCode.ToUpper() && x.IsActive == true);
                var checkIfExistingAndDeleted = await _context.Variances.SingleOrDefaultAsync(x => x.VarianceCode == codeToAdd.VarianceCode.ToUpper() && x.IsActive == false);
                if (checkIfExisting != null)
                {
                    return new OkObjectResult(new ResponseModel<VarianceDto> { Value = codeToAdd, IsSuccess = true, Message = "Code already exists" });
                }
                else if (checkIfExistingAndDeleted != null)
                {
                    checkIfExistingAndDeleted.VarianceDesc = codeToAdd.VarianceDesc;
                    checkIfExistingAndDeleted.IsActive = true;
                    checkIfExistingAndDeleted.UpdatedBy = User.Identity.Name.Split('@')[0];
                    checkIfExistingAndDeleted.UpdatedDateTime = DateTime.Now;
                    _context.Variances.Update(checkIfExistingAndDeleted);
                }
                else
                {
                    var newVarianceCode = new Variance();
                    newVarianceCode.VarianceCode = codeToAdd.VarianceCode.ToUpper();
                    newVarianceCode.VarianceDesc = codeToAdd.VarianceDesc;
                    newVarianceCode.IsActive = true;
                    newVarianceCode.CreatedBy = User.Identity.Name.Split('@')[0];
                    newVarianceCode.CreatedDateTime = DateTime.Now;
                    _context.Variances.Add(newVarianceCode);
                }

                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<VarianceDto> { Value = codeToAdd, IsSuccess = true, Message = "Variance code added successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<VarianceDto> { IsSuccess = false, Message = "Failed to Add Variance Code", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateVarianceCodeAsync([FromBody] VarianceDto codeToUpdate)
        {
            try
            {
                var varianceCode = await _context.Variances.SingleOrDefaultAsync(x => x.VarianceCode == codeToUpdate.VarianceCode.ToUpper());
                if (varianceCode != null)
                {
                    varianceCode.VarianceDesc = codeToUpdate.VarianceDesc;
                    varianceCode.IsActive = true;
                    varianceCode.UpdatedBy = User.Identity.Name.Split('@')[0];
                    varianceCode.UpdatedDateTime = DateTime.Now;
                    _context.Variances.Update(varianceCode);
                    await _context.SaveChangesAsync();
                }

                return new OkObjectResult(new ResponseModel<VarianceDto> { Value = codeToUpdate, IsSuccess = true, Message = "Variance code updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<VarianceDto> { IsSuccess = false, Message = "Failed to Update Variance Code", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteVarianceCodeAsync([FromBody] VarianceDto codeToDelete)
        {
            try
            {
                var varianceCode = await _context.Variances.SingleOrDefaultAsync(x => x.VarianceCode == codeToDelete.VarianceCode.ToUpper());
                if (varianceCode != null)
                {
                    varianceCode.IsActive = false;
                    varianceCode.UpdatedBy = User.Identity.Name.Split('@')[0];
                    varianceCode.UpdatedDateTime = DateTime.Now;
                    _context.Variances.Update(varianceCode);
                    await _context.SaveChangesAsync();
                }

                return new OkObjectResult(new ResponseModel<VarianceDto> { Value = codeToDelete, IsSuccess = true, Message = "Variance code deleted successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<VarianceDto> { IsSuccess = false, Message = "Failed to Delete Variance Code", Value = null });
            }
        }

        [HttpGet("{subdivisionId}")]
        public async Task<IActionResult> PackageNamesBySubdivisionIdAsync(int subdivisionId)
        {
            try
            {
                var packageNames = await _context.ScheStartPackages.Where(x => x.SubdivisionId == subdivisionId && x.IsActive == true).ToListAsync();
                var packageNamesDto = _mapper.Map<List<ScheStartPackageDto>>(packageNames);
                return new OkObjectResult(new ResponseModel<List<ScheStartPackageDto>> { Value = packageNamesDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheStartPackageDto>> { IsSuccess = false, Message = "Failed to get Package Names by Subdivision", Value = new List<ScheStartPackageDto>() });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddPackageNameAsync([FromBody] ScheStartPackageDto packageNametoAdd)
        {
            try
            {
                var checkIfExisting = await _context.ScheStartPackages.SingleOrDefaultAsync(x => x.PackageName.ToLower() == packageNametoAdd.PackageName.ToLower());
                if (checkIfExisting != null)
                {
                    checkIfExisting.IsActive = true;
                    checkIfExisting.UpdatedDateTime = DateTime.Now;
                    checkIfExisting.UpdatedBy = User.Identity.Name.Split('@')[0];
                }
                else
                {
                    var newPackageName = new ScheStartPackage();
                    newPackageName.PackageName = packageNametoAdd.PackageName;
                    newPackageName.SubdivisionId = packageNametoAdd.SubdivisionId;
                    newPackageName.IsActive = true;
                    newPackageName.CreatedBy = User.Identity.Name.Split('@')[0];
                    newPackageName.CreatedDateTime = DateTime.Now;
                    _context.ScheStartPackages.Add(newPackageName);
                }
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<ScheStartPackageDto> { Value = packageNametoAdd, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheStartPackageDto> { IsSuccess = false, Message = "Failed to Add Package Name", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdatePackageNameAsync([FromBody] ScheStartPackageDto packageToUpdate)
        {
            try
            {
                var packageName = await _context.ScheStartPackages.SingleOrDefaultAsync(x => x.PackageId == packageToUpdate.PackageId);
                if (packageName != null)
                {
                    packageName.PackageName = packageToUpdate.PackageName;
                    packageName.IsActive = true;
                    packageName.UpdatedBy = User.Identity.Name.Split('@')[0];
                    packageName.UpdatedDateTime = DateTime.Now;
                    _context.ScheStartPackages.Update(packageName);
                    await _context.SaveChangesAsync();
                }

                return new OkObjectResult(new ResponseModel<ScheStartPackageDto> { Value = packageToUpdate, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheStartPackageDto> { IsSuccess = false, Message = "Failed to Update Package Name", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeletePackageNameAsync([FromBody] ScheStartPackageDto packageNameToDelete)
        {
            try
            {
                var packageName = await _context.ScheStartPackages.SingleOrDefaultAsync(x => x.PackageId == packageNameToDelete.PackageId);
                if (packageName != null)
                {
                    packageName.IsActive = false;
                    packageName.UpdatedBy = User.Identity.Name.Split('@')[0];
                    packageName.UpdatedDateTime = DateTime.Now;
                    _context.ScheStartPackages.Update(packageName);
                    await _context.SaveChangesAsync();
                }

                return new OkObjectResult(new ResponseModel<ScheStartPackageDto> { Value = packageNameToDelete, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheStartPackageDto> { IsSuccess = false, Message = "Failed to Delete Package Name", Value = null });
            }
        }

        [HttpGet("{packageId}")]
        public async Task<IActionResult> PackageItemsAsync(int packageId)
        {
            try
            {
                var packageItems = await _context.SchePackageItems.Where(x => x.PackageId == packageId && x.IsActive == true).Include("SchePackageAssignments").ToListAsync();
                var packageItemsDto = _mapper.Map<List<SchePackageItemDto>>(packageItems);
                return new OkObjectResult(new ResponseModel<List<SchePackageItemDto>> { Value = packageItemsDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SchePackageItemDto>> { IsSuccess = false, Message = "Failed to get Package Items", Value = new List<SchePackageItemDto>() });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddPackageItemAsync([FromBody] SchePackageItemDto packageItemtoAdd)
        {
            try
            {
                var checkIfExisting = await _context.SchePackageItems.SingleOrDefaultAsync(x => x.ItemName.ToLower() == packageItemtoAdd.ItemName.ToLower() && x.PackageId == packageItemtoAdd.PackageId);
                if (checkIfExisting != null)
                {
                    packageItemtoAdd.PackageItemId = checkIfExisting.PackageItemId;
                    checkIfExisting.IsActive = true;
                    checkIfExisting.UpdatedDateTime = DateTime.Now;
                    checkIfExisting.UpdatedBy = User.Identity.Name.Split('@')[0];
                }
                else
                {
                    var newPackageItem = new SchePackageItem();
                    newPackageItem.ItemName = packageItemtoAdd.ItemName;
                    newPackageItem.PackageId = packageItemtoAdd.PackageId;
                    newPackageItem.WebLink = packageItemtoAdd.WebLink;
                    newPackageItem.Notes = packageItemtoAdd.Notes;
                    newPackageItem.IsActive = true;
                    newPackageItem.CreatedBy = User.Identity.Name.Split('@')[0];
                    newPackageItem.CreatedDateTime = DateTime.Now;
                    _context.SchePackageItems.Add(newPackageItem);
                    await _context.SaveChangesAsync();
                    packageItemtoAdd.PackageItemId = newPackageItem.PackageItemId;
                }

                await UpdateItemAssignment(packageItemtoAdd);

                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<SchePackageItemDto> { Value = packageItemtoAdd, IsSuccess = true, Message = "Sucessfully added Package Item" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SchePackageItemDto> { IsSuccess = false, Message = "Failed to Add Package Item", Value = null });
            }
        }

        private async Task UpdateItemAssignment(SchePackageItemDto packageItemToAdd)
        {
            foreach (var itemAssignment in packageItemToAdd.SchePackageAssignments)
            {
                if (itemAssignment.ItemAssignmentId == 0)
                {
                    var checkIfExisting = await _context.SchePackageAssignments.SingleOrDefaultAsync(x => x.PackageItemId == packageItemToAdd.PackageItemId && x.DepartmentAssigned == itemAssignment.DepartmentAssigned && x.Manager == itemAssignment.Manager);
                    if (checkIfExisting != null)
                    {
                        checkIfExisting.IsActive = true;
                        checkIfExisting.UpdatedDateTime = DateTime.Now;
                        checkIfExisting.UpdatedBy = User.Identity.Name.Split('@')[0];
                        _context.SchePackageAssignments.Update(checkIfExisting);
                    }
                    else
                    {
                        var newPackageAssignment = new SchePackageAssignment();
                        newPackageAssignment.PackageItemId = packageItemToAdd.PackageItemId;
                        newPackageAssignment.Manager = itemAssignment.Manager;
                        newPackageAssignment.DepartmentAssigned = itemAssignment.DepartmentAssigned;
                        newPackageAssignment.IsActive = true;
                        newPackageAssignment.CreatedBy = User.Identity.Name.Split('@')[0];
                        newPackageAssignment.CreatedDateTime = DateTime.Now;
                        _context.SchePackageAssignments.Add(newPackageAssignment);
                    }
                }
                else
                {
                    var existingAssignment = await _context.SchePackageAssignments.SingleOrDefaultAsync(x => x.ItemAssignmentId == itemAssignment.ItemAssignmentId);
                    if (existingAssignment != null)
                    {
                        existingAssignment.DepartmentAssigned = itemAssignment.DepartmentAssigned;
                        existingAssignment.Manager = itemAssignment.Manager;
                        existingAssignment.UpdatedDateTime = DateTime.Now;
                        existingAssignment.UpdatedBy = User.Identity.Name.Split('@')[0];
                        _context.SchePackageAssignments.Update(existingAssignment);
                    }
                }
            }
            var assignmentList = await _context.SchePackageAssignments.Where(x => x.PackageItemId == packageItemToAdd.PackageItemId && x.IsActive == true).ToListAsync();
            var assignmentIdList = packageItemToAdd.SchePackageAssignments.Select(x => x.ItemAssignmentId).ToList();
            foreach (var assignment in assignmentList)
            {
                if (!assignmentIdList.Contains(assignment.ItemAssignmentId))
                {
                    assignment.IsActive = false;
                    assignment.UpdatedDateTime = DateTime.Now;
                    assignment.UpdatedBy = User.Identity.Name.Split('@')[0];
                    _context.SchePackageAssignments.Update(assignment);
                }
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdatePackageItemAsync([FromBody] SchePackageItemDto packageItemToUpdate)
        {
            try
            {
                var packageItem = await _context.SchePackageItems.SingleOrDefaultAsync(x => x.PackageItemId == packageItemToUpdate.PackageItemId);
                if (packageItem != null)
                {
                    packageItem.ItemName = packageItemToUpdate.ItemName;
                    packageItem.WebLink = packageItemToUpdate.WebLink;
                    packageItem.Notes = packageItemToUpdate.Notes;
                    packageItem.FinishDate = packageItemToUpdate.FinishDate;
                    packageItem.IsActive = true;
                    packageItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                    packageItem.UpdatedDateTime = DateTime.Now;
                    _context.SchePackageItems.Update(packageItem);

                    await UpdateItemAssignment(packageItemToUpdate);

                    await _context.SaveChangesAsync();
                }

                return new OkObjectResult(new ResponseModel<SchePackageItemDto> { Value = packageItemToUpdate, IsSuccess = true, Message = "Sucessfully updated Package Item" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SchePackageItemDto> { IsSuccess = false, Message = "Failed to Update Package Item", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeletePackageItemAsync([FromBody] SchePackageItemDto packageItemToDelete)
        {
            try
            {
                var packageItem = await _context.SchePackageItems.SingleOrDefaultAsync(x => x.PackageItemId == packageItemToDelete.PackageItemId);
                if (packageItem != null)
                {
                    packageItem.IsActive = false;
                    packageItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                    packageItem.UpdatedDateTime = DateTime.Now;
                    _context.SchePackageItems.Update(packageItem);

                    var assignmentList = await _context.SchePackageAssignments.Where(x => x.PackageItemId == packageItemToDelete.PackageItemId && x.IsActive == true).ToListAsync();
                    foreach (var assignment in assignmentList)
                    {
                        assignment.IsActive = false;
                        assignment.UpdatedDateTime = DateTime.Now;
                        assignment.UpdatedBy = User.Identity.Name.Split('@')[0];
                        _context.SchePackageAssignments.Update(assignment);
                    }
                    await _context.SaveChangesAsync();
                }


                return new OkObjectResult(new ResponseModel<SchePackageItemDto> { Value = packageItemToDelete, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SchePackageItemDto> { IsSuccess = false, Message = "Failed to Delete Package Item", Value = null });
            }
        }

        [HttpGet("{packageItemId}")]
        public async Task<IActionResult> PackageItemAssignmentAsync(int packageItemId)
        {
            try
            {
                var packageItemAssignment = await _context.SchePackageAssignments.Where(x => x.PackageItemId == packageItemId && x.IsActive == true).ToListAsync();
                var packageItemAssignmentDto = _mapper.Map<List<SchePackageAssignmentDto>>(packageItemAssignment);
                return new OkObjectResult(new ResponseModel<List<SchePackageAssignmentDto>> { Value = packageItemAssignmentDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SchePackageAssignmentDto>> { IsSuccess = false, Message = "Failed to get Package Item Assignment", Value = new List<SchePackageAssignmentDto>() });
            }
        }

        [HttpGet]
        public async Task<IActionResult> AllPackageItemAssignmentAsync()
        {
            try
            {
                var packageItemAssignment = await _context.SchePackageAssignments.Where(x => x.IsActive == true).ToListAsync();
                var packageItemAssignmentDto = _mapper.Map<List<SchePackageAssignmentDto>>(packageItemAssignment);
                return new OkObjectResult(new ResponseModel<List<SchePackageAssignmentDto>> { Value = packageItemAssignmentDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SchePackageAssignmentDto>> { IsSuccess = false, Message = "Failed to get All Package Item Assignment", Value = new List<SchePackageAssignmentDto>() });
            }
        }

        [HttpGet]
        public async Task<IActionResult> CalendarAsync()
        {
            try
            {
                var calendar = await _context.CalendarsDays.Where(x => x.IsActive == true).OrderBy(x => x.WorkDate).ToListAsync();
                var calendarsDto = _mapper.Map<List<CalendarsDayDto>>(calendar);
                return new OkObjectResult(new ResponseModel<List<CalendarsDayDto>> { Value = calendarsDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CalendarsDayDto>> { IsSuccess = false, Message = "Failed to get Calendar" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> UpdateCalendarAsync([FromBody] CalendarsDayDto model)
        {
            try
            {
                // if date doesn't exist, we have to make a new entry to the database
                // if the date exists, we have to find the current entry and update the database row
                var updateDate = await _context.CalendarsDays.FindAsync(model.CalendarsDaysId);
                if (!string.IsNullOrWhiteSpace(model.Description))
                {
                    if (updateDate == null)
                    {
                        updateDate = new CalendarsDay()
                        {
                            WorkDate = model.WorkDate,
                            Description = model.Description,
                            CreatedDateTime = DateTime.Now,
                            CreatedBy = User.Identity.Name.Split('@')[0],
                        };
                        _context.CalendarsDays.Add(updateDate);
                        await _context.SaveChangesAsync();
                    }
                    else
                    {
                        updateDate.WorkDate = model.WorkDate;
                        updateDate.Description = model.Description;
                        updateDate.UpdatedBy = User.Identity.Name.Split('@')[0];
                        updateDate.UpdatedDateTime = DateTime.Now;
                        _context.CalendarsDays.Update(updateDate);
                        await _context.SaveChangesAsync();
                    }
                }
                return Ok(new ResponseModel<CalendarsDayDto> { IsSuccess = true, Message = "update", Value = model });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<CalendarsDayDto> { IsSuccess = false, Message = "Failed to update calendar", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> DeleteCalendarItemAsync([FromBody] CalendarsDayDto model)
        {
            try
            {
                var deleteDate = await _context.CalendarsDays.FindAsync(model.CalendarsDaysId);
                if (deleteDate != null)
                {
                    //TODO: soft delete instead?
                    _context.CalendarsDays.Where(x => x.CalendarsDaysId == model.CalendarsDaysId).ExecuteDelete();
                    await _context.SaveChangesAsync();
                    return Ok(new ResponseModel<CalendarsDayDto> { IsSuccess = true, Message = "Deleted date from calendar", Value = model });
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
            }
            return StatusCode(500, new ResponseModel<CalendarsDayDto> { IsSuccess = false, Message = "Failed to delete from calendar", Value = null });
        }


        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> GetJobSActivityAsync(string jobNumber)
        {
            try
            {
                var getSActivities = new List<SactivityDto>();
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "select sa.SACTIVITY_ID, sa.ACTIVITY_NAME from dbo.SCHEDULE_SACTIVITY ss JOIN dbo.SCHEDULE s on ss.SCHEDULE_ID = s.SCHEDULE_ID JOIN dbo.SACTIVITY sa on ss.SACTIVITY_ID = sa.SACTIVITY_ID where s.JOB_NUMBER = @JobNumber and ss.IsActive = 1 and sa.IsActive = 1 order by 2";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@JobNumber", SqlDbType.VarChar).Value = jobNumber;

                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        getSActivities.Add(new SactivityDto
                        {
                            SactivityId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                            ActivityName = (reader.GetValue(1) != DBNull.Value) ? reader.GetValue(1).ToString() : string.Empty
                        });
                    }
                }

                return new OkObjectResult(new ResponseModel<List<SactivityDto>> { Value = getSActivities, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SactivityDto>> { IsSuccess = false, Message = "Failed to fetch sactivities" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetTotalIncompleteActivitiesBySubdivisionAsync()
        {
            try
            {
                var returnData = new List<IncompleteSubdivisionActivitiesDto>();

                var conn = _configuration.GetConnectionString("ERPConnection");

                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT x.Marketing_Name, x.SUBDIVISION_ID, COUNT(x.COMPLETE) 'Total' FROM ( SELECT CASE WHEN s.MARKETING_NAME <> NULL THEN s.MARKETING_NAME ELSE s.SUBDIVISION_NAME END 'Marketing_Name', j.Lot_Number, j.JOB_NUMBER, sa.ACTIVITY_NAME, ss.COMPLETE, CAST(SCH_END_DATE AS DATE) AS sch_end_date, sc.PUBLISHED, s.SUBDIVISION_ID FROM dbo.SCHEDULE_SACTIVITY ss JOIN dbo.SACTIVITY sa ON ss.SACTIVITY_ID = sa.SACTIVITY_ID AND ss.IsActive = 1 AND sa.IsActive = 1 JOIN dbo.SCHEDULE sc ON ss.SCHEDULE_ID = sc.SCHEDULE_ID AND sc.IsActive = 1 JOIN dbo.JOB j ON sc.JOB_NUMBER = j.JOB_NUMBER AND j.IsActive = 1 JOIN dbo.SUBDIVISION s ON j.SUBDIVISION_ID = s.SUBDIVISION_ID AND s.IsActive = 1 WHERE YEAR(sch_end_date) = YEAR(GETDATE()) AND CAST(sch_end_date AS DATE) <= CAST(GETDATE() AS DATE) AND ss.COMPLETE = 'F' AND ss.ACTUAL_END_DATE IS NULL AND j.JOB_NUMBER NOT IN (SELECT JOB_Num_TXT FROM dbo.INVENTORY WHERE SETTLEMENT_DATE IS NOT NULL) AND j.JOB_NUMBER NOT IN ('GMSF0003', 'GMSF0007', 'GMSF0024') AND sc.PUBLISHED = 'T') x GROUP BY x.Marketing_Name, x.SUBDIVISION_ID;";
                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        var incompleteSubdivisionActivities = new IncompleteSubdivisionActivitiesDto()
                        {
                            Amount = Convert.ToInt32(reader.GetValue(2).ToString())
                        };

                        incompleteSubdivisionActivities.Subdivision.MarketingName = reader.GetString(0);
                        incompleteSubdivisionActivities.Subdivision.SubdivisionId = Convert.ToInt32(reader.GetValue(1).ToString());
                        returnData.Add(incompleteSubdivisionActivities);
                    }

                }

                return new OkObjectResult(new ResponseModel<List<IncompleteSubdivisionActivitiesDto>> { Value = returnData, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the incomplete subdivision activities"));
            }
        }

        [HttpGet("{subdivisionId}")]
        public async Task<IActionResult> GetIncompleteActivitiesBySubdivisionAsync(int subdivisionId)
        {
            try
            {
                var returnData = new List<ScheduleSactivityDto>();

                var conn = _configuration.GetConnectionString("ERPConnection");

                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT x.ACTIVITY_NAME, x.SACTIVITY_ID, x.JOB_NUMBER,  x.Lot_Number, x.sch_end_date FROM ( SELECT CASE WHEN s.MARKETING_NAME <> NULL THEN s.MARKETING_NAME ELSE s.SUBDIVISION_NAME END 'Marketing_Name', j.Lot_Number, j.JOB_NUMBER, sa.ACTIVITY_NAME, ss.COMPLETE, CAST(SCH_END_DATE AS DATE) AS sch_end_date, sc.PUBLISHED, s.SUBDIVISION_ID, ss.SACTIVITY_ID FROM dbo.SCHEDULE_SACTIVITY ss JOIN dbo.SACTIVITY sa ON ss.SACTIVITY_ID = sa.SACTIVITY_ID AND ss.IsActive = 1 AND sa.IsActive = 1 JOIN dbo.SCHEDULE sc ON ss.SCHEDULE_ID = sc.SCHEDULE_ID AND sc.IsActive = 1 JOIN dbo.JOB j ON sc.JOB_NUMBER = j.JOB_NUMBER AND j.IsActive = 1 JOIN dbo.SUBDIVISION s ON j.SUBDIVISION_ID = s.SUBDIVISION_ID AND s.IsActive = 1 WHERE YEAR(sch_end_date) = YEAR(GETDATE()) AND CAST(sch_end_date AS DATE) <= CAST(GETDATE() AS DATE) AND ss.COMPLETE = 'F' AND ss.ACTUAL_END_DATE IS NULL AND j.JOB_NUMBER NOT IN (SELECT JOB_Num_TXT FROM dbo.INVENTORY WHERE SETTLEMENT_DATE IS NOT NULL) AND j.JOB_NUMBER NOT IN ('GMSF0003', 'GMSF0007', 'GMSF0024') AND sc.PUBLISHED = 'T') x where x.SUBDIVISION_ID = @subdivisionId;";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@subdivisionId", SqlDbType.Int).Value = subdivisionId;
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    { 
                        var scheduleSActivity = new ScheduleSactivityDto
                        {
                            ActivityName = reader.GetValue(0) != DBNull.Value ? reader.GetValue(0).ToString() : null,
                            SactivityId = reader.GetValue(1) != DBNull.Value ? Convert.ToInt32(reader.GetValue(1).ToString()) : null,
                            SchEndDate = (reader.GetValue(4) != DBNull.Value) ? Convert.ToDateTime(reader.GetValue(4).ToString()) : (DateTime?)null,
                        };

                        scheduleSActivity.ScheduleM = new ScheduleMilestoneDto
                        {
                            Schedule = new ScheduleDto
                            {
                                JobNumberNavigation = new JobDto
                                {
                                    JobNumber = reader.GetValue(2) != DBNull.Value ? reader.GetValue(2).ToString() : null,
                                    LotNumber = reader.GetValue(3) != DBNull.Value ? reader.GetValue(3).ToString() : null,
                                }
                            }
                        };

                        returnData.Add(scheduleSActivity);

                    }
                }

                return new OkObjectResult(new ResponseModel<List<ScheduleSactivityDto>> { Value = returnData, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception($"Error in getting the open activities for the subdivsion id {subdivisionId}"));
            }
        }
        [HttpPost]
        public async Task<IActionResult> DownloadSchedulesExport([FromBody] List<ScheduleTemplateTreeModel> model)
        {
            try
            {
                var author = User.Identity.Name.Split('@')[0];
                var myFile = File(_service.CreateSchedulesExport((List<ScheduleTemplateTreeModel>)model, author), contentType, fileName);
                var fileBytes = myFile.FileContents;
                return Ok(new ResponseModel<byte[]>() { Value = fileBytes, IsSuccess = true });//returns the byte array, won't work for large files
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<byte[]>() { IsSuccess = false, Message = "Failed to download costs export", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> DownloadSchedulesTemplatesExport([FromBody] List<ScheduleTemplateTreeModel> model)
        {
            try
            {
                var author = User.Identity.Name.Split('@')[0];
                var myFile = File(_service.CreateSchedulesTemplatesExport((List<ScheduleTemplateTreeModel>)model, author), contentType, fileName);
                var fileBytes = myFile.FileContents;
                return Ok(new ResponseModel<byte[]>() { Value = fileBytes, IsSuccess = true });//returns the byte array, won't work for large files
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<byte[]>() { IsSuccess = false, Message = "Failed to download costs export", Value = null });
            }
        }
    }
}
