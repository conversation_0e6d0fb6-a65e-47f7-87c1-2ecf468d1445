﻿@inject HttpClient Http

<h3>Attachment Viewer</h3>

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="400px"
               Height="400px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        View Attachments
    </WindowTitle>
    <WindowContent>
        <div>
            <button @onclick="NavigatePrevious">Previous</button>
            <button @onclick="NavigateNext">Next</button>
        </div>

    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>


<h4>All Attachments</h4>

@code {
    [Parameter]
    public bool IsModalVisible { get; set; }
    [Parameter]
    public List<BlobFileDetails>? Attachments { get; set; }

    private int currentIndex = 0;
    private BlobFileDetails currentBlobFileDetails;

    public async Task Show()
    {
        // currentBlobFileDetails = Attachments[currentIndex];
    }
    protected override void OnInitialized()
    {
        var a = 4;
    }

    private void NavigateNext()
    {
        currentIndex = (currentIndex + 1) % Attachments.Count; // Loop back to the first attachment
        currentBlobFileDetails = Attachments[currentIndex];
    }

    private void NavigatePrevious()
    {
        currentIndex = (currentIndex - 1 + Attachments.Count) % Attachments.Count; // Loop to the last attachment
        currentBlobFileDetails = Attachments[currentIndex];
    }

}

