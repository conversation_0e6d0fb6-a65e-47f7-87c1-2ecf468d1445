﻿@page "/approveselections"
@inject IJSRuntime JsRuntime
@inject SalesConfigService SalesConfigService
@inject SelectedOptionsService SelectedOptionsService
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject SubdivisionService SubdivisionService
@inject AuthenticationStateProvider AuthenticationStateProvider
@implements IDisposable
@inject IJSRuntime JsRuntime
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling")]
@using System.Collections.ObjectModel
@using Telerik.DataSource.Extensions
@using ERP.Data.Models
@using Telerik.Blazor.Components.Grid
@using Telerik.Documents.SpreadsheetStreaming
@using static ERP.Web.Components.ApproveSelectedOptions

<style>
    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }

    .mostRecent .k-grid-checkbox {
        color: mediumpurple;
    }

    .structuralOption {
        background-color: lightblue !important;
    }

    .nonStructuralOption {
        background-color: lightyellow !important;
    }

    .builderApproved {
        background-color: #d7f5e3 !important;
    }

    .mostRecent {
        color: mediumpurple;
    }
    .notRecent .k-grid-checkbox {
        display: none;
    }

    .noApproveCheckbox .k-grid-checkbox {
        display: none;
    }

    /*the following selectors target the locked/frozen columns*/
    /*===*/
    .k-grid .k-master-row.myCustomRowFormatting .k-grid-content-sticky,
    .k-grid .k-master-row.myCustomRowFormatting.k-alt .k-grid-content-sticky
    /*===*/ {
        background-color: inherit;
    }

    .k-grid .k-master-row.myCustomRowFormatting:hover {
        background-color: green !important;
    }

    .k-grid .k-master-row.myCustomRowFormatting {
        background-color: yellow;
    }
</style>

<PageTitle>Approve Selected Options</PageTitle>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="false" ShowJob="true" ShowSubdivision="true"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
<TelerikTooltip TargetSelector=".tooltip-target" />

<div class="container-fluid">
    <div class="row">
        <div class="card" style="background-color:#2e5771">
            <div class="card-body" style="padding:0.5rem">
                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Approve Selected Options</h7>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item active">Approve Selected Options</li>
        </ol>

        @if(SelectedOptionsData == null && !loading)
        {
            <p>Select a job to see options</p>
        }
        else if(loading)
        {
            <p><em>Loading...</em></p>
            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />

        }        
        else
        {
            <TelerikTabStrip>
                <TabStripTab Title="Selected Options">
                    <div class="col-lg-9">
                        <TelerikGrid Data="@SelectedOptionsData"
                                     SelectionMode="@GridSelectionMode.Multiple"
                                     SelectedItems="@SelectedItems"
                                     Groupable="true"
                                     PageSize="50"
                                     Resizable="true"
                                     Width="100%"
                                     RowHeight=40
                                     OnRowClick="@OnRowClickHandler"
                                     OnRowRender="@OnRowRenderHandler"
                                     Height="1000px"
                                     Sortable="true"
                                     FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                     EditMode="@GridEditMode.Inline"
                                     @ref="@SelectedOptionsGrid">
                            <GridColumns>
                                <GridColumn Field="BuilderApproved" Title="" Visible="true" Editable="true" Width="40px" Filterable="false" Sortable="false">
                                    <Template>
                                        @{
                                            var SelectedRow = context as TbBuiltOptionDto;
                                            if (SelectedRow.NeedsApproval == true)
                                            {
                                                <TelerikCheckBox Enabled=@true @bind-Value="@SelectedRow.BoolBuilderApproved"></TelerikCheckBox>
                                            }
                                            else
                                            {
                                                //show disabled checkbox if it was approved
                                                if (SelectedRow.BuilderApproved == 1)
                                                {
                                                    <TelerikCheckBox Enabled=@false @bind-Value="@SelectedRow.BoolBuilderApproved"></TelerikCheckBox>
                                                }
                                            }
                                        }
                                    </Template>
                                    <HeaderTemplate>
                                        @{
                                            <TelerikCheckBox @bind-Value="@SelectAllCheckBoxValue"
                                                             Indeterminate="@(SelectAllCheckBoxValue == null)"
                                                             OnChange="@ToggleSelectAll" />

                                        }
                                    </HeaderTemplate>
                                </GridColumn>
                                <GridColumn Field="JobNumber" Editable="false" Visible="false" Width="100px"></GridColumn>
                                <GridColumn Field="OptionCode" Title="Option Code" Editable="false" Width="100px" FilterMenuType="@FilterMenuType.CheckBoxList"></GridColumn>
                                <GridColumn Field="OptionDesc" Title="Option Desc" Editable="false" Width="200px"></GridColumn>
                                <GridColumn Field="Id" Title="Change" Editable="false" Width="200px">
                                    <Template>
                                        @{
                                            var item = context as TbBuiltOptionDto;
                                            if (item.Removed)
                                            {
                                                <span style="color:red"><strong>Remove</strong></span>
                                            }
                                            else if (item.QtyDelta != 0 && (item.QtyDelta == item.Qty || item.QtyDelta == null))
                                            {
                                                if (item.BuildAttributeItems != null && item.BuildAttributeItems.Any())
                                                {
                                                    <TelerikButton OnClick="()=> ShowAttributes(item)" Title="show attributes" Class="k-button-success">Selections</TelerikButton>
                                                    <span style="color:blue"> - </span>
                                                }
                                                <span style="color:blue"><strong>Add</strong></span>
                                            }
                                            else if (item.QtyDelta != 0 && item.QtyDelta != item.Qty)
                                            {
                                                if (item.BuildAttributeItems != null && item.BuildAttributeItems.Any())
                                                {
                                                    <TelerikButton OnClick="()=>ShowAttributes(item)" Title="show attributes" Class="k-button-success">Selections</TelerikButton>
                                                    <span style="color:purple"> - </span>
                                                }
                                                <span style="color:purple"><strong>Change Qty</strong></span>
                                            }
                                            else if (item.QtyDelta == 0 && !item.Removed && item.BuildAttributeItems != null && item.BuildAttributeItems.Any())
                                            {
                                                if (item.BuildAttributeItems != null && item.BuildAttributeItems.Any())
                                                {
                                                    <TelerikButton OnClick="()=>ShowAttributes(item)" Title="show attributes" Class="k-button-success">Selections</TelerikButton>
                                                    <span style="color:green"> - </span>
                                                }
                                                <span style="color:green"><strong>Selections/Other Change</strong></span>
                                            }
                                            else
                                            {
                                                <span style="color:green"><strong>Selections/Other Change</strong></span>
                                            }
                                        }
                                    </Template>
                                </GridColumn>
                                <GridColumn Field="Removed" Visible="false" Title="Removed" Editable="false" Width="100px"></GridColumn>
                                <GridColumn Field="MostRecent" Visible="false" Title="MostRecent" Editable="false" Width="100px"></GridColumn>
                                <GridColumn Field="Exported" Visible="false" Title="Approved" Editable="false" Width="100px"></GridColumn>
                                <GridColumn Field="BuilderApproved" Visible="false" Title="BuilderApproved" Editable="false" Width="100px"></GridColumn>
                                <GridColumn Field="Qty" Title="Quantity" Editable="false" Width="50px"></GridColumn>
                                <GridColumn Field="CreatedDateTime" Title="Date" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Width="100px"></GridColumn>
                                <GridColumn Field="QtyDelta" Visible="false" Title="Quantity Delta" Editable="false" Width="50px"></GridColumn>
                                <GridColumn Field="Price" Title="Price" Visible="false" Editable="false" Width="100px"></GridColumn>
                                <GridColumn Field="PriceDelta" Title="Price Delta" Visible="false" Editable="false" Width="100px"></GridColumn>
                                <GridColumn Field="OptionNotes" Title="Option Notes" Editable="false" Visible="false"></GridColumn>
                                <GridColumn Field="OptionSelections" Title="Option Selections" Editable="false" Visible="false"></GridColumn>
                                <GridColumn Field="UnitCost" Title="Unit Cost" Editable="false" Visible="false" DisplayFormat="{0:C2}" Width="100px"></GridColumn>
                                <GridColumn Field="ListPrice" Title="Unit List Price" Visible="false" DisplayFormat="{0:C2}" Editable="false" Width="100px"></GridColumn>
                                <GridColumn Field="OptionTypeNavigation.OptionType1" Title="Type" Editable="false" Width="70px"></GridColumn>
                                @* <GridColumn Field="BoolFromSpec" Title="Spec Option" Visible="true" Editable="false" Width="50px"></GridColumn> *@
                                @* <GridColumn Field="CancelledOption" Title="Cancelled Option" Visible="true" Editable="false" Width="50px"></GridColumn> *@
                            </GridColumns>
                            <GridToolBarTemplate>
                                <GridSearchBox DebounceDelay="200">
                                </GridSearchBox>
                                <GridCommandButton Command="ApproveSelected" Title="Approve Selected" Icon="@FontIcon.Check" OnClick="ApproveSelected" Class="k-button-success">Approve Selected</GridCommandButton>
                                <TelerikToggleButton @bind-Selected="@ToggleShowAll" OnClick="@ToggleNeedApprovalOnly" Class="k-button-add">All/Need Approval Only: <strong>@ShowHideNeedApprove</strong></TelerikToggleButton>
                                <TelerikButton OnClick="DownloadApprovedOptionsPdf">Approved Options PDF</TelerikButton>
                                <TelerikButton OnClick="DownloadSelectedOptionsPdf">Selected Options PDF</TelerikButton>
                                <TelerikButton OnClick="DownloadAllOptionsPdf">All Available Options PDF</TelerikButton>
                                <GridCommandButton Command="ExcelExport" Icon="@FontIcon.FileExcel" Class="k-button-add">Export to Excel</GridCommandButton>
                                <span style="vertical-align: middle;">
                                    <div style="display: inline-block; margin-right: 5px; background-color: #d7f5e3; width: 20px; height: 20px; border-radius: 50%; border: 1px solid black;"></div> - Approved
                                    <div style="display: inline-block; margin-right: 5px; margin-left: 10px;background-color: lightblue; width: 20px; height: 20px; border-radius: 50%; border: 1px solid black;"></div> - Structural
                                    <div style="display: inline-block; margin-right: 5px; margin-left: 10px;background-color: lightyellow; width: 20px; height: 20px; border-radius: 50%; border: 1px solid black;"></div> - Non Structural
                                </span>                               
                            </GridToolBarTemplate>
                            <GridExport>
                                <GridExcelExport FileName="SelectedOptions" OnBeforeExport="@OnExcelBeforeExport" AllPages="true" />
                            </GridExport>
                        </TelerikGrid>
                    </div>
                    <div class="card col-lg-3 p-2">
                        <h5>Customer</h5>
                        <p><strong>Status: </strong>@LotStatus</p>
                        @if (BuyerData != null && BuyerData.Count != 0)
                        {
                            foreach (var buyer in BuyerData)
                            {
                                //var firstBuyer = BuyerData.First(x => x.ListOrder == 0);//ListOrder 0 = Primary Buyer
                                <p><strong>CMS Buyer: </strong>@($"{buyer.FirstName} {buyer.LastName}") </p>
                                <p><strong>CMS Buyer Email: </strong> @buyer.Email</p>
                                <p><strong>CMS Buyer Address: </strong> @buyer.Address</p>
                                <p><strong>CMS Buyer Phone: </strong> @buyer.Phone</p>
                                <br />

                                if (buyer.IsCancelled == true)
                                {
                                    <TelerikButton Title="Push Cancellation Data" Icon="@FontIcon.Check" OnClick="PushCustomerData" Class="k-button-success">Push Cancellation Data</TelerikButton>
                                }
                                else
                                {
                                    <TelerikButton Title="Push Customer Data" Icon="@FontIcon.Check" OnClick="PushCustomerData" Class="k-button-success">Push Customer Data</TelerikButton>
                                }

                            }
                        }
                        <br />
                        <p><strong>ERP Customer: </strong>@($"{ERPCustomerData.CustomerName}") </p>
                        <p><strong>ERP Customer Email: </strong> @ERPCustomerData.Email</p>
                        <p><strong>ERP Customer Address: </strong> @ERPCustomerData.Address1</p>
                        <p><strong>ERP Customer Phone: </strong> @ERPCustomerData.HomePhone</p>
                    </div>
                </TabStripTab>

                <TabStripTab Title="All Options">
                    <TelerikGrid Data="@AllOptionsData"
                                 Groupable="true"
                                 PageSize="50"
                                 Resizable="true"
                                 Width="100%"
                                 RowHeight=40
                                 Height="600px"
                                 Sortable="true"
                                 FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                 OnRowRender="@AllOptionsOnRowRenderHandler"
                                 @ref="@AllOptionsGrid">
                        <GridColumns>
                            <GridColumn Field="OptionCode" Title="Option Code" Editable="false" Width="100px"></GridColumn>
                            <GridColumn Field="ModifiedOptionDesc" Title="Option Description" Editable="false" Width="200px"></GridColumn>
                            <GridColumn Field="OptionTypeNavigation.OptionType1" Title="Option Type" Editable="false" Width="200px"></GridColumn>
                        </GridColumns>

                    </TelerikGrid>
                </TabStripTab>
            </TelerikTabStrip>

        }
    </div>
</div>
<ShowAttributes SelectedOption="@ItemToEdit" @ref="ShowAttributesModal"></ShowAttributes>
@code {

    public List<AvailablePlanOptionDto>? AllOptionsData { get; set; }
    public ObservableCollection<TbBuiltOptionDto>? SelectedOptionsData { get; set; }
    public ObservableCollection<TbBuiltOptionDto>? AllSelectedOptionsData { get; set; }
    private TelerikGrid<TbBuiltOptionDto>? SelectedOptionsGrid { get; set; }
    private TelerikGrid<AvailablePlanOptionDto>? AllOptionsGrid { get; set; }
    public ShowAttributes? ShowAttributesModal { get; set; }
    public TbBuiltOptionDto? ItemToEdit { get; set; }
    public TbBuiltOptionDto? ItemDetails { get; set; } = new TbBuiltOptionDto();
    public IEnumerable<TbBuiltOptionDto>? SelectedItems { get; set; } = Enumerable.Empty<TbBuiltOptionDto>();
    public List<BuyerDto>? BuyerData { get; set; }
    public string? LotStatus { get; set; }//spec, sold, tbb
    public CustomerDto? ERPCustomerData { get; set; } = new CustomerDto();
    public bool WindowIsVisible { get; set; } = true;
    private string? SelectedJob { get; set; }
    private string? JobSelected { get; set; }
    private bool ToggleShowAll { get; set; } = true;
    private bool ShowAll { get; set; } = false;
    private string? ShowHideNeedApprove { get; set; } = "Need Approval";
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    private bool loading { get; set; } = false;
    private bool AllowEdit { get; set; } = true;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        //TODO: if no job selected, show jobs with recent approved options
        SubdivisionJobPickService.OnChanged += JobChangedHandler;
        if (SubdivisionJobPickService.JobNumber != null)
        {
            loading = true;
            JobSelected = SubdivisionJobPickService.JobNumber;
            var getSelectedOptionsTask = SelectedOptionsService.GetSelectedOptionsByJobAsync(JobSelected);
            var getBuyerTask = SelectedOptionsService.GetBuyersByJobAsync(JobSelected);
            var getExistingERPCustomerTask = SelectedOptionsService.GetJobCustomerAsync(JobSelected);
            var getAllOptionsTask = SelectedOptionsService.GetAllOptionsByJobAsync(JobSelected);
            await Task.WhenAll(new Task[] { getBuyerTask, getSelectedOptionsTask, getExistingERPCustomerTask, getAllOptionsTask });
            if (!getSelectedOptionsTask.Result.IsSuccess || !getBuyerTask.Result.IsSuccess || !getExistingERPCustomerTask.Result.IsSuccess)
            {
                ShowSuccessOrErrorNotification(getSelectedOptionsTask.Result.Message, getSelectedOptionsTask.Result.IsSuccess);
                ShowSuccessOrErrorNotification(getExistingERPCustomerTask.Result.Message, getExistingERPCustomerTask.Result.IsSuccess);
                ShowSuccessOrErrorNotification(getBuyerTask.Result.Message, getBuyerTask.Result.IsSuccess);
                ShowSuccessOrErrorNotification(getAllOptionsTask.Result.Message, getAllOptionsTask.Result.IsSuccess);
            }

            AllOptionsData = getAllOptionsTask.Result.Value;
            var getSelectedOptionsData = getSelectedOptionsTask.Result.Value;
            SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(getSelectedOptionsData);
            AllSelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(getSelectedOptionsData);
            if (!ShowAll)
            {
                SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData.Where(x => x.NeedsApproval == true));
            }
            else
            {
                SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData);
            }
            BuyerData = getBuyerTask.Result.Value;
            LotStatus = getBuyerTask.Result.Message;//TODO: put this someplace cleaner
            ERPCustomerData = getExistingERPCustomerTask.Result.Value;
            loading = false;
            StateHasChanged();
        }
    }
    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= JobChangedHandler;
    }
    async Task JobChangedHandler()
    {
        var selected = SubdivisionJobPickService.JobNumber;
        if (JobSelected != selected)//check if actual change, so not calling this multiple times
        {            
            loading = true;
            JobSelected = selected;
            var getSelectedOptionsTask = SelectedOptionsService.GetSelectedOptionsByJobAsync(JobSelected);
            var getBuyerTask = SelectedOptionsService.GetBuyersByJobAsync(JobSelected);
            var getExistingERPCustomerTask = SelectedOptionsService.GetJobCustomerAsync(JobSelected);
            await Task.WhenAll(new Task[] { getBuyerTask, getSelectedOptionsTask, getExistingERPCustomerTask });
            if (!getSelectedOptionsTask.Result.IsSuccess || !getBuyerTask.Result.IsSuccess || !getExistingERPCustomerTask.Result.IsSuccess)
            {
                ShowSuccessOrErrorNotification(getSelectedOptionsTask.Result.Message, getSelectedOptionsTask.Result.IsSuccess);
                ShowSuccessOrErrorNotification(getExistingERPCustomerTask.Result.Message, getExistingERPCustomerTask.Result.IsSuccess);
                ShowSuccessOrErrorNotification(getBuyerTask.Result.Message, getBuyerTask.Result.IsSuccess);
            }
            var getSelectedOptionsData = getSelectedOptionsTask.Result.Value;
            SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(getSelectedOptionsData);
            AllSelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(getSelectedOptionsData);
            if (!ShowAll)
            {
                SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData.Where(x => x.NeedsApproval == true));
            }
            else
            {
                SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData);
            }
            BuyerData = getBuyerTask.Result.Value;
            LotStatus = getBuyerTask.Result.Message;//TODO: put this someplace cleaner
            ERPCustomerData = getExistingERPCustomerTask.Result.Value;
            loading = false;
            StateHasChanged();
        }
    }

    void AllOptionsOnRowRenderHandler(GridRowRenderEventArgs args)
    {
        var option = args.Item as AvailablePlanOptionDto;
        if (AllSelectedOptionsData != null)
        {
            if (AllSelectedOptionsData.Where(x => x.MostRecent == true && x.Removed == false).Any(x => x.OptionCode == option.OptionCode))
            {
                args.Class = "myCustomRowFormatting";
            }
        }
    }
    void OnRowRenderHandler(GridRowRenderEventArgs args)
    {
        TbBuiltOptionDto item = args.Item as TbBuiltOptionDto;
        if (item != null && item.OptionCode != null && item.NeedsApproval == true)
        {
            if (item.OptionGroup != null && item.OptionGroup.OptionGroupLetter != null && item.OptionGroup.OptionGroupLetter.StartsWith("A") || item.OptionGroup.OptionGroupLetter.StartsWith("B"))
            {
                args.Class = "structuralOption";
            }
            else
            {
                args.Class = "nonStructuralOption";
            }
        }

        if (item != null && item.BoolBuilderApproved == true)
        {
            args.Class = "builderApproved";
        }

    }
    void SelectedItemsChanged(IEnumerable<TbBuiltOptionDto> data)
    {
        var test = data;
        foreach (var item in data)
        {
            if (item.MostRecent == true)
            {
                if (SelectedItems.Any(x => x.Id == item.Id))
                {
                    SelectedItems = SelectedItems.Where(x => x.Id != item.Id);//deselect, it was alredy selected
                }
                else
                {
                    SelectedItems = SelectedItems.Concat(new[] { item });//add to selected
                }

                SelectedItems = new List<TbBuiltOptionDto>(SelectedItems);
            }
            else
            {
                //don't add
            }
        }
    }
    async Task OnRowClickHandler(GridRowClickEventArgs args)
    {
        var currItem = args.Item as TbBuiltOptionDto;
        var addItems = new List<TbBuiltOptionDto>();
        var currentSelected = SelectedItems.ToList();
        if (currItem.NeedsApproval == true)
        {
            if (SelectedItems.Any(x => x.Id == currItem.Id))
            {
                currItem.BoolBuilderApproved = false;
                SelectedItems = SelectedItems.Where(x => x.Id != currItem.Id);//deselect, it was alredy selected
            }
            else
            {
                currItem.BoolBuilderApproved = true;
                currentSelected.Add(currItem);
                SelectedItems = currentSelected;
                //SelectedItems = SelectedItems.Concat(new[] { currItem });//add to selected
            }

            SelectedItems = new List<TbBuiltOptionDto>(SelectedItems);
            args.ShouldRender = true;
        }
        else
        {
            //don't add
        }
    }
    async Task ToggleNeedApprovalOnly()
    {
        //It doesn't change the value until after this runs
        ShowAll = ToggleShowAll;
        ShowHideNeedApprove = !ShowAll ? "Need Approval" : "All";
        if (!ShowAll)
        {
            SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData.Where(x => x.NeedsApproval == true));
        }
        else
        {
            SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData);
        }
    }
    protected async Task ApproveSelected()
    {
        var selected = SelectedItems;
        var response = await SelectedOptionsService.ApproveSelectedOptionsAsync(SelectedItems.ToList());
        if (response.IsSuccess)
        {
            // foreach (var item in SelectedItems)
            // {
            //     SelectedOptionsData.Remove(item);//probably slow, might be easier to just refresh
            // }
            SelectedItems = Enumerable.Empty<TbBuiltOptionDto>();
            var getSelectedOptions = await SelectedOptionsService.GetSelectedOptionsByJobAsync(JobSelected);
            var getSelectedOptionsData = getSelectedOptions.Value;
            SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(getSelectedOptionsData);
            AllSelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(getSelectedOptionsData);
            if (!ShowAll)
            {
                SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData.Where(x => x.NeedsApproval == true));
            }
            else
            {
                SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData);
            }
        }
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);

    }
    protected async Task PushCustomerData()
    {
        //TODO: show indication if customer data already pushed
        if(JobSelected != null)
        {
            var response = await SelectedOptionsService.PushCustomerDataAsync(JobSelected);
            ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
            if (response.IsSuccess)
            {
                //refresh
                var getExistingERPCustomer = await SelectedOptionsService.GetJobCustomerAsync(JobSelected);
                ERPCustomerData = getExistingERPCustomer.Value;
            }
        }       
    }
    protected async Task PushCustomerDataNav()
    {
        //TODO: show indication if customer data already pushed
        if (JobSelected != null)
        {
            var response = await SelectedOptionsService.PushCustomerDataNavAsync(JobSelected);
            ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
            if (response.IsSuccess)
            {
                //refresh
                var getExistingERPCustomer = await SelectedOptionsService.GetJobCustomerAsync(JobSelected);
                ERPCustomerData = getExistingERPCustomer.Value;
                //download data
                if (response.Value != null)
                {
                    var fileData = response.Value;
                    DemoFileExporter.Save(JsRuntime, fileData, "application/xml", $"Customer-Job-{DateTime.Now.ToString("MM-dd-yyyy")}.xml");
                }
            }
        }
    }
    private void ShowSuccessOrErrorNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }

    private void ToggleSelectAll()
    {
        var test = SelectAllCheckBoxValue;
        if (SelectAllCheckBoxValue == true)
        {
            foreach (var item in SelectedOptionsData.Where(x => x.NeedsApproval == true))
            {
                item.BoolBuilderApproved = true;
                item.BuilderApproved = 1;
            }
        }
        if (SelectAllCheckBoxValue == false)
        {
            foreach (var item in SelectedOptionsData.Where(x => x.NeedsApproval == true))
            {
                item.BoolBuilderApproved = false;
                item.BuilderApproved = 0;
            }
        }
    }

    private bool? SelectAllCheckBoxValue
    {
        get
        {
            if (IsAllDataSelected())
            {
                return true;
            }
            else if (IsAnyDataSelected())
            {
                return null;
            }

            return false;
        }

        set
        {
            if (value.HasValue && value.Value == true)
            {
                SelectedItems = SelectedOptionsData.Where(x => x.NeedsApproval == true);
            }
            else
            {
                SelectedItems = new List<TbBuiltOptionDto>();
            }
        }
    }

    private bool IsAnyDataSelected()
    {
        return SelectedItems.Count() > 0 && SelectedItems.Count() < SelectedOptionsData.Where(x => x.NeedsApproval == true).Count();
    }

    private bool IsAllDataSelected()
    {
        return SelectedItems.Count() == SelectedOptionsData.Where(x => x.NeedsApproval == true).Count();
    }
    protected async Task ShowAttributes(TbBuiltOptionDto item)
    {
        //var item = args.Item as TbBuiltOptionDto;
        if (item != null)
        {
            ItemToEdit = item;
            await ShowAttributesModal.Show();
        }
    }

    async Task DownloadSelectedOptionsPdf()
    {
        if (AllSelectedOptionsData != null)
        {
            var getJobTask = SubdivisionService.GetLotAsync(JobSelected);
            var getBuyerTask = SelectedOptionsService.GetBuyersByJobAsync(JobSelected);
            await Task.WhenAll(new Task[] { getBuyerTask, getJobTask });
            var getJob = getJobTask.Result.Value;
            var getBuyers = getBuyerTask.Result.Value;
            var selectedOptions = AllSelectedOptionsData.Where(x => x.MostRecent == true && x.Removed == false).ToList();
            await GenerateDocumentAndDownload(selectedOptions, getJob, getBuyers, $"Construction Release - Options and Selections List.pdf", SelectedApprovedOrAllOptions.Selected);
        }
    }

    async Task DownloadAllOptionsPdf()
    {
        if (AllSelectedOptionsData != null)
        {
            var getJobTask = SubdivisionService.GetLotAsync(JobSelected);
            var getBuyerTask = SelectedOptionsService.GetBuyersByJobAsync(JobSelected);
            var getAllOptionsTask = SelectedOptionsService.GetAllOptionsByJobAsync(JobSelected);
            await Task.WhenAll(new Task[] { getBuyerTask, getJobTask, getAllOptionsTask });
            var getJob = getJobTask.Result.Value;
            var getBuyers = getBuyerTask.Result.Value;
            var allOptions = getAllOptionsTask.Result.Value;
            var selectedOptions = AllSelectedOptionsData.Where(x => x.MostRecent == true && x.Removed == false).ToList();

            await GenerateDocumentAndDownload(selectedOptions, getJob, getBuyers, $"Construction Release - Options and Selections List.pdf", SelectedApprovedOrAllOptions.All, allOptions);
        }
    }

    async Task DownloadApprovedOptionsPdf()
    {
        if (AllSelectedOptionsData != null)
        {
            var getJobTask = SubdivisionService.GetLotAsync(JobSelected);
            var getBuyerTask = SelectedOptionsService.GetBuyersByJobAsync(JobSelected);
            await Task.WhenAll(new Task[] { getBuyerTask, getJobTask });
            var getJob = getJobTask.Result.Value;
            var getBuyers = getBuyerTask.Result.Value;
            //TODO: Is this getting the right options? Approved ones is not really correct, need to sum up the correct qty, etc, if there has been an add and delete
            //Use most recent, but not if it's deleted
            var approvedOptions = AllSelectedOptionsData.Where(x => x.BuilderApproved == 1 && x.MostRecent == true && x.Removed == false).ToList();
            await GenerateDocumentAndDownload(approvedOptions, getJob, getBuyers, $"Construction Release - Options and Selections List.pdf", SelectedApprovedOrAllOptions.Approved);
        }

    }
    async Task GenerateDocumentAndDownload(List<TbBuiltOptionDto> options, JobDto job, List<BuyerDto> buyers, string fileName, SelectedApprovedOrAllOptions selectedApprovedOrAll, List<AvailablePlanOptionDto> allOptions = null)
    {
        byte[] fileData = ERP.Web.DocumentProcessing.GenerateReleasePdf.GenerateReleaseFile(options, job, buyers, selectedApprovedOrAll, allOptions);
        DemoFileExporter.Save(JsRuntime, fileData, "application/pdf", fileName);
    }


    private async Task OnExcelBeforeExport(GridBeforeExcelExportEventArgs args)
    {

        // Customize the Width of the first exported column
        // Customize the Width of the exported column
        // args.Columns[0].Width = "100px";
        // args.Columns[1].Width = "100px";
        // args.Columns[2].Width = "100px";
        // args.Columns[3].Width = "100px";
        // args.Columns[4].Width = "150px";
        // args.Columns[5].Width = "150px";
        // args.Columns[6].Width = "100px";
        // args.Columns[7].Width = "100px";
        // args.Columns[8].Width = "100px";
        // args.Columns[9].Width = "100px";
        // args.Columns[10].Width = "100px";
        // args.Columns[11].Width = "100px";
        // args.Columns[12].Width = "100px";
        // // Change the format of the Price column
        // // BuiltInNumberFormats is part of the Telerik.Documents.SpreadsheetStreaming namespace


        // // Change the format of the ReleaseDate column
        // args.Columns[8].NumberFormat = BuiltInNumberFormats.GetShortDate();
        // args.Columns[9].NumberFormat = BuiltInNumberFormats.GetShortDate();
        // args.Columns[11].NumberFormat = BuiltInNumberFormats.GetShortDate();


        // var hiddenColunnsToAdd = new List<GridExcelExportColumn>()
        // {
        //     new GridExcelExportColumn(){Title = "Lot Section Code", Field = "LotSectionCode" },
        //     new GridExcelExportColumn(){Title = "Lot Swing", Field = "LotSwing" },
        //     new GridExcelExportColumn(){Title = "Garage Orientation", Field = "GarageOrientation.Name" },
        //     new GridExcelExportColumn(){Title = "Lot Size", Field = "LotSize" },
        //     new GridExcelExportColumn(){Title = "Lot Width", Field = "LotWidth" },
        //     new GridExcelExportColumn(){Title = "Phase", Field = "Phase" },
        //     new GridExcelExportColumn(){Title = "Building Num", Field = "BuildingNum" },
        //     new GridExcelExportColumn(){Title = "Stick Num", Field = "StickBuildingNum" },
        //     new GridExcelExportColumn(){Title = "Home Orientation", Field = "HomeOrientation" },
        //     new GridExcelExportColumn(){Title = "Parking Num", Field = "ParkingNum" },
        //     new GridExcelExportColumn(){Title = "Storage Num", Field = "StorageNum" },
        //     new GridExcelExportColumn(){Title = "Notes", Field = "Notes" }
        // };
        // args.Columns.AddRange(hiddenColunnsToAdd);
    }
}
