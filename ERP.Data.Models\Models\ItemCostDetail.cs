﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ItemCostDetail
{
    public int ItemCostDetailId { get; set; }

    public int? SessionId { get; set; }

    public int? MasterItemId { get; set; }

    public string? PhaseCode { get; set; }

    public string? ItemNumber { get; set; }

    public int? SeqNumber { get; set; }

    public string? PeCategoryCode { get; set; }

    public string? Activity { get; set; }

    public string? Releasecode { get; set; }

    public string? SortKey1 { get; set; }

    public string? SortKey2 { get; set; }

    public string? ItemDesc { get; set; }

    public string? ItemNotes { get; set; }

    public int? Waste { get; set; }

    public string? UseWaste { get; set; }

    public double? TakeoffQty { get; set; }

    public string? TakeoffUnit { get; set; }

    public double? OrderQty { get; set; }

    public string? OrderUnit { get; set; }

    public double? OrderLength { get; set; }

    public double? OrderLengthQty { get; set; }

    public double? CnvFctr { get; set; }

    public string? Multdiv { get; set; }

    public string? RndDir { get; set; }

    public double? RndUnit { get; set; }

    public double? PeUnitPrice { get; set; }

    public DateTime? PeUnitPriceDtCg { get; set; }

    public double? PeLumpSumAmount { get; set; }

    public int? SelectedSubNumber { get; set; }

    public string? SubName { get; set; }

    public double? OrderUnitPrice { get; set; }

    public double? OrderAmount { get; set; }

    public string? LumpSum { get; set; }

    public string? Taxable { get; set; }

    public string? TaxGroup { get; set; }

    public string? TaxGroupType { get; set; }

    public double? TaxPercent { get; set; }

    public double? TaxAmount { get; set; }

    public string? JcExtra { get; set; }

    public string? JcExtraDesc { get; set; }

    public string? JcPhase { get; set; }

    public string? JcCategory { get; set; }

    public int? SubNumberWbs { get; set; }

    public string? JcExtraWbs { get; set; }

    public string? JcPhaseWbs { get; set; }

    public string? JcCategoryWbs { get; set; }

    public double? LengthWbs { get; set; }

    public string? MasterItemWbs { get; set; }

    public double? MiscQtyWbs { get; set; }

    public double? MiscLengthWbs { get; set; }

    public string? MiscDescWbs { get; set; }

    public string? PeAssemblyCode { get; set; }

    public string? PeLocation { get; set; }

    public string? TradeName { get; set; }

    public int? WarningCount { get; set; }

    public int? ErrorCount { get; set; }

    public int? CombinedId { get; set; }

    public double? CalcPercent { get; set; }

    public string? ExcludeFromPo { get; set; }

    public int? PeeHeaderId { get; set; }

    public int? EstdetailId { get; set; }

    public string? UseEstCost { get; set; }

    public string? ReissueItem { get; set; }

    public string? VarianceJcCategory { get; set; }

    public int? PoheaderId { get; set; }

    public int? PodetailId { get; set; }

    public string? Errors { get; set; }

    public string? Warnings { get; set; }

    public int? PactivityId { get; set; }

    public int? SupplierContractsId { get; set; }

    public int? SupplierContCoId { get; set; }

    public int? WorksheetId { get; set; }

    public int? CostsId { get; set; }

    public int? CalcBasis { get; set; }

    public string? UseSortKey2 { get; set; }

    public int? VpoNumber { get; set; }

    public string? VpoVendorinvoice { get; set; }

    public string? ElevationCode { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public int? SupplierCostreservationId { get; set; }

    public string? CubitId { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;
}
