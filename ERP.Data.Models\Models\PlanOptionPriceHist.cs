﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class PlanOptionPriceHist
{
    public int HistId { get; set; }

    public int PlanOptionId { get; set; }

    public string? OptionCode { get; set; }

    public string? ModifiedOptionDesc { get; set; }

    public decimal? UnitPrice { get; set; }

    public decimal? UnitCost { get; set; }

    public decimal? UnitQty { get; set; }

    public double? SellingPrice { get; set; }

    public DateTime? SellingPriceStartDate { get; set; }

    public DateTime? SellingPriceEndDate { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public byte[] RecordTimeStampe { get; set; } = null!;
}
