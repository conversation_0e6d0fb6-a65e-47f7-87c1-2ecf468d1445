﻿@page "/masterschedule/"
@inject ScheduleService ScheduleService
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling")]
@using ERP.Web.Components
<PageTitle>Master Schedule</PageTitle>


@if (Schedules == null)
{
    <p><em>Loading...</em></p>
}
else
{
    <TelerikGrid Data=@Schedules
             ConfirmDelete="true"
             ScrollMode="@GridScrollMode.Virtual"
             FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
             Height="1000px" RowHeight="60" PageSize="20"
             Sortable="true"
             Resizable="true"
             @ref="@GridRef">
        <GridColumns>
            <GridColumn Field="JobNumber" Title="Job Number" Editable="false" />
            <GridColumn Field="SiteAddress" Title="Address" Editable="false" />
            <GridColumn Field="Supervisor" Title="Supervisor" />
            <GridColumn Field="Subdivision" Title="Subdivision" />
            <GridColumn Field="UserCreated" Title="Created By" />
            <GridColumn Field="DateCreated" Title="Created Date" DisplayFormat="{0:MM/dd/yyyy}" />
            <GridColumn Field="DateToStart" Title="Start Date" DisplayFormat="{0:MM/dd/yyyy}" />
            <GridColumn Field="BaseStartDate" Title="Base Start" DisplayFormat="{0:MM/dd/yyyy}" />
            <GridColumn Field="BaseEndDate" Title="Base Finish" DisplayFormat="{0:MM/dd/yyyy}" />            
            <GridColumn Field="ActualStartDate" Title="Actual Start" DisplayFormat="{0:MM/dd/yyyy}" />
            <GridColumn Field="ActualEndDate" Title="Actual Finish" DisplayFormat="{0:MM/dd/yyyy}" />
            <GridColumn Field="PlusminusDays" Title="+/1 Days"/>
            <GridColumn Field="BaseDuration" Title="Baseline Total Work Days"  />
            <GridColumn Field="ProjDuration" Title="Projected Total Work Days" />
            <GridColumn Field="BaseCalduration" Title="Baseline Total Calendar Days" />
            <GridColumn Field="ProjCalduration" Title="Projected Total Calendar Days" />
            <GridColumn Field="ActualDuration" Title="Actual Duration" />
        </GridColumns>

        <GridToolBarTemplate>
            <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>
        </GridToolBarTemplate>
    </TelerikGrid>
}


@code {
    private List<ScheduleDto>? Schedules { get; set; }
    private TelerikGrid<ScheduleDto>? GridRef { get; set; }

    protected override async Task OnInitializedAsync()
    {
        Schedules = (await ScheduleService.GetSchedulesAsync()).Value;
    }  
}
