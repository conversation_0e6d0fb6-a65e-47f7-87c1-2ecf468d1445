﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class BCCheckPayments 
{

    public string? SupplierShortName { get; set; }//for BC integration, doesn't have subnumber
    public string? BankAccount { get; set; }

    public string? CheckNumber { get; set; }

    public DateTime? CheckDate { get; set; }

    public double? CheckAmount { get; set; }

    public List<BCPaymentInvoice>? Invoices { get; set; }

    public int? PaymentType { get; set; }

    public string? VoidedPayment { get; set; }

    public double? VoidedAmount { get; set; }

    public DateTime? VoidedDate { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? FoundDuringUpdate { get; set; }

    public string? CreatedBy { get; set; }

}
public class BCPayment : IMapFrom<CheckPayment>
{

    public string? SupplierShortName { get; set; }//for BC integration, doesn't have subnumber
    public string? BankAccount { get; set; }

    public string? CheckNumber { get; set; }

    public DateTime? CheckDate { get; set; }

    public double? CheckAmount { get; set; }

    public string? InvNumber { get; set; }//for BC integration doesn't have poheaderId

    public double? InvAmountPaid { get; set; }

    public string? PoNumber { get; set; }//for BC integration doesn't have poheaderId

    public double? PoAmountPaid { get; set; }

    public int? PaymentType { get; set; }

    public string? VoidedPayment { get; set; }

    public double? VoidedAmount { get; set; }

    public DateTime? VoidedDate { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? FoundDuringUpdate { get; set; }

    public string? CreatedBy { get; set; }

}


public class BCPaymentInvoice
{
    public string? InvNumber { get; set; }//for BC integration doesn't have poheaderId

    public double? InvAmountPaid { get; set; }

    public string? PoNumber { get; set; }//for BC integration doesn't have poheaderId

    public double? PoAmountPaid { get; set; }
}