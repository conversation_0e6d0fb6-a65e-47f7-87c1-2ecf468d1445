﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class UserDto : IMapFrom<User>
{
    public string UserId { get; set; } 

    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    public string? DropdownDisplay { get; set; }//to display name and email as textfield in dropdown

    public string? FullName { get; set; }
    public string? IsActive { get; set; }

    public DateTime? DateInactivated { get; set; }

    public string? UserInactivated { get; set; }

    public string? Notes { get; set; }

    public DateTime? DateStamp { get; set; }

    public string? UserStamp { get; set; }

    public string? EmailAddress { get; set; }

    public string? Supervisor { get; set; }

    public string? Administrator { get; set; }

    public string? IsProtected { get; set; }

    public string? Title { get; set; }

    public string? Extension { get; set; }

    public string? WorkPhone { get; set; }

    public string? MobilePhone { get; set; }

    public int? CorrigoUserId { get; set; }

    public double? ApprovalLimit { get; set; }

    public string? Accesstobmtsupport { get; set; }

    public string? MobileSchedUser { get; set; }

    public string? SuptPortalUser { get; set; }

    public string? VpoPortalUser { get; set; }

    public DateTime Createddatetime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive1 { get; set; }



    public void Mapping(Profile profile)
    {
        profile.CreateMap<UserDto, User>().ReverseMap();
    }
}
