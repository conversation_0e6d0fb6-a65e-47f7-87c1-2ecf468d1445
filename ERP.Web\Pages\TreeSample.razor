﻿@page "/treesample"
@inject OptionService OptionService
@using Telerik.DataSource
@using Telerik.DataSource.Extensions

<div class="container-fluid" style="margin-top:50px">
    <div class="row">
        <TelerikGrid Data="@TreeListData"
                     OnUpdate="UpdateOptionGroupGrid" EditMode="GridEditMode.Popup">
            <DetailTemplate Context="optionGroupContext">
                @{
                    var optionGroup = optionGroupContext as TreeOptionGroup;
                    <TelerikGrid Data="optionGroup.MasterOptions" Pageable="true" PageSize="5" OnUpdate="@((GridCommandEventArgs args) => UpdateMasterOptionGrid(optionGroup, args))" EditMode="GridEditMode.Popup">
                        <DetailTemplate Context="masterOptionContext">
                            @{
                                var masterOptions = masterOptionContext as TreeMasterOption;
                                <TelerikGrid Data="masterOptions.AsmHeaders" Pageable="true" PageSize="5">
                                    <GridColumns>
                                        <GridColumn Field="AsmHeaderId"></GridColumn>
                                        <GridColumn Field="AssemblyCode"></GridColumn>
                                        <GridColumn Field="AssemblyDesc"></GridColumn>
                                    </GridColumns>
                                </TelerikGrid>
                            }
                        </DetailTemplate>
                        <GridColumns>
                            <GridColumn Field="OptionId"></GridColumn>
                            <GridColumn Field="OptionCode"></GridColumn>
                            <GridColumn Field="OptionDesc"></GridColumn>
                            <GridCommandColumn Width="auto">
                                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil">Edit</GridCommandButton>
                            </GridCommandColumn>
                        </GridColumns>
                    </TelerikGrid>
                }
            </DetailTemplate>
            <GridColumns>
                <GridColumn Field="OptionGroupId"></GridColumn>
                <GridColumn Field="OptionGroupName"></GridColumn>
                <GridCommandColumn Width="auto">
                    <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil">Edit</GridCommandButton>
                </GridCommandColumn>
            </GridColumns>
        </TelerikGrid>
    </div>
</div>

@code {
    public List<TreeOptionGroup>? TreeListData { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        var getOptionGroupAndMaster = await OptionService.GetTreeOptionAsync();
        TreeListData = getOptionGroupAndMaster.Value;
    }

    private void UpdateOptionGroupGrid(GridCommandEventArgs args){
        var optionGroupItem = args.Item as TreeOptionGroup;
    }

    private void UpdateMasterOptionGrid(TreeOptionGroup optionGroup, GridCommandEventArgs args){
        var masterItem = args.Item as TreeMasterOption;
        var optionGroupData = optionGroup.MasterOptions;
    } 
}


