﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-ERP.Web-************************************</UserSecretsId>
    <WebProject_DirectoryAccessLevelKey>0</WebProject_DirectoryAccessLevelKey>
    <LangVersion>preview</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="C:\Users\<USER>\OneDrive - Van Metre Companies\Desktop\app.css" Link="app.css" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.1" />
    <PackageReference Include="Azure.Identity" Version="1.12.0" />
    <PackageReference Include="BouncyCastle.Cryptography" Version="2.4.0" />
    <PackageReference Include="ClosedXML" Version="0.101.0" />
    <PackageReference Include="DocuSign.eSign.dll" Version="6.8.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.1" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="7.0.1" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Graph" Version="5.43.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="2.17.1" />
    <PackageReference Include="Microsoft.Identity.Web.DownstreamApi" Version="2.17.1" />
    <PackageReference Include="Microsoft.Identity.Web.GraphServiceClient" Version="2.17.1" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="2.17.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog" Version="5.1.3" />
    <PackageReference Include="NLog.Database" Version="5.1.3" />
    <PackageReference Include="Openize.HEIC" Version="25.2.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.8" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.Formats.Asn1" Version="9.0.4" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="Telerik.DataSource" Version="3.0.2" />
    <PackageReference Include="Telerik.Documents.Core" Version="2024.3.806" />
    <PackageReference Include="Telerik.Documents.Fixed" Version="2024.3.806" />
    <PackageReference Include="Telerik.Documents.ImageUtils" Version="2024.3.806" />
    <PackageReference Include="Telerik.Documents.Spreadsheet" Version="2024.3.806" />
    <PackageReference Include="Telerik.Documents.Spreadsheet.FormatProviders.OpenXml" Version="2024.3.806" />
    <PackageReference Include="Telerik.Documents.SpreadsheetStreaming" Version="2024.3.806" />
    <PackageReference Include="Telerik.UI.for.Blazor" Version="6.2.0" />
    <PackageReference Include="Telerik.Zip" Version="2024.3.806" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ERP.Data.Models\ERP.Data.Models.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="Pages\JobTasks.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\ScheduleCondensed.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
  </ItemGroup>

</Project>
