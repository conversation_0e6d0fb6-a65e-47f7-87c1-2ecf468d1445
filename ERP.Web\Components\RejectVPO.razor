﻿@inject PoService PoService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="300px"
               Height="200px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Reject VPO
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@VPOToReject" OnValidSubmit="@HandleValidRejectSubmit">
            <style>
                .validated {
                    border-color: red;
                }
            </style>
            @if (validation)
            {
                <div class="mb-3">
                    <label class="form-label">Reason for Rejection</label><br />
                    <TelerikTextBox Class="validated" @bind-Value="@VPOToReject.Notes"></TelerikTextBox>
                    <label style="color:red">Reason cannot be empty</label>
                </div>
            }
            else
            {
                <div class="mb-3">
                    <label class="form-label">Reason for Rejection</label><br />
                    <TelerikTextBox @bind-Value="@VPOToReject.Notes"></TelerikTextBox>
                </div>
            }
            <button type="submit" class="btn btn-primary">Reject</button>
            <button type="button" @onclick="CancelReject" class="btn btn-secondary">Cancel</button>
            <div style=@submittingStyle>Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public bool validation { get; set; } = false;
    private string submittingStyle = "display:none";

    [Parameter]
    public PoapprovalDto VPOToReject { get; set; }
    [Parameter]
    public EventCallback<ResponseModel<PoapprovalDto>> HandleRejectSubmit { get; set; }

    public void Show()
    {
        IsModalVisible = true;
        StateHasChanged();
    }

    protected override async Task OnParametersSetAsync()
    {
        // VPOToReject.Notes = "";
    }

    protected override async Task OnInitializedAsync()
    {
        // VPOToReject.Notes = "";
    }

    private async void HandleValidRejectSubmit()
    {
        if (VPOToReject.Notes == "")
        {
            validation = true;
        }
        else
        {
            submittingStyle = "";
            var responseItem = await PoService.RejectVPO(VPOToReject);

            var poHeaderdata = await PoService.GetPoHeaderAsync(VPOToReject.PoheaderId);
            var poHeader = poHeaderdata.Value;

            if (poHeader != null)
            {
                if (poHeader.Postatus == 5)
                {
                    var data = await PoService.GetVPODetailByPOHeaderIdAsync(VPOToReject.PoheaderId);
                    var poDetails = data.Value;
                    if (poDetails != null)
                    {
                        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(poHeader, poDetails);

                        var files = new List<FileModel>();
                        files.Add(new FileModel()
                            {
                                FileData = fileData,
                                FileName = $"{poHeader.Ponumber}.pdf"
                            });
                        var emailModel = new PoEmailModel()
                            {
                                Files = files,
                                Poheader = poHeader,
                                Subject = $"CANCELLED Van Metre Variance Purchase Order: {poHeader.Ponumber}",
                                Body = "The attached variance purchase order is cancelled. Visit the Van Metre Trade Portal (https://trades.vanmetrehomes.com/) for more information"
                            };
                        await PoService.EmailPos(emailModel);
                    }
                }
            }

            submittingStyle = "display:none";
            await HandleRejectSubmit.InvokeAsync(responseItem);
        }
    }
    void CancelReject()
    {
        IsModalVisible = false;
    }
    public void Hide()
    {
        IsModalVisible = false;
    }
}

