﻿
using AutoMapper;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Data;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class TradeController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        public TradeController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
        }
        /// <summary>
        /// update from bc
        /// </summary>
        /// <param name="createSupplier"></param>
        /// <returns></returns>
        [AllowAnonymous]//TODO: oath from bc
        [HttpPost]
        public async Task<IActionResult> CreateSupplierAsync([FromBody] SupplierDto createSupplier)
        {
            try
            {

                var addSupplier = _mapper.Map<Supplier>(createSupplier);
                _context.Suppliers.Add(addSupplier);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<SupplierDto> { IsSuccess = true, Message = "Created Supplier successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<JobDto> { IsSuccess = false, Message = "Failed to create supplier", Value = null });
            }
        }
        /// <summary>
        /// update from bc
        /// </summary>
        /// <param name="createJob"></param>
        /// <returns></returns>
        [AllowAnonymous]//TODO: oath from bc
        [HttpPost]
        public async Task<IActionResult> UpdateSupplierFromBCAsync([FromBody] SupplierDto updateSupplier)
        {
            try
            {
                var findSupplier = _context.Suppliers.SingleOrDefault(x => x.SubNumber == updateSupplier.SubNumber);
                findSupplier.SubName = updateSupplier.SubName;
                findSupplier.SubPhone = updateSupplier.SubPhone;
                findSupplier.SubApNumber = updateSupplier.SubApNumber;
                findSupplier.AbnNumber = updateSupplier.AbnNumber;
                findSupplier.Create2ndLienWaiverHold = updateSupplier.Create2ndLienWaiverHold;
                //TODO: all the fields, what should update
                _context.Suppliers.Update(findSupplier);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<JobDto> { IsSuccess = true, Message = "Created Job successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<JobDto> { IsSuccess = false, Message = "Failed to update Job", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetTradesAsync()
        {
            try
            {
                var trades = await _context.Trades.AsNoTracking().Where(x => x.IsActive == true).Select(x => new TradeSupplierModel()
                {
                    TradeId = x.TradeId,
                    TradeName = x.TradeName,
                    TradeDesc = x.TradeDesc,
                }).ToListAsync();
                return new OkObjectResult(new ResponseModel<List<TradeSupplierModel>> { Value = trades, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<TradeSupplierModel>> { IsSuccess = false, Message = "Failed to get trades", Value = null });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetTradesAndSuppliersAsync()
        {
            try
            {
                var trades = await _context.TradeSuppliers.AsNoTracking().Where(x => x.IsActive == true).Select(x => new TradeSupplierModel()
                {
                    TradeId = x.TradeId,
                    TradeName = x.Trade.TradeName,
                    TradeDesc = x.Trade.TradeDesc,
                    SubName = x.SubNumberNavigation.SubName,
                    SubdivisionId = x.SubdivisionId,
                    DefaultSupplier = x.DefaultSupplier,
                    IsDefault = x.DefaultSupplier == "T" ?  true : false,
                }).Distinct().ToListAsync();
                return new OkObjectResult(new ResponseModel<List<TradeSupplierModel>> { Value = trades, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<TradeSupplierModel>> { IsSuccess = false, Message = "Failed to fetch trades and suppliers", Value = null });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetSuppliersAsync()
        {
            try
            {
                var suppliers = _context.Suppliers.AsNoTracking().Include(x => x.SupplierTradeTypes).Select(x => new TradeSupplierModel()
                {
                    SubName = x.SubName,
                    SubNumber = x.SubNumber,
                    ShortName = x.ShortName,
                    SubAddress = x.SubAddress,
                    SubAddress2 = x.SubAddress2,
                    SubApNumber = x.SubApNumber,
                    SubCity = x.SubCity,
                    SubContact = x.SubContact,
                    SubContact1Pos = x.SubContact1Pos,
                    SubContact2Pos = x.SubContact2Pos,
                    SubContact2 = x.SubContact2,
                    SubContact2Dc = x.SubContact2Dc,
                    SubContact2Email = x.SubContact2Email,
                    SubContact2Isadmin = x.SubContact2Isadmin,
                    SubContact2Mobile = x.SubContact2Mobile,
                    SubContact2MobileSp = x.SubContact2MobileSp,
                    SubContact2Purch = x.SubContact2Purch,
                    SubContact2Sched = x.SubContact2Sched,
                    SubContactIsadmin = x.SubContactIsadmin,
                    SubPhone = x.SubPhone,
                    SubPhone2 = x.SubPhone2,
                    SubNotes = x.SubNotes,
                    SubType1 = x.SubType1,
                    Email = x.Email,
                    Subtaxexempt = x.Subtaxexempt,
                    Subtaxgroup = x.Subtaxgroup,
                    OrderMode = x.OrderMode,
                    Phone = x.Phone,
                    IsActive1 = x.IsActive1 ?? false,
                    Type = string.Join(", ", x.SupplierTradeTypes.Select(y => y.SubType.SubTypeName))
                }).ToList();
                return new OkObjectResult(new ResponseModel<List<TradeSupplierModel>> { Value = suppliers, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<TradeSupplierModel>> { IsSuccess = false, Message = "Failed to get suppliers", Value = null });
            }
        }

        [HttpGet("{supplierNumber}")]
        public async Task<IActionResult> GetSupplierAsync(int supplierNumber)
        {
            try
            {
              //  var supplier = _context.Suppliers.AsNoTracking().Include("SupplierTradeTypes").SingleOrDefault(x => x.SubNumber == supplierNumber);
                //var supplierDto = _mapper.Map<SupplierDto>(supplier);
                //var supplierTypes = supplier.SupplierTradeTypes.ToList();
                var supplier2 = _context.Suppliers.AsNoTracking().Where(x => x.SubNumber == supplierNumber).Select(x => new TradeSupplierModel()
                {
                    SubName = x.SubName,
                    SubNumber = x.SubNumber,
                    ShortName = x.ShortName,
                    SubAddress = x.SubAddress,
                    SubAddress2 = x.SubAddress2,
                    SubApNumber = x.SubApNumber,
                    SubCity = x.SubCity,
                    SubState = x.SubState,
                    SubCountry = x.SubCountry,
                    SubPostcode = x.SubPostcode,
                    SubPhone = x.SubPhone,
                    SubPhone2 = x.SubPhone2,
                    SubPhone3 = x.SubPhone3,
                    SubFax = x.SubFax,
                    SubFax2 = x.SubFax2,
                    SubContact = x.SubContact,
                    SubContact2 = x.SubContact2,
                    SubContact3 = x.SubContact3,
                    SubContact1Pos = x.SubContact1Pos,
                    SubContact2Pos = x.SubContact2Pos,
                    SubContact3Pos = x.SubContact3Pos,
                    SubContactEmail = x.SubContactEmail,
                    SubContact2Email = x.SubContact2Email,
                    SubContact3Email = x.SubContact3Email,
                    SubContactMobile = x.SubContactMobile,
                    SubContact2Mobile = x.SubContact2Mobile,
                    SubContact3Mobile = x.SubContact3Mobile,
                    SubContactDc = x.SubContactDc,
                    SubContact2Dc = x.SubContact2Dc,
                    SubContact3Dc = x.SubContact3Dc,
                    SubContact2Isadmin = x.SubContact2Isadmin,
                    SubContact2MobileSp = x.SubContact2MobileSp,
                    SubContact2Purch = x.SubContact2Purch,
                    SubContact2Sched = x.SubContact2Sched,
                    SubContactIsadmin = x.SubContactIsadmin,
                    SubNotes = x.SubNotes,
                    SubType1 = x.SubType1,
                    SubType2 = x.SubType2,
                    SubType3 = x.SubType3,
                    SubType4 = x.SubType4,
                    EdiOrders = x.EdiOrders,
                    Email = x.Email,
                    Subtaxexempt = x.Subtaxexempt,
                    IsSubtaxexempt = x.Subtaxexempt == "T" ? true : false,
                    Subtaxgroup = x.Subtaxgroup,
                    DiscountDays = x.DiscountDays,
                    DiscountPercent = x.DiscountPercent,
                    PaymentDays = x.PaymentDays,
                    ReceivesForm1099 = x.ReceivesForm1099,
                    IsReceivesForm1099 = x.ReceivesForm1099 == "T" ? true : false,
                    AbnNumber = x.AbnNumber,
                    Retainage = x.Retainage,
                    OrderMode = x.OrderMode,
                    Phone = x.Phone,
                    WarrantyEnabled = x.WarrantyEnabled,
                    IsWarrantyEnabled = x.WarrantyEnabled == "T" ? true : false,
                    SubContactWrty = x.SubContactWrty,
                    SubContactWrtyDc = x.SubContactWrtyDc,
                    SubContactWrtyEmail = x.SubContactWrtyEmail,
                    SubContactWrtyMobile = x.SubContactWrtyMobile,
                    SubContactWrtyMobileSp = x.SubContactWrtyMobileSp,
                    SubFaxWrty = x.SubFaxWrty,
                    SubContactWrtyPos = x.SubContactWrtyPos,
                    SubPhoneWrty = x.SubPhoneWrty,
                    IsActive = x.IsActive,
                    IsActive1 = x.IsActive1 ?? false,
                    Blocked = x.Blocked,
                    BoolBlocked = x.Blocked == true
                }).SingleOrDefault();
                return new OkObjectResult(new ResponseModel<TradeSupplierModel> { Value = supplier2, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Failed to get supplier", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetNextOneTimeBidAsync()
        {
            try
            {
                var suppliers = _context.Suppliers.AsNoTracking().Where(x => x.ShortName.StartsWith("ONETIMEBID")).Select(x => x.ShortName.Remove(0, 10)).ToList();
                var oneTimeBidNumbers = new List<int>();
                foreach(var supplier in suppliers)
                {
                    int number;
                    if(int.TryParse(supplier, out number))
                    {
                        oneTimeBidNumbers.Add(number);
                    }
                }
                int Max = oneTimeBidNumbers.Max();
                return new OkObjectResult(new ResponseModel<int> { Value = Max+1, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<int> { IsSuccess = false, Message = "Failed to get Bid" });
            }
        }

        [HttpGet("{supplierNumber}")]
        public async Task<IActionResult> GetSupplierContactsAsync(int supplierNumber)
        {
            try
            {
                var contacts = await _context.SupplierContacts.AsNoTracking().Where(x => x.SubNumber == supplierNumber && x.IsActive == true).Select(x => new SupplierContactModel()
                {
                    SubNumber = x.SubNumber,
                    ContactId = x.ContactId,
                    ContactKey = x.Contact.ContactKey,
                    FirstName = x.Contact.FirstName,
                    LastName = x.Contact.LastName,
                    Address1 = x.Contact.Address1,
                    Address2 = x.Contact.Address2,
                    City = x.Contact.City,
                    State = x.Contact.State,
                    Postcode = x.Contact.Postcode,
                    WorkPhone = x.Contact.WorkPhone,
                    HomePhone = x.Contact.HomePhone,
                    MobilePhone = x.Contact.MobilePhone,
                    Fax = x.Contact.Fax,
                    Country = x.Contact.Country,
                    Email = x.Contact.Email,
                    SupplierContactId = x.SupplierContactId,
                    SupplierContactIsadmin = x.SupplierContactIsadmin,
                    IsSupplierContactAdmin = x.SupplierContactIsadmin == "T" ?  true: false,
                    SupplierContactNumber = x.SupplierContactNumber,
                    SupplierContactPurch = x.SupplierContactPurch,
                    IsSupplierContactPurch = x.SupplierContactPurch == "T" ? true: false,
                    SupplierContactSched = x.SupplierContactSched,
                    IsSupplierContactSched = x.SupplierContactSched == "T" ? true : false,
                    SupplierContactTitle = x.SupplierContactTitle,

                }).ToListAsync();
                return new OkObjectResult(new ResponseModel<List<SupplierContactModel>> { Value = contacts, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SupplierContactModel>> { IsSuccess = false, Message = "Failed to get supplier contacts", Value = null });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetAllSupplierTypesAsync()
        {
            try
            {
                List<SupplierType> supplierTypes = await _context.SupplierTypes.AsNoTracking().Where(x => x.IsActive == true).ToListAsync();
                return new OkObjectResult(new ResponseModel<List<SupplierType>> { Value = supplierTypes, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SupplierType>> { IsSuccess = false, Message = "Failed to get supplier types", Value = null });
            }
        }

        [HttpGet("{supplierNumber}")]
        public async Task<IActionResult> GetSupplierTypesAsync(int supplierNumber)
        {
            try
            {
                List<SupplierType> supplierTypes = new List<SupplierType>();
                var supplierTradeTypes = await _context.SupplierTradeTypes.AsNoTracking().Where(x => x.SubNumber == supplierNumber && x.IsActive == true).ToListAsync();
                supplierTypes.AddRange(from supplierTradeType in supplierTradeTypes
                                       select _context.SupplierTypes.Where(x => x.SubTypeId == supplierTradeType.SubTypeId && x.IsActive == true).First());

                var supplierTypesList = supplierTypes.Select(x => new SupplierTypeModel
                {
                    SupplierTypeId = x.SubTypeId,
                    SupplierTypeName = x.SubTypeName
                }).ToList();

                return new OkObjectResult(new ResponseModel<List<SupplierTypeModel>> { Value = supplierTypesList, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SupplierTypeModel>> { IsSuccess = false, Message = "Failed to get supplier types", Value = new List<SupplierTypeModel>() });
            }
        }

        [HttpPut]
        public async Task<IActionResult> RefreshSupplierAsync([FromBody] RefreshSupplierAndCostsModel refreshSupplierModel)
        {
            //finds supplier for activity and subdivision, 
            //for use in refresh supplier, to get new one if it's changed
            try
            {
                var findBlockedVendors = _context.Suppliers.AsNoTrackingWithIdentityResolution().Where(x => x.Blocked == true).Select(x => x.SubNumber).Distinct().ToList();
                var findInsuranceExpiredVendors = _context.SupplierInsurances.AsNoTrackingWithIdentityResolution().Where(x => x.InsuranceRequired == 1 && x.PolicyExpirationDate.Date < DateTime.Now.Date && x.IsActive == true).Select(x => x.SubNumber).Distinct().ToList();

                foreach (var estActivityId in refreshSupplierModel.EstActivityIds)
                {
                    var estActivity = await _context.Estactivities.SingleOrDefaultAsync(x => x.EstactivityId == estActivityId);

                    if (estActivity != null)
                    {
                        var pActivity = await _context.Pactivities.AsNoTracking().SingleOrDefaultAsync(x => x.Activity == estActivity.BomClass && x.DivId == 1);
                        if (pActivity != null)
                        {
                            var findSupplier = await _context.PactivityAreaSuppliers.AsNoTracking().FirstOrDefaultAsync(x => x.PactivityId == pActivity.PactivityId && x.SubdivisionId == refreshSupplierModel.SubdivisionId);
                            if (findSupplier != null && findSupplier.SubNumber != null)
                            {
                                estActivity.SelectedVendor = (int)findSupplier.SubNumber;
                                estActivity.DefaultVendor = (int)findSupplier.SubNumber;
                                estActivity.UpdatedBy = User.Identity.Name.Split('@')[0];
                                estActivity.UpdatedDateTime = DateTime.Now; 
                                _context.Estactivities.Update(estActivity);

                                if (refreshSupplierModel.EstActivityIds.Count == 1)
                                {
                                    refreshSupplierModel.SupplierNumber = findSupplier.SubNumber;
                                    refreshSupplierModel.VendorBlocked = findBlockedVendors.Contains((int)findSupplier.SubNumber);
                                    refreshSupplierModel.VendorNoInsurance = findInsuranceExpiredVendors.Contains(findSupplier.SubNumber);
                                }
                            }
                        }  
                    }
                }
                await _context.SaveChangesAsync();

                return new OkObjectResult(new ResponseModel<RefreshSupplierAndCostsModel> { Value = refreshSupplierModel, IsSuccess = true, Message = "Refreshed Supplier" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<RefreshSupplierAndCostsModel> { IsSuccess = false, Message = "Failed to refresh supplier " });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddSupplierContactAsync([FromBody] SupplierContactModel addContact)
        {
            try
            {
                var newContact = new Contact();
                newContact.ContactKey = addContact.ContactKey;
                newContact.FirstName = addContact.FirstName;
                newContact.LastName = addContact.LastName;
                newContact.Address2 = addContact.Address2;
                newContact.Address1 = addContact.Address1;
                newContact.City = addContact.City;
                newContact.State = addContact.State;
                newContact.Postcode = addContact.Postcode;
                newContact.Country = addContact.Country;
                newContact.WorkPhone = addContact.WorkPhone;
                newContact.HomePhone = addContact.HomePhone;
                newContact.MobilePhone = addContact.MobilePhone;
                newContact.Fax = addContact.Fax;
                newContact.Email = addContact.Email;
                newContact.CreatedBy = User.Identity.Name.Split('@')[0];
                newContact.CreatedDateTime = DateTime.Now;
                newContact.IsActive = true;
                _context.Contacts.Update(newContact);
                await _context.SaveChangesAsync();

                var addSupplierContact = new SupplierContact();
                addSupplierContact.ContactId = newContact.ContactId;
                addSupplierContact.SubNumber = (int)addContact.SubNumber;
                addSupplierContact.SupplierContactIsadmin = addContact.IsSupplierContactAdmin ? "T" : "F";
                addSupplierContact.SupplierContactNumber = addContact.SupplierContactNumber;
                addSupplierContact.SupplierContactPurch = addContact.IsSupplierContactPurch ? "T" : "F";
                addSupplierContact.SupplierContactSched = addContact.IsSupplierContactSched? "T" : "F";
                addSupplierContact.SupplierContactTitle = addContact.SupplierContactTitle;
                addSupplierContact.IsActive = true;
                _context.SupplierContacts.Add(addSupplierContact);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<SupplierContactModel> { Value = addContact, IsSuccess = true, Message = "Supplier contact added successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SupplierContactModel> { IsSuccess = false, Message = "Failed to add Supplier Contact" });
            }
        }

        /// <summary>
        /// Update supplier details. This does not change trade/subdivision assignement
        /// </summary>
        /// <param name="updateContact"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<IActionResult> UpdateSupplierContactAsync([FromBody] SupplierContactModel updateContact)
        {
            try
            {
                var findContact = _context.Contacts.Where(x => x.ContactId == updateContact.ContactId).SingleOrDefault();
                findContact.ContactKey = updateContact.ContactKey;
                findContact.FirstName = updateContact.FirstName;
                findContact.LastName = updateContact.LastName;
                findContact.Address2 = updateContact.Address2;
                findContact.Address1 = updateContact.Address1;
                findContact.City = updateContact.City;
                findContact.State = updateContact.State;
                findContact.Postcode = updateContact.Postcode;
                findContact.Country = updateContact.Country;
                findContact.WorkPhone = updateContact.WorkPhone;
                findContact.HomePhone = updateContact.HomePhone;
                findContact.MobilePhone = updateContact.MobilePhone;
                findContact.Fax = updateContact.Fax;
                findContact.Email = updateContact.Email;
                findContact.UpdatedBy = User.Identity.Name.Split('@')[0];
                findContact.UpdatedDateTime = DateTime.Now;
                _context.Contacts.Update(findContact);
                await _context.SaveChangesAsync();

                var findSupplierContact = _context.SupplierContacts.Where(x => x.SupplierContactId == updateContact.SupplierContactId).SingleOrDefault();
                findSupplierContact.SupplierContactIsadmin = updateContact.IsSupplierContactAdmin ? "T" : "F";
                findSupplierContact.SupplierContactPurch = updateContact.IsSupplierContactPurch ? "T" : "F";
                findSupplierContact.SupplierContactNumber = updateContact.SupplierContactNumber;
                findSupplierContact.SupplierContactSched = updateContact.IsSupplierContactSched ? "T" : "F";
                findSupplierContact.SupplierContactTitle = updateContact.SupplierContactTitle;
                findSupplierContact.UpdatedBy = User.Identity.Name.Split('@')[0];
                findSupplierContact.UpdatedDateTime = DateTime.Now;
                _context.SupplierContacts.Update(findSupplierContact);
                await _context.SaveChangesAsync();

                return new OkObjectResult(new ResponseModel<SupplierContactModel> { Value = updateContact, IsSuccess = true, Message = "Supplier contact updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SupplierContactModel> { IsSuccess = false, Message = "Failed to update Supplier Contact" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteSupplierContactAsync([FromBody] int updateContactId)
        {
            try
            {
                var findSupplierContact = _context.SupplierContacts.Where(x => x.SupplierContactId == updateContactId).SingleOrDefault();
                findSupplierContact.IsActive = false;
                findSupplierContact.UpdatedBy = User.Identity.Name.Split('@')[0];
                findSupplierContact.UpdatedDateTime = DateTime.Now;
                _context.SupplierContacts.Update(findSupplierContact);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<SupplierContactModel> {  IsSuccess = true, Message = "Supplier contact deleted successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SupplierContactModel> { IsSuccess = false, Message = "Failed to delete Supplier Contact" });
            }
        }


        [HttpGet("{supplierNumber}")]
        public async Task<IActionResult> GetTradeAndSubdivisionsForSupplierAsync(int supplierNumber)
        {
            try
            {
                var supplier = await _context.TradeSuppliers.AsNoTracking().Where(x => x.SubNumber == supplierNumber && x.IsActive == true).Select(x => new TradeSupplierModel()
                {
                    SubNumber = supplierNumber,
                    TradeId = x.TradeId,
                    SubdivisionId = x.SubdivisionId,
                    TradeName = x.Trade.TradeName,
                    TradeDesc = x.Trade.TradeDesc,
                    SubdivisionName = x.Subdivision.SubdivisionName,
                    DefaultSupplier = x.DefaultSupplier,
                    IsDefault = x.DefaultSupplier == "T"
                }).ToListAsync();
                return new OkObjectResult(new ResponseModel<List<TradeSupplierModel>> { Value = supplier, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<TradeSupplierModel>> { IsSuccess = false, Message = "Failed to get Trade and Subdivisions for Supplier" });
            }
        }

        [HttpGet("{SubdivsionId}/{TradeId}")]
        public async Task<IActionResult> GetSuppliersForSubdivisionAndTradeAsync(int SubdivsionId, int TradeId)
        {
            try
            {
                var supplier = await _context.TradeSuppliers.AsNoTracking().Include("SubNumberNavigation").Where(x => x.SubdivisionId == SubdivsionId && x.TradeId == TradeId && x.IsActive == true).Select(x => new TradeSupplierModel()
                {
                    TradeId = x.TradeId,
                    SubdivisionId = x.SubdivisionId,
                    DefaultSupplier = x.DefaultSupplier,
                    IsDefault = x.DefaultSupplier == "T",
                    SubName = x.SubNumberNavigation.SubName,
                    SubNumber = x.SubNumberNavigation.SubNumber,
                    ShortName = x.SubNumberNavigation.ShortName,
                    SubAddress = x.SubNumberNavigation.SubAddress,
                    SubAddress2 = x.SubNumberNavigation.SubAddress2,
                    SubApNumber = x.SubNumberNavigation.SubApNumber,
                    SubCity = x.SubNumberNavigation.SubCity,
                    SubContact = x.SubNumberNavigation.SubContact,
                    SubContact1Pos = x.SubNumberNavigation.SubContact1Pos,
                    SubContact2Pos = x.SubNumberNavigation.SubContact2Pos,
                    SubContact2 = x.SubNumberNavigation.SubContact2,
                    SubContact2Dc = x.SubNumberNavigation.SubContact2Dc,
                    SubContact2Email = x.SubNumberNavigation.SubContact2Email,
                    SubContact2Isadmin = x.SubNumberNavigation.SubContact2Isadmin,
                    SubContact2Mobile = x.SubNumberNavigation.SubContact2Mobile,
                    SubContact2MobileSp = x.SubNumberNavigation.SubContact2MobileSp,
                    SubContact2Purch = x.SubNumberNavigation.SubContact2Purch,
                    SubContact2Sched = x.SubNumberNavigation.SubContact2Sched,
                    SubContactIsadmin = x.SubNumberNavigation.SubContactIsadmin,
                    SubPhone = x.SubNumberNavigation.SubPhone,
                    SubPhone2 = x.SubNumberNavigation.SubPhone2,
                    SubNotes = x.SubNumberNavigation.SubNotes,
                    SubType1 = x.SubNumberNavigation.SubType1,
                    Email = x.SubNumberNavigation.Email,
                    Subtaxexempt = x.SubNumberNavigation.Subtaxexempt,
                    Subtaxgroup = x.SubNumberNavigation.Subtaxgroup,
                    OrderMode = x.SubNumberNavigation.OrderMode,
                    Phone = x.SubNumberNavigation.Phone
                }).ToListAsync();
                return new OkObjectResult(new ResponseModel<List<TradeSupplierModel>> { Value = supplier, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<TradeSupplierModel>> { IsSuccess = false, Message = "Failed to get Suppliers for Trade and Subdivision" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddSupplierTradeTypeAsync([FromBody] SupplierTradeTypeModel supplierTradeType)
        {
            try
            {
                var checkIfExistingTradeType = await _context.SupplierTradeTypes.SingleOrDefaultAsync(x => x.SubNumber == supplierTradeType.SupplierNumber && x.SubTypeId == supplierTradeType.SupplierTypeId);
                if (checkIfExistingTradeType != null)
                {
                    checkIfExistingTradeType.IsActive = true;
                    checkIfExistingTradeType.UpdatedBy = User.Identity.Name.Split('@')[0];
                    checkIfExistingTradeType.UpdatedDateTime = DateTime.Now;
                    _context.SupplierTradeTypes.Update(checkIfExistingTradeType);
                    await _context.SaveChangesAsync();
                }
                else
                {
                    var newSupplierTradeType = new SupplierTradeType();
                    newSupplierTradeType.SubTypeId = supplierTradeType.SupplierTypeId;
                    newSupplierTradeType.SubNumber = supplierTradeType.SupplierNumber;
                    newSupplierTradeType.IsActive = true;
                    newSupplierTradeType.CreatedBy = User.Identity.Name.Split('@')[0];
                    newSupplierTradeType.CreatedDateTime = DateTime.Now;
                    _context.SupplierTradeTypes.Add(newSupplierTradeType);
                    await _context.SaveChangesAsync();
                }
                return new OkObjectResult(new ResponseModel<SupplierTradeTypeModel> { Value = supplierTradeType, IsSuccess = true, Message = "Supplier Trade type added successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SupplierTradeTypeModel> { IsSuccess = false, Message = "Failed to add Supplier Trade type" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddSupplierTypeAsync([FromBody] string supplierTypeName)
        {
            try
            {
                var checkIfExistingSupplierType = await _context.SupplierTypes.SingleOrDefaultAsync(x => x.SubTypeName == supplierTypeName);
                if (checkIfExistingSupplierType != null)
                {
                    checkIfExistingSupplierType.IsActive = true;
                    checkIfExistingSupplierType.UpdatedBy = User.Identity.Name.Split('@')[0];
                    checkIfExistingSupplierType.UpdatedDateTime = DateTime.Now;
                    _context.SupplierTypes.Update(checkIfExistingSupplierType);
                    await _context.SaveChangesAsync();
                }
                else
                {
                    var newSupplierType = new SupplierType();
                    newSupplierType.SubTypeName = supplierTypeName;
                    newSupplierType.IsActive = true;
                    newSupplierType.CreatedBy = User.Identity.Name.Split('@')[0];
                    newSupplierType.CreatedDateTime = DateTime.Now;
                    _context.SupplierTypes.Add(newSupplierType);
                    await _context.SaveChangesAsync();
                }
                var supplierType = await _context.SupplierTypes.SingleOrDefaultAsync(x => x.SubTypeName == supplierTypeName);
                return new OkObjectResult(new ResponseModel<SupplierType> { Value = supplierType, IsSuccess = true, Message = "Supplier type added successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SupplierType> { IsSuccess = false, Message = "Failed to add Supplier type" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddSupplierToTradeAndSubdivisionAsync([FromBody] TradeSupplierModel addSupplier)
        {
            try
            {
                //If it's default, set any previous supplier for the trade and subdivision to false
                if (addSupplier.IsDefault)
                {
                    var findPreviousDefault = await _context.TradeSuppliers.Where(x => x.TradeId == addSupplier.TradeId && x.SubdivisionId == addSupplier.SubdivisionId && x.DefaultSupplier == "T").ToListAsync();
                    foreach(var supplier in findPreviousDefault)//threre really should only be one
                    {
                        supplier.DefaultSupplier = "F";
                        _context.TradeSuppliers.Update(supplier);
                    }                   
                    await _context.SaveChangesAsync();
                }
               
                var findSupplier = _context.TradeSuppliers.Where(x => x.SubNumber == addSupplier.SubNumber && x.SubdivisionId == addSupplier.SubdivisionId && x.TradeId == addSupplier.TradeId).FirstOrDefault();
                //If supplier already existed, just make sure it's active and set to default as needed, else add it
                if (findSupplier != null)
                {
                    findSupplier.IsActive = true;
                    findSupplier.DefaultSupplier = addSupplier.IsDefault ? "T" : "F";
                    findSupplier.UpdatedBy = User.Identity.Name.Split('@')[0];
                    findSupplier.UpdatedDateTime = DateTime.Now;
                    _context.TradeSuppliers.Update(findSupplier);
                    await _context.SaveChangesAsync();
                }
                else
                {
                    var newSupplier = new TradeSupplier();
                    newSupplier.TradeId = (int)addSupplier.TradeId;
                    newSupplier.SubNumber = addSupplier.SubNumber;
                    newSupplier.SubdivisionId = (int)addSupplier.SubdivisionId;
                    newSupplier.DefaultSupplier = addSupplier.IsDefault ? "T" : "F";
                    newSupplier.CreatedBy = User.Identity.Name.Split('@')[0];
                    _context.TradeSuppliers.Add(newSupplier);
                    await _context.SaveChangesAsync();
                }
                          

                return new OkObjectResult(new ResponseModel<TradeSupplierModel> { Value = addSupplier, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Failed to add Supplier To Trade and Subdivision" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddSupplierToTradeAndSubdivisionsAsync([FromBody] TradeSupplierModel addSupplier)
        {
            try
            {
                string userName = User.Identity.Name.Split('@')[0];
                //If adding as default supplier, set any previous supplier for the trade and subdivision to false
                if (addSupplier.IsDefault)
                {
                    var findPreviousDefault = _context.TradeSuppliers.Where(x => x.TradeId == addSupplier.TradeId && addSupplier.SubdivisionIds.Contains(x.SubdivisionId) && x.DefaultSupplier == "T");
                    findPreviousDefault.ExecuteUpdate(s => s.SetProperty(b => b.DefaultSupplier, "F").SetProperty(b => b.UpdatedBy, userName).SetProperty(b => b.UpdatedDateTime, DateTime.Now));                    
                }

                //If supplier already existed, just make sure it's active and set to default as needed, else add it
                var findSuppliersToUpdate = _context.TradeSuppliers.Where(x => x.SubNumber == addSupplier.SubNumber && addSupplier.SubdivisionIds.Contains(x.SubdivisionId) && x.TradeId == addSupplier.TradeId);
                
                findSuppliersToUpdate.ExecuteUpdate(s => s.SetProperty(b => b.IsActive, true).SetProperty(b=> b.DefaultSupplier, addSupplier.IsDefault ? "T" : "F").SetProperty(b => b.UpdatedBy, userName).SetProperty(b => b.UpdatedDateTime, DateTime.Now));

                var subdivsFound = findSuppliersToUpdate.Select(x => x.SubdivisionId);
                var suppliersToInsert = addSupplier.SubdivisionIds.Where(x => !subdivsFound.Contains(x)).Select(x => new TradeSupplier()
                {
                    SubdivisionId = x,
                    TradeId = (int)addSupplier.TradeId,
                    SubNumber = addSupplier.SubNumber,
                    DefaultSupplier = addSupplier.IsDefault ? "T" : "F",
                    CreatedBy = userName
                });
                //await _context.TradeSuppliers.AddRangeAsync(suppliersToInsert);//TODO: is this too slow
                //await _context.SaveChangesAsync();

                DataTable TradeSupplierToAdd = new DataTable("TradeSupplier");
                DataColumn SubdivisionId = new DataColumn("SubdivisionId", typeof(int));
                TradeSupplierToAdd.Columns.Add(SubdivisionId);
                DataColumn TradeId = new DataColumn("TradeId", typeof(int));
                TradeSupplierToAdd.Columns.Add(TradeId);
                DataColumn SubNumber = new DataColumn("SubNumber", typeof(int));
                TradeSupplierToAdd.Columns.Add(SubNumber);
                DataColumn DefaultSupplier = new DataColumn("DefaultSupplier", typeof(string));
                TradeSupplierToAdd.Columns.Add(DefaultSupplier);
                DataColumn CreatedBy = new DataColumn("CreatedBy");
                TradeSupplierToAdd.Columns.Add(CreatedBy);

                foreach (var tradeSupplierToAdd in suppliersToInsert)
                {
                    TradeSupplierToAdd.Rows.Add(tradeSupplierToAdd.SubdivisionId, tradeSupplierToAdd.TradeId, tradeSupplierToAdd.SubNumber, tradeSupplierToAdd.DefaultSupplier, userName);
                }

                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    using (var bulkCopy = new SqlBulkCopy(connection))
                    {
                        bulkCopy.DestinationTableName = "TRADE_SUPPLIER";
                        bulkCopy.ColumnMappings.Add("SubdivisionId", "SUBDIVISION_ID");
                        bulkCopy.ColumnMappings.Add("TradeId", "TRADE_ID");
                        bulkCopy.ColumnMappings.Add("SubNumber", "SUB_NUMBER");
                        bulkCopy.ColumnMappings.Add("DefaultSupplier", "DEFAULT_SUPPLIER");
                        bulkCopy.ColumnMappings.Add("CreatedBy", "CreatedBy");
                        try
                        {
                            bulkCopy.WriteToServer(TradeSupplierToAdd);
                        }
                        catch (Exception ex)
                        {
                            var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                            _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                        }
                    }
                }

                return new OkObjectResult(new ResponseModel<TradeSupplierModel> { Value = addSupplier, IsSuccess = true, Message = "Added Supplier to Trade and Subdivision successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Failed to add Supplier To Trade and Subdivision" });
            }
        }
        [HttpPost]
        public async Task<IActionResult> AddSupplierAsync([FromBody] SimplerSupplierDto addSupplier)
        {
            //Only "onetimebid" suppliers added here. The rest come from BC. Only name and short name get entered, per Julie
            try
            {
                var newSupplier = new Supplier();                
                newSupplier.ShortName = addSupplier.ShortName;
                newSupplier.SubName = addSupplier.SubName;
                if(newSupplier.SubName != null && newSupplier.SubName.ToLower().Contains("onetimebid"))
                {
                    newSupplier.IsActive = "F";
                    newSupplier.IsActive1 = false;//Julie said ONETIMEBID should be inactive by default
                }
                newSupplier.CreatedBy = User.Identity.Name.Split('@')[0];

                _context.Suppliers.Add(newSupplier);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<SimplerSupplierDto> { Value = addSupplier, IsSuccess = true, Message = "Added Supplier successfully" });
            }
            catch (Exception ex)
            {
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif
                return StatusCode(500, new ResponseModel<SimplerSupplierDto> { IsSuccess = false, Message = "Failed to add Supplier To Trade and Subdivision" });
            }
        }
        /// <summary>
        /// Update supplier details. This does not change trade/subdivision assignement
        /// </summary>
        /// <param name="updateSupplier"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<IActionResult> UpdateSupplierAsync([FromBody] SupplierDto updateSupplier)
        {
            try
            {
               // await UpdateSupplierTypeAsync(updateSupplier);
                var findSupplier = _context.Suppliers.Where(x => x.SubNumber == updateSupplier.SubNumber).SingleOrDefault();                
                findSupplier.ShortName = updateSupplier.ShortName;
                findSupplier.SubName = updateSupplier.SubName;
                findSupplier.AbnNumber = updateSupplier.AbnNumber;
                findSupplier.SubApNumber   = updateSupplier.SubApNumber;
                findSupplier.SubPhone = updateSupplier.SubPhone;
                findSupplier.SubPhone2 = updateSupplier.SubPhone2;
                findSupplier.SubPhoneWrty = updateSupplier.SubPhoneWrty;
                findSupplier.SubPostcode = updateSupplier.SubPostcode;
                findSupplier.SubAddress = updateSupplier.SubAddress;
                findSupplier.SubAddress2 = updateSupplier.SubAddress2;
                findSupplier.SubCity = updateSupplier.SubCity;
                findSupplier.SubContact = updateSupplier.SubContact;
                findSupplier.SubCountry = updateSupplier.SubCountry;
                findSupplier.Phone = updateSupplier.Phone;
                findSupplier.SubState = updateSupplier.SubState;
                findSupplier.SubPhone = updateSupplier.SubPhone;
                findSupplier.SubPhone2 = updateSupplier.SubPhone2;
                findSupplier.SubPhone3 = updateSupplier.SubPhone3;
                findSupplier.SubFax = updateSupplier.SubFax;
                findSupplier.SubContact2 = updateSupplier.SubContact2;
                findSupplier.SubContact3 = updateSupplier.SubContact3;
                findSupplier.SubNotes = updateSupplier.SubNotes;
                findSupplier.SubType1 = updateSupplier.SubType1;
                findSupplier.SubType2 = updateSupplier.SubType2;
                findSupplier.SubType3 = updateSupplier.SubType3;
                findSupplier.SubType4 = updateSupplier.SubType4;
                findSupplier.OrderMode = updateSupplier.OrderMode;
                findSupplier.PaymentDays = updateSupplier.PaymentDays;
                findSupplier.Email = updateSupplier.Email;
                findSupplier.Phone = updateSupplier.Phone;
                findSupplier.DiscountDays = updateSupplier.DiscountDays;
                findSupplier.DiscountPercent = updateSupplier.DiscountPercent;
                findSupplier.ReceivesForm1099 = updateSupplier.IsReceivesForm1099 == true ? "T" : "F";
                findSupplier.Subtaxexempt = updateSupplier.IsSubtaxexempt == true ? "T" : "F";
                findSupplier.Subtaxgroup = updateSupplier.Subtaxgroup;
                findSupplier.WarrantyEnabled = updateSupplier.IsWarrantyEnabled == true ? "T" : "F";                
                findSupplier.SubPhoneWrty = updateSupplier.SubPhoneWrty;
                findSupplier.SubContactWrty = updateSupplier.SubContactWrty;
                findSupplier.SubContactWrtyPos = updateSupplier.SubContactWrtyPos;
                findSupplier.SubContactWrtyEmail = updateSupplier.SubContactWrtyEmail;
                findSupplier.SubContactWrtyMobileSp = updateSupplier.SubContactWrtyMobileSp;
                findSupplier.SubContactWrtyMobile = updateSupplier.SubContactWrtyMobile;
                findSupplier.SubFaxWrty = updateSupplier.SubFaxWrty;
                findSupplier.SubContactWrtyDc = updateSupplier.SubContactWrtyDc;
                findSupplier.SubContactWrtyMobile = updateSupplier.SubContactWrtyMobile;
                findSupplier.IsActive1 = updateSupplier.IsActive1;
                findSupplier.UpdatedBy = User.Identity.Name.Split('@')[0];
                findSupplier.UpdatedDateTime = DateTime.Now;
                _context.Suppliers.Update(findSupplier);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<SupplierDto> { Value = updateSupplier, IsSuccess = true, Message = "Updated Supplier successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SupplierDto> { IsSuccess = false, Message = "Failed to update Supplier" });
            }
        }
        /// <summary>
        /// Update supplier name and is default for the subdivision. This does not change trade/subdivision assignement
        /// </summary>
        /// <param name="updateSupplier"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<IActionResult> UpdateTradeSupplierAsync([FromBody] TradeSupplierModel updateSupplier)
        {
            try
            {
                var findSupplier = _context.Suppliers.Where(x => x.SubNumber == updateSupplier.SubNumber).SingleOrDefault();
               // findSupplier.ShortName = updateSupplier.ShortName;
                findSupplier.SubName = updateSupplier.SubName;                
                findSupplier.UpdatedBy = User.Identity.Name.Split('@')[0];
                findSupplier.UpdatedDateTime = DateTime.Now;
                _context.Suppliers.Update(findSupplier);
                await _context.SaveChangesAsync();

                //update is default if needed
                var findTradeSupplier = _context.TradeSuppliers.Where(x => x.TradeId == updateSupplier.TradeId && x.SubdivisionId == updateSupplier.SubdivisionId && x.SubNumber == updateSupplier.SubNumber).SingleOrDefault();
                updateSupplier.DefaultSupplier = updateSupplier.IsDefault ? "T" : "F";
                if (findTradeSupplier.DefaultSupplier != updateSupplier.DefaultSupplier)
                {
                    if (updateSupplier.DefaultSupplier == "T")
                    {
                        //reset the current true one to false
                        var findDefault = _context.TradeSuppliers.Where(x => x.TradeId == updateSupplier.TradeId && x.SubdivisionId == updateSupplier.SubdivisionId && x.DefaultSupplier == "T").ToList();
                        foreach (var defaultSupplier in findDefault)//there really should be only one, but no guarantee
                        {
                            defaultSupplier.DefaultSupplier = "F";
                            _context.TradeSuppliers.Update(defaultSupplier);
                        }

                        findTradeSupplier.DefaultSupplier = "T";
                    }
                    else
                    {
                        findTradeSupplier.DefaultSupplier = "F";
                    }
                    _context.TradeSuppliers.Update(findTradeSupplier);
                    await _context.SaveChangesAsync();
                }

                return new OkObjectResult(new ResponseModel<TradeSupplierModel> { Value = updateSupplier, IsSuccess = true, Message = "Updated Trade Supplier successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Failed to update Trade Supplier " });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeleteSupplierAsync([FromBody] TradeSupplierModel updateSupplier)
        {
            try
            {
                var findSupplier = _context.Suppliers.Where(x => x.SubNumber == updateSupplier.SubNumber).SingleOrDefault();
                findSupplier.IsActive = "F";
                findSupplier.IsActive1 = false;
                findSupplier.UpdatedBy = User.Identity.Name.Split('@')[0];
                findSupplier.UpdatedDateTime = DateTime.Now;
                _context.Suppliers.Update(findSupplier);
                await _context.SaveChangesAsync();

                var findTradeSuppliers = _context.TradeSuppliers.Where(x => x.SubNumber == updateSupplier.SubNumber);
                await findTradeSuppliers.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false));
                return new OkObjectResult(new ResponseModel<TradeSupplierModel> { Value = updateSupplier, IsSuccess = true, Message = "Deleted Supplier successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Failed to delete Supplier " });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteSupplierTradeTypeAsync([FromBody] SupplierTradeTypeModel supplierTradeTypeModel)
        {
            try
            {
                var findSupplierTradeType = _context.SupplierTradeTypes.Where(x => x.SubNumber == supplierTradeTypeModel.SupplierNumber && x.SubTypeId == supplierTradeTypeModel.SupplierTypeId).SingleOrDefault();
                findSupplierTradeType.IsActive = false;
                findSupplierTradeType.UpdatedBy = User.Identity.Name.Split('@')[0];
                findSupplierTradeType.UpdatedDateTime = DateTime.Now;
                _context.SupplierTradeTypes.Update(findSupplierTradeType);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<SupplierTradeTypeModel> { Value = supplierTradeTypeModel, IsSuccess = true, Message = "Deleted Supplier Trade Type successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SupplierTradeTypeModel> { IsSuccess = false, Message = "Failed to delete Supplier Trade Type" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteSupplierFromTradeAsync([FromBody] AssignTradeSupplierModel updateSupplier)
        {
            try
            {
                var findSupplier = _context.TradeSuppliers.Where(x => x.SubNumber == updateSupplier.SubNumber && x.SubdivisionId == updateSupplier.SubdivisionId && x.TradeId == updateSupplier.TradeId && x.IsActive == true).SingleOrDefault();
                findSupplier.IsActive = false;
                findSupplier.UpdatedBy = User.Identity.Name.Split('@')[0];
                findSupplier.UpdatedDateTime = DateTime.Now;
                _context.TradeSuppliers.Update(findSupplier);
                await _context.SaveChangesAsync();                
                return new OkObjectResult(new ResponseModel<AssignTradeSupplierModel> { Value = updateSupplier, IsSuccess = true, Message = "Deleted Supplier from Trade successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<AssignTradeSupplierModel> { IsSuccess = false, Message = "Failed to delete Supplier from Trade " });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddTradeAsync([FromBody] TradeSupplierModel tradeToAdd)
        {
            try
            {
                //add trade creates default activity, which creates default item
                var addTrade = new Trade();
                addTrade.IsActive = true;
                addTrade.TradeDesc = tradeToAdd.TradeDesc;
                addTrade.TradeName = tradeToAdd.TradeName;
                addTrade.CreatedBy = User.Identity.Name.Split('@')[0];
                addTrade.CreatedDateTime = DateTime.Now;
                _context.Trades.Add(addTrade);
                await _context.SaveChangesAsync();


                //create the default bom class
                var addBomClass = new BomClass();
                addBomClass.IsActive = true;
                addBomClass.BomClass1 = tradeToAdd.TradeName;//???
                addBomClass.DefaultPhaseCode = tradeToAdd.TradeName;//TODO: what should this be
                addBomClass.CreatedBy = User.Identity.Name.Split('@')[0];
                addBomClass.CreatedDateTime = DateTime.Now;
                _context.BomClasses.Add(addBomClass);
                await _context.SaveChangesAsync();

                //then add the pactivity
                var addActivity = new Pactivity();
                addActivity.DivId = 1;//This is default for now, since we don't use divisions
                addActivity.Releasecode = tradeToAdd.TradeName.Substring(0, 3);//TODO: check
                addActivity.IsActive = true;
                addActivity.BomClassId = addBomClass.BomClassId;
                addActivity.Activity = tradeToAdd.TradeName;//WMS sets default activity is same as trade name
                addActivity.TradeId = addTrade.TradeId;
                addActivity.Description = tradeToAdd.TradeDesc;
                addActivity.Taxable = "T";//this seems to be the default WMS puts in 
                addActivity.IncludeSelectionsOnPo = "F";

                // addActivity.Notes = tradeToAdd.Notes;
                addActivity.CreatedBy = User.Identity.Name.Split('@')[0];
                addActivity.CreatedDateTime = DateTime.Now;
                _context.Pactivities.Add(addActivity);
                await _context.SaveChangesAsync();

                //insert new phase
                var newPhase = new MasterItemPhasis()
                {
                    PhaseDesc = tradeToAdd.TradeName + " " + tradeToAdd.TradeName,//WMS Seems to duplicate the trade name for the phase??
                    PhaseCode = tradeToAdd.TradeName?.Substring(0, 3),
                    CreatedBy = User.Identity.Name.Split('@')[0]
                };

                _context.MasterItemPhases.Add(newPhase);
                await _context.SaveChangesAsync();

                var addDefaultItem = new MasterItem()
                {
                    MasterItemPhaseId = newPhase.MasterItemPhaseId,
                    BomClassId = addBomClass.BomClassId,
                    OrderUnit = "LS",//These seem to be defaut in WMS
                    TakeoffUnit = "LS",
                    Taxable = "T",
                    PlanSpecific = "T",//This indicates Lump Sum item
                    RndDir = "N",
                    Multdiv = "D",
                    ItemNumber = tradeToAdd.TradeName?.Substring(0, 3),//OR should this be the three digit code?
                    ItemDesc = tradeToAdd.TradeDesc,//TODO: what should this be
                    CreatedBy = User.Identity.Name.Split('@')[0],
                    CreatedDateTime = DateTime.Now,
                };
                _context.MasterItems.Add(addDefaultItem);
                await _context.SaveChangesAsync();


                return new OkObjectResult(new ResponseModel<TradeSupplierModel> { Value = tradeToAdd, IsSuccess = true, Message = "Added Trade successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Failed to add Trade" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateTradeAsync([FromBody] TradeSupplierModel item)
        {
            try
            {
                var findTrade = _context.Trades.SingleOrDefault(x => x.TradeId == item.TradeId);
                findTrade.IsActive = true;
                findTrade.TradeDesc = item.TradeDesc;
                findTrade.TradeName = item.TradeName;
                findTrade.UpdatedBy = User.Identity.Name.Split('@')[0];
                findTrade.UpdatedDateTime = DateTime.Now;
                _context.Trades.Update(findTrade);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<TradeSupplierModel> { Value = item, IsSuccess = true, Message = "Updated Trade successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Failed to update Trade" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteTradeAsync([FromBody] TradeSupplierModel item)
        {
            try
            {
                var findTrade = _context.Trades.SingleOrDefault(x => x.TradeId == item.TradeId);
                findTrade.IsActive = false;
                findTrade.UpdatedDateTime = DateTime.Now;
                findTrade.UpdatedBy = User.Identity.Name.Split('@')[0];
                _context.Trades.Update(findTrade);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<TradeSupplierModel> { Value = item, IsSuccess = true, Message = "Deleted Trade successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Failed to delete Trade" });
            }
        }
    }
}
