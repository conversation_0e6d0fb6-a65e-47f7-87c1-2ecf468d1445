﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class PlanOptionPriceCostModel
{
    public int PlanOptionId { get; set; }
    public decimal? Price { get; set; }
    public decimal? Cost { get; set; }

    public int? PhasePlanId { get; set; }

    public int? MasterPlanId { get; set; }

    public int? HomeAreaId { get; set; }

    public int? SsAvailablePlanOptionId { get; set; }

    public string? Phase { get; set; }

    public int? SubdivisionId { get; set; }

    public int? MasterOptionId { get; set; }

    public int? MasterItemId { get; set; }

    public int? SsMasterOptionId { get; set; }

    public int? BuildInStageId { get; set; }

    public int? CutoffStageId { get; set; }

    public int? OptionGroupId { get; set; }

    public int? UnitMeasureId { get; set; }

    public int? OptionTypeId { get; set; }

    public string? OptionCodeSsold { get; set; }

    public string? OptionCode { get; set; }

    public string? ModifiedOptionDesc { get; set; }

    public string? OptionLongDesc { get; set; }

    public string? Restrictions { get; set; }

    public decimal? UnitPrice { get; set; }

    public decimal? UnitCost { get; set; }

    public decimal? UnitQty { get; set; }

    public bool? FloorOption { get; set; }

    public bool? AddPriceToBase { get; set; }

    public bool? PrintOption { get; set; }

    public bool CustChoiceReq { get; set; }

    public bool AgentModType { get; set; }

    public bool AgentModQty { get; set; }

    public bool AgentModPrice { get; set; }

    public bool Elevation { get; set; }

    public int? RoomPlanId { get; set; }

    public string? DepositDesc { get; set; }

    public decimal? Amount { get; set; }

    public short? DaysFromTrigger { get; set; }

    public int? DepositTypeId { get; set; }

    public string? BeforeAfter { get; set; }

    public int? TriggerEventId { get; set; }

    public int? ConstrStageId { get; set; }

    public int? EscrowStageId { get; set; }

    public bool DollarAmt { get; set; }

    public bool ActiveDepositSched { get; set; }

    public int? SqFt { get; set; }

    public int? SubNumber { get; set; }

    public int? MarginType { get; set; }

    public double? MarginPercent { get; set; }

    public double? MarginMarketValue { get; set; }

    public double? MarginLumpSum { get; set; }

    public double? LastCost { get; set; }

    public double? LastMargin { get; set; }

    public double? LastSelling { get; set; }

    public DateTime? LastDate { get; set; }

    public double? CurrCost { get; set; }

    public double? CurrMargin { get; set; }

    public double? CurrSelling { get; set; }

    public DateTime? CurrDate { get; set; }

    public double? NextCost { get; set; }

    public double? NextMargin { get; set; }

    public double? NextSelling { get; set; }

    public DateTime? NextDate { get; set; }

    public int? AreaOptionSource { get; set; }

    public string? AssociatedEstimate { get; set; }

    public int? WarningCount { get; set; }

    public string? IsStandard { get; set; }

    public string? IncludeInBase { get; set; }

    public string? HasEstimate { get; set; }

    public string? IsElevation { get; set; }

    public bool? IsActive { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }
    public bool SendToSalesCenter { get; set; }

}
