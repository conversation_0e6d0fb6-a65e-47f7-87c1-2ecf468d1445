﻿
using AutoMapper;
using ERP.API.Utilities;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NLog;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class MaterialColorDocumentController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private readonly Email _email;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        public MaterialColorDocumentController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _email = new Email(configuration);
        }

        [HttpPost]
        public async Task<IActionResult> UploadDocument([FromForm] DocumentUploadModel model)
        {
            try
            {
                var getType = _context.Doctypes.SingleOrDefault(x => x.DoctypeId == model.DocTypeId)?.Doctype1 ?? "";
                var response = await BlobStorage.UploadFileToBlobAsync(model.files, model.FolderName, model.FileName, getType);
                //containers ("folders") named as "materialcolorimages" for material and color predefined combinations
                var nameContents = model.FileName.Split('|');
                int.TryParse(nameContents[0], out int materialId);
                int.TryParse(nameContents[1], out int colorSchemeId);

                var materialColorPredefined = await _context.MaterialColorPredefineds.SingleOrDefaultAsync(x => x.MaterialId== (int)materialId && x.ColorSchemeId == (int)colorSchemeId);
                if (materialColorPredefined != null)
                {
                    materialColorPredefined.ImageLink = response.FileName;
                    _context.MaterialColorPredefineds.Update(materialColorPredefined);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<bool>() { IsSuccess = false, Message = "failed upload material color image", Value = false });
            }
            return Ok(new ResponseModel<bool>() { Value = true, IsSuccess = true });
        }

        [HttpPut]
        public async Task<IActionResult> DeleteDocumentAsync([FromBody] MaterialColorPredefinedDto item)
        {
            try
            {
                var materialColorItem = await _context.MaterialColorPredefineds.SingleOrDefaultAsync(x => x.MaterialId == (int)item.MaterialId && x.ColorSchemeId == (int)item.ColorSchemeId);
                if (materialColorItem != null)
                {
                    var fileName = materialColorItem.ImageLink;
                    await BlobStorage.MoveFileToInactiveFolderBlobAsync("materialcolorimages", fileName);
                    materialColorItem.ImageLink = null;
                    _context.MaterialColorPredefineds.Update(materialColorItem);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MaterialColorPredefinedDto>() { IsSuccess = false, Message = "Failed to delete image", Value = null });
            }
            return Ok(new ResponseModel<MaterialColorPredefinedDto>() { Value = item, IsSuccess = true, Message = "Deleted Image successfully" });
        }

        [HttpPost]
        public async Task<IActionResult> DownloadDocument([FromBody] DocumentUploadModel documentDetails)
        {
            try
            {
                var folderName = documentDetails.FolderName;
                var filename = documentDetails.FileName;
                var docDownload = BlobStorage.DownloadFileBytes(filename, folderName);
                return Ok(new ResponseModel<byte[]>() { Value = docDownload, IsSuccess = true });//returns the byte array, won't work for large files
                                                                                                 //  return File(_service.CreateCostsExport(costs), contentType, fileName);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<byte[]>() { IsSuccess = false, Message = "failed to download document", Value = null });
            }
            return BadRequest();
        }
    }
}

