﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Jccostcode
{
    public int JccostcodeId { get; set; }

    public string? CostCode { get; set; }

    public string? CcDescription { get; set; }

    public int? AcntDbId { get; set; }

    public int? IgBusinessRuleId { get; set; }

    public int? AcntUomId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<Pactivity> Pactivities { get; set; } = new List<Pactivity>();
}
