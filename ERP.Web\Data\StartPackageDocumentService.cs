﻿using Azure.Core;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.ExtendedProperties;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using NLog;
using System.Text;
using Telerik.Blazor.Components.FileSelect;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class StartPackageDocumentService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public StartPackageDocumentService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }


        public async Task<ResponseModel<string>> GetTokenAsync()
        {
            var scopes = _configuration.GetSection("DownstreamApi:Scopes").Value;
            var scopesArray = new string[] { scopes};
            var token = await _tokenAcquisition.GetAccessTokenForUserAsync(scopesArray, tokenAcquisitionOptions: new TokenAcquisitionOptions { ForceRefresh = true});
            //ForceRefresh = true ignores the token cache
            //this is wrong, but seems to fix errors about multiple tokens in the cache
            return new ResponseModel<string>() { Value = token, IsSuccess = true };
        }

        
        public async Task<ResponseModel<JobAttachmentDto>> AddStartPackageDocumentAsync(JobAttachmentDto document)
        {
            var returnDocument = new ResponseModel<JobAttachmentDto>();
            try
            {

                returnDocument = await _downstreamAPI.PostForUserAsync<JobAttachmentDto, ResponseModel<JobAttachmentDto>>(
                            "DownstreamApi", document,
                             options =>
                             {
                                 options.RelativePath = "api/startpackagedocument/addstartpackagedocument/";
                             });


            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return returnDocument;
        }
        public async Task<ResponseModel<SchePackageItemDto>> DeleteDocumentAsync(SchePackageItemDto document)
        {
            var returnDocument = new ResponseModel<SchePackageItemDto>();
            try
            {

                returnDocument = await _downstreamAPI.PutForUserAsync<SchePackageItemDto, ResponseModel<SchePackageItemDto>>(
                            "DownstreamApi", document,
                             options =>
                             {
                                 options.RelativePath = "api/startpackagedocument/deletedocument/";
                             });


            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return returnDocument;
        }

        public async Task<ResponseModel<byte[]>> DownloadDocumentAsync(DocumentUploadModel documentDetails)
        {
            try
            {

                var response = await _downstreamAPI.PostForUserAsync<DocumentUploadModel, ResponseModel<byte[]>>(
                           "DownstreamApi", documentDetails,
                            options => {
                                options.RelativePath = "api/startpackagedocument/DownloadDocument/";
                            });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<byte[]>() {  Value = new byte[0], IsSuccess = true };

        }

    }
}
