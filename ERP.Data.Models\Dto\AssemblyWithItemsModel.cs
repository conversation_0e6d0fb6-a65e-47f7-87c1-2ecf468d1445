﻿namespace ERP.Data.Models.Dto
{
    public class ItemModel
    {
        public AssemblyWithItemsModel ParentNode { get; set; }
        public int MasterPlanId { get; set; }
        public int ItemId { get; set; }
        public string? ItemDesc { get; set; }
        public ModelManagerItemModel? MasterItem { get; set; }
    }

    public class AssemblyWithItemsModel
    {
        public PlanWithAssembliesModel ParentNode { get; set; }
        public int AssemblyHdrId { get; set; }
        public int MasterPlanId { get; set; }
        public string? AssemblyDesc { get; set; }
        public string? AssemblyCode { get; set; }
        public List<ItemModel>? Items { get; set; }
        public AsmHeader Assembly { get; set; }
    }
    public class PlanWithAssembliesModel
    {
        public int SubdivisionId { get; set; }
        public int PlanId { get; set; }
        public string? PlanName { get; set; }
        public List<AssemblyWithItemsModel>? Assemblies { get; set; }
    }


    public class ActivityWithItemsModel
    {
        public TradeWithActivitiesModel ParentNode { get; set; }
        public int ActivityId { get; set; }
        public int TradeId { get; set; }
        public string? ActivityDesc { get; set; }
        public List<ActivityItemModel>? Items { get; set; }
    }

    public class TradeWithActivitiesModel
    {
        public int TradeId { get; set; }
        public string? TradeName { get; set; }
        public List<ActivityWithItemsModel>? Activities { get; set; }
    }

    public class ActivityItemModel
    {
        public ActivityWithItemsModel ParentNode { get; set; }
        public int ItemId { get; set; }
        public string? ItemDesc { get; set; }
        public ModelManagerItemModel? MasterItem { get; set; }
    }

    public class OptionItemModel
    {
        public OptionWithItemsModel ParentNode { get; set; }
        public int ItemId { get; set; }
        public string? ItemDesc { get; set; }
        public ModelManagerItemModel? MasterItem { get; set; }
    }
    public class GroupWithOptionsModel
    {
        public int GroupId { get; set; }
        public string? GroupName { get; set; }
        public List<OptionWithItemsModel>? Options { get; set; }
    }

    public class OptionWithItemsModel
    {
        public GroupWithOptionsModel ParentNode { get; set; }
        public int MasterOptionId { get; set; }
        public int? AsmHeaderId { get; set; }
        public string? OptionDesc { get; set; }
        public List<OptionItemModel>? Items { get; set; }
    }
}
