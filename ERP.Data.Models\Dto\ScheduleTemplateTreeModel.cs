﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public record ScheduleTemplateTreeModel
    {
        //TODO: make an interface instead
        public string? TestField { get; set; }
        public DateTime? CompletedDate { get; set; }
        public Guid Id { get; set; }
        public Guid? ParentId { get; set; }
        public bool HasChildren { get; set; }
        public string? FolderName { get; set; }//to create an addition layer
        public string? Predecessors { get; set; }//TODO: fix so it's in the activity
        public List<int>? PredecessorIds { get; set; }
        public List<ScheduleSactivityPredDto>? ScheduleSactivityPreds {get; set;}
        public string? Links { get; set; }//TODO: fix so it's in the activity
        public List<int>? SchedSactivityLinkIds { get; set; }
        public List<ScheduleTemplateTreeModel>? Children { get; set; }
        public TemplateSactivityDto? TemplateSactivity { get; set; }
        public TemplateMilestoneDto? TemplateMilestone { get; set; }
        public ScheduleSactivityDto? ScheduleSactivity { get; set; }
        public int? ScheduleSactivityId { get; set; }
        public ScheduleMilestoneDto? ScheduleMilestone { get; set; }
        public SactivityDto? Sactivity { get; set; }       
        public MilestoneDto? Milestone { get; set; }
        public string? MilestoneName { get; set; }
        public string? ActivityName { get; set; }
        public bool SchedulePublished { get; set; }
        public bool SupplierEditable { get; set; }  //sactivity supplier will be editable if no issued pos
        public double TotalPOAmount { get; set; }
        public bool IsAllDay { get; set; } = true;
        public string? DragClue { get; set; }
        public List<int?> SactivityIds { get; set; } = new List<int?>();
        public string? SearchTags { get; set; } // to make search work for children in TreeList
        public string? PurchasingActivities { get; set; }
    }
}
