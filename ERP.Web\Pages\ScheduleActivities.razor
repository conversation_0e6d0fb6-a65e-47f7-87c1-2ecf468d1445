﻿@page "/scheduleactivities"

@inject ScheduleService ScheduleService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector, ConstructionManager")]
@using ERP.Data.Models.Dto
@using ERP.Data.Models
@using Telerik.DataSource.Extensions
@using System.Collections.ObjectModel

<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }
    /*.mytreeclass .k-drag-cell {
            display:none;
        }*/
/*    .mytreeclass .k-table-th.k-header.k-drag-cell{
        visibility: collapse;
    }
    .mytreeclass .k-drag-cell.k-touch-action-none{
        visibility: collapse;

    }*/
    .mytreeclass .k-drag-col{
        visibility: collapse;
    }
    .mytreeclass .k-header {
        min-width: 200px;
    }

    .mytreeclass  {
       overflow-x: auto;
    }
    .k-drag-clue {
        color: white;
    }

    .k-icon.k-icon-with-modifier {
        display: none;
    }

    .SubdivisionTable .k-hierarchy-cell {
        display: none;
    }
    
    .k-splitbar {
        width: 15px;
        color: black;
        padding-left:5px;
        padding-right:5px;
    }

    .pane {
        padding: 8px;
        overflow-x: auto;
    }

    .k-splitter .k-pane {
        overflow-x: auto !important;
    }

    .k-splitter-horizontal{
        overflow-x: auto !important;
    }
    .page-wrapper .page-content{     
       /* padding-right: 100px !important;
        margin-right: 100px !important;*/ 
/*        width: 95% !important;*/
    }
    .page-wrapper{
       /* padding-right: 100px !important;
        margin-right: 100px !important;
            width: 80% !important;*/
    }
    .k-table-td{
        padding: 4px !important;
        font-size: 11px;
    }

    .k-table-td { /*these settings are to prevent the 0 width columns from affecting the column height*/
        padding: 4px !important;
        font-size: 11px;
        height: 30px !important;
        white-space: nowrap !important; 
        overflow: visible;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>

   <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Schedule Activities</h7>
                </div>
            </div>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<TelerikTooltip TargetSelector=".tooltip-target" />
    <div class="row">
    <TelerikGrid Data=@AllSActivities
                 ConfirmDelete="true"
                 OnUpdate="@UpdateActivity"
                 OnEdit="@EditActivity"
                 OnDelete="@DeleteActivity"
                 OnCreate="@CreateActivity"
                 EditMode="@GridEditMode.Inline"
                 ScrollMode="@GridScrollMode.Virtual"
                 FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                 Height="1000px" RowHeight="60" PageSize="20"
                 Sortable="true"
                 Resizable="true"
                 Reorderable="true"
                 RowDraggable="true"
                 OnRowDrop="@((GridRowDropEventArgs<SactivityDto> args) => OnRowDropHandler(args))">
        <GridColumns>
            <GridColumn Field="ActivityName" Title="Activity Name" Editable="true" /> 
            <GridColumn Field="Trade.TradeName" Title="Trade" Editable="true">
                <EditorTemplate>
                    @{
                        ItemToEdit = context as SactivityDto;
                        <TelerikDropDownList @bind-Value="@ItemToEdit.TradeId"
                                             Data="@AllTrades"
                                             ScrollMode="@DropDownScrollMode.Virtual"
                                             ItemHeight="40"
                                             PageSize="20"
                                             TextField="TradeName"
                                             ValueField="TradeId"
                                             DefaultText="Select Trade"
                                             Filterable="true"
                                             FilterOperator="StringFilterOperator.Contains"
                                             Width="100%">
                        </TelerikDropDownList>
                    }
                </EditorTemplate>
            </GridColumn> 
            <GridColumn Field="Seq" Title="Sequence" Editable="false" />
            <GridColumn Field="DefaultDuration" Title="Default Duration" Editable="true" />
            <GridColumn Field="DefaultLagtime" Title="Default Lagtime" Editable="true" />
           @*  <GridColumn Field="Workdays" Title="Workdays" Editable="true" /> *@
            <GridCommandColumn>
                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true" Class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">Update</GridCommandButton>
                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
            </GridCommandColumn>
        </GridColumns>

        <GridToolBarTemplate>            
            <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>
            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add</GridCommandButton>
        </GridToolBarTemplate>
    </TelerikGrid>

    </div>

@code {

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    public List<SactivityDto>? AllSActivities { get; set; }
    public List<TradeDto>? AllTrades { get; set; }
    public SactivityDto? ItemToEdit {get; set; }
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        AllSActivities = (await ScheduleService.GetSactivitiesAsync()).Value;
        AllTrades = (await ScheduleService.GetTradesAsync()).Value;
    }

    void EditActivity(GridCommandEventArgs args)
    {
        SactivityDto item = (SactivityDto)args.Item;
    }

    async Task UpdateActivity(GridCommandEventArgs args)
    {
        SactivityDto item = (SactivityDto)args.Item;
        var updateResponse = await ScheduleService.UpdateActivityAsync(item);
        AllSActivities = (await ScheduleService.GetSactivitiesAsync()).Value;
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
    }

    async Task DeleteActivity(GridCommandEventArgs args)
    {
        SactivityDto item = (SactivityDto)args.Item;
        var deleteResponse = await ScheduleService.DeleteActivityAsync(item);
        AllSActivities = (await ScheduleService.GetSactivitiesAsync()).Value;
        ShowSuccessOrErrorNotification(deleteResponse.Message, deleteResponse.IsSuccess);
    }

    async Task CreateActivity(GridCommandEventArgs args)
    {
        SactivityDto item = (SactivityDto)args.Item;
        var addResponse = await ScheduleService.AddActivityAsync(item);
        AllSActivities = (await ScheduleService.GetSactivitiesAsync()).Value;
        ShowSuccessOrErrorNotification(addResponse.Message, addResponse.IsSuccess);
    }
    private async Task OnRowDropHandler(GridRowDropEventArgs<SactivityDto> args)
    {
        var initialItemIndex = AllSActivities.IndexOf(args.Item);
        AllSActivities.Remove(args.Item);

        var destinationItemIndex = AllSActivities.IndexOf(args.DestinationItem);

        if (args.DropPosition == GridRowDropPosition.After)
        {
            destinationItemIndex++;
        }

        AllSActivities.Insert(destinationItemIndex, args.Item);
        foreach(var item in AllSActivities)
        {
            item.Seq = AllSActivities.IndexOf(item);
        }        
        var updateResponse = await ScheduleService.UpdateActivitiesSeqAsync(AllSActivities);
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
    }

    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
