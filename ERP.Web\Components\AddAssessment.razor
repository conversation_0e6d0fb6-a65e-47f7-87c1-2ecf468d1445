﻿@using ERP.Data.Models;
@inject HOAService HOAService
@using ERP.Data.Models.Dto;
<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <h7 class="page-title" style="font-weight:bold">Add Assessment</h7>
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@AssessmentToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />

            <div class="mb-3">
                <label class="form-label">HOA</label><br />
                <TelerikDropDownList Data="@associations" TextField="AssociationName" ValueField="HoaId" @bind-Value="@AssessmentToAdd.HoaId"></TelerikDropDownList>
                <ValidationMessage For="@(() => AssessmentToAdd.HoaId)" />
            </div>
            @if(AssessmentToAdd.HoaId == 0)
            {
                <div class="mb-3">
                    <label class="form-label">New HOA</label><br />
                    <TelerikTextBox @bind-Value="@AssessmentToAdd.Hoa.AssociationName"></TelerikTextBox>
                </div>
                <div class="mb-3">
                    <label class="form-label">New HOA Category (HOA1, HOA2, etc)</label><br />
                    <TelerikTextBox @bind-Value="@AssessmentToAdd.Hoa.Category"></TelerikTextBox>
                </div>
            }
            <div class="mb-3">
                <label class="form-label">Subdivision</label><br />
                <TelerikDropDownList Data="@subdivisions" TextField="SubdivisionName" ValueField="SubdivisionId" @bind-Value="@AssessmentToAdd.SubdivisionId"></TelerikDropDownList>
                <ValidationMessage For="@(() => AssessmentToAdd.SubdivisionId)" />
            </div>
            <div class="mb-3">
                <label class="form-label">Assessment Label</label><br />
                <TelerikTextBox @bind-Value="@AssessmentToAdd.AssessmentLabel"></TelerikTextBox>
                <ValidationMessage For="@(() => AssessmentToAdd.AssessmentLabel)" />
            </div>
            <div class="mb-3">
                <label class="form-label">ICC</label><br />
                <TelerikNumericTextBox @bind-Value="@AssessmentToAdd.Icc"></TelerikNumericTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Month Assessment</label><br />
                <TelerikNumericTextBox @bind-Value="@AssessmentToAdd.MonthAssessment"></TelerikNumericTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Other Fee</label><br />
                <TelerikNumericTextBox @bind-Value="@AssessmentToAdd.OtherFee"></TelerikNumericTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Budget Year</label><br />
                <TelerikDropDownList Data="@years" TextField="Year" @bind-Value="@AssessmentToAdd.BudgetYear"></TelerikDropDownList>
                <ValidationMessage For="@(() => AssessmentToAdd.BudgetYear)" />
            </div>
            <button type="submit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                    </svg>
                </span> Update
            </button>
            <button type="button" @onclick="CancelAddItem" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-base">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel" aria-hidden="true">
                    <!--!-->
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M256 32c-50.3 0-96.8 16.6-134.1 44.6-17.2 12.8-32.4 28.1-45.3 45.3C48.6 159.2 32 205.7 32 256c0 123.7 100.3 224 224 224 50.3 0 96.8-16.6 134.1-44.6 17.2-12.8 32.4-28.1 45.3-45.3 28-37.4 44.6-83.8 44.6-134.1 0-123.7-100.3-224-224-224zm0 384c-88.2 0-160-71.8-160-160 0-32.6 9.8-62.9 26.6-88.2l221.6 221.6C318.9 406.2 288.6 416 256 416zm133.4-71.8L167.8 122.6C193.1 105.8 223.4 96 256 96c88.2 0 160 71.8 160 160 0 32.6-9.8 62.9-26.6 88.2z"></path><!--!-->
                    </svg><!--!-->
                    <!--!-->
                </span> Cancel
            </button>
            <div style=@submittingStyle>Adding item. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public HoaAssessmentDto AssessmentToAdd { get; set; } = new HoaAssessmentDto() { Hoa = new HoaDto() };
    public List<SubdivisionDto> subdivisions;
    public List<HoaDto> associations;
    public List<DateTime> years = new List<DateTime>();

    private string submittingStyle = "display:none";
    //public List<int>? ItemsToAdd { get; set; } = new List<int>();
    //public List<int>? AllItemsToAdd {get; set;} = new List<int>();

    [Parameter]
    public EventCallback<ResponseModel<HoaAssessmentDto>> HandleAddSubmit { get; set; }

    public ValidationEvent ValidationEvent { get; set; } = ValidationEvent.Change;

    public async Task Show()
    {
        IsModalVisible = true;

        // add the year before, current, and after
        years.Add(new DateTime(DateTime.UtcNow.Year - 1, 1, 1));
        years.Add(new DateTime(DateTime.UtcNow.Year, 1, 1));
        years.Add(new DateTime(DateTime.UtcNow.Year + 1, 1, 1));
    }

    public async void TransferSubdivisions(List<SubdivisionDto> transferedSubdivisions)
    {
        subdivisions = transferedSubdivisions;
    }

    public async void TransferAssociations(List<HoaDto> transferedAssociations)
    {
        associations = transferedAssociations;
        associations.Add(new HoaDto()
            {
                HoaId = 0,
                AssociationName = "Add New"
            });
    }

    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";

        var responseItem = await HOAService.AddAssessmentAsync(AssessmentToAdd);
        submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(responseItem);
    }

    async void CancelAddItem()
    {
        IsModalVisible = false;
    }

    public async Task Hide()
    {
        IsModalVisible = false;
    }
}
