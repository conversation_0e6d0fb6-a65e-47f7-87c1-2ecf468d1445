﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;
public partial class EstactivityDto : IMapFrom<Estactivity>
{
    public int EstactivityId { get; set; }
    public bool? IsIssued { get; set; }//this will be used to track if some or all of the items in the activity are issued
    public double? ActivityTotal { get; set; } //This will be sum from estdetails
    public string? JobNumber { get; set; }

    public string? BomClass { get; set; }

    public string? Releasecode { get; set; }

    public int? SelectedVendor { get; set; }

    public int? DefaultVendor { get; set; }

    public string? UseLocation { get; set; }
    public bool BoolUseLocation { get; set; }
    //public bool? BoolUseLocation
    //{
    //    get { return UseLocation == "T"; }
    //    set { }//TODO: I don't think this works
    //}
    public string? UseWbsSort { get; set; }
    public bool BoolUseWbsSort { get; set; }
    //public bool? BoolUseWbsSort
    //{
    //    get { return UseWbsSort == "T"; }
    //    set { }//TODO: I don't think this works
    //}
    public string? Taxable { get; set; }
    public bool BoolTaxable { get; set; }
    //public bool? BoolTaxable
    //{
    //    get { return Taxable == "T"; }
    //    set { }//TODO: I don't think this works
    //}
    public string? TaxGroup { get; set; }

    public int? TradeId { get; set; }

    public int? SactivityId { get; set; }

    public int? Poseq { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CubitId { get; set; }

    public int? SubNumber { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }
    public bool? VendorBlocked { get; set; }
    public bool? VendorNoInsurance { get; set; }

    //public byte[]? RecordTimeStamp { get; set; }

    //public virtual ICollection<Estdetail> Estdetails { get; set; } = new List<Estdetail>();

    //public virtual Job? JobNumberNavigation { get; set; }

    //public virtual Trade? Trade { get; set; }
}
