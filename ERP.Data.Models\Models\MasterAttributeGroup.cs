﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class MasterAttributeGroup
{
    public int AttributeGroupId { get; set; }

    public string Description { get; set; } = null!;

    public int? Seq { get; set; }

    public bool? UseText { get; set; }

    public bool? IsActive { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public byte[] RecordTimeStampe { get; set; } = null!;

    public string? GroupOldName { get; set; }

    public virtual ICollection<AttrGroupAssignment> AttrGroupAssignments { get; set; } = new List<AttrGroupAssignment>();
}
