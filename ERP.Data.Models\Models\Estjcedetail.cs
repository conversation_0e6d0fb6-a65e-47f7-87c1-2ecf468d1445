﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Estjcedetail
{
    public int EstjcedetailId { get; set; }

    public int? SessionId { get; set; }

    public int EstoptionId { get; set; }

    public string? Costcode { get; set; }

    public string? Category { get; set; }

    public DateTime? EstimateDate { get; set; }

    public double? EstimateUnits { get; set; }

    public string? UnitDesc { get; set; }

    public double? EstimateAmount { get; set; }

    public string? EstimateExported { get; set; }

    public string? UserStamp { get; set; }

    public string? IsCancelled { get; set; }

    public string? DeleteAfterCancel { get; set; }

    public string? BudgetPendingSend { get; set; }

    public DateTime? BudgetDatetimeExported { get; set; }

    public string? BudgetUserExported { get; set; }

    public string? BudgetPendingSendStatus { get; set; }

    public string? BudgetReissueItem { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public bool? ExportBc { get; set; }

    public virtual ICollection<Estdetail> Estdetails { get; set; } = new List<Estdetail>();

    public virtual Estoption Estoption { get; set; } = null!;
}
