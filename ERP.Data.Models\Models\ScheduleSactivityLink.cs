﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ScheduleSactivityLink
{
    public int ScheduleAid { get; set; }

    public int EstactivityId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual Estactivity Estactivity { get; set; } = null!;

    public virtual ScheduleSactivity ScheduleA { get; set; } = null!;
}
