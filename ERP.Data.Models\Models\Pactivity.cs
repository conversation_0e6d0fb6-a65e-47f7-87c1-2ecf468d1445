﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Pactivity
{
    public int PactivityId { get; set; }

    public string? Activity { get; set; }

    public string? Printlocations { get; set; }

    public string? Printschedule { get; set; }

    public string? Poindex { get; set; }

    public string? Pecategory { get; set; }

    public string? Releasecode { get; set; }

    public int? SactivityId { get; set; }

    public int? TradeId { get; set; }

    public int? MasterPactivityId { get; set; }

    public string? IncludeSelectionsOnPo { get; set; }

    public string? Notes { get; set; }

    public int? DivId { get; set; }

    public int? JccostcodeId { get; set; }

    public int? JccategoryId { get; set; }

    public int BomClassId { get; set; }

    public int? InvApprovalRole { get; set; }

    public string? AutoCreateInvoice { get; set; }

    public string? ProtectFromMaster { get; set; }

    public string? Taxable { get; set; }

    public string? Description { get; set; }

    public int? MasterItemId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual BomClass BomClass { get; set; } = null!;

    public virtual Jccategory? Jccategory { get; set; }

    public virtual Jccostcode? Jccostcode { get; set; }

    public virtual MasterItem? MasterItem { get; set; }

    public virtual ICollection<PactivityAreaSupplier> PactivityAreaSuppliers { get; set; } = new List<PactivityAreaSupplier>();

    public virtual Sactivity? Sactivity { get; set; }

    public virtual Trade? Trade { get; set; }
}
