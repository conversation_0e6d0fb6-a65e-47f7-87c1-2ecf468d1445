﻿@using ERP.Data.Models;
@inject SubdivisionService SubdivisionService
@inject BudgetService BudgetService
@using ERP.Data.Models.Dto;
@inject AuthenticationStateProvider AuthenticationStateProvider

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Custom Option Estimate
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@EstimateToAdd" OnValidSubmit="@HandleValidAddSubmit">
            @* <div class="mb-3">
                <label class="form-label">Subdivision</label><br />
                <TelerikDropDownList @bind-Value="@EstimateToAdd.SubdivisionId"
                                     DefaultText="Select Subdivision"
                                     ScrollMode="@DropDownScrollMode.Virtual"
                                     ItemHeight="40"
                                     PageSize="20"
                                     Filterable="true"
                                     FilterOperator="StringFilterOperator.Contains"
                                     TextField="SubdivisionName"
                                     ValueField="SubdivisionId"
                                     Data="@AllSubdivisions">
                </TelerikDropDownList>
                
            </div>
            <div class="mb-3">
                <label class="form-label">Job Number</label><br />
                <TelerikTextBox @bind-Value="@EstimateToAdd.JobNumber"></TelerikTextBox>
            </div> *@
            <div class="mb-3">
                <label class="form-label">Option Code</label><br />
                <TelerikTextBox @bind-Value="@EstimateToAdd.OptionCode"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Option Description</label><br />
                <TelerikTextBox @bind-Value="@EstimateToAdd.Description"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Estimator Name</label><br />
                <TelerikTextBox @bind-Value="@EstimateToAdd.EstimatorName"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Estimator Email</label><br />
                <TelerikTextBox @bind-Value="@EstimateToAdd.EsimatorEmail"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Notes</label><br />
                <TelerikTextArea @bind-Value="@EstimateToAdd.Notes"></TelerikTextArea>
            </div>
            <div class="mb-3">
                <label class="form-label">Sales Price</label><br />
                <TelerikNumericTextBox Format="C" @bind-Value="@EstimateToAdd.SalesPrice"></TelerikNumericTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Customer Information Notes</label><br />
                <TelerikTextArea @bind-Value="@EstimateToAdd.CustomerInfoNotes"></TelerikTextArea>
            </div>
            <button type="submit" class="btn btn-primary">Add</button>
            <button type="button" @onclick="CancelAddItem" class="btn btn-secondary">Cancel</button>
            <div style=@submittingStyle>Adding estimate. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>
<ERP.Web.Components.AddItemToBudget SelectedEstOptionId="@SelectedCustBudget.Estoption.EstoptionId" @ref="AddItemToBudgetModal" HandleAddSubmit="HandleValidAddItemsSubmit"></ERP.Web.Components.AddItemToBudget>
@code {

    public bool IsModalVisible { get; set; }
    public AddCustomEstimateModel EstimateToAdd { get; set; } = new AddCustomEstimateModel();
    public List<SubdivisionDto>? AllSubdivisions { get; set; }
    private string submittingStyle = "display:none";
    public CombinedPOBudgetTreeModel? SelectedCustBudget { get; set; } = new CombinedPOBudgetTreeModel() { Estoption = new EstoptionDto() };
    public ERP.Web.Components.AddItemToBudget? AddItemToBudgetModal { get; set; }

    [Parameter]
    public EventCallback<AddCustomEstimateModel> HandleAddSubmit { get; set; }


    public async Task Show()
    {
        IsModalVisible = true;
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userEmail = user.User.Identity.Name;
        var userName = user.User.Identity.Name.Split('@')[0];
        EstimateToAdd = new AddCustomEstimateModel() { JobNumber = "CUSTEST0001", EstimatorName = userName, EsimatorEmail = userEmail, SubdivisionId = 1 };//default values for job number and subdivision  
        StateHasChanged();
    }


    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        var responseItem = await BudgetService.AddCustomEstimateAsync(EstimateToAdd);
        submittingStyle = "display:none";
        //after add, a new window pops up to add the items, for now selecting by trade/activity/item and adding a Base house as the option level
        SelectedCustBudget.Estoption.EstoptionId = responseItem.Value.EstoptionId;
        StateHasChanged();
        await AddItemToBudgetModal.Show();
        IsModalVisible = false;
    }
    async void CancelAddItem()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;
    }
    private async void HandleValidAddItemsSubmit(ResponseModel<List<EstdetailDto>> responseItem)
    {
        AddItemToBudgetModal.Hide();
        var responseEstimate = new AddCustomEstimateModel();
        await HandleAddSubmit.InvokeAsync(responseEstimate);
    }
}
