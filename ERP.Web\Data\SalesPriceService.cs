﻿using DocumentFormat.OpenXml.Spreadsheet;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using NLog;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class SalesPriceService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public SalesPriceService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }


        public async Task<List<WorksheetDto>> GetWorksheetsAsync()
        {
            var items = new List<WorksheetDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesprice/GetWorksheets/");
                var responseString = await response.Content.ReadAsStringAsync();
                items = JsonConvert.DeserializeObject<List<WorksheetDto>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return items;
        }
        public async Task<string> GetWorksheetLockAsync(int worksheetId)
        {
            string lockedBy = null;
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesprice/GetWorksheetLock/{worksheetId}");
                lockedBy = await response.Content.ReadAsStringAsync();
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return lockedBy;
        }
        public async Task<string> BreakWorksheetLockAsync(int worksheetId)
        {
            //changes lock to current user (this could be instead sequential clear and then get lock)
            string lockedBy = null;
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesprice/BreakWorksheetLock/{worksheetId}");
                lockedBy = await response.Content.ReadAsStringAsync();
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return lockedBy;
        }
        public async Task<string> ClearWorksheetLockAsync(int worksheetId)
        {
            //sets lockedby to null
            string lockedBy = null;
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesprice/ClearWorksheetLock/{worksheetId}");
                lockedBy = await response.Content.ReadAsStringAsync();
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return lockedBy;
        }
        public async Task<ResponseModel<List<WorksheetTreeModel>>> GetAllWorksheetDataAsync(int worksheetId)
        {
            var items = new List<WorksheetTreeModel>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesprice/GetAllWorksheetData/{worksheetId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<WorksheetTreeModel>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<WorksheetTreeModel>>() { Value = items, IsSuccess = false, Message = "Failed to get all Worksheet Data" };
        }
        public async Task<List<WorksheetPlanModel>> GetWorksheetPlansAsync(int worksheetId)
        {
            var items = new List<WorksheetPlanModel>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesprice/GetWorksheetPlans/{worksheetId}");
                var responseString = await response.Content.ReadAsStringAsync();
                items = JsonConvert.DeserializeObject<List<WorksheetPlanModel>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return items;
        }
        public async Task<ResponseModel<List<WorksheetOptActModel>>> GetWorksheetOptionsActivitiesAsync(int worksheetOptId)
        {
            var items = new List<WorksheetOptActModel>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesprice/GetWorksheetOptActs/{worksheetOptId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<WorksheetOptActModel>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<WorksheetOptActModel>>() { Value = items, IsSuccess = false, Message = "Failed to get Worksheet Option Activities" };
        }
        public async Task<ResponseModel<List<WorksheetOptActModel>>> GetWorksheetPlansActivitiesAsync(int worksheetPlanId)
        {
            var items = new List<WorksheetOptActModel>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesprice/GetWorksheetPlanAct/{worksheetPlanId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<WorksheetOptActModel>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes or something
                return new ResponseModel<List<WorksheetOptActModel>> { IsSuccess = false, Message = "Login or consent needed", Value = items };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<WorksheetOptActModel>> { IsSuccess = false, Message = "Error while fetching Worksheet Option Activities", Value = items };
            }
        }
        public async Task<ResponseModel<List<WorksheetOptModel>>> GetWorksheetOptionsAsync(int worksheetPlanId)
        {
            var items = new List<WorksheetOptModel>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesprice/GetWorksheetPlanOption/{worksheetPlanId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<WorksheetOptModel>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes or something
                return new ResponseModel<List<WorksheetOptModel>> { IsSuccess = false, Message = "Login or consent needed", Value = items };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<WorksheetOptModel>> { IsSuccess = false, Message = "Failed to get Worksheet Options", Value = items };
            }
        }
        public async Task<ResponseModel<WorksheetDto>> CreateWorksheetAsync(WorksheetDto model)
        {

            try
            {
                var response = await _downstreamAPI.PostForUserAsync<WorksheetDto, ResponseModel<WorksheetDto>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/salesprice/createworksheet/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<WorksheetDto> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<WorksheetDto> { IsSuccess = false, Message = "Failed to create Worksheet" };
            }
        }
        public async Task<ResponseModel<RepriceWorksheetModel>> RepriceWorksheetAsync(RepriceWorksheetModel model)
        {
            try
            {
                // reprice worksheet plan/planact 
                var response = await _downstreamAPI.PostForUserAsync<RepriceWorksheetModel, ResponseModel<RepriceWorksheetModel>>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/salesprice/RepriceWorksheet/";
                             });
                return response;


            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<RepriceWorksheetModel> { IsSuccess = false, Message = "Failed to reprice Worksheet", Value = model };
        }


        public async Task<ResponseModel<RepriceWorksheetOptsModel>> RepriceWorksheetOptsAsync(RepriceWorksheetOptsModel model)
        {            
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<RepriceWorksheetOptsModel, ResponseModel<RepriceWorksheetOptsModel>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/salesprice/RepriceWorksheetOpts/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<RepriceWorksheetOptsModel>() { IsSuccess = false, Message = "Error repricing option. Contact BI if error persists" };
            }
            return new ResponseModel<RepriceWorksheetOptsModel>() { IsSuccess = false, Message = "Error repricing option. Contact BI if error persists" };
        }

        public async Task<ResponseModel<RepriceWorksheetPlansModel>> RepriceWorksheetPlansAsync(RepriceWorksheetPlansModel model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<RepriceWorksheetPlansModel, ResponseModel<RepriceWorksheetPlansModel>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/salesprice/RepriceWorksheetPlans/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<RepriceWorksheetPlansModel>() { IsSuccess = false, Message = "Error repricing plan. Contact BI if error persists" };
            }
            return new ResponseModel<RepriceWorksheetPlansModel>() { IsSuccess = false, Message = "Error repricing plan. Contact BI if error persists" };
        }

        public async Task<ResponseModel<WorksheetDto>> CopyWorksheetAsync(WorksheetDto model)
        {
            //the model should have the id of the worksheet to be copied, with the new name and new description
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<WorksheetDto, ResponseModel<WorksheetDto>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/salesprice/copyworksheet/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<WorksheetDto> { IsSuccess = false, Message = "Failed to copy Worksheet" };
        }
        public async Task<ResponseModel<List<WorksheetPlanModel>>> AddPlansToWorksheetAsync(List<WorksheetPlanModel> model)
        {

            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<WorksheetPlanModel>, ResponseModel<List<WorksheetPlanModel>>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/salesprice/addplanstoworksheet/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<WorksheetPlanModel>> { IsSuccess = false, Message = "Failed to add plans to Worksheet", Value = model };
        }
        public async Task<ResponseModel<List<WorksheetPlanModel>>> AddPlansWithOptionsToWorksheetAsync(List<WorksheetPlanModel> model)
        {

            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<WorksheetPlanModel>, ResponseModel<List<WorksheetPlanModel>>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/salesprice/addplanswithoptionstoworksheet/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<WorksheetPlanModel>> { IsSuccess = false, Message = "Failed to add plans to Worksheet", Value = model };
        }

        public async Task<ResponseModel<List<WorksheetOptModel>>> AddOptionsToWorksheetAsync(List<WorksheetOptModel> model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<WorksheetOptModel>, ResponseModel<List<WorksheetOptModel>>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/salesprice/addoptionstoworksheet/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<WorksheetOptModel>> { IsSuccess = false, Message = "Failed to add options to Worksheet Plan", Value = model };
        }
        public async Task<ResponseModel<List<WorksheetTreeModel>>> UpdateWorksheetAsync(List<WorksheetTreeModel> model)
        {
            //This will update/save an entire worksheet.
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<WorksheetTreeModel>, ResponseModel<List<WorksheetTreeModel>>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/salesprice/updateworksheet/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<WorksheetTreeModel>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<WorksheetTreeModel>> { IsSuccess = false, Message = "Failed to update Worksheet" };
            }
        }
        public async Task<ResponseModel<List<WorksheetTreeModel>>> SaveUpdateWorksheetAsync(List<WorksheetTreeModel> model)
        {
            //This will update/save an entire worksheet.
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<WorksheetTreeModel>, ResponseModel<List<WorksheetTreeModel>>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/salesprice/saveupdateworksheet/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<WorksheetTreeModel>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<WorksheetTreeModel>> { IsSuccess = false, Message = "Failed to update Worksheet" };
            }
        }
        public async Task<ResponseModel<List<WorksheetTreeModel>>> UpdateWorksheetFromImportAsync(List<WorksheetTreeModel> model)
        {
            //This will update/save an entire worksheet, from imported worksheet data, which seems missing sub number so other method was overwriting
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<WorksheetTreeModel>, ResponseModel<List<WorksheetTreeModel>>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/salesprice/updateworksheetfromimport/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<WorksheetTreeModel>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<WorksheetTreeModel>> { IsSuccess = false, Message = "Failed to update Worksheet" };
            }
        }
        public async Task<ResponseModel<List<WorksheetTreeModel>>> UpdateWorksheetOptsAsync(List<WorksheetTreeModel> model)
        {
            //This will update/save an option within a worksheet.
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<WorksheetTreeModel>, ResponseModel<List<WorksheetTreeModel>>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/salesprice/updateworksheetopts/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<WorksheetTreeModel>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<WorksheetTreeModel>> { IsSuccess = false, Message = "Failed to update Worksheet" };
            }
        }
        public async Task<ResponseModel<WorksheetDto>> DeleteWorksheetAsync(WorksheetDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<WorksheetDto, ResponseModel<WorksheetDto>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/salesprice/deleteworksheet/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<WorksheetDto> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<WorksheetDto> { IsSuccess = false, Message = "Failed to delete Worksheet" };
            }
        }

        public async Task<ResponseModel<List<AvailablePlanOptionDto>>> GetRollOverAvailablePlanOptionsAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/salesprice/GetRollOverAvailablePlanOptions");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var options = JsonConvert.DeserializeObject<ResponseModel<List<AvailablePlanOptionDto>>>(responseString);
                    return options;
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<AvailablePlanOptionDto>>() { IsSuccess = false, Value = null, Message = "Something went wrong" };
            }

            return new ResponseModel<List<AvailablePlanOptionDto>>() { IsSuccess = false, Value = null, Message = "Something went wrong" };
        }
        
        public async Task<ResponseModel<string>> UpdatePricingAsync(List<AvailablePlanOptionDto> planOptions)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<AvailablePlanOptionDto>, ResponseModel<string>>(
                            "DownstreamApi", planOptions,
                             options => {
                                 options.RelativePath = "api/salesprice/updatepricing/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<string>() { IsSuccess = false, Value = string.Empty, Message = "Something went wrong" };
            }
            return new ResponseModel<string> () { IsSuccess = false, Value = string.Empty, Message = "Something went wrong" };
        }

        public async Task<ResponseModel<ApplyPricingModel>> ApplyPricingAsync(ApplyPricingModel applyPricing)
        {

            try
            {
                var response = await _downstreamAPI.PutForUserAsync<ApplyPricingModel, ResponseModel<ApplyPricingModel>>(
                            "DownstreamApi", applyPricing,
                             options => {
                                 options.RelativePath = "api/salesprice/applypricing/";
                             });
                return new ResponseModel<ApplyPricingModel>() { IsSuccess = true, Value = applyPricing, Message = "Applied Pricing" };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<ApplyPricingModel>() { IsSuccess = false, Value = applyPricing, Message = "Something went wrong" };
            }
            return new ResponseModel<ApplyPricingModel>() { IsSuccess = false, Value = applyPricing, Message = "Something went wrong" };
        }

        public async Task<ResponseModel<ApplyPricingModel>> ApplyPricingOptionAsync(ApplyPricingModel model)
        {
            //this one should apply the pricing of just one option (or a list of options)
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<ApplyPricingModel, ResponseModel<ApplyPricingModel>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/salesprice/applypricingoption/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<ApplyPricingModel>() { IsSuccess = false };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<ApplyPricingModel>() { IsSuccess = false, Message = "something went wrong" }; ;
            }
        }
        
        
        /// <summary>
        /// Get items in plan option
        /// </summary>
        /// <param name="optionId"></param>
        /// <returns></returns>
        public async Task<ResponseModel<List<AvailablePlanOptionDto>>> GetAvailablePlanOptionsByPlanAsync(int planId)
        {
            var items = new ResponseModel<List<AvailablePlanOptionDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesprice/GetAvailablePlanOptionsByPlan/{planId}");
                var responseString = await response.Content.ReadAsStringAsync();
                items = JsonConvert.DeserializeObject<ResponseModel<List<AvailablePlanOptionDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return items;
        }

        public async Task<ResponseModel<string>> UpdateOptionsAsync(List<AvailablePlanOptionDto> options)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<AvailablePlanOptionDto>, ResponseModel<string>>(
                            "DownstreamApi", options,
                             options => {
                                 options.RelativePath = "api/salesprice/updateavailableplanoptions/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<string> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<string> { IsSuccess = false, Value = string.Empty, Message = "Failed to update Base House Option" };
            }
        }

        public async Task<ResponseModel<AvailablePlanOptionDto>> UpdateOptionAsync(AvailablePlanOptionDto Option)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<AvailablePlanOptionDto, ResponseModel<AvailablePlanOptionDto>>(
                            "DownstreamApi", Option,
                             options => {
                                 options.RelativePath = "api/salesprice/updateavailableplanoption/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<AvailablePlanOptionDto> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<AvailablePlanOptionDto> { IsSuccess = false, Message = "Failed to update selected option" };
            }
        }

        public async Task<ResponseModel<TileDto>> GetTotalOptionsSold()
        {
            var items = new ResponseModel<TileDto>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/salesprice/gettotaloptionssold/");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    items = JsonConvert.DeserializeObject<ResponseModel<TileDto>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return items;
        }

        //not used, no updating base house pricing
        //        public async Task<ResponseModel<AvailablePlanOptionDto>> UpdateBaseHouseOptionAsync(AvailablePlanOptionDto option)
        //        {
        //            try
        //            {
        //                var response = await _downstreamAPI.PutForUserAsync<AvailablePlanOptionDto, ResponseModel<AvailablePlanOptionDto>>(
        //                            "DownstreamApi", option,
        //                             options => {
        //                                 options.RelativePath = "api/salesprice/updatebasehouseoption/";
        //                             });
        //                return response;
        //            }
        //            catch (MicrosoftIdentityWebChallengeUserException ex)
        //            {
        //                _consentHandler.HandleException(ex);
        //                return new ResponseModel<AvailablePlanOptionDto> { IsSuccess = false, Message = "Login or consent needed" };
        //            }
        //            catch (Exception ex)
        //            {
        //#if DEBUG
        //                _logger.Debug(ex);
        //#else
        //                _logger.Error(ex);
        //#endif
        //                return new ResponseModel<AvailablePlanOptionDto> { IsSuccess = false, Message = "Failed to update Base House Option" };
        //            }
        //        }
    }
}
