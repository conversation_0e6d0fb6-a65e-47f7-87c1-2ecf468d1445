﻿
using AutoMapper;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NLog;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class ItemController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        public ItemController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
        }


        [HttpGet]
        public async Task<IActionResult> GetPurchasingActivitiesAsync()
        {
            try
            {
                //Div = 1 is supposed to be division, div = 2 is area. But they have the same bom classes, hence same items. 
                var activities = await _context.Pactivities.Where(x => x.IsActive == true && x.DivId == 1).Select(x => new PactivityModel()
                {
                    PactivityId = x.PactivityId,
                    BomClassId = x.BomClassId,
                    Activity = x.Activity,
                    TradeName = x.Trade.TradeName
                }).OrderBy(x => x.Activity).ToListAsync();
                return Ok(new ResponseModel<List<PactivityModel>>() { Value = activities, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<PactivityModel>>() { IsSuccess = false, Message = "failed to getpurchasing activites", Value = null });
            }
        }

        [HttpGet("{planId}")]
        public async Task<IActionResult> GetAssemblyForPlanAsync(int planId)
        {
            try
            {
                var assemblies = await _context.AsmHeaders.Include(x => x.MasterOption.OptionGroup).Where(x => x.MasterPlanId == planId && x.IsActive == true).Select(x => new AsmHeaderModel()
                {
                    OptionGroupName = x.MasterOption.OptionGroup.OptionGroupName,
                    OptionGroupId = x.MasterOption.OptionGroupId,
                    DisplayName = x.MasterOption.OptionCode + "-" + x.AssemblyDesc,
                    MasterOptionCode = x.MasterOption.OptionCode,
                    AsmGroupId = x.AsmGroupId,
                    AsmHeaderId = x.AsmHeaderId,
                    AssemblyCode = x.AssemblyCode,
                    AssemblyDesc = x.AssemblyDesc,
                    AssemblyNotes = x.AssemblyNotes,
                    AssemblySize = x.AssemblySize,
                    AssemblyUnit = x.AssemblyUnit,
                    CreatedBy = x.CreatedBy,
                    Calculation = x.Calculation,
                    CreatedDateTime = x.CreatedDateTime,
                    DeletedFromPe = x.DeletedFromPe,
                    EstDbOwner = x.EstDbOwner,
                    IsElevation = x.IsElevation,
                    BoolIsElevation = x.IsElevation == "T" ? true : false,
                    Formula = x.Formula,
                    HomeAreaId = x.HomeAreaId,
                    HomeAreaName = x.HomeArea.HomeArea1,
                    IsBaseHouse = x.IsBaseHouse,
                    BoolIsBaseHouse = x.IsBaseHouse == "T" ? true : false,
                    MasterPlanId = x.MasterPlanId,
                    OptionScope = x.OptionScope,
                    TlpeOptionCategoryId = x.TlpeOptionCategoryId,
                    PeDatetimestamp = x.PeDatetimestamp,
                    PeHeader = x.PeHeader,
                    PeUpdated = x.PeUpdated,
                    UpdatedBy = x.UpdatedBy,
                    UpdatedDateTime = x.UpdatedDateTime,
                    IsActive = x.IsActive
                }).OrderBy(x => x.AssemblyCode).ToListAsync();

                var data = new List<AsmHeaderModel>();

                foreach (var x in assemblies)
                {
                    var build = new AsmHeaderModel()
                    {
                        OptionGroupName = x.OptionGroupName,
                        OptionGroupId = x.OptionGroupId,
                        DisplayName = x.MasterOptionCode + "-" + x.AssemblyDesc,
                        MasterOptionCode = x.MasterOptionCode,
                        AsmGroupId = x.AsmGroupId,
                        AsmHeaderId = x.AsmHeaderId,
                        AssemblyCode = x.AssemblyCode,
                        AssemblyDesc = x.AssemblyDesc,
                        AssemblyNotes = x.AssemblyNotes,
                        AssemblySize = x.AssemblySize,
                        AssemblyUnit = x.AssemblyUnit,
                        CreatedBy = x.CreatedBy,
                        Calculation = x.Calculation,
                        CreatedDateTime = x.CreatedDateTime,
                        DeletedFromPe = x.DeletedFromPe,
                        EstDbOwner = x.EstDbOwner,
                        IsElevation = x.IsElevation,
                        BoolIsElevation = x.IsElevation == "T" ? true : false,
                        Formula = x.Formula,
                        HomeAreaId = x.HomeAreaId,
                        HomeAreaName = x.HomeAreaName,
                        IsBaseHouse = x.IsBaseHouse,
                        BoolIsBaseHouse = x.IsBaseHouse == "T" ? true : false,
                        MasterPlanId = x.MasterPlanId,
                        OptionScope = x.OptionScope,
                        TlpeOptionCategoryId = x.TlpeOptionCategoryId,
                        PeDatetimestamp = x.PeDatetimestamp,
                        PeHeader = x.PeHeader,
                        PeUpdated = x.PeUpdated,
                        UpdatedBy = x.UpdatedBy,
                        UpdatedDateTime = x.UpdatedDateTime,
                        IsActive = x.IsActive,
                        MasterPlanIds = new List<int>()
                    };
                    data.Add(build);
                }
               
                return Ok(new ResponseModel<List<AsmHeaderModel>>() { Value = data, IsSuccess = true } );
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<AsmHeaderModel>>() { IsSuccess = false, Message = "failed to get assembly for plan", Value = null });
            }
        }
        [HttpGet("{planId}")]
        public async Task<IActionResult> GetAssemblyForPlanWithOtherMasterPlanAsync(int planId)
        {
            try
            {

                var assemblies = await _context.AsmHeaders.AsNoTracking().Include(x => x.MasterOption.OptionGroup).Where(x => x.MasterPlanId == planId && x.IsActive == true).Select(x => new AsmHeaderModel()
                {
                    OptionGroupName = x.MasterOption.OptionGroup.OptionGroupName,
                    OptionGroupId = x.MasterOption.OptionGroupId,
                    DisplayName = x.MasterOption.OptionCode + "-" + x.AssemblyDesc,
                    MasterOptionCode = x.MasterOption.OptionCode,
                    AsmGroupId = x.AsmGroupId,
                    AsmHeaderId = x.AsmHeaderId,
                    AssemblyCode = x.AssemblyCode,
                    AssemblyDesc = x.AssemblyDesc,
                    AssemblyNotes = x.AssemblyNotes,
                    AssemblySize = x.AssemblySize,
                    AssemblyUnit = x.AssemblyUnit,
                    CreatedBy = x.CreatedBy,
                    Calculation = x.Calculation,
                    CreatedDateTime = x.CreatedDateTime,
                    DeletedFromPe = x.DeletedFromPe,
                    EstDbOwner = x.EstDbOwner,
                    IsElevation = x.IsElevation,
                    BoolIsElevation = x.IsElevation == "T" ? true : false,
                    Formula = x.Formula,
                    HomeAreaId = x.HomeAreaId,
                    HomeAreaName = x.HomeArea.HomeArea1,
                    IsBaseHouse = x.IsBaseHouse,
                    BoolIsBaseHouse = x.IsBaseHouse == "T" ? true : false,
                    MasterPlanId = x.MasterPlanId,
                    OptionScope = x.OptionScope,
                    TlpeOptionCategoryId = x.TlpeOptionCategoryId,
                    PeDatetimestamp = x.PeDatetimestamp,
                    PeHeader = x.PeHeader,
                    PeUpdated = x.PeUpdated,
                    UpdatedBy = x.UpdatedBy,
                    UpdatedDateTime = x.UpdatedDateTime,
                    IsActive = x.IsActive,
                    MasterPlanIds = new List<int>()
                }).OrderBy(x => x.AssemblyCode).ToListAsync();

                var data = new List<AsmHeaderModel>();
                //other plan ids same asm header
                var optionCodes = assemblies.Select(x => x.MasterOptionCode).ToList();
                var otherPlansSameOptionCodes = _context.AsmHeaders.AsNoTracking().Include(x => x.MasterOption).Where(x => optionCodes.Contains(x.MasterOption.OptionCode) && x.MasterPlanId != planId && x.IsActive == true).ToList();
                foreach (var assembly in assemblies)
                {
                    assembly.MasterPlanIds = new List<int>();
                    //var getAssemblyCode = (build.AssemblyCode.Length == 12) ? build.AssemblyCode.Substring(build.AssemblyCode.Length - 4) : build.AssemblyCode;
                    //var findOtherMasterPlanIds = _context.AsmHeaders.Where(x => x.AssemblyCode == getAssemblyCode && x.IsActive == true && x.AsmHeaderId != build.AsmHeaderId).Select(x => (int)x.MasterPlanId);//slow, also, wrong

                    var otherMasterPlanIds = otherPlansSameOptionCodes.Where(x => x.MasterOption.OptionCode == assembly.MasterOptionCode).Select(x => (int)x.MasterPlanId).ToList();

                    assembly.MasterPlanIds.AddRange(otherMasterPlanIds);

                    data.Add(assembly);
                }
                return Ok(new ResponseModel<List<AsmHeaderModel>>() { Value = data, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<AsmHeaderModel>>() { IsSuccess = false, Message = "failed to get assembly for plan", Value = null });
            }
        }
        [HttpGet("{planId}")]
        public async Task<IActionResult> GetAssemblyForPlanIncludeInactiveAsync(int planId)
        {
            try
            {
                var assemblies = await _context.AsmHeaders.Where(x => x.MasterPlanId == planId).Select(x => new AsmHeaderModel()
                {
                    OptionGroupName = x.MasterOption.OptionGroup.OptionGroupName,
                    OptionGroupId = x.MasterOption.OptionGroupId,
                    DisplayName = x.MasterOption.OptionCode + "-" + x.AssemblyDesc,
                    MasterOptionCode = x.MasterOption.OptionCode,
                    AsmGroupId = x.AsmGroupId,
                    AsmHeaderId = x.AsmHeaderId,
                    AssemblyCode = x.AssemblyCode,
                    AssemblyDesc = x.AssemblyDesc,
                    DisplayDesc = $"{x.AssemblyCode} - {x.AssemblyDesc}",
                    AssemblyNotes = x.AssemblyNotes,
                    AssemblySize = x.AssemblySize,
                    AssemblyUnit = x.AssemblyUnit,
                    CreatedBy = x.CreatedBy,
                    Calculation = x.Calculation,
                    CreatedDateTime = x.CreatedDateTime,
                    DeletedFromPe = x.DeletedFromPe,
                    EstDbOwner = x.EstDbOwner,
                    IsElevation = x.IsElevation,
                    BoolIsElevation = x.IsElevation == "T" ? true : false,
                    Formula = x.Formula,
                    HomeAreaId = x.HomeAreaId,
                    HomeAreaName = x.HomeArea.HomeArea1,
                    IsBaseHouse = x.IsBaseHouse,
                    BoolIsBaseHouse = x.IsBaseHouse == "T" ? true : false,
                    MasterPlanId = x.MasterPlanId,
                    OptionScope = x.OptionScope,
                    TlpeOptionCategoryId = x.TlpeOptionCategoryId,
                    PeDatetimestamp = x.PeDatetimestamp,
                    PeHeader = x.PeHeader,
                    PeUpdated = x.PeUpdated,
                    UpdatedBy = x.UpdatedBy,
                    UpdatedDateTime = x.UpdatedDateTime,
                    IsActive = x.IsActive,
                }).OrderBy(x => x.AssemblyCode).ToListAsync();

                return Ok(new ResponseModel<List<AsmHeaderModel>>() { Value = assemblies, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<AsmHeaderModel>>() { IsSuccess = false, Message = "failed to get assembly for plan", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetAllAssembliesAsync()
        {
            try
            {
                var assemblies = await _context.AsmHeaders.Include("MasterOption.OptionGroup").Include("HomeArea").Where(x => x.IsActive == true).Select(x => new AsmHeaderModel()
                {
                    OptionGroupName = x.MasterOption.OptionGroup.OptionGroupName,
                    OptionGroupId = x.MasterOption.OptionGroupId,
                    AsmGroupId = x.AsmGroupId,
                    AsmHeaderId = x.AsmHeaderId,
                    AssemblyCode = x.AssemblyCode,
                    AssemblyDesc = x.AssemblyDesc,
                    AssemblyNotes = x.AssemblyNotes,
                    AssemblySize = x.AssemblySize,
                    AssemblyUnit = x.AssemblyUnit,
                    CreatedBy = x.CreatedBy,
                    Calculation = x.Calculation,
                    CreatedDateTime = x.CreatedDateTime,
                    DeletedFromPe = x.DeletedFromPe,
                    EstDbOwner = x.EstDbOwner,
                    IsElevation = x.IsElevation,
                    BoolIsElevation = x.IsElevation == "T" ? true : false,
                    Formula = x.Formula,
                    HomeAreaId = x.HomeAreaId,
                    HomeAreaName = x.HomeArea.HomeArea1,
                    IsBaseHouse = x.IsBaseHouse,
                    BoolIsBaseHouse = x.IsBaseHouse == "T" ? true : false,
                    MasterPlanId = x.MasterPlanId,
                    OptionScope = x.OptionScope,
                    TlpeOptionCategoryId = x.TlpeOptionCategoryId,
                    PeDatetimestamp = x.PeDatetimestamp,
                    PeHeader = x.PeHeader,
                    PeUpdated = x.PeUpdated,
                    UpdatedBy = x.UpdatedBy,
                    UpdatedDateTime = x.UpdatedDateTime,
                    IsActive = x.IsActive,
                }).ToListAsync();
                return Ok(new ResponseModel<List<AsmHeaderModel>>() { Value = assemblies, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<AsmHeaderModel>>() { IsSuccess = false, Message = "failed to get all assemblies", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> GetAllAssembliesForPlansAsync([FromBody] List<int> masterPlanIds)
        {
            try
            {
                var assemblies = await _context.AsmHeaders.Include("MasterOption.OptionGroup").Include("HomeArea").Where(x => x.IsActive == true && masterPlanIds.Contains((int)x.MasterPlanId)).Select(x => new AsmHeaderModel()
                {
                    OptionGroupName = x.MasterOption.OptionGroup.OptionGroupName,
                    OptionGroupId = x.MasterOption.OptionGroupId,
                    AsmGroupId = x.AsmGroupId,
                    AsmHeaderId = x.AsmHeaderId,
                    AssemblyCode = x.AssemblyCode,
                    AssemblyDesc = x.AssemblyDesc,
                    AssemblyNotes = x.AssemblyNotes,
                    AssemblySize = x.AssemblySize,
                    AssemblyUnit = x.AssemblyUnit,
                    CreatedBy = x.CreatedBy,
                    Calculation = x.Calculation,
                    CreatedDateTime = x.CreatedDateTime,
                    DeletedFromPe = x.DeletedFromPe,
                    EstDbOwner = x.EstDbOwner,
                    IsElevation = x.IsElevation,
                    BoolIsElevation = x.IsElevation == "T" ? true : false,
                    Formula = x.Formula,
                    HomeAreaId = x.HomeAreaId,
                    HomeAreaName = x.HomeArea.HomeArea1,
                    IsBaseHouse = x.IsBaseHouse,
                    BoolIsBaseHouse = x.IsBaseHouse == "T" ? true : false,
                    MasterPlanId = x.MasterPlanId,
                    OptionScope = x.OptionScope,
                    TlpeOptionCategoryId = x.TlpeOptionCategoryId,
                    PeDatetimestamp = x.PeDatetimestamp,
                    PeHeader = x.PeHeader,
                    PeUpdated = x.PeUpdated,
                    UpdatedBy = x.UpdatedBy,
                    UpdatedDateTime = x.UpdatedDateTime,
                    IsActive = x.IsActive,
                }).ToListAsync();
                return Ok(new ResponseModel<List<AsmHeaderModel>>() { Value = assemblies, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<AsmHeaderModel>>() { IsSuccess = false, Message = "failed to get all assemblies for plans", Value = null });
            }
        }
        [HttpGet("{assemblyId}")]
        public async Task<IActionResult> GetItemsInAssemblyAsync(int assemblyId)
        {
            try
            {
                var items = await _context.AsmDetails.Include("MasterItem.BomClass").Where(x => x.AsmHeaderId == assemblyId && x.IsActive == true).Select(x => new ModelManagerItemModel()
                {
                    AsmHeaderId = x.AsmHeaderId,
                    AsmDetailId = x.AsmDetailId,
                    BomClassId = x.BomClassId ?? x.MasterItem.BomClassId,
                    BomClassName = x.BomClassId != null ? _context.BomClasses.SingleOrDefault(y => y.BomClassId == x.BomClassId).BomClass1 : x.MasterItem.BomClass.BomClass1,
                    PhaseCode = x.MasterItem.MasterItemPhase.PhaseCode,
                    MasterItemId = x.MasterItemId,
                    ItemDesc = x.MasterItem.ItemDesc,
                    OptionItemNotes = x.OptionItemNotes,
                    ItemNotes = x.MasterItem.ItemNotes,
                    ItemNumber = x.MasterItem.ItemNumber,
                    IsActive = x.IsActive,
                    Formula = x.Formula,
                    CnvFctr = x.MasterItem.CnvFctr,
                    CalcBasis = x.MasterItem.CalcBasis,
                    DeletedFromPe = x.MasterItem.DeletedFromPe,
                    MasterPlanId = x.AsmHeader.MasterPlanId,
                    PlanSpecific = x.MasterItem.PlanSpecific,
                    TakeoffUnit = x.MasterItem.TakeoffUnit,
                    Taxable = x.MasterItem.Taxable, 
                    Factor = x.Factor,
                    OptionsContainingItem = _context.AsmDetails.Include("AsmHeader").Where(y => y.IsActive == true && y.MasterItemId == x.MasterItemId).Select(y => $"{y.AsmHeader.AssemblyCode } - { y.AsmHeader.AssemblyDesc}").ToList(),
                }).ToListAsync();
                return Ok(new ResponseModel<List<ModelManagerItemModel>>() { Value = items, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ModelManagerItemModel>>() { IsSuccess = false, Message = "failed to get items in assembly", Value = null });
            }
        }

        [HttpGet("{optionId}")]
        public async Task<IActionResult> GetItemsInMasterOptionAsync(int optionId)
        {
            //optionId parameter needs to be masterOptionId, not asmheader id
            try
            {
                var assembly = await _context.AsmHeaders.Where(x => x.MasterOptionId == optionId && x.MasterPlanId == 1).FirstOrDefaultAsync();//What if there is more than one?? There really shouldn't be
                var items = await _context.AsmDetails.Include(x => x.BomClass).Include(x => x.MasterItem).Where(x => x.AsmHeaderId == assembly.AsmHeaderId && x.IsActive == true).Select(x => new ModelManagerItemModel()
                {
                    AsmDetailId = x.AsmDetailId,
                    BomClassId = x.BomClassId ??  x.MasterItem.BomClassId,
                    BomClassName = x.BomClassId != null ? x.BomClass.BomClass1 : x.MasterItem.BomClass.BomClass1,
                    MasterItemId = x.MasterItemId,
                    ItemDesc = x.MasterItem.ItemDesc,
                    ItemNotes = x.MasterItem.ItemNotes,
                    OptionItemNotes = x.OptionItemNotes,
                    PhaseCode = x.MasterItem.MasterItemPhase.PhaseCode,
                    ItemNumber = x.MasterItem.ItemNumber,
                    IsActive = x.IsActive,
                    Formula = x.MasterItem.Formula,
                    Factor = x.Factor,
                    CnvFctr = x.MasterItem.CnvFctr,
                    CalcBasis = x.MasterItem.CalcBasis,
                    DeletedFromPe = x.MasterItem.DeletedFromPe,
                    MasterPlanId = x.AsmHeader.MasterPlanId,
                    ExcludeFromPo = x.MasterItem.ExcludeFromPo,
                    IsExcludeFromPO = x.MasterItem.ExcludeFromPo != "F" ? true : false,
                    Taxable = x.MasterItem.Taxable,
                    IsTaxable = x.MasterItem.Taxable != "F" ? true : false,
                    TakeoffUnit = x.MasterItem.TakeoffUnit,
                    PlanSpecific = x.MasterItem.PlanSpecific,
                }).ToListAsync();
                return Ok(new ResponseModel<List<ModelManagerItemModel>>() { Value = items, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ModelManagerItemModel>>() { IsSuccess = false, Message = "failed to get items in master option", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetTradesAsync()
        {
            try
            {
                var trades = await _context.Trades.Where(x => x.IsActive == true).OrderBy(x => x.TradeName).ToListAsync();
                var tradesDto = _mapper.Map<List<TradeDto>>(trades);
                return Ok(new ResponseModel<List<TradeDto>>() { Value = tradesDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<TradeDto>>() { IsSuccess = false, Message = "failed to get trades", Value = null });
            }
        }
        [HttpGet("{tradeId}")]
        public async Task<IActionResult> GetPurchasingActivityByTradeAsync(int tradeId)
        {
            var activities = new List<PactivityModel>();
            try
            {
                //filter out DivId = 2 since its not used
                activities = await _context.Pactivities.Where(x => x.TradeId == tradeId && x.DivId == 1 && x.IsActive == true).Select(x => new PactivityModel
                {
                    PactivityId = x.PactivityId,
                    Activity = x.Activity,
                    Printlocations = x.Printlocations,
                    Printschedule = x.Printschedule,
                    Poindex = x.Poindex,
                    Pecategory = x.Pecategory,
                    Releasecode = x.Releasecode,
                    SactivityId = x.SactivityId,
                    TradeId = x.TradeId,
                    MasterPactivityId = x.MasterPactivityId,
                    IncludeSelectionsOnPo = x.IncludeSelectionsOnPo,
                    IsIncludeSelections = x.IncludeSelectionsOnPo == "T" ? true : false,
                    Notes = x.Notes,
                    DivId = x.DivId,
                    JccategoryId = x.JccategoryId,
                    JccostcodeId = x.JccostcodeId,
                    BomClassId = x.BomClassId,
                    AutoCreateInvoice = x.AutoCreateInvoice,
                    ProtectFromMaster = x.ProtectFromMaster,
                    IsTaxable = x.Taxable == "T" ? true : false,
                    Taxable = x.Taxable,
                    Description = x.Description,
                    MasterItemId = x.MasterItemId,
                    CreatedDateTime = x.CreatedDateTime,
                    UpdatedBy = x.UpdatedBy,
                    UpdatedDateTime = x.UpdatedDateTime,
                    IsActive = x.IsActive,
                    
                }).ToListAsync();
                return Ok(new ResponseModel<List<PactivityModel>>() { Value = activities, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<PactivityModel>>() { IsSuccess = false, Message = "failed to get assembly for plan", Value = null });
            }
        }

        [HttpGet("{activityId}")]
        public async Task<IActionResult> GetMasterItemsByTradeAndActivityAsync(int activityId, bool optionSpecific)
        {
            try
            {
                // bool optionSpecific = true;

                //plan_specific seems to correspond to lumpsum items (not ls as takeoff unit). This is not the same as option specific. Those are different. 

                var findPactivity = _context.Pactivities.SingleOrDefault(x => x.PactivityId == activityId);
                var bomClassId = _context.BomClasses.SingleOrDefault(x => x.BomClassId == findPactivity.BomClassId).BomClassId;

                //option specific items will be in asm detail/asm header. master items will be just master item. to add it to an option (in a plan) add to asmdetail and asm header.
                //
                //var allASMDetailMasterItemId = _context.AsmDetails.Select(x => x.MasterItemId).Distinct().ToList();
                var optionSpecific1 = _context.AsmDetails.Include("MasterItem").Where(x => x.MasterItem.BomClassId == bomClassId && x.IsActive == true).Select(x => x.MasterItem).ToList();
                //then maybe the code is the asm header assembly code and desc is assembly desc??
                //so maybe need to add soemthing to master item phases when add an item to an option. I think that might be it, but also need to mark the plan specific maybe?? 
                //also it takes too long to render, need to page results if it's option specific 
                //it seems option specific have codes either just three digits or three digits plus-a0000000
                //  var testOptionSpecific = _context.MasterItems.Where(x => x.BomClassId == bomClassId && x.IsActive == true && !allASMDetailMasterItemId.Contains(x.MasterItemId)).ToList();
                //may need to remove plan specific (PlanSpecific = T) plan specific = lump sum???
                var items = new List<ModelManagerItemModel>();
                if (optionSpecific)
                {
                    items = await _context.MasterItems.Include("MasterItemPhase").Where(x => x.BomClassId == bomClassId && x.IsActive == true).Select(x => new ModelManagerItemModel()
                    {
                        MasterItemId = x.MasterItemId,
                        ItemNumber = x.ItemNumber,
                        BomClassId = x.BomClassId,
                        PhaseCode = x.MasterItemPhase.PhaseCode,
                        PhaseDesc = x.MasterItemPhase.PhaseDesc,
                        ItemDesc = x.ItemDesc,
                        TakeoffUnit = x.TakeoffUnit,
                        OrderUnit = x.OrderUnit,
                        RndDir = x.RndDir == "U" ? "Up To Multiple" : "None",
                        RndUnit = x.RndUnit,
                        Multdiv = x.Multdiv == "M" ? "Multiply" : "Divide",
                        CnvFctr = x.CnvFctr,
                        Waste = x.Waste,
                        ExcludeFromPo = x.ExcludeFromPo,
                        IsExcludeFromPO = x.ExcludeFromPo == "T" ? true : false,
                        Taxable = x.Taxable,
                        IsTaxable = x.Taxable == "T" ? true : false,
                        ItemNotes = x.ItemNotes,
                        PlanSpecific = x.PlanSpecific,
                        IsPlanSpecific = x.PlanSpecific == "T" ? true : false,
                    }).Distinct().ToListAsync();
                }
                else
                {
                    items = await _context.MasterItems.Include("MasterItemPhase").Where(x => x.BomClassId == bomClassId && x.IsActive == true && (x.PlanSpecific == "F" || x.MasterItemPhase.PhaseCode.Trim() == x.ItemNumber.Trim())).Select(x => new ModelManagerItemModel()
                    {
                        MasterItemId = x.MasterItemId,
                        ItemNumber = x.ItemNumber,
                        BomClassId = x.BomClassId,
                        PhaseCode = x.MasterItemPhase.PhaseCode,
                        PhaseDesc = x.MasterItemPhase.PhaseDesc,
                        ItemDesc = x.ItemDesc,
                        TakeoffUnit = x.TakeoffUnit,
                        OrderUnit = x.OrderUnit,
                        RndDir = x.RndDir == "U" ? "Up To Multiple" : "None",
                        RndUnit = x.RndUnit,
                        Multdiv = x.Multdiv == "M" ? "Multiply" : "Divide",
                        CnvFctr = x.CnvFctr,
                        Waste = x.Waste,
                        ExcludeFromPo = x.ExcludeFromPo,
                        IsExcludeFromPO = x.ExcludeFromPo == "T" ? true : false,
                        Taxable = x.Taxable,
                        IsTaxable = x.Taxable == "T" ? true : false,
                        ItemNotes = x.ItemNotes,
                        PlanSpecific = x.PlanSpecific,
                        IsPlanSpecific = x.PlanSpecific == "T" ? true : false,
                    }).Distinct().ToListAsync();
                }

                return Ok(new ResponseModel<List<ModelManagerItemModel>>() { Value = items, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ModelManagerItemModel>>() { IsSuccess = false, Message = "failed to get master items by trade and activity", Value = null });
            }
        }

        /// <summary>
        /// Items to add to a master plan option
        /// </summary>
        /// <param name="activityId"></param>
        /// <returns></returns>
        [HttpGet("{activityId}")]
        public async Task<IActionResult> GetMasterItemsToAddByTradeAndActivityAsync(int activityId)
        {
            try
            {
                //items to add to plan, which includes plan specific but only the master one, not the ones already in other plans

                var findPactivity = _context.Pactivities.SingleOrDefault(x => x.PactivityId == activityId);
                var bomClassId = _context.BomClasses.SingleOrDefault(x => x.BomClassId == findPactivity.BomClassId).BomClassId;

                //option specific items will be in asm detail/asm header. master items will be just master item. to add it to an option (in a plan) add to asmdetail and asm header.
                //ones not in a plan should have masteritemphase.asmheaderId == null , and masteritemphase.phasecode = masteritem.itemnumber not sure why that's how these are identified, but it seems to be the case, but some are named differently. There doesn't seem to be a simple way to get the original plan specific one that's not in a plan
                //2/24/25 added bomclass name contains condition instead of equals because char limit in master item phases meant those got truncated so don't match
                var items = new List<ModelManagerItemModel>();
                items = await _context.MasterItems.AsNoTracking().Include(x => x.MasterItemPhase).Where(x => x.BomClassId == bomClassId && x.IsActive == true && x.MasterItemPhase.AsmHeaderId == null && (x.PlanSpecific == "F" || x.MasterItemPhase.PhaseCode.Trim() == x.ItemNumber.Trim() || x.MasterItemPhase.PhaseDesc.Trim() == x.ItemNumber.Trim() || x.BomClass.BomClass1.Contains(x.MasterItemPhase.PhaseCode.Trim()) ||  x.BomClass.BomClass1.Contains(x.MasterItemPhase.PhaseDesc.Trim()))).Select(x => new ModelManagerItemModel()
                {
                    MasterItemId = x.MasterItemId,
                    ItemNumber = x.ItemNumber,
                    BomClassId = x.BomClassId,
                    PhaseCode = x.MasterItemPhase.PhaseCode,
                    PhaseDesc = x.MasterItemPhase.PhaseDesc,
                    ItemDesc = x.ItemDesc,
                    TakeoffUnit = x.TakeoffUnit,
                    OrderUnit = x.OrderUnit,
                    RndDir = x.RndDir == "U" ? "Up To Multiple" : "None",
                    RndUnit = x.RndUnit,
                    Multdiv = x.Multdiv == "M" ? "Multiply" : "Divide",
                    CnvFctr = x.CnvFctr,
                    Waste = x.Waste,
                    ExcludeFromPo = x.ExcludeFromPo,
                    IsExcludeFromPO = x.ExcludeFromPo == "T" ? true : false,
                    Taxable = x.Taxable,
                    IsTaxable = x.Taxable == "T" ? true : false,
                    ItemNotes = x.ItemNotes,
                    PlanSpecific = x.PlanSpecific,
                    IsPlanSpecific = x.PlanSpecific == "T" ? true : false,
                }).Distinct().ToListAsync();

                return Ok(new ResponseModel<List<ModelManagerItemModel>>() { Value = items, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ModelManagerItemModel>>() { IsSuccess = false, Message = "failed to get master items by trade and activity", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetMasterItemsAsync()
        {
            try
            {
                var items = await _context.MasterItems.ToListAsync();
                return Ok(new ResponseModel<List<MasterItem>>() { Value = items, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MasterItem>>() { IsSuccess = false, Message = "failed to get master items", Value = null });
            }
        }
            
        [HttpGet("{masterItemId}")]
        public async Task<IActionResult> PlanOptionsForMasterItemAsync(int masterItemId)
        {
            try
            {
                var options = await _context.AsmDetails.Include("AsmHeader").Where(x => x.MasterItemId == masterItemId && x.IsActive == true).Select(x => x.AsmHeader).ToListAsync();
                var optionsDto = _mapper.Map<List<AsmHeaderDto>>(options);
                return Ok(options);
            }
           catch(Exception ex)
            {
                return BadRequest(ex.ToString());
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteActivityAsync([FromBody] int activityId)
        {
            try
            {
                var findItem = await _context.Pactivities.SingleOrDefaultAsync(x => x.PactivityId == activityId);
                findItem.IsActive = false;
                findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                findItem.UpdatedDateTime = DateTime.Now;
                _context.Pactivities.Update(findItem);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<bool>() { Value = true, IsSuccess = true, Message = "Activity deleted" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<bool>() { IsSuccess = false, Message = "failed to delete activity", Value = false });
            }
        }      

        [HttpPut]
        public async Task<IActionResult> DeleteAsmHeaderAsync([FromBody] int asmHeaderId)
        {
            try
            {
                //inactivate all the asm details for that asm header. 
                var updateBy = User.Identity.Name.Split('@')[0];

                //Ernie update 11/11. If items are deactivated when the option is, if buyer buys before option is deactivated, it can't find items to generate budget because the items would be inactive. If in budget look for items including inactive, then can't distinguish items that really should have been removed from the option from items deactivated because the option was deactivated
                //var findDetails = _context.AsmDetails.Where(x => x.AsmHeaderId == asmHeaderId);
                //await findDetails.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));

                //now inactivate the header
                var findItem = await _context.AsmHeaders.SingleOrDefaultAsync(x => x.AsmHeaderId == asmHeaderId);
                findItem.IsActive = false;
                findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                findItem.UpdatedDateTime = DateTime.Now;
                _context.AsmHeaders.Update(findItem);

                // Deactivate available plan options for this specific plan in all subdivisions
                if (findItem != null)
                {
                    var findAvailablePlanOptions = _context.AvailablePlanOptions.Where(x => x.MasterOptionId == findItem.MasterOptionId && x.MasterPlanId == findItem.MasterPlanId);//fix 1/13/25 to include plan id, not just option id. It was deleting from all plans

                    if (findAvailablePlanOptions.Any())
                    {
                        await findAvailablePlanOptions.ExecuteUpdateAsync(x => x.SetProperty(y => y.IsActive, false).SetProperty(y => y.UpdatedBy, updateBy).SetProperty(y => y.UpdatedDateTime, DateTime.Now));
                    }
                }               

                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<bool>() { Value = true, IsSuccess = true, Message = "Asm header deleted" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<bool>() { IsSuccess = false, Message = "Failed to delete Asm header", Value = false });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeletePlanItemAsync([FromBody] int itemId)
        {
            try
            {
                var findItem = await _context.AsmDetails.SingleOrDefaultAsync(x => x.AsmDetailId == itemId);
                findItem.IsActive = false;
                findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                findItem.UpdatedDateTime = DateTime.Now;
                _context.AsmDetails.Update(findItem);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<bool>() { Value = true, IsSuccess = true, Message = "Plan item deleted" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<bool>() { IsSuccess = false, Message = "failed to delete plan item", Value = false });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteMasterItemAsync([FromBody] int itemId)
        {
            try
            {
                var findItem = await _context.MasterItems.SingleOrDefaultAsync(x => x.MasterItemId == itemId);
                findItem.IsActive = false;
                findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                findItem.UpdatedDateTime = DateTime.Now;
                _context.MasterItems.Update(findItem);
                await _context.SaveChangesAsync();
                //inactivate the asm detail as well
                string updateBy = User.Identity.Name.Split('@')[0];
                var asmDetailItems = _context.AsmDetails.Where(x => x.MasterItemId == itemId);
                await asmDetailItems.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));
                return Ok(new ResponseModel<bool>() { Value = true, IsSuccess = true, Message = "Master item deleted" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<bool>() { IsSuccess = false, Message = "failed to delete master item", Value = false });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateAssemblyAsync([FromBody] AsmHeaderModel assembly)
        {
            try
            {
                var updatedBy = User.Identity.Name.Split('@')[0];

                var findAssembly = await _context.AsmHeaders.SingleOrDefaultAsync(x => x.AsmHeaderId == assembly.AsmHeaderId);
                findAssembly.IsActive = true;
                //findAssembly.AssemblyCode = assembly.AssemblyCode; assembly code should not be editable
                findAssembly.AssemblyNotes = assembly.AssemblyNotes;
                findAssembly.AssemblyDesc = assembly.AssemblyDesc;
                findAssembly.IsElevation = assembly.BoolIsElevation ? "T" : "F";
                //findAssembly.IsBaseHouse = assembly.BoolIsBaseHouse ? "T" : "F"; //base house options are different, cannot change this
                findAssembly.HomeAreaId = assembly.HomeAreaId;
                findAssembly.AssemblySize = assembly.AssemblySize;
                findAssembly.AssemblyUnit = assembly.AssemblyUnit;
                findAssembly.UpdatedBy = updatedBy;
                findAssembly.UpdatedDateTime = DateTime.Now;
                _context.AsmHeaders.Update(findAssembly);
                await _context.SaveChangesAsync();

                // update plan(s), exclude existing
                // find if user removes plan
                var getMasterOptionId = _context.AsmHeaders.Where(x => x.AsmHeaderId == assembly.AsmHeaderId).Select(x => new { MasterOptionId = x.MasterOptionId, MasterPlanId = x.MasterPlanId, AssemblyCode = x.AssemblyCode, AssemblyDesc = x.AssemblyDesc }).FirstOrDefault();

                if (getMasterOptionId != null && getMasterOptionId.MasterOptionId != 0)
                {
                    //update available plan options for same master plan and master option
                    var findPreviousAvailablePlanOptions = _context.AvailablePlanOptions.Where(x => x.MasterOptionId == getMasterOptionId.MasterOptionId && x.MasterPlanId == getMasterOptionId.MasterPlanId &&  x.IsActive == true);

                    if (findPreviousAvailablePlanOptions.Any())
                    {
                        // deactivate
                        //await findPreviousAvailablePlanOptions.ExecuteUpdateAsync(a => a.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updatedBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));//why deactivate???

                        //update with new properties - same option in same plan, could be multiple communities
                        foreach (var option in findPreviousAvailablePlanOptions)
                        {
                            option.ModifiedOptionDesc = assembly.AssemblyDesc;
                            option.IsElevation = assembly.BoolIsElevation ? "T" : "F";
                            option.UpdatedBy = updatedBy;
                            option.UpdatedDateTime = DateTime.Now;
                        }
                        await _context.AvailablePlanOptions.BulkUpdateAsync(findPreviousAvailablePlanOptions);
                    }
                }

                //11/19 disable for now. Loop is slow. Need to revisit if this is what is wanted. 
                //updates option in other plans
                //if (assembly.MasterPlanIds != null && assembly.MasterPlanIds.Any())
                //{
                //    //this loop is way too slow
                //    foreach (var planId in assembly.MasterPlanIds)
                //    {
                //        //var findExisting = _context.AsmHeaders.Where(x => x.MasterPlanId == planId && x.MasterOptionId == getMasterOptionId.MasterOptionId && x.IsActive == true);
                //        var findExisting = _context.AsmHeaders.Where(x => x.MasterPlanId == planId && x.MasterOptionId == getMasterOptionId.MasterOptionId);//including inactive so they could be reactivate
                //        var findPlanNum = _context.MasterPlans.Where(x => x.MasterPlanId == planId).Select(x => x.PlanNum).SingleOrDefault();
                //        var setAssemblyCode = $"{findPlanNum}{assembly.MasterOptionCode}";

                //        // Asm Header
                //        // New
                //        if (!findExisting.Any())
                //        {
                //            //this happens if user chooses to add to a plan that didn't already have the option 
                //            await _context.AsmHeaders.AddAsync(new AsmHeader
                //            {
                //                MasterPlanId = planId,
                //                MasterOptionId = getMasterOptionId.MasterOptionId,
                //                HomeAreaId = 1,
                //                AssemblyCode = setAssemblyCode,
                //                AssemblyDesc = getMasterOptionId.AssemblyDesc,
                //                IsElevation = assembly.BoolIsElevation ? "T" : "F",
                //                CreatedBy = updatedBy,
                //                CreatedDateTime = DateTime.Now,
                //                IsActive = true
                //            });
                //            await _context.SaveChangesAsync();
                //        }
                //        //Reactivate
                //        else
                //        {
                //            //reactivate and update properties
                //            await findExisting.ExecuteUpdateAsync(a => a.SetProperty(b => b.AssemblyCode, setAssemblyCode).SetProperty(b => b.AssemblyDesc, getMasterOptionId.AssemblyDesc).SetProperty(b => b.IsElevation, assembly.BoolIsElevation ? "T" : "F").SetProperty(b => b.IsActive, true).SetProperty(b => b.UpdatedBy, updatedBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));
                //        }

                //        // Available Plan Options in Subdivisions
                //        // TODO: Add Attributes if the Assembly Code contains attribute. Need to double check this if it makes sense to add (09/30/2024)
                //        var findPhasePlanIds = _context.PhasePlans.Where(x => x.MasterPlanId == planId).ToList();

                //        if (findPhasePlanIds != null && findPhasePlanIds.Any())
                //        {
                //            foreach (var phasePlanId in findPhasePlanIds)
                //            {
                //                // Check existing
                //                var findPreviousAvailablePlanOptionsInPhase = _context.AvailablePlanOptions.Where(x => x.MasterPlanId == phasePlanId.MasterPlanId && x.MasterOptionId == getMasterOptionId.MasterOptionId && x.PhasePlanId == phasePlanId.PhasePlanId);

                //                // Reactivate/update
                //                if (findPreviousAvailablePlanOptionsInPhase.Any())
                //                {
                //                    await findPreviousAvailablePlanOptionsInPhase.ExecuteUpdateAsync(a => a.SetProperty(b => b.IsActive, true).SetProperty(b => b.ModifiedOptionDesc, getMasterOptionId.AssemblyDesc).SetProperty(b => b.IsElevation, assembly.BoolIsElevation ? "T" : "F").SetProperty(b => b.UpdatedBy, updatedBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));
                //                }
                //                // New
                //                else
                //                {
                //                    var getOptionGroupId = _context.MasterOptions.Where(x => x.OptionId == getMasterOptionId.MasterOptionId).Select(x => x.OptionGroupId).FirstOrDefault();

                //                    await _context.AvailablePlanOptions.AddAsync(new AvailablePlanOption
                //                    {
                //                        PhasePlanId = phasePlanId.PhasePlanId,
                //                        MasterPlanId = phasePlanId.MasterPlanId,
                //                        HomeAreaId = 1,
                //                        SubdivisionId = phasePlanId.SubdivisionId,
                //                        MasterOptionId = getMasterOptionId.MasterOptionId,
                //                        CutoffStageId = 3, //Default?
                //                        OptionGroupId = (int)getOptionGroupId,
                //                        OptionTypeId = 6, //Default? - Per Julie 11/19/24, 6 (Finance) is the default
                //                        OptionCode = getMasterOptionId.AssemblyCode,
                //                        OptionLongDesc = getMasterOptionId.AssemblyDesc,
                //                        MarginType = 0,
                //                        MarginPercent = 71,
                //                        CreatedBy = updatedBy,
                //                        CreatedDateTime = DateTime.Now,
                //                        IsActive = true
                //                    });
                //                    await _context.SaveChangesAsync();
                //                    // Add attributes. Need to double check this if it makes sense to add (09/30/2024)
                //                }
                //            }
                //        }
                //    }
                //}
                return Ok(new ResponseModel<AsmHeaderModel>() { Value = assembly, IsSuccess = true, Message = "Success updating assembly" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<AsmHeaderModel>() { IsSuccess = false, Message = "failed to update assembly", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateActivityAsync([FromBody] PactivityModel item)
        {
            try
            {
                var findItem = await _context.Pactivities.SingleOrDefaultAsync(x => x.PactivityId == item.PactivityId);
                findItem.IsActive = true;
                findItem.Description = item.Description;
                findItem.Activity = item.Activity;
                findItem.Releasecode = item.Releasecode;
                findItem.TradeId = item.TradeId;
                findItem.Taxable = item.IsTaxable ? "T" : "F";
                findItem.Notes = item.Notes;
                findItem.IncludeSelectionsOnPo = item.IsIncludeSelections ? "T" : "F";
                findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                findItem.UpdatedDateTime = DateTime.Now;
                _context.Pactivities.Update(findItem);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<PactivityModel>() { Value = item, IsSuccess = true, Message = "Activity updated" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<PactivityModel>() { IsSuccess = false, Message = "failed to update activity", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateAsmDetailAsync([FromBody] AsmDetailDto item)
        {
            try
            {
                var findItem = _context.AsmDetails.SingleOrDefault(x => x.AsmDetailId == item.AsmDetailId);
                findItem.IsActive = true;
                //findItem.Formula = item.Formula; //not editable in wms in manage options
                findItem.Factor = item.Factor;//This is actually quantity
                findItem.OptionItemNotes = item.OptionItemNotes;
                //findItem.Calculation = item.Calculation;//not editable in wms in manage options
                findItem.BomClassId = item.BomClassId;
                findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                findItem.UpdatedDateTime = DateTime.Now;
                _context.AsmDetails.Update(findItem); //Update range
                await _context.SaveChangesAsync();

                //update takeoff unit in the master item
                var findMasterItem = _context.MasterItems.SingleOrDefault(x => x.MasterItemId == item.MasterItemId);
                findMasterItem.TakeoffUnit = item.SelectAtTakeoffUnit; //using select at takeoffunit as temporary placeholder field to avoid adding the whole master item into asmdetaildto
                _context.MasterItems.Update(findMasterItem);
                await _context.SaveChangesAsync();

                return Ok(new ResponseModel<AsmDetailDto>() { Value = item, IsSuccess = true, Message = "Asm detail updated" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<AsmDetailDto>() { IsSuccess = false, Message = "failed to update asm detail", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateAsmDetailsAsync([FromBody] AsmDetailDto item)
        {
            try
            {

                var findDetail =  _context.AsmDetails.Where(x => x.AsmDetailId == item.AsmDetailId).FirstOrDefault();
                if(findDetail != null)
                {
                    findDetail.IsActive = true;
                    findDetail.Formula = item.Formula;
                    findDetail.Factor = item.Factor;
                    findDetail.OptionItemNotes = item.OptionItemNotes;
                    findDetail.BomClassId = item.BomClassId;
                    findDetail.SelectAtTakeoffUnit = item.SelectAtTakeoffUnit;
                    findDetail.UpdatedBy = User.Identity.Name.Split('@')[0];
                    findDetail.UpdatedDateTime = DateTime.Now;
                }
                _context.AsmDetails.Update(findDetail);
                await _context.SaveChangesAsync();
                //NOTE 12/12 Julie does not want the item qty updated in all asmdetails that have the item, so disabling below
                //var itemsFound = _context.AsmDetails.Where(x => x.MasterItemId == item.MasterItemId).ToList();
                //foreach (var currentItem in itemsFound)
                //{
                //    currentItem.IsActive = true;
                //    //findItem.Formula = item.Formula; //not editable in wms in manage options
                //    currentItem.Factor = item.Factor;//This is actually quantity
                //    currentItem.OptionItemNotes = item.OptionItemNotes;
                //    //findItem.Calculation = item.Calculation;//not editable in wms in manage options
                //    currentItem.BomClassId = item.BomClassId;
                //    currentItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                //    currentItem.UpdatedDateTime = DateTime.Now;
                //    _context.AsmDetails.Update(currentItem); //Update range
                //    await _context.SaveChangesAsync();
                //}

                return Ok(new ResponseModel<AsmDetailDto>() { Value = item, IsSuccess = true, Message = "Asm details updated" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<AsmDetailDto>() { IsSuccess = false, Message = "failed to update asm details", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteAsmDetailAsync([FromBody] AsmDetailDto item)
        {
            try
            {
                var findItem = _context.AsmDetails.SingleOrDefault(x => x.AsmDetailId == item.AsmDetailId);
                findItem.IsActive = false;
                findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                findItem.UpdatedDateTime = DateTime.Now;
                _context.AsmDetails.Update(findItem);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<AsmDetailDto>() { Value = item, IsSuccess = true, Message = "Asm detail deleted" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<AsmDetailDto>() { IsSuccess = false, Message = "failed to delete asm detail", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdateItemAsync([FromBody] MasterItemModel item)
        {            
            try
            {
                var findItem = await _context.MasterItems.SingleOrDefaultAsync(x => x.MasterItemId == item.MasterItemId);
                findItem.IsActive = true;
                findItem.ItemNotes = item.ItemNotes;
                findItem.PeUnitPrice = item.PeUnitPrice;
                findItem.RndUnit = item.RndUnit;
                findItem.RndDir = item.RndDir == "Up To Multiple" ? "U" : "N";
                findItem.ItemDesc = item.ItemDesc;
                findItem.Formula = item.Formula;
                findItem.EstDbOwner = item.EstDbOwner;
                findItem.ExcludeFromPo = item.ExcludeFromPo;
                findItem.DeletedFromPe = item.DeletedFromPe;
                findItem.CnvFctr = item.CnvFctr;
                findItem.CalcBasis = item.CalcBasis;
                findItem.CalcPercent = item.CalcPercent;
                findItem.Taxable = item.Taxable;
                findItem.ExcludeFromPo = item.ExcludeFromPo;
                findItem.TakeoffUnit = item.TakeoffUnit;
                findItem.OrderUnit = item.OrderUnit;
                findItem.PlanSpecific = item.PlanSpecific;//This actually means lump sum?
                findItem.BomClassId = item.BomClassId;
                findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                findItem.UpdatedDateTime = DateTime.Now;
                _context.MasterItems.Update(findItem);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<MasterItemModel>() { Value = item, IsSuccess = true, Message = "Item updated" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MasterItemModel>() { IsSuccess = false, Message = "failed to update item", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddItemToOptionAsync([FromBody] ModelManagerItemModel item)
        {
            //This is for adding item to plan option
            //Add new master item here if the selected one is option specific, else just add new asm detail/header to add it to an option in a plan
            ///also, it seems there should be a picker for quantity, automatically put 1 and so on....
            try
            {
                var masterItem = _context.MasterItems.SingleOrDefault(x => x.MasterItemId == item.MasterItemId);

                if (masterItem.PlanSpecific == "T")
                {
                    //option specific item (plan specific = T)
                    if (_context.AsmDetails.Any(x => x.MasterItemId == item.MasterItemId && x.AsmHeaderId == item.AsmHeaderId))
                    {
                        return Ok(new ResponseModel<ModelManagerItemModel>() { IsSuccess = false, Value = item, Message = "Item exists in this option" }); //Don't add the item again if it exists    //TODO: if the item exists but was deactivated, //TODO: if it exists but they were changing its quantity
                    }
                    var getOption = _context.AsmHeaders.SingleOrDefault(x => x.AsmHeaderId == item.AsmHeaderId);
                    var phaseToAdd = new MasterItemPhasis()
                    {
                        PhaseCode = getOption.AssemblyCode,
                        PhaseDesc = getOption.AssemblyDesc,
                        PhaseNotes = getOption.AssemblyNotes,
                        AsmHeaderId = item.AsmHeaderId,
                        CreatedBy = User.Identity.Name.Split('@')[0],
                    };
                    _context.MasterItemPhases.Add(phaseToAdd);
                    await _context.SaveChangesAsync();

                    var masterItemToAdd = new MasterItem()
                    {
                        MasterItemPhaseId = phaseToAdd.MasterItemPhaseId,
                        ItemNumber = masterItem.ItemNumber,
                        ItemDesc = masterItem.ItemDesc,
                        BomClassId = masterItem.BomClassId,//everything else should copy from original master item
                        PlanSpecific = "T",
                        PeCategoryCode = masterItem.PeCategoryCode,
                        PeUnitPrice = masterItem.PeUnitPrice,
                        PeUnitPriceDtCg = masterItem.PeUnitPriceDtCg,
                        CalcPercent = masterItem.CalcPercent,
                        DeletedFromPe = masterItem.DeletedFromPe,
                        ExcludeFromPo = masterItem.ExcludeFromPo,
                        OrderUnit = masterItem.OrderUnit,
                        TakeoffUnit = masterItem.TakeoffUnit,
                        Taxable = masterItem.Taxable,
                        CalcBasis = masterItem.CalcBasis,
                        CnvFctr = masterItem.CnvFctr,
                        CreatedBy = User.Identity.Name.Split('@')[0],
                        Formula = masterItem.Formula,
                        ItemNotes = masterItem.ItemNotes,
                        JcCategory = masterItem.JcCategory,
                        JcPhase = masterItem.JcPhase,
                        Multdiv = masterItem.Multdiv,
                    };
                    _context.MasterItems.Add(masterItemToAdd);
                    _context.SaveChanges();

                    var addAsmDetail = new AsmDetail()
                    {
                        MasterItemId = masterItemToAdd.MasterItemId,
                        AsmHeaderId = item.AsmHeaderId,
                        Factor = item.Factor,//this is quantity
                        IsActive = true,
                        CreatedBy = User.Identity.Name.Split('@')[0]
                    };
                    _context.AsmDetails.Add(addAsmDetail);
                    await _context.SaveChangesAsync();
                }

                else
                {
                    //not option-specific
                    if (_context.AsmDetails.Any(x => x.MasterItemId == item.MasterItemId && x.AsmHeaderId == item.AsmHeaderId))
                    {
                        return Ok(new ResponseModel<ModelManagerItemModel>() { IsSuccess = false, Value = item, Message = "Item exists in this option" });
                    }
                    var addAsmDetail = new AsmDetail()
                    {
                        MasterItemId = item.MasterItemId,
                        AsmHeaderId = item.AsmHeaderId,
                        Factor = item.Factor,//this is quantity
                        IsActive = true,
                        CreatedBy = User.Identity.Name.Split('@')[0]
                    };
                    _context.AsmDetails.Add(addAsmDetail);
                    await _context.SaveChangesAsync();
                }
                item.ItemDesc = masterItem.ItemDesc;
                return Ok(new ResponseModel<ModelManagerItemModel>() { IsSuccess = true, Value = item, Message = "Added item to option" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return Ok(new ResponseModel<ModelManagerItemModel>() { IsSuccess = false, Value = item, Message = "Error adding item to option" });
            }        
        }

        [HttpPost]
        public async Task<IActionResult> AddItemsToMasterPlanOptionAsync([FromBody] List<ModelManagerItemModel> items)
        {
            //This is for adding item to plan option
            //Add new master item here if the selected one is option specific, else just add new asm detail/header to add it to an option in a plan
            foreach (var item in items)
            {
                //if it was a lump sum item (plan specific = true) also add a master item phase and a new master item (copy of previous lump sum item). 
                var masterItem = _context.MasterItems.SingleOrDefault(x => x.MasterItemId == item.MasterItemId);

                // Get assembly_code based on asm_header_id to see what other master_plan_id uses it. Pick where master_plan_id = 1 since it'll not be prepended with the Plan Num.
                var findOtherAssembly = _context.AsmHeaders.Where(x => x.AsmHeaderId == item.AsmHeaderId && x.MasterPlanId == 1).Select(x => x.AssemblyCode).FirstOrDefault();

                try
                {  
                    // Can't re-add the same item if it's already existed. Except, if it's a Plan Specific. A little odd, but this is what I found after several debugs as of 09/30/2024.
                    if (masterItem.PlanSpecific == "T")
                    {
                        //option specific item (plan specific = T)
                        var findExisting = _context.AsmDetails.Where(x => x.MasterItemId == item.MasterItemId && x.AsmHeaderId == item.AsmHeaderId);

                        if (!findExisting.Any())
                        {
                            var getOption = _context.AsmHeaders.SingleOrDefault(x => x.AsmHeaderId == item.AsmHeaderId);

                            var phaseToAdd = new MasterItemPhasis()
                            {
                                PhaseCode = getOption.AssemblyCode,
                                PhaseDesc = getOption.AssemblyDesc,
                                PhaseNotes = getOption.AssemblyNotes,
                                AsmHeaderId = item.AsmHeaderId,
                                CreatedBy = User.Identity.Name.Split('@')[0],
                            };

                            _context.MasterItemPhases.Add(phaseToAdd);
                            await _context.SaveChangesAsync();

                            var masterItemToAdd = new MasterItem()
                            {
                                MasterItemPhaseId = phaseToAdd.MasterItemPhaseId,
                                ItemNumber = masterItem.ItemNumber,//TODO: does the item number change to the phase code??? 
                               //ItemDesc = phaseToAdd.PhaseDesc,
                                ItemDesc = masterItem.ItemDesc,
                                BomClassId = masterItem.BomClassId,//everything else should copy from original master item
                                PlanSpecific = "T",
                                PeCategoryCode = masterItem.PeCategoryCode,
                                PeUnitPrice = masterItem.PeUnitPrice,
                                PeUnitPriceDtCg = masterItem.PeUnitPriceDtCg,
                                CalcPercent = masterItem.CalcPercent,
                                DeletedFromPe = masterItem.DeletedFromPe,
                                ExcludeFromPo = masterItem.ExcludeFromPo,
                                OrderUnit = masterItem.OrderUnit,
                                TakeoffUnit = masterItem.TakeoffUnit,
                                Taxable = masterItem.Taxable,
                                CalcBasis = masterItem.CalcBasis,
                                CnvFctr = masterItem.CnvFctr,
                                CreatedBy = User.Identity.Name.Split('@')[0],
                                Formula = masterItem.Formula,
                                ItemNotes = masterItem.ItemNotes,
                                JcCategory = masterItem.JcCategory,
                                JcPhase = masterItem.JcPhase,
                                Multdiv = masterItem.Multdiv,
                            };
                            _context.MasterItems.Add(masterItemToAdd);
                            _context.SaveChanges();

                            var addAsmDetail = new AsmDetail()
                            {
                                MasterItemId = masterItemToAdd.MasterItemId,
                                AsmHeaderId = item.AsmHeaderId,
                                Factor = item.Factor,//this is quantity
                                IsActive = true,
                                CreatedBy = User.Identity.Name.Split('@')[0]
                            };
                            _context.AsmDetails.Add(addAsmDetail);
                            await _context.SaveChangesAsync();

                            // Add items to all plans that have the option, exclude the ones already added
                            if (!string.IsNullOrWhiteSpace(findOtherAssembly))
                            {
                                var findAsmHeaders = _context.AsmHeaders.Where(x => x.AssemblyCode == findOtherAssembly && x.AsmHeaderId != item.AsmHeaderId);

                                if (findAsmHeaders != null && findAsmHeaders.Any())
                                {
                                    foreach (var asmHeader in findAsmHeaders)
                                    {
                                        _context.AsmDetails.Add(new AsmDetail
                                        {
                                            MasterItemId = masterItemToAdd.MasterItemId,
                                            AsmHeaderId = asmHeader.AsmHeaderId,
                                            Factor = item.Factor,//this is quantity
                                            IsActive = true,
                                            CreatedBy = User.Identity.Name.Split('@')[0]
                                        });
                                    }

                                    await _context.SaveChangesAsync();
                                }
                            }
                        }
                    }
                    else
                    {
                        //not option-specific
                        var findExisting = _context.AsmDetails.Where(x => x.MasterItemId == item.MasterItemId && x.AsmHeaderId == item.AsmHeaderId).ToList();
                        if (findExisting.Count != 0)
                        {
                            //item exists
                            //reactivate if it was inactive
                            if(findExisting.Any(x => x.IsActive == false))
                            {
                                var reactivateItem = findExisting.First(x => x.IsActive == false);
                                reactivateItem.IsActive = true;
                                reactivateItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                                reactivateItem.UpdatedDateTime = DateTime.Now;
                                _context.AsmDetails.Update(reactivateItem);
                                await _context.SaveChangesAsync();
                            }

                        }
                        else
                        {
                            //item not exists
                            var addAsmDetail = new AsmDetail()
                            {
                                MasterItemId = item.MasterItemId,
                                AsmHeaderId = item.AsmHeaderId,
                                Factor = item.Factor,//this is quantity
                                IsActive = true,
                                CreatedBy = User.Identity.Name.Split('@')[0]
                            };
                            _context.AsmDetails.Add(addAsmDetail);
                            await _context.SaveChangesAsync();

                            // Add items to all plans that have the option
                            if (!string.IsNullOrWhiteSpace(findOtherAssembly))
                            {
                                var findAsmHeaders = _context.AsmHeaders.Where(x => x.AssemblyCode == findOtherAssembly && x.AsmHeaderId != item.AsmHeaderId);

                                if (findAsmHeaders != null && findAsmHeaders.Any())
                                {
                                    foreach (var asmHeader in findAsmHeaders)
                                    {
                                        _context.AsmDetails.Add(new AsmDetail
                                        {
                                            MasterItemId = item.MasterItemId,
                                            AsmHeaderId = asmHeader.AsmHeaderId,
                                            Factor = item.Factor,//this is quantity
                                            IsActive = true,
                                            CreatedBy = User.Identity.Name.Split('@')[0]
                                        });
                                    }

                                    await _context.SaveChangesAsync();
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                    _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                    return StatusCode(500, new ResponseModel<List<ModelManagerItemModel>>() { IsSuccess = false, Message = "failed to add items", Value = null });
                }
            }
            return Ok(new ResponseModel<List<ModelManagerItemModel>>() { Value = items, IsSuccess = true, Message = "Added master item" });

        }

        [HttpPost]
        public async Task<IActionResult> AddMasterItemAsync([FromBody] ModelManagerItemModel item)
        {
            //This is for adding new master item. Not in a plan.          
            //insert new phase
            var newPhase = new MasterItemPhasis()
            {
                PhaseDesc = item.PhaseDesc,
                PhaseCode = item.ItemNumber,//Should this be item.phase code?
                CreatedBy = User.Identity.Name.Split('@')[0]
            };
            try
            {
                _context.MasterItemPhases.Add(newPhase);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ModelManagerItemModel>() { IsSuccess = false, Message = "failed to add master item", Value = null });
            }

            var addItem = new MasterItem();
            addItem.BomClassId = (int)item.BomClassId;
            addItem.MasterItemPhaseId = newPhase.MasterItemPhaseId;
            addItem.IsActive = true;
            addItem.PeCategoryCode = item.PeCategoryCode;
            addItem.ItemNumber = item.ItemNumber;
            addItem.TakeoffUnit = item.TakeoffUnit;
            addItem.Taxable = item.IsTaxable ? "T" : "F";
            addItem.Waste = item.Waste;
            addItem.UseWaste = item.UseWaste;
            addItem.OrderUnit = item.OrderUnit;
            addItem.Multdiv = item.Multdiv == "Multiply" ? "M" : item.Multdiv == "Divide" ? "D" : null;
            addItem.ItemNotes = item.ItemNotes;
            addItem.PeUnitPrice = item.PeUnitPrice;
            addItem.PeUnitPriceDtCg = item.PeUnitPriceDtCg;
            addItem.RndUnit = item.RndUnit;
            addItem.RndDir = item.RndDir == "Up To Multiple" ? "U" : "N";
            addItem.ItemDesc = item.ItemDesc;
            addItem.Formula = item.Formula;
            addItem.EstDbOwner = item.EstDbOwner;
            addItem.ExcludeFromPo = item.IsExcludeFromPO ? "T" : "F";
            addItem.DeletedFromPe = item.DeletedFromPe;
            addItem.CnvFctr = item.CnvFctr;
            addItem.CalcBasis = item.CalcBasis;
            addItem.CalcPercent = item.CalcPercent;
            addItem.PlanSpecific = item.IsPlanSpecific ? "T" :"F";
            addItem.CreatedBy = User.Identity.Name.Split('@')[0];
            addItem.CreatedDateTime = DateTime.Now;
            try
            {
                _context.MasterItems.Add(addItem);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ModelManagerItemModel>() { IsSuccess = false, Message = "failed to add master item", Value = null });
            }
            item.MasterItemId = addItem.MasterItemId;
            return Ok(new ResponseModel<ModelManagerItemModel>() { Value = item, IsSuccess = true, Message = "Master item added" });
        }

        [HttpPost]
        public async Task<IActionResult> AddActivityAsync([FromBody] PactivityModel item)
        {
            //This is for adding new purchasing activity, into a trade/bom class
            var addItem = new BomClass();
            try
            {
               
                addItem.IsActive = true;
                addItem.Description = item.Description;
                addItem.BomClass1 = item.Activity; 
                addItem.DefaultPhaseCode = item.Activity.Length >= 3 ? item.Activity.Substring(0, 3) : item.Activity;
                addItem.TradeId = item.TradeId;
                addItem.Releasecode = item.Releasecode;
                addItem.CreatedBy = User.Identity.Name.Split('@')[0];
                addItem.CreatedDateTime = DateTime.Now;
                _context.BomClasses.Add(addItem);
                await _context.SaveChangesAsync();
                item.BomClassId = addItem.BomClassId;
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<PactivityModel>() { IsSuccess = false, Message = "failed to add activity", Value = null });
            }

            //then add the pactivity
            var addactivity = new Pactivity();
            try
            {
                
                addactivity.DivId = 1;//This is default for now, since we don't use divisions
                addactivity.Poindex = item.Activity.Length >= 3 ? item.Activity.Substring(0, 3) : item.Activity;
                addactivity.Releasecode = item.Releasecode;
                addactivity.IsActive = true;
                addactivity.BomClassId = addItem.BomClassId;
                addactivity.Activity = item.Activity;
                addactivity.TradeId = item.TradeId;
                addactivity.Description = item.Description;
                addactivity.Taxable = item.IsTaxable ? "T" : "F";
                addactivity.IncludeSelectionsOnPo = item.IsIncludeSelections ? "T" : "F";
                addactivity.Notes = item.Notes;               
                addactivity.CreatedBy = User.Identity.Name.Split('@')[0];
                addactivity.CreatedDateTime = DateTime.Now;
                _context.Pactivities.Add(addactivity);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<PactivityModel>() { IsSuccess = false, Message = "failed to add activity", Value = null });
            }

            //insert new phase
            var newPhase = new MasterItemPhasis()
            {
                PhaseDesc = item.Activity,
                PhaseCode = item.Activity?.Substring(0,3),
                CreatedBy = User.Identity.Name.Split('@')[0]
            };
            try
            {
                _context.MasterItemPhases.Add(newPhase);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<PactivityModel>() { IsSuccess = false, Message = "failed to add activity", Value = null });
            }


            var addDefaultItem = new MasterItem()
            {
                MasterItemPhaseId = newPhase.MasterItemPhaseId,
                BomClassId = addItem.BomClassId,
                OrderUnit = "LS",
                TakeoffUnit = "LS",
                Taxable = "T",
                PlanSpecific = "T",//This indicates Lump Sum item
                ItemNumber = item.Activity?.Substring(0,3),
                ItemDesc = item.Description,
                CreatedBy = User.Identity.Name.Split('@')[0],
                CreatedDateTime = DateTime.Now,
            };
            _context.MasterItems.Add(addDefaultItem);
            await _context.SaveChangesAsync();

            return Ok(new ResponseModel<PactivityModel>() { IsSuccess = true, Message = "Activity Added", Value = new PactivityModel() { PactivityId = addactivity.PactivityId } });
        }

        [HttpPost]
        public async Task<IActionResult> AddTradeAsync([FromBody] TradeDto tradeToAdd)
        {
            try
            {                
                var addTrade = new Trade();
                addTrade.IsActive = true;
                addTrade.TradeDesc = tradeToAdd.TradeDesc;
                addTrade.TradeName = tradeToAdd.TradeName;
                addTrade.CreatedBy = User.Identity.Name.Split('@')[0];
                addTrade.CreatedDateTime = DateTime.Now;
                _context.Trades.Add(addTrade);
                await _context.SaveChangesAsync();

                //create the default bom class
                //var addBomClass = new BomClass();
                //addBomClass.IsActive = true;
                //addBomClass.BomClass1 = tradeToAdd.TradeName;//???
                //addBomClass.DefaultPhaseCode = tradeToAdd.TradeName;//TODO: what should this be
                //addBomClass.CreatedBy = User.Identity.Name.Split('@')[0];
                //addBomClass.CreatedDateTime = DateTime.Now;
                //_context.BomClasses.Add(addBomClass);
                //await _context.SaveChangesAsync();
                
                ////then add the pactivity
                //var addActivity = new Pactivity();
                //addActivity.DivId = 1;//This is default for now, since we don't use divisions
                //addActivity.Releasecode = tradeToAdd.TradeName.Substring(0, 3);//TODO: check
                //addActivity.IsActive = true;
                //addActivity.BomClassId = addBomClass.BomClassId;
                //addActivity.Activity = tradeToAdd.TradeName;//WMS sets default activity is same as trade name
                //addActivity.TradeId = addTrade.TradeId;
                //addActivity.Description = tradeToAdd.TradeDesc;
                //addActivity.Taxable = "T";//this seems to be the default WMS puts in 
                //addActivity.IncludeSelectionsOnPo = "F";
                //addActivity.CreatedBy = User.Identity.Name.Split('@')[0];
                //addActivity.CreatedDateTime = DateTime.Now;
                //_context.Pactivities.Add(addActivity);
                //await _context.SaveChangesAsync();
               
                ////insert new phase
                //var newPhase = new MasterItemPhasis()
                //{
                //    PhaseDesc = tradeToAdd.TradeName + " " + tradeToAdd.TradeName,//WMS Seems to duplicate the trade name for the phase??
                //    PhaseCode = tradeToAdd.TradeName?.Substring(0, 3),
                //    CreatedBy = User.Identity.Name.Split('@')[0]
                //};

                //_context.MasterItemPhases.Add(newPhase);
                //await _context.SaveChangesAsync();
               

                //var addDefaultItem = new MasterItem()
                //{
                //    MasterItemPhaseId = newPhase.MasterItemPhaseId,
                //    BomClassId = addBomClass.BomClassId,
                //    OrderUnit = "LS",//These seem to be defaut in WMS
                //    TakeoffUnit = "LS",
                //    Taxable = "T",
                //    PlanSpecific = "T",//This indicates Lump Sum item
                //    RndDir = "N",
                //    Multdiv = "D",
                //    ItemNumber = tradeToAdd.TradeName?.Substring(0, 3),//OR should this be the three digit code?
                //    ItemDesc = tradeToAdd.TradeDesc,//TODO: what should this be
                //    CreatedBy = User.Identity.Name.Split('@')[0],
                //    CreatedDateTime = DateTime.Now,
                //};
                //_context.MasterItems.Add(addDefaultItem);
                //await _context.SaveChangesAsync();

                return new OkObjectResult(new ResponseModel<TradeDto> { Value = tradeToAdd, IsSuccess = true, Message = "Trade added" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TradeDto> { IsSuccess = false, Message = "Failed to add trade" });
            }
        }
            
        [HttpPut]
        public async Task<IActionResult> UpdateTradeAsync([FromBody] TradeDto updateTrade)
        {
            try
            {
                var findTrade = _context.Trades.SingleOrDefault(x => x.TradeId == updateTrade.TradeId);
                findTrade.IsActive = true;
                findTrade.TradeDesc = updateTrade.TradeDesc;
                findTrade.TradeName = updateTrade.TradeName;
                findTrade.UpdatedBy = User.Identity.Name.Split('@')[0];
                findTrade.UpdatedDateTime = DateTime.Now;
                _context.Trades.Update(findTrade);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<TradeDto> { Value = updateTrade, IsSuccess = true, Message = "Trade updated" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TradeDto> { IsSuccess = false, Message = "Failed to update trade" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteTradeAsync([FromBody] TradeDto deleteTrade)
        {
            try
            {
                var findTrade = _context.Trades.SingleOrDefault(x => x.TradeId == deleteTrade.TradeId);
                findTrade.IsActive = false;
                findTrade.UpdatedDateTime = DateTime.Now;
                findTrade.CreatedDateTime = DateTime.Now;
                findTrade.UpdatedBy = User.Identity.Name.Split('@')[0];
                _context.Trades.Update(findTrade);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<TradeDto> { Value = deleteTrade, IsSuccess = true, Message = "Trade deleted" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<TradeDto> { IsSuccess = false, Message = "Failed to delete trade" });
            }
        }
    }    
}
