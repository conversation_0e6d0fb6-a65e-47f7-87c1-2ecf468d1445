﻿using ERP.Data.Models.Abstract;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class MasterAttributeGroupDto : IMapFrom<MasterAttributeGroup>
    {
        public int AttributeGroupId { get; set; }

        public string? Description { get; set; } = null!;

        public int Seq { get; set; }

        public bool UseText { get; set; }

        public bool? IsActive { get; set; }

        public DateTime CreatedDateTime { get; set; }

        public string? CreatedBy { get; set; }

        public DateTime? UpdatedDateTime { get; set; }

        public string? UpdatedBy { get; set; }

        public int? TotalItems { get; set; }

        public List<MasterAttributeItemDto>? MasterAttributeItems { get; set; }

        public string? ErrorMessage { get; set; }

        public int? AttributeGroupAssignmentId { get; set; }

        public int? MasterOptionId { get; set; }

        public int? PlanOptionId { get; set; }

        public string? PlanNum { get; set; }

        public int? MasterPlanId { get; set; }

        public int? SubdivisionId { get; set; }

        public string? SubdivisionName { get; set; }

        public bool? IsMaster { get; set; }
    }
}
