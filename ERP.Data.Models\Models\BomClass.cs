﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class BomClass
{
    public int BomClassId { get; set; }

    public string? BomClass1 { get; set; }

    public string? BomNotes { get; set; }

    public string? DeletedFromPe { get; set; }

    public int? EstDbId { get; set; }

    public int? EstDbOwner { get; set; }

    public string? Printlocations { get; set; }

    public string? Printschedule { get; set; }

    public string? Releasecode { get; set; }

    public int? TradeId { get; set; }

    public int? SactivityId { get; set; }

    public string? IncludeSelectionsOnPo { get; set; }

    public string? Taxable { get; set; }

    public string? Description { get; set; }

    public string? DefaultPhaseCode { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public string? UpdatedBy { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual ICollection<AsmDetail> AsmDetails { get; set; } = new List<AsmDetail>();

    public virtual ICollection<MasterItem> MasterItems { get; set; } = new List<MasterItem>();

    public virtual ICollection<Pactivity> Pactivities { get; set; } = new List<Pactivity>();
}
