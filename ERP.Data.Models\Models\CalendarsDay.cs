﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class CalendarsDay
{
    public int CalendarsDaysId { get; set; }

    public int CalendarId { get; set; }

    public DateTime WorkDate { get; set; }

    public string? Description { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;
}
