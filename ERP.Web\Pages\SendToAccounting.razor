﻿@page "/accounting"
@inject BudgetService BudgetService
@inject PoService PoService
@inject TradeService TradeService
@inject SubdivisionService SubdivisionService
@inject SelectedOptionsService SelectedOptionsService
@inject BCAPILogService BCAPILogService
@inject IJSRuntime JsRuntime
@inject IHostEnvironment Env
@inject AuthenticationStateProvider AuthenticationStateProvider
@attribute [Authorize(Roles = "Admin, Purchasing, ReadOnly, Accounting")]
@using ERP.Data.Models
@using System.Collections.ObjectModel
@using Telerik.DataSource.Extensions
@using System.Diagnostics


<style>

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }

    .noApproveCheckbox .k-grid-checkbox {
        display: none;
    }

    .k-table-td { /*these settings are to prevent the 0 width columns from affecting the column height*/
        padding: 4px !important;
        font-size: 11px;
        height: 30px !important;
        white-space: nowrap !important;
        overflow: visible;
    }

</style>

<PageTitle>PO's | Send To Accounting</PageTitle>
<TelerikTooltip TargetSelector=".tooltip-target" />
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<TelerikLoaderContainer Visible="@ShowWait" Text="Please wait..." />
<div class="container-fluid flex">
    <div class="col-lg-12">
        <div class="card" style="background-color: #2e5771">
            <div class="card-body" style="padding:0.5rem">
                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Send To Accounting</h7>
            </div>
        </div>
    </div>

    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/issuepo">Issue Purchase Orders</a></li>
        <li class="breadcrumb-item"><a href="/approvepos">Appprove Payment</a></li>
        <li class="breadcrumb-item active">Send To Accounting</li>
    </ol>

    <div class="row d-flex">
        <div class="col-lg-12">        
            <TelerikTabStrip ActiveTabIndexChanged="@TabChangedHandler">
                <TabStripTab Title="Budgets">
                    @if (AllBudgetData == null || IsLoading)
                    {
                        <p><em>Loading...</em></p>
                        <TelerikLoader Size="@ThemeConstants.Loader.Size.Small" Type="@LoaderType.ConvergingSpinner"/>
                    }
                    else
                    {
                        <TelerikTreeList Data="@AllBudgetData"
                        OnStateInit="@((TreeListStateEventArgs<CombinedJCETreeModel> args) => OnStateInitHandler(args))"
                        SelectionMode="@TreeListSelectionMode.Multiple"
                        @bind-SelectedItems= "@SelectedBudgetItems"
                        ScrollMode="@TreeListScrollMode.Virtual"
                        Height="80vh"
                        PageSize="40"
                        RowHeight="40"
                        IdField="Id"
                        ItemsField="Children"                                       
                        @ref="@BudgetTreeList"                                        
                        EditMode="@TreeListEditMode.Inline"
                        ConfirmDelete="true"
                        OnRowRender="@OnRowRenderHandler"
                        Width="100%">
                            <TreeListColumns>
                                <TreeListCheckboxColumn SelectChildren = "true" CheckBoxOnlySelection = "true" SelectAll="true" SelectAllMode=TreeListSelectAllMode.All />
                                <TreeListColumn Field="SearchTags" Title="" Width="0px"/>
                                <TreeListColumn Field="JobNumber" Title="Job/Option/Cost Code" Expandable="true" Editable="false" Visible="true">
                                    <Template>
                                        @{
                                            var item = context as CombinedJCETreeModel;
                                            if (item.JobNumber != null && item.ParentId == null)
                                            {
                                                @($"{item.JobNumber}")
                                            }
                                            else if (item.Estoption != null)
                                            {
                                                @($"{item.Estoption.OptionNumber} - {item.Estoption.OptionDesc}")
                                            }
                                            else
                                            {
                                                var costCodeDescription = AllCostCodes.FirstOrDefault(x => x.CostCode == item.Estjcedetail.Costcode).CcDescription;
                                                @($"{item.Estjcedetail.Costcode} - {costCodeDescription}")
                                            }
                                        }
                                    </Template>
                                </TreeListColumn>
                                <TreeListColumn Field="Estjcedetail.Costcode" Title="Cost Code">
                                    <Template>
                                        @{
                                            var item = context as CombinedJCETreeModel;
                                            if (item.Estjcedetail != null)
                                            {
                                                @($"{item.Estjcedetail.Costcode}")
                                                if (item.Estjcedetail.MissingJobTask == true)
                                                {
                                                    <span title="Job Task Missing in NAV" class="tooltip-target">
                                                        &nbsp;
                                                        <TelerikFontIcon Icon="@FontIcon.WarningCircle"></TelerikFontIcon>
                                                    </span>
                                                }
                                            }
                                            if (item.Estoption != null)
                                            {
                                                if (item.Children != null && item.Children.Any(x => x.Estjcedetail != null && x.Estjcedetail.MissingJobTask == true))
                                                {
                                                    <span title="Job Task Missing in NAV" class="tooltip-target">
                                                        &nbsp;
                                                        <TelerikFontIcon Icon="@FontIcon.WarningCircle"></TelerikFontIcon>
                                                    </span>
                                                }
                                            }
                                            if (item.JobNumber != null && item.ParentId == null)
                                            {
                                                if (item.Children != null && item.Children.SelectMany(x => x.Children).Any(x => x.Estjcedetail != null && x.Estjcedetail.MissingJobTask == true))
                                                {
                                                    <span title="Job Task Missing in NAV" class="tooltip-target">
                                                        &nbsp;
                                                        <TelerikFontIcon Icon="@FontIcon.WarningCircle"></TelerikFontIcon>
                                                    </span>
                                                }
                                            }
                                        }
                                    </Template>
                                </TreeListColumn>
                                <TreeListColumn Field="Estjcedetail.Category" Title="Category"></TreeListColumn>
                                <TreeListColumn Field="Estjcedetail.EstimateUnits" Title="Units"></TreeListColumn>
                                <TreeListColumn Field="Estjcedetail.UnitDesc" Title="Unit Desc"></TreeListColumn>
                                <TreeListColumn Field="Amount" DisplayFormat="{0:c2}" Title="Budget Amount"></TreeListColumn>
                                @* <TreeListColumn Field="Estjcedetail.EstimateAmount" DisplayFormat="{0:c2}" Title="Bugdet Amount"></TreeListColumn> *@
                                <TreeListColumn Field="Estjcedetail.EstimateDate" DisplayFormat="{0:MM/dd/yyyy}" Title="Issue Date"></TreeListColumn>                                
                            </TreeListColumns>
                            <TreeListToolBarTemplate>
                                <TreeListSearchBox />
                                @if(AllowEdit)
                                {                                 
                                    <TreeListCommandButton Title="Send To BC" Class="tooltip-target k-button-success" OnClick="@SendBudgetItemsToBC" Icon="@FontIcon.Envelop"></TreeListCommandButton>     
                                }
                            </TreeListToolBarTemplate>
                        </TelerikTreeList>
                    }                
                </TabStripTab>
                <TabStripTab Title="Approved PO With Invoice">
                    @if (AllRepushPOData == null || IsLoading)
                    {
                        <p><em>Loading...</em></p>
                        <TelerikLoader Size="@ThemeConstants.Loader.Size.Small" Type="@LoaderType.ConvergingSpinner" />
                    }
                    else
                    {
                        <TelerikTreeList Data="@AllRepushPOData"
                        OnStateInit="@((TreeListStateEventArgs<CombinedJCETreeModel> args) => OnRepushPoStateInitHandler(args))"
                        SelectionMode="@TreeListSelectionMode.Multiple"
                        @bind-SelectedItems="@SelectedRepushPOItems"
                        IdField="Id"
                        ItemsField="Children"
                        ScrollMode="@TreeListScrollMode.Virtual"
                        Height="80vh"
                        PageSize="40"
                        RowHeight="40"
                        @ref="@POTreeList"
                        EditMode="@TreeListEditMode.Inline"
                        ConfirmDelete="true"
                        OnRowRender="@OnRowRenderHandler"
                        Width="100%">
                            <TreeListColumns>
                                <TreeListCheckboxColumn SelectChildren="true" SelectAll="true" SelectAllMode=TreeListSelectAllMode.All />
                                <TreeListColumn Field="SearchTags" Title="" Width="0px" />
                                <TreeListColumn Field="JobNumber" Title="Job / Purchase Order / PO Item" Expandable="true" Width="300px" Editable="false" Visible="true">
                                    <Template>
                                        @{
                                            var item = context as CombinedJCETreeModel;
                                            if (item.JobNumber != null && item.ParentId == null && item.Supplier == null)
                                            {
                                                @($"{item.JobNumber}")
                                            }
                                            if (item.Poheader != null)
                                            {
                                                @($"{item.Poheader.Ponumber} - {item.Poheader.Podescription}")
                                            }
                                            if (item.Supplier != null )
                                            {
                                                @($"{item.Supplier.SubName}")
                                            }
                                            if(item.Podetail != null)
                                            {
                                                @($"{item.Podetail?.Poitemno} {item.Podetail?.Poitemdesc}")
                                                // @($"{item.Pojccdetail?.JccItemnumber} {item.Pojccdetail?.JccItemDesc}")
                                            }
                                        }
                                    </Template>
                                </TreeListColumn>
                                @if (ShowPOSupplierJobSort == "Job")
                                {
                                    <TreeListColumn Field="Poheader.SubNumberNavigation.SubName" Title="Supplier" Width="300px">
                                        <Template>
                                            @{
                                                var item = context as CombinedJCETreeModel;
                                                <span>@item.Poheader?.SubNumberNavigation?.SubName</span>
                                                if (item.Poheader?.SubNumberNavigation != null && item.Supplier?.Blocked == true)
                                                {
                                                    <span title="Supplier is Blocked" class="tooltip-target">
                                                        &nbsp;
                                                        <TelerikFontIcon Icon="@FontIcon.WarningCircle"></TelerikFontIcon>
                                                    </span>
                                                }
                                                if (item.Poheader?.SubNumberNavigation != null && item.Supplier?.InsuranceExpired == true)
                                                {
                                                    <span title="Supplier Insurance Expired" class="tooltip-target">
                                                        &nbsp;
                                                        <TelerikFontIcon Icon="@FontIcon.WarningTriangle"></TelerikFontIcon>
                                                    </span>
                                                }
                                            }
                                        </Template>
                                    </TreeListColumn>
                                }
                                else
                                {
                                    <TreeListColumn Field="Poheader.Pojobnumber" Title="Job Number" Width="120px">
                                    </TreeListColumn>
                                }
                                <TreeListColumn Field="Podetail.Poextra" Title="Option">
                                </TreeListColumn>
                                <TreeListColumn Field="Podetail.Pocategory" Title="Category">
                                    <Template>
                                        @{
                                            var item = context as CombinedJCETreeModel;
                                            if (item.Podetail != null)
                                            {
                                                @($"{item.Podetail.Pocategory}")
                                            }
                                        }
                                    </Template>
                                </TreeListColumn>
                                <TreeListColumn Field="Podetail.Pocostcode" Title="Cost Code">
                                    <Template>
                                        @{
                                            var item = context as CombinedJCETreeModel;
                                            if (item.Podetail != null)
                                            {
                                                @($"{item.Podetail.Pocostcode}")
                                                if (item.Podetail.MissingJobTask == true)
                                                {
                                                    <span title="Job Task Missing" class="tooltip-target">
                                                        &nbsp;
                                                        <TelerikFontIcon Icon="@FontIcon.WarningCircle"></TelerikFontIcon>
                                                    </span>
                                                }
                                            }
                                            if (item.Poheader != null)
                                            {
                                                if (item.Children != null && item.Children.Any(x => x.Podetail != null && x.Podetail.MissingJobTask == true))
                                                {
                                                    <span title="Job Task Missing" class="tooltip-target">
                                                        &nbsp;
                                                        <TelerikFontIcon Icon="@FontIcon.WarningCircle"></TelerikFontIcon>
                                                    </span>
                                                }
                                            }
                                            if (item.Poheader == null && item.Podetail == null)
                                            {

                                                if (item.Children != null && item.Children.SelectMany(x=> x.Children).Any(x => x.Podetail != null && x.Podetail.MissingJobTask == true))
                                                {
                                                    <span title="Job Task Missing" class="tooltip-target">
                                                        &nbsp;
                                                        <TelerikFontIcon Icon="@FontIcon.WarningCircle"></TelerikFontIcon>
                                                    </span>
                                                }
                                            }
                                        }
                                    </Template>
                                </TreeListColumn>
                                <TreeListColumn Field="Podetail.Pounit" Title="Unit Desc">
                                    <Template>
                                        @{
                                            var item = context as CombinedJCETreeModel;
                                            if (item.Podetail != null)
                                            {
                                                @($"{item.Podetail.Pounit}")
                                            }

                                        }
                                    </Template>
                                </TreeListColumn>
                                <TreeListColumn Field="Podetail.Pounitqty" Title="Units">
                                    <Template>
                                        @{
                                            var item = context as CombinedJCETreeModel;
                                            if (item.Podetail != null)
                                            {
                                                @($"{item.Podetail.Pounitqty}")
                                            }

                                        }
                                    </Template>
                                </TreeListColumn>
                                <TreeListColumn Field="Podetail.Pounitcost" DisplayFormat="{0:c2}" Title="Unit Cost">
                                </TreeListColumn>
                                <TreeListColumn Field="Amount" DisplayFormat="{0:c2}" Title="Amount"></TreeListColumn>
                                @* <TreeListColumn Field="Podetail.JccAmountIncTax" DisplayFormat="{0:c2}" Title="PO Amount">
                                    <Template>
                                        @{
                                            var item = context as CombinedJCETreeModel;
                                            if (item.Pojccdetail != null)
                                            {
                                                @(string.Format("{0:C}", item.Pojccdetail.JccAmountIncTax))
                                            }
                                            else if (item.Poheader != null)
                                            {
                                                @(string.Format("{0:C}", item.Poheader.Pototal))
                                            }
                                        }                                        
                                    </Template>
                                </TreeListColumn> *@                          
                                <TreeListColumn Field="Poheader.Podateissued" DisplayFormat="{0:MM/dd/yyyy}" Width="80px" Title="Issue Date"></TreeListColumn>
                                <TreeListColumn Field="Poheader.Podateapproved" DisplayFormat="{0:MM/dd/yyyy}" Width="80px" Title="Approved Date"></TreeListColumn>
                            </TreeListColumns>
                            <TreeListToolBarTemplate>
                                <TreeListSearchBox />
                                 <TelerikToggleButton @bind-Selected="@TogglePOSupplierSort" OnClick="@TogglePOFilterBySupplier" Class="k-button-add">Sort By Supplier/Job: <strong>@ShowPOSupplierJobSort</strong></TelerikToggleButton>
                                @if (AllowEdit)
                                {                                                                               
                                    <TreeListCommandButton Title="Send To BC" Class="tooltip-target k-button-success" OnClick="@SendApprovedPOItemsToBC" Icon="@FontIcon.Envelop"></TreeListCommandButton>
                                }
                            </TreeListToolBarTemplate>
                        </TelerikTreeList>
                    }
                </TabStripTab>
                    <TabStripTab Title="BC Integration Logs">
                        @if (BCLogData == null || IsLoading)
                        {
                            <p><em>Loading...</em></p>
                            <TelerikLoader Size="@ThemeConstants.Loader.Size.Small" Type="@LoaderType.ConvergingSpinner" />
                        }
                        else
                        {
                            <TelerikGrid Data="@BCLogData"
                            ScrollMode="@GridScrollMode.Virtual"
                            FilterMode="@GridFilterMode.FilterMenu"
                            OnRowDoubleClick="@OnRowDoubleClickHandler"
                            Resizable="true"
                            Reorderable="true"
                            Height="80vh"
                            PageSize="40"
                            RowHeight="40"
                            Sortable="true"
                            ConfirmDelete="true"
                            Width="100%">
                                <GridColumns>
                                    <GridColumn Field="Method" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Method"></GridColumn>
                                    <GridColumn Field="Type" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Type"></GridColumn>
                                    <GridColumn Field="RequestUrl" Title="Request URL"></GridColumn>
                                    <GridColumn Field="RequestBody" Title="Request Body"></GridColumn>
                                    <GridColumn Field="ResponseCode"  Title="Response Code" FilterMenuType="@FilterMenuType.CheckBoxList">
                                        <Template>
                                            @{
                                                var item = context as ErpBcApiLogDto;
                                                if (item?.ResponseCode != null && item.ResponseCode != "SUCCESS")
                                                {
                                                    <div style="color:red">@item.ResponseCode</div>
                                                }
                                                else
                                                {
                                                    <div>@item.ResponseCode</div>
                                                }
                                            }
                                        </Template>
                                    </GridColumn>
                                    <GridColumn Field="ResponseBody"  Title="Response Body"></GridColumn>
                                    <GridColumn Field="CreatedDateTime" Title="Date"></GridColumn>
                                </GridColumns>
                                <GridToolBarTemplate>
                                    <GridSearchBox />
                                </GridToolBarTemplate>
                            </TelerikGrid>
                        }
                    </TabStripTab>                
            </TelerikTabStrip>
        </div>
    </div>
</div>
<TelerikWindow @bind-Visible="@WindowIsVisible" Modal="true">
    <WindowTitle>
        <strong>Details for Selected API Request</strong>
    </WindowTitle>
    <WindowContent>
        <div>
            <p><b>Request URL:</b></p>
            <p>@CurrentAPILog?.RequestUrl</p>
        </div>
        <div>
            <p><b>Request Body:</b></p>
            <p>@CurrentAPILog?.RequestBody</p>
        </div>
        <div>
            <p><b>Response:</b></p>
            <p>@CurrentAPILog?.ResponseBody</p>
        </div>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Minimize"></WindowAction>
        <WindowAction Name="Maximize"></WindowAction>
        <WindowAction Name="Close"></WindowAction>
    </WindowActions>
</TelerikWindow>


@code {
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    private ObservableCollection<CombinedJCETreeModel>? BudgetData { get; set; }
    private ObservableCollection<CombinedJCETreeModel>? AllBudgetData { get; set; }
    private IEnumerable<CombinedJCETreeModel>? SelectedBudgetItems { get; set; } = Enumerable.Empty<CombinedJCETreeModel>();
    private ObservableCollection<CombinedJCETreeModel>? POData { get; set; }
    private ObservableCollection<CombinedJCETreeModel>? AllPOData { get; set; }
    private IEnumerable<CombinedJCETreeModel>? SelectedPOItems { get; set; } = Enumerable.Empty<CombinedJCETreeModel>();
    private ObservableCollection<CombinedJCETreeModel>? CancelledPOData { get; set; }
    private ObservableCollection<CombinedJCETreeModel>? AllCancelledPOData { get; set; }
    private IEnumerable<CombinedJCETreeModel>? SelectedCancelledPOItems { get; set; } = Enumerable.Empty<CombinedJCETreeModel>();
    private ObservableCollection<CombinedJCETreeModel>? RepushPOData { get; set; }
    private ObservableCollection<CombinedJCETreeModel>? AllRepushPOData { get; set; }
    private IEnumerable<CombinedJCETreeModel>? SelectedRepushPOItems { get; set; } = Enumerable.Empty<CombinedJCETreeModel>();
    private List<ErpBcApiLogDto>? BCLogData { get; set; }
    private ObservableCollection<CombinedJCETreeModel>? InvoiceData { get; set; }
    private ObservableCollection<CombinedJCETreeModel>? AllInvoiceData { get; set; }
    private IEnumerable<CombinedJCETreeModel>? SelectedInvoiceItems { get; set; } = Enumerable.Empty<CombinedJCETreeModel>();
    private IEnumerable<JobCustomerDto>? SelectedCustomers { get; set; } = Enumerable.Empty<JobCustomerDto>();
    private TelerikTreeList<CombinedJCETreeModel>? BudgetTreeList { get; set; }
    private TelerikTreeList<CombinedJCETreeModel>? POTreeList { get; set; }
    private TelerikTreeList<CombinedJCETreeModel>? InvoiceTreeList { get; set; }
    private List<JobCustomerDto>? CustomerData { get; set; }
    private bool ShowWait { get; set; } = false;
    private TelerikGrid<JobCustomerDto>? CustomerGrid { get; set; }
    private List<JccostcodeDto>? AllCostCodes { get; set; } = new List<JccostcodeDto>();
    // private bool ShowAll { get; set; } = false;
    // private bool ToggleShowAll { get; set; } = true;
    // private string? ShowHideNeedApprove { get; set; } = "Not Exported";
    // private bool ShowPOAll { get; set; } = false;
    // private bool TogglePOShowAll { get; set; } = true;
    // private string? ShowHidePONeedApprove { get; set; } = "Not Exported";
    // private bool ShowInvoiceAll { get; set; } = false;
    // private bool ToggleInvoiceShowAll { get; set; } = true;
    // private string? ShowHideInvoiceNeedApprove { get; set; } = "Not Exported";
    // private bool ShowCustomerAll { get; set; } = false;
    // private bool ToggleCustomerShowAll { get; set; } = true;
    // private string? ShowHideCustomerNeedApprove { get; set; } = "Not Exported";
    // private bool ShowInvoiceSupplierSort { get; set; }
    // private bool ToggleInvoiceSupplierSort { get; set; } = true;
    // private string? ShowInvoiceSupplierJobSort { get; set; } = "Job";
    private bool ShowPOSupplierSort { get; set; }
    private bool TogglePOSupplierSort { get; set; } = true;
    private string? ShowPOSupplierJobSort { get; set; } = "Job";
    private bool AllowEdit { get; set; } = true;
    public bool IsLoading { get; set; } = false;
    private bool WindowIsVisible { get; set; }
    private ErpBcApiLogDto? CurrentAPILog { get; set; }
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }
    protected override async Task OnInitializedAsync()
    {
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
      //  var response = await PoService.BackfillBCIDInPOHeadersync();//This won't be needed
        await LoadData();
    }
    private async Task LoadData()
    {

        var BudgetTask = BudgetService.GetSendToAccountingAsync();
        // var PoTask = PoService.GetSendToAccountingAsync();
        // var InvoiceTask = PoService.GetInvoiceSendToAccountingAsync();
        var costCodesTask = PoService.GetJcCostCodesAsync();
        //var customersTask = SelectedOptionsService.GetRecentJobCustomersAsync();
        //await Task.WhenAll(new Task[] {BudgetTask, PoTask, InvoiceTask, costCodesTask, customersTask });
        await Task.WhenAll(new Task[] { BudgetTask, costCodesTask });
        // Console.WriteLine($"loaded  data: {sw.Elapsed}");
        // CustomerData = customersTask.Result.Value;
        AllCostCodes = costCodesTask.Result.Value;
        AllBudgetData = new ObservableCollection<CombinedJCETreeModel>(BudgetTask.Result.Value);
        BudgetData = new ObservableCollection<CombinedJCETreeModel>(AllBudgetData.Where(x => x.BoolExported == false));
        // AllInvoiceData = new ObservableCollection<CombinedJCETreeModel>(InvoiceTask.Result.Value);
        // AllPOData = new ObservableCollection<CombinedJCETreeModel>(PoTask.Result.Value);
        //  POData = new ObservableCollection<CombinedJCETreeModel>(AllPOData.Where(x => x.BoolExported == false));
        // //POData = new ObservableCollection<CombinedJCETreeModel>(AllPOData);
        // InvoiceData = new ObservableCollection<CombinedJCETreeModel>(AllInvoiceData.Where(x => x.BoolExported == false));
        StateHasChanged();

    }
    private void OnRowDoubleClickHandler(GridRowClickEventArgs args)
    {
        CurrentAPILog = args.Item as ErpBcApiLogDto;

        WindowIsVisible = !WindowIsVisible;
    }

    async Task OnStateInitHandler(TreeListStateEventArgs<CombinedJCETreeModel> args)
    {
        var collapsedItemsState = new TreeListState<CombinedJCETreeModel>()
            {
                //collapse all items in the TreeList upon initialization of the state
                ExpandedItems = new List<CombinedJCETreeModel>(),
                FilterDescriptors = new List<IFilterDescriptor>()
                {
                    new CompositeFilterDescriptor()
                    {
                        FilterDescriptors = new FilterDescriptorCollection()
                        {
                            new FilterDescriptor()
                            {
                                Member = nameof(CombinedJCETreeModel.BoolExported),
                                MemberType = typeof(bool),
                                Operator = FilterOperator.IsEqualTo,
                                Value = false
                            }
                        }
                    }
                },
            };

        args.TreeListState = collapsedItemsState;
    }
    async Task OnRepushPoStateInitHandler(TreeListStateEventArgs<CombinedJCETreeModel> args)
    {
        var collapsedItemsState = new TreeListState<CombinedJCETreeModel>()
            {
                //collapse all items in the TreeList upon initialization of the state
                ExpandedItems = new List<CombinedJCETreeModel>(),
                //not filtering on already sent
            };

        args.TreeListState = collapsedItemsState;
    }
    async Task OnCustomerGridStateInitHandler(GridStateEventArgs<JobCustomerDto> args)
    {
        var filterState = new GridState<JobCustomerDto>()
            {
                FilterDescriptors = new List<IFilterDescriptor>()
                {
                    new CompositeFilterDescriptor()
                    {
                        FilterDescriptors = new FilterDescriptorCollection()
                        {
                            new FilterDescriptor()
                            {
                                Member = "Customer.BoolSentToNav",
                                MemberType = typeof(bool),
                                Operator = FilterOperator.IsEqualTo,
                                Value = false
                            }
                        }
                    }
                },
            };
        args.GridState = filterState;
    }

    private async Task SendApprovedPOItemsToBC()
    {
        // if (SelectedPOItems.Any(x => x.Poheader != null && (x.Supplier?.Blocked == true || x.Supplier?.InsuranceExpired == true)))
        // {
        //     await Dialogs.AlertAsync("Blocked suppliers and suppliers with expired insurance will not be exported.");
        // }

        ShowWait = true;
        //var headersToSend = SelectedPOItems.Where(x => x.Poheader != null && (x.Supplier?.Blocked == null || x.Supplier?.Blocked == false) && (x.Supplier?.InsuranceExpired != true));
        //NOTE: 11/14 Per Kevin request, allow pay suppliers with expired insurance if the PO already issued and they did the work
        var headersToSend = SelectedRepushPOItems.Where(x => x.Poapproval != null && x.Poheader != null && x.Poheader.Pototal > 0);
        var poTask = PoService.SendApprovedPoHeadersToBCBatchAsync(headersToSend.Select(x => x.Poapproval).ToList());

        var creditMemosToSend = SelectedRepushPOItems.Where(x => x.Poapproval != null && x.Poheader != null && x.Poheader.Pototal < 0);
        var creditMemoTask =  PoService.SendApprovedCreditMemosToBCAsync(creditMemosToSend.Select(x => x.Poapproval).ToList());
        
        await Task.WhenAll(new Task[] { poTask, creditMemoTask });
       // await Task.WhenAll(new Task[] { poTask});

        var response = poTask.Result;
        var creditMemoResponse = creditMemoTask.Result;

        var getPoDataTask = PoService.GetApprovedPOSendToAccountingAsync();
       
        await Task.WhenAll(new Task[] { getPoDataTask });
        var poData = getPoDataTask.Result.Value;
        AllRepushPOData = new ObservableCollection<CombinedJCETreeModel>(poData);
        RepushPOData =  new ObservableCollection<CombinedJCETreeModel>(AllRepushPOData);

        ShowMessage(response.IsSuccess, response.Message);
        ShowMessage(creditMemoResponse.IsSuccess, creditMemoResponse.Message);
        SelectedRepushPOItems = Enumerable.Empty<CombinedJCETreeModel>();
        ShowWait = false;
    }
    
    private async Task SendBudgetItemToBC(TreeListCommandEventArgs args)
    {
        ShowWait = true;
        var item = args.Item as CombinedJCETreeModel;
        if (item != null && item.Estjcedetail != null)
        {
            var response = await BudgetService.SendBudgetToBCAsync(item.Estjcedetail);
            if (response.IsSuccess)
            {
                var findParent = BudgetData.SelectMany(x => x.Children).Where(x => x.Children.Select(y => y.Id).Contains(item.Id)).SingleOrDefault();
                if (findParent != null)
                {
                    findParent.Children.Remove(item);
                    if (!findParent.Children.Any())
                    {
                        //remove parent if no children
                        var findGrandParent = BudgetData.Where(x => x.Children.Select(y => y.Id).Contains(findParent.Id)).SingleOrDefault();
                        if (findGrandParent != null)
                        {
                            findGrandParent.Children.Remove(findParent);
                            if (!findGrandParent.Children.Any())
                            {
                                BudgetData.Remove(findGrandParent);
                            }
                        }
                    }
                }
                BudgetTreeList.Rebind();
            }           
            ShowMessage(response.IsSuccess, response.Message);
        }
        else if(item.Estoption != null)
        {
            //option level send all children
            var findChildren = item.Children.Select(x => x.Estjcedetail).ToList();
            //var findChildren = BudgetData.Where(x => x.ParentId == item.Id).Select(x => x.Estjcedetail).ToList();
            var response = await BudgetService.SendBudgetsToBCAsync(findChildren);
            if (response.IsSuccess)
            {
                var findParent = BudgetData.Where(x => x.Children.Select(y => y.Id).Contains(item.Id)).SingleOrDefault();
                if (findParent != null)
                {
                    findParent.Children.Remove(item);
                    if (!findParent.Children.Any())
                    {
                        BudgetData.Remove(findParent);
                    }
                }
                BudgetTreeList.Rebind();
            }
            ShowMessage(response.IsSuccess, response.Message);
        }
        else
        {        
            //Job level - send all children
            var findChildren = item.Children.ToList();
            var findGrandChildren = findChildren.SelectMany(x => x.Children.Select(y => y.Estjcedetail)).ToList();
            var response = await BudgetService.SendBudgetsToBCAsync(findGrandChildren);
            if (response.IsSuccess)
            {
                BudgetData.Remove(item);
                BudgetTreeList.Rebind();
            }
            ShowMessage(response.IsSuccess, response.Message);
        }
        ShowWait = false;
    }
   
    private async Task SendBudgetItemsToBC()
    {
        ShowWait = true;
        var sendItems = SelectedBudgetItems.Where(x => x.Estjcedetail != null).Select(x => x.Estjcedetail).ToList();
        var response = await BudgetService.SendBudgetsToBCAsync(sendItems);
        var getData = await BudgetService.GetSendToAccountingAsync();
        AllBudgetData = new ObservableCollection<CombinedJCETreeModel>(getData.Value);
        BudgetData = new ObservableCollection<CombinedJCETreeModel>(AllBudgetData);
        ShowMessage(response.IsSuccess, response.Message);
        // BudgetTreeList.Rebind();
        SelectedBudgetItems = Enumerable.Empty<CombinedJCETreeModel>();
        ShowWait = false;
    }
    
   
   
    async Task TogglePOFilterBySupplier()
    {
        ShowPOSupplierSort = TogglePOSupplierSort;
        ShowPOSupplierJobSort = ShowPOSupplierSort ? "Supplier" : "Job";
        //InvoiceData = ShowAll ? InvoiceData = new ObservableCollection<CombinedJCETreeModel>(AllInvoiceData) : new ObservableCollection<CombinedJCETreeModel>(AllInvoiceData.Where(x => x.BoolExported == false));
        var state = POTreeList.GetState();

        if (ShowPOSupplierSort)
        {
            IsLoading = true;
            var POTask = await PoService.GetPOSendToAccountingSupplierSortAsync();
            AllRepushPOData = new ObservableCollection<CombinedJCETreeModel>(POTask.Value);
            IsLoading = false;
        }
        else
        {
            IsLoading = true;
            var POTask = await PoService.GetApprovedPOSendToAccountingAsync();
            AllRepushPOData = new ObservableCollection<CombinedJCETreeModel>(POTask.Value);
            IsLoading = false;
        }

        await POTreeList.SetStateAsync(state);
    }
   
    async void ShowMessage(bool success, string message)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
    void OnRowRenderHandler(TreeListRowRenderEventArgs args)
    {
        CombinedJCETreeModel item = args.Item as CombinedJCETreeModel;
        if (item != null && (item.Pojccdetail != null || item.Podetail != null ))
        {            
            args.Class = "noApproveCheckbox";
        }
        if (item != null && item.Poheader != null && item.Children.Any(x => x.Podetail != null && x.Podetail.MissingJobTask == true))
        {
            args.Class = "noApproveCheckbox";
        }
    }

    async Task TabChangedHandler(int selectedIndex)
    {
        switch (selectedIndex)
        {
            case 0:
                if (BudgetData == null)
                {
                    IsLoading = true;
                    var BudgetTask = await BudgetService.GetSendToAccountingAsync();
                    AllBudgetData = new ObservableCollection<CombinedJCETreeModel>(BudgetTask.Value);
                    BudgetData = new ObservableCollection<CombinedJCETreeModel>(AllBudgetData.Where(x => x.BoolExported == false));
                    IsLoading = false;
                }
                break;            
            case 1:
                if (RepushPOData == null)
                {
                    IsLoading = true;
                    var PoTask = await PoService.GetApprovedPOSendToAccountingAsync();
                    AllRepushPOData = new ObservableCollection<CombinedJCETreeModel>(PoTask.Value);
                    RepushPOData = new ObservableCollection<CombinedJCETreeModel>(AllRepushPOData.Where(x => x.BoolExported == false));
                    IsLoading = false;
                }
                break;
            
            case 2:
                IsLoading = true;
                var logTask = await BCAPILogService.GetBCLogsAsync();
                BCLogData = logTask.Value;
                IsLoading = false;
                break;
        }
    }   
}
