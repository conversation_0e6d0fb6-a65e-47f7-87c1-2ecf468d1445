﻿@using ERP.Data.Models;
@inject SubdivisionService SubdivisionService
@inject BudgetService BudgetService
@inject PlanService PlanService
@inject PoService PoService
@inject OptionService OptionService
@inject ItemService ItemService
@using ERP.Data.Models.Dto;


<TelerikWindow Modal="true"
@bind-Visible="@IsModalVisible"
Width="600px"
Height="600px"
CloseOnOverlayClick="true">
    <WindowTitle>
        Add Estimate
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@EstimateToAdd" OnValidSubmit="@HandleValidAddSubmit">        
            @*  <div class="mb-3">
                <label> Load change order/variance itames from </label>
                <TelerikRadioGroup Data="@TakeoffTypes" @bind-Value="@SelectedTakeoffType"></TelerikRadioGroup>
            </div> *@
            <div class="mb-3">
                <label class="form-label">Reference Number</label><br />
                <TelerikTextBox @bind-Value="@EstimateToAdd.Estoption.Estheader.ReferenceNumber"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Document</label><br />
                <TelerikTextBox @bind-Value="@EstimateToAdd.Estoption.Estheader.ReferenceDesc"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Description</label><br />
                <TelerikTextBox @bind-Value="@EstimateToAdd.Estoption.Estheader.EstimateDescPe"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Document Type</label><br />
                <TelerikDropDownList Data="@ReferenceTypes" TextField="ReferenceType1" ValueField="ReferenceTypeId" @bind-Value="@EstimateToAdd.Estoption.Estheader.ReferenceType" Width="100%"></TelerikDropDownList>
            </div>
            @* <div>
                <label>Variance Category</label>
                <TelerikDropDownList @bind-Value="@EstimateToAdd.VarianceJcCategory"
                                     Data="@AllVarianceCategories"
                                     DefaultText="Select Variance Category"
                                     Filterable="true"
                                     Width="100%">
                </TelerikDropDownList>
            </div>
            <br/>
            <br />
            <p>
                <label>Variance Category</label>
                <TelerikTextBox Enabled="@false" @bind-Value="@VarianceCategory"
                                Width="100%">
                </TelerikTextBox>
            </p> *@

            <p>
                <label>List Items by:</label>
                <TelerikDropDownList @bind-Value="@SelectedItemDisplayCategory"
                Data="@ItemDisplayCategoriesList"
                TextField="DisplayCategory"
                ValueField="DisplayCategoryId"
                DefaultText="Select Category to list Items"
                Width="100%">
                </TelerikDropDownList>
            </p>

            @if (SelectedItemDisplayCategory == 1)
            {
                <p>
                    <label>Master Plan</label>
                    @if (AllMasterPlans != null && AllMasterPlans.Count != 0)
                    {
                        <TelerikDropDownList @bind-Value="@SelectedMasterPlanId"
                        Data="@AllMasterPlans"
                        TextField="PlanName"
                        ValueField="MasterPlanId"
                        DefaultText="Select Master Plan"
                        Filterable="true"
                        FilterOperator="StringFilterOperator.Contains"
                        OnChange="@CascadePlanOptions"
                        ScrollMode="@DropDownScrollMode.Virtual"
                        ItemHeight="30"
                        PageSize="20"
                        Width="100%" Context="planDropdownContext">
                            <ItemTemplate>@planDropdownContext.PlanNum - @planDropdownContext.PlanName </ItemTemplate>
                        </TelerikDropDownList>
                    }
                    else
                    {
                        <p>No plans are assigned to this subdivision</p>
                    }
                </p>
                @if (SelectedMasterPlanId != 0)
                {
                    <p>
                        <label>Master Plan Options</label>
                        <TelerikMultiSelect @bind-Value="@SelectedAsmHeaderIds"
                        Data="@AllOptionsInMasterPlan"
                        TextField="DisplayDesc"
                        ValueField="AsmHeaderId"
                        Placeholder="Select options"
                        OnChange="@CascadeItems"
                        AutoClose="false"
                        FilterOperator="StringFilterOperator.Contains"
                        Filterable="true"
                        Width="100%" Context="optinDropdownContext">
                            <ItemTemplate>@optinDropdownContext.AssemblyCode - @optinDropdownContext.AssemblyDesc </ItemTemplate>
                        </TelerikMultiSelect>
                    </p>
                }
                @if (SelectedAsmHeaderIds != null && SelectedAsmHeaderIds.Count != 0 && AllItemsOptionSort != null)
                {
                    <p>
                        <label>Item</label>
                        <TelerikMultiSelect @bind-Value="@SelectedAsmDetailIds"
                        @ref="OptionSortSelectItemsRef"
                        TextField="ItemDesc"
                        ValueField="AsmDetailId"
                        Filterable="true"
                        FilterOperator="StringFilterOperator.Contains"
                        Placeholder="Select items"
                        Data="@AllItemsOptionSort"
                                AutoClose="false"
                        Width="100%" Context="itemDropdownContext">
                            @*                             <ItemTemplate>@itemDropdownContext.ItemNumber - @itemDropdownContext.ItemDesc </ItemTemplate> *@
                            <HeaderTemplate>
                                <label style="padding: 4px 8px;">
                                    <TelerikCheckBox TValue="bool"
                                    Value="@IsAllOptionItemsSelected()"
                                    ValueChanged="@( (bool v) => ToggleOptionSortSelectAll(v) )">
                                    </TelerikCheckBox>
                                    &nbsp;Select All
                                </label>
                            </HeaderTemplate>
                            <ItemTemplate>
                                <input type="checkbox"
                                class="k-checkbox k-checkbox-md"
                                checked="@GetSelectedOptionSortChecked((int)itemDropdownContext.AsmDetailId)">
                                @itemDropdownContext.ItemNumber - @itemDropdownContext.ItemDesc
                            </ItemTemplate>
                        </TelerikMultiSelect>
                    </p>
                }
            }
            @if (SelectedItemDisplayCategory == 2)
            {


                @if (AllTrades != null && AllTrades.Count != 0)
                {
                    @*  <TelerikDropDownList @bind-Value="@SelectedTradeId"
                                             Data="@AllTrades"
                                             TextField="TradeName"
                                             ValueField="TradeId"
                                             DefaultText="Select Trade"
                                             Filterable="true"
                                             OnChange="@CascadeActivities"
                                             ScrollMode="@DropDownScrollMode.Virtual"
                                             ItemHeight="30"
                                             PageSize="20"
                                             Width="100%">
                        </TelerikDropDownList> *@
                    @*   <TelerikMultiSelect @bind-Value="@SelectedTradeIds"
                                            Data="@AllTrades"
                                            TextField="TradeName"
                                            ValueField="TradeId"
                                            Placeholder="Select trades"
                                            OnChange="@CascadeActivities"
                                            Filterable="true"
                                            FilterOperator="@StringFilterOperator.Contains"
                                            Width="100%">
                        </TelerikMultiSelect> *@
                }

                <p>
                    <label>Activity</label>                       
                    <TelerikMultiSelect @bind-Value="@SelectedActivityIds"
                    Data="@PurchasingActivityData"
                    TextField="DropdownDescription"
                    ValueField="PactivityId"
                    Placeholder="Select activities"
                    OnChange="@CascadeItems"
                        AutoClose="false"
                    Filterable="true"
                    FilterOperator="StringFilterOperator.Contains"
                    Width="100%">
                    </TelerikMultiSelect>
                </p>

                @if (SelectedActivityIds.Count != 0)
                {
                    <p>
                        <label>Item</label>
                        <TelerikMultiSelect @bind-Value="@SelectedMasterItemIds"
                        @ref="TradeSortSelectItemsRef"
                        TextField="ItemDesc"
                        ValueField="MasterItemId"
                                AutoClose="false"
                        Filterable="true"
                        FilterOperator="StringFilterOperator.Contains"
                        Placeholder="Select items"
                        Data="@AllItemsTradeSort"
                        Width="100%" Context="itemDropdownContext">
                            @*  <ItemTemplate>@itemDropdownContext.ItemNumber - @itemDropdownContext.ItemDesc </ItemTemplate> *@
                            <HeaderTemplate>
                                <label style="padding: 4px 8px;">
                                    <TelerikCheckBox TValue="bool"
                                    Value="@IsAllTradeActivityItemsSelected()"
                                    ValueChanged="@( (bool v) => ToggleTradeActivitySelectAll(v) )">
                                    </TelerikCheckBox>
                                    &nbsp;Select All
                                </label>
                            </HeaderTemplate>
                            <ItemTemplate>
                                <input type="checkbox"
                                class="k-checkbox k-checkbox-md"
                                checked="@GetTradeActivityItemsChecked(itemDropdownContext.MasterItemId)">
                                @itemDropdownContext.ItemNumber - @itemDropdownContext.ItemDesc
                            </ItemTemplate>
                        </TelerikMultiSelect>
                    </p>
                }
            }

            <button type="submit" class="btn btn-primary">Update</button>
            <button type="button" @onclick="CancelAddItem" class="btn btn-secondary">Cancel</button>
            <div style=@submittingStyle>Adding items. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public EstdetailDto EstimateToAdd { get; set; } = new EstdetailDto(){Estoption = new EstoptionDto(){Estheader = new EstheaderDto()}};
    public List<SubdivisionDto>? AllSubdivisions { get; set; }
    private string submittingStyle = "display:none";
    public CombinedPOBudgetTreeModel? SelectedCustBudget { get; set; } = new CombinedPOBudgetTreeModel() { Estoption = new EstoptionDto(), Estheader = new EstheaderDto() };
    public ERP.Web.Components.AddItemToBudget? AddItemToBudgetModal { get; set; }
    public EstoptionDto? ResponseOption { get; set; }
    [Parameter]
    public EventCallback<ResponseModel<CombinedPOBudgetTreeModel>> HandleAddSubmit { get; set; }
    [Parameter]
    public string? JobNumber { get; set; }
    [Parameter]
    public int? SelectedSubdivisionId { get; set; }
    [Parameter]
    public bool? IsPendingCustomEstimate { get; set; } 
    private List<ReferenceTypeDto>? ReferenceTypes { get; set; }
    private List<EstimateSourceDto>? EstimateSources { get; set; }
    public List<string>? AllVarianceCategories { get; set; }
    public string? SelectedVarianceCategory { get; set; }
    private List<string>? TakeoffTypes { get; set; }
    private string? SelectedTakeoffType { get; set; }
    public string VarianceCategory { get; set; } = "CO";
    public List<TradeDto>? AllTrades { get; set; }
    public List<MasterPlanDto>? AllMasterPlans { get; set; }
    public List<MasterOptionHeaderModel>? AllMasterOptions { get; set; }
    public List<AsmHeaderModel>? AllOptionsInMasterPlan { get; set; }
    public int SelectedTradeId;
    //public List<PactivityModel>? AllActivitiesInTrade { get; set; }
    private List<PactivityDto> PurchasingActivityData { get; set; }
    public int SelectedActivityId;
    public List<int>? SelectedActivityIds { get; set; } = new List<int>();
    public List<ModelManagerItemModel>? AllItemsOptionSort { get; set; } = new List<ModelManagerItemModel>();
    public List<ModelManagerItemModel>? AllItemsTradeSort { get; set; } = new List<ModelManagerItemModel>();
    public int SelectedItemId;
    public int SelectedOptionId;
    public int SelectedMasterPlanId;
    public List<int>? SelectedMasterItemIds { get; set; } = new List<int>();
    public List<int>? SelectedAsmDetailIds { get; set; } = new List<int>();
    public List<int>? SelectedAsmHeaderIds { get; set; } = new List<int>();
    public List<int>? SelectedTradeIds { get; set; } = new List<int>();
    public List<ItemDisplayCategory> ItemDisplayCategoriesList { get; set; } = new List<ItemDisplayCategory>()
    {
        new ItemDisplayCategory()
        {
            DisplayCategory = "Plan / Option / Item",
            DisplayCategoryId = 1
        },
        new ItemDisplayCategory()
        {
            DisplayCategory = "Activity / Item",
            DisplayCategoryId = 2
        }
    };
    public int SelectedItemDisplayCategory { get; set; } = 2;
    public CombinedPOBudgetTreeModel? AddedEstimate { get; set; }
    private TelerikMultiSelect<ModelManagerItemModel, int>? TradeSortSelectItemsRef;
    private TelerikMultiSelect<ModelManagerItemModel, int>? OptionSortSelectItemsRef;
    public async Task Show()
    {
        IsModalVisible = true; 
        submittingStyle = "display:none";
        StateHasChanged();
    }
    protected override async Task OnParametersSetAsync()
    {
        EstimateToAdd = new EstdetailDto() 
        { 
            Estoption = new EstoptionDto() 
            { 
                Estheader = new EstheaderDto() 
                { 
                    JobNumber = JobNumber, 
                    ReferenceNumber = IsPendingCustomEstimate == true ? "Custom" : "",
                    EstimateDescPe = IsPendingCustomEstimate == true ? "Pending/Custom Estimate" : "",
                } 
            } 
        };
        if(JobNumber != null)
        {
            if(SelectedSubdivisionId != null)
            {
                var activityTask = PoService.GetPurchasingActivitiesByJob(JobNumber);
                var plansTask = PlanService.GetPhasePlansInSubdivisionAsync((int)SelectedSubdivisionId);
                await Task.WhenAll(plansTask, activityTask);
                AllMasterPlans = plansTask.Result.Value.Select(x => x.MasterPlan).ToList();
                PurchasingActivityData = activityTask.Result.Value;
            }
            else
            {
                var activityTask = PoService.GetPurchasingActivitiesByJob(JobNumber);
                var plansTask = PlanService.GetMasterPlansAsync();
                await Task.WhenAll(plansTask, activityTask );
                AllMasterPlans = plansTask.Result.Value;
                PurchasingActivityData = activityTask.Result.Value;
            }
        }
    }
    protected override async Task OnInitializedAsync()
    {
        var tradeTask = ItemService.GetTradesAsync();
        var varianceCategoriesTask = BudgetService.GetVarianceCategoriesAsync();
        var plansTask = PlanService.GetMasterPlansAsync();
        var refTypesTask = BudgetService.GetReferenceTypesAsync();
        await Task.WhenAll(tradeTask, varianceCategoriesTask, plansTask, refTypesTask);
        AllVarianceCategories = varianceCategoriesTask.Result.Value;
        AllMasterPlans = plansTask.Result.Value;
        AllTrades = tradeTask.Result.Value;
        ReferenceTypes = refTypesTask.Result.Value;
        TakeoffTypes = new List<string>() { "Item Takeoff", "Option Takeoff", "Pending/Custom Estimate" };
    }
    private async void HandleValidAddSubmit()
    {

        submittingStyle = "";

        //if they pick an option, add it, if they pick by trade, add base house as option
        //TODO: check for if they don't pick any items. 
        if (EstimateToAdd.Estoption.Estheader.ReferenceType == null){
            EstimateToAdd.Estoption.Estheader.ReferenceType = 99;//set to "Default" if the user didn't pick
        }

        var addEstResponse = await BudgetService.AddEstheaderAsync(EstimateToAdd.Estoption.Estheader);
        if(addEstResponse.IsSuccess)
        {
            AddedEstimate = new CombinedPOBudgetTreeModel() { Estheader = addEstResponse.Value, Id= Guid.NewGuid() };
            var detailsToAdd = new List<EstdetailDto>();
            var responseItem = new ResponseModel<List<EstdetailDto>>();
            if (SelectedItemDisplayCategory == 2)
            {
                //trade sort, add base house option, then add the details 
                //add option
                var selectedOption = SelectedItemDisplayCategory == 1 ? AllOptionsInMasterPlan?.FirstOrDefault(x => x.AsmHeaderId == SelectedOptionId) : null;
                // EstimateToAdd.Estoption.OptionDesc = SelectedItemDisplayCategory == 2 ? "Base House" : selectedOption.AssemblyDesc;
                // EstimateToAdd.Estoption.OptionNumber = SelectedItemDisplayCategory == 2 ? "Base House" : selectedOption.AssemblyCode;
                // EstimateToAdd.Estoption.EstheaderId = addEstResponse.Value.EstheaderId;

                var optionsToAdd = new List<EstoptionDto>();
                var selectedOptions = AllOptionsInMasterPlan?.Where(x => SelectedAsmHeaderIds.Contains(x.AsmHeaderId)).Select(x => new EstoptionDto()
                    {
                        OptionDesc = x.AssemblyDesc,
                        OptionNumber = x.AssemblyCode,
                        EstheaderId = addEstResponse.Value.EstheaderId,
                        AsmHeaderId = x.AsmHeaderId
                    }).ToList();
                optionsToAdd = SelectedItemDisplayCategory == 2 ? new List<EstoptionDto>() { new EstoptionDto() { OptionDesc = "Base House", OptionNumber = "Base House", EstheaderId = addEstResponse.Value.EstheaderId } } : selectedOptions;
                var optionResponse = await BudgetService.AddEstoptionsAsync(optionsToAdd);

                if (optionResponse.IsSuccess)
                {
                    AddedEstimate.Children = optionResponse.Value.Select(x => new CombinedPOBudgetTreeModel()
                        {
                            Estoption = x
                        }).ToList();
                    // //add item
                    var responseOptions = optionResponse.Value;
                    // var findDetails = AllItemsOptionSort.Where(x => SelectedAsmDetailIds.Contains((int)x.AsmDetailId));

                    detailsToAdd = AllItemsTradeSort.Where(x => SelectedMasterItemIds.Contains((int)x.MasterItemId)).Select(x => new EstdetailDto()
                        {
                            MasterItemsId = x.MasterItemId,
                            Estoption = responseOptions.FirstOrDefault(),//there would only be one option in the case of adding by trade
                            EstoptionId = responseOptions.FirstOrDefault().EstoptionId,
                        }).ToList();
                    responseItem = await BudgetService.AddEstdetailsAsync(detailsToAdd);

                }
                else
                {
                    await HandleAddSubmit.InvokeAsync(new ResponseModel<CombinedPOBudgetTreeModel> { Value = null, IsSuccess = false, Message = optionResponse.Message });
                }

            }
            else
            {
                //option sort
                detailsToAdd = AllItemsOptionSort.Where(x => SelectedAsmDetailIds.Contains((int)x.AsmDetailId)).Select(x => new EstdetailDto()
                    {
                        MasterItemsId = x.MasterItemId,
                        EstitemheaderId = addEstResponse.Value.EstheaderId,
                        //Estoption = responseOptions.Where(y => y.AsmHeaderId == x.AsmHeaderId).FirstOrDefault(),
                        Estoption = new EstoptionDto()
                        {
                            AsmHeaderId = x.AsmHeaderId
                        },
                        VarianceJcCategory = VarianceCategory,
                        JcCategory = VarianceCategory,//TODO: not sure if it's supposed to be jc category or variance jc category, maybe it's different if it's a pending estimate??
                        OrderQty = 1//TODO: allow pick it
                       //EstoptionId = responseOptions.Where(y => y.AsmHeaderId == x.AsmHeaderId).FirstOrDefault().EstoptionId,//to fix: estoption passed back from response shouldn't't have asmheader
                    }).ToList();               
                responseItem = await BudgetService.AddEstOptionsAndEstdetailsAsync(detailsToAdd);
                if (responseItem.IsSuccess)
                {
                    var newOptions = responseItem.Value.Select(x => x.Estoption).DistinctBy(x=>x.EstoptionId).ToList();
                    AddedEstimate.Children = newOptions.Select(x => new CombinedPOBudgetTreeModel()
                        {
                            Estoption = x,
                            ParentId = AddedEstimate.Id
                        }).ToList();
                }

            }

            if (responseItem.IsSuccess)
            {
                var responseEst = CreateTreeItemsToReturn(responseItem.Value);
                await HandleAddSubmit.InvokeAsync(new ResponseModel<CombinedPOBudgetTreeModel> { Value = responseEst, IsSuccess = true, Message = responseItem.Message });
            }
            else
            {
                await HandleAddSubmit.InvokeAsync(new ResponseModel<CombinedPOBudgetTreeModel> { Value = null, IsSuccess = false, Message = responseItem.Message });
            }

        }
        else
        {           
            await HandleAddSubmit.InvokeAsync(new ResponseModel<CombinedPOBudgetTreeModel> { Value = null, IsSuccess = false, Message = addEstResponse.Message });
        } 
        //clear the selections for next use
        submittingStyle = "display:none";
        SelectedActivityIds = new List<int>();
        SelectedAsmHeaderIds = new List<int>();
        SelectedTradeIds = new List<int>();
        SelectedAsmDetailIds = new List<int>();
        SelectedMasterItemIds = new List<int>();
        SelectedMasterPlanId = 0;
        SelectedItemDisplayCategory = 2;
        StateHasChanged();
        IsModalVisible = false;
    }
    async void CancelAddItem()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;
    }
    private CombinedPOBudgetTreeModel CreateTreeItemToReturn(EstdetailDto responseItem)
    {
        var responseEstimate = new CombinedPOBudgetTreeModel()
            {
                Estheader = responseItem.Estoption.Estheader,
                Id = Guid.NewGuid(),
                JobNumber = JobNumber,
                EstimateNumber = responseItem.Estoption.Estheader.EstimateNumber,
                HasChildren = true,
                IsIssued = false,
                IssueEnabled = true,
                IsPendingCustomEstimate = false,
                Children = new List<CombinedPOBudgetTreeModel>()
            {
                new CombinedPOBudgetTreeModel
                {

                    Id = Guid.NewGuid(),
                    JobNumber = JobNumber,
                    ReleaseCode = responseItem.Estactivity.Releasecode,
                    Estoption = responseItem.Estoption,
                    EstimateNumber = responseItem.Estoption.Estheader.EstimateNumber,
                    HasChildren = true,
                    IsIssued = false,
                    IssueEnabled = true,
                    IsPendingCustomEstimate = false,
                    PoIsIssued = false,
                    PoIssueEnabled = true,
                    Children = new List<CombinedPOBudgetTreeModel>()
                    {
                        new CombinedPOBudgetTreeModel()
                        {
                             Id = Guid.NewGuid(),
                             JobNumber = JobNumber,
                             Estactivity = responseItem.Estactivity,
                             ActivityTotal = responseItem.Amount,
                             EstimateNumber = responseItem.Estoption.Estheader.EstimateNumber,
                             HasChildren = true,
                             IsIssued = false,
                             IssueEnabled = true,
                             IsPendingCustomEstimate = false,
                             PoIsIssued = false,
                             PoIssueEnabled = true,
                             Children = new List<CombinedPOBudgetTreeModel>(){
                                new CombinedPOBudgetTreeModel()
                                {
                                    Id = Guid.NewGuid(),
                                    JobNumber = JobNumber,
                                    EstimateNumber = responseItem.Estoption.Estheader.EstimateNumber,
                                    Estdetail = responseItem,
                                    Estactivity = responseItem.Estactivity,
                                    HasChildren = false,
                                    IsIssued = false,
                                    IssueEnabled = true,
                                    IsPendingCustomEstimate = false,
                                    PoIsIssued = false,
                                    PoIssueEnabled = true,
                                }
                            }
                        }
                    }
                }
            } 
        };
        //Set the parent ids
        foreach (var estOption in responseEstimate.Children)
        {
            estOption.ParentId = responseEstimate.Id;
            foreach (var estActivity in estOption.Children)
            {
                estActivity.ParentId = estOption.Id;
                foreach (var estDetail in estActivity.Children)
                {
                    estDetail.ParentId = estActivity.Id;
                }
            }
        }
        return responseEstimate;
    }
    private CombinedPOBudgetTreeModel CreateTreeItemsToReturn(List<EstdetailDto> responseItem)
    {
        //TODO: release sort?
        var responseEstimate = new CombinedPOBudgetTreeModel()
            {
                Estheader = AddedEstimate.Estheader,
                Id = Guid.NewGuid(),
                JobNumber = JobNumber,
                EstimateNumber = AddedEstimate.Estheader.EstimateNumber,
                HasChildren = true,
                IsIssued = false,
                IssueEnabled = true,
                IsPendingCustomEstimate = IsPendingCustomEstimate == true,
                Children = AddedEstimate.Children,
        };
        foreach (var option in responseEstimate.Children)
        {
            option.ParentId = responseEstimate.Id;
            option.Id = Guid.NewGuid();
            option.JobNumber = JobNumber;
            // option.ReleaseCode = ??
            option.EstimateNumber = responseEstimate.EstimateNumber;
            option.HasChildren = true;
            option.IsIssued = false;
            option.IssueEnabled = true;
            option.IsPendingCustomEstimate = false;//TODO:???
            option.PoIsIssued = false;
            option.PoIssueEnabled = true;
            //TODO: needs cost
            var activities = responseItem.Where(x => x.Estoption.EstoptionId == option.Estoption.EstoptionId).Select(x => x.Estactivity).DistinctBy(x => x.EstactivityId).ToList();//might need distinct by id
            option.Children = activities.Select(x => new CombinedPOBudgetTreeModel()
                {
                    Id = Guid.NewGuid(),
                    ParentId = option.Id,
                    JobNumber = JobNumber,
                    Estactivity = x,
                    ReleaseCode = x.Releasecode,
                    EstimateNumber = AddedEstimate.Estheader.EstimateNumber,
                    HasChildren = true,
                    IsIssued = false,
                    IssueEnabled = true,
                    IsPendingCustomEstimate = false,
                    PoIsIssued = false,
                    PoIssueEnabled = true,                    
                }).ToList();
            foreach(var activity in option.Children)
            {
                //add the items
                activity.Children = responseItem.Where(x => x.EstactivityId == activity.Estactivity.EstactivityId && x.Estoption.EstoptionId == option.Estoption.EstoptionId).Select(x => new CombinedPOBudgetTreeModel()                    
                {
                        Id = Guid.NewGuid(),
                        ParentId = activity.Id,
                        JobNumber = JobNumber,
                        EstimateNumber = AddedEstimate.Estheader.EstimateNumber,
                        Estdetail = x,
                        Estactivity = x.Estactivity,
                        HasChildren = false,
                        IsIssued = false,
                        IssueEnabled = true,
                        IsPendingCustomEstimate = false,
                        PoIsIssued = false,
                        PoIssueEnabled = true,                        
                }).ToList();
                activity.ActivityTotal = activity.Children.Sum(x => x.Estdetail.Amount);
            }
            option.TotalCost = option.Children.Sum(x => x.ActivityTotal);
        }        
        return responseEstimate;
    }
    private async void HandleValidAddItemsSubmit(ResponseModel<EstdetailDto> responseItem)
    {
        //TODO: add items actually need to pick by option, in which case it won't be an option returned...
        //TODO: pick by option needs to add the option not base house
        await AddItemToBudgetModal.Hide();
        if (responseItem.IsSuccess)
        {
            var responseEstimate = new CombinedPOBudgetTreeModel()
                {
                    Estheader = ResponseOption.Estheader,
                    Id = Guid.NewGuid(),
                    JobNumber = JobNumber,
                    EstimateNumber = ResponseOption.Estheader.EstimateNumber,
                    HasChildren = true,
                    IsIssued = false,
                    IssueEnabled = true,
                    IsPendingCustomEstimate = false,
                    Children = new List<CombinedPOBudgetTreeModel>()
            {
                new CombinedPOBudgetTreeModel
                {

                    Id = Guid.NewGuid(),
                    JobNumber = JobNumber,
                    Estoption = ResponseOption,
                    EstimateNumber = ResponseOption.Estheader.EstimateNumber,
                    HasChildren = true,
                    IsIssued = false,
                    IssueEnabled = true,
                    IsPendingCustomEstimate = false,
                    PoIsIssued = false,
                    PoIssueEnabled = true,
                    Children = new List<CombinedPOBudgetTreeModel>()
                    {
                        new CombinedPOBudgetTreeModel()
                        {
                             Id = Guid.NewGuid(),
                             JobNumber = JobNumber,
                             Estactivity = responseItem.Value.Estactivity,
                             ActivityTotal = responseItem.Value.Amount,
                             EstimateNumber = ResponseOption.Estheader.EstimateNumber,
                             HasChildren = true,
                             IsIssued = false,
                             IssueEnabled = true,
                             IsPendingCustomEstimate = false,
                             PoIsIssued = false,
                             PoIssueEnabled = true,
                             Children = new List<CombinedPOBudgetTreeModel>(){
                                new CombinedPOBudgetTreeModel()
                                {
                                    Id = Guid.NewGuid(),
                                    JobNumber = JobNumber,
                                    EstimateNumber = ResponseOption.Estheader.EstimateNumber,
                                    Estdetail = responseItem.Value,
                                    Estactivity = responseItem.Value.Estactivity,
                                    HasChildren = false,
                                    IsIssued = false,
                                    IssueEnabled = true,
                                    IsPendingCustomEstimate = false,
                                    PoIsIssued = false,
                                    PoIssueEnabled = true,
                                }
                            }
                        }
                    }
                }
            }
                };
            //Set the parent ids
            foreach (var estOption in responseEstimate.Children)
            {
                estOption.ParentId = responseEstimate.Id;
                foreach (var estActivity in estOption.Children)
                {
                    estActivity.ParentId = estOption.Id;
                    foreach (var estDetail in estActivity.Children)
                    {
                        estDetail.ParentId = estActivity.Id;
                    }
                }
            }
            await HandleAddSubmit.InvokeAsync(new ResponseModel<CombinedPOBudgetTreeModel> { Value = responseEstimate, IsSuccess = true, Message = "Success" });
        }
        else
        {
            await HandleAddSubmit.InvokeAsync(new ResponseModel<CombinedPOBudgetTreeModel> { Value = null, IsSuccess = false, Message = responseItem.Message});
        }

    }
    private async Task LoadAllMasterOptionsAsync()
    {
        AllMasterOptions = (await OptionService.GetAllMasterOptionsAsync()).Value;
    }
    private async Task LoadAllMasterPlansAsync()
    {
        var allMasterPlansResponse = await PlanService.GetMasterPlansAsync();
        AllMasterPlans = allMasterPlansResponse.Value;
    }
    private async Task LoadTradesAsync()
    {
        AllTrades = (await ItemService.GetTradesAsync()).Value;
    }
    private async Task CascadePlanOptions()
    {
        AllOptionsInMasterPlan = (await ItemService.GetAssembliesInPlanIncludeInactiveAsync(SelectedMasterPlanId)).Value;
        SelectedOptionId = 0;
    }
    // private async Task CascadeActivities(object newVal)
    // {
    //    // AllActivitiesInTrade = await ItemService.GetActivityByTradeAsync(SelectedTradeId);
    //     var activityList = new List<PactivityModel>();
    //     foreach (var id in SelectedTradeIds)//todo : parallel foreach
    //     {
    //         var getItems = await ItemService.GetActivityByTradeAsync(id);
    //         activityList.AddRange(getItems.Value);
    //     }
    //     AllActivitiesInTrade = activityList;
    // }
    private async Task CascadeItems(object newVal)
    {
        //TODO: if a item removed at the trade level was already selcted, need to remove the cascaded items
        switch (SelectedItemDisplayCategory)
        {
            case 1:
                //AllItems = await ItemService.GetItemsInAssemblyAsync(SelectedOptionId);//OptionId here is asmheader id, not master optionid
                //AllItems = new List<ModelManagerItemModel>();
                var itemList = new List<ModelManagerItemModel>();
                // foreach(var id in SelectedAsmHeaderIds)//todo : parallel foreach
                // {
                //     var getItems = await ItemService.GetItemsInAssemblyAsync(id);
                //     itemList.AddRange(getItems.Value);
                // }
                await Parallel.ForEachAsync(SelectedAsmHeaderIds, async (id, token) =>
                {
                   var getItems = await ItemService.GetItemsInAssemblyAsync(id);
                   itemList.AddRange(getItems.Value);
                });
                AllItemsOptionSort = itemList;
                break;
            case 2:              
                var itemList2 = new List<ModelManagerItemModel>();
                // foreach (var id in SelectedActivityIds)//todo : parallel foreach
                // {
                //     var getItems = await ItemService.GetItemsInActivityAsync(id);
                //     itemList2.AddRange(getItems.Value);
                // }
                await Parallel.ForEachAsync(SelectedActivityIds, async (id, token) =>
               {
                   var getItems = await ItemService.GetItemsInActivityAsync(id);
                   itemList2.AddRange(getItems.Value);
               });
                AllItemsTradeSort = itemList2;
                break;
            default:
                break;
        }
    }
    bool GetSelectedOptionSortChecked(int id)
    {
        return SelectedAsmDetailIds.Contains(id);
    }
    bool IsAllOptionItemsSelected()
    {
        return SelectedAsmDetailIds.Count == AllItemsOptionSort.Count;
    }
    void ToggleOptionSortSelectAll(bool selectAll)
    {
        SelectedAsmDetailIds.Clear();

        if (selectAll)
        {
            SelectedAsmDetailIds.AddRange(AllItemsOptionSort.Select(x => (int)x.AsmDetailId));
        }

        TradeSortSelectItemsRef?.Rebind();
        OptionSortSelectItemsRef?.Rebind();
    }
    bool GetTradeActivityItemsChecked(int id)
    {
        return SelectedMasterItemIds.Contains(id);
    }
    bool IsAllTradeActivityItemsSelected()
    {
        return SelectedMasterItemIds.Count == AllItemsTradeSort.Count;
    }
    void ToggleTradeActivitySelectAll(bool selectAll)
    {
        SelectedMasterItemIds.Clear();

        if (selectAll)
        {
            SelectedMasterItemIds.AddRange(AllItemsTradeSort.Select(x => x.MasterItemId));
        }

        TradeSortSelectItemsRef?.Rebind();
        OptionSortSelectItemsRef?.Rebind();
    }
    public class ItemDisplayCategory
    {
        public string DisplayCategory { get; set; }
        public int DisplayCategoryId { get; set; }
    }
}
