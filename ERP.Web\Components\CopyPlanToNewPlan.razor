﻿
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject PlanService PlanService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <span style="font-weight:bold">Copy a Plan to Create a New Plan</span>
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@PlanToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <div class="mb-3">
                <label class="form-label">New Plan Number</label>
                <TelerikTextBox @bind-Value="PlanToAdd.PlanNum" Width="200"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">New Plan Name</label>
                <TelerikTextBox @bind-Value="PlanToAdd.PlanName" Width="200"></TelerikTextBox>
            </div>            
            <div class="mb-3">
                <label class="form-label">Select Plan to copy. This will include all the options/items</label>
                <TelerikDropDownList Data="@AllMasterPlans"
                                     @bind-Value="@PlanToAdd.MasterPlanId"
                                     TextField="PlanName"
                                     ValueField="MasterPlanId"
                                     Filterable="true"
                                     FilterOperator="@StringFilterOperator.Contains"
                                     DefaultText="Select Plan to Copy"
                                     Width="100%">
                    <DropDownListSettings>
                        <DropDownListPopupSettings Height="200px"></DropDownListPopupSettings>
                    </DropDownListSettings>
                </TelerikDropDownList>
            </div>
            <div class="mb-3">
                <label class="form-label">Notes</label>
                <TelerikTextArea @bind-Value="PlanToAdd.ElevationNotes"></TelerikTextArea>
            </div>
            <button type="submit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                    </svg>
                </span> Update
            </button>
            <button type="button" @onclick="CancelAddPlan" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-base">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel" aria-hidden="true">
                    <!--!-->
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M256 32c-50.3 0-96.8 16.6-134.1 44.6-17.2 12.8-32.4 28.1-45.3 45.3C48.6 159.2 32 205.7 32 256c0 123.7 100.3 224 224 224 50.3 0 96.8-16.6 134.1-44.6 17.2-12.8 32.4-28.1 45.3-45.3 28-37.4 44.6-83.8 44.6-134.1 0-123.7-100.3-224-224-224zm0 384c-88.2 0-160-71.8-160-160 0-32.6 9.8-62.9 26.6-88.2l221.6 221.6C318.9 406.2 288.6 416 256 416zm133.4-71.8L167.8 122.6C193.1 105.8 223.4 96 256 96c88.2 0 160 71.8 160 160 0 32.6-9.8 62.9-26.6 88.2z"></path><!--!-->
                    </svg><!--!-->
                    <!--!-->
                </span> Cancel
            </button>
            <div style="@submittingStyle">Submitting. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public MasterPlanDto PlanToAdd { get; set; } = new MasterPlanDto();
    public List<MasterPlanDto>? AllMasterPlans;
    private string? submittingStyle = "display:none";
    [Parameter]
    public EventCallback<ResponseModel<MasterPlanDto>> HandleAddSubmit { get; set; }

    public async Task Show()
    {
        IsModalVisible = true;
        var masterPlanDtos = await PlanService.GetMasterPlansAsync();
        AllMasterPlans = masterPlanDtos.Value.Select(x => new MasterPlanDto()
            {
                PlanName = x.PlanNum + " - " + x.PlanName,
                MasterPlanId = x.MasterPlanId,
                PlanTypeId = x.PlanTypeId
            }).ToList();
        StateHasChanged();
    }
    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        var addCopyPlan = PlanToAdd;
        var selectedPlanToCopy = AllMasterPlans.SingleOrDefault(x => x.MasterPlanId == PlanToAdd.MasterPlanId);
        addCopyPlan.PlanTypeId = selectedPlanToCopy.PlanTypeId;
        var responseItem = await PlanService.CopyMasterPlanAsync(addCopyPlan);
        submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(responseItem);
    }
    private void CancelAddPlan()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;
    }
}
