﻿@page "/setuptradesuppliers"
@using ERP.Data.Models.Dto
@using ERP.Web.Components
@using ERP.Data.Models
@attribute [Authorize(Roles = "Admin, Purchasing")]
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject TradeService TradeService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject ScheduleService ScheduleService

<style>
    /* Gutter */
    .row > * {
        padding-right: calc(var(--bs-gutter-x) * .10);
        padding-left: calc(var(--bs-gutter-x) * .10);
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>

<PageTitle>Trades &amp; Suppliers | Setup</PageTitle>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Assign Suppliers to Trade &amp; Subdivision</h7>
                </div>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item"><a href="/suppliers">Suppliers</a></li>
            <li class="breadcrumb-item active">Assign Trade Suppliers</li>
        </ol>

        <div class="col-lg-3">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Trade</h7>
                </div>
            </div>
            @if (TradeData == null)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingTrade />
            }

            else
            {
                <TelerikGrid Data=@TradeData
                ScrollMode="@GridScrollMode.Virtual"
                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                Height="1000px" RowHeight="60" PageSize="20"
                Sortable="true"
                Resizable="true"
                Groupable="false"
                OnUpdate="@UpdateTradeHandler"
                OnEdit="@EditTradeHandler"
                OnDelete="@DeleteTradeHandler"
                OnCreate="@CreateTradeHandler"
                OnCancel="@CancelTradeHandler"
                OnRowClick="@OnTradeRowClickHandler"
                SelectionMode="GridSelectionMode.Single"
                EditMode="@GridEditMode.Popup"
                ConfirmDelete="true"
                @ref="@TradeGridRef">
                    <GridToolBarTemplate>
                        @if(AllowEdit)
                        {
                            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Trade</GridCommandButton>
                        }
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>                    
                    <GridColumns>
                        <GridColumn Field="TradeName" Title="Trade" Editable="true" Groupable="false" />
                        <GridColumn Field="TradeDesc" Title="Trade Description" Editable="true" Groupable="false" Width="0" />
                        <GridColumn Field="TradeId" Visible="false" Editable="false" Groupable="false" />
                        <GridCommandColumn>
                            @if(AllowEdit)
                            {
                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                            }
                        </GridCommandColumn>
                    </GridColumns>
                    <GridSettings>
                        <GridPopupEditSettings Width="400px"
                        Height="350px"
                        Title="Trade">
                        </GridPopupEditSettings>
                    </GridSettings>
                </TelerikGrid>

            }
        </div>
        <div class="col-lg-3">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Subdivisions</h7>
                </div>
            </div>
            @if (SubdivisionData == null)
            {
                <div style=@loadingSubdivisionStyle>Loading...</div>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Large" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingSubivision />
            }
            else
            {
                <TelerikGrid Data=@SubdivisionData                        
                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                Height="1000px" RowHeight="60" PageSize="20"
                Sortable="true"
                Resizable="true"
                Groupable="false"
                OnCreate="@CreateTradeHandler"
                OnRowClick="@OnSubdivisionRowClickHandler"
                SelectionMode="GridSelectionMode.Single"
                ConfirmDelete="true"
                @ref="@SubdivisionGridRef">
                    <GridColumns>
                        <GridColumn Field="SubdivisionName" Title="Subdivision Name" Editable="true" Groupable="false" />
                    </GridColumns>
                    <GridToolBarTemplate>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <GridSettings>
                        <GridPopupEditSettings Width="400px"
                        Height="400px"
                        Title="Activity">
                        </GridPopupEditSettings>
                    </GridSettings>
                </TelerikGrid>

            }
        </div>
        <div class="col-lg-6">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Suppliers for Subdivision: @SelectedSubdivision?.SubdivisionName. Trade: @SelectedTrade?.TradeName</h7>
                </div>
            </div>
            @if (SupplierData == null)
            {
                <p><em>Select a subdivsion and trade to see suppliers</em></p>
                <div style=@loadingSupplierStyle>Loading...</div>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Large" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingSupplier />
            }
            else
            {
                <TelerikGrid Data=@SupplierData                      
                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                Height="1000px" RowHeight="60" PageSize="20"
                Sortable="true"
                Resizable="true"
                Groupable="false"
                SelectionMode="GridSelectionMode.Single"
                EditMode="@GridEditMode.Inline"
                OnUpdate="@UpdateSupplierHandler"
                OnEdit="@EditSupplierHandler"
                OnDelete="@DeleteSupplierHandler"
                ConfirmDelete="true"
                @ref="@SupplierGridRef">
                    <GridColumns>
                        <GridColumn Field="SubNumber" Visible="false" Editable="false" Groupable="false" />
                        <GridColumn Field="TradeId" Visible="false" Editable="false" Groupable="false" />
                        <GridColumn Field="SubdivisionId" Visible="false" Editable="false" Groupable="false" />
                        <GridColumn Field="SubName" Title="Supplier Name" Editable="true" Groupable="false" />
                        <GridColumn Field="IsDefault" Title="IsDefault" Editable="true" EditorType="GridEditorType.CheckBox" Groupable="false" />
                        @* <GridCommandColumn Width="120px">
                            @{
                                var supplier = context as TradeSupplierModel;
                                <a href=@($"/supplierdetails/{supplier.SubNumber}") class="btn btn-outline-primary">View Details</a>
                            }
                        </GridCommandColumn>*@
                        <GridCommandColumn Width="120px">
                            <GridCommandButton Command="ViewDetails" OnClick="@ShowDetails" Class="k-button-success">Details</GridCommandButton>
                        </GridCommandColumn>
                        <GridCommandColumn>
                            @if(AllowEdit)
                            {
                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                            }
                        </GridCommandColumn>
                    </GridColumns>                   
                    <GridToolBarTemplate>
                        @if(AllowEdit)
                        {
                            <GridCommandButton Command="MyToolbarCommand" Icon="@FontIcon.Plus" OnClick="@AddSupplierFromToolbar" Class="k-button-add">Add Supplier</GridCommandButton>
                            <GridCommandButton Command="ModifySupplierForScheduleActivities" OnClick="@ModifySupplierForScheduleActivities" Class="k-button-info">Open Schedule Activities for Suppliers</GridCommandButton>
                        }

                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <GridSettings>
                        <GridPopupEditSettings Width="600px"
                        Height="600px"
                        Title="Item">
                        </GridPopupEditSettings>                       
                    </GridSettings>
                </TelerikGrid>                
            }
        </div>
    </div>
</div>

<AddSupplierToTrade @ref="AddSupplierModal" Trade=@SelectedTrade Subdivision=@SelectedSubdivision HandleAddSubmit="HandleValidAddSupplierSubmit"></AddSupplierToTrade>
<SupplierDetailsComponent @ref="SupplierDetailsModal" SubNumber=@SelectedSupplierNum></SupplierDetailsComponent>
<ModifySupplierForScheduleActivitiesComponent @ref="ModifySupplierForScheduleActivitiesModal" DefaultSupplier="DefaultSupplier" SelectedSubdivisionId="SelectedSubdivisionId" SelectedTradeId="SelectedTradeId" HandleUpdateSubmit="HandleValidUpdateOpenScheduleActivities"></ModifySupplierForScheduleActivitiesComponent>

@code {
    private TelerikGrid<TradeSupplierModel> TradeGridRef { get; set; }
    private TelerikGrid<SubdivisionDto> SubdivisionGridRef { get; set; }
    private TelerikGrid<TradeSupplierModel> SupplierGridRef { get; set; }
    public List<TradeSupplierModel>? TradeData { get; set; }
    public List<SubdivisionDto>? SubdivisionData { get; set; }
    public List<TradeSupplierModel>? SupplierData { get; set; }
    protected AddSupplierToTrade? AddSupplierModal { get; set; }
    protected ModifySupplierForScheduleActivitiesComponent? ModifySupplierForScheduleActivitiesModal { get; set; }
    public TradeSupplierModel SelectedTrade { get; set; }
    public SubdivisionDto? SelectedSubdivision { get; set; }
    public TradeSupplierModel SelectedSupplier { get; set; }
    protected SupplierDetailsComponent? SupplierDetailsModal { get; set; }
    public TradeSupplierModel DefaultSupplier { get; set; }
    public int SelectedSupplierNum { get; set; } = 0;
    public int SelectedTradeId { get; set; } = 0;
    public int SelectedSubdivisionId { get; set; } = 0;
    private string loadingSubdivisionStyle = "display:none";
    private string loadingSupplierStyle = "display:none";
    public bool IsLoadingTrade { get; set; } = false;
    public bool IsLoadingSubivision { get; set; } = false;
    public bool IsLoadingSupplier { get; set; } = false;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }
    private bool AllowEdit { get; set; } = true;

    protected override async Task OnInitializedAsync()
    {
        IsLoadingTrade = true;
        TradeData = (await TradeService.GetTradesAsync()).Value;
        IsLoadingTrade = false;
        loadingSubdivisionStyle = "";
        IsLoadingSubivision = true;
        var response = await SubdivisionService.GetSubdivisionsAsync();
        SubdivisionData = response.Value.Select(x => new SubdivisionDto()
        {
            MarketingName = x.MarketingName ?? x.SubdivisionName,//Some subdivisions don't have marketing name
            SubdivisionId = x.SubdivisionId,
            SubdivisionNum = x.SubdivisionNum,
            SubdivisionName = x.SubdivisionName
        }).ToList();
        loadingSubdivisionStyle = "display:none";
        IsLoadingSubivision = false;
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }


    protected async Task OnTradeRowClickHandler(GridRowClickEventArgs args)
    {
        var trade = args.Item as TradeSupplierModel;
        SelectedTrade = trade;
        SelectedTradeId = (int)trade.TradeId;
        SupplierData = null;
        loadingSupplierStyle = "";
        IsLoadingSupplier = true;
        if (SelectedSubdivision != null && SelectedTrade != null && SelectedTrade.TradeId != null)
        {
            SupplierData = (await TradeService.GetSuppliersForSubdivisionAndTradeAsync(SelectedSubdivision.SubdivisionId, (int)SelectedTrade.TradeId)).Value;
            DefaultSupplier = SupplierData.SingleOrDefault(x => x.IsDefault == true);
        }
        loadingSupplierStyle = "display:none";
        IsLoadingSupplier = false;
    }

    protected async Task OnSubdivisionRowClickHandler(GridRowClickEventArgs args)
    {
        SupplierData = null;
        loadingSupplierStyle = "";
        IsLoadingSupplier = true;
        SelectedSubdivision = args.Item as SubdivisionDto;
        SelectedSubdivisionId = SelectedSubdivision.SubdivisionId;
        if (SelectedSubdivision != null && SelectedTrade != null && SelectedTrade.TradeId != null)
        {
            SupplierData = (await TradeService.GetSuppliersForSubdivisionAndTradeAsync(SelectedSubdivision.SubdivisionId, (int)SelectedTrade.TradeId)).Value;
            DefaultSupplier = SupplierData.SingleOrDefault(x => x.IsDefault == true);
        }

        loadingSupplierStyle = "display:none";
        IsLoadingSupplier = false;
    }

    private void AddSupplierFromToolbar(GridCommandEventArgs args)
    {
        AddSupplierModal.Show();
        StateHasChanged();
    }

    private async void ModifySupplierForScheduleActivities(GridCommandEventArgs args)
    {
        ModifySupplierForScheduleActivitiesModal.Show();
        StateHasChanged();
    }

    private async void HandleValidUpdateOpenScheduleActivities(ResponseModel<List<ScheduleSactivityDto>> responseItem)
    {
        ModifySupplierForScheduleActivitiesModal.Hide();
        
        ShowSuccessOrErrorNotification(responseItem.Message, responseItem.IsSuccess);
        StateHasChanged();
    }

    private async void HandleValidAddSupplierSubmit(ResponseModel<TradeSupplierModel> responseItem)
    {
        if (SelectedSubdivision != null && SelectedTrade != null && SelectedTrade.TradeId != null)
        {
            SupplierData = (await TradeService.GetSuppliersForSubdivisionAndTradeAsync(SelectedSubdivision.SubdivisionId, (int)SelectedTrade.TradeId)).Value;
            DefaultSupplier = SupplierData.SingleOrDefault(x => x.IsDefault == true);
        }
        AddSupplierModal.Hide();
        ShowSuccessOrErrorNotification(responseItem.Message, responseItem.IsSuccess);
        StateHasChanged();
    }

    void EditSupplierHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel item = (TradeSupplierModel)args.Item;
    }

    async Task UpdateSupplierHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel item = (TradeSupplierModel)args.Item;
        var updateResponse = await TradeService.UpdateTradeSupplierAsync(item);
        if (SelectedSubdivision != null && SelectedTrade != null && SelectedTrade.TradeId != null)
        {
            SupplierData = (await TradeService.GetSuppliersForSubdivisionAndTradeAsync(SelectedSubdivision.SubdivisionId, (int)SelectedTrade.TradeId)).Value;
            DefaultSupplier = SupplierData.SingleOrDefault(x => x.IsDefault == true);
        }
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
    }

    async Task DeleteSupplierHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel supplier = (TradeSupplierModel)args.Item;
        var deleteSupplier = new AssignTradeSupplierModel()
            {
                SubdivisionId = supplier.SubdivisionId,
                SubNumber = supplier.SubNumber,
                TradeId = (int)supplier.TradeId
            };
        var deleteResponse = await TradeService.DeleteSupplierFromTradeAsync(deleteSupplier);
        if (SelectedSubdivision != null && SelectedTrade != null && SelectedTrade.TradeId != null)
        {
            SupplierData = (await TradeService.GetSuppliersForSubdivisionAndTradeAsync(SelectedSubdivision.SubdivisionId, (int)SelectedTrade.TradeId)).Value;
            DefaultSupplier = SupplierData.SingleOrDefault(x => x.IsDefault == true);
        }
        ShowSuccessOrErrorNotification(deleteResponse.Message, deleteResponse.IsSuccess);
    }
    async Task CancelTradeHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel item = (TradeSupplierModel)args.Item;
    }

    void EditTradeHandler(GridCommandEventArgs args)
    {  
        TradeSupplierModel item = (TradeSupplierModel)args.Item;
    }

    async Task UpdateTradeHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel item = (TradeSupplierModel)args.Item;
        var updateResponse = await TradeService.UpdateTradeAsync(item);
        TradeData = (await TradeService.GetTradesAsync()).Value;
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
    }
    async Task DeleteTradeHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel item = (TradeSupplierModel)args.Item;
        var response = await TradeService.DeleteTradeAsync(item);
        TradeData = (await TradeService.GetTradesAsync()).Value;
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
    }

    async Task CreateTradeHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel item = (TradeSupplierModel)args.Item;
        var addResponse = await TradeService.AddTradeAsync(item);
        TradeData = (await TradeService.GetTradesAsync()).Value;
        ShowSuccessOrErrorNotification(addResponse.Message, addResponse.IsSuccess);
    }

    private async Task ShowDetails(GridCommandEventArgs args)
    {
        StateHasChanged();//parameters set doesn't seem to fire again if it was the same one as selected. is that why id doesn't show
        TradeSupplierModel item = (TradeSupplierModel)args.Item;
        SelectedSupplierNum = item.SubNumber;
        //Is the above conflicting with row selected item??
        await SupplierDetailsModal.Show();
    }

    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
