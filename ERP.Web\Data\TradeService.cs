﻿using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Pages;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using NLog;
using System.ComponentModel;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class TradeService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public TradeService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<ResponseModel<List<TradeSupplierModel>>> GetTradesAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/trade/GetTrades");
                var responseString = await response.Content.ReadAsStringAsync();
                var trades = JsonConvert.DeserializeObject<ResponseModel<List<TradeSupplierModel>>>(responseString);
                return trades;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }
            return new ResponseModel<List<TradeSupplierModel>> { IsSuccess = false, Message = "Error while fetching trades" };
        }
       
        public async Task<ResponseModel<int>> GetNextOneTimeBidAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/trade/GetNextOneTimeBid");
                var responseString = await response.Content.ReadAsStringAsync();
                var nextOneTimeBid = JsonConvert.DeserializeObject<ResponseModel<int>>(responseString);
                return nextOneTimeBid;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);               
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }
            return new ResponseModel<int> { IsSuccess = false, Value = 0, Message = "Error while fetching Bid" };
        }
        public async Task<ResponseModel<List<TradeSupplierModel>>> GetSuppliersAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/trade/GetSuppliers");
                var responseString = await response.Content.ReadAsStringAsync();
                var trades = JsonConvert.DeserializeObject<ResponseModel<List<TradeSupplierModel>>>(responseString);
                return trades;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<TradeSupplierModel>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<TradeSupplierModel>> { IsSuccess = false, Message = "Error while fetching suppliers" };
            }
        }
        public async Task<ResponseModel<TradeSupplierModel>> GetSupplierAsync(int SupplierNumber)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/trade/GetSupplier/{SupplierNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var supplier = JsonConvert.DeserializeObject<ResponseModel<TradeSupplierModel>>(responseString);
                return supplier;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Error while fetching supplier" };
            }
        }
        public async Task<ResponseModel<List<SupplierContactModel>>> GetSupplierContactsAsync(int SupplierNumber)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/trade/GetSupplierContacts/{SupplierNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var suppliers = JsonConvert.DeserializeObject<ResponseModel<List<SupplierContactModel>>>(responseString);
                return suppliers;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<SupplierContactModel>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<SupplierContactModel>> { IsSuccess = false, Message = "Error while fetching supplier contacts" };
            }
        }

        public async Task<ResponseModel<List<SupplierType>>> GetAllSupplierTypesAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/trade/GetAllSupplierTypes/");
                var responseString = await response.Content.ReadAsStringAsync();
                var supplierTypes = JsonConvert.DeserializeObject<ResponseModel<List<SupplierType>>>(responseString);
                return supplierTypes;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<SupplierType>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<SupplierType>> { IsSuccess = false, Message = "Error while fetching supplier types" };
            }
        }


        public async Task<ResponseModel<List<SupplierTypeModel>>> GetSupplierTypesAsync(int supplierNumber)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/trade/GetSupplierTypes/{supplierNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var supplierTypes = JsonConvert.DeserializeObject<ResponseModel<List<SupplierTypeModel>>>(responseString);
                return supplierTypes;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<SupplierTypeModel>> { IsSuccess = false, Message = "Login or consent needed", Value = new List<SupplierTypeModel>() };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<SupplierTypeModel>> { IsSuccess = false, Message = "Error while fetching supplier types" , Value = new List<SupplierTypeModel>() };
            }
        }

        public async Task<ResponseModel<RefreshSupplierAndCostsModel>> RefreshSupplierAsync(RefreshSupplierAndCostsModel refreshSupplier)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<RefreshSupplierAndCostsModel, ResponseModel<RefreshSupplierAndCostsModel>>("DownstreamApi", refreshSupplier,
                      options => {
                          options.RelativePath = $"api/trade/RefreshSupplier/";
                      });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<RefreshSupplierAndCostsModel>() { Value = refreshSupplier, IsSuccess = false, Message= "Error while refreshing supplier" };
        }

        public async Task<ResponseModel<SupplierContactModel>> AddContactAsync(SupplierContactModel addSupplier)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<SupplierContactModel, ResponseModel<SupplierContactModel>>(
                            "DownstreamApi", addSupplier,
                             options => {
                                 options.RelativePath = "api/trade/addsuppliercontact/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<SupplierContactModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<SupplierContactModel> { IsSuccess = false, Message = "Error while adding supplier contact" };
            }
        }
        public async Task<ResponseModel<SupplierContactModel>> UpdateContactAsync(SupplierContactModel updateSupplierContact)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<SupplierContactModel, ResponseModel<SupplierContactModel >>(
                            "DownstreamApi", updateSupplierContact,
                             options => {
                                 options.RelativePath = "api/trade/updatesuppliercontact/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif             
            }
            return new ResponseModel<SupplierContactModel> { IsSuccess = false, Message = "Error while updating supplier contact" };
        }
        public async Task<ResponseModel<SupplierContactModel>> DeleteContactAsync(SupplierContactModel updateSupplierContact)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<int, ResponseModel<SupplierContactModel>>(
                            "DownstreamApi", (int)updateSupplierContact.SupplierContactId,
                             options => {
                                 options.RelativePath = "api/trade/deletesuppliercontact/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }
            return new ResponseModel<SupplierContactModel> { IsSuccess = false, Message = "Error while deleting supplier contact" };
        }

        public async Task<ResponseModel<List<TradeSupplierModel>>> GetTradeAndSubdivisionsForSupplierAsync(int SupplierNumber)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/trade/GetTradeAndSubdivisionsForSupplier/{SupplierNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var suppliers = JsonConvert.DeserializeObject<ResponseModel<List<TradeSupplierModel>>>(responseString);
                return suppliers;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<TradeSupplierModel>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<TradeSupplierModel>> { IsSuccess = false, Message = "Error while fetching trades and sibdivision for supplier" };
            }
        }

        public async Task<ResponseModel<List<TradeSupplierModel>>> GetSuppliersForSubdivisionAndTradeAsync(int SubdivsionId, int TradeId)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/trade/GetSuppliersForSubdivisionAndTrade/{SubdivsionId}/{TradeId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var trades = JsonConvert.DeserializeObject<ResponseModel<List<TradeSupplierModel>>>(responseString);
                return trades;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<TradeSupplierModel>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<TradeSupplierModel>> { IsSuccess = false, Message = "Error while fetching suppliers for trade and subdivision" };
            }
        }

        public async Task<ResponseModel<SupplierType>> AddSupplierTypeAsync(string supplierType)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<string, ResponseModel<SupplierType>>(
                            "DownstreamApi", supplierType,
                             options => {
                                 options.RelativePath = "api/trade/addsuppliertype/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<SupplierType> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<SupplierType> { IsSuccess = false, Message = "Error while adding Supplier type" };
            }
        }

        public async Task<ResponseModel<SupplierTradeTypeModel>> AddSupplierTradeTypeAsync(SupplierTradeTypeModel supplierTradeType)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<SupplierTradeTypeModel, ResponseModel<SupplierTradeTypeModel>>(
                            "DownstreamApi", supplierTradeType,
                             options => {
                                 options.RelativePath = "api/trade/addsuppliertradetype/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<SupplierTradeTypeModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<SupplierTradeTypeModel> { IsSuccess = false, Message = "Error while adding Supplier Trade type" };
            }
        }
        public async Task<ResponseModel<TradeSupplierModel>> AddSupplierToTradeAndSubdivision(TradeSupplierModel updateSupplier)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<TradeSupplierModel, ResponseModel<TradeSupplierModel>>(
                            "DownstreamApi", updateSupplier,
                             options => {
                                 options.RelativePath = "api/trade/addsuppliertotradeandsubdivision/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Error while adding Supplier to Trade and Subdivision" };
            }
        }
        public async Task<ResponseModel<TradeSupplierModel>> AddSupplierToTradeAndSubdivisions(TradeSupplierModel updateSupplier)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<TradeSupplierModel, ResponseModel<TradeSupplierModel>>(
                            "DownstreamApi", updateSupplier,
                             options => {
                                 options.RelativePath = "api/trade/addsuppliertotradeandsubdivisions/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Error while adding Supplier to Trade and Subdivision" };
            }
        }

        public async Task<ResponseModel<SimplerSupplierDto>> AddSupplierAsync(SimplerSupplierDto updateSupplier)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<SimplerSupplierDto, ResponseModel<SimplerSupplierDto>>(
                            "DownstreamApi", updateSupplier,
                             options => {
                                 options.RelativePath = "api/trade/addsupplier/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<SimplerSupplierDto> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<SimplerSupplierDto> { IsSuccess = false, Message = "Error while adding Supplier" };
            }
        }

        //update the supplier details; does not change subdivision, trade or default for subdivision
        public async Task<ResponseModel<TradeSupplierModel>> UpdateSupplierAsync(TradeSupplierModel updateSupplier)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<TradeSupplierModel, ResponseModel<TradeSupplierModel>>(
                            "DownstreamApi", updateSupplier,
                             options => {
                                 options.RelativePath = "api/trade/updatesupplier/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Error while updating Supplier" };
            }
        }

        //update the supplier name and if it's default for the subdivision
        public async Task<ResponseModel<TradeSupplierModel>> UpdateTradeSupplierAsync(TradeSupplierModel updateSupplier)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<TradeSupplierModel, ResponseModel<TradeSupplierModel>>(
                            "DownstreamApi", updateSupplier,
                             options => {
                                 options.RelativePath = "api/trade/updatetradesupplier/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Error while updating Trade Supplier" };
            }
        }
        public async Task<ResponseModel<TradeSupplierModel>> DeleteSupplierAsync(TradeSupplierModel updateSupplier)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<TradeSupplierModel, ResponseModel<TradeSupplierModel>>(
                            "DownstreamApi", updateSupplier,
                             options => {
                                 options.RelativePath = "api/trade/deletesupplier/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Error while deleting Supplier" };
            }
        }

        public async Task<ResponseModel<SupplierTradeTypeModel>> DeleteSupplierTradeTypeAsync(SupplierTradeTypeModel supplierTradeTypeModel)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<SupplierTradeTypeModel, ResponseModel<SupplierTradeTypeModel>>(
                            "DownstreamApi", supplierTradeTypeModel,
                             options => {
                                 options.RelativePath = "api/trade/deletesuppliertradetype/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<SupplierTradeTypeModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<SupplierTradeTypeModel> { IsSuccess = false, Message = "Error while deleting Supplier Trade Type" };
            }
        }
        public async Task<ResponseModel<AssignTradeSupplierModel>> DeleteSupplierFromTradeAsync(AssignTradeSupplierModel updateSupplier)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<AssignTradeSupplierModel, ResponseModel<AssignTradeSupplierModel>>(
                            "DownstreamApi", updateSupplier,
                             options => {
                                 options.RelativePath = "api/trade/deletesupplierfromtrade/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<AssignTradeSupplierModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<AssignTradeSupplierModel> { IsSuccess = false, Message = "Error while deleting Supplier from Trade" };
            }
        }

        public async Task<ResponseModel<TradeSupplierModel>> AddTradeAsync(TradeSupplierModel tradeToAdd)
        {
            try
            {

                var response = await _downstreamAPI.PostForUserAsync<TradeSupplierModel, ResponseModel<TradeSupplierModel>>(
                    "DownstreamApi", tradeToAdd,
                    options => {
                        options.RelativePath = "api/trade/addtrade/";
                    });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Error while adding Trade" };
            }
        }

        public async Task<ResponseModel<TradeSupplierModel>> UpdateTradeAsync(TradeSupplierModel tradeToUpdate)
        {
            try
            {

                var response = await _downstreamAPI.PutForUserAsync<TradeSupplierModel, ResponseModel<TradeSupplierModel>>(
                    "DownstreamApi", tradeToUpdate,
                    options => {
                        options.RelativePath = "api/trade/updatetrade/";
                    });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Error while updating Trade" };
            }
        }
        public async Task<ResponseModel<TradeSupplierModel>> DeleteTradeAsync(TradeSupplierModel tradeToUpdate)
        {
            try
            {

                var response = await _downstreamAPI.PutForUserAsync<TradeSupplierModel, ResponseModel<TradeSupplierModel>>(
                    "DownstreamApi", tradeToUpdate,
                    options => {
                        options.RelativePath = "api/trade/deletetrade/";
                    });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<TradeSupplierModel> { IsSuccess = false, Message = "Error while deleting Trade" };
            }
        }
    }
}
