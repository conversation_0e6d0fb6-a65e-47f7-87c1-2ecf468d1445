﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class MaterialDto : IMapFrom<Material>
{
    public int MateriaId { get; set; }

    public string? Material1 { get; set; } 

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

   // public byte[] RecordTimeStamp { get; set; } = null!;

   // public virtual ICollection<MaterialColorPredefined> MaterialColorPredefineds { get; set; } = new List<MaterialColorPredefined>();
}
