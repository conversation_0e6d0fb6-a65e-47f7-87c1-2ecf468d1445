﻿@inject SubdivisionService SubdivisionService
@inject ScheduleService ScheduleService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject PoService PoService
@inject IJSRuntime JsRuntime
@inject BudgetService BudgetService
@using ERP.Data.Models.ExtensionMethods
@attribute [Authorize(Roles = "Admin, ConstructionDirector, ConstructionManager")]
@using ERP.Data.Models.Dto
@using ERP.Data.Models
@using Telerik.DataSource.Extensions
@using System.Collections.ObjectModel


<style type="text/css">

    /* Gutter */
    .row > * {
        padding-right: calc(var(--bs-gutter-x) * .10);
        padding-left: calc(var(--bs-gutter-x) * .10);
    }
     .k-table-td {
        padding: 4px !important;
        font-size: 11px;
    }

    .k-table-row {
        height: 40px !important
    }
 
    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }


</style>
<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="false" ShowJob="false" ShowSubdivision="false" @ref="subdivisionJobPickerMenuBar"></ERP.Web.Components.SubdivisionJobPickerMenuBar>

<NavigationLock ConfirmExternalNavigation="@UnsavedChanges" OnBeforeInternalNavigation="BeforeInternalNavigation" />
<TelerikTooltip TargetSelector=".tooltip-target" />
<div class="row">
    <TelerikGrid Data="@MyScheduleActivities"
                 @ref="GridRef"
                 FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                 Height="1000px" RowHeight="60" PageSize="20"
                 Width="100%"
                 Sortable="true"
                 Resizable="true"
                 Reorderable="true"
                 Groupable="true"
                 Size="@ThemeConstants.Grid.Size.Small"
                 EditMode="GridEditMode.Incell"
                 OnUpdate="@UpdateItem"
                 OnEdit="@EditItem"
                 SelectionMode="GridSelectionMode.Single"
                 OnStateInit="@( (GridStateEventArgs<ScheduleSactivityDto> args) => OnGridStateInit(args) )"
                 ConfirmDelete="true">
        <GridColumns>
            <GridColumn Field="ScheduleM.Schedule.JobNumber" Title="Job" Editable="false" Groupable="true" Width="200px">
                <Template>
                    @{
                        var item = context as ScheduleSactivityDto;
                        @item.ScheduleM.Schedule.JobNumber <span> - </span>
                        <TelerikButton Class="tooltip-target k-button-success" Icon="@FontIcon.DetailSection" Title="Job Details" OnClick="() => SelectJobDetails(item)"  ></TelerikButton>
                        if (item.HasPo)
                        {
                            <TelerikButton Class="tooltip-target k-button-success" Icon="@FontIcon.Download" Title="PO" OnClick="() => ViewPo(item)"></TelerikButton>
                        }                                                 
                        <TelerikButton Class="tooltip-target k-button-success" Icon="@FontIcon.Calendar" Title="Schedule" OnClick="() => SelectSchedule(item)" ></TelerikButton>
                    }
                </Template>
            </GridColumn> 
           @*  <GridColumn Field="ScheduleM.Schedule.JobNumberNavigation.LotNumber" Title="Lot" Editable="false" Groupable="true" Width="50px" /> *@
            <GridColumn Field="BoolStarted" Title="Started" Editable="false" Groupable="true">
                <Template>
                    @{
                        var itemToEdit = context as ScheduleSactivityDto;
                        if (itemToEdit != null && itemToEdit.BoolStarted != null)
                        {
                            <TelerikCheckBox OnChange="() => ChangeStarted(itemToEdit)" @bind-Value="itemToEdit.BoolStarted"></TelerikCheckBox>
                        }
                    }
                </Template>
            </GridColumn>
            <GridColumn Field="Complete" Title="Complete" Editable="false" Groupable="true">
                <Template>
                    @{
                        var itemToEdit = context as ScheduleSactivityDto;
                        if (itemToEdit != null)
                        {
                            <TelerikCheckBox OnChange="() => ChangeComplete(itemToEdit)" @bind-Value="itemToEdit.BoolComplete"></TelerikCheckBox>
                        }
                    }
                </Template>
            </GridColumn>
            
            <GridColumn Field="Sactivity.ActivityName" Title="Activity" Editable="false" Groupable="true" Width="200px">
                <Template>
                    @{
                        var item = context as ScheduleSactivityDto;
                        <TelerikButton Title="View/Edit" OnClick="() => EditActivity(item)" Icon="@FontIcon.Pencil" Class="k-button-success"></TelerikButton>
                        <span> - </span>
                        @item.Sactivity.ActivityName                       
                    }
                </Template>
            </GridColumn> 
            <GridColumn Field="VarianceCode" Title="Variance" Editable="false" Groupable="true" Width="90px">
                <Template>
                    @{
                        var item = context as ScheduleSactivityDto;
                        if (item != null)
                        {
                            <span>@item.VarianceCode</span>
                        }
                    }
                </Template>
                <EditorTemplate>
                    @{
                        ItemToEdit = context as ScheduleSactivityDto;
                        if (ItemToEdit != null)
                        {
                            <TelerikDropDownList Data="@VarianceCodes"
                                                 TextField="VarianceDesc"
                                                 ValueField="VarianceCode"
                                                 @bind-Value="ItemToEdit.VarianceCode">
                            </TelerikDropDownList>
                        }
                    }
                </EditorTemplate>
            </GridColumn> 
            <GridColumn Field="SchStartDate" Title="Scheduled Start" Editable="false" Groupable="true" DisplayFormat="{0:MM/dd/yyyy}" Width="90px" />
            <GridColumn Field="SchEndDate" Title="Scheduled End" Editable="false" Groupable="true" DisplayFormat="{0:MM/dd/yyyy}" Width="90px" />
            @* <GridColumn Field="ActualStartDate" Title="Actual Start" Editable="false" Groupable="true" DisplayFormat="{0:MM/dd/yyyy}" Width="90px" />
            <GridColumn Field="ActualEndDate" Title="Actul End" Editable="false" Groupable="true" DisplayFormat="{0:MM/dd/yyyy}" Width="90px" /> *@
           @*  <GridColumn Field="ScheduleM.Schedule.JobNumberNavigation.JobAddress1" Title="Address" Editable="false" Groupable="true" Width="200px" /> *@
            @* <GridCommandColumn Width="200px">
                <GridCommandButton Class="tooltip-target k-button-success" Icon="@FontIcon.DetailSection" Title="Job Details" OnClick="SelectJobDetails" Command="Details"></GridCommandButton>
                <GridCommandButton Class="tooltip-target k-button-success" Icon="@FontIcon.Calendar" Title="Schedule" OnClick="SelectSchedule" Command="Schedule"></GridCommandButton>
            </GridCommandColumn> *@
        </GridColumns>
        <GridToolBarTemplate>
            <GridSearchBox DebounceDelay="200"></GridSearchBox>
            <GridCommandButton Command="Save" OnClick="@Save" Title="Save Changes" Icon="@FontIcon.Save" Class=" tooltip-target k-button-success"></GridCommandButton>
            <GridCommandButton Command="Cancel" OnClick="@Cancel" Title="Cancel Changes" Icon="@FontIcon.Cancel" Class=" tooltip-target k-button-danger"></GridCommandButton>
        </GridToolBarTemplate>
    </TelerikGrid>

    <br />
    <br />
</div>
<EditScheduleActivity @ref="EditScheduleActivity" SelectedActivity="@SelectedActivity" HandleAddSubmit="@HandleValidEditActivitySubmit"></EditScheduleActivity>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
@code {
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    private bool userIsDirector { get; set; } = false;
    public List<ScheduleSactivityDto>? MyScheduleActivities { get; set; } = new List<ScheduleSactivityDto>();
    public TelerikGrid<ScheduleSactivityDto>? GridRef { get; set; }
    public ScheduleSactivityDto? ItemToEdit { get; set; } = new ScheduleSactivityDto();
    public List<VarianceDto>? VarianceCodes { get; set; } = new List<VarianceDto>();
    protected EditScheduleActivity? EditScheduleActivity { get; set; }
    public ScheduleSactivityDto? SelectedActivity { get; set; }
    private bool UnsavedChanges { get; set; } = false;
    private List<DateTime>? holidays { get; set; }
    public SubdivisionJobPickerMenuBar? subdivisionJobPickerMenuBar { get; set; }
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    private async Task BeforeInternalNavigation(LocationChangingContext context)
    {
        if (UnsavedChanges)
        {
            var proceed = await Dialogs.ConfirmAsync("You have unsaved changes, are you sure you want to leave this page?");

            if (!proceed)
            {
                context.PreventNavigation();
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userRoleConstructionDirector = user.User.IsInRole("ConstructionDirector");
        userIsDirector = userRoleAdmin || userRoleConstructionDirector;
        var varianceCodesTask = ScheduleService.GetVarianceCodesAsync();       
        var schedulesTask = ScheduleService.GetScheduleActivitysForConstructionSuperAsync(userName);
        var getCalendarTask = ScheduleService.GetHolidaysAsync();
        await Task.WhenAll(new Task[] { schedulesTask, varianceCodesTask, getCalendarTask });
        MyScheduleActivities = schedulesTask.Result.Value;
        VarianceCodes = varianceCodesTask.Result.Value;
        holidays = getCalendarTask.Result.Value.Select(x => x.WorkDate).ToList();
    }
    private async Task OnGridStateInit(GridStateEventArgs<ScheduleSactivityDto> args)
    {

        // Filter ctivities that start today or earlier
        var todayActivities = new CompositeFilterDescriptor()
            {
                FilterDescriptors = new FilterDescriptorCollection() {
                 new FilterDescriptor()
                 {
                    Member = nameof(ScheduleSactivityDto.SchStartDate),
                    MemberType = typeof(DateTime?),
                    Operator = FilterOperator.IsLessThanOrEqualTo,
                    Value = DateTime.Now
                 }
             }
            };
        args.GridState.FilterDescriptors.Add(todayActivities);

    }
    async Task ViewPo(ScheduleSactivityDto activity)
    {
        if (activity != null)
        {
            var getPos = await PoService.GetPOForScheduleActivityAsync(activity.ScheduleAid);
            if(getPos?.Value != null && getPos.Value.Count != 0)
            {
                var details = getPos.Value;
                var groupedByHeder = details.GroupBy(x => x.PoheaderId).ToList();
                foreach (var po in groupedByHeder)
                {
                    var header = po.First().Poheader;
                    await GenerateDocumentAndDownload(header, po.Select(x => x).ToList());
                }
            }
        }
    }
    async Task SelectSchedule(ScheduleSactivityDto activity)
    {
        if (activity != null)
        {
            SubdivisionJobPickService.JobNumber = activity.ScheduleM.Schedule.JobNumber;
            subdivisionJobPickerMenuBar.StoreSubdivJobSupplier();
            NavManager.NavigateTo($"schedule");
            StateHasChanged();
        }
    }
    async Task SelectJobDetails(ScheduleSactivityDto activity)
    {
        if (activity != null)
        {
            SubdivisionJobPickService.JobNumber = activity.ScheduleM.Schedule.JobNumber;
            subdivisionJobPickerMenuBar.StoreSubdivJobSupplier();
            NavManager.NavigateTo($"lotdetails/{activity.ScheduleM.Schedule.JobNumber}");
            StateHasChanged();
        }
    }
    async Task JobChangedHandler()
    {
        var selected = SubdivisionJobPickService.JobNumber;
    }
    public async Task ChangeComplete(ScheduleSactivityDto itemToEdit)
    {
        var findItem = MyScheduleActivities.FirstOrDefault(x => x.ScheduleAid == itemToEdit.ScheduleAid);
        if (itemToEdit.BoolComplete)
        {
            if (itemToEdit.ActualStartDate == null)
            {
                Dialogs.AlertAsync("Activity must be started before being completed.");
                findItem.BoolComplete = false;
                return;
            }
            else if (!userIsDirector && (CalendarExtension.AddWorkingDays(itemToEdit.SchEndDate.Value.Date, 1, holidays) < DateTime.Now.Date || (CalendarExtension.AddWorkingDays(itemToEdit.SchEndDate.Value.Date, 1, holidays) == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                //basedateeditable is user is in construction dir role
                // await Dialogs.AlertAsync("Activity is late! You must select a variance and then adjust the scheduled dates. Contact Construction Director to override this if needed");
                // findItem.BoolComplete = false;
                //for now just warn if late
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    findItem.BoolComplete = false;
                }
                else
                {
                    //findItem.ActualStartDate = findItem.SchStartDate;
                    findItem.ActualEndDate = findItem.SchEndDate;
                    UnsavedChanges = true;
                }
            }
            else if (userIsDirector && (CalendarExtension.AddWorkingDays(itemToEdit.SchEndDate.Value.Date, 1, holidays) < DateTime.Now.Date))
            {
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    findItem.BoolComplete = false;
                }
                else
                {
                    //findItem.ActualStartDate = findItem.SchStartDate;
                    findItem.ActualEndDate = findItem.SchEndDate;
                    UnsavedChanges = true;
                }
            }
            else
            {
               // findItem.ActualStartDate = findItem.SchStartDate;
                findItem.ActualEndDate = findItem.SchEndDate;
                UnsavedChanges = true;
            }

        }
        else
        {
            findItem.ActualEndDate = null;
            findItem.ActualStartDate = null;
            UnsavedChanges = true;
        }
        GridRef.Rebind();
    }
    public async Task ChangeStarted(ScheduleSactivityDto itemToEdit)
    {
        var findItem = MyScheduleActivities.FirstOrDefault(x => x.ScheduleAid == itemToEdit.ScheduleAid);
        if (itemToEdit.BoolStarted)
        {
            if (!userIsDirector && (itemToEdit.SchStartDate.Value.Date < DateTime.Now.Date || (itemToEdit.SchStartDate.Value.Date == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                // await Dialogs.AlertAsync("This activity is late! You must select a variance and then adjust the scheduled dates. Contact Construction Director to override this if needed");
                // findItem.BoolStarted = false;
                //for now just warn if late
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    findItem.BoolStarted = false;
                }
                else
                {
                    itemToEdit.ActualStartDate = itemToEdit.SchStartDate;
                    UnsavedChanges = true;
                }
            }
            else if (userIsDirector && (itemToEdit.SchStartDate.Value.Date < DateTime.Now.Date))
            {
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    findItem.BoolStarted = false;
                }
                else
                {
                    itemToEdit.ActualStartDate = itemToEdit.SchStartDate;
                    UnsavedChanges = true;
                }
            }
            else
            {
                itemToEdit.ActualStartDate = itemToEdit.SchStartDate;
                UnsavedChanges = true;
            }
        }
        else
        {
            findItem.ActualEndDate = null;
            findItem.ActualStartDate = null;
            UnsavedChanges = true;
        }
        GridRef.Rebind();
    }
    private async Task EditItem(GridCommandEventArgs args)
    {
        var item = args.Item as ScheduleSactivityDto;

    }
    private async Task UpdateItem(GridCommandEventArgs args)
    {
        var item = args.Item as ScheduleSactivityDto;
        UnsavedChanges = true;
        var findItem = MyScheduleActivities.FirstOrDefault(x => x.ScheduleAid == item.ScheduleAid);
        if(findItem != null)
        {
            findItem.VarianceCode = item.VarianceCode;
            findItem.ActualStartDate = item.ActualStartDate;
            findItem.ActualEndDate = item.ActualEndDate;
        }

    }
    public async void Save()
    {
        //TODO: if activity marked complete, should it change scheduled dates of other activities?
        var activitiesToUpdate = MyScheduleActivities;
        var response = await ScheduleService.UpdateScheduleSactivitysAsync(activitiesToUpdate);
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        if (response.IsSuccess)
        {
            UnsavedChanges = false;
            //reload the data to remove the approved ones
            var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userName = user.User.Identity.Name.Split('@')[0];
            MyScheduleActivities = (await ScheduleService.GetScheduleActivitysForConstructionSuperAsync(userName)).Value;
        }
        StateHasChanged();
    }

    public async void Cancel()
    {
        //refresh the data
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        MyScheduleActivities = (await ScheduleService.GetScheduleActivitysForConstructionSuperAsync(userName)).Value;
        UnsavedChanges = false;
        ShowSuccessOrErrorNotification("Edits Cancelled", true);
        StateHasChanged();
    }
    private void EditActivity(ScheduleSactivityDto activity)
    {
        if (activity != null)
        {
            SelectedActivity = activity;
        }
        EditScheduleActivity?.Show();
        StateHasChanged();
    }
    private async void HandleValidEditActivitySubmit(ResponseModel<ScheduleSactivityDto> responseActivity)
    {
        EditScheduleActivity.Hide();
        if (responseActivity.IsSuccess)
        {
            var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userName = user.User.Identity.Name.Split('@')[0];
            var schedulesTask = await ScheduleService.GetScheduleActivitysForConstructionSuperAsync(userName);
            MyScheduleActivities = schedulesTask.Value;
        }
        ShowSuccessOrErrorNotification(responseActivity.Message, responseActivity.IsSuccess);
        StateHasChanged();
    }
    async Task GenerateDocumentAndDownload(PoheaderDto poHeader, List<PodetailDto> poDetails)
    {
        //TODO: at this point it should be getting a stored po, not generating it
        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(poHeader, poDetails);
        DemoFileExporter.Save(JsRuntime, fileData, "application/pdf", $"PO-{poHeader.Ponumber}.pdf");
    }    
    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                Closable = true,
                CloseAfter = 0,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
