﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{

    public class RootBCPOResponseObject
    {
        public string odatacontext { get; set; }
        public Value[] value { get; set; }
    }

    public class Value
    {
        public string odataetag { get; set; }
        public string id { get; set; }
        public string number { get; set; }
        public string orderDate { get; set; }
        public string postingDate { get; set; }
        public string vendorId { get; set; }
        public string vendorNumber { get; set; }
        public string vendorName { get; set; }
        public string payToName { get; set; }
        public string payToVendorId { get; set; }
        public string payToVendorNumber { get; set; }
        public string shipToName { get; set; }
        public string shipToContact { get; set; }
        public string buyFromAddressLine1 { get; set; }
        public string buyFromAddressLine2 { get; set; }
        public string buyFromCity { get; set; }
        public string buyFromCountry { get; set; }
        public string buyFromState { get; set; }
        public string buyFromPostCode { get; set; }
        public string payToAddressLine1 { get; set; }
        public string payToAddressLine2 { get; set; }
        public string payToCity { get; set; }
        public string payToCountry { get; set; }
        public string payToState { get; set; }
        public string payToPostCode { get; set; }
        public string shipToAddressLine1 { get; set; }
        public string shipToAddressLine2 { get; set; }
        public string shipToCity { get; set; }
        public string shipToCountry { get; set; }
        public string shipToState { get; set; }
        public string shipToPostCode { get; set; }
        public string shortcutDimension1Code { get; set; }
        public string shortcutDimension2Code { get; set; }
        public string currencyId { get; set; }
        public string currencyCode { get; set; }
        public bool pricesIncludeTax { get; set; }
        public string paymentTermsId { get; set; }
        public string shipmentMethodId { get; set; }
        public string purchaser { get; set; }
        public string requestedReceiptDate { get; set; }
        public int discountAmount { get; set; }
        public bool discountAppliedBeforeTax { get; set; }
        public float totalAmountExcludingTax { get; set; }
        public int totalTaxAmount { get; set; }
        public float totalAmountIncludingTax { get; set; }
        public bool fullyReceived { get; set; }
        public string status { get; set; }
        public DateTime lastModifiedDateTime { get; set; }
    }

    
    public class BCPoHeader
    {
        // public string? id { get; set; }
        public string? number { get; set; }
        public string? postingDescription { get; set; }
        //public string orderDate { get; set; }
        //public string postingDate { get; set; }
        //public string dueDate { get; set; }
        //public string vendorId { get; set; }
        // public string? apCategoryCode { get; set; }
        public string? documentType { get; set; }
        public string? documentDate { get; set; }
        public string? postingDate { get; set; }
        public string? vendorNumber { get; set; }
        public string? vendorInvoiceNumber { get; set; }
        public string? shortcutDimension1Code { get; set; }//subdivision
        public string? shortcutDimension2Code { get; set; }//entity
        //public string payToVendorId { get; set; }
        //public string payToVendorNumber { get; set; }
        //public string shipToName { get; set; }
        //public string shipToContact { get; set; }
        //public string buyFromAddressLine1 { get; set; }
        //public string buyFromAddressLine2 { get; set; }
        //public string buyFromCity { get; set; }
        //public string buyFromCountry { get; set; }
        //public string buyFromState { get; set; }
        //public string buyFromPostCode { get; set; }
        //public string shipToAddressLine1 { get; set; }
        //public string shipToAddressLine2 { get; set; }
        //public string shipToCity { get; set; }
        //public string shipToCountry { get; set; }
        //public string shipToState { get; set; }
        //public string shipToPostCode { get; set; }
        //public string currencyId { get; set; }
        //public string currencyCode { get; set; }
        //public bool pricesIncludeTax { get; set; }
        //public string paymentTermsId { get; set; }
        //public string shipmentMethodId { get; set; }
        //public string purchaser { get; set; }
        //public string requestedReceiptDate { get; set; }
        //public int discountAmount { get; set; }
        public List<BCPoDetail>? purchaseOrderLines { get; set; }
        public List<DimensionSetLines>? dimensionSetLines { get; set; }
    }
    public class BCCreditMemoHeader
    {
        public string? number { get; set; }
        public string? postingDescription { get; set; }
        public string? postingDate { get; set; }
        public string? documentDate { get; set; }
        public string? documentType { get; set; }
        public string? vendorCrMemoNumber { get; set; }
        public string? vendorNumber { get; set; }
        public string? shortcutDimension1Code { get; set; }//subdivision
        public string? shortcutDimension2Code { get; set; }//entity
        public List<BCCreditMemoDetail>? purchaseCreditMemoLines { get; set; }
        public List<DimensionSetLines>? dimensionSetLines { get; set; }
    }
    public class DimensionSetLines
    {
        public string? code { get; set; }
        public string? valueCode { get; set; }
    }
    public class BcResponsePoHeader
    {
        public string odatacontext { get; set; }
        public string odataetag { get; set; }
        public string id { get; set; }
        public string number { get; set; }
        public string orderDate { get; set; }
        public string postingDate { get; set; }
        public string vendorId { get; set; }
        public string vendorNumber { get; set; }
        public string vendorName { get; set; }
        public string payToName { get; set; }
        public string payToVendorId { get; set; }
        public string payToVendorNumber { get; set; }
        public string shipToName { get; set; }
        public string shipToContact { get; set; }
        public string buyFromAddressLine1 { get; set; }
        public string buyFromAddressLine2 { get; set; }
        public string buyFromCity { get; set; }
        public string buyFromCountry { get; set; }
        public string buyFromState { get; set; }
        public string buyFromPostCode { get; set; }
        public string payToAddressLine1 { get; set; }
        public string payToAddressLine2 { get; set; }
        public string payToCity { get; set; }
        public string payToCountry { get; set; }
        public string payToState { get; set; }
        public string payToPostCode { get; set; }
        public string shipToAddressLine1 { get; set; }
        public string shipToAddressLine2 { get; set; }
        public string shipToCity { get; set; }
        public string shipToCountry { get; set; }
        public string shipToState { get; set; }
        public string shipToPostCode { get; set; }
        public string shortcutDimension1Code { get; set; }
        public string shortcutDimension2Code { get; set; }
        public string currencyId { get; set; }
        public string currencyCode { get; set; }
        public bool pricesIncludeTax { get; set; }
        public string paymentTermsId { get; set; }
        public string shipmentMethodId { get; set; }
        public string purchaser { get; set; }
        public string requestedReceiptDate { get; set; }
        public int discountAmount { get; set; }
        public bool discountAppliedBeforeTax { get; set; }
        public int totalAmountExcludingTax { get; set; }
        public int totalTaxAmount { get; set; }
        public int totalAmountIncludingTax { get; set; }
        public bool fullyReceived { get; set; }
        public string status { get; set; }
        public DateTime lastModifiedDateTime { get; set; }
        // public List<BCre>? BCPoDetail { get; set; }
    }

}
