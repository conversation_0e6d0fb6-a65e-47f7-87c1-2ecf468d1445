﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class JobAttachmentNotify
{
    public int AttachmentNotifyId { get; set; }

    public int? AttachmentId { get; set; }

    public int? Status { get; set; }

    public DateTime? AttachmentDate { get; set; }

    public string? Userid { get; set; }

    public int? SubNumber { get; set; }

    public int? ContactId { get; set; }

    public DateTime? DateViewed { get; set; }

    public DateTime? DateModified { get; set; }

    public string? EmailTitle { get; set; }

    public string? EmailContent { get; set; }

    public string? Notes { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual JobAttachment? Attachment { get; set; }
}
