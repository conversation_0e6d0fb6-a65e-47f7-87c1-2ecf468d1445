﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class EstoptionDto : IMapFrom<Estoption>
{
    public int EstoptionId { get; set; }
    public bool? IsIssued { get; set; }//to be used to track if the estdetails in the option are issued (ie they have estjcedetailid)

    public int? EstheaderId { get; set; }
    public double? TotalCost { get; set; }//sum from item costs

    public string? OptionNumber { get; set; }

    public string? OptionDesc { get; set; }

    public double? OptionSalesPrice { get; set; }

    public double? OptionQty { get; set; }

    public string? IsElevation { get; set; }

    public string? OptionNotes { get; set; }

    public int? SalesconfigoptionsId { get; set; }

    public int? SalesconfigcooptionsId { get; set; }

    public string? IsDeleted { get; set; }

    public string? OptionSelections { get; set; }

    public string? OptionLongDesc { get; set; }

    public string? OptionExtendedDesc { get; set; }

    public string? ElevationCode { get; set; }

    public string? ElevationDesc { get; set; }

    public string? WmsplanNum { get; set; }

    public string? PlanName { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CubitId { get; set; }

    public string? IsSelected { get; set; }

    public string? IncludeInBase { get; set; }

    public int? EstselectiontypeId { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    //public byte[]? RecordTimeStamp { get; set; }

    public EstheaderDto? Estheader { get; set; }

    public SalesconfigcooptionDto? Salesconfigcooptions { get; set; }

    public SalesconfigoptionDto? Salesconfigoptions { get; set; }
    public List<EstdetailDto>? EstdetailList { get; set; }//to use for adding, but shouldn't map else get circular reference
    public int? AsmHeaderId { get; set; }//to use in selecting from asm header to add new estimate
}
