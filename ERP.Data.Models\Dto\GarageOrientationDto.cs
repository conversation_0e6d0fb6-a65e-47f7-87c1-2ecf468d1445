﻿using System;
using System.Collections.Generic;
using AutoMapper;
using ERP.Data.Models.Abstract;
namespace ERP.Data.Models;

public class GarageOrientationDto : IMapFrom<GarageOrientation>
{
    public int GarageOrientationId { get; set; }

    public short? ListOrder { get; set; }

    public string? Name { get; set; }

    public bool? IsActive { get; set; }

    public string? Note { get; set; }

    public DateTime? CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

}
