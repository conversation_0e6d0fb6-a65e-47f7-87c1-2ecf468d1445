﻿using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models;

public class ColorGridModel
{
    public int Id { get; set; }
    public string? SubdivisionName { get; set; }
    public string? SubdivisionNum { get; set; }
    public string? ColorSchemeNum { get; set; }
    public string? PlanName { get; set; }
    public string? PlanNum { get; set; }
    public string? PlanOptionName { get; set; }
    public string? OptionCode { get; set; }
    public string? MaterialName { get; set; }
    public string? ColorScheme1 { get; set; }
    public int? MaterialId { get; set; }
    public int? ColorSchemeId { get; set; }
    public int? PredefinedMaterialColorId { get; set; }
}