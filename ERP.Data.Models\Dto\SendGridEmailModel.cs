﻿namespace ERP.Data.Models.Dto
{
    public class SendGridEmailModel
    {
        public string? MessageSubject { get; set; }

        public string? MessageBody { get; set; }

        public string? POMessageHead { get; set; }

        public string? POMessageText { get; set; }

        public string? Email { get; set; }

        public string? SubName { get; set; }

        public string? JobNumber { get; set; }

        public string? LotNumber { get; set; }

        public string? SubdivisionName { get; set; }

        public string? JobAddress { get; set; }

        public Activities? Activities { get; set; }

        public int? VpoNumber { get; set; }

        public string? Podescription { get; set; }

        public double? Pototal { get; set; }

    }
    public class Activities
    {
        public List<UpdateActivity>? UpdateActivities { get; set; }
    }
    public class UpdateActivity
    {
        public int? SubNumber { get; set; }
        public string? SubName { get; set; }
        public string? JobNumber { get; set; }
        public string? ActivityName { get; set; }
        public string? StartDate { get; set; }
        public string? EndDate { get; set; } 
    }
}
