﻿
using AutoMapper;
using ERP.API.Utilities;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using NLog;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class JobDocumentController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private readonly Email _email;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        public JobDocumentController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _email = new Email(configuration);
        }

        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> ListJobDocumentsAsync(string jobNumber)
        {
            var documentsDto = new List<JobAttachmentDto>();
            try
            {
                var documents = await _context.JobAttachments.AsNoTracking().Include("Doctype").Where(x => x.JobNumber == jobNumber  && x.Path != null && x.Path.StartsWith("https")).ToListAsync();//excluding old documents from WMS which are still in the table, they have path to WMS server like this \\VMBMTDB\Data\BuilderMT\ProductionData\DocumentAttachments\BS273466 or C:\Users\<USER>\Desktop\LB2 LOT 105 . New ERP ones have path to Azure blob storage like:https://vanmetreerp.blob.core.windows.net/ppth0
                documentsDto = _mapper.Map<List<JobAttachmentDto>>(documents);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobAttachmentDto>>() { IsSuccess = false, Message = "failed to alist job documents", Value = null });
            }
            return Ok(new ResponseModel<List<JobAttachmentDto>>() { Value = documentsDto, IsSuccess = true });
        }
        [HttpGet]
        public async Task<IActionResult> GetDocumentTypesAsync()
        {
            var docTypes = new List<DoctypeDto>();
            try
            {
                var documentTypes = await _context.Doctypes.AsNoTracking().Where(x => x.IsActive == true).AsNoTracking().ToListAsync();
                docTypes = _mapper.Map<List<DoctypeDto>>(documentTypes);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<DoctypeDto>>() { IsSuccess = false, Message = "failed to get document types", Value = null });
            }
            return Ok(new ResponseModel<List<DoctypeDto>> { Value = docTypes, IsSuccess = true });
        }

        [HttpPost]
        public async Task<IActionResult> UploadJobDocument([FromForm]DocumentUploadModel model)
        {

            try
            {
                //per Denise 2/18/25, she will pick the doc type after upload. This way multiple of different types can be uploaded at once
                //if(model.DocTypeId == 0 )
                //{ 
                //    return BadRequest("Document Type must be selected"); 
                //}
                var getType = _context.Doctypes.SingleOrDefault(x => x.DoctypeId == model.DocTypeId)?.Doctype1 ?? "";
                var response = await BlobStorage.UploadFileToBlobAsync(model.files, model.JobNumber.ToLower(), model.files.FileName, getType);
                //containers ("folders") named by job number
                var newDoc = new JobAttachment()
                {
                   // DoctypeId = model.DocTypeId,//Per Denise 2/18/25, to be added later
                    JobNumber = model.JobNumber,
                    Name = response.FileName,
                    Path = response.Path,
                    CreatedBy = User.Identity.Name.Split('@')[0] 
                };
                _context.JobAttachments.Add(newDoc);
                await _context.SaveChangesAsync();
                if (model.EmailNotification && model.EmailRecipients != null && model.EmailRecipients.Count != 0)
                {
                    _email.SendEmail($"{model.EmailSubject}", $"{model.EmailBody}", model.EmailRecipients);
                    foreach (var recipient in model.EmailRecipients)
                    {
                        var findContact = _context.Users.SingleOrDefault(x => x.EmailAddress == recipient);
                        if (findContact != null)
                        {
                            var attachNotify = new JobAttachmentNotify()
                            {
                                AttachmentId = newDoc.AttachmentId,
                                EmailContent = model.EmailBody,
                                EmailTitle = model.EmailSubject,
                                Userid = findContact.UserId,
                            };
                            _context.JobAttachmentNotifies.Add(attachNotify);
                            await _context.SaveChangesAsync();
                        }                       
                    }                   
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<bool>() { IsSuccess = false, Message = "failed upload job document", Value = false });
            }
            return Ok(new ResponseModel<bool>() { Value = true, IsSuccess = true });
        }

        [HttpPut]
        public async Task<IActionResult> UpdateJobDocumentAsync([FromBody] JobAttachmentDto document)
        {

            try
            {
                var findDoc = await _context.JobAttachments.SingleOrDefaultAsync(x => x.AttachmentId == document.AttachmentId);                
                if(findDoc.Name != document.Name)
                {
                    //rename it in the blob -- this actually creates a new one and deletes the old 
                    //TODO: make sure the new name is a valid filename and extension, show error if it is not
                    var response = await BlobStorage.RenameBlobAsync(findDoc.JobNumber.ToLower(), findDoc.Name, document.Name);
                    document.Name = response.FileName;
                    document.Path = response.Path;
                }
                findDoc.UpdatedDateTime = DateTime.Now;
                findDoc.UpdatedBy = User.Identity.Name.Split('@')[0];
                findDoc.DoctypeId = document.DoctypeId;
                findDoc.Name = document.Name;
                findDoc.Path = document.Path;
                findDoc.IsActive = document.IsActive;
                _context.JobAttachments.Update(findDoc);
                await _context.SaveChangesAsync();

                //TODO: update any metadata in blob storage
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<JobAttachmentDto>() { IsSuccess = false, Message = "failed to update job document", Value = null });
            }
            return Ok(new ResponseModel<JobAttachmentDto>() { Value = document, IsSuccess = true });
        }

        [HttpPut]
        public async Task<IActionResult> DeleteJobDocumentAsync([FromBody] JobAttachmentDto document)
        {
            
            try
            {//TODO: mark inactive in storage
                var findDoc = await _context.JobAttachments.SingleOrDefaultAsync(x => x.AttachmentId == document.AttachmentId );
                await BlobStorage.MoveFileToInactiveFolderBlobAsync(findDoc.JobNumber.ToLower(), findDoc.Name);
                _context.JobAttachments.Remove(findDoc);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<JobAttachmentDto>() { IsSuccess = false, Message = "failed to delete job document", Value = null });
            }
            return Ok(new ResponseModel<JobAttachmentDto>() { Value = document, IsSuccess = true, Message = "Document deleted successfully" });
        }

        [HttpPut]
        public async Task<IActionResult> DeleteJobDocumentsAsync([FromBody] List<JobAttachmentDto> documents)
        {

            try
            {
                var updateBy = User.Identity.Name.Split('@')[0];
                var findDocs =  _context.JobAttachments.Where(x => documents.Select(y => x.AttachmentId).Contains(x.AttachmentId));                
                await findDocs.ExecuteUpdateAsync(s => s.SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedDateTime, DateTime.Now));
                //TODO: mark inactive in storage
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobAttachmentDto>>() { IsSuccess = false, Message = "failed to delete job documents", Value = null });
            }
            return Ok(new ResponseModel<List<JobAttachmentDto>>() { Value = documents, IsSuccess = true });
        }

        [HttpPost]
        public async Task<IActionResult> DownloadDocument([FromBody] int documentId)
        {
            try
            {
                var getDocDetails = _context.JobAttachments.SingleOrDefault(x => x.AttachmentId == documentId);
                var docDownload = BlobStorage.DownloadFileBytes(getDocDetails.Name, getDocDetails.JobNumber.ToLower());
                //var author = User.Identity.Name.Split('@')[0];
                //var myFile = File(_service.CreateCostsExport(costs, author), contentType, fileName);
                //var fileBytes = myFile.FileContents;
                return Ok(new ResponseModel<byte[]>() { Value = docDownload, IsSuccess = true });//returns the byte array, won't work for large files
                                     //  return File(_service.CreateCostsExport(costs), contentType, fileName);

            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<byte[]>() { IsSuccess = false, Message = "failed to download document", Value = null });
            }
            return BadRequest();
        }

        [AllowAnonymous]
        [HttpGet("{documentId}")]
        public async Task<IActionResult> GetDocument(int documentId)
        {
            //TODO: fix so it doesn't have to allow anon
            //returns the file as a file
            try
            {
                var getDocDetails = _context.JobAttachments.SingleOrDefault(x => x.AttachmentId == documentId);
                var docDownload = BlobStorage.DownloadFileBytes(getDocDetails.Name, getDocDetails.JobNumber.ToLower());
                //var author = User.Identity.Name.Split('@')[0];
                //var myFile = File(_service.CreateCostsExport(costs, author), contentType, fileName);
                //var fileBytes = myFile.FileContents;
                // return Ok(new ResponseModel<byte[]>() { Value = docDownload, IsSuccess = true });//returns the byte array, won't work for large files
                var provider = new FileExtensionContentTypeProvider();
                string DefaultContentType = "application/octet-stream";

                if (!provider.TryGetContentType(getDocDetails.Name, out string contentType))
                {
                    contentType = DefaultContentType;
                }
                return File(docDownload, contentType);

            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<byte[]>() { IsSuccess = false, Message = "failed to download document", Value = null });
            }
        }
    }
}
