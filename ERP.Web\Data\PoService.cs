﻿using Azure.Core;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.ExtendedProperties;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Models;
using ERP.Web.Pages;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using NLog;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using Telerik.Blazor.Components.FileSelect;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class PoService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public PoService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }
        
        public async Task<ResponseModel<List<JccategoryDto>>> GetJccategoriesAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getjccategories");
                var responseString = await response.Content.ReadAsStringAsync();
                var categories = JsonConvert.DeserializeObject<ResponseModel<List<JccategoryDto>>>(responseString);
                return categories;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<JccategoryDto>>() { IsSuccess = false, Value = new List<JccategoryDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<JccostcodeDto>>> GetJcCostCodesAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getjccostcodes");
                var responseString = await response.Content.ReadAsStringAsync();
                var pactivities = JsonConvert.DeserializeObject<ResponseModel<List<JccostcodeDto>>>(responseString);
                return pactivities;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<JccostcodeDto>>() { IsSuccess = false, Value = new List<JccostcodeDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<MasterItemDto>>> GetDefaultMasterItemsAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getdefaultmasteritems");
                var responseString = await response.Content.ReadAsStringAsync();
                var masterItems = JsonConvert.DeserializeObject<ResponseModel<List<MasterItemDto>>>(responseString);
                return masterItems;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<MasterItemDto>>() { IsSuccess = false, Value = new List<MasterItemDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<SactivityDto>>> GetSactivitiesAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getsactivities");
                var responseString = await response.Content.ReadAsStringAsync();
                var pactivities = JsonConvert.DeserializeObject<ResponseModel<List<SactivityDto>>>(responseString);
                return pactivities;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<SactivityDto>>() { IsSuccess = false, Value = new List<SactivityDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<TradeDto>>> GetTradesAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/gettrades");
                var responseString = await response.Content.ReadAsStringAsync();
                var trades = JsonConvert.DeserializeObject<ResponseModel<List<TradeDto>>>(responseString);
                return trades;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<TradeDto>>() { IsSuccess = false, Value = new List<TradeDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<SupplierDto>>> GetSuppliersAsync(bool IncludeBlocked = true, bool IncludeInactive = false)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getsuppliers/{IncludeBlocked}/{IncludeInactive}");
                var responseString = await response.Content.ReadAsStringAsync();
                var suppliers = JsonConvert.DeserializeObject<ResponseModel<List<SupplierDto>>>(responseString);
                return suppliers;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<SupplierDto>>() { IsSuccess = false, Value = new List<SupplierDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<PactivityDto>>> GetPactivitiesAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getpactivities");
                var responseString = await response.Content.ReadAsStringAsync();
                var pactivities = JsonConvert.DeserializeObject<ResponseModel<List<PactivityDto>>>(responseString);
                return pactivities;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PactivityDto>>() { IsSuccess = false, Value = new List<PactivityDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<PactivityDto>>> GetPactivitiesBySubdivision(int subdivisionId)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/GetPActivitiesBySubdivision/{subdivisionId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var pactivities = JsonConvert.DeserializeObject<ResponseModel<List<PactivityDto>>>(responseString);
                return pactivities;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PactivityDto>>() { IsSuccess = false, Value = new List<PactivityDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<PactivityAreaSupplierDto>>> GetSubdivisionActivitesSupplierAsync(int activityId)
        {
           
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/ActivitySubdivisionSuppliers/{activityId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var pactivities = JsonConvert.DeserializeObject<ResponseModel<List<PactivityAreaSupplierDto>>>(responseString);
                return pactivities;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PactivityAreaSupplierDto>>() { IsSuccess = false, Value = new List<PactivityAreaSupplierDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<PactivityDto>> UpdateActivity(PactivityDto pactivity)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<PactivityDto, ResponseModel<PactivityDto>>(
                            "DownstreamApi", pactivity,
                             options => {
                                 options.RelativePath = "api/po/updatepactivity/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PactivityDto>() { IsSuccess = false, Value = new PactivityDto(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<PactivityAreaSupplierDto>> UpdateActivitySupplier(PactivityAreaSupplierDto pactivitySupplier)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<PactivityAreaSupplierDto, ResponseModel<PactivityAreaSupplierDto>>(
                            "DownstreamApi", pactivitySupplier,
                             options => {
                                 options.RelativePath = "api/po/updatepactivitysupplier/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PactivityAreaSupplierDto>() { IsSuccess = false, Value = new PactivityAreaSupplierDto(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<PactivityAreaSupplierDto>>> AssignActivitySuppliers(List<PactivityAreaSupplierDto> pactivities)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<PactivityAreaSupplierDto>, ResponseModel<List <PactivityAreaSupplierDto>>>(
                            "DownstreamApi", pactivities,
                             options => {
                                 options.RelativePath = "api/po/AssignPactivitySuppliers/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<PactivityAreaSupplierDto>>() { IsSuccess = false, Value = new List<PactivityAreaSupplierDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<CombinedPOBudgetTreeModel>>> GetPosForJobAsync(string jobNumber)
        {
            
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/po/GetPOTreeByJob/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();

                var poHeaders = JsonConvert.DeserializeObject<ResponseModel<List<CombinedPOBudgetTreeModel>>>(responseString);
                return poHeaders;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<CombinedPOBudgetTreeModel>>() { IsSuccess = false, Value = new List<CombinedPOBudgetTreeModel>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<PoheaderDto>>> GetPoHeadersForJobAsync(string jobNumber, DateTime startDateTime = default, DateTime endDateTime = default)
        {

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/po/GetPOHeadersByJob/{jobNumber}/{startDateTime.ToString("MM-dd-yyyy")}/{endDateTime.ToString("MM-dd-yyyy")}");
                var responseString = await response.Content.ReadAsStringAsync();

                var poHeaders = JsonConvert.DeserializeObject<ResponseModel<List<PoheaderDto>>>(responseString);
                return poHeaders;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PoheaderDto>>() { IsSuccess = false, Value = new List<PoheaderDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<PoheaderDto>>> GetPoHeadersForSupplierAsync(int supplierNumber, DateTime startDateTime = default, DateTime endDateTime = default)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/po/GetPOHeadersBySupplier/{supplierNumber}/{startDateTime.ToString("MM-dd-yyyy")}/{endDateTime.ToString("MM-dd-yyyy")}");
                var responseString = await response.Content.ReadAsStringAsync();

                var poHeaders = JsonConvert.DeserializeObject<ResponseModel<List<PoheaderDto>>>(responseString);
                return poHeaders;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PoheaderDto>>() { IsSuccess = false, Value = new List<PoheaderDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<PoheaderDto>>> GetPoHeadersForPONumberAsync(string poNumber)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/po/GetPOHeadersByPONumber/{poNumber}");
                var responseString = await response.Content.ReadAsStringAsync();

                var poHeaders = JsonConvert.DeserializeObject<ResponseModel<List<PoheaderDto>>>(responseString);
                return poHeaders;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PoheaderDto>>() { IsSuccess = false, Value = new List<PoheaderDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<PoheaderDto>>> GetPoHeadersAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/po/GetPOHeaders/");
                var responseString = await response.Content.ReadAsStringAsync();

                var poHeaders = JsonConvert.DeserializeObject<ResponseModel<List<PoheaderDto>>>(responseString);
                return poHeaders;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PoheaderDto>>() { IsSuccess = false, Value = new List<PoheaderDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<PoheaderDto>>> GetAllPoHeadersAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = "api/po/GetPOHeaders/");
                var responseString = await response.Content.ReadAsStringAsync();

                var poHeaders = JsonConvert.DeserializeObject<ResponseModel<List<PoheaderDto>>>(responseString);
                return poHeaders;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PoheaderDto>>() { IsSuccess = false, Value = new List<PoheaderDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<PoheaderDto>>> GetPONeedApprovalByJobAsync(string jobNumber)
        {           
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/po/GetPONeedApprovalByJob/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();

                var poHeaders = JsonConvert.DeserializeObject<ResponseModel<List<PoheaderDto>>>(responseString);
                return poHeaders; 
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PoheaderDto>>() { IsSuccess = false, Value = new List<PoheaderDto>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<PoheaderDto>>> GetPOApprovalByJobAsync(string jobNumber)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/po/GetPOApprovalByJob/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();

                var poHeaders = JsonConvert.DeserializeObject<ResponseModel<List<PoheaderDto>>>(responseString);
                return poHeaders;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PoheaderDto>>() { IsSuccess = false, Value = new List<PoheaderDto>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<PoapprovalDto>>> GetPoApprovalByPoHeaderIdAsync(int poHeaderId)
        {
            var poApprovals = new ResponseModel<List<PoapprovalDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getPoApprovalByPoHeaderId/{poHeaderId}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    poApprovals = JsonConvert.DeserializeObject<ResponseModel<List<PoapprovalDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return poApprovals;
        }

        public async Task<ResponseModel<PoheaderDto>> ApprovePo(PoheaderDto poheader)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<PoheaderDto, ResponseModel<PoheaderDto>>(
                            "DownstreamApi", poheader,
                             options => {
                                 options.RelativePath = "api/po/approvepo/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PoheaderDto>() { IsSuccess = false, Value = new PoheaderDto(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<PoheaderDto>> UnApprovePo(PoheaderDto poheader)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<PoheaderDto, ResponseModel<PoheaderDto>>(
                            "DownstreamApi", poheader,
                             options => {
                                 options.RelativePath = "api/po/unapprovepo/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PoheaderDto>() { IsSuccess = false, Value = new PoheaderDto(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<CombinedPOBudgetTreeModel>>> IssuePos(List<EstdetailDto> estdetails)
        {
            try
            {
                var issueDetails = estdetails.Select(x => new EstdetailDto() { EstdetailId = x.EstdetailId}).ToList();
                var response = await _downstreamAPI.PostForUserAsync<List<EstdetailDto>, ResponseModel<List<CombinedPOBudgetTreeModel>>>(
                            "DownstreamApi", issueDetails,
                             options => {
                                 options.RelativePath = "api/po/issuePos/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<CombinedPOBudgetTreeModel>>() { IsSuccess = false, Value = new List<CombinedPOBudgetTreeModel>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<PoEmailModel>> EmailPos(PoEmailModel model)
        {
            try
            {
                model.PoheaderId =  model.Poheader.PoheaderId;
                model.Poheader = null;
                var response = await _downstreamAPI.PostForUserAsync<PoEmailModel, ResponseModel<PoEmailModel>>(
                           "DownstreamApi", model,
                            options => {
                                options.RelativePath = "api/po/emailPo/";
                            });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PoEmailModel>() { IsSuccess = false, Value = new PoEmailModel(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<PoEmailModel>> UploadPoAsync(PoheaderDto poHeader, byte[] fileData)
        {
            try
            {
                var files = new List<FileModel>();
                files.Add(new FileModel()
                {
                    FileData = fileData,
                    FileName = "testPO.pdf"
                });

                var PoEmailModel = new PoEmailModel()
                {
                    Files = files,
                    Poheader = poHeader,
                };
                var response = await _downstreamAPI.PostForUserAsync<PoEmailModel, ResponseModel<PoEmailModel>>(
                           "DownstreamApi", PoEmailModel,
                            options => {
                                options.RelativePath = "api/po/uploadPoDocument/";
                            });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PoEmailModel>() { IsSuccess = false, Value = new PoEmailModel(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<PoheaderDto>> CancelPo(PoheaderDto poHeader)
        {
            try
            {   
                var response = await _downstreamAPI.PostForUserAsync<int, ResponseModel<PoheaderDto>>(
                           "DownstreamApi", poHeader.PoheaderId,
                            options => {
                                options.RelativePath = "api/po/CancelPo/";
                            });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PoheaderDto>() { IsSuccess = false, Value = new PoheaderDto(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<PoheaderDto>> DeletePo(PoheaderDto poHeader)
        {
            try
            {
                var poHeaderId = poHeader.PoheaderId;
                var response = await _downstreamAPI.PostForUserAsync<int, ResponseModel<PoheaderDto>>(
                           "DownstreamApi", poHeaderId,
                            options => {
                                options.RelativePath = "api/po/deletePo/";                               
                            });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PoheaderDto>() { IsSuccess = false, Value = new PoheaderDto(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<CombinedJCETreeModel>>> GetInvoiceSendToAccountingAsync()
        {

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/po/GetInvoiceSendToAccounting/");
                var responseString = await response.Content.ReadAsStringAsync();

                return JsonConvert.DeserializeObject<ResponseModel<List<CombinedJCETreeModel>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<CombinedJCETreeModel>>() { IsSuccess = false, Value = new List<CombinedJCETreeModel>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<CombinedJCETreeModel>>> GetInvoiceSendToAccountingSupplierSortAsync()
        {

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/po/GetInvoiceSendToAccountingSupplierSort/");
                var responseString = await response.Content.ReadAsStringAsync();

                return JsonConvert.DeserializeObject<ResponseModel<List<CombinedJCETreeModel>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<CombinedJCETreeModel>>() { IsSuccess = false, Value = new List<CombinedJCETreeModel>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<CombinedJCETreeModel>>> GetSendToAccountingAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/po/GetSendToAccounting/");
                var responseString = await response.Content.ReadAsStringAsync();

                var data = JsonConvert.DeserializeObject<ResponseModel<List<CombinedJCETreeModel>>>(responseString);
                return data;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<CombinedJCETreeModel>>() { IsSuccess = false, Value = new List<CombinedJCETreeModel>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<CombinedJCETreeModel>>> GetCancelledPOSendToAccountingAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/po/GetCancelledPOSendToAccounting/");
                var responseString = await response.Content.ReadAsStringAsync();

                var data = JsonConvert.DeserializeObject<ResponseModel<List<CombinedJCETreeModel>>>(responseString);
                return data;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<CombinedJCETreeModel>>() { IsSuccess = false, Value = new List<CombinedJCETreeModel>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<CombinedJCETreeModel>>> GetApprovedPOSendToAccountingAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/po/GetApprovedPOSendToAccounting/");
                var responseString = await response.Content.ReadAsStringAsync();

                var data = JsonConvert.DeserializeObject<ResponseModel<List<CombinedJCETreeModel>>>(responseString);
                return data;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<CombinedJCETreeModel>>() { IsSuccess = false, Value = new List<CombinedJCETreeModel>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<CombinedJCETreeModel>>> GetPOSendToAccountingSupplierSortAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/po/GetSendToAccountingSupplierSort/");
                var responseString = await response.Content.ReadAsStringAsync();

                var data = JsonConvert.DeserializeObject<ResponseModel<List<CombinedJCETreeModel>>>(responseString);
                return data;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<CombinedJCETreeModel>>() { IsSuccess = false, Value = new List<CombinedJCETreeModel>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<PoheaderDto>> SendPoHeaderToBCAsync(PoheaderDto poheader)
        {
            try
            {

                var response = await _downstreamAPI.PostForUserAsync<PoheaderDto, ResponseModel<PoheaderDto>> (
                           "DownstreamApi", poheader,
                            options => {
                                options.RelativePath = "api/po/SendPoHeaderToBC/";
                            });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            //TODO: postforuser throws if response is not success, it does not return the response
            return new ResponseModel<PoheaderDto>() { IsSuccess = false, Value = null, Message = "Failed to send to BC" };
        }
        public async Task<ResponseModel<PoheaderDto>> SendCancelledPoHeaderToBCAsync(PoheaderDto poheader)
        {
            try
            {
                var sendHeader = new PoheaderDto() { PoheaderId = poheader.PoheaderId, Pojobnumber = poheader.Pojobnumber, Podescription = poheader.Podescription };
                var response = await _downstreamAPI.PostForUserAsync<PoheaderDto, ResponseModel<PoheaderDto>>(
                           "DownstreamApi", sendHeader,
                            options => {
                                options.RelativePath = "api/po/SendCancelledPoHeaderToBC/";
                            });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            //TODO: postforuser throws if response is not success, it does not return the response
            return new ResponseModel<PoheaderDto>() { IsSuccess = false, Value = null, Message = "Failed to send to BC" };
        }
        public async Task<ResponseModel<PoheaderDto>> SendApprovedPoHeaderToBCAsync(PoheaderDto poheader)
        {
            try
            {
                var sendHeader = new PoheaderDto() { PoheaderId = poheader.PoheaderId, Pojobnumber = poheader.Pojobnumber, Podescription = poheader.Podescription };
                var response = await _downstreamAPI.PostForUserAsync<PoheaderDto, ResponseModel<PoheaderDto>>(
                           "DownstreamApi", sendHeader,
                            options => {
                                options.RelativePath = "api/po/SendApprovedPoHeaderToBC/";
                            });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            //TODO: postforuser throws if response is not success, it does not return the response
            return new ResponseModel<PoheaderDto>() { IsSuccess = false, Value = null, Message = "Failed to send to BC" };
        }
        public async Task<ResponseModel<PoheaderDto>> BackfillBCIDInPOHeadersync()
        {
            try
            {
                var response = await _downstreamAPI.GetForUserAsync<ResponseModel<PoheaderDto>>(
                           "DownstreamApi", 
                            options => {
                                options.RelativePath = "api/po/BackfillBCIdsToERP/";
                            });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            //TODO: postforuser throws if response is not success, it does not return the response
            return new ResponseModel<PoheaderDto>() { IsSuccess = false, Value = null, Message = "Failed to send to BC" };
        }

        public async Task<ResponseModel<POAndBillableLinesFilesModel>> SendPoHeaderToNAVAsync(PoheaderDto poheader)
        {
            try
            {

                var response = await _downstreamAPI.PostForUserAsync<PoheaderDto, ResponseModel<POAndBillableLinesFilesModel>>(
                           "DownstreamApi", poheader,
                            options => {
                                options.RelativePath = "api/po/SendPoHeaderToNAV/";
                            });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            //TODO: postforuser throws if response is not success, it does not return the response
            return new ResponseModel<POAndBillableLinesFilesModel>() { IsSuccess = false, Value = null, Message = "Failed to send to BC" };
        }

        public async Task<ResponseModel<List<PoheaderDto>>> SendPoHeadersToBCAsync(List<PoheaderDto> poheaders)
        {
            try
            {
                var headerIds = poheaders.Select(x =>x.PoheaderId).ToList();
                var response = await _downstreamAPI.PostForUserAsync<List<int>, ResponseModel<List<PoheaderDto>>>(
                           "DownstreamApi", headerIds,
                            options => {
                                options.RelativePath = "api/po/SendPoHeadersToBC/";
                            });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PoheaderDto>>() { IsSuccess = false, Value = null, Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<PoheaderDto>>> SendApprovedPoHeadersToBCAsync(List<PoapprovalDto> poapprovals)
        {
            try
            {
                var approvalIds = poapprovals.Select(x => x.PoapprovalId).ToList();
                var response = await _downstreamAPI.PostForUserAsync<List<int>, ResponseModel<List<PoheaderDto>>>(
                       "DownstreamApi", approvalIds,
                        options => {
                            options.RelativePath = "api/po/SendApprovedPoHeadersToBC/";
                        });

                //TODO: fix message and response
                return new ResponseModel<List<PoheaderDto>>() { Value = null, IsSuccess = true, Message = "Success. " };

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PoheaderDto>>() { IsSuccess = false, Value = null, Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<PoheaderDto>>> SendApprovedPoHeadersToBCBatchAsync(List<PoapprovalDto> poapprovals)
        {
            try
            {
                if (poapprovals.Count == 0)
                {
                    return new ResponseModel<List<PoheaderDto>>() { IsSuccess = true, Value = null, Message = "No POs selected" };
                }
                //Try sending in batches of 500 because else request times out
                int count = 0;
                var approvalIds = poapprovals.Select(x => x.PoapprovalId).ToList();
                var tasksToSend = new List<Task<ResponseModel<List<PoheaderDto>>>>();
                while (count < approvalIds.Count)
                {
                    var approvalsToSend = approvalIds.Skip(count).Take(500).ToList();
                    var request =  _downstreamAPI.PostForUserAsync<List<int>, ResponseModel<List<PoheaderDto>>>(
                           "DownstreamApi", approvalsToSend,
                            options => {
                                options.RelativePath = "api/po/SendApprovedPoHeadersToBC/";
                            });
                    tasksToSend.Add(request);
                    count += 500;

                }
                await Task.WhenAll(tasksToSend);
                string message = tasksToSend.Any(x => x.Result.IsSuccess == false) ? "Some POs failed to send to BC. See logs for details." : "Sent POs to BC. See logs for details.";

                return new ResponseModel<List<PoheaderDto>>() { Value = null, IsSuccess = true, Message = message };

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PoheaderDto>>() { IsSuccess = false, Value = null, Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<PoheaderDto>>> SendApprovedCreditMemosToBCAsync(List<PoapprovalDto> poapprovals)
        {
            try
            {
                if(poapprovals.Count == 0)
                {
                    return new ResponseModel<List<PoheaderDto>>() { IsSuccess = true, Value = null, Message = "No credit memos selected" };
                }
                var approvalIds = poapprovals.Select(x => x.PoapprovalId).ToList();
                var response = await _downstreamAPI.PostForUserAsync<List<int>, ResponseModel<List<PoheaderDto>>>(
                           "DownstreamApi", approvalIds,
                            options => {
                                options.RelativePath = "api/po/SendApprovedCreditMemosToBC/";
                            });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PoheaderDto>>() { IsSuccess = false, Value = null, Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<PoheaderDto>>> SendCancelledPoHeadersToBCAsync(List<PoheaderDto> poheaders)
        {
            try
            {
                var headerIds = poheaders.Select(x => x.PoheaderId).ToList();
                var response = await _downstreamAPI.PostForUserAsync<List<int>, ResponseModel<List<PoheaderDto>>>(
                           "DownstreamApi", headerIds,
                            options => {
                                options.RelativePath = "api/po/SendCancelledPoHeadersToBC/";
                            });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PoheaderDto>>() { IsSuccess = false, Value = null, Message = "Something went wrong" };
        }
        public async Task<ResponseModel<POAndBillableLinesFilesModel>> SendPoHeadersToNAVAsync(List<PoheaderDto> poheaders)
        {
            try
            {
                var headerIds = poheaders.Select(x => x.PoheaderId).ToList();
                var response = await _downstreamAPI.PostForUserAsync<List<int>, ResponseModel<POAndBillableLinesFilesModel>>(
                           "DownstreamApi", headerIds,
                            options => {
                                options.RelativePath = "api/po/SendPoHeadersToNAV/";
                            });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<POAndBillableLinesFilesModel>() { IsSuccess = false, Value = null, Message = "Something went wrong" };
        }
        public async Task<ResponseModel<PoapprovalDto>> SendPOInvoiceToBCAsync(PoapprovalDto poheader)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<PoapprovalDto, ResponseModel<PoapprovalDto>>(
                            "DownstreamApi", poheader,
                             options => {
                                 options.RelativePath = "api/po/SendInvoiceToBC/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PoapprovalDto>() { IsSuccess = false, Value = new PoapprovalDto(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<byte[]>> SendPOInvoiceToNAVAsync(PoapprovalDto poheader)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<PoapprovalDto, ResponseModel<byte[]>>(
                            "DownstreamApi", poheader,
                             options => {
                                 options.RelativePath = "api/po/SendInvoiceToNAV/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<byte[]>() { IsSuccess = false, Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<PoapprovalDto>>> SendPOInvoicesToBCAsync(List<PoapprovalDto> invoices)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<PoapprovalDto>, ResponseModel<List<PoapprovalDto>>>(
                            "DownstreamApi", invoices,
                             options => {
                                 options.RelativePath = "api/po/SendInvoicesToBC/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<PoapprovalDto>>() { IsSuccess = false, Value = new List<PoapprovalDto>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<byte[]>> SendPOInvoicesToNAVAsync(List<PoapprovalDto> invoices)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<PoapprovalDto>, ResponseModel<byte[]>>(
                            "DownstreamApi", invoices,
                             options => {
                                 options.RelativePath = "api/po/SendInvoicesToNAV/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<byte[]>() { IsSuccess = false,  Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<PodetailDto>>> UpdatePoDetailsExportedAsync(List<PodetailDto> podetailsToUpdate)
        {
            try
            {
                foreach(var detail in podetailsToUpdate)
                {
                    detail.Poheader = null;//to get around validation on poheader for now
                }
                var response = await _downstreamAPI.PutForUserAsync<List<PodetailDto>, ResponseModel<List<PodetailDto>>>(
                            "DownstreamApi", podetailsToUpdate,
                             options => {
                                 options.RelativePath = "api/po/markpodetailexported/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<PodetailDto>>() { Value = podetailsToUpdate, IsSuccess = false };
        }
        public async Task<ResponseModel<List<PoapprovalDto>>> UpdatePoApprovalExportedAsync(List<PoapprovalDto> poapprovalsToUpdate)
        {
            try
            {
                foreach (var approval in poapprovalsToUpdate)
                {
                    approval.Poheader = null;//to get around validation on poheader for now
                }
                var response = await _downstreamAPI.PutForUserAsync<List<PoapprovalDto>, ResponseModel<List<PoapprovalDto>>>(
                            "DownstreamApi", poapprovalsToUpdate,
                             options => {
                                 options.RelativePath = "api/po/markinvoiceexported/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<PoapprovalDto>>() { Value = poapprovalsToUpdate, IsSuccess = false };
        }
        public async Task<ResponseModel<PojccdetailDto>> DeleteJceDetailAsync(PojccdetailDto poJccDetail)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<PojccdetailDto, ResponseModel<PojccdetailDto>>(
                            "DownstreamApi", poJccDetail,
                             options => {
                                 options.RelativePath = "api/po/deletejccdetail/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<PojccdetailDto> { IsSuccess = false, Message = "Something went wrong", Value = null };
        }

        public async Task<ResponseModel<List<PojccdetailDto>>> DeleteJccDetailsAsync(List<PojccdetailDto> estDetailToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<PojccdetailDto>, ResponseModel<List<PojccdetailDto>>>(
                            "DownstreamApi", estDetailToUpdate,
                             options => {
                                 options.RelativePath = "api/po/deletejccdetails/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PojccdetailDto>> { IsSuccess = false, Message = "Something went wrong", Value = null  };
        }

        public async Task<ResponseModel<List<PoheaderDto>>> GetVPOs(string? userName = null)
        {
            var vpos = new ResponseModel<List<PoheaderDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = userName == null ? $"api/po/getvpos/" : $"api/po/getvpos/{userName}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    vpos = JsonConvert.DeserializeObject<ResponseModel<List<PoheaderDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return vpos;
        }

        public async Task<ResponseModel<List<PoapprovalDto>>> GetVPOApprovals(string? userName = null)
        {
            var poApprovals = new ResponseModel<List<PoapprovalDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = userName == null ? $"api/po/getvpoapprovals/" : $"api/po/getvpoapprovals/{userName}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    poApprovals = JsonConvert.DeserializeObject<ResponseModel<List<PoapprovalDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return poApprovals;
        }

        public async Task<ResponseModel<List<PodetailDto>>> GetVPODetailByPOHeaderIdAsync(int poHeaderId)
        {
            var responseModel = new ResponseModel<List<PodetailDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getvpodetailbypoheaderid/{poHeaderId}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<PodetailDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }
        public async Task<ResponseModel<PoheaderDto>> GetPoHeaderAsync(int poHeaderId)
        {
            var responseModel = new ResponseModel<PoheaderDto>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getpoheader/{poHeaderId}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<PoheaderDto>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return responseModel;
        }

        public async Task<ResponseModel<List<BlobFileDetails>>> GetBlobFilesDetailAsync(PoheaderDto poheaderDto)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<PoheaderDto, ResponseModel<List<BlobFileDetails>>>(
                            "DownstreamApi", poheaderDto,
                             options =>
                             {
                                 options.RelativePath = "api/po/GetBlobFilesDetail/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
            }

            return new ResponseModel<List<BlobFileDetails>>() { Value = null, IsSuccess = false };
        }

        public async Task<ResponseModel<PoheaderDto>> AddVPOAsync(PoheaderDto model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<PoheaderDto, ResponseModel<PoheaderDto>>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/po/addvpo/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new ResponseModel<PoheaderDto>() { Value = model, IsSuccess = false };
        }

        public async Task<ResponseModel<PodetailDto>> AddVPODetailAsync(PodetailDto model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<PodetailDto, ResponseModel<PodetailDto>>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/po/addvpodetail/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new ResponseModel<PodetailDto>() { Value = model, IsSuccess = false };
        }

        public async Task<ResponseModel<PoheaderDto>> EditVPOAsync(PoheaderDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<PoheaderDto, ResponseModel<PoheaderDto>>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/po/editvpo/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PoheaderDto>() { Value = model, IsSuccess = false };
        }

        public async Task<ResponseModel<PodetailDto>> EditVPODetailAsync(PodetailDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<PodetailDto, ResponseModel<PodetailDto>>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/po/editvpodetail/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PodetailDto>() { Value = model, IsSuccess = false };
        }

        public async Task<ResponseModel<List<JccategoryDto>>> GetVariances()
        {
            var variances = new ResponseModel<List<JccategoryDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getvariances");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    variances = JsonConvert.DeserializeObject<ResponseModel<List<JccategoryDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return variances;
        }

        public async Task<ResponseModel<List<PactivityDto>>> GetPurchasingActivities()
        {
            var pactivities = new ResponseModel<List<PactivityDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getpurchasingactivities");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    pactivities = JsonConvert.DeserializeObject<ResponseModel<List<PactivityDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return pactivities;
        }
        public async Task<ResponseModel<List<PactivityDto>>> GetPurchasingActivitiesByJob(string jobNumber)
        {
            var pactivities = new ResponseModel<List<PactivityDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getpurchasingactivitiesbyjob/{jobNumber}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    pactivities = JsonConvert.DeserializeObject<ResponseModel<List<PactivityDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return pactivities;
        }

        public async Task<ResponseModel<List<TradeDto>>> GetTradeByActivityAsync(int pactivityId)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/gettradebyactivity/{pactivityId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var trades = JsonConvert.DeserializeObject<ResponseModel<List<TradeDto>>>(responseString);
                return trades;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<TradeDto>>() { IsSuccess = false, Value = new List<TradeDto>(), Message = "Something went wrong" };

        }

        public async Task<ResponseModel<List<SupplierDto>>> GetSupplierData()
        {
            var suppliers = new ResponseModel<List<SupplierDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getsupplierdata");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    suppliers = JsonConvert.DeserializeObject<ResponseModel<List<SupplierDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return suppliers;
        }

        public async Task<ResponseModel<List<SupplierDto>>> GetContactBySupplierAsync(int supplierId)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getcontactbysupplier/{supplierId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var contacts = JsonConvert.DeserializeObject<ResponseModel<List<SupplierDto>>>(responseString);
                return contacts;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<SupplierDto>>() { IsSuccess = false, Value = new List<SupplierDto>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<PodetailDto>>> GetPOForScheduleActivityAsync(int scheduleAid)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/GetPOForScheduleActivity/{scheduleAid}");
                var responseString = await response.Content.ReadAsStringAsync();
                var po = JsonConvert.DeserializeObject<ResponseModel<List<PodetailDto>>>(responseString);
                return po;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PodetailDto>>() { IsSuccess = false, Value = null, Message = "Something went wrong" };

        }

        public async Task<ResponseModel<PactivityDto>> AddPActivityAsync(PactivityDto model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<PactivityDto, ResponseModel<PactivityDto>>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/po/addpactivity/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new ResponseModel<PactivityDto>() { IsSuccess = false, Message = "failed to add pactivity"};
        }

        public async Task<ResponseModel<PactivityDto>> EditPActivityAsync(PactivityDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<PactivityDto, ResponseModel<PactivityDto>>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/po/editpactivity/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PactivityDto>() { Message = "failed to edit activity", IsSuccess = false};
        }

        public async Task<ResponseModel<PactivityDto>> DeletePActivityAsync(PactivityDto model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<PactivityDto, ResponseModel<PactivityDto>>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/po/deletepactivity/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PactivityDto>() { Message = "failed to delete activity", IsSuccess = false };
        }

        public async Task<ResponseModel<List<BomClassDto>>> GetBOMsAsync()
        {
            var boms = new ResponseModel<List<BomClassDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getboms");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    boms = JsonConvert.DeserializeObject<ResponseModel<List<BomClassDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return boms;
        }
        public async Task<ResponseModel<List<ScheduleSactivityLinkDto>>> GetScheduleActivityLinkforEstActivityAsync(int estActivityId)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/GetScheduleActivityLinkforEstActivity/{estActivityId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var link = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleSactivityLinkDto>>>(responseString);
                return link;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<ScheduleSactivityLinkDto>>() { IsSuccess = false, Value = null, Message = "Something went wrong" };

        }
        public async Task<ResponseModel<List<ScheduleSactivityDto>>> GetScheduleActivityforEstActivityAsync(int estActivityId)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/GetScheduleActivityforEstActivity/{estActivityId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var link = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleSactivityDto>>>(responseString);
                return link;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Value = null, Message = "Something went wrong" };

        }
        public async Task<ResponseModel<List<ScheduleSactivityLinkDto>>> GetScheduleActivityLinksforEstActivitiesAsync(List<EstactivityDto> estActivities)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<int>, ResponseModel<List<ScheduleSactivityLinkDto>>>
                ("DownstreamApi", estActivities.Select(x => x.EstactivityId).ToList(), options => options.RelativePath = $"api/po/GetScheduleActivityLinksforEstActivities/");

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<ScheduleSactivityLinkDto>>() { IsSuccess = false, Value = null, Message = "Something went wrong" };

        }
        public async Task<ResponseModel<List<ScheduleSactivityDto>>> GetScheduleActivityforEstActivitiesAsync(List<EstactivityDto> estActivities)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<int>, ResponseModel<List<ScheduleSactivityDto>>>
                ("DownstreamApi", estActivities.Select(x => x.EstactivityId).ToList(), options => options.RelativePath = $"api/po/GetScheduleActivityforEstActivities/");

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Value = null, Message = "Something went wrong" };

        }
        public async Task<PoapprovalDto> ApproveVPO(PoapprovalDto model)
        {
            try
            {
                var approveModel = new PoapprovalDto()
                {
                    PoapprovalId = model.PoapprovalId,
                    PoheaderId = model.PoheaderId,
                    Invdate = model.Invdate,
                    Invdescription = model.Invdescription,
                    Invnetamount = model.Invnetamount,
                    Invnumber = model.Invnumber,
                    Notes = model.Notes,
                };
                var response = await _downstreamAPI.PostForUserAsync<PoapprovalDto, PoapprovalDto>(
                            "DownstreamApi", approveModel,
                             options =>
                             {
                                 options.RelativePath = $"api/po/approvevpo";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new PoapprovalDto();
        }

        public async Task<ResponseModel<PoapprovalDto>> RejectVPO(PoapprovalDto model)
        {
            try
            {
                var approveModel = new PoapprovalDto()
                {
                    PoapprovalId = model.PoapprovalId,
                    PoheaderId = model.PoheaderId,
                    Invdate = model.Invdate,
                    Invdescription = model.Invdescription,
                    Invnetamount = model.Invnetamount,
                    Invnumber = model.Invnumber,
                    Notes = model.Notes
                };
                var response = await _downstreamAPI.PostForUserAsync<PoapprovalDto, ResponseModel<PoapprovalDto>>(
                            "DownstreamApi", approveModel,
                             options => {
                                 options.RelativePath = "api/po/rejectvpo/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PoapprovalDto>() { IsSuccess = false, Value = new PoapprovalDto(), Message = "Something went wrong" };
        }

        public async Task<PoapprovalDto> ApproveVPOFromPowerAutomate(PoapprovalDto model, string approver = "")
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<PoapprovalDto, PoapprovalDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = $"api/po/approvevpo/{approver}";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new PoapprovalDto();
        }

        public async Task<ResponseModel<string>> StartPowerAutomateApprovalFlowAsync(int poApprovalId)
        {
            var responseModel = new ResponseModel<string>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                            "DownstreamApi", options => options.RelativePath = $"api/po/StartPowerAutomateApprovalFlow/{poApprovalId}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<string>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }
            return responseModel;
        }

        public async Task<PoapprovalDto> BulkApproveVPO(List<PoapprovalDto> models)
        {
            try
            {
                var approveModels = models.Select(x => new PoapprovalDto()
                {
                    PoapprovalId = x.PoapprovalId,
                    PoheaderId = x.PoheaderId,
                    Invdate = x.Invdate,
                    Invdescription = x.Invdescription,
                    Invnetamount = x.Invnetamount,
                    Invnumber = x.Invnumber,
                    Notes = x.Notes
                }).ToList();

                var response = await _downstreamAPI.PostForUserAsync<List<PoapprovalDto>, PoapprovalDto>(
                            "DownstreamApi", approveModels,
                             options =>
                             {
                                 options.RelativePath = "api/po/bulkapprovevpo/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new PoapprovalDto();
        }

        public async Task<ResponseModel<List<VpoGroupDto>>> GetVpoGroupsAsync()
        {
            var vpoGroups = new ResponseModel<List<VpoGroupDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getvpogroups/");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    vpoGroups = JsonConvert.DeserializeObject<ResponseModel<List<VpoGroupDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return vpoGroups;
        }
        public async Task<ResponseModel<List<VpoApprovalHierarchyDto>>> GetVpoHierarchiesAsync()
        {
            var vpoGroups = new ResponseModel<List<VpoApprovalHierarchyDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/GetVPOApprovalHierarchy/");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    vpoGroups = JsonConvert.DeserializeObject<ResponseModel<List<VpoApprovalHierarchyDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return vpoGroups;
        }
        public async Task<VpoApprovalSeqDto> AddVpoApprovalSeqAsync(VpoApprovalSeqDto model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<VpoApprovalSeqDto, VpoApprovalSeqDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/po/addvpoapprovalseq/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new VpoApprovalSeqDto();
        }
        public async Task<VpoApprovalSeqDto> DeleteVpoApprovalSeqAsync(VpoApprovalSeqDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<VpoApprovalSeqDto, VpoApprovalSeqDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/po/deletevpoapprovalseq/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new VpoApprovalSeqDto();
        }
        public async Task<VpoApprovalSeqDto> EditVpoApprovalSeqAsync(VpoApprovalSeqDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<VpoApprovalSeqDto, VpoApprovalSeqDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/po/editvpoapprovalseq/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new VpoApprovalSeqDto();
        }

        public async Task<ResponseModel<List<RoleDto>>> GetAllRolesAsync()
        {
            var roles = new ResponseModel<List<RoleDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getallroles/");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    roles = JsonConvert.DeserializeObject<ResponseModel<List<RoleDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return roles;
        }

        public async Task<ResponseModel<string>> RestartPowerAutomateAsync(int poApprovalId)
        {
            var responseModel = new ResponseModel<string>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                            "DownstreamApi", options => options.RelativePath = $"api/po/callpowerautomate/{poApprovalId}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<string>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }
            return responseModel;
        }
        public async Task<ResponseModel<List<JobTaskDto>>> GetJobTasksAsync()
        {
            var jobTasks = new ResponseModel<List<JobTaskDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/jobtasks/");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    jobTasks = JsonConvert.DeserializeObject<ResponseModel<List<JobTaskDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return jobTasks;
        }
        public async Task<ResponseModel<List<JobTaskDto>>> GetJobTasksByJobAsync(string jobNumber)
        {
            var jobTasks = new ResponseModel<List<JobTaskDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/jobtasks/{jobNumber}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    jobTasks = JsonConvert.DeserializeObject<ResponseModel<List<JobTaskDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return jobTasks;
        }

        public async Task<ResponseModel<TileDto>> GetPendingVPO()
        {
            var poApprovals = new ResponseModel<TileDto>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/po/getpendingvpo/");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    poApprovals = JsonConvert.DeserializeObject<ResponseModel<TileDto>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return poApprovals;
        }

        public async Task<PoapprovalDto> ApproveVPOForPowerAutomate(int poApprovalId, int poHeaderId, string approver = "")
        {
            try
            {
                var model = new PoapprovalDto
                {
                    PoapprovalId = poApprovalId,
                    PoheaderId = poHeaderId
                };

                var response = await _downstreamAPI.PostForUserAsync<PoapprovalDto, PoapprovalDto>(
                            "DownstreamApi",
                            model,
                             options =>
                             {
                                 options.RelativePath = $"api/po/approvevpoforpowerautomate/{poApprovalId}/{poHeaderId}/{approver}";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new PoapprovalDto();
        }

        public async Task<ResponseModel<PoapprovalDto>> RejectVPOForPowerAutomate(int poApprovalId, int poHeaderId, string approver = "")
        {
            try
            {
                var model = new PoapprovalDto
                {
                    PoapprovalId = poApprovalId,
                    PoheaderId = poHeaderId
                };

                var response = await _downstreamAPI.PostForUserAsync<PoapprovalDto, ResponseModel<PoapprovalDto>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = $"api/po/rejectvpoforpowerautomate/{poApprovalId}/{poHeaderId}/{approver}";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<PoapprovalDto>() { IsSuccess = false, Value = new PoapprovalDto(), Message = "Something went wrong" };
        }
    }
}
