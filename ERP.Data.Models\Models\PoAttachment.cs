﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class PoAttachment
{
    public int PoattachmentId { get; set; }

    public int PoheaderId { get; set; }

    public string? Path { get; set; }

    public string? Name { get; set; }

    public int? DoctypeId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual Doctype? Doctype { get; set; }

    public virtual Poheader Poheader { get; set; } = null!;
}
