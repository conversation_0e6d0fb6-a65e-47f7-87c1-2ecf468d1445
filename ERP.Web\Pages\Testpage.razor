﻿@page "/testpage"

@using ERP.Data.Models
@using System.Collections.ObjectModel
@using Telerik.DataSource.Extensions

<PageTitle>TEST</PageTitle>

<style>
    /* all these styles are not relevant to the 2 alternative solutions */
    .box {
        display: inline-block;
        width: 45vw;
        margin: 1em;
    }
    .empty-grid .k-grid-content {
        background: #fdd;
    }
    .empty-grid .k-grid-norecords {
        background: #cfc;
    }
</style>
<div class="box">
    <p>Source Grid</p>

    <TelerikGrid Data="@MyGridData"
                    @ref="@SourceGrid"
                    RowDraggable="true"
                    OnRowDrop="@((GridRowDropEventArgs<SampleData> args) => OnGrid1RowDropHandler(args))">
        <GridSettings>
            <GridRowDraggableSettings DragClueField="@nameof(SampleData.Name)"></GridRowDraggableSettings>
        </GridSettings>
        <GridColumns>
            <GridColumn Field="@(nameof(SampleData.Id))" Width="120px" />
            <GridColumn Field="@(nameof(SampleData.Name))" Title="Name" Groupable="false" />
        </GridColumns>
    </TelerikGrid>
</div>

<div class="box">
    <p>Empty destination Grid with no Height</p>

    <TelerikGrid RowDraggable="true" Data="@MyEmptyData">
        <GridColumns>
            <GridColumn Field="@(nameof(SampleData.Id))" Width="120px" />
            <GridColumn Field="@(nameof(SampleData.Name))" Title="Name" Groupable="false" />
        </GridColumns>
    </TelerikGrid>
</div>

<div class="box">
    <p>Empty destination Grid with large Height and small NoDataTemplate</p>

    <TelerikGrid RowDraggable="true" Data="@MyEmptyData" Height="200px" Class="empty-grid">
        <GridColumns>
            <GridColumn Field="@(nameof(SampleData.Id))" Width="120px" />
            <GridColumn Field="@(nameof(SampleData.Name))" Title="Name" Groupable="false" />
        </GridColumns>
    </TelerikGrid>
</div>

<div class="box">
    <p>Empty destination Grid with larger NoDataTemplate</p>

    <style>
        /* how to expand the default NoDataTemplate */
        .my-grid-class .k-grid-norecords {
            height: 160px; /* Grid Height - 40px */
        }
    </style>

    <TelerikGrid RowDraggable="true" Data="@MyEmptyData" Height="200px" Class="my-grid-class">
        <GridColumns>
            <GridColumn Field="@(nameof(SampleData.Id))" Width="120px" />
            <GridColumn Field="@(nameof(SampleData.Name))" Title="Employee Name" Groupable="false" />
        </GridColumns>
    </TelerikGrid>
</div>



@code {

     TelerikGrid<SampleData> SourceGrid { get; set; }

    private void OnGrid1RowDropHandler(GridRowDropEventArgs<SampleData> args)
    {
        MyGridData.Remove(args.Item);
        InsertItem(args);
    }

    private void InsertItem(GridRowDropEventArgs<SampleData> args)
    {
        var destinationData = MyEmptyData;

        var destinationIndex = 0;

        if (args.DestinationItem != null)
        {
            destinationIndex = destinationData.IndexOf(args.DestinationItem);
            if (args.DropPosition == GridRowDropPosition.After)
            {
                destinationIndex += 1;
            }
        }

        destinationData.InsertRange(destinationIndex, args.Items);
    }

    public List<SampleData> MyGridData = Enumerable.Range(1, 3).Select(x => new SampleData
    {
        Id = x,
        Name = "Name  " + x
    }).ToList();

    public List<SampleData> MyEmptyData = new();

    public class SampleData
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
}

