﻿using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using NLog;
using System.Diagnostics;
using System.Text;
using Telerik.Blazor.Components.FileSelect;
using Microsoft.Identity.Abstractions;
using ERP.Data.Models.ExtensionMethods;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Components.Authorization;
using DocumentFormat.OpenXml.EMMA;
using Telerik.Windows.Documents.Spreadsheet.Expressions.Functions;
using ERP.Web.Pages;

namespace ERP.Web.Data
{
    public class ScheduleService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public ScheduleService(IConfiguration configuration, HttpClient httpClient, IDownstream<PERSON>pi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<ResponseModel<List<ScheduleDto>>> GetSchedulesAsync(bool openOnly = false)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/schedules/{openOnly}");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleDto>>() { IsSuccess = false, Message = "Failed to get schedules." };
        }

        public async Task<ResponseModel<List<ScheduleDto>>> GetSchedulesByContactAsync(string userId, bool openOnly = false)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/schedulesbycontact/{userId}/{openOnly}");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleDto>>() { IsSuccess = false, Message = "Failed to get schedules." };
        }

        public async Task<ResponseModel<List<ScheduleSactivityDto>>> GetScheduleActivitysForSupplierAsync(int subNumber)
        {


            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/ScheduleActivityForSupplier/{subNumber}");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleSactivityDto>>>(responseString);
                    return result;
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Message = "Failed to get schedules." };
        }

        public async Task<ResponseModel<List<ScheduleSactivityDto>>> GetOpenScheduleActivitiesForTradeSupplierAsync(int tradeId, int subdivisionId)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                "DownstreamApi",
               options => options.RelativePath = $"api/schedule/OpenScheduleActivitiesForTradeSubdivision/{tradeId}/{subdivisionId}/");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleSactivityDto>>>(responseString);
                    return result;
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Message = "Failed to get schedule activities for trade subdivision." };
        }

        public async Task<ResponseModel<List<ScheduleSactivityDto>>> GetScheduleActivitysForConstructionSuperAsync(string userName)
        {


            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/ScheduleActivityForSuper/{userName}");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleSactivityDto>>>(responseString);
                    return result;
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Message = "Failed to get schedules." };
        }

        public async Task<ResponseModel<List<ScheduleSactivityDto>>> GetScheduleActivitysForConstructionSuperByJobNumberAsync(string userName, string jobNumber)
        {


            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/ScheduleActivityForSuperByJobNumber/{userName}/{jobNumber}");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleSactivityDto>>>(responseString);
                    return result;
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Message = "Failed to get schedules." };
        }

        public async Task<ResponseModel<ScheduleDto>> GetPreScheduleAsync(string jobNumber)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/preschedule/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<ScheduleDto>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ScheduleDto>() { IsSuccess = false, Message = "Could not get PreSchedule. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<ScheduleDto>>> UpdatePrescheduleAsync(ScheduleDto updateSchedule)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<ScheduleDto, ResponseModel<List<ScheduleDto>>>(
                            "DownstreamApi", updateSchedule,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/UpdatePreschedule/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleDto>>() { IsSuccess = false, Message = "Could not update Schedule. If issue persists please contact BI." };
        }
        public async Task<ResponseModel<List<ScheduleDto>>> UpdatePrescheduleStickAsync(ScheduleDto updateSchedule)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<ScheduleDto, ResponseModel<List<ScheduleDto>>>(
                            "DownstreamApi", updateSchedule,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/UpdatePrescheduleStick/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleDto>>() { IsSuccess = false, Message = "Could not update Schedule. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<bool>> CheckHolidayAsync(DateTime checkDate)
        {
            try
            {
                var dateString = checkDate.ToString("o");
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/CheckHoliday/{dateString}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<bool>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<bool>() { IsSuccess = false, Value = false, Message = "Could not check holiday. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<CalendarsDayDto>>> GetHolidaysAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/GetHolidays");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<CalendarsDayDto>>>(responseString);
                    return result;
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<CalendarsDayDto>>() { IsSuccess = false, Message = "Failed to get holidays", Value = null };
        }

        public async Task<ResponseModel<ScheduleDto>> CalculateScheduleEndFromStartAsync(ScheduleDto updateSchedule)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<ScheduleDto, ResponseModel<ScheduleDto>>(
                            "DownstreamApi", updateSchedule,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/CalculateScheduleBasedOnStartDate/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ScheduleDto>() { IsSuccess = false, Value = updateSchedule, Message = "Could not update PreSchedule Start Date. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<ScheduleDto>>> GetSchedulesForTemplateAsync(int templateId)
        {
            var schedules = new List<ScheduleDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/schedulesfortemplate/{templateId}");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleDto>>() { Value = schedules, IsSuccess = false, Message = "Failed to get Schedules for Selected Template" };
        }
        public async Task<ResponseModel<List<JobDto>>> GetJobsNoScheduleAsync()
        {
            //all jobs with no active schedule, for adding schedule
            var schedules = new List<JobDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/JobsNoSchedules/");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<JobDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<JobDto>>() { IsSuccess = false, Message = "Failed to get jobs" };
        }
        public async Task<ResponseModel<List<ScheduleTemplateTreeModel>>> GetScheduleAsync(string jobNumber)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/schedule/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleTemplateTreeModel>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleTemplateTreeModel>>() { IsSuccess = false, Message = "Could not fetch schedules. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<ScheduleMilestoneDto>>> GetMilestonesForScheduleAsync(string jobNumber)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/MilestonesForSchedule/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleMilestoneDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleMilestoneDto>>() { IsSuccess = false, Message = "Could not fetch Milestones for Schedule. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<ScheduleSactivityDto>>> GetScheduleActivitiesForMilestoneAsync(int scheduleMid)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/ActivitiesForScheduleMilestone/{scheduleMid}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleSactivityDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Message = "Could not fetch Schedule activities for milestone. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<ScheduleSactivityDto>>> GetScheduleActivitiesForScheduleAsync(int scheduleId)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/ActivitiesForSchedule/{scheduleId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleSactivityDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Message = "Could not fetch Schedule activities for milestone. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<ScheduleDto>> UpdateSchedulePublishedAsync(ScheduleDto updateSchedule)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<ScheduleDto, ResponseModel<ScheduleDto>>(
                            "DownstreamApi", updateSchedule,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/UpdatePublishSchedule/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ScheduleDto>() { IsSuccess = false, Value = updateSchedule, Message = "Could not update Schedule Published. If issue persists please contact BI." };
        }
        public async Task<ResponseModel<ScheduleSactivityDto>> UpdateScheduleSactivityNotesAsync(ScheduleSactivityDto updateScheduleSactivity)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<ScheduleSactivityDto, ResponseModel<ScheduleSactivityDto>>(
                            "DownstreamApi", updateScheduleSactivity,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/updateschedulesactivitynotes/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ScheduleSactivityDto>() { Value = updateScheduleSactivity, Message = "Failed to update schedule. Please contact BI if issue persists", IsSuccess = false };
        }
        public async Task<ResponseModel<ScheduleSactivityDto>> UpdateScheduleSactivityAsync(ScheduleSactivityDto updateScheduleSactivity)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<ScheduleSactivityDto, ResponseModel<ScheduleSactivityDto>>(
                            "DownstreamApi", updateScheduleSactivity,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/updateschedulesactivity/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ScheduleSactivityDto>() { Value = updateScheduleSactivity, Message = "Failed to update schedule. Please contact BI if issue persists", IsSuccess = false };
        }

        public async Task<ResponseModel<List<ScheduleSactivityDto>>> UpdateScheduleSactivitysAsync(List<ScheduleSactivityDto> updateScheduleSactivities)
        {
            try
            {
                foreach (var scheduleSactivity in updateScheduleSactivities)
                {
                    if (scheduleSactivity.Predecessors != null)
                    {
                        foreach (var pred in scheduleSactivity.Predecessors)
                        {
                            if(pred.ScheduleA != null)
                            {
                                pred.ScheduleA.Predecessors = null;//else there is a potential circular reference that can't be serialized 
                            }
                        }
                    }
                }
                if (updateScheduleSactivities.Count == 0)
                {
                    return new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = true, Value = null, Message = "Nothing to update" };
                }


                var response = await _downstreamAPI.PutForUserAsync<List<ScheduleSactivityDto>, ResponseModel<List<ScheduleSactivityDto>>>(
                            "DownstreamApi", updateScheduleSactivities,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/updateschedulesactivitys/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Message = "Could not update Schedule Sactivities. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<ScheduleMilestoneDto>>> UpdateScheduleMilestonesAsync(List<ScheduleMilestoneDto> updateScheduleMilestones)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<ScheduleMilestoneDto>, ResponseModel<List<ScheduleMilestoneDto>>>(
                            "DownstreamApi", updateScheduleMilestones,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/updateschedulemilestones/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleMilestoneDto>>() { IsSuccess = false, Message = "Could not update Schedule Sactivities. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<ScheduleDto>> UpdateScheduleAsync(ScheduleDto updateSchedule)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<ScheduleDto, ResponseModel<ScheduleDto>>(
                            "DownstreamApi", updateSchedule,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/updateschedule/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ScheduleDto>() { IsSuccess = false, Message = "Could not update Schedule. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<TemplateDto>>> GetTemplatesAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/templates");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<TemplateDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<TemplateDto>>() { IsSuccess = false, Value = new List<TemplateDto>(), Message = "Could not fetch Templates. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<ScheduleTemplateTreeModel>>> GetTemplateAsync(int templateId)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/template/{templateId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleTemplateTreeModel>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleTemplateTreeModel>>() { IsSuccess = false, Value = new List<ScheduleTemplateTreeModel>(), Message = "Could not fetch Templates. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<TemplateSactivityDto>> UpdateTemplateActivityAsync(TemplateSactivityDto updateTemplateActivity)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<TemplateSactivityDto, ResponseModel<TemplateSactivityDto>>(
                            "DownstreamApi", updateTemplateActivity,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/updatetemplateactivity/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<TemplateSactivityDto>() { IsSuccess = false, Message = "Could not udpate Template activity. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<TemplateSactivityDto>> DeleteTemplateActivityAsync(TemplateSactivityDto updateTemplateActivity)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<TemplateSactivityDto, ResponseModel<TemplateSactivityDto>>(
                            "DownstreamApi", updateTemplateActivity,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/deletetemplateactivity/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<TemplateSactivityDto>() { IsSuccess = false, Message = "Could not delete Template activity. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<TemplateMilestoneDto>> DeleteTemplateMilestoneAsync(TemplateMilestoneDto templateMilestone)
        {

            try
            {
                var response = await _downstreamAPI.PostForUserAsync<TemplateMilestoneDto, ResponseModel<TemplateMilestoneDto>>(
                            "DownstreamApi", templateMilestone,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/deletetemplatemilestone/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<TemplateMilestoneDto>() { IsSuccess = false, Message = "Could not delete Template milestone. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<TemplateDto>> AddTemplateAsync(TemplateDto addTemplate)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<TemplateDto, ResponseModel<TemplateDto>>(
                            "DownstreamApi", addTemplate,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/addtemplate/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<TemplateDto>() { IsSuccess = false, Message = "Could not add Template. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<TemplateDto>> DeleteTemplateAsync(TemplateDto deleteTemplate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<TemplateDto, ResponseModel<TemplateDto>>(
                            "DownstreamApi", deleteTemplate,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/deletetemplate/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<TemplateDto>() { IsSuccess = false, Message = "Could not delete Template. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<TemplateDto>> CopyTemplateAsync(TemplateDto addTemplate)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<TemplateDto, ResponseModel<TemplateDto>>(
                            "DownstreamApi", addTemplate,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/copytemplate/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<TemplateDto>() { IsSuccess = false, Message = "Could not copy Template. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<ScheduleDto>> GenerateScheduleFromTemplateAsync(ScheduleDto addSchedule)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ScheduleDto, ResponseModel<ScheduleDto>>(
                            "DownstreamApi", addSchedule,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/ScheduleFromTemplate/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ScheduleDto>() { IsSuccess = false, Message = "Could not generate Schedule from Template. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<ScheduleDto>>> UpdateScheduleFromTemplateAsync(ScheduleDto addSchedule)
        {
            try
            {
                var listSchedule = new List<ScheduleDto>() { addSchedule };
                var response = await _downstreamAPI.PostForUserAsync<List<ScheduleDto>, ResponseModel<List<ScheduleDto>>>(
                            "DownstreamApi", listSchedule,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/UpdateSchedulesFromTemplate/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleDto>>() { IsSuccess = false, Message = "Could not generate Schedule from Template. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<ScheduleDto>>> UpdateSchedulesFromTemplateAsync(List<ScheduleDto> addSchedule)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<ScheduleDto>, ResponseModel<List<ScheduleDto>>>(
                            "DownstreamApi", addSchedule,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/UpdateSchedulesFromTemplate/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleDto>>() { IsSuccess = false, Message = "Could not generate Schedule from Template. If issue persists please contact BI." };
        }
       
        public async Task<ResponseModel<List<ScheduleDto>>> GenerateSchedulesFromTemplateAsync(List<ScheduleDto> addSchedules)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<ScheduleDto>, ResponseModel<List<ScheduleDto>>>(
                            "DownstreamApi", addSchedules,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/SchedulesFromTemplate/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleDto>>() { IsSuccess = false, Message = "Could not generate Schedules from Template. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<ScheduleTemplateTreeModel>> AddMilestoneToTemplateAsync(ScheduleTemplateTreeModel addMilestone)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ScheduleTemplateTreeModel, ResponseModel<ScheduleTemplateTreeModel>>(
                            "DownstreamApi", addMilestone,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/addmilestonetotemplate/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ScheduleTemplateTreeModel>() { IsSuccess = false, Message = "Could not add Milestone to Template. If issue persists please contact BI." };
        }
        public async Task<ResponseModel<ScheduleDto>> DeleteScheduleAsync(ScheduleDto deleteSchedule)
        {
            //To be used so they could select another template, but must deliberately delete the first schedule
            //Only allow delete if not started, not released, maybe only TEST job
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ScheduleDto, ResponseModel<ScheduleDto>>(
                            "DownstreamApi", deleteSchedule,
                             options => {
                                 options.RelativePath = "api/schedule/DeleteSchedule/";
                             }
                );
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ScheduleDto>() { IsSuccess = false, Message = "Could not delete schedule. If issue persists please contact BI." };
        }
        public async Task<ResponseModel<ScheduleTemplateTreeModel>> AddActivityToTemplateAsync(ScheduleTemplateTreeModel addMilestone)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ScheduleTemplateTreeModel, ResponseModel<ScheduleTemplateTreeModel>>(
                            "DownstreamApi", addMilestone,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/addactivitytotemplate/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ScheduleTemplateTreeModel>() { IsSuccess = false, Message = "Could not add Activity to Template. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<TemplateSactivityDto>>> UpdateTemplateActivitiesSeqAsync(List<TemplateSactivityDto> updateMilestones)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<TemplateSactivityDto>, ResponseModel<List<TemplateSactivityDto>>>(
                            "DownstreamApi", updateMilestones,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/updatetemplateactivitiesseq/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<TemplateSactivityDto>>() { IsSuccess = false, Message = "Could not update Template activities. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<TemplateMilestoneDto>>> UpdateTemplateMilestonesSeqAsync(List<TemplateMilestoneDto> updateMilestones)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<TemplateMilestoneDto>, ResponseModel<List<TemplateMilestoneDto>>>(
                            "DownstreamApi", updateMilestones,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/updatetemplatemilestonesseq/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<TemplateMilestoneDto>>() { IsSuccess = false, Message = "Could not update Template milestones. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<MilestoneDto>>> GetMilestonesAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/milestones");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<MilestoneDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<MilestoneDto>>() { IsSuccess = false, Message = "Could not fetch milestones. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<MilestoneDto>> UpdateMilestoneAsync(MilestoneDto updateMilestone)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MilestoneDto, ResponseModel<MilestoneDto>>(
                            "DownstreamApi", updateMilestone,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/updatemilestone/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<MilestoneDto>() { IsSuccess = false, Message = "Could not update milestone. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<MilestoneDto>>> UpdateMilestonesSeqAsync(List<MilestoneDto> updateMilestones)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<MilestoneDto>, ResponseModel<List<MilestoneDto>>>(
                            "DownstreamApi", updateMilestones,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/updatemilestonesseq/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<MilestoneDto>>() { IsSuccess = false, Message = "Could not update milestones. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<MilestoneDto>> AddMilestoneAsync(MilestoneDto addMilestone)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<MilestoneDto, ResponseModel<MilestoneDto>>(
                            "DownstreamApi", addMilestone,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/addmilestone/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<MilestoneDto>() { IsSuccess = false, Message = "Could not add milestone. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<MilestoneDto>> DeleteMilestoneAsync(MilestoneDto deleteMilestone)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MilestoneDto, ResponseModel<MilestoneDto>>(
                            "DownstreamApi", deleteMilestone,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/deletemilestone/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<MilestoneDto>() { IsSuccess = false, Message = "Could not delete milestone. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<SactivityDto>>> GetSactivitiesAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/sactivities");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<SactivityDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<SactivityDto>>() { IsSuccess = false, Message = "Could not get Sactivities. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<SactivityDto>> DeleteActivityAsync(SactivityDto deleteActivity)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<SactivityDto, ResponseModel<SactivityDto>>(
                            "DownstreamApi", deleteActivity,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/deleteactivity/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<SactivityDto>() { IsSuccess = false, Message = "Could not delete activity. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<SactivityDto>> UpdateActivityAsync(SactivityDto updateActivity)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<SactivityDto, ResponseModel<SactivityDto>>(
                            "DownstreamApi", updateActivity,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/updateactivity/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<SactivityDto>() { IsSuccess = false, Message = "Could not update activity. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<SactivityDto>> AddActivityAsync(SactivityDto addActivity)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<SactivityDto, ResponseModel<SactivityDto>>(
                            "DownstreamApi", addActivity,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/addactivity/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<SactivityDto>() { IsSuccess = false, Message = "Could not add activity. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<SactivityDto>>> UpdateActivitiesSeqAsync(List<SactivityDto> updateMilestones)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<SactivityDto>, ResponseModel<List<SactivityDto>>>(
                            "DownstreamApi", updateMilestones,
                             options =>
                             {
                                 options.RelativePath = "api/schedule/updateactivitiesseq/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<SactivityDto>>() { IsSuccess = false, Message = "Could not update activities. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<TradeDto>>> GetTradesAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/trades");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<TradeDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<TradeDto>>() { IsSuccess = false, Message = "Could not fetch trades. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<List<VarianceDto>>> GetVarianceCodesAsync()
        {
            var codes = new List<VarianceDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/variancecodes/");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<VarianceDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<VarianceDto>>() { Value = codes, IsSuccess = false, Message = "Failed to get Variance Codes" };
        }

        public async Task<ResponseModel<VarianceDto>> AddVarianceCodeAsync(VarianceDto codeToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<VarianceDto, ResponseModel<VarianceDto>>(
                    "DownstreamApi", codeToAdd,
                    options =>
                    {
                        options.RelativePath = "api/schedule/addvariancecode/";
                    });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<VarianceDto>() { Value = null, IsSuccess = false, Message = "Failed to Add Variance Code" };
        }

        public async Task<ResponseModel<VarianceDto>> UpdateVarianceCodeAsync(VarianceDto codeToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<VarianceDto, ResponseModel<VarianceDto>>(
                    "DownstreamApi", codeToAdd,
                    options =>
                    {
                        options.RelativePath = "api/schedule/updatevariancecode/";
                    });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<VarianceDto>() { Value = null, IsSuccess = false, Message = "Failed to update Variance Code" };
        }

        public async Task<ResponseModel<VarianceDto>> DeleteVarianceCodeAsync(VarianceDto codeToDelete)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<VarianceDto, ResponseModel<VarianceDto>>(
                    "DownstreamApi", codeToDelete,
                    options =>
                    {
                        options.RelativePath = "api/schedule/deletevariancecode/";
                    });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<VarianceDto>() { Value = null, IsSuccess = false, Message = "Failed to delete Variance Code" };
        }

        public async Task<ResponseModel<List<CalendarsDayDto>>> GetCalendarAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/calendar");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<CalendarsDayDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<CalendarsDayDto>>() { IsSuccess = false, Message = "Could not fetch Calendar. If issue persists please contact BI." };
        }

        public async Task<ResponseModel<CalendarsDayDto>> UpdateCalendarAsync(CalendarsDayDto entryToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<CalendarsDayDto, ResponseModel<CalendarsDayDto>>(
                            "DownstreamApi", entryToAdd,
                             options =>
                             {
                                 options.RelativePath = $"api/schedule/UpdateCalendar/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<CalendarsDayDto>() { IsSuccess = false, Message = "Failed to update" };
        }

        public async Task<ResponseModel<CalendarsDayDto>> DeleteCalendarItemAsync(CalendarsDayDto entryToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<CalendarsDayDto, ResponseModel<CalendarsDayDto>>(
                            "DownstreamApi", entryToAdd,
                             options =>
                             {
                                 options.RelativePath = $"api/schedule/DeleteCalendarItem/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<CalendarsDayDto>() { IsSuccess = false, Message = "Failed to delete" };
        }

        public async Task<ResponseModel<List<ScheduleSactivityPredDto>>> GetPredsForScheduleAsync(int scheduleId)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/PredsForSchedule/{scheduleId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleSactivityPredDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheduleSactivityPredDto>>() { IsSuccess = false, Message = "Could not fetch preds. If issue persists please contact BI." };
        }

        private List<ScheduleSactivityDto> CalculateAdjustDates(ScheduleSactivityDto activity, List<ScheduleSactivityDto> updateActivities, List<ScheduleSactivityPredDto> preds, List<DateTime>? holidays)
        {
            //TODO: baseline dates do update unless the start date activity is complete. else base dates are locked


            var currentActivity = activity;
            var findSuccessorActivities = preds.Where(x => x.PredSactivityId == currentActivity.SactivityId).ToList();
            if (findSuccessorActivities.Count > 0)
            {
                Parallel.ForEach(findSuccessorActivities, succ =>
                {
                    //if actual end date is not null, adjust based on that rather than scheduled end date
                    if (succ.ScheduleA.ActualStartDate == null || succ.ScheduleA.ActualEndDate == null)
                    {
                        succ.ScheduleA.SchStartDate = succ.ScheduleA.IsLocked != "T" ? currentActivity.ActualEndDate != null ? CalendarExtension.AddWorkingDays((DateTime)currentActivity.ActualEndDate, (int)succ.ScheduleA.LagTime + 1, holidays) : CalendarExtension.AddWorkingDays((DateTime)currentActivity.SchEndDate, (int)succ.ScheduleA.LagTime + 1, holidays) : currentActivity.ActualEndDate != null ? currentActivity.ActualEndDate.Value.AddDays((int)succ.ScheduleA.LagTime + 1) : currentActivity.SchEndDate.Value.AddDays((int)succ.ScheduleA.LagTime + 1);
                        succ.ScheduleA.SchEndDate = succ.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)succ.ScheduleA.SchStartDate, (int)succ.ScheduleA.Duration > 0 ? (int)succ.ScheduleA.Duration - 1 : 0, holidays) : succ.ScheduleA.SchStartDate.Value.AddDays((int)succ.ScheduleA.Duration > 0 ? (int)succ.ScheduleA.Duration - 1 : 0);
                    }

                    updateActivities.Add(succ.ScheduleA);
                    CalculateAdjustDates(succ.ScheduleA, updateActivities, preds, holidays);
                });

                //foreach (var dependentActivity in findSuccessorActivities)
                //{
                //    //if actual end date is not null, adjust based on that rather than scheduled end date
                //    if (dependentActivity.ScheduleA.ActualStartDate == null || dependentActivity.ScheduleA.ActualEndDate == null)
                //    {
                //        dependentActivity.ScheduleA.SchStartDate = dependentActivity.ScheduleA.IsLocked != "T" ? currentActivity.ActualEndDate != null ? CalendarExtension.AddWorkingDays((DateTime)currentActivity.ActualEndDate, (int)dependentActivity.ScheduleA.LagTime + 1, holidays) : CalendarExtension.AddWorkingDays((DateTime)currentActivity.SchEndDate, (int)dependentActivity.ScheduleA.LagTime + 1, holidays) : currentActivity.ActualEndDate != null ? currentActivity.ActualEndDate.Value.AddDays((int)dependentActivity.ScheduleA.LagTime + 1) : currentActivity.SchEndDate.Value.AddDays((int)dependentActivity.ScheduleA.LagTime + 1);
                //        dependentActivity.ScheduleA.SchEndDate = dependentActivity.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)dependentActivity.ScheduleA.SchStartDate, (int)dependentActivity.ScheduleA.Duration > 0 ? (int)dependentActivity.ScheduleA.Duration - 1 : 0, holidays) : dependentActivity.ScheduleA.SchStartDate.Value.AddDays((int)dependentActivity.ScheduleA.Duration > 0 ? (int)dependentActivity.ScheduleA.Duration - 1 : 0);
                //    }
                //    updateActivities.Add(dependentActivity.ScheduleA);
                //    CalculateAdjustDates(dependentActivity.ScheduleA, updateActivities, preds, holidays);
                //};
            }
            return updateActivities;
        }

        private List<ScheduleSactivityDto> CalculateAdjustBaseDates(ScheduleSactivityDto activity, List<ScheduleSactivityDto> updateActivities, List<ScheduleSactivityPredDto> preds, List<DateTime>? holidays)
        {
            var currentActivity = activity;
            var findSuccessorActivities = preds.Where(x => x.PredSactivityId == currentActivity.SactivityId).ToList();
            if (findSuccessorActivities.Count > 0)
            {
                foreach (var dependentActivity in findSuccessorActivities)
                {
                    dependentActivity.ScheduleA.BaseStartDate = dependentActivity.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentActivity.BaseEndDate, (int)dependentActivity.ScheduleA.LagTime + 1, holidays) : currentActivity.BaseEndDate.Value.AddDays((int)dependentActivity.ScheduleA.LagTime + 1);
                    dependentActivity.ScheduleA.BaseEndDate = dependentActivity.ScheduleA.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)dependentActivity.ScheduleA.BaseStartDate, (int)dependentActivity.ScheduleA.Duration > 0 ? (int)dependentActivity.ScheduleA.Duration - 1 : 0, holidays) : dependentActivity.ScheduleA.BaseStartDate.Value.AddDays((int)dependentActivity.ScheduleA.Duration > 0 ? (int)dependentActivity.ScheduleA.Duration - 1 : 0);
                    updateActivities.Add(dependentActivity.ScheduleA);
                    CalculateAdjustBaseDates(dependentActivity.ScheduleA, updateActivities, preds, holidays);
                }
                ;
            }
            return updateActivities;
        }
        Dictionary<int, List<ActivityNodeDto>> children2 = null;
        // Dictionary<int, List<ScheduleSactivityDto>> children = null;
        List<ScheduleSactivityDto> result = null;
        Queue<ActivityNodeDto> q = new Queue<ActivityNodeDto>();
        List<ScheduleSactivityDto> visited = new List<ScheduleSactivityDto>();

        private void RecalculateDates(List<ScheduleSactivityPredDto> preds, List<ScheduleSactivityDto> allActivities, ScheduleSactivityDto topActivity, List<DateTime>? holidays, bool? adjustBaseDates = false)
        {
            //using a Breadth-first tree traversal, adding nodes to a Queue, to update scheduled or baseline dates based on predecessor
            children2 = new Dictionary<int, List<ActivityNodeDto>>();
            var countNodes1 = 0;
            //construct the dictionary 
            visited = new List<ScheduleSactivityDto>();
            foreach (var n in preds.Where(x => x.PredScheduleAid != null && x.ScheduleA != null && x.IsActive == true))//only active predecessors
            {                
                if (!children2.ContainsKey((int)n.PredScheduleAid))
                {
                    children2[(int)n.PredScheduleAid] = new List<ActivityNodeDto>();
                }
                children2[(int)n.PredScheduleAid].Add(new ActivityNodeDto() { CurrentActivity = allActivities.FirstOrDefault(x => x.ScheduleAid == n.ScheduleA.ScheduleAid), ParentActivityId = n.PredScheduleAid });//key is the predecessor, value is the child
            }

            //add the first node to the queue
            q.Enqueue(new ActivityNodeDto() { CurrentActivity = topActivity, ParentActivityId = null });
            while (q.Count > 0)
            {
                var currentNode = q.Dequeue();
                var currentParent = currentNode.ParentActivityId != null ? currentNode.ParentActivity : null;//current parent null for top of tree
                DateTime newStart;
                DateTime newEnd;
                if (visited.Contains(currentNode.CurrentActivity) && currentParent != null)
                {
                    //if this activity is already visited, calculate new dates bases on current parent, compare with the visited one to see which branch should stay, to avoid recalculating multiple times for case when actiity is in tree multiple times due to multiple predecessor
                    //calculate new dates
                    if (adjustBaseDates == true)
                    {
                        newStart = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.BaseEndDate, (int)currentNode.CurrentActivity.LagTime + 1, holidays) : currentParent.CurrentActivity.BaseEndDate.Value.AddDays((int)currentNode.CurrentActivity.LagTime + 1);
                        newEnd = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays(newStart, (int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0, holidays) : newStart.AddDays((int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0);
                    }
                    else
                    {
                        newStart = currentNode.CurrentActivity.IsLocked != "T" ? currentParent.CurrentActivity.ActualEndDate != null ? CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.ActualEndDate, (int)currentNode.CurrentActivity.LagTime + 1, holidays) : CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.SchEndDate, (int)currentNode.CurrentActivity.LagTime + 1, holidays) : currentParent.CurrentActivity.ActualEndDate != null ? currentParent.CurrentActivity.ActualEndDate.Value.AddDays((int)currentNode.CurrentActivity.LagTime + 1) : currentParent.CurrentActivity.SchEndDate.Value.AddDays((int)currentNode.CurrentActivity.LagTime + 1);
                        newEnd = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays(newStart, (int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0, holidays) : newStart.AddDays((int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0);
                    }

                    //if previously calculated value is later date than or equal to new calculated value, just keep going, don't enque the current children, don't add current to visited, because the previous one will be used, otherwise, remove visited ones, add new
                    if ((adjustBaseDates != true && visited.Where(x => x.ScheduleAid == currentNode.CurrentActivity.ScheduleAid).FirstOrDefault().SchStartDate < newStart) || (adjustBaseDates == true && visited.Where(x => x.ScheduleAid == currentNode.CurrentActivity.ScheduleAid).FirstOrDefault().BaseStartDate < newStart))
                    {
                        // if current value is greater, remove previous children from q, enque current children
                        visited.RemoveAll(x => x.ScheduleAid == currentNode.CurrentActivity.ScheduleAid);
                        if (adjustBaseDates == true)
                        {
                            currentNode.CurrentActivity.BaseStartDate = newStart;
                            currentNode.CurrentActivity.BaseEndDate = newEnd;
                        }
                        else if (currentNode.CurrentActivity.ActualStartDate == null || currentNode.CurrentActivity.ActualEndDate == null)//don't update sched dates if actually started
                        {
                            currentNode.CurrentActivity.SchStartDate = newStart;
                            currentNode.CurrentActivity.SchEndDate = newEnd;
                        }


                        visited.Add(currentNode.CurrentActivity);
                        q = new Queue<ActivityNodeDto>(q.Where(x => x.ParentActivityId != currentNode.CurrentActivity.ScheduleAid));//remove any old children from queue -- this creates new queue, probably poor performance but should not happen often
                        if (children2.ContainsKey(currentNode.CurrentActivity.ScheduleAid))
                        {
                            foreach (var n in children2[currentNode.CurrentActivity.ScheduleAid])
                            {
                                q.Enqueue(new ActivityNodeDto() { CurrentActivity = n.CurrentActivity, ParentActivityId = currentNode.CurrentActivity.ScheduleAid, ParentActivity = currentNode });
                                countNodes1++;
                            }
                        }
                    }
                    //else do nothing
                }
                else
                {
                    //activity was not already visited, add to visited collection and process dates
                    visited.Add(currentNode.CurrentActivity);
                    if (adjustBaseDates != true && currentParent != null && (currentNode.CurrentActivity.ActualStartDate == null || currentNode.CurrentActivity.ActualEndDate == null))
                    {
                        currentNode.CurrentActivity.SchStartDate = currentNode.CurrentActivity.IsLocked != "T" ? currentParent.CurrentActivity.ActualEndDate != null ? CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.ActualEndDate, (int)currentNode.CurrentActivity.LagTime + 1, holidays) : CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.SchEndDate, (int)currentNode.CurrentActivity.LagTime + 1, holidays) : currentParent.CurrentActivity.ActualEndDate != null ? currentParent.CurrentActivity.ActualEndDate.Value.AddDays((int)currentNode.CurrentActivity.LagTime + 1) : currentParent.CurrentActivity.SchEndDate.Value.AddDays((int)currentNode.CurrentActivity.LagTime + 1);
                        currentNode.CurrentActivity.SchEndDate = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentNode.CurrentActivity.SchStartDate, (int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0, holidays) : currentNode.CurrentActivity.SchStartDate.Value.AddDays((int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0);
                    }
                    else if (adjustBaseDates == true && currentParent != null)
                    {
                        currentNode.CurrentActivity.BaseStartDate = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentParent.CurrentActivity.BaseEndDate, (int)currentNode.CurrentActivity.LagTime + 1, holidays) : currentParent.CurrentActivity.BaseEndDate.Value.AddDays((int)currentNode.CurrentActivity.LagTime + 1);
                        currentNode.CurrentActivity.BaseEndDate = currentNode.CurrentActivity.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)currentNode.CurrentActivity.BaseStartDate, (int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0, holidays) : currentNode.CurrentActivity.BaseStartDate.Value.AddDays((int)currentNode.CurrentActivity.Duration > 0 ? (int)currentNode.CurrentActivity.Duration - 1 : 0);
                    }
                    if (children2.ContainsKey(currentNode.CurrentActivity.ScheduleAid))
                    {
                        foreach (var n in children2[currentNode.CurrentActivity.ScheduleAid])
                        {
                            q.Enqueue(new ActivityNodeDto() { CurrentActivity = n.CurrentActivity, ParentActivityId = currentNode.CurrentActivity.ScheduleAid, ParentActivity = currentNode });
                            countNodes1++;
                        }
                    }
                }
            }
        }
        //private void DepthFirstRecalculate(List<ScheduleSactivityPredDto> preds, ScheduleSactivityDto topActivity, List<DateTime>? holidays)
        //{
        //    var ie = preds;
        //    children = new Dictionary<int, List<ScheduleSactivityDto>>();

        //    // construct the dictionary 
        //    foreach (var n in ie)
        //    {
        //        if (!children.ContainsKey((int)n.PredScheduleAid))
        //        {
        //            children[(int)n.PredScheduleAid] = new List<ScheduleSactivityDto>();
        //        }
        //        children[(int)n.PredScheduleAid].Add(n.ScheduleA);
        //    }

        //    // Depth first traversal
        //    result = new List<ScheduleSactivityDto>();
        //    var sw = new Stopwatch();
        //    sw.Start();
        //    countNodes = 0;
        //    DepthFirstTraversal(topActivity, holidays);
        //    var test = countNodes;
        //    sw.Stop();
        //    var elapsed = sw.Elapsed;
        //    if (result.Count() != ie.Count())
        //    {
        //        // If there are cycles, some nodes cannot be reached from the root,
        //        // and therefore will not be contained in the result. 
        //       // throw new Exception("Original list of nodes contains cycles");

        //       //not correct here. same item can be in tree multiple times
        //    }
        //}
        //int countNodes = 0;
        //private void DepthFirstTraversal(ScheduleSactivityDto parent, List<DateTime>? holidays)
        //{
        //    if (children.ContainsKey(parent.ScheduleAid))
        //    {
        //        //foreach (var child in children[parent.ScheduleAid])
        //        //{
        //        //    countNodes++;
        //        //    //calculate dates
        //        //    if (child.ActualStartDate == null || child.ActualEndDate == null)
        //        //    {
        //        //        child.SchStartDate = child.IsLocked != "T" ? parent.ActualEndDate != null ? CalendarExtension.AddWorkingDays((DateTime)parent.ActualEndDate, (int)child.LagTime + 1, holidays) : CalendarExtension.AddWorkingDays((DateTime)parent.SchEndDate, (int)child.LagTime + 1, holidays) : parent.ActualEndDate != null ? parent.ActualEndDate.Value.AddDays((int)child.LagTime + 1) : parent.SchEndDate.Value.AddDays((int)child.LagTime + 1);
        //        //        child.SchEndDate = child.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)child.SchStartDate, (int)child.Duration > 0 ? (int)child.Duration - 1 : 0, holidays) : child.SchStartDate.Value.AddDays((int)child.Duration > 0 ? (int)child.Duration - 1 : 0);
        //        //    }
        //        //    //try instead of just add everything to results, replace results for that activity if the new date is later?
        //        //    //adding to this result set is slow. why not just use the children
        //        //    //adding to the result set doesn't work anyway, because it's addding a reference type not value, so it changes. 
        //        //    //using the child ends up just keeping the last calculation, which might not be right


        //        //    //if (result.Select(x => x.ScheduleAid).Contains(child.ScheduleAid))
        //        //    //{
        //        //    //    if (result.Where(x => x.ScheduleAid == child.ScheduleAid && x.SchStartDate < child.SchStartDate).Any())
        //        //    //    {
        //        //    //        result.RemoveAll(x => x.ScheduleAid == child.ScheduleAid && x.SchStartDate < child.SchStartDate);
        //        //    //        result.Add(child);
        //        //    //    }
        //        //    //}
        //        //    //else
        //        //    //{
        //        //    //    result.Add(child);
        //        //    //}

        //        //    DepthFirstTraversal(child, holidays);
        //        //}
        //        Parallel.ForEach(children[parent.ScheduleAid], child =>
        //        {
        //            countNodes++;
        //            //calculate dates
        //            if (child.ActualStartDate == null || child.ActualEndDate == null)
        //            {
        //                child.SchStartDate = child.IsLocked != "T" ? parent.ActualEndDate != null ? CalendarExtension.AddWorkingDays((DateTime)parent.ActualEndDate, (int)child.LagTime + 1, holidays) : CalendarExtension.AddWorkingDays((DateTime)parent.SchEndDate, (int)child.LagTime + 1, holidays) : parent.ActualEndDate != null ? parent.ActualEndDate.Value.AddDays((int)child.LagTime + 1) : parent.SchEndDate.Value.AddDays((int)child.LagTime + 1);
        //                child.SchEndDate = child.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)child.SchStartDate, (int)child.Duration > 0 ? (int)child.Duration - 1 : 0, holidays) : child.SchStartDate.Value.AddDays((int)child.Duration > 0 ? (int)child.Duration - 1 : 0);
        //            }

        //            DepthFirstTraversal(child, holidays);
        //        });

        //    }
        //}

        public async Task<ResponseModel<List<ScheduleSactivityDto>>> UpdateScheduleSActivityAsync(ScheduleSactivityDto updateActivity, List<ScheduleSactivityDto> allActivitiesInSchedule, bool updateBaseDates = false)
        {
            try
            {
                //calculates updated dates for front end, no save to db          
                var getCalendar = (await GetHolidaysAsync()).Value;
                var holidays = getCalendar.Select(x => x.WorkDate).ToList();
                var findActivity = updateActivity;
                var updateActivities = new List<ScheduleSactivityDto>();
                var findSactivities = allActivitiesInSchedule;

                //TODO: it should also change if a pred changed
                var returnList = new List<ScheduleSactivityDto>();

                //update the whole schedule
                var listActivities = new List<ScheduleSactivityDto>();

                //var findSuccessorActivities = (await GetPredsForScheduleAsync(updateActivity.ScheduleM.ScheduleId)).Value;

                var allPredecessors = allActivitiesInSchedule.SelectMany(x => x.Predecessors).ToList();

                if (updateBaseDates)
                {
                    // returnList = CalculateAdjustBaseDates(findActivity, listActivities, findSuccessorActivities, holidays);
                    RecalculateDates(allPredecessors, allActivitiesInSchedule, findActivity, holidays, true);
                    returnList = visited;
                }
                else
                {
                    //returnList = CalculateAdjustDates(findActivity, listActivities, findSuccessorActivities, holidays);
                    RecalculateDates(allPredecessors, allActivitiesInSchedule, findActivity, holidays, false);
                    //DepthFirstRecalculate(findSuccessorActivities, findActivity, holidays);
                    returnList = visited;
                }
                updateActivities = allActivitiesInSchedule.Where(x => returnList.Select(y => y.ScheduleAid).Contains(x.ScheduleAid)).ToList();
                foreach (var activity in updateActivities)
                {
                    // var changed = activity.SchStartDate != returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);                   
                    if (updateBaseDates)
                    {
                        activity.BaseStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.BaseStartDate);
                        activity.BaseEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.BaseEndDate);
                    }
                    else
                    {
                        activity.SchStartDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchStartDate);
                        activity.SchEndDate = returnList.Where(x => x.ScheduleAid == activity.ScheduleAid).Max(x => x.SchEndDate);
                    }
                    DateTime? plusminusdaysend = activity.ActualEndDate != null ? activity.ActualEndDate.Value : activity.SchEndDate != null ? activity.SchEndDate.Value : null;
                    if (plusminusdaysend != null && activity.BaseEndDate != null)
                    {
                        activity.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(activity.BaseEndDate.Value, plusminusdaysend.Value, holidays);
                    }

                }
                updateActivities.Add(updateActivity);//add the one being updated
                return new ResponseModel<List<ScheduleSactivityDto>>() { Value = updateActivities, IsSuccess = true, Message = "Schedule updated" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<ScheduleSactivityDto>> { IsSuccess = false, Message = "Failed to update Schedule SActivity" };
            }
        }

        public async Task<ResponseModel<List<SactivityDto>>> GetJobSActivityAsync(string jobNumber)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedule/getjobsactivity/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<SactivityDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<SactivityDto>>() { IsSuccess = false, Message = "Could not fetch sactivity. If issue persists please contact BI." };
        }
        public async Task<ResponseModel> LogException(Exception ex)
        {
#if DEBUG
            _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif

            return new ResponseModel() { IsSuccess = true, Message = "logged" };
        }

        public async Task<ResponseModel<List<IncompleteSubdivisionActivitiesDto>>> GetTotalIncompleteActivitiesBySubdivision()
        {
            var incompleteSubdivisionActivities = new ResponseModel<List<IncompleteSubdivisionActivitiesDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/schedule/gettotalincompleteactivitiesbysubdivision/");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    incompleteSubdivisionActivities = JsonConvert.DeserializeObject<ResponseModel<List<IncompleteSubdivisionActivitiesDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return incompleteSubdivisionActivities;
        }


        public async Task<ResponseModel<List<ScheduleSactivityDto>>> GetIncompleteActivitiesBySubdivision(int subdivisionId)
        {
            var activities = new ResponseModel<List<ScheduleSactivityDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/schedule/GetIncompleteActivitiesBySubdivision/{subdivisionId}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    activities = JsonConvert.DeserializeObject<ResponseModel<List<ScheduleSactivityDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return activities;
        }

        public async Task<ResponseModel<byte[]>> DownloadExcelSchedulesAsync(List<ScheduleTemplateTreeModel> model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<ScheduleTemplateTreeModel>, ResponseModel<byte[]>>(
                           "DownstreamApi", model,
                            options =>
                            {
                                options.RelativePath = "api/schedule/DownloadSchedulesExport/";
                            });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<byte[]>() { Value = null, IsSuccess = false };
        }

        public async Task<ResponseModel<byte[]>> DownloadExcelScheduleTemplatesAsync(List<ScheduleTemplateTreeModel> model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<ScheduleTemplateTreeModel>, ResponseModel<byte[]>>(
                           "DownstreamApi", model,
                            options =>
                            {
                                options.RelativePath = "api/schedule/DownloadSchedulesTemplatesExport/";
                            });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<byte[]>() { Value = null, IsSuccess = false };
        }
    }
}
