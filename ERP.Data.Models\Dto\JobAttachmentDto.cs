﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class JobAttachmentDto :IMapFrom<JobAttachment>
{
    public int AttachmentId { get; set; }

    public string? JobNumber { get; set; }

    public string? Path { get; set; }

    public string? Name { get; set; }

    public int? DoctypeId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }
    public virtual DoctypeDto? Doctype { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<JobAttachmentDto, JobAttachment>().ReverseMap();
    }
}
