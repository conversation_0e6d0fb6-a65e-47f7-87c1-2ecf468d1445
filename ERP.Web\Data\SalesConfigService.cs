﻿using DocumentFormat.OpenXml.Spreadsheet;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using NLog;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class SalesConfigService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public SalesConfigService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;

        }       
        public async Task<ResponseModel<List<SalesConfigCombinedTreeModel>>> GetSalesConfigCommunitiesAsync()
        {
            var items = new List<SalesConfigCombinedTreeModel>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesconfig/getsalesconfigcommunities/");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<SalesConfigCombinedTreeModel>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<SalesConfigCombinedTreeModel>>() { Value = items, IsSuccess = false, Message = "Failed to get Sales Config Communities Data" };
        }
        public async Task<ResponseModel<List<SalesConfigCombinedTreeModel>>> GetSalesConfigJobsByCommunityAsync(int subdivisionId)
        {
            var items = new List<SalesConfigCombinedTreeModel>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesconfig/getsalesconfigjobsbycommunity/{subdivisionId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<SalesConfigCombinedTreeModel>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<SalesConfigCombinedTreeModel>>() { Value = items, IsSuccess = false, Message = "Failed to get Sales Config Jobs by Community" };
        }
        public async Task<ResponseModel<List<SalesConfigCombinedTreeModel>>> GetSalesConfigsAndCosByJobAsync(string jobNumber)
        {
            var items = new List<SalesConfigCombinedTreeModel>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesconfig/GetSalesConfigsAndCosByJob/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<SalesConfigCombinedTreeModel>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<SalesConfigCombinedTreeModel>>() { Value = items, IsSuccess = false, Message = "Failed to get Sales Config and Cos by Job" };
        }
        public async Task<ResponseModel<List<SalesConfigCombinedTreeModel>>> GetSalesConfigOptionsAsync(int salesConfigId)
        {
            var items = new List<SalesConfigCombinedTreeModel>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesconfig/getsalesconfigoptions/{salesConfigId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<SalesConfigCombinedTreeModel>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<SalesConfigCombinedTreeModel>>() { Value = items, IsSuccess = false, Message = "Failed to get Sales Config Options" };
        }      
        public async Task<ResponseModel<List<SalesConfigCombinedTreeModel>>> GetSalesConfigsCoOptionsAsync(int salesConfigCoId)
        {
            var items = new List<SalesConfigCombinedTreeModel>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/salesconfig/getsalesconfigcooptions/{salesConfigCoId}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<SalesConfigCombinedTreeModel>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<SalesConfigCombinedTreeModel>>() { Value = items, IsSuccess = false, Message = "Failed to get Sales Config Co Options" };
        }
        public async Task<ResponseModel<SalesConfigCombinedTreeModel>> ApproveCoAsync(SalesConfigCombinedTreeModel coToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<SalesConfigCombinedTreeModel, ResponseModel<SalesConfigCombinedTreeModel>>(
                            "DownstreamApi", coToUpdate,
                             options => {
                                 options.RelativePath = "api/salesconfig/approveco/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<SalesConfigCombinedTreeModel>() { IsSuccess = false, Message = "Failed to approve Co" };

        }
        public async Task<ResponseModel<SalesConfigCombinedTreeModel>> ApproveConfigAsync(SalesConfigCombinedTreeModel configToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<SalesConfigCombinedTreeModel, ResponseModel<SalesConfigCombinedTreeModel>>(
                            "DownstreamApi", configToUpdate,
                             options => {
                                 options.RelativePath = "api/salesconfig/approveconfig/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<SalesConfigCombinedTreeModel>() { IsSuccess = false, Message = "Failed to approve Config" };
        }
    }
}
