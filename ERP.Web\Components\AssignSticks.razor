﻿@using ERP.Data.Models;
@inject SubdivisionService SubdivisionService
@using ERP.Data.Models.Dto;
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<TelerikWindow Modal="true"
@bind-Visible="@IsModalVisible"
Width="1000px"
Height="600px"
CloseOnOverlayClick="true">
    <WindowTitle>
        <h7 class="page-title" style="font-weight:bold">Assign Sticks</h7>
    </WindowTitle>
    <WindowContent>
        <div class="card-body">
            <div class="row">
                <div class="col-4">
                    <div class="mb-3 row">
                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Subdivision:</label>
                        <div class="col-sm-8">
                            &nbsp;
                            @{
                                <TelerikDropDownList Data="@Subdivisions"
                                TextField="SubdivisionName"
                                ValueField="SubdivisionId"
                                Enabled="@AllowEdit"
                                OnChange="OnSubdivisionSelect"
                                Filterable="true"
                                FilterOperator="@StringFilterOperator.Contains"
                                @bind-Value="SelectedSubdivisionId"
                                Width="200px">
                                </TelerikDropDownList>
                            }
                        </div>
                    </div>
                    <div class="mb-3 row">
                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Stick:</label>
                        <div class="col-sm-8">
                            &nbsp;
                            @{
                                <TelerikDropDownList Data="@AllSticksInSubdivision"
                                @ref="StickDropdownListRef"
                                Enabled="@AllowEdit"
                                Filterable="true"
                                FilterOperator="@StringFilterOperator.Contains"
                                @bind-Value="SelectedStick"
                                Width="200px">
                                </TelerikDropDownList>
                                @if (SelectedStick == "Add New")
                                {
                                    <br />
                                    <TelerikTextBox @bind-Value="NewStickName" Placeholder="New Stick Name" Width="200px"></TelerikTextBox>
                                }
                            }
                        </div>
                    </div>
                    <div class="mb-3 row">
                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Building Num (for Condo):</label>
                        <div class="col-sm-8">
                            &nbsp;
                            @{
                                <TelerikDropDownList Data="@AllBuildingNumsInSubdivision"
                                @ref="BuildingDropdownListRef"
                                Filterable="true"
                                Enabled="@AllowEdit"
                                FilterOperator="@StringFilterOperator.Contains"
                                @bind-Value="SelectedBuildingNum"
                                Width="200px">
                                </TelerikDropDownList>
                                @if (SelectedBuildingNum == "Add New")
                                {
                                    <br />
                                    <TelerikTextBox @bind-Value="NewBuildingNumber" Placeholder="New Building Num" Width="200px"></TelerikTextBox>
                                }
                            }
                        </div>
                    </div>
                    <div class="mb-3 row">
                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Phase:</label>
                        <div class="col-sm-8">
                            &nbsp;
                            @{
                                <TelerikDropDownList @ref="PhaseDropdownListRef"
                                Data="@AllPhasesInSubdivision"
                                Filterable="true"
                                Enabled="@AllowEdit"
                                FilterOperator="@StringFilterOperator.Contains"
                                @bind-Value="SelectedPhase"
                                Width="200px">
                                </TelerikDropDownList>
                                @if (SelectedPhase == "Add New")
                                {
                                    <br />
                                    <TelerikTextBox @bind-Value="NewPhase" Placeholder="New Phase" Width="200px"></TelerikTextBox>
                                }
                            }
                        </div>
                    </div>
                </div>
                <div class="col-8">
                    <div class="mb-3 row">
                        <div class="col-sm-8">
                            <label class="form-label">Select jobs to apply the same Stick/Building Num/Phase</label><br />
                            <TelerikGrid 
                            Height="400px"
                            Width="600px"
                            RowHeight="40"
                            @bind-SelectedItems="@SelectedJobs"
                            FilterMode="@GridFilterMode.FilterMenu"
                            Data="AvailableJobs"
                            SelectionMode="GridSelectionMode.Multiple">
                                <GridColumns>
                                    <GridCheckboxColumn SelectAllMode="GridSelectAllMode.All"></GridCheckboxColumn>
                                    <GridColumn Field="JobNumber" Filterable="true"></GridColumn>
                                    <GridColumn Field="LotNumber" Title="Lot"></GridColumn>
                                    @*  <GridColumn Field="Subdivision.SubdivisionName" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Subdivision"></GridColumn> *@                                                   
                                    <GridColumn Field="Phase" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Phase"></GridColumn>
                                    <GridColumn Field="BuildingNum" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Building"></GridColumn>
                                    <GridColumn Field="StickBuilingNum" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Stick"></GridColumn>
                                </GridColumns>
                            </TelerikGrid>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mb-3 row">
                <div class="col-1"></div>
                @if (AllowEdit)
                {
                    <div class="col">
                        <button type="button" onclick="@HandleValidSubmit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                            <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                                <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                    <!--!-->
                                    <!--!-->
                                    <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                                </svg>
                            </span> Update
                        </button>
                    </div>
                }
            </div>
        </div>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {
    public bool IsModalVisible { get; set; } = false;
    public List<JobDto> jobs { get; set; }
    public List<SubdivisionDto>? Subdivisions { get; set; } = new List<SubdivisionDto>();
    private string submittingStyle = "display:none";
    public List<JobDto> SelectLots { get; set; } = new List<JobDto>();
    public List<string> AllLots { get; set; }
    public List<string> AllPhasesInSubdivision { get; set; } = new List<string> { "Add New" };
    public List<string> AllBuildingNumsInSubdivision { get; set; } = new List<string> { "Add New" };
    public List<string> AllSticksInSubdivision { get; set; } = new List<string> { "Add New" };
    private TelerikDropDownList<string, string?>? StickDropdownListRef;
    private TelerikDropDownList<string, string?>? BuildingDropdownListRef;
    private TelerikDropDownList<string, string?>? PhaseDropdownListRef;
    private IEnumerable<JobDto> SelectedJobs { get; set; } = new List<JobDto>();
    private List<JobDto> AvailableJobs { get; set; } = new List<JobDto>();
    private bool AllowEdit { get; set; } = true;
    public string? SelectedPhase { get; set; }
    public string? SelectedBuildingNum { get; set; }
    public string? SelectedStick { get; set; }
    public string NewStickName { get; set; }
    public string NewBuildingNumber { get; set; }
    public string NewPhase { get; set; }
    public int? SelectedSubdivisionId { get; set; }

    [Parameter]
    public EventCallback<ResponseModel?> HandleAddSubmit { get; set; }

    public ValidationEvent ValidationEvent { get; set; } = ValidationEvent.Change;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var subdivisionsTask = SubdivisionService.GetSubdivisionsAsync();
        await Task.WhenAll(new Task[] { subdivisionsTask});
        Subdivisions = subdivisionsTask.Result.Value;
    }
    public void Show()
    {
        SelectedJobs = Enumerable.Empty<JobDto>();
        IsModalVisible = true;
        StateHasChanged();
    }

    private async void HandleValidSubmit()
    {
        if (SelectedPhase == "Add New")
        {
            SelectedPhase = NewPhase;
            AllPhasesInSubdivision.Add(NewPhase);
            PhaseDropdownListRef?.Rebind();
        }
        if (SelectedBuildingNum == "Add New")
        {
            SelectedBuildingNum = NewBuildingNumber;
            AllBuildingNumsInSubdivision.Add(NewBuildingNumber);
            BuildingDropdownListRef?.Rebind();
        }
        if (SelectedStick == "Add New")
        {
            SelectedStick = NewStickName;
            AllSticksInSubdivision.Add(NewStickName);
            StickDropdownListRef?.Rebind();
        }
        var response = new ResponseModel();
        if (SelectedJobs.Count() > 0)
        {
            var selectedJobNums = SelectedJobs.Select(x => x.JobNumber).ToList();
            var updateLot = new JobDto()
            {
                StickBuilingNum = SelectedStick,
                Phase = SelectedPhase,
                BuildingNum = SelectedBuildingNum
            };
            var updateMultipleResponse = await SubdivisionService.UpdateLotsAsync(updateLot, selectedJobNums);
            ShowSuccessOrErrorNotification(updateMultipleResponse.Message, updateMultipleResponse.IsSuccess);
            response.IsSuccess = updateMultipleResponse.IsSuccess; 
            response.Message = updateMultipleResponse.Message;
        }
        if(SelectedSubdivisionId != null)
        {
            var jobsInSubdivision = (await SubdivisionService.GetJobsAsync((int)SelectedSubdivisionId)).Value;
            AvailableJobs = jobsInSubdivision;
        }
        SelectedJobs = new List<JobDto>();
        StateHasChanged();
        await HandleAddSubmit.InvokeAsync(response);
    }
    async Task OnSubdivisionSelect(object selectedSubdivisions)
    {
        if (SelectedSubdivisionId != null)
        {
            AllPhasesInSubdivision = new List<string> { "Add New" };
            AllBuildingNumsInSubdivision  = new List<string> { "Add New" };
            AllSticksInSubdivision  = new List<string> { "Add New" };
            var JobsInSubdivision = await SubdivisionService.GetJobsAsync((int)SelectedSubdivisionId);
            AvailableJobs = JobsInSubdivision.Value;
            AllPhasesInSubdivision.AddRange(AvailableJobs.Select(x => x.Phase).Distinct().ToList());
            AllBuildingNumsInSubdivision.AddRange(AvailableJobs.Select(x => x.BuildingNum).Distinct().ToList());
            AllSticksInSubdivision.AddRange(AvailableJobs.Select(x => x.StickBuilingNum).Distinct().ToList());
            StickDropdownListRef?.Rebind();
            PhaseDropdownListRef?.Rebind();
            BuildingDropdownListRef?.Rebind();
        }
    }
    async void CancelAddItem()
    {
        IsModalVisible = false;
    }

    public async Task Hide()
    {
        IsModalVisible = false;        
    }
    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
