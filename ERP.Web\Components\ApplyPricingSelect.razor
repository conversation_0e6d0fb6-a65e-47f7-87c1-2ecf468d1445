﻿@using ERP.Data.Models;
@inject SalesPriceService SalesPriceService
@using ERP.Data.Models.Dto;
<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <h7 class="page-title" style="font-weight:bold">Apply Pricing</h7>
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@ApplyPrice" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />

            <div class="k-validation-summary k-messagebox k-messagebox-error p-0" role="alert">
                <ValidationSummary />
            </div>
            <div class="mb-3">
                <label class="form-label">Apply as Current? Leave unchecked to apply as Next cost</label><br />
                <TelerikCheckBox @bind-Value = "@ApplyPrice.ApplyAsCurrent"></TelerikCheckBox>
            </div>
            @if (!ApplyPrice.ApplyAsCurrent)
            {
                <div class="mb-3">
                    <label class="form-label">Next Price Start Date</label><br />
                    <TelerikDatePicker @bind-Value="@ApplyPrice.PriceDate"></TelerikDatePicker>
                </div>
            }
           

            <button type="submit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                    </svg>
                </span> Apply
            </button>
           
            <div style=@submittingStyle>Applying Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    [Parameter]
    public ApplyPricingModel? ApplyPrice { get; set; } 
    public bool Current { get; set; }
    public DateTime? PriceDate { get; set; }

    private string submittingStyle = "display:none";


    [Parameter]
    public EventCallback<ResponseModel<ApplyPricingModel>> HandleAddSubmit { get; set; }

    public ValidationEvent ValidationEvent { get; set; } = ValidationEvent.Change;

    public async Task Show()
    {
        ApplyPrice.ApplyAsCurrent = true;
        IsModalVisible = true;
        StateHasChanged();
    }


    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        ResponseModel<ApplyPricingModel> response = new ResponseModel<ApplyPricingModel>();
        if(ApplyPrice.WorksheetId != null && ApplyPrice.WorksheetData == null)
        {
            response = await SalesPriceService.ApplyPricingAsync(ApplyPrice);//whole worksheet
        }
        else if(ApplyPrice.WorksheetData != null)
        {
            response = await SalesPriceService.ApplyPricingOptionAsync(ApplyPrice);//individual option
        }
        submittingStyle = "display:none";
        IsModalVisible = false;
        await HandleAddSubmit.InvokeAsync(response);
    }


    public async Task Hide()
    {
        IsModalVisible = false;
    }
}
