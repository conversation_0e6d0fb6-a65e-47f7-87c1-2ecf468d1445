﻿using Azure.Core;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Models;
using ERP.Web.Pages;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Abstractions;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using NLog;
using System.Xml;
using Telerik.DataSource;
using Telerik.DataSource.Extensions;


namespace ERP.Web.Data
{
    public class AttributeService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public AttributeService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<ResponseModel<List<MasterAttributeGroupDto>>> GetAttributeGroupItemsAsync()
        {
            var responseModel = new ResponseModel<List<MasterAttributeGroupDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/attribute/getattributegroupitems");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<MasterAttributeGroupDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }

        public async Task<ResponseModel<List<MasterOptionDto>>> GetOptionAttributeGroupItemsAsync(int MasterOptionId)
        {
            var responseModel = new ResponseModel<List<MasterOptionDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/attribute/getoptionattributegroupitems/{MasterOptionId}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<MasterOptionDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }

        public async Task<ResponseModel<List<MasterOptionDto>>> GetDistinctMasterOptionsInAttributeAsync()
        {
            var responseModel = new ResponseModel<List<MasterOptionDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/attribute/getdistinctmasteroptionsinattribute");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<MasterOptionDto>>>(responseString);
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }

        public async Task<ResponseModel<List<MasterOptionDto>>> GetMasterOptionsFromAttributeGroupAssignmentAsync(int AttributeGroupAssignmentId)
        {
            var responseModel = new ResponseModel<List<MasterOptionDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/attribute/getmasteroptionsfromattributegroupassignment/{AttributeGroupAssignmentId}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<MasterOptionDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }

        public async Task<ResponseModel<List<MasterAttributeGroupDto>>> GetMasterAttributeGroupsAsync()
        {
            var responseModel = new ResponseModel<List<MasterAttributeGroupDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/attribute/getmasterattributegroups");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<MasterAttributeGroupDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }

        public async Task<MasterAttributeGroupDto> UpdateAttributeGroupAsync(MasterAttributeGroupDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterAttributeGroupDto, MasterAttributeGroupDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/attribute/updateattributegroup/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new MasterAttributeGroupDto();
        }

        public async Task<MasterAttributeGroupDto> DeleteAttributeGroupAsync(MasterAttributeGroupDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterAttributeGroupDto, MasterAttributeGroupDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/attribute/deleteattributegroup/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new MasterAttributeGroupDto();
        }

        public async Task<MasterAttributeGroupDto> ReactivateAttributeGroupAsync(MasterAttributeGroupDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterAttributeGroupDto, MasterAttributeGroupDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/attribute/reactivateattributegroup/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new MasterAttributeGroupDto();
        }

        public async Task<MasterAttributeItemDto> UpdateAttributeItemAsync(MasterAttributeItemDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterAttributeItemDto, MasterAttributeItemDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/attribute/updateattributeitem/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new MasterAttributeItemDto();
        }

        public async Task<MasterAttributeItemDto> DeleteAttributeItemAsync(MasterAttributeItemDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterAttributeItemDto, MasterAttributeItemDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/attribute/deleteattributeitem/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new MasterAttributeItemDto();
        }

        public async Task<MasterAttributeItemDto> ReactivateAttributeItemAsync(MasterAttributeItemDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterAttributeItemDto, MasterAttributeItemDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/attribute/reactivateattributeitem/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new MasterAttributeItemDto();
        }

        public async Task<MasterAttributeItemDto> RelinkAttributeItemAsync(MasterAttributeItemDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterAttributeItemDto, MasterAttributeItemDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/attribute/relinkattributeitem/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new MasterAttributeItemDto();
        }

        public async Task<ResponseModel<List<MasterAttributeItemDto>>> GetAttributeAssignmentByGroupIdAsync(int AttributeGroupId)
        {
            var responseModel = new ResponseModel<List<MasterAttributeItemDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/attribute/getattributeassignmentbygroupid/{AttributeGroupId}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<MasterAttributeItemDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }

        public async Task<ResponseModel<List<MasterAttributeItemDto>>> GetAttributeAssignmentByGroupAndOptionIdAsync(int AttributeGroupId, int MasterOptionId, int? PlanOptionId)
        {
            var responseModel = new ResponseModel<List<MasterAttributeItemDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/attribute/getattributeassignmentbygroupandoptionid/{AttributeGroupId}/{MasterOptionId}/{PlanOptionId}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<MasterAttributeItemDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }

        public async Task<MasterAttributeGroupDto> AddAttributeGroupAsync(MasterAttributeGroupDto model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<MasterAttributeGroupDto, MasterAttributeGroupDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/attribute/addattributegroup/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex) 
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new MasterAttributeGroupDto();
        }

        public async Task<MasterAttributeItemDto> AddAttributeItemAsync(MasterAttributeItemDto model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<MasterAttributeItemDto, MasterAttributeItemDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/attribute/addattributeitem/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new MasterAttributeItemDto();
        }

        public async Task<OptionAttributeGroupItemDto> AddAttributeItemByAssignmentAndOption(OptionAttributeGroupItemDto model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<OptionAttributeGroupItemDto, OptionAttributeGroupItemDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/attribute/addattributeitembyassignmentandoption/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif            
            }

                return new OptionAttributeGroupItemDto();
        }

        public async Task<ResponseModel<List<MasterAttributeGroupDto>>> GetGroupByMasterOptionIdAsync(int MasterOptionId)
        {
            var responseModel = new ResponseModel<List<MasterAttributeGroupDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/attribute/getgroupbymasteroptionid/{MasterOptionId}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<MasterAttributeGroupDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                    var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                    _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                }

                return responseModel;
        }

        public async Task<MasterAttributeItemDto> DeleteOptionAttributeGroupItemAsync(MasterAttributeItemDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterAttributeItemDto, MasterAttributeItemDto>("DownstreamApi", model,
                      options =>
                      {
                          options.RelativePath = $"api/attribute/deleteoptionattributegroupitem/";
                      });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return null;
        }

        public async Task<MasterAttributeItemDto> ReactivateOptionAttributeGroupItemAsync(MasterAttributeItemDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterAttributeItemDto, MasterAttributeItemDto>("DownstreamApi", model,
                      options =>
                      {
                          options.RelativePath = $"api/attribute/reactivateoptionattributegroupitem/";
                      });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return null;
        }

        public async Task<MasterAttributeGroupDto> DeleteOptionAttributeGroupAsync(MasterAttributeGroupDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterAttributeGroupDto, MasterAttributeGroupDto>("DownstreamApi", model,
                      options =>
                      {
                          options.RelativePath = $"api/attribute/deleteoptionattributegroup/";
                      });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return null;
        }

        public async Task<MasterAttributeGroupDto> ReactivateOptionAttributeGroupAsync(MasterAttributeGroupDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterAttributeGroupDto, MasterAttributeGroupDto>("DownstreamApi", model,
                      options =>
                      {
                          options.RelativePath = $"api/attribute/reactivateoptionattributegroup/";
                      });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return null;
        }

        public async Task<MasterAttributeGroupDto> AddGroupToOption(MasterAttributeGroupDto model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<MasterAttributeGroupDto, MasterAttributeGroupDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/attribute/addgrouptooption/";
                             });

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new MasterAttributeGroupDto();
        }

        public async Task<ResponseModel<List<MasterAttributeItemDto>>> GetMasterAttributeItemsAsync()
        {
            var responseModel = new ResponseModel<List<MasterAttributeItemDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/attribute/getmasterattributeitems");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<MasterAttributeItemDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }

        public async Task<ResponseModel<List<MasterOptionDto>>> GetDistinctMasterOptionsInAvailablePlanOptionsAsync()
        {
            var responseModel = new ResponseModel<List<MasterOptionDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/attribute/getdistinctmasteroptionsinavailableplanoptions");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<MasterOptionDto>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }

        public async Task<DataEnvelope<MasterOptionDto>> GetDistinctMasterOptionsInAvailablePlanOptionsVirtualizeAsync(DataSourceRequest request)
        {
            var responseModel = new ResponseModel<List<MasterOptionDto>>();
            var dataToReturn = new DataEnvelope<MasterOptionDto>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/attribute/getdistinctmasteroptionsinavailableplanoptionsvirtualize");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<MasterOptionDto>>>(responseString);

                    var result = responseModel.Value.ToDataSourceResultAsync(request).Result;

                    dataToReturn = new DataEnvelope<MasterOptionDto>
                    {
                        Data = result.Data.Cast<MasterOptionDto>().ToList(),
                        Total = 0
                    };
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return dataToReturn;
        }

        public async Task<ResponseModel<List<OptionAndPlan>>> GetDistinctMasterPlansInAvailablePlanOptionsAsync(int optionId)
        {
            var responseModel = new ResponseModel<List<OptionAndPlan>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/attribute/getdistinctmasterplansinavailableplanoptions/{optionId}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<OptionAndPlan>>>(responseString);
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }
    }
}
