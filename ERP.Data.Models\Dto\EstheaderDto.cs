﻿using System;
using System.Collections.Generic;
using ERP.Data.Models.Abstract;
namespace ERP.Data.Models.Dto;

public partial class EstheaderDto : IMapFrom<Estheader> 
{
    public int EstheaderId { get; set; }
    public bool? IsIssued { get; set; }//to be used to track if the estdetails in the option are issued (ie they have estjcedetailid)

    public string JobNumber { get; set; } = null!;

    public int? EstimateNumber { get; set; }

    public string? EstimateDescPe { get; set; }

    public int? EstimateSource { get; set; }

    public string? AssociatedFile { get; set; }

    public string? ReferenceNumber { get; set; }

    public int? ReferenceType { get; set; }

    public string? ReferenceDesc { get; set; }

    public double? EstimateSalesPrice { get; set; }

    public string? Estimator { get; set; }

    public string? Heading1 { get; set; }

    public string? Heading2 { get; set; }

    public string? Heading3 { get; set; }

    public int? JobSize { get; set; }

    public string? JobUnit { get; set; }

    public int? SalesconfigId { get; set; }

    public int? SalesconfigcoId { get; set; }

    public string? VarianceJcCategory { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CubitId { get; set; }

    public int? EstbuildelementId { get; set; }

    public string? BasehouseCode { get; set; }

    public string? BasehouseDesc { get; set; }

    public string? WmsplanNum { get; set; }

    public string? PlanName { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

   // public byte[]? RecordTimeStamp { get; set; }

   // public virtual ICollection<Estcustoption> Estcustoptions { get; set; } = new List<Estcustoption>();

   // public virtual ICollection<Estoption> Estoptions { get; set; } = new List<Estoption>();

    public SalesconfigDto? Salesconfig { get; set; }

    public SalesconfigcoDto? Salesconfigco { get; set; }
    public ReferenceTypeDto? ReferenceTypeNavigation { get; set; }
    public EstimateSourceDto? EstimateSourceNavigation { get; set; } 
}
