﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class CalendarsDayDto : IMapFrom<CalendarsDay>
{
    public int CalendarsDaysId { get; set; }

    public int CalendarId { get; set; }

    public DateTime WorkDate { get; set; }

    public string? Description { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public bool IsAllDay { get; set; } = true;
    //public byte[] RecordTimeStamp { get; set; } = null!;
}
