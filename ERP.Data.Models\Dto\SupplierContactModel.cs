﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ERP.Data.Models.Validation;
namespace ERP.Data.Models.Dto;

public class SupplierContactModel
{
    public int? SubNumber { get; set; }
    public int? SupplierContactId { get; set; }
    public int ContactId { get; set; }

    public string? ContactKey { get; set; }

    [StringLength(30, ErrorMessage = "First Name must be 30 characters or less")]
    public string? FirstName { get; set; }

    [StringLength(30, ErrorMessage = "Last Name must be 30 characters or less")]
    public string? LastName { get; set; }

    public string? Address1 { get; set; }

    public string? Address2 { get; set; }

    public string? City { get; set; }

    public string? State { get; set; }

    public string? Postcode { get; set; }
    [CustomPhoneValidation]
    public string? WorkPhone { get; set; }

    [CustomPhoneValidation]
    public string? HomePhone { get; set; }
    [CustomPhoneValidation]
    public string? MobilePhone { get; set; }

    public string? Fax { get; set; }

    [CustomEmailValidation]
    public string? Email { get; set; }

    public string? ContactPassword { get; set; }

    public string? Country { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }
    public string? SupplierContactTitle { get; set; }

    public string? SupplierContactPurch { get; set; }
    public bool IsSupplierContactPurch { get; set; }
    public string? SupplierContactSched { get; set; }
    public bool IsSupplierContactSched { get; set; }
    public string? SupplierContactIsadmin { get; set; }
    public bool IsSupplierContactAdmin { get; set; }
    public int? SupplierContactNumber { get; set; }
}
