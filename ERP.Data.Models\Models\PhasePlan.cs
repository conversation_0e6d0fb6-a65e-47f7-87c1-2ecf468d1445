﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class PhasePlan
{
    public int PhasePlanId { get; set; }

    public int MasterPlanId { get; set; }

    public int SubdivisionId { get; set; }

    public string? Phase { get; set; }

    public int? PlanTypeId { get; set; }

    public decimal? PhasePlanPrice { get; set; }

    public decimal? PhasePlanCost { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStampe { get; set; } = null!;

    public int? MarginType { get; set; }

    public double? MarginPercent { get; set; }

    public double? MarginMarketValue { get; set; }

    public double? MarginLumpSum { get; set; }

    public double? LastCost { get; set; }

    public double? LastMargin { get; set; }

    public double? LastSelling { get; set; }

    public DateTime? LastDate { get; set; }

    public double? CurrCost { get; set; }

    public double? CurrMargin { get; set; }

    public double? CurrSelling { get; set; }

    public DateTime? CurrDate { get; set; }

    public double? NextCost { get; set; }

    public double? NextMargin { get; set; }

    public double? NextSelling { get; set; }

    public DateTime? NextDate { get; set; }

    public int? AreaElevationSource { get; set; }

    public string? AssociatedEstimate { get; set; }

    public int? WarningCount { get; set; }

    public int? MarketingPackageId { get; set; }

    public int? SqFt { get; set; }

    public int? BedRoom { get; set; }

    public decimal? BathRoom { get; set; }

    public string? ImageCode { get; set; }

    public string? GarageBays { get; set; }

    public int? FullBath { get; set; }

    public int? HalfBath { get; set; }

    public decimal? FinishedBasement { get; set; }

    public decimal? Deck { get; set; }

    public decimal? Patios { get; set; }

    public virtual ICollection<AddendaPhasePlan> AddendaPhasePlans { get; set; } = new List<AddendaPhasePlan>();

    public virtual ICollection<AvailablePlanOption> AvailablePlanOptions { get; set; } = new List<AvailablePlanOption>();

    public virtual ICollection<Contract> Contracts { get; set; } = new List<Contract>();

    public virtual ICollection<Job> Jobs { get; set; } = new List<Job>();

    public virtual MasterPlan MasterPlan { get; set; } = null!;

    public virtual ICollection<Salesconfig> Salesconfigs { get; set; } = new List<Salesconfig>();

    public virtual ICollection<Spec> Specs { get; set; } = new List<Spec>();

    public virtual Subdivision Subdivision { get; set; } = null!;

    public virtual ICollection<WorksheetPlan> WorksheetPlans { get; set; } = new List<WorksheetPlan>();
}
