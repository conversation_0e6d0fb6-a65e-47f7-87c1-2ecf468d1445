﻿
using AutoMapper;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Data;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class BCAPILogController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        public BCAPILogController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
        }

        [HttpGet]
        public async Task<IActionResult> GetAPILogsAsync()
        {
            try
            {
                //for display on the send to accounting page, so getting only the PO and Budget ones, and only past two weeks
                var logs = await _context.ErpBcApiLogs.Where(x => x.RequestUrl != null && !x.RequestUrl.Contains("$batch") && (x.Type == "Budget" || x.Type == "PO" || x.Type == "Credit Memo" || x.Type == "Post PO") && x.CreatedDateTime > DateTime.Now.AddDays(-14)).AsNoTracking().OrderByDescending(x => x.CreatedDateTime).ToListAsync();//batch is also logging the individual requests and responses
                var logsDto = _mapper.Map<List<ErpBcApiLogDto>>(logs);
                foreach(var log in logsDto)
                {
                    log.ResponseCode = log.ResponseCode != null && log.ResponseCode.StartsWith("20") || log.ResponseCode == "OK" ? "SUCCESS" : log.ResponseCode != null && log.ResponseCode.StartsWith("40") ? "FAIL" : log.ResponseCode;
                }
                return new OkObjectResult(new ResponseModel<List<ErpBcApiLogDto>> { IsSuccess = true, Value = logsDto, Message = "Got Logs" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ErpBcApiLogDto>> { IsSuccess = false, Message = "Failed to get logs", Value = null });
            }
        }
        
    }
}
