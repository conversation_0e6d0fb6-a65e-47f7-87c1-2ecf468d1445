﻿@using System.Collections.ObjectModel
@inject AttributeService AttributeService
@using Telerik.DataSource
@using Telerik.DataSource.Extensions

<div>
    <strong>Select option by double-clicking the item to make the selection:</strong>
    <TelerikMultiSelect OnRead="@GetRemoteData"
                        TextField="DisplayText"
                        ValueField="OptionId"
                        ValueChanged="@((List<int> s) => OnMasterOptionIdChanged(s))"
                        Filterable="true"
                        ScrollMode="@DropDownScrollMode.Virtual"
                        TItem="@MasterOptionDto" 
                        TValue="@int"
                        ItemHeight="30"
                        Width="300"
                        AutoClose="false"
                        PageSize="20"
                        FilterOperator="@StringFilterOperator.Contains"
                        Value="@CurrentOption.MasterOptions">
    </TelerikMultiSelect>
</div>
<div style="margin-top: 8px">
    <strong>Select plan based on the option selected above:</strong>
    <TelerikMultiSelect Value="@CurrentOption.MasterPlans"
                    Data="@CurrentMasterPlan"
                    TextField="PlanNum"
                    ValueField="MasterPlanId"
                    Filterable="true">
    </TelerikMultiSelect>
</div>
@code {
    private StringFilterOperator FilterOperator { get; set; } = StringFilterOperator.StartsWith;
    public List<MasterOptionDto>? MasterOptionList = new List<MasterOptionDto>();
    [Parameter] public int SelectedOptionId { get; set; }
    [Parameter] public EventCallback<int> SelectedOptionIdChanged { get; set; } // Naming convention: Add the Property from the child component + Changed. In this case, the child property is SelectedOptionId (from Attributes.razor)

    OptionAttributes CurrentOption { get; set; } = new OptionAttributes();
    public List<OptionAndPlan>? MasterPlanList = new List<OptionAndPlan>();
    ObservableCollection<OptionAndPlan>? CurrentMasterPlan = new ObservableCollection<OptionAndPlan>();

    // protected override async Task OnInitializedAsync()
    // {
    //     var masterOptionTask = AttributeService.GetDistinctMasterOptionsInAvailablePlanOptionsAsync();

    //     await Task.WhenAll(new Task[] { masterOptionTask });

    //     MasterOptionList = masterOptionTask.Result.Value;
    // }

    async Task GetRemoteData(MultiSelectReadEventArgs args)
    {
        // Virtualize. Too much data. Trade-off is double clicking the item to preventdefault().
        var masterOptionTask = await AttributeService.GetDistinctMasterOptionsInAvailablePlanOptionsVirtualizeAsync(args.Request);

        args.Data = masterOptionTask.Data;
        args.Total = masterOptionTask.Total;

        MasterOptionList = masterOptionTask.Data;
    }

    private async Task OnMasterOptionIdChanged(List<int> optionIds)
    {
        //SelectedOptionId = (int)selectedValue;
        //await SelectedOptionIdChanged.InvokeAsync(SelectedOptionId);

        if (optionIds.Count == 0)
        {
            CurrentOption = new OptionAttributes();
            return;
        }

        CurrentMasterPlan.Clear();

        foreach (var item in optionIds)
        {
            var masterPlanTask = AttributeService.GetDistinctMasterPlansInAvailablePlanOptionsAsync(item);
            await Task.WhenAll(new Task[] { masterPlanTask });
            MasterPlanList = masterPlanTask.Result.Value;

            var planForOptions = MasterPlanList.Where(x => x.MasterOptionId == item);

            foreach (var p in planForOptions)
            {
                CurrentMasterPlan.Add(p);
            }
        }

        CurrentMasterPlan.OrderBy(p => p.PlanName);

        CurrentOption.MasterOptions.Clear();
        CurrentOption.ChosenMasterOptions.Clear();

        foreach (var item in optionIds)
        {
            var selectedOption = MasterOptionList.Where(x => x.OptionId == item).First();
            CurrentOption.MasterOptions.Add(item);
            CurrentOption.ChosenMasterOptions.Add(selectedOption);
        }
    }
}