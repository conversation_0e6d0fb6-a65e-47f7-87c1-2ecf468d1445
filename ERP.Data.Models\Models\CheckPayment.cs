﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class CheckPayment
{
    public int CheckPaymentsId { get; set; }

    public string? BankAccount { get; set; }

    public string? CheckNumber { get; set; }

    public DateTime? CheckDate { get; set; }

    public double? CheckAmount { get; set; }

    public int SubNumber { get; set; }

    public string? InvNumber { get; set; }

    public double? InvAmountPaid { get; set; }

    public int PoheaderId { get; set; }

    public double? PoAmountPaid { get; set; }

    public int? PaymentType { get; set; }

    public string? VoidedPayment { get; set; }

    public double? VoidedAmount { get; set; }

    public DateTime? VoidedDate { get; set; }

    public int? AcntDbId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? FoundDuringUpdate { get; set; }

    public int? PaymentId { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public string? Notes { get; set; }

    public string? OldCheckNumber { get; set; }

    public virtual Poheader Poheader { get; set; } = null!;

    public virtual Supplier SubNumberNavigation { get; set; } = null!;
}
