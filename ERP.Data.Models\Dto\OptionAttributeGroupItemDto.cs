﻿using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class OptionAttributeGroupItemDto : IMapFrom<OptionAttributeGroupItem>
{
    public int OpAttrGroupItemId { get; set; }

    public int MasterOptionId { get; set; }

    public int AttrGroupAssignmentId { get; set; }

    public decimal? PriceChange { get; set; }

    public int? Seq { get; set; }

    public bool? DefaultSelection { get; set; }

    public bool? IsActive { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? Updatedby { get; set; }

    public int? PlanOptionId { get; set; }

    public string? PlanNum { get; set; }

    public int? MasterPlanId { get; set; }

    public AttrGroupAssignmentDto? AttrGroupAssignment { get; set; }

    public string? ErrorMessage { get; set; }

    public int? AttributeGroupId { get; set; }

    //public virtual ICollection<BuildAttributeItem> BuildAttributeItems { get; set; } = new List<BuildAttributeItem>();

    //public virtual AvailablePlanOption? PlanOption { get; set; }

    //public virtual ICollection<SalesconfigcooptionsAttribute> SalesconfigcooptionsAttributes { get; set; } = new List<SalesconfigcooptionsAttribute>();

    //public virtual ICollection<SalesconfigoptionsAttribute> SalesconfigoptionsAttributes { get; set; } = new List<SalesconfigoptionsAttribute>();
}
