﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class SchePackageAssignment
{
    public int ItemAssignmentId { get; set; }

    public int PackageItemId { get; set; }

    public string? DepartmentAssigned { get; set; }

    public string? Manager { get; set; }

    public string? Notes { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual SchePackageItem PackageItem { get; set; } = null!;
}
