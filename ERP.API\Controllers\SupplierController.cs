﻿
using AutoMapper;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using System.Data;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class SupplierController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        private readonly IWebHostEnvironment _env;
        public SupplierController(IConfiguration configuration, ErpDevContext context, IMapper mapper, IWebHostEnvironment env)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _env = env;
        }
        /// <summary>
        /// update from bc,update or create
        /// </summary>
        /// <param name="updateSupplier"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SupplierAsync([FromBody] BCSupplierModel updateSupplier)
        {
            try
            {
                //BC has subapnumbe (same as supplier short name), not id
                var findSupplier = _context.Suppliers.SingleOrDefault(x => x.SubApNumber == updateSupplier.SubApNumber);
                if(findSupplier != null)
                {
                    findSupplier.ShortName = updateSupplier.ShortName ?? findSupplier.ShortName;
                    findSupplier.SubName = updateSupplier.SubName ?? findSupplier.SubName;
                    findSupplier.SubCity = updateSupplier.SubCity ?? findSupplier.SubCity;
                    findSupplier.SubPhone = updateSupplier.SubPhone ?? findSupplier.SubPhone;
                    findSupplier.SubPhone2 = updateSupplier.SubPhone2 ?? findSupplier.SubPhone2;
                    findSupplier.SubCountry = updateSupplier.SubCountry ?? findSupplier.SubCountry;
                    findSupplier.SubState = updateSupplier.SubState ?? findSupplier.SubState;
                    findSupplier.SubFax = updateSupplier.SubFax ?? findSupplier.SubFax;
                    findSupplier.SubContact = updateSupplier.SubContact ?? findSupplier.SubContact;
                    findSupplier.SubContact2 = updateSupplier.SubContact2 ?? findSupplier.SubContact2;
                    findSupplier.SubContact3 = updateSupplier.SubContact3 ?? findSupplier.SubContact3;
                    findSupplier.SubPostcode = updateSupplier.SubPostcode ?? findSupplier.SubPostcode;
                    findSupplier.SubAddress = updateSupplier.SubAddress ?? findSupplier.SubAddress;
                    findSupplier.SubAddress2 = updateSupplier.SubAddress2 ?? findSupplier.SubAddress2;
                    findSupplier.SubApNumber = updateSupplier.SubApNumber ?? findSupplier.SubApNumber;
                    findSupplier.Email = updateSupplier.Email ?? findSupplier.Email;                  
                    findSupplier.IsActive = updateSupplier.LinkWithErp == false ? "F" : "T";//deactivate if not link with erp
                    findSupplier.IsActive1 = updateSupplier.LinkWithErp == false ? false : true;
                    findSupplier.Blocked = updateSupplier.Blocked ?? findSupplier.Blocked;
                    findSupplier.UpdatedBy = updateSupplier.UpdatedBy;
                    findSupplier.UpdatedDateTime = DateTime.Now;
                    _context.Suppliers.Update(findSupplier);
                    await _context.SaveChangesAsync();
                    var returnSupplier = _mapper.Map<BCSupplierModel>(findSupplier);
                    return new OkObjectResult(new ResponseModel<BCSupplierModel> { IsSuccess = true, Message = "Updated supplier successfully", Value = returnSupplier });
                }
                else
                {
                    var addSupplier = _mapper.Map<Supplier>(updateSupplier);
                    addSupplier.ShortName = updateSupplier.SubApNumber;//BC will pass this in, it is Id in BC
                    addSupplier.IsActive = updateSupplier.LinkWithErp == false ? "F" : "T";//deactivate if not link with erp
                    addSupplier.IsActive1 = updateSupplier.LinkWithErp == false ? false : true;
                    addSupplier.CreatedBy = updateSupplier.CreatedBy;
                    _context.Suppliers.Add(addSupplier);
                    await _context.SaveChangesAsync();
                    var returnSupplier = _mapper.Map<BCSupplierModel>(addSupplier);
                    return new OkObjectResult(new ResponseModel<BCSupplierModel> { IsSuccess = true, Message = "Created Supplier successfully", Value = returnSupplier });
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                //log to bc api log
                string baseRequestURL = _env.IsDevelopment() ? "https://vmerpservices-dev.azurewebsites.net/api/" : "https://vmerpservices.azurewebsites.net/api/";
                var jsonItem = JsonConvert.SerializeObject(updateSupplier);
                var newLog = new ErpBcApiLog()
                {
                    Method = "POST",
                    Type = "Supplier",
                    RequestUrl = $"{baseRequestURL}supplier/Supplier",
                    RequestBody = jsonItem,
                    ResponseBody = $"Failed to add or update Job. Error: {ex.Message} {ex.InnerException?.Message}",
                    ResponseCode = "500",
                    CreatedBy = updateSupplier?.CreatedBy,
                    CreatedDateTime = DateTime.Now,
                };
                _context.ErpBcApiLogs.Add(newLog);
                await _context.SaveChangesAsync();
                return StatusCode(500, new ResponseModel<BCSupplierModel> { IsSuccess = false, Message = "Failed to create or update supplier", Error = $"Error: {ex.Message} {ex.InnerException.Message}", Value = null });
            }
        }
        
        /// <summary>
        /// update from bc
        /// </summary>
        /// <param name="createSupplierContact"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SupplierContactAsync([FromBody] SupplierContactDto createSupplierContact)
        {
            try
            {
                var findSupplier = await _context.Suppliers.FirstOrDefaultAsync(x => x.ShortName == createSupplierContact.SupplierShortName);//supplier short name is id
                if (findSupplier == null)
                {

                    return BadRequest(new ResponseModel<SupplierContactDto> { IsSuccess = false, Message = $"Could not find supplier {createSupplierContact.SupplierShortName}", Value = null });
                }
                if (createSupplierContact.BcContactId == null)
                {
                    return BadRequest(new ResponseModel<SupplierContactDto> { IsSuccess = false, Message = $"Missing BC contact Id", Value = null });
                }
                    
                var findSupplierContact = await _context.SupplierContacts.FirstOrDefaultAsync(x => x.BcContactId == createSupplierContact.BcContactId && x.SubNumber == findSupplier.SubNumber);

                if (findSupplierContact == null)
                {
                    //add if it is new
                    var addContact = _mapper.Map<Contact>(createSupplierContact.Contact);
                    addContact.CreatedBy = createSupplierContact.CreatedBy;
                    await _context.Contacts.AddAsync(addContact);
                    await _context.SaveChangesAsync();

                    createSupplierContact.SubNumber = findSupplier.SubNumber;
                    createSupplierContact.ContactId = addContact.ContactId;
                    createSupplierContact.CreatedBy = createSupplierContact.CreatedBy;//BC should pass in user
                    var addSupplierContact = _mapper.Map<SupplierContact>(createSupplierContact);
                    _context.SupplierContacts.Add(addSupplierContact);
                    await _context.SaveChangesAsync();
                    findSupplierContact = addSupplierContact;

                    var returnSupplier = _mapper.Map<SupplierContactDto>(findSupplierContact);
                    return new OkObjectResult(new ResponseModel<SupplierContactDto> { IsSuccess = true, Message = "Created Supplier contact successfully", Value = returnSupplier });
                }
                else
                {
                    //update contact 
                    var findContact  = _context.Contacts.FirstOrDefault(x => x.ContactId == findSupplierContact.ContactId);
                    if(findContact == null)
                    {
                        //return 
                        return StatusCode(500, new ResponseModel<SupplierContactDto> { IsSuccess = false, Message = $"Failed to update supplier contact.", Error = "Could not find contact", Value = null });
                    }
                    else
                    {
                        //update the contact
                        findContact.ContactKey = createSupplierContact.Contact?.ContactKey ?? findContact.ContactKey;
                        findContact.FirstName = createSupplierContact.Contact?.FirstName ?? findContact.FirstName;
                        findContact.LastName = createSupplierContact.Contact?.LastName ?? findContact.LastName;
                        findContact.Address1 = createSupplierContact.Contact?.Address1 ?? findContact.Address1;
                        findContact.Address2 = createSupplierContact.Contact?.Address2 ?? findContact.Address2;
                        findContact.City = createSupplierContact.Contact?.City ?? findContact.City;
                        findContact.State = createSupplierContact.Contact?.State ?? findContact.State;
                        findContact.Postcode = createSupplierContact.Contact?.Postcode ?? findContact.Postcode;
                        findContact.WorkPhone = createSupplierContact.Contact?.WorkPhone ?? findContact.WorkPhone;
                        findContact.HomePhone = createSupplierContact.Contact?.HomePhone ?? findContact.HomePhone;
                        findContact.MobilePhone = createSupplierContact.Contact?.MobilePhone ?? findContact.MobilePhone;
                        findContact.Fax = createSupplierContact.Contact?.Fax ?? findContact.Fax;
                        findContact.Email = createSupplierContact.Contact?.Email ?? findContact.Email;
                        findContact.Country = createSupplierContact.Contact?.Country ?? findContact.Country;
                        findContact.IsActive = createSupplierContact.Contact?.IsActive ?? findContact.IsActive;

                        findSupplierContact.UpdatedBy = createSupplierContact.UpdatedBy;
                        findSupplierContact.UpdatedDateTime = DateTime.Now;

                        _context.Contacts.Update(findContact);
                        await _context.SaveChangesAsync();
                    }
                    //update supplier contact --BC wont have these
                    findSupplierContact.SupplierContactPurch = createSupplierContact.SupplierContactPurch ?? findSupplierContact.SupplierContactPurch;
                    findSupplierContact.SupplierContactNumber = createSupplierContact.SupplierContactNumber ?? findSupplierContact.SupplierContactNumber;
                    findSupplierContact.SupplierContactIsadmin = createSupplierContact.SupplierContactIsadmin ?? findSupplierContact.SupplierContactIsadmin;
                    findSupplierContact.SupplierContactTitle = createSupplierContact.SupplierContactTitle ?? findSupplierContact.SupplierContactTitle;
                    findSupplierContact.SupplierContactSched = createSupplierContact.SupplierContactSched ?? findSupplierContact.SupplierContactSched;

                    findSupplier.UpdatedBy = createSupplierContact.UpdatedBy;//BC should pass in user
                    findSupplier.UpdatedDateTime = DateTime.Now;

                    _context.Suppliers.Update(findSupplier);
                    await _context.SaveChangesAsync();

                    var returnSupplier = _mapper.Map<SupplierContactDto>(findSupplierContact);
                    return new OkObjectResult(new ResponseModel<SupplierContactDto> { IsSuccess = true, Message = "Updated Supplier contact successfully", Value = returnSupplier });
                }

                
                
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                //log to bc api log
                string baseRequestURL = _env.IsDevelopment() ? "https://vmerpservices-dev.azurewebsites.net/api/" : "https://vmerpservices.azurewebsites.net/api/";
                var jsonItem = JsonConvert.SerializeObject(createSupplierContact);
                var newLog = new ErpBcApiLog()
                {
                    Method = "POST",
                    Type = "Supplier Contact",
                    RequestUrl = $"{baseRequestURL}supplier/SupplierContact",
                    RequestBody = jsonItem,
                    ResponseBody = $"Failed to add or update Job. Error: {ex.Message} {ex.InnerException?.Message}",
                    ResponseCode = "500",
                    CreatedBy = createSupplierContact?.CreatedBy,
                    CreatedDateTime = DateTime.Now,
                };
                _context.ErpBcApiLogs.Add(newLog);
                await _context.SaveChangesAsync();
                return StatusCode(500, new ResponseModel<SupplierContactDto> { IsSuccess = false, Message = $"Failed to create supplier contact.",  Error = $"{ex.Message} {ex.InnerException?.Message}", Value = null });
            }
        }
        /// <summary>
        /// update from bc, create or update
        /// </summary>
        /// <param name="updateSupplierInsurance"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SupplierInsuranceAsync([FromBody] SupplierInsuranceDto updateSupplierInsurance)
        {
            try
            {
                //subnav number is supplier short name/subapnumber, and is pk for supplier insurance table
                var findInsurance = _context.SupplierInsurances.FirstOrDefault(x => x.LineNo == updateSupplierInsurance.LineNo && x.SubNavNumber == updateSupplierInsurance.SubNavNumber);
                if(findInsurance != null) 
                {
                    //update
                    var findSupplier = _context.Suppliers.FirstOrDefault(x => x.SubApNumber == updateSupplierInsurance.SubNavNumber);
                    findInsurance.SubNumber = findSupplier?.SubNumber;
                    findInsurance.LineNo = updateSupplierInsurance.LineNo;
                    findInsurance.InsuranceTypeCode = updateSupplierInsurance.InsuranceTypeCode ?? findInsurance.InsuranceTypeCode;
                    findInsurance.InsuranceRequired = updateSupplierInsurance.InsuranceRequired;
                    findInsurance.PolicyStartDate = updateSupplierInsurance.PolicyStartDate == default || updateSupplierInsurance.PolicyStartDate == null ? DateTime.Parse("01/01/1753") : updateSupplierInsurance.PolicyStartDate.Value;//TODO: require start and expiration dates. filling default for now
                    findInsurance.PolicyExpirationDate = updateSupplierInsurance.PolicyExpirationDate == default || updateSupplierInsurance.PolicyExpirationDate == null ? DateTime.Parse("01/01/1753") : updateSupplierInsurance.PolicyExpirationDate.Value;
                    findInsurance.PolicyNumber = updateSupplierInsurance.PolicyNumber ?? findInsurance.PolicyNumber;
                    findInsurance.InsuranceContactName = updateSupplierInsurance.InsuranceContactName ?? findInsurance.InsuranceContactName;
                    findInsurance.InsuranceCompanyName = updateSupplierInsurance.InsuranceCompanyName ?? findInsurance.InsuranceCompanyName;
                    findInsurance.Coverageamount = updateSupplierInsurance.Coverageamount;
                    findInsurance.InsuranceContactPhoneNo = updateSupplierInsurance.InsuranceContactPhoneNo ?? findInsurance.InsuranceContactPhoneNo;
                    findInsurance.InsuranceTypeCode = updateSupplierInsurance.InsuranceTypeCode?.ToUpper();
                    findInsurance.Updatedby = updateSupplierInsurance.Updatedby;//BC should pass in the user
                    findInsurance.Updateddatetime = DateTime.Now;
                    _context.SupplierInsurances.Update(findInsurance);
                    await _context.SaveChangesAsync();
                    var returnInsurance = _mapper.Map<SupplierInsuranceDto>(findInsurance);
                    return new OkObjectResult(new ResponseModel<SupplierInsuranceDto>() { IsSuccess = true, Message = "Updated insurance successfully", Value = returnInsurance });
                }
                else
                {
                    //add
                    var findSupplier = _context.Suppliers.FirstOrDefault(x => x.SubApNumber == updateSupplierInsurance.SubNavNumber);
                    var addInsurance = _mapper.Map<SupplierInsurance>(updateSupplierInsurance);
                    addInsurance.Createddatetime = DateTime.Now;
                    addInsurance.SubNumber = findSupplier?.SubNumber;
                    addInsurance.InsuranceTypeCode = updateSupplierInsurance.InsuranceTypeCode?.ToUpper();
                    addInsurance.PolicyStartDate = updateSupplierInsurance.PolicyStartDate == default || updateSupplierInsurance.PolicyStartDate == null ? DateTime.Parse("01/01/1753") : updateSupplierInsurance.PolicyStartDate.Value;//TODO: require start and expiration dates. filling default for now//TODO: require start and expiration dates. filling default for now
                    addInsurance.PolicyExpirationDate = updateSupplierInsurance.PolicyExpirationDate == default || updateSupplierInsurance.PolicyExpirationDate == null ? DateTime.Parse("01/01/1753") : updateSupplierInsurance.PolicyExpirationDate.Value;
                    await _context.SupplierInsurances.AddAsync(addInsurance);
                    await _context.SaveChangesAsync();
                    var returnInsurance = _mapper.Map<SupplierInsuranceDto>(addInsurance);
                    return new OkObjectResult(new ResponseModel<SupplierInsuranceDto>() { IsSuccess = true, Message = "Created insurance successfully", Value = returnInsurance });
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                //log to bc api log
                string baseRequestURL = _env.IsDevelopment() ? "https://vmerpservices-dev.azurewebsites.net/api/" : "https://vmerpservices.azurewebsites.net/api/";
                var jsonItem = JsonConvert.SerializeObject(updateSupplierInsurance);
                var newLog = new ErpBcApiLog()
                {
                    Method = "POST",
                    Type = "Supplier Insurance",
                    RequestUrl = $"{baseRequestURL}supplier/SupplierInsurance",
                    RequestBody = jsonItem,
                    ResponseBody = $"Failed to add or update supplier insurance. Error: {ex.Message} {ex.InnerException?.Message}",
                    ResponseCode = "500",
                    CreatedBy = updateSupplierInsurance?.Updatedby,
                    CreatedDateTime = DateTime.Now,
                };
                _context.ErpBcApiLogs.Add(newLog);
                await _context.SaveChangesAsync();
                return StatusCode(500, new ResponseModel<SupplierInsuranceDto> { IsSuccess = false, Message = "Failed to create supplier Insurance ", Error = $"{ex.Message} {ex.InnerException?.Message}", Value = null });
            }
        }
        
        /// <summary>
        /// update from bc
        /// </summary>
        /// <param name="createContact"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ContactAsync([FromBody] ContactDto createContact)
        {
            try
            {
                var addContact = _mapper.Map<Contact>(createContact);
                var findContact = await _context.Contacts.FirstOrDefaultAsync(x => x.ContactKey == addContact.ContactKey);//Contact key is email
                if (findContact == null)
                {
                    await _context.Contacts.AddAsync(addContact);
                    await _context.SaveChangesAsync();
                    findContact = addContact;
                }                
                var returnContact = _mapper.Map<ContactDto>(findContact);
                return new OkObjectResult(new ResponseModel<ContactDto> (){ IsSuccess = true, Message = "Created Contact successfully", Value = returnContact });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                //log to bc api log
                string baseRequestURL = _env.IsDevelopment() ? "https://vmerpservices-dev.azurewebsites.net/api/" : "https://vmerpservices.azurewebsites.net/api/";
                var jsonItem = JsonConvert.SerializeObject(createContact);
                var newLog = new ErpBcApiLog()
                {
                    Method = "POST",
                    Type = "Supplier Contact",
                    RequestUrl = $"{baseRequestURL}supplier/Contact",
                    RequestBody = jsonItem,
                    ResponseBody = $"Failed to add or update Contact. Error: {ex.Message} {ex.InnerException?.Message}",
                    ResponseCode = "500",
                    CreatedBy = createContact?.CreatedBy,
                    CreatedDateTime = DateTime.Now,
                };
                _context.ErpBcApiLogs.Add(newLog);
                await _context.SaveChangesAsync();
                return StatusCode(500, new ResponseModel<JobDto> { IsSuccess = false, Message = "Failed to create Contact", Value = null });
            }
        }
        /// <summary>
        /// update from bc
        /// </summary>
        /// <param name="supplierId"></param>
        /// <returns></returns>
        [HttpPatch("{supplierShortName}")]
        public async Task<IActionResult> SupplierContactAsync(string supplierShortName, [FromBody] SupplierContactDto updateSupplier)
        {
            try
            {
                //TODO: BC wont' have supplier Id, using short name
                var findSupplier = await _context.Suppliers.FirstOrDefaultAsync(x => x.ShortName == supplierShortName);
                var findContact = await _context.Contacts.FirstOrDefaultAsync(x => x.ContactKey == supplierShortName);
                var findSupplierContact = _context.SupplierContacts.SingleOrDefault(x => x.SubNumber == findSupplier.SubNumber && x.ContactId == findContact.ContactId);
                findSupplierContact.SupplierContactPurch = updateSupplier.SupplierContactPurch ?? findSupplierContact.SupplierContactPurch;
                findSupplierContact.SupplierContactNumber = updateSupplier.SupplierContactNumber ?? findSupplierContact.SupplierContactNumber;
                findSupplierContact.SupplierContactIsadmin = updateSupplier.SupplierContactIsadmin ?? findSupplierContact.SupplierContactIsadmin;
                findSupplierContact.SupplierContactTitle = updateSupplier.SupplierContactTitle ?? findSupplierContact.SupplierContactTitle;
                findSupplierContact.SupplierContactSched = updateSupplier.SupplierContactSched ?? findSupplierContact.SupplierContactSched;

               
                findSupplier.UpdatedBy = "BC integration";
                findSupplier.UpdatedDateTime = DateTime.Now;
                //TODO: all the fields, what should update
                _context.Suppliers.Update(findSupplier);
                await _context.SaveChangesAsync();
                var returnSupplier = _mapper.Map<SupplierDto>(findSupplier);
                return new OkObjectResult(new ResponseModel<SupplierDto> { IsSuccess = true, Message = "Updated supplier successfully", Value = returnSupplier });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                //log to bc api log
                string baseRequestURL = _env.IsDevelopment() ? "https://vmerpservices-dev.azurewebsites.net/api/" : "https://vmerpservices.azurewebsites.net/api/";
                var jsonItem = JsonConvert.SerializeObject(updateSupplier);
                var newLog = new ErpBcApiLog()
                {
                    Method = "PATCH",
                    Type = "Supplier Contact",
                    RequestUrl = $"{baseRequestURL}supplier/SupplierContact",
                    RequestBody = jsonItem,
                    ResponseBody = $"Failed to add or update Supplier Contact. Error: {ex.Message} {ex.InnerException?.Message}",
                    ResponseCode = "500",
                    CreatedBy = updateSupplier?.CreatedBy,
                    CreatedDateTime = DateTime.Now,
                };
                _context.ErpBcApiLogs.Add(newLog);
                await _context.SaveChangesAsync();
                return StatusCode(500, new ResponseModel<JobDto> { IsSuccess = false, Message = $"Failed to update supplier {ex.Message} {ex.InnerException?.Message}", Value = null });
            }
        }
        /// <summary>
        /// update from bc
        /// </summary>
        /// <param name="contactKey"></param>
        /// <returns></returns>
        [HttpPatch("{supplierShortName}")]
        public async Task<IActionResult> ContactAsync(string contactKey, [FromBody] ContactDto updateContact)
        {
            try
            {
                //BC wont' have contact Id, using contact key
                var findContact = _context.Contacts.SingleOrDefault(x => x.ContactKey == contactKey);
                findContact.ContactKey = updateContact.ContactKey ?? findContact.ContactKey;
                findContact.FirstName = updateContact.FirstName ?? findContact.FirstName;
                findContact.LastName = updateContact.LastName ?? findContact.LastName;
                findContact.Address1 = updateContact.Address1 ?? findContact.Address1;
                findContact.Address2 = updateContact.Address2 ?? findContact.Address2;
                findContact.City = updateContact.City ?? findContact.City;
                findContact.State = updateContact.State ?? findContact.State;
                findContact.Postcode = updateContact.Postcode ?? findContact.Postcode;
                findContact.WorkPhone = updateContact.WorkPhone ?? findContact.WorkPhone;
                findContact.HomePhone = updateContact.HomePhone ?? findContact.HomePhone;
                findContact.MobilePhone = updateContact.MobilePhone ?? findContact.MobilePhone;
                findContact.Fax = updateContact.Fax ?? findContact.Fax;
                findContact.Email = updateContact.Email ?? findContact.Email;
                findContact.Country = updateContact.Country ?? findContact.Country;
                
                findContact.IsActive = updateContact.IsActive ?? findContact.IsActive;

                findContact.UpdatedBy = "BC integration";
                findContact.UpdatedDateTime = DateTime.Now;
                //TODO: all the fields, what should update
                _context.Contacts.Update(findContact);
                await _context.SaveChangesAsync();
                var returnContact = _mapper.Map<ContactDto>(findContact);
                return new OkObjectResult(new ResponseModel<ContactDto> { IsSuccess = true, Message = "Updated contact successfully", Value = returnContact });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                //log to bc api log
                string baseRequestURL = _env.IsDevelopment() ? "https://vmerpservices-dev.azurewebsites.net/api/" : "https://vmerpservices.azurewebsites.net/api/";
                var jsonItem = JsonConvert.SerializeObject(updateContact);
                var newLog = new ErpBcApiLog()
                {
                    Method = "PATCH",
                    Type = "Supplier Contact",
                    RequestUrl = $"{baseRequestURL}supplier/Contact",
                    RequestBody = jsonItem,
                    ResponseBody = $"Failed to add or update Contact. Error: {ex.Message} {ex.InnerException?.Message}",
                    ResponseCode = "500",
                    CreatedBy = updateContact?.CreatedBy,
                    CreatedDateTime = DateTime.Now,
                };
                _context.ErpBcApiLogs.Add(newLog);
                await _context.SaveChangesAsync();
                return StatusCode(500, new ResponseModel<ContactDto> { IsSuccess = false, Message = $"Failed to update contact {ex.Message} {ex.InnerException?.Message}", Value = null });
            }
        }

        /// <summary>
        /// get supplier insurance
        /// </summary>
        /// <param name="updateSupplierInsurance"></param>
        /// <returns></returns>
        [HttpGet("{subNum}")]
        public async Task<IActionResult> GetSupplierInsuranceAsync(int subNum)
        {
            try
            {                
                //subnav number is supplier short name/subapnumber, and is pk for supplier insurance table
                var findInsurance = await _context.SupplierInsurances.Include(x => x.InsuranceTypeCodeNavigation).Where(x => x.SubNumber == subNum && x.IsActive== true && x.InsuranceRequired == 1).ToListAsync();
                var insurancesDto = _mapper.Map<List<SupplierInsuranceDto>>(findInsurance);
                foreach(var insurance in insurancesDto)
                {
                    insurance.PolicyStartDate = insurance.PolicyStartDate != DateTime.Parse("01/01/1753") ? insurance.PolicyStartDate : null;
                    insurance.PolicyExpirationDate = insurance.PolicyExpirationDate != DateTime.Parse("01/01/1753") ? insurance.PolicyExpirationDate : null;
                }
                return Ok(new ResponseModel<List<SupplierInsuranceDto>> {Value = insurancesDto, Message = "Fetched supplier insurance data", IsSuccess = true});
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SupplierInsuranceDto> { IsSuccess = false, Message = "Failed to fetch Insurance data ", Error = $"{ex.Message} {ex.InnerException?.Message}", Value = null });
            }
        }

    }
}
