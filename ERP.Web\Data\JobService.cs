﻿using Azure.Core;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.ExtendedProperties;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Models;
using ERP.Web.Pages;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using NLog;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using Telerik.Blazor.Components.FileSelect;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;
using ERP.Web.Components;

namespace ERP.Web.Data
{
    public class JobService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public JobService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<ResponseModel<List<LotStatusDto>>> GetJobStatusList()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/job/JobStatus");
                var responseString = await response.Content.ReadAsStringAsync();
                var statuses = JsonConvert.DeserializeObject<ResponseModel<List<LotStatusDto>>>(responseString);
                return statuses;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<LotStatusDto>>() { IsSuccess = false, Value = new List<LotStatusDto>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<JobPostingGroupDto>>> GetJobPostingGroups()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/job/JobPostingGroups");
                var responseString = await response.Content.ReadAsStringAsync();
                var jobPostingGroups = JsonConvert.DeserializeObject<ResponseModel<List<JobPostingGroupDto>>>(responseString);
                return jobPostingGroups;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<JobPostingGroupDto>>() { IsSuccess = false, Value = new List<JobPostingGroupDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<GarageOrientationDto>>> GetGarageOrientationsList()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/job/GarageOrientations");
                var responseString = await response.Content.ReadAsStringAsync();
                var orientations = JsonConvert.DeserializeObject<ResponseModel<List<GarageOrientationDto>>>(responseString);
                return orientations;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<GarageOrientationDto>>() { IsSuccess = false, Value = new List<GarageOrientationDto>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<JobConstructionTypeDto>>> GetJobConstructionTypes()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/job/JobConstructionTypes");
                var responseString = await response.Content.ReadAsStringAsync();
                var types = JsonConvert.DeserializeObject<ResponseModel<List<JobConstructionTypeDto>>>(responseString);
                return types;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<JobConstructionTypeDto>>() { IsSuccess = false, Value = new List<JobConstructionTypeDto>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<JobDto>> TestMultipleAsync()
        {


            try
            {
                var sw = new Stopwatch();
                sw.Start();
                for (int i = 0; i < 500; i++)
                {
                    var updateJob = new JobDto()
                    {
                        JobNumber = $"TESTJOB{i}",
                        SubdivisionNum = "1-111507",
                        JobAddress1 = $" Job address {i}",
                        LinkWithErp = true
                    };
                    var response = await _downstreamAPI.PostForUserAsync<JobDto, ResponseModel<JobDto>>(
                     "DownstreamApi", updateJob,
                     options =>
                     {
                         options.RelativePath = $"api/job/Job/";
                     });
                }
                sw.Stop();
                var test = sw.Elapsed;
                var testList = new List<JobDto>();

                var sw2 = new Stopwatch();
                sw2.Start();
                for (int i = 0; i < 500; i++)
                {
                    testList.Add(new JobDto()
                    {
                        JobNumber = $"TESTJOB{i}",
                        SubdivisionNum = "1-111507",
                        JobAddress1 = $" Job address {i}",
                        LinkWithErp = true
                    });
                    
                }
                var response2 = await _downstreamAPI.PostForUserAsync<List<JobDto>, ResponseModel<List<JobDto>>>(
                     "DownstreamApi", testList,
                     options =>
                     {
                         options.RelativePath = $"api/job/Jobs/";
                     });
                return new ResponseModel<JobDto> { IsSuccess = true, Value = new JobDto() , Message = $"done. took {test} sec"};
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<JobDto>() { IsSuccess = false,  Message = "Failed" };
        }
    }
}
