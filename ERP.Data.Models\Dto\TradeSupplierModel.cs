﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ERP.Data.Models.Validation;
namespace ERP.Data.Models.Dto;

public class TradeSupplierModel
{
   // [Required(ErrorMessage = "Trade is required")]
    public int? TradeId { get; set; }
    public int? OldTradeId { get; set; }
    public int? SubdivisionId { get; set; }
    public List<int>? SubdivisionIds { get; set; } //For add supplier to multiple subdivisions
    public string? SubdivisionName { get; set; }
    public string? Type { get; set; }//eg Design Build, Construction, etc. This field will hold comma separated list from Supplier_Type
    public string? DefaultSupplier { get; set; }
    public bool IsDefault { get; set; }
    public string? AreaName { get; set; }
    public string? TradeName { get; set; }
    public string? TradeDesc { get; set; }
    public int SubNumber { get; set; }

    [StringLength(20, ErrorMessage = "Short Name must be 20 characters or less")]
    public string? ShortName { get; set; }

    [StringLength(30, ErrorMessage = "Supplier Name must be 30 characters or less")]
    public string? SubName { get; set; }

    [StringLength(10, ErrorMessage = "Supplier Name must be 10 characters or less")]
    public string? SubApNumber { get; set; }

    public string? SubAddress { get; set; }

    public string? SubAddress2 { get; set; }

    [StringLength(30, ErrorMessage = "Supplier City must be 30 characters or less")]
    public string? SubCity { get; set; }

    [StringLength(4, ErrorMessage = "Supplier State must be 4 characters or less")]
    public string? SubState { get; set; }

    public string? SubCountry { get; set; }

    public string? SubPostcode { get; set; }

    //[CustomPhoneValidation]
    public string? SubPhone { get; set; }

    //[CustomPhoneValidation]
    public string? SubPhone2 { get; set; }

    //[CustomPhoneValidation]
    public string? SubPhone3 { get; set; }

    public string? SubFax { get; set; }

    public string? SubFax2 { get; set; }

    public string? SubContact { get; set; }

    public string? SubContact2 { get; set; }

    public string? SubContact3 { get; set; }

    public string? SubContact1Pos { get; set; }

    public string? SubContact2Pos { get; set; }

    public string? SubContact3Pos { get; set; }

    public string? SubNotes { get; set; }

    [StringLength(20, ErrorMessage = "Type 1 must be 20 characters or less")]
    public string? SubType1 { get; set; }

    [StringLength(10, ErrorMessage = "Type 2 must be 10 characters or less")]
    public string? SubType2 { get; set; }

    [StringLength(10, ErrorMessage = "Type 3 must be 10 characters or less")]
    public string? SubType3 { get; set; }

    [StringLength(10, ErrorMessage = "Type 4 must be 10 characters or less")]
    public string? SubType4 { get; set; }

    public int? OrderMode { get; set; }

    public string? EdiOrders { get; set; }

    // [CustomEmailValidation]
    public string? Email { get; set; }

    public int? Ediorderbatchno { get; set; }

    [StringLength(20, ErrorMessage = "AR Code must be 20 characters or less")]
    public string? Supplierarcode { get; set; }

    public string? EdiInterchange { get; set; }

    public string? EdiGscode { get; set; }

    public string? Subtaxgroup { get; set; }

    public string? Subtaxexempt { get; set; }
    public bool IsSubtaxexempt { get; set; }

    public double? DiscountPercent { get; set; }

    public int? DiscountDays { get; set; }

    public int? PaymentDays { get; set; }

    public string? PmtDaysType { get; set; }

    public double? MiscDeductRate { get; set; }

    public double? MiscDeduct2Rate { get; set; }

    public string? PrefilledTaxAmount { get; set; }

    public string? ReceivesForm1099 { get; set; }
    public bool IsReceivesForm1099 { get; set; }

    public string? AbnNumber { get; set; }

    //[CustomPhoneValidation]//removed this validation 2/24 it was causing errors no the assign trade supplier
    public string? Phone { get; set; }

    public string? Fax { get; set; }

    public string? SubFax3 { get; set; }

    public string? SubContactDc { get; set; }

    public string? SubContact2Dc { get; set; }

    public string? SubContact3Dc { get; set; }
    [CustomPhoneValidation]
    public string? SubContactMobile { get; set; }
    [CustomPhoneValidation]
    public string? SubContact2Mobile { get; set; }
    [CustomPhoneValidation]
    public string? SubContact3Mobile { get; set; }

    public int? SubContactMobileSp { get; set; }

    public int? SubContact2MobileSp { get; set; }

    public int? SubContact3MobileSp { get; set; }

    [CustomEmailValidation]
    public string? SubContactEmail { get; set; }

    [CustomEmailValidation]
    public string? SubContact2Email { get; set; }

    public string? SubContact3Email { get; set; }

    public string? SubContactPurch { get; set; }

    public string? SubContact2Purch { get; set; }

    public string? SubContact3Purch { get; set; }

    public string? SubContactSched { get; set; }

    public string? SubContact2Sched { get; set; }

    public string? SubContact3Sched { get; set; }

    public string? GlInsRequired { get; set; }

    public DateTime? GlInsExpiryDate { get; set; }

    public string? WcompInsRequired { get; set; }

    public DateTime? WcompInsExpiryDate { get; set; }

    public string? PoPlugSupplier { get; set; }

    public double? Retainage { get; set; }


    public string? SubContactIsadmin { get; set; }

    public string? SubContact2Isadmin { get; set; }

    public string? SubContact3Isadmin { get; set; }

    public string? IsActive { get; set; }

    public string? WarrantyEnabled { get; set; }
    public bool IsWarrantyEnabled { get; set; }

    public int? OrganizationId { get; set; }

    public int? OrganizationUserId { get; set; }

    public string? SubContactWrty { get; set; }

    public string? SubContactWrtyPos { get; set; }

    public string? SubPhoneWrty { get; set; }

    public string? SubFaxWrty { get; set; }

    public string? SubContactWrtyDc { get; set; }

    public string? SubContactWrtyMobile { get; set; }

    public int? SubContactWrtyMobileSp { get; set; }

    [CustomEmailValidation]
    public string? SubContactWrtyEmail { get; set; }

    public string? TradePortalSupplier { get; set; }

    public string? TermsAccepted { get; set; }

    public DateTime? TermsAcceptedDate { get; set; }

    public string? TermsAcceptedUser { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool IsActive1 { get; set; }

    public string? VehicleInsRequired { get; set; }

    public DateTime? VehicleInsExpiryDate { get; set; }

    public string? MasterAgreementRequired { get; set; }

    public DateTime? MasterAgreementExpiryDate { get; set; }

    public string? Create2ndLienWaiverHold { get; set; }

    public string? UsesReservedCosts { get; set; }
    public bool? Blocked { get; set; }
    public bool BoolBlocked { get; set; }

}
