﻿<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">ERP.Web</a>
        <button title="Navigation menu" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass" @onclick="ToggleNavMenu">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="oi oi-home" aria-hidden="true"></span> Home
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="subdivisions">
                <span class="oi oi-list-rich" aria-hidden="true"></span> Subdivisions
            </NavLink>
        </div>
        
@*        <div class="nav-item px-3">
            <NavLink class="nav-link" href="masteroptiontree">
                <span class="oi oi-list-rich" aria-hidden="true"></span> Master Options  
            </NavLink>
        </div>*@

@*        <div class="nav-item px-3">
            <NavLink class="nav-link" href="subdivisionplanoption">
                <span class="oi oi-list-rich" aria-hidden="true"></span> SubdivisionPlanOption
            </NavLink>
        </div>*@
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="masteroptionplan">
                <span class="oi oi-list-rich" aria-hidden="true"></span> Master Option to Plan
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="masterplantosubdiv">
                <span class="oi oi-list-rich" aria-hidden="true"></span> MasterPlan Subdiv 
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="subdivplantosubdiv">
                <span class="oi oi-list-rich" aria-hidden="true"></span> Copy Subdivision Plan
            </NavLink>
        </div>
        @*<div class="nav-item px-3">
            <NavLink class="nav-link" href="configuremasterplanitems">
                <span class="oi oi-list-rich" aria-hidden="true"></span> ConfigureMasterPlan
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="configureassemblyitems">
                <span class="oi oi-list-rich" aria-hidden="true"></span> ConfigureMasterPlan2
            </NavLink>
        </div>*@
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="masterplans">
                <span class="oi oi-list-rich" aria-hidden="true"></span> Master Plans
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="modelmanager">
                <span class="oi oi-list-rich" aria-hidden="true"></span> Manage Models
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="modelmanager2">
                <span class="oi oi-list-rich" aria-hidden="true"></span> Manage Models2
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="masteroptions">
                <span class="oi oi-list-rich" aria-hidden="true"></span> Manage Options
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="configuremasteritems">
                <span class="oi oi-list-rich" aria-hidden="true"></span> Manage Items
            </NavLink>
        </div>
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }
}
