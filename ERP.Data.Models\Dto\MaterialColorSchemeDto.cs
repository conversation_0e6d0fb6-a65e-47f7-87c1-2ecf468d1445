﻿using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
namespace ERP.Data.Models;

public class MaterialColorSchemeDto : IMapFrom<MaterialColorScheme>
{
    public int MaterialColorSchemeId { get; set; }
    public Guid? Guid { get; set; }

   // [Required(ErrorMessage = "Color Scheme Name is required")]
    public string ColorSchemeNum { get; set; } 

    public int MaterialIdNotused { get; set; }

    public int ColorSchemeIdNotused { get; set; }

    public int PlanOptionId { get; set; }

    public bool CanBeReplaced { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

   // public byte[] RecordTimeStamp { get; set; } = null!;

    public int MaterialColorPredefinedId { get; set; }

    public MaterialColorPredefinedDto? MaterialColorPredefined { get; set; } 

    public AvailablePlanOptionDto? PlanOption { get; set; }
}
