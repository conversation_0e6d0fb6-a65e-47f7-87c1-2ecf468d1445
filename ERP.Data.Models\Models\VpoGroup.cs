﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class VpoGroup
{
    public int VpoGroupId { get; set; }

    public string? GroupDesc { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual ICollection<VpoApprovalSeq> VpoApprovalSeqs { get; set; } = new List<VpoApprovalSeq>();

    public virtual ICollection<VpoGroupJpg> VpoGroupJpgs { get; set; } = new List<VpoGroupJpg>();
}
