﻿using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class JobContactDto :IMapFrom<JobContact>
{
    public int JobContactId { get; set; }

    public string? JobNumber { get; set; } 

    public string? UserId { get; set; } 

    public int RoleId { get; set; }

    public DateTime? CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

   // public byte[] RecordTimeStamp { get; set; } = null!;

    public int? OldJobContractId { get; set; }

    public JobDto? JobNumberNavigation { get; set; } 

    public RoleDto? Role { get; set; } 

    public UserDto? User { get; set; }

    public int? VpoGroupId { get; set; }
}
