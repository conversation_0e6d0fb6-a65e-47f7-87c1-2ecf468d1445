﻿@page "/attributesbyplan"
@using ERP.Data.Models.Dto;
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject PlanService PlanService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager NavManager
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, DesignCenter")]
<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }

    /* Gutter */
    .row > * {
        padding-right: calc(var(--bs-gutter-x) * .10);
        padding-left: calc(var(--bs-gutter-x) * .10);
    }

    .k-notification-success {
        border-color: #c3e6cb;
        color: #155724;
        background-color: #d4edda;
        font-size: .688rem;
        font-family: "Roboto",sans-serif;
        width: 250px;
        padding: .75rem 1.25rem;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }

    .k-form .k-form-label, .k-form .k-form-field-wrap {
        display: inline;
    }
</style>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<PageTitle>Manage Attributes By Plan</PageTitle>

<div class="container-fluid flex">
    <div class="row d-flex">
        <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Manage Attributes By Plan</h7>
                </div>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item"><a href="/attributes">Attributes</a></li>
            <li class="breadcrumb-item active">Attributes By Plan</li>
        </ol>

        <div class="col-2">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Master Plan</h7>
                </div>
            </div>

            @if (MasterPlanData == null)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingPlan />
            }
            else
            {
                <TelerikGrid Data=@MasterPlanData
                             ScrollMode="@GridScrollMode.Virtual"
                             FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                             Height="1000px" RowHeight="60" PageSize="20"
                             Sortable="true"
                             Resizable="true"
                             Reorderable="true"
                             Groupable="false"
                             Size="@ThemeConstants.Grid.Size.Small"
                             OnRowClick="@OnPlanRowClickHandler"
                             SelectionMode="GridSelectionMode.Single"
                             @ref="@PlanGridRef">
                    <GridColumns>
                        <GridColumn Field="PlanNum" Title="Plan Number" Width="110px" Editable="true" Groupable="false" />
                        <GridColumn Field="PlanName" Title="Plan Name" Editable="true" Groupable="false" />
                    </GridColumns>
                    <GridToolBarTemplate>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                </TelerikGrid>

            }
        </div>
        <div class="col-3">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Selected Plan: @SelectedPlan?.PlanName</h7>
                </div>
            </div>

            @if (SubdivisionData == null)
            {
                <p><em>Select a plan to see subdivisions</em></p>
                <div style=@loadingOptionStyle>Loading...</div>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingOption />
            }

            else
            {
                <TelerikGrid Data=@SubdivisionData
                             FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                             Height="1000px" RowHeight="60" PageSize="20"
                             Sortable="true"
                             Resizable="true"
                             Reorderable="true"
                             Groupable="false"
                             OnRowClick="@OnOptionRowClickHandler"
                             @ref="@OptionGridRef">
                    <GridColumns>
                        <GridColumn Field="SubdivisionName" Title="Subdivision Name" Editable="true" Groupable="false" />
                        <GridColumn Field="MarketingName" Title="Marketing Name" Editable="true" Groupable="false" />
                    </GridColumns>
                    <GridToolBarTemplate>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                </TelerikGrid>
            }
        </div>
        <div class="col-3">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Selected Subdivision: @SelectedSubdivision?.SubdivisionName</h7>
                </div>
            </div>

            @if (AvailablePlanOptionData == null)
            {
                <p><em>Select a subdivision to see available options</em></p>
                <div style=@loadingItemStyle>Loading...</div>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingItem />
            }
            else
            {
                <TelerikGrid Data=@AvailablePlanOptionData
                             FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                             Height="1000px" RowHeight="60" PageSize="20"
                             Sortable="true"
                             Resizable="true"
                             Reorderable="true"
                             Groupable="false"
                             @ref="@ItemGridRef"
                             OnRowClick="@OnAvailablePlanOptionRowClickHandler">
                    <GridColumns>
                        <GridColumn Field="OptionCode" Title="Option Code" Editable="false" Groupable="false" />
                        <GridColumn Field="ModifiedOptionDesc" Title="Option Description" Editable="false" Groupable="false" />
                    </GridColumns>
                    <GridToolBarTemplate>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                </TelerikGrid>
            }

        </div>
        <div class="col-4">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Attributes for Option: @PlanOption?.ModifiedOptionDesc</h7>
                </div>
            </div>
            @if (PlanWithAttributeGroup == null)
            {
                <p><em>Select an option to see group attached to it</em></p>
                <div style=@loadingItemStyle>Loading...</div>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingItem />
            }
            else
            {
                <TelerikGrid Data=@PlanWithAttributeGroup
                             FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                             Height="1000px" RowHeight="60" PageSize="20"
                             Sortable="true"
                             Resizable="true"
                             Groupable="false"
                             Reorderable="true"
                             Size="@ThemeConstants.Grid.Size.Small"
                             SelectionMode="GridSelectionMode.Single"
                             @ref="@AttributeGridRef">
                    <GridColumns>
                        <GridColumn Field="AttrGroupAssignment.AttributeGroup.Description" Width="200px" Title="Description" Editable="false" Groupable="false" />
                        <GridColumn Width="115px">
                            <Template>
                                @{
                                    var item = context as OptionAttributeGroupItemDto;

                                    <TelerikButton Icon="@FontIcon.Trash" OnClick="@(() => DeactivateItem(item))" Class="k-button-danger" />
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                    <GridToolBarTemplate>
                        @if (AllowEdit)
                        {
                            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" OnClick="@MyCommandFromToolbar" Class="k-button-add">Add Group</GridCommandButton>
                        }
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                </TelerikGrid>
            }

        </div>
    </div>
</div>

<ERP.Web.Components.AddGroupToPlan @ref="AddMasterAttributeGroupModal" Plan="@SelectedPlan" PlanNum="@SelectedPlan?.PlanNum" PlanOption="@PlanOption" HandleAddSubmit="HandleValidAddItemSubmit"></ERP.Web.Components.AddGroupToPlan>

@code {
    private TelerikGrid<MasterPlanDto>? PlanGridRef { get; set; }
    private TelerikGrid<SubdivisionDto>? OptionGridRef { get; set; }
    private TelerikGrid<AvailablePlanOptionDto>? ItemGridRef { get; set; }
    public List<MasterPlanDto>? MasterPlanData { get; set; }
    public List<SubdivisionDto>? SubdivisionData { get; set; }
    public List<AvailablePlanOptionDto>? AvailablePlanOptionData { get; set; }
    public SubdivisionDto SelectedSubdivision { get; set; }
    public MasterPlanDto SelectedPlan { get; set; }
    public AvailablePlanOptionDto PlanOption { get; set; }
    public ModelManagerItemModel SelectedItem { get; set; }
    private string loadingOptionStyle = "display:none";
    private string loadingItemStyle = "display:none";
    public bool IsLoadingOption { get; set; } = false;
    public bool IsLoadingPlan { get; set; } = false;
    public bool IsLoadingItem { get; set; } = false;
    private bool AllowEdit { get; set; } = true;
    public string? ErrorMessage;
    public bool? ShowError;

    // Attributes Data
    public List<OptionAttributeGroupItemDto>? PlanWithAttributeGroup { get; set; }
    private TelerikGrid<OptionAttributeGroupItemDto>? AttributeGridRef { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    // Component
    protected ERP.Web.Components.AddGroupToPlan? AddMasterAttributeGroupModal { get; set; }

    protected override async Task OnInitializedAsync()
    {
        IsLoadingPlan = true;

        var masterPlanTask = PlanService.GetMasterPlansAsync();
        var groupsTask = OptionService.GetOptionGroupsAsync();        
        await Task.WhenAll(new Task[] { masterPlanTask, groupsTask });        
        var masterPlanData = masterPlanTask.Result;
        MasterPlanData = masterPlanData.Value;       
        var getGroups = groupsTask.Result;
        ShowError = getGroups.IsSuccess;
        ErrorMessage = getGroups.Message;
        IsLoadingPlan = false;
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }

    protected async Task OnPlanRowClickHandler(GridRowClickEventArgs args)
    {
        loadingOptionStyle = "";
        IsLoadingOption = true;
        SubdivisionData = null;
        AvailablePlanOptionData = null;
        SelectedPlan = args.Item as MasterPlanDto;
        SubdivisionData = (await PlanService.GetAvailablePlanOptionInSubdivisionAsync(SelectedPlan.MasterPlanId)).Value;
        AvailablePlanOptionData = null;//reset
        loadingOptionStyle = "display:none";
        IsLoadingOption = false;
    }

    protected async Task OnOptionRowClickHandler(GridRowClickEventArgs args)
    {
        IsLoadingItem = true;
        SelectedSubdivision = args.Item as SubdivisionDto;
        AvailablePlanOptionData = (await PlanService.GetAvailableOptionByPlanAndSubdivisionAsync((int)SelectedPlan.MasterPlanId, (int)SelectedSubdivision.SubdivisionId)).Value;
        IsLoadingItem = false;
    }

    private void MyCommandFromToolbar(GridCommandEventArgs args)
    {
        AddMasterAttributeGroupModal.Show();
        StateHasChanged();
    }

    private async void HandleValidAddItemSubmit(ResponseModel responseItem)
    {
        AvailablePlanOptionData = (await PlanService.GetAvailableOptionByPlanAndSubdivisionAsync((int)SelectedPlan.MasterPlanId, (int)SelectedSubdivision.SubdivisionId)).Value;
        ShowSuccessOrErrorMessage(responseItem.Message, responseItem.IsSuccess);
        AddMasterAttributeGroupModal.Hide();

        // Reload
        PlanWithAttributeGroup = (await PlanService.GetPlanWithGroupsAsync(PlanOption.PlanOptionId)).Value;

        StateHasChanged();
    }

    protected async Task OnAvailablePlanOptionRowClickHandler(GridRowClickEventArgs args)
    {
        IsLoadingItem = true;
        PlanWithAttributeGroup = null;
        loadingItemStyle = "";
        var option = args.Item as AvailablePlanOptionDto;

        PlanOption = option;

        // Reload
        PlanWithAttributeGroup = (await PlanService.GetPlanWithGroupsAsync(PlanOption.PlanOptionId)).Value;

        loadingItemStyle = "display:none";
        IsLoadingItem = false;
    }

    async void ShowSuccessOrErrorMessage(string message, bool isSuccess)
    {
        // Alert
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }

    async Task DeactivateItem(OptionAttributeGroupItemDto item)
    {
        var result = await PlanService.DeleteOptionAttributeGroupItemAsync(item);

        // Reload
        PlanWithAttributeGroup = (await PlanService.GetPlanWithGroupsAsync(PlanOption.PlanOptionId)).Value;

        // Alert
        ShowSuccessOrErrorMessage(result.Message, result.IsSuccess);

        StateHasChanged();
    }
}
