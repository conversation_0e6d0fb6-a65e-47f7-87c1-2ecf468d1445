﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class JobCustomer
{
    public int JobCustomerId { get; set; }

    public string JobNumber { get; set; } = null!;

    public int CustomerId { get; set; }

    public bool? Cancelled { get; set; }

    public bool? Transfer { get; set; }

    public string? TransfferedTo { get; set; }

    public bool? IsActive { get; set; }

    public DateTime Createddatetime { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? Createdby { get; set; }

    public string? Updatedby { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual Customer Customer { get; set; } = null!;

    public virtual Job JobNumberNavigation { get; set; } = null!;
}
