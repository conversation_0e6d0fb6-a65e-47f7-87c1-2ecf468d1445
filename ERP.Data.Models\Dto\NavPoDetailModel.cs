﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class PurchaseLineList
    {
        public int? TableID { get; set; }
        public string? PackageCode { get; set; }
        public List<PurchaseLine>? PurchaseLines { get; set; }
    }
    public class PurchaseLine
    {
        //public string id { get; set; }
       // public string? documentId { get; set; }
        //public int sequence { get; set; }
      //  public string itemId { get; set; }
       // public string accountId { get; set; }
        public string? Type { get; set; }
        public string? No { get; set; }//G/L Account number
        public string? DocumentNo { get; set; }
        //public string lineObjectNumber { get; set; }
        //public string description { get; set; }
       // public string unitOfMeasureId { get; set; }
        public string? UnitOfMeasureCode { get; set; } //unit, eg "EA", "SF"
        public double? Quantity { get; set; }
        public double? DirectUnitCost { get; set; }
        public string? DocumentType { get; set; }
        public string? BuyFromVendorNo { get; set; }
        public string? JobNo { get; set; }
        public string? JobTaskNo { get; set; }
        public string? ShortcutDimension1Code { get; set; }
        public string? ShortcutDimension2Code {  get; set; }
        public string? TaxGroupCode { get; set; }
        public decimal? Amount { get; set; }
    }
}
