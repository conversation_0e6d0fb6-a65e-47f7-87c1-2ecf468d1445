﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace ERP.Data.Models;

public partial class ErpDevContext : DbContext
{
    public ErpDevContext()
    {
    }

    public ErpDevContext(DbContextOptions<ErpDevContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AsmDetail> AsmDetails { get; set; }

    public virtual DbSet<AsmHeader> AsmHeaders { get; set; }

    public virtual DbSet<AvailablePlanOption> AvailablePlanOptions { get; set; }

    public virtual DbSet<BomClass> BomClasses { get; set; }

    public virtual DbSet<CalendarsDay> CalendarsDays { get; set; }

    public virtual DbSet<ColorScheme> ColorSchemes { get; set; }

    public virtual DbSet<ColorSchemeLoadTemplate> ColorSchemeLoadTemplates { get; set; }

    public virtual DbSet<Contact> Contacts { get; set; }

    public virtual DbSet<Cost> Costs { get; set; }

    public virtual DbSet<CostsHistory> CostsHistories { get; set; }

    public virtual DbSet<Customer> Customers { get; set; }

    public virtual DbSet<Doctype> Doctypes { get; set; }

    public virtual DbSet<EstDb> EstDbs { get; set; }

    public virtual DbSet<Estactivity> Estactivities { get; set; }

    public virtual DbSet<Estcustoption> Estcustoptions { get; set; }

    public virtual DbSet<Estdetail> Estdetails { get; set; }

    public virtual DbSet<Estheader> Estheaders { get; set; }

    public virtual DbSet<EstimateSource> EstimateSources { get; set; }

    public virtual DbSet<Estjcedetail> Estjcedetails { get; set; }

    public virtual DbSet<Estoption> Estoptions { get; set; }

    public virtual DbSet<HomeArea> HomeAreas { get; set; }

    public virtual DbSet<ItemCostDetail> ItemCostDetails { get; set; }

    public virtual DbSet<ItemCostSummary> ItemCostSummaries { get; set; }

    public virtual DbSet<Jccategory> Jccategories { get; set; }

    public virtual DbSet<Jccostcode> Jccostcodes { get; set; }

    public virtual DbSet<Jccosttype> Jccosttypes { get; set; }

    public virtual DbSet<Job> Jobs { get; set; }

    public virtual DbSet<JobAttachment> JobAttachments { get; set; }

    public virtual DbSet<JobAttachmentNotify> JobAttachmentNotifies { get; set; }

    public virtual DbSet<JobContact> JobContacts { get; set; }

    public virtual DbSet<JobCustomer> JobCustomers { get; set; }

    public virtual DbSet<MasterAttributeGroup> MasterAttributeGroups { get; set; }

    public virtual DbSet<MasterAttributeItem> MasterAttributeItems { get; set; }

    public virtual DbSet<MasterItem> MasterItems { get; set; }

    public virtual DbSet<MasterItemPhasis> MasterItemPhases { get; set; }

    public virtual DbSet<MasterOption> MasterOptions { get; set; }

    public virtual DbSet<MasterPlan> MasterPlans { get; set; }

    public virtual DbSet<Material> Materials { get; set; }

    public virtual DbSet<MaterialColorScheme> MaterialColorSchemes { get; set; }

    public virtual DbSet<Milestone> Milestones { get; set; }

    public virtual DbSet<OptionGroup> OptionGroups { get; set; }

    public virtual DbSet<OptionType> OptionTypes { get; set; }

    public virtual DbSet<Pactivity> Pactivities { get; set; }

    public virtual DbSet<PactivityAreaSupplier> PactivityAreaSuppliers { get; set; }

    public virtual DbSet<PhasePlan> PhasePlans { get; set; }

    public virtual DbSet<PlanOptionAttributeItem> PlanOptionAttributeItems { get; set; }

    public virtual DbSet<PlanType> PlanTypes { get; set; }

    public virtual DbSet<Poapproval> Poapprovals { get; set; }

    public virtual DbSet<Podetail> Podetails { get; set; }

    public virtual DbSet<Podetailoption> Podetailoptions { get; set; }

    public virtual DbSet<Poheader> Poheaders { get; set; }

    public virtual DbSet<Pojccdetail> Pojccdetails { get; set; }

    public virtual DbSet<Postatus> Postatuses { get; set; }

    public virtual DbSet<ReferenceType> ReferenceTypes { get; set; }

    public virtual DbSet<Sactivity> Sactivities { get; set; }

    public virtual DbSet<Salesconfig> Salesconfigs { get; set; }

    public virtual DbSet<Salesconfigco> Salesconfigcos { get; set; }

    public virtual DbSet<Salesconfigcooption> Salesconfigcooptions { get; set; }

    public virtual DbSet<Salesconfigoption> Salesconfigoptions { get; set; }

    public virtual DbSet<Schedule> Schedules { get; set; }

    public virtual DbSet<ScheduleArea> ScheduleAreas { get; set; }

    public virtual DbSet<ScheduleChain> ScheduleChains { get; set; }

    public virtual DbSet<ScheduleChainSchedAid> ScheduleChainSchedAids { get; set; }

    public virtual DbSet<ScheduleMaster> ScheduleMasters { get; set; }

    public virtual DbSet<ScheduleMasterPred> ScheduleMasterPreds { get; set; }

    public virtual DbSet<ScheduleMasterSchedule> ScheduleMasterSchedules { get; set; }

    public virtual DbSet<ScheduleMilestone> ScheduleMilestones { get; set; }

    public virtual DbSet<SchedulePhase> SchedulePhases { get; set; }

    public virtual DbSet<ScheduleSactivity> ScheduleSactivities { get; set; }

    public virtual DbSet<ScheduleSactivityPred> ScheduleSactivityPreds { get; set; }

    public virtual DbSet<Subdivision> Subdivisions { get; set; }

    public virtual DbSet<SubdivisionContact> SubdivisionContacts { get; set; }

    public virtual DbSet<Supplier> Suppliers { get; set; }

    public virtual DbSet<SupplierContact> SupplierContacts { get; set; }

    public virtual DbSet<SupplierTradeType> SupplierTradeTypes { get; set; }

    public virtual DbSet<SupplierType> SupplierTypes { get; set; }

    public virtual DbSet<TbBuiltOption> TbBuiltOptions { get; set; }

    public virtual DbSet<Template> Templates { get; set; }

    public virtual DbSet<TemplateMilestone> TemplateMilestones { get; set; }

    public virtual DbSet<TemplateSactivity> TemplateSactivities { get; set; }

    public virtual DbSet<TemplateSactivityPred> TemplateSactivityPreds { get; set; }

    public virtual DbSet<Trade> Trades { get; set; }

    public virtual DbSet<TradeSupplier> TradeSuppliers { get; set; }

    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<Variance> Variances { get; set; }

    public virtual DbSet<Worksheet> Worksheets { get; set; }

    public virtual DbSet<WorksheetOpt> WorksheetOpts { get; set; }

    public virtual DbSet<WorksheetOptAct> WorksheetOptActs { get; set; }

    public virtual DbSet<WorksheetPlan> WorksheetPlans { get; set; }

    public virtual DbSet<WorksheetPlanAct> WorksheetPlanActs { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        => optionsBuilder.UseSqlServer("Name=ConnectionStrings:ERPConnection");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AsmDetail>(entity =>
        {
            entity.ToTable("ASM_DETAIL");

            entity.HasIndex(e => new { e.AsmHeaderId, e.IsActive }, "nci_wi_ASM_DETAIL_FEE871F79017FEBF6F23EB2B3E6855B1");

            entity.Property(e => e.AsmDetailId).HasColumnName("ASM_DETAIL_ID");
            entity.Property(e => e.AsbDetailNumber).HasColumnName("ASB_DETAIL_NUMBER");
            entity.Property(e => e.AsmHeaderId).HasColumnName("ASM_HEADER_ID");
            entity.Property(e => e.BomClassId).HasColumnName("BOM_CLASS_ID");
            entity.Property(e => e.Calculation)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("CALCULATION");
            entity.Property(e => e.CalculationCode).HasColumnName("CALCULATION_CODE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DetailType).HasColumnName("DETAIL_TYPE");
            entity.Property(e => e.Factor).HasColumnName("FACTOR");
            entity.Property(e => e.Formula)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("FORMULA");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.ItemTableHeaderId).HasColumnName("ITEM_TABLE_HEADER_ID");
            entity.Property(e => e.MasterItemId).HasColumnName("MASTER_ITEM_ID");
            entity.Property(e => e.OptionItemNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("OPTION_ITEM_NOTES");
            entity.Property(e => e.Ordinality).HasColumnName("ORDINALITY");
            entity.Property(e => e.PeHeader).HasColumnName("PE_HEADER");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SelectAtTakeoffDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("SELECT_AT_TAKEOFF_DESC");
            entity.Property(e => e.SelectAtTakeoffUnit)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("SELECT_AT_TAKEOFF_UNIT");
            entity.Property(e => e.SessionId).HasColumnName("SESSION_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UseItemFormula)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("USE_ITEM_FORMULA");

            entity.HasOne(d => d.AsmHeader).WithMany(p => p.AsmDetails)
                .HasForeignKey(d => d.AsmHeaderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ASM_DETAIL_ASM_HEADER");

            entity.HasOne(d => d.BomClass).WithMany(p => p.AsmDetails)
                .HasForeignKey(d => d.BomClassId)
                .HasConstraintName("fk_BOMCLASS_ASMDETAIL");

            entity.HasOne(d => d.MasterItem).WithMany(p => p.AsmDetails)
                .HasForeignKey(d => d.MasterItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ASM_DETAIL_MasterItems");
        });

        modelBuilder.Entity<AsmHeader>(entity =>
        {
            entity.ToTable("ASM_HEADER");

            entity.HasIndex(e => new { e.IsActive, e.MasterPlanId }, "nci_wi_ASM_HEADER_0DD0CA2F6BF91755AE2874BDA9C508E8");

            entity.Property(e => e.AsmHeaderId).HasColumnName("ASM_HEADER_ID");
            entity.Property(e => e.AsmGroupId).HasColumnName("ASM_GROUP_ID");
            entity.Property(e => e.AssemblyCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("ASSEMBLY_CODE");
            entity.Property(e => e.AssemblyDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("ASSEMBLY_DESC");
            entity.Property(e => e.AssemblyNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("ASSEMBLY_NOTES");
            entity.Property(e => e.AssemblySize).HasColumnName("ASSEMBLY_SIZE");
            entity.Property(e => e.AssemblyUnit)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("ASSEMBLY_UNIT");
            entity.Property(e => e.Calculation)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("CALCULATION");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DeletedFromPe)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("DELETED_FROM_PE");
            entity.Property(e => e.EstDbOwner).HasColumnName("EST_DB_OWNER");
            entity.Property(e => e.Formula)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("FORMULA");
            entity.Property(e => e.HomeAreaId).HasColumnName("HOME_AREA_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.IsBaseHouse)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_BASE_HOUSE");
            entity.Property(e => e.IsElevation)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_ELEVATION");
            entity.Property(e => e.MasterOptionId).HasColumnName("MASTER_OPTION_ID");
            entity.Property(e => e.MasterPlanId).HasColumnName("MASTER_PLAN_ID");
            entity.Property(e => e.OptionScope).HasColumnName("OPTION_SCOPE");
            entity.Property(e => e.PeDatetimestamp)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("PE_DATETIMESTAMP");
            entity.Property(e => e.PeHeader).HasColumnName("PE_HEADER");
            entity.Property(e => e.PeUpdated)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PE_UPDATED");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.TlpeOptionCategoryId).HasColumnName("TLPE_OPTION_CATEGORY_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.HomeArea).WithMany(p => p.AsmHeaders)
                .HasForeignKey(d => d.HomeAreaId)
                .HasConstraintName("FK_ASM_HEADER_HomeArea");

            entity.HasOne(d => d.MasterOption).WithMany(p => p.AsmHeaders)
                .HasForeignKey(d => d.MasterOptionId)
                .HasConstraintName("FK_ASM_HEADER_MasterOption");

            entity.HasOne(d => d.MasterPlan).WithMany(p => p.AsmHeaders)
                .HasForeignKey(d => d.MasterPlanId)
                .HasConstraintName("FK_ASM_HEADER_MasterPlan");
        });

        modelBuilder.Entity<AvailablePlanOption>(entity =>
        {
            entity.HasKey(e => e.PlanOptionId).HasName("PK_tbAvailablePlanOption");

            entity.ToTable("AVAILABLE_PLAN_OPTION");

            entity.HasIndex(e => e.MasterPlanId, "idxMasterPlanID");

            entity.HasIndex(e => new { e.IsActive, e.PhasePlanId }, "nci_wi_AVAILABLE_PLAN_OPTION_4B9859E456A3F270508E3478CD3E1351");

            entity.Property(e => e.PlanOptionId).HasColumnName("PLAN_OPTION_ID");
            entity.Property(e => e.ActiveDepositSched).HasColumnName("ACTIVE_DEPOSIT_SCHED");
            entity.Property(e => e.AddPriceToBase).HasColumnName("ADD_PRICE_TO_BASE");
            entity.Property(e => e.AgentModPrice).HasColumnName("AGENT_MOD_PRICE");
            entity.Property(e => e.AgentModQty).HasColumnName("AGENT_MOD_QTY");
            entity.Property(e => e.AgentModType).HasColumnName("AGENT_MOD_TYPE");
            entity.Property(e => e.Amount)
                .HasColumnType("decimal(9, 0)")
                .HasColumnName("AMOUNT");
            entity.Property(e => e.AreaOptionSource).HasColumnName("AREA_OPTION_SOURCE");
            entity.Property(e => e.AssociatedEstimate)
                .HasMaxLength(254)
                .IsUnicode(false)
                .HasColumnName("ASSOCIATED_ESTIMATE");
            entity.Property(e => e.BeforeAfter)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("BEFORE_AFTER");
            entity.Property(e => e.BuildInStageId).HasColumnName("BUILD_IN_STAGE_ID");
            entity.Property(e => e.ConstrStageId).HasColumnName("CONSTR_STAGE_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CurrCost).HasColumnName("CURR_COST");
            entity.Property(e => e.CurrDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("CURR_DATE");
            entity.Property(e => e.CurrMargin).HasColumnName("CURR_MARGIN");
            entity.Property(e => e.CurrSelling).HasColumnName("CURR_SELLING");
            entity.Property(e => e.CustChoiceReq).HasColumnName("CUST_CHOICE_REQ");
            entity.Property(e => e.CustomerTranslation)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("CUSTOMER_TRANSLATION");
            entity.Property(e => e.CutoffStageId).HasColumnName("CUTOFF_STAGE_ID");
            entity.Property(e => e.DaysFromTrigger).HasColumnName("DAYS_FROM_TRIGGER");
            entity.Property(e => e.DepositDesc)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("DEPOSIT_DESC");
            entity.Property(e => e.DepositTypeId).HasColumnName("DEPOSIT_TYPE_ID");
            entity.Property(e => e.DollarAmt).HasColumnName("DOLLAR_AMT");
            entity.Property(e => e.Elevation).HasColumnName("ELEVATION");
            entity.Property(e => e.EscrowStageId).HasColumnName("ESCROW_STAGE_ID");
            entity.Property(e => e.FloorOption).HasColumnName("FLOOR_OPTION");
            entity.Property(e => e.HasEstimate)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("HAS_ESTIMATE");
            entity.Property(e => e.HomeAreaId).HasColumnName("HOME_AREA_ID");
            entity.Property(e => e.ImageUpload)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("IMAGE_UPLOAD");
            entity.Property(e => e.IncludeInBase)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("INCLUDE_IN_BASE");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.IsElevation)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_ELEVATION");
            entity.Property(e => e.IsStandard)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_STANDARD");
            entity.Property(e => e.LastCost).HasColumnName("LAST_COST");
            entity.Property(e => e.LastDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("LAST_DATE");
            entity.Property(e => e.LastMargin).HasColumnName("LAST_MARGIN");
            entity.Property(e => e.LastSelling).HasColumnName("LAST_SELLING");
            entity.Property(e => e.MarginLumpSum).HasColumnName("MARGIN_LUMP_SUM");
            entity.Property(e => e.MarginMarketValue).HasColumnName("MARGIN_MARKET_VALUE");
            entity.Property(e => e.MarginPercent).HasColumnName("MARGIN_PERCENT");
            entity.Property(e => e.MarginType).HasColumnName("MARGIN_TYPE");
            entity.Property(e => e.MasterOptionId).HasColumnName("MASTER_OPTION_ID");
            entity.Property(e => e.MasterPlanId).HasColumnName("MASTER_PLAN_ID");
            entity.Property(e => e.ModifiedOptionDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("MODIFIED_OPTION_DESC");
            entity.Property(e => e.NextCost).HasColumnName("NEXT_COST");
            entity.Property(e => e.NextDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("NEXT_DATE");
            entity.Property(e => e.NextMargin).HasColumnName("NEXT_MARGIN");
            entity.Property(e => e.NextSelling).HasColumnName("NEXT_SELLING");
            entity.Property(e => e.OptionCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("OPTION_CODE");
            entity.Property(e => e.OptionCodeSsold)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("OptionCode_SSold");
            entity.Property(e => e.OptionGroupId).HasColumnName("OPTION_GROUP_ID");
            entity.Property(e => e.OptionLongDesc)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("OPTION_LONG_DESC");
            entity.Property(e => e.OptionSelectionType)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("OPTION_SELECTION_TYPE");
            entity.Property(e => e.OptionTypeId)
                .HasDefaultValueSql("((6))")
                .HasColumnName("OPTION_TYPE_ID");
            entity.Property(e => e.Phase)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("PHASE");
            entity.Property(e => e.PhasePlanId).HasColumnName("PHASE_PLAN_ID");
            entity.Property(e => e.PrintOption).HasColumnName("PRINT_OPTION");
            entity.Property(e => e.ProductSpecifications)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("PRODUCT_SPECIFICATIONS");
            entity.Property(e => e.RecordTimeStampe)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Restrictions)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("RESTRICTIONS");
            entity.Property(e => e.RoomPlanId).HasColumnName("ROOM_PLAN_ID");
            entity.Property(e => e.SendSalesCenterDate)
                .HasColumnType("datetime")
                .HasColumnName("SEND_SALES_CENTER_DATE");
            entity.Property(e => e.SendToSalesCenter).HasColumnName("SEND_TO_SALES_CENTER");
            entity.Property(e => e.SqFt).HasColumnName("SQ_FT");
            entity.Property(e => e.SsAvailablePlanOptionId).HasColumnName("SS_AvailablePlanOptionID");
            entity.Property(e => e.SsMasterOptionId).HasColumnName("SS_MasterOptionID");
            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.SubdivisionId).HasColumnName("SUBDIVISION_ID");
            entity.Property(e => e.TriggerEventId).HasColumnName("TRIGGER_EVENT_ID");
            entity.Property(e => e.UnitCost)
                .HasColumnType("decimal(8, 2)")
                .HasColumnName("UNIT_COST");
            entity.Property(e => e.UnitMeasureId).HasColumnName("UNIT_MEASURE_ID");
            entity.Property(e => e.UnitPrice)
                .HasColumnType("decimal(8, 2)")
                .HasColumnName("UNIT_PRICE");
            entity.Property(e => e.UnitQty)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("UNIT_QTY");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
            entity.Property(e => e.WarningCount).HasColumnName("WARNING_COUNT");

            entity.HasOne(d => d.HomeArea).WithMany(p => p.AvailablePlanOptions)
                .HasForeignKey(d => d.HomeAreaId)
                .HasConstraintName("FK_AvailablePlanOption_HomeArea");

            entity.HasOne(d => d.MasterOption).WithMany(p => p.AvailablePlanOptions)
                .HasForeignKey(d => d.MasterOptionId)
                .HasConstraintName("FK_AvailablePlanOption_MasterOption");

            entity.HasOne(d => d.OptionGroup).WithMany(p => p.AvailablePlanOptions)
                .HasForeignKey(d => d.OptionGroupId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AVAILABLE_PLAN_OPTION_OPTION_GROUPS");

            entity.HasOne(d => d.OptionType).WithMany(p => p.AvailablePlanOptions)
                .HasForeignKey(d => d.OptionTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AvailablePlanOption_OptionType");

            entity.HasOne(d => d.PhasePlan).WithMany(p => p.AvailablePlanOptions)
                .HasForeignKey(d => d.PhasePlanId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AvailablePlanOption_PhasePlan");
        });

        modelBuilder.Entity<BomClass>(entity =>
        {
            entity.ToTable("BOM_CLASS");

            entity.Property(e => e.BomClassId).HasColumnName("BOM_CLASS_ID");
            entity.Property(e => e.BomClass1)
                .HasMaxLength(36)
                .IsUnicode(false)
                .HasColumnName("BOM_CLASS");
            entity.Property(e => e.BomNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("BOM_NOTES");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DefaultPhaseCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("DEFAULT_PHASE_CODE");
            entity.Property(e => e.DeletedFromPe)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("DELETED_FROM_PE");
            entity.Property(e => e.Description)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("DESCRIPTION");
            entity.Property(e => e.EstDbId).HasColumnName("EST_DB_ID");
            entity.Property(e => e.EstDbOwner).HasColumnName("EST_DB_OWNER");
            entity.Property(e => e.IncludeSelectionsOnPo)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("INCLUDE_SELECTIONS_ON_PO");
            entity.Property(e => e.Printlocations)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PRINTLOCATIONS");
            entity.Property(e => e.Printschedule)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PRINTSCHEDULE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Releasecode)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("RELEASECODE");
            entity.Property(e => e.SactivityId).HasColumnName("SACTIVITY_ID");
            entity.Property(e => e.Taxable)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("TAXABLE");
            entity.Property(e => e.TradeId).HasColumnName("TRADE_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<CalendarsDay>(entity =>
        {
            entity.HasKey(e => e.CalendarsDaysId).HasName("CALENDARS_DAYS_PK");

            entity.ToTable("CALENDARS_DAYS");

            entity.HasIndex(e => e.CalendarId, "CALENDARS_DAYS_FK1");

            entity.HasIndex(e => new { e.CalendarId, e.WorkDate }, "CALENDARS_DAYS_IDX1").IsUnique();

            entity.Property(e => e.CalendarsDaysId).HasColumnName("CALENDARS_DAYS_ID");
            entity.Property(e => e.CalendarId)
                .HasDefaultValueSql("((1))")
                .HasColumnName("CALENDAR_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Description)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("DESCRIPTION");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.WorkDate)
                .HasColumnType("datetime")
                .HasColumnName("WORK_DATE");
        });

        modelBuilder.Entity<ColorScheme>(entity =>
        {
            entity.HasKey(e => e.ColorShemeId);

            entity.ToTable("ColorScheme");

            entity.Property(e => e.ColorShemeId).HasColumnName("ColorShemeID");
            entity.Property(e => e.ColorScheme1)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("ColorScheme");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
        });

        modelBuilder.Entity<ColorSchemeLoadTemplate>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_COLOR_SCHEME_LOAD_TEMPLATE");

            entity.ToTable("ColorSchemeLoadTemplate");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CanBeReplaced)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ColorScheme)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.ColorSchemeNum)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.CreateDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Material)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.OptionCode)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.OptionName)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.PlanNumber)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.SubdivisonNumber)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.SubdivsionName)
                .HasMaxLength(250)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Contact>(entity =>
        {
            entity.ToTable("CONTACT");

            entity.Property(e => e.ContactId).HasColumnName("CONTACT_ID");
            entity.Property(e => e.Address1)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("ADDRESS1");
            entity.Property(e => e.Address2)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("ADDRESS2");
            entity.Property(e => e.City)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("CITY");
            entity.Property(e => e.ContactKey)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("CONTACT_KEY");
            entity.Property(e => e.ContactPassword)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("CONTACT_PASSWORD");
            entity.Property(e => e.Country)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("COUNTRY");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Email)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("EMAIL");
            entity.Property(e => e.Fax)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("FAX");
            entity.Property(e => e.FirstName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("FIRST_NAME");
            entity.Property(e => e.HomePhone)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("HOME_PHONE");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.LastName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("LAST_NAME");
            entity.Property(e => e.MobilePhone)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("MOBILE_PHONE");
            entity.Property(e => e.Postcode)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("POSTCODE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.State)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("STATE");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.WorkPhone)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WORK_PHONE");
        });

        modelBuilder.Entity<Cost>(entity =>
        {
            entity.HasKey(e => e.CostsId);

            entity.ToTable("COSTS");

            entity.HasIndex(e => e.SubdivisionId, "nci");

            entity.HasIndex(e => e.MasterItemId, "nci_wi_COSTS_5646DBFE08E84A359615A4C9C14503CC");

            entity.HasIndex(e => new { e.IsActive, e.NextCostDue }, "nci_wi_COSTS_982E2014F2D2E42B1A40F0D3FA6A81D3");

            entity.Property(e => e.CostsId).HasColumnName("COSTS_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IncludesTax)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("INCLUDES_TAX");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.ItemTaxGroup)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("ITEM_TAX_GROUP");
            entity.Property(e => e.LastCost1).HasColumnName("LAST_COST_1");
            entity.Property(e => e.LastCost2).HasColumnName("LAST_COST_2");
            entity.Property(e => e.LastCost2Expired)
                .HasColumnType("smalldatetime")
                .HasColumnName("LAST_COST_2_EXPIRED");
            entity.Property(e => e.LastCost3).HasColumnName("LAST_COST_3");
            entity.Property(e => e.LastCost3Expired)
                .HasColumnType("smalldatetime")
                .HasColumnName("LAST_COST_3_EXPIRED");
            entity.Property(e => e.LastCostExpired)
                .HasColumnType("smalldatetime")
                .HasColumnName("LAST_COST_EXPIRED");
            entity.Property(e => e.MasterItemId).HasColumnName("MASTER_ITEM_ID");
            entity.Property(e => e.NextCost).HasColumnName("NEXT_COST");
            entity.Property(e => e.NextCost2).HasColumnName("NEXT_COST_2");
            entity.Property(e => e.NextCost2Due)
                .HasColumnType("smalldatetime")
                .HasColumnName("NEXT_COST_2_DUE");
            entity.Property(e => e.NextCostDue)
                .HasColumnType("smalldatetime")
                .HasColumnName("NEXT_COST_DUE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.SubdivisionId).HasColumnName("SUBDIVISION_ID");
            entity.Property(e => e.SupAltDesc)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("SUP_ALT_DESC");
            entity.Property(e => e.SupOrigCost).HasColumnName("SUP_ORIG_COST");
            entity.Property(e => e.SupOrigCostLast).HasColumnName("SUP_ORIG_COST_LAST");
            entity.Property(e => e.SupOrigCostLast2).HasColumnName("SUP_ORIG_COST_LAST2");
            entity.Property(e => e.SupOrigCostLast3).HasColumnName("SUP_ORIG_COST_LAST3");
            entity.Property(e => e.SupOrigCostNext).HasColumnName("SUP_ORIG_COST_NEXT");
            entity.Property(e => e.SupOrigCostNext2).HasColumnName("SUP_ORIG_COST_NEXT2");
            entity.Property(e => e.SupProductCode)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("SUP_PRODUCT_CODE");
            entity.Property(e => e.SupUnit)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("SUP_UNIT");
            entity.Property(e => e.SupplierContCoId).HasColumnName("SUPPLIER_CONT_CO_ID");
            entity.Property(e => e.SupplierContractsId).HasColumnName("SUPPLIER_CONTRACTS_ID");
            entity.Property(e => e.UnitCost).HasColumnName("UNIT_COST");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.Warrantydays).HasColumnName("WARRANTYDAYS");
            entity.Property(e => e.Warrantyitem)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("WARRANTYITEM");
            entity.Property(e => e.Warrantytype).HasColumnName("WARRANTYTYPE");

            entity.HasOne(d => d.MasterItem).WithMany(p => p.Costs)
                .HasForeignKey(d => d.MasterItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_COSTS_MasterItems");

            entity.HasOne(d => d.SubNumberNavigation).WithMany(p => p.Costs)
                .HasForeignKey(d => d.SubNumber)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_COSTS_SUPPLIER");

            entity.HasOne(d => d.Subdivision).WithMany(p => p.Costs)
                .HasForeignKey(d => d.SubdivisionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_COSTS_Subdivision");
        });

        modelBuilder.Entity<CostsHistory>(entity =>
        {
            entity.HasKey(e => e.CostsHistoryId).HasName("COSTS_HISTORY_PK");

            entity.ToTable("COSTS_HISTORY");

            entity.Property(e => e.CostsHistoryId).HasColumnName("COSTS_HISTORY_ID");
            entity.Property(e => e.CostExpired)
                .HasColumnType("datetime")
                .HasColumnName("COST_EXPIRED");
            entity.Property(e => e.CostRolledOverBy)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("COST_ROLLED_OVER_BY");
            entity.Property(e => e.CostsId).HasColumnName("COSTS_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.MasterItemsId).HasColumnName("MASTER_ITEMS_ID");
            entity.Property(e => e.OldCost).HasColumnName("OLD_COST");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.SubdivisionId).HasColumnName("SUBDIVISION_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
        });

        modelBuilder.Entity<Customer>(entity =>
        {
            entity.ToTable("CUSTOMER");

            entity.Property(e => e.CustomerId).HasColumnName("CUSTOMER_ID");
            entity.Property(e => e.AcntCustomerCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("ACNT_CUSTOMER_CODE");
            entity.Property(e => e.Address1)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("ADDRESS1");
            entity.Property(e => e.Address2)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("ADDRESS2");
            entity.Property(e => e.City)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("CITY");
            entity.Property(e => e.Country)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("COUNTRY");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.CustomerKey)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("CUSTOMER_KEY");
            entity.Property(e => e.CustomerName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("CUSTOMER_NAME");
            entity.Property(e => e.Email)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("EMAIL");
            entity.Property(e => e.Fax)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("FAX");
            entity.Property(e => e.HomePhone)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("HOME_PHONE");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.MobilePhone)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("MOBILE_PHONE");
            entity.Property(e => e.Postcode)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("POSTCODE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.State)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("STATE");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.WorkPhone)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WORK_PHONE");
        });

        modelBuilder.Entity<Doctype>(entity =>
        {
            entity.ToTable("DOCTYPES");

            entity.Property(e => e.DoctypeId).HasColumnName("DOCTYPE_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Doctype1)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("DOCTYPE");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<EstDb>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("EST_DB");

            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.EstDbDefault)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("EST_DB_DEFAULT");
            entity.Property(e => e.EstDbDesc)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("EST_DB_DESC");
            entity.Property(e => e.EstDbId).HasColumnName("EST_DB_ID");
            entity.Property(e => e.EstDbOwner).HasColumnName("EST_DB_OWNER");
            entity.Property(e => e.EstDbPath)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("EST_DB_PATH");
            entity.Property(e => e.EstDbStatus)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("EST_DB_STATUS");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.MasterEstDbId).HasColumnName("MASTER_EST_DB_ID");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<Estactivity>(entity =>
        {
            entity.ToTable("ESTACTIVITY");

            entity.Property(e => e.EstactivityId).HasColumnName("ESTACTIVITY_ID");
            entity.Property(e => e.BomClass)
                .HasMaxLength(36)
                .IsUnicode(false)
                .HasColumnName("BOM_CLASS");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.CubitId)
                .HasMaxLength(128)
                .IsUnicode(false)
                .HasColumnName("CUBIT_ID");
            entity.Property(e => e.DefaultVendor).HasColumnName("DEFAULT_VENDOR");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JobNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JOB_NUMBER");
            entity.Property(e => e.Poseq).HasColumnName("POSEQ");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Releasecode)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("RELEASECODE");
            entity.Property(e => e.SactivityId).HasColumnName("SACTIVITY_ID");
            entity.Property(e => e.SelectedVendor).HasColumnName("SELECTED_VENDOR");
            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.TaxGroup)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("TAX_GROUP");
            entity.Property(e => e.Taxable)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("TAXABLE");
            entity.Property(e => e.TradeId).HasColumnName("TRADE_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UseLocation)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("USE_LOCATION");
            entity.Property(e => e.UseWbsSort)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("USE_WBS_SORT");

            entity.HasOne(d => d.JobNumberNavigation).WithMany(p => p.Estactivities)
                .HasForeignKey(d => d.JobNumber)
                .HasConstraintName("fk_JobESTACTIVITY");

            entity.HasOne(d => d.Sactivity).WithMany(p => p.Estactivities)
                .HasForeignKey(d => d.SactivityId)
                .HasConstraintName("fk_SACTIVITYIDESTACTIVITY");

            entity.HasOne(d => d.Trade).WithMany(p => p.Estactivities)
                .HasForeignKey(d => d.TradeId)
                .HasConstraintName("FK_ESTACTIVITY_TRADE");
        });

        modelBuilder.Entity<Estcustoption>(entity =>
        {
            entity.HasKey(e => e.EstcustoptionId).HasName("ESTCUSTOPTION_PK");

            entity.ToTable("ESTCUSTOPTION");

            entity.Property(e => e.EstcustoptionId).HasColumnName("ESTCUSTOPTION_ID");
            entity.Property(e => e.Addendumnum).HasColumnName("ADDENDUMNUM");
            entity.Property(e => e.Builderapproved).HasColumnName("BUILDERAPPROVED");
            entity.Property(e => e.Builtoptionid).HasColumnName("BUILTOPTIONID");
            entity.Property(e => e.Changedate)
                .HasColumnType("datetime")
                .HasColumnName("CHANGEDATE");
            entity.Property(e => e.Conflict)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasDefaultValueSql("('F')")
                .HasColumnName("CONFLICT");
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Crgroup)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("CRGROUP");
            entity.Property(e => e.Customerapproved).HasColumnName("CUSTOMERAPPROVED");
            entity.Property(e => e.Customerdesc)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("CUSTOMERDESC");
            entity.Property(e => e.Description)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("DESCRIPTION");
            entity.Property(e => e.DocsFolder)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("DOCS_FOLDER");
            entity.Property(e => e.Elevation)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasDefaultValueSql("('F')")
                .HasColumnName("ELEVATION");
            entity.Property(e => e.EstheaderId).HasColumnName("ESTHEADER_ID");
            entity.Property(e => e.EstimateCompleted)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasDefaultValueSql("('F')")
                .HasColumnName("ESTIMATE_COMPLETED");
            entity.Property(e => e.EstimateCost).HasColumnName("ESTIMATE_COST");
            entity.Property(e => e.EstimateRequired)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasDefaultValueSql("('F')")
                .HasColumnName("ESTIMATE_REQUIRED");
            entity.Property(e => e.EstimatorEmail)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("ESTIMATOR_EMAIL");
            entity.Property(e => e.EstimatorNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("ESTIMATOR_NOTES");
            entity.Property(e => e.Externalcode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("EXTERNALCODE");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JobNumber)
                .HasMaxLength(12)
                .IsUnicode(false)
                .HasColumnName("JOB_NUMBER");
            entity.Property(e => e.Lastchanged)
                .HasColumnType("datetime")
                .HasColumnName("LASTCHANGED");
            entity.Property(e => e.Lastuser).HasColumnName("LASTUSER");
            entity.Property(e => e.LotId).HasColumnName("LOT_ID");
            entity.Property(e => e.Optioncode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("OPTIONCODE");
            entity.Property(e => e.Optiondesc)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("OPTIONDESC");
            entity.Property(e => e.Optiongroupname)
                .HasMaxLength(63)
                .IsUnicode(false)
                .HasColumnName("OPTIONGROUPNAME");
            entity.Property(e => e.Optionlongdesc)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("OPTIONLONGDESC");
            entity.Property(e => e.Optionnum).HasColumnName("OPTIONNUM");
            entity.Property(e => e.Optiontype)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("OPTIONTYPE");
            entity.Property(e => e.Price).HasColumnName("PRICE");
            entity.Property(e => e.Pricedelta).HasColumnName("PRICEDELTA");
            entity.Property(e => e.Printdate)
                .HasColumnType("datetime")
                .HasColumnName("PRINTDATE");
            entity.Property(e => e.Qty).HasColumnName("QTY");
            entity.Property(e => e.Qtydelta).HasColumnName("QTYDELTA");
            entity.Property(e => e.Quotecomplete)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasDefaultValueSql("('F')")
                .HasColumnName("QUOTECOMPLETE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Removed)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasDefaultValueSql("('F')")
                .HasColumnName("REMOVED");
            entity.Property(e => e.Restrictions)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("RESTRICTIONS");
            entity.Property(e => e.SubdivisionId).HasColumnName("SUBDIVISION_ID");
            entity.Property(e => e.Unitcost).HasColumnName("UNITCOST");
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Estheader).WithMany(p => p.Estcustoptions)
                .HasForeignKey(d => d.EstheaderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ESTCUSTOPTION_ESTHEADER");

            entity.HasOne(d => d.Subdivision).WithMany(p => p.Estcustoptions)
                .HasForeignKey(d => d.SubdivisionId)
                .HasConstraintName("FK_ESTCUSTOPTION_SUBDIVISION");
        });

        modelBuilder.Entity<Estdetail>(entity =>
        {
            entity.HasKey(e => e.EstdetailId).HasName("PK__ESTDETAI__2F3CDD7E92C956DD");

            entity.ToTable("ESTDETAIL");

            entity.HasIndex(e => e.EstactivityId, "nci_wi_ESTDETAIL_42103D413DDD57C288FDF96FD7F9D9D3");

            entity.HasIndex(e => new { e.EstoptionId, e.IsActive }, "nci_wi_ESTDETAIL_4A8FE7C5A47513E3A559FF7AEC51D902");

            entity.HasIndex(e => e.EstjcedetailId, "nci_wi_ESTDETAIL_64D604DBBFFF727C6FEF3066EAFB4220");

            entity.HasIndex(e => e.VarianceJcCategory, "nci_wi_ESTDETAIL_B31DDE288D30764602E4BDB4A06D6F82");

            entity.Property(e => e.EstdetailId).HasColumnName("ESTDETAIL_ID");
            entity.Property(e => e.Amount).HasColumnName("AMOUNT");
            entity.Property(e => e.ApplyWaste)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("APPLY_WASTE");
            entity.Property(e => e.Category)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("CATEGORY");
            entity.Property(e => e.ChangedInHms)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("CHANGED_IN_HMS");
            entity.Property(e => e.CnvFctr).HasColumnName("CNV_FCTR");
            entity.Property(e => e.CostsId).HasColumnName("COSTS_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.CubitId)
                .HasMaxLength(128)
                .IsUnicode(false)
                .HasColumnName("CUBIT_ID");
            entity.Property(e => e.DeletedFromPe)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("DELETED_FROM_PE");
            entity.Property(e => e.Errors)
                .HasMaxLength(21)
                .IsUnicode(false)
                .HasColumnName("ERRORS");
            entity.Property(e => e.EstactivityId).HasColumnName("ESTACTIVITY_ID");
            entity.Property(e => e.EstdetailcalcsheetId).HasColumnName("ESTDETAILCALCSHEET_ID");
            entity.Property(e => e.EstimateExportAmt).HasColumnName("ESTIMATE_EXPORT_AMT");
            entity.Property(e => e.EstimateExportQty).HasColumnName("ESTIMATE_EXPORT_QTY");
            entity.Property(e => e.EstimateExportTax).HasColumnName("ESTIMATE_EXPORT_TAX");
            entity.Property(e => e.EstimateExportTaxGroup)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("ESTIMATE_EXPORT_TAX_GROUP");
            entity.Property(e => e.EstimateExportUnit)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("ESTIMATE_EXPORT_UNIT");
            entity.Property(e => e.EstimateExported)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("ESTIMATE_EXPORTED");
            entity.Property(e => e.EstimateOnlyVarianceItem)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("ESTIMATE_ONLY_VARIANCE_ITEM");
            entity.Property(e => e.EstimateReissueItem)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("ESTIMATE_REISSUE_ITEM");
            entity.Property(e => e.EstimateVarianceItem)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("ESTIMATE_VARIANCE_ITEM");
            entity.Property(e => e.EstitemheaderId).HasColumnName("ESTITEMHEADER_ID");
            entity.Property(e => e.EstjcedetailId).HasColumnName("ESTJCEDETAIL_ID");
            entity.Property(e => e.EstoptionId).HasColumnName("ESTOPTION_ID");
            entity.Property(e => e.HeadingId).HasColumnName("HEADING_ID");
            entity.Property(e => e.Instance).HasColumnName("INSTANCE");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.ItemDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("ITEM_DESC");
            entity.Property(e => e.ItemNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("ITEM_NOTES");
            entity.Property(e => e.ItemNumber)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("ITEM_NUMBER");
            entity.Property(e => e.JcCategory)
                .HasMaxLength(3)
                .IsUnicode(false)
                .HasColumnName("JC_CATEGORY");
            entity.Property(e => e.JcPhase)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JC_PHASE");
            entity.Property(e => e.LengthWbs)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("LENGTH_WBS");
            entity.Property(e => e.Location)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("LOCATION");
            entity.Property(e => e.Lump)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("LUMP");
            entity.Property(e => e.MasterItemsId).HasColumnName("MASTER_ITEMS_ID");
            entity.Property(e => e.Multdiv)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("MULTDIV");
            entity.Property(e => e.NoOfLengths)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("NO_OF_LENGTHS");
            entity.Property(e => e.OrderQty).HasColumnName("ORDER_QTY");
            entity.Property(e => e.OrdrUnit)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("ORDR_UNIT");
            entity.Property(e => e.OrgPrice).HasColumnName("ORG_PRICE");
            entity.Property(e => e.OrigBudgetEstdetailId).HasColumnName("ORIG_BUDGET_ESTDETAIL_ID");
            entity.Property(e => e.OrigCommittedAmount).HasColumnName("ORIG_COMMITTED_AMOUNT");
            entity.Property(e => e.OrigCommittedQty).HasColumnName("ORIG_COMMITTED_QTY");
            entity.Property(e => e.OrigCommittedTaxAmount).HasColumnName("ORIG_COMMITTED_TAX_AMOUNT");
            entity.Property(e => e.OrigEstoptionId).HasColumnName("ORIG_ESTOPTION_ID");
            entity.Property(e => e.PhaseCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("PHASE_CODE");
            entity.Property(e => e.PoExported)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PO_EXPORTED");
            entity.Property(e => e.PodetailId).HasColumnName("PODETAIL_ID");
            entity.Property(e => e.Price).HasColumnName("PRICE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.ReissueItem)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("REISSUE_ITEM");
            entity.Property(e => e.RndDir)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("RND_DIR");
            entity.Property(e => e.RndUnit).HasColumnName("RND_UNIT");
            entity.Property(e => e.SeqNumber).HasColumnName("SEQ_NUMBER");
            entity.Property(e => e.SortLocation)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("SORT_LOCATION");
            entity.Property(e => e.SortWbs)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SORT_WBS");
            entity.Property(e => e.SupplierContCoId).HasColumnName("SUPPLIER_CONT_CO_ID");
            entity.Property(e => e.SupplierContractsId).HasColumnName("SUPPLIER_CONTRACTS_ID");
            entity.Property(e => e.SupplierCostreservationId).HasColumnName("SUPPLIER_COSTRESERVATION_ID");
            entity.Property(e => e.TakeoffQuantity).HasColumnName("TAKEOFF_QUANTITY");
            entity.Property(e => e.TakeoffUnit)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("TAKEOFF_UNIT");
            entity.Property(e => e.TaxAmount).HasColumnName("TAX_AMOUNT");
            entity.Property(e => e.TaxGroup)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("TAX_GROUP");
            entity.Property(e => e.TaxGroupType)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("TAX_GROUP_TYPE");
            entity.Property(e => e.Taxable)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("TAXABLE");
            entity.Property(e => e.ThicknessWbs)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("THICKNESS_WBS");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTim).HasColumnType("smalldatetime");
            entity.Property(e => e.UseEstPrice)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("USE_EST_PRICE");
            entity.Property(e => e.VarianceJcCategory)
                .HasMaxLength(3)
                .IsUnicode(false)
                .HasColumnName("VARIANCE_JC_CATEGORY");
            entity.Property(e => e.VendorNumber).HasColumnName("VENDOR_NUMBER");
            entity.Property(e => e.Warnings)
                .HasMaxLength(21)
                .IsUnicode(false)
                .HasColumnName("WARNINGS");
            entity.Property(e => e.Waste).HasColumnName("WASTE");
            entity.Property(e => e.WidthWbs)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WIDTH_WBS");

            entity.HasOne(d => d.Costs).WithMany(p => p.Estdetails)
                .HasForeignKey(d => d.CostsId)
                .HasConstraintName("FK_ESTDETAIL_COSTS");

            entity.HasOne(d => d.Estactivity).WithMany(p => p.Estdetails)
                .HasForeignKey(d => d.EstactivityId)
                .HasConstraintName("FK_ESTDETAIL_ESTACTIVITY");

            entity.HasOne(d => d.Estjcedetail).WithMany(p => p.Estdetails)
                .HasForeignKey(d => d.EstjcedetailId)
                .HasConstraintName("FK_ESTDETAIL_ESTJCEDETAIL");

            entity.HasOne(d => d.Estoption).WithMany(p => p.Estdetails)
                .HasForeignKey(d => d.EstoptionId)
                .HasConstraintName("FK_ESTDETAIL_ESTOPTION");

            entity.HasOne(d => d.MasterItems).WithMany(p => p.Estdetails)
                .HasForeignKey(d => d.MasterItemsId)
                .HasConstraintName("FK_ESTDETAIL_MASTER_ITEMS");

            entity.HasOne(d => d.Podetail).WithMany(p => p.Estdetails)
                .HasForeignKey(d => d.PodetailId)
                .HasConstraintName("FK_ESTDETAIL_PODETAIL");

            entity.HasOne(d => d.VendorNumberNavigation).WithMany(p => p.Estdetails)
                .HasForeignKey(d => d.VendorNumber)
                .HasConstraintName("FK_ESTDETAIL_SUPPLIER");
        });

        modelBuilder.Entity<Estheader>(entity =>
        {
            entity.HasKey(e => e.EstheaderId).HasName("pk_EastHeaderId");

            entity.ToTable("ESTHEADER");

            entity.Property(e => e.EstheaderId).HasColumnName("ESTHEADER_ID");
            entity.Property(e => e.AssociatedFile)
                .HasMaxLength(254)
                .IsUnicode(false)
                .HasColumnName("ASSOCIATED_FILE");
            entity.Property(e => e.BasehouseCode)
                .HasMaxLength(60)
                .IsUnicode(false)
                .HasColumnName("BASEHOUSE_CODE");
            entity.Property(e => e.BasehouseDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("BASEHOUSE_DESC");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.CubitId)
                .HasMaxLength(128)
                .IsUnicode(false)
                .HasColumnName("CUBIT_ID");
            entity.Property(e => e.EstbuildelementId).HasColumnName("ESTBUILDELEMENT_ID");
            entity.Property(e => e.EstimateDescPe)
                .HasMaxLength(25)
                .IsUnicode(false)
                .HasColumnName("ESTIMATE_DESC_PE");
            entity.Property(e => e.EstimateNumber).HasColumnName("ESTIMATE_NUMBER");
            entity.Property(e => e.EstimateSalesPrice).HasColumnName("ESTIMATE_SALES_PRICE");
            entity.Property(e => e.EstimateSource)
                .HasDefaultValueSql("((1))")
                .HasColumnName("ESTIMATE_SOURCE");
            entity.Property(e => e.Estimator)
                .HasMaxLength(61)
                .IsUnicode(false)
                .HasColumnName("ESTIMATOR");
            entity.Property(e => e.Heading1)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("HEADING1");
            entity.Property(e => e.Heading2)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("HEADING2");
            entity.Property(e => e.Heading3)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("HEADING3");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JobNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JOB_NUMBER");
            entity.Property(e => e.JobSize).HasColumnName("JOB_SIZE");
            entity.Property(e => e.JobUnit)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("JOB_UNIT");
            entity.Property(e => e.PlanName)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("Plan_Name");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.ReferenceDesc)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("REFERENCE_DESC");
            entity.Property(e => e.ReferenceNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("REFERENCE_NUMBER");
            entity.Property(e => e.ReferenceType).HasColumnName("REFERENCE_TYPE");
            entity.Property(e => e.SalesconfigId).HasColumnName("SALESCONFIG_ID");
            entity.Property(e => e.SalesconfigcoId).HasColumnName("SALESCONFIGCO_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.VarianceJcCategory)
                .HasMaxLength(3)
                .IsUnicode(false)
                .HasColumnName("VARIANCE_JC_CATEGORY");
            entity.Property(e => e.WmsplanNum)
                .HasMaxLength(60)
                .IsUnicode(false)
                .HasColumnName("WMSPlanNum");

            entity.HasOne(d => d.EstimateSourceNavigation).WithMany(p => p.Estheaders)
                .HasForeignKey(d => d.EstimateSource)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_ESTIMATE_SOURCE");

            entity.HasOne(d => d.ReferenceTypeNavigation).WithMany(p => p.Estheaders)
                .HasForeignKey(d => d.ReferenceType)
                .HasConstraintName("FK_ESTHEADER_REFERENCE_TYPE");

            entity.HasOne(d => d.Salesconfig).WithMany(p => p.Estheaders)
                .HasForeignKey(d => d.SalesconfigId)
                .HasConstraintName("fk_SALESCONFIG_EstHeader");

            entity.HasOne(d => d.Salesconfigco).WithMany(p => p.Estheaders)
                .HasForeignKey(d => d.SalesconfigcoId)
                .HasConstraintName("FK_ESTHEADER_SALESCONFIGCO");
        });

        modelBuilder.Entity<EstimateSource>(entity =>
        {
            entity.HasKey(e => e.EstsourceId).HasName("pk_ESTSOURCE_ID");

            entity.ToTable("ESTIMATE_SOURCE");

            entity.Property(e => e.EstsourceId).HasColumnName("ESTSOURCE_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.EstsourceDesc)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("ESTSOURCE_DESC");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<Estjcedetail>(entity =>
        {
            entity.HasKey(e => e.EstjcedetailId).HasName("PK__ESTJCEDE__B3DC2CB7A0378A98");

            entity.ToTable("ESTJCEDETAIL");

            entity.HasIndex(e => new { e.IsActive, e.EstoptionId }, "nci_wi_ESTJCEDETAIL_3A08550634716448AFB16F3C4ACBAF17");

            entity.Property(e => e.EstjcedetailId).HasColumnName("ESTJCEDETAIL_ID");
            entity.Property(e => e.BudgetDatetimeExported)
                .HasColumnType("smalldatetime")
                .HasColumnName("BUDGET_DATETIME_EXPORTED");
            entity.Property(e => e.BudgetPendingSend)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("BUDGET_PENDING_SEND");
            entity.Property(e => e.BudgetPendingSendStatus)
                .HasMaxLength(2)
                .IsUnicode(false)
                .HasColumnName("BUDGET_PENDING_SEND_STATUS");
            entity.Property(e => e.BudgetReissueItem)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("BUDGET_REISSUE_ITEM");
            entity.Property(e => e.BudgetUserExported)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("BUDGET_USER_EXPORTED");
            entity.Property(e => e.Category)
                .HasMaxLength(3)
                .IsUnicode(false)
                .HasColumnName("CATEGORY");
            entity.Property(e => e.Costcode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("COSTCODE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DeleteAfterCancel)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("DELETE_AFTER_CANCEL");
            entity.Property(e => e.EstimateAmount).HasColumnName("ESTIMATE_AMOUNT");
            entity.Property(e => e.EstimateDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("ESTIMATE_DATE");
            entity.Property(e => e.EstimateExported)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("ESTIMATE_EXPORTED");
            entity.Property(e => e.EstimateUnits).HasColumnName("ESTIMATE_UNITS");
            entity.Property(e => e.EstoptionId).HasColumnName("ESTOPTION_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.IsCancelled)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_CANCELLED");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SessionId).HasColumnName("SESSION_ID");
            entity.Property(e => e.UnitDesc)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("UNIT_DESC");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UserStamp)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_STAMP");

            entity.HasOne(d => d.Estoption).WithMany(p => p.Estjcedetails)
                .HasForeignKey(d => d.EstoptionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ESTJCEDETAIL_ESTOPTION");
        });

        modelBuilder.Entity<Estoption>(entity =>
        {
            entity.HasKey(e => e.EstoptionId).HasName("PK__ESTOPTIO__F01315D276326CD4");

            entity.ToTable("ESTOPTION");

            entity.HasIndex(e => new { e.EstheaderId, e.IsActive }, "nci_wi_ESTOPTION_2B63A635B345D014E596FED4A220B213");

            entity.Property(e => e.EstoptionId).HasColumnName("ESTOPTION_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.CubitId)
                .HasMaxLength(128)
                .IsUnicode(false)
                .HasColumnName("CUBIT_ID");
            entity.Property(e => e.ElevationCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("ELEVATION_CODE");
            entity.Property(e => e.ElevationDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("ELEVATION_DESC");
            entity.Property(e => e.EstheaderId).HasColumnName("ESTHEADER_ID");
            entity.Property(e => e.EstselectiontypeId).HasColumnName("ESTSELECTIONTYPE_ID");
            entity.Property(e => e.IncludeInBase)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("INCLUDE_IN_BASE");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.IsDeleted)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_DELETED");
            entity.Property(e => e.IsElevation)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_ELEVATION");
            entity.Property(e => e.IsSelected)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_SELECTED");
            entity.Property(e => e.OptionDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("OPTION_DESC");
            entity.Property(e => e.OptionExtendedDesc)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("OPTION_EXTENDED_DESC");
            entity.Property(e => e.OptionLongDesc)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("OPTION_LONG_DESC");
            entity.Property(e => e.OptionNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("OPTION_NOTES");
            entity.Property(e => e.OptionNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("OPTION_NUMBER");
            entity.Property(e => e.OptionQty).HasColumnName("OPTION_QTY");
            entity.Property(e => e.OptionSalesPrice).HasColumnName("OPTION_SALES_PRICE");
            entity.Property(e => e.OptionSelections)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("OPTION_SELECTIONS");
            entity.Property(e => e.PlanName)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("Plan_Name");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SalesconfigcooptionsId).HasColumnName("SALESCONFIGCOOPTIONS_ID");
            entity.Property(e => e.SalesconfigoptionsId).HasColumnName("SALESCONFIGOPTIONS_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.WmsplanNum)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WMSPlanNum");

            entity.HasOne(d => d.Estheader).WithMany(p => p.Estoptions)
                .HasForeignKey(d => d.EstheaderId)
                .HasConstraintName("FK_ESTOPTION_ESTHEADER");

            entity.HasOne(d => d.Salesconfigcooptions).WithMany(p => p.Estoptions)
                .HasForeignKey(d => d.SalesconfigcooptionsId)
                .HasConstraintName("fk_SALESCONFIGCOOPTIONSEstoption");

            entity.HasOne(d => d.Salesconfigoptions).WithMany(p => p.Estoptions)
                .HasForeignKey(d => d.SalesconfigoptionsId)
                .HasConstraintName("fk_SALESCONFIGOPTIONSEstoption");
        });

        modelBuilder.Entity<HomeArea>(entity =>
        {
            entity.HasKey(e => e.HomeAreaId).HasName("PK_RoomType");

            entity.ToTable("HOME_AREA");

            entity.Property(e => e.HomeAreaId).HasColumnName("HOME_AREA_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.HomeArea1)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("HOME_AREA");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.Note)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("NOTE");
            entity.Property(e => e.RecordTimeStampe)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
        });

        modelBuilder.Entity<ItemCostDetail>(entity =>
        {
            entity.ToTable("ITEM_COST_DETAIL");

            entity.Property(e => e.ItemCostDetailId).HasColumnName("ITEM_COST_DETAIL_ID");
            entity.Property(e => e.Activity)
                .HasMaxLength(36)
                .IsUnicode(false)
                .HasColumnName("ACTIVITY");
            entity.Property(e => e.CalcBasis).HasColumnName("CALC_BASIS");
            entity.Property(e => e.CalcPercent).HasColumnName("CALC_PERCENT");
            entity.Property(e => e.CnvFctr).HasColumnName("CNV_FCTR");
            entity.Property(e => e.CombinedId).HasColumnName("COMBINED_ID");
            entity.Property(e => e.CostsId).HasColumnName("COSTS_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.CubitId)
                .HasMaxLength(128)
                .IsUnicode(false)
                .HasColumnName("CUBIT_ID");
            entity.Property(e => e.ElevationCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("ELEVATION_CODE");
            entity.Property(e => e.ErrorCount).HasColumnName("ERROR_COUNT");
            entity.Property(e => e.Errors)
                .HasMaxLength(21)
                .IsUnicode(false)
                .HasColumnName("ERRORS");
            entity.Property(e => e.EstdetailId).HasColumnName("ESTDETAIL_ID");
            entity.Property(e => e.ExcludeFromPo)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("EXCLUDE_FROM_PO");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.ItemDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("ITEM_DESC");
            entity.Property(e => e.ItemNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("ITEM_NOTES");
            entity.Property(e => e.ItemNumber)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("ITEM_NUMBER");
            entity.Property(e => e.JcCategory)
                .HasMaxLength(3)
                .IsUnicode(false)
                .HasColumnName("JC_CATEGORY");
            entity.Property(e => e.JcCategoryWbs)
                .HasMaxLength(3)
                .IsUnicode(false)
                .HasColumnName("JC_CATEGORY_WBS");
            entity.Property(e => e.JcExtra)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JC_EXTRA");
            entity.Property(e => e.JcExtraDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("JC_EXTRA_DESC");
            entity.Property(e => e.JcExtraWbs)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JC_EXTRA_WBS");
            entity.Property(e => e.JcPhase)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JC_PHASE");
            entity.Property(e => e.JcPhaseWbs)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JC_PHASE_WBS");
            entity.Property(e => e.LengthWbs).HasColumnName("LENGTH_WBS");
            entity.Property(e => e.LumpSum)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("LUMP_SUM");
            entity.Property(e => e.MasterItemId).HasColumnName("MASTER_ITEM_ID");
            entity.Property(e => e.MasterItemWbs)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("MASTER_ITEM_WBS");
            entity.Property(e => e.MiscDescWbs)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("MISC_DESC_WBS");
            entity.Property(e => e.MiscLengthWbs).HasColumnName("MISC_LENGTH_WBS");
            entity.Property(e => e.MiscQtyWbs).HasColumnName("MISC_QTY_WBS");
            entity.Property(e => e.Multdiv)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("MULTDIV");
            entity.Property(e => e.OrderAmount).HasColumnName("ORDER_AMOUNT");
            entity.Property(e => e.OrderLength).HasColumnName("ORDER_LENGTH");
            entity.Property(e => e.OrderLengthQty).HasColumnName("ORDER_LENGTH_QTY");
            entity.Property(e => e.OrderQty).HasColumnName("ORDER_QTY");
            entity.Property(e => e.OrderUnit)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("ORDER_UNIT");
            entity.Property(e => e.OrderUnitPrice).HasColumnName("ORDER_UNIT_PRICE");
            entity.Property(e => e.PactivityId).HasColumnName("PACTIVITY_ID");
            entity.Property(e => e.PeAssemblyCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("PE_ASSEMBLY_CODE");
            entity.Property(e => e.PeCategoryCode)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PE_CATEGORY_CODE");
            entity.Property(e => e.PeLocation)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("PE_LOCATION");
            entity.Property(e => e.PeLumpSumAmount).HasColumnName("PE_LUMP_SUM_AMOUNT");
            entity.Property(e => e.PeUnitPrice).HasColumnName("PE_UNIT_PRICE");
            entity.Property(e => e.PeUnitPriceDtCg)
                .HasColumnType("smalldatetime")
                .HasColumnName("PE_UNIT_PRICE_DT_CG");
            entity.Property(e => e.PeeHeaderId).HasColumnName("PEE_HEADER_ID");
            entity.Property(e => e.PhaseCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("PHASE_CODE");
            entity.Property(e => e.PodetailId).HasColumnName("PODETAIL_ID");
            entity.Property(e => e.PoheaderId).HasColumnName("POHEADER_ID");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.ReissueItem)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("REISSUE_ITEM");
            entity.Property(e => e.Releasecode)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("RELEASECODE");
            entity.Property(e => e.RndDir)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("RND_DIR");
            entity.Property(e => e.RndUnit).HasColumnName("RND_UNIT");
            entity.Property(e => e.SelectedSubNumber).HasColumnName("SELECTED_SUB_NUMBER");
            entity.Property(e => e.SeqNumber).HasColumnName("SEQ_NUMBER");
            entity.Property(e => e.SessionId).HasColumnName("SESSION_ID");
            entity.Property(e => e.SortKey1)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SORT_KEY_1");
            entity.Property(e => e.SortKey2)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SORT_KEY_2");
            entity.Property(e => e.SubName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("SUB_NAME");
            entity.Property(e => e.SubNumberWbs).HasColumnName("SUB_NUMBER_WBS");
            entity.Property(e => e.SupplierContCoId).HasColumnName("SUPPLIER_CONT_CO_ID");
            entity.Property(e => e.SupplierContractsId).HasColumnName("SUPPLIER_CONTRACTS_ID");
            entity.Property(e => e.SupplierCostreservationId).HasColumnName("SUPPLIER_COSTRESERVATION_ID");
            entity.Property(e => e.TakeoffQty).HasColumnName("TAKEOFF_QTY");
            entity.Property(e => e.TakeoffUnit)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("TAKEOFF_UNIT");
            entity.Property(e => e.TaxAmount).HasColumnName("TAX_AMOUNT");
            entity.Property(e => e.TaxGroup)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("TAX_GROUP");
            entity.Property(e => e.TaxGroupType)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("TAX_GROUP_TYPE");
            entity.Property(e => e.TaxPercent).HasColumnName("TAX_PERCENT");
            entity.Property(e => e.Taxable)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("TAXABLE");
            entity.Property(e => e.TradeName)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("TRADE_NAME");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UseEstCost)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("USE_EST_COST");
            entity.Property(e => e.UseSortKey2)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("USE_SORT_KEY_2");
            entity.Property(e => e.UseWaste)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("USE_WASTE");
            entity.Property(e => e.VarianceJcCategory)
                .HasMaxLength(3)
                .IsUnicode(false)
                .HasColumnName("VARIANCE_JC_CATEGORY");
            entity.Property(e => e.VpoNumber).HasColumnName("VPO_NUMBER");
            entity.Property(e => e.VpoVendorinvoice)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("VPO_VENDORINVOICE");
            entity.Property(e => e.WarningCount).HasColumnName("WARNING_COUNT");
            entity.Property(e => e.Warnings)
                .HasMaxLength(21)
                .IsUnicode(false)
                .HasColumnName("WARNINGS");
            entity.Property(e => e.Waste).HasColumnName("WASTE");
            entity.Property(e => e.WorksheetId).HasColumnName("WORKSHEET_ID");
        });

        modelBuilder.Entity<ItemCostSummary>(entity =>
        {
            entity.HasKey(e => e.ItemCostDetailId);

            entity.ToTable("ITEM_COST_SUMMARY");

            entity.Property(e => e.ItemCostDetailId)
                .ValueGeneratedNever()
                .HasColumnName("ITEM_COST_DETAIL_ID");
            entity.Property(e => e.Activity)
                .HasMaxLength(36)
                .IsUnicode(false)
                .HasColumnName("ACTIVITY");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.ErrorCount).HasColumnName("ERROR_COUNT");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.OrderAmount).HasColumnName("ORDER_AMOUNT");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SelectedSubNumber).HasColumnName("SELECTED_SUB_NUMBER");
            entity.Property(e => e.SessionId).HasColumnName("SESSION_ID");
            entity.Property(e => e.SubName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("SUB_NAME");
            entity.Property(e => e.TaxAmount).HasColumnName("TAX_AMOUNT");
            entity.Property(e => e.TradeName)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("TRADE_NAME");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.WarningCount).HasColumnName("WARNING_COUNT");
        });

        modelBuilder.Entity<Jccategory>(entity =>
        {
            entity.HasKey(e => e.JccategoryId).HasName("pk_JCCATEGORY_ID");

            entity.ToTable("JCCATEGORY");

            entity.Property(e => e.JccategoryId).HasColumnName("JCCATEGORY_ID");
            entity.Property(e => e.Catdescription)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("CATDESCRIPTION");
            entity.Property(e => e.Category)
                .HasMaxLength(3)
                .IsUnicode(false)
                .HasColumnName("CATEGORY");
            entity.Property(e => e.CategoryType)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("CATEGORY_TYPE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DefaultCoCategory)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("DEFAULT_CO_CATEGORY");
            entity.Property(e => e.DefaultCostVarianceCategory)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("DEFAULT_COST_VARIANCE_CATEGORY");
            entity.Property(e => e.DefaultTaxRoundingCategory)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("DEFAULT_TAX_ROUNDING_CATEGORY");
            entity.Property(e => e.IgBusinessRuleId).HasColumnName("IG_BUSINESS_RULE_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JccosttypeId).HasColumnName("JCCOSTTYPE_ID");
            entity.Property(e => e.PrintPoIfAllThisCat)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PRINT_PO_IF_ALL_THIS_CAT");
            entity.Property(e => e.PrintPricesOnPo)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PRINT_PRICES_ON_PO");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SendBudgetToJc)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SEND_BUDGET_TO_JC");
            entity.Property(e => e.SentCommitmentToJc)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SENT_COMMITMENT_TO_JC");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.VpoApprovalUser)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("VPO_APPROVAL_USER");

            entity.HasOne(d => d.Jccosttype).WithMany(p => p.Jccategories)
                .HasForeignKey(d => d.JccosttypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_JCCOSTTYPE_ID");
        });

        modelBuilder.Entity<Jccostcode>(entity =>
        {
            entity.HasKey(e => e.JccostcodeId).HasName("pk_JCCOSTCODE_ID");

            entity.ToTable("JCCOSTCODE");

            entity.Property(e => e.JccostcodeId).HasColumnName("JCCOSTCODE_ID");
            entity.Property(e => e.AcntDbId).HasColumnName("ACNT_DB_ID");
            entity.Property(e => e.AcntUomId).HasColumnName("ACNT_UOM_ID");
            entity.Property(e => e.CcDescription)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("CC_DESCRIPTION");
            entity.Property(e => e.CostCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("COST_CODE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IgBusinessRuleId).HasColumnName("IG_BUSINESS_RULE_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<Jccosttype>(entity =>
        {
            entity.HasKey(e => e.JccosttypeId).HasName("pk_JCCOSTTYPE_ID");

            entity.ToTable("JCCOSTTYPE");

            entity.Property(e => e.JccosttypeId).HasColumnName("JCCOSTTYPE_ID");
            entity.Property(e => e.AcntDbId).HasColumnName("ACNT_DB_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Defaulttaxgroup)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("DEFAULTTAXGROUP");
            entity.Property(e => e.IgCostClassNumber).HasColumnName("IG_COST_CLASS_NUMBER");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.Jccosttype1)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JCCOSTTYPE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<Job>(entity =>
        {
            entity.HasKey(e => e.JobNumber).HasName("PK_Job");

            entity.ToTable("JOB");

            entity.HasIndex(e => e.SubdivisionId, "nci_wi_JOB_24D289FE71DE3793699935E3E773BB35");

            entity.Property(e => e.JobNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JOB_NUMBER");
            entity.Property(e => e.AcquisitionLotCost)
                .HasColumnType("decimal(18, 4)")
                .HasColumnName("ACQUISITION_LOT_COST");
            entity.Property(e => e.ApprovedDepositRequirement).HasColumnName("APPROVED_DEPOSIT_REQUIREMENT");
            entity.Property(e => e.BuildingNum)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CustomerId).HasColumnName("CUSTOMER_ID");
            entity.Property(e => e.ElevationCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("ELEVATION_CODE");
            entity.Property(e => e.ElevationDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("ELEVATION_DESC");
            entity.Property(e => e.FieldSuper)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("FIELD_SUPER");
            entity.Property(e => e.GarageOrientation)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("GARAGE_ORIENTATION");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JobAddress1)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("JOB_ADDRESS1");
            entity.Property(e => e.JobAddress2)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("JOB_ADDRESS2");
            entity.Property(e => e.JobCity)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("JOB_CITY");
            entity.Property(e => e.JobCounty)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("JOB_COUNTY");
            entity.Property(e => e.JobDesc)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("JOB_DESC");
            entity.Property(e => e.JobPostingGroup)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("JOB_POSTING_GROUP");
            entity.Property(e => e.JobState)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("JOB_STATE");
            entity.Property(e => e.JobZipCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JOB_ZIP_CODE");
            entity.Property(e => e.LotAvailability).HasColumnName("LOT_AVAILABILITY");
            entity.Property(e => e.LotCost)
                .HasColumnType("decimal(18, 4)")
                .HasColumnName("LOT_COST");
            entity.Property(e => e.LotNumber)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("Lot_Number");
            entity.Property(e => e.LotPremium)
                .HasColumnType("decimal(18, 4)")
                .HasColumnName("LOT_PREMIUM");
            entity.Property(e => e.LotSectionCode)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("LOT_SECTION_CODE");
            entity.Property(e => e.LotStatus).HasColumnName("LOT_STATUS");
            entity.Property(e => e.LotStatusId).HasColumnName("LOT_STATUS_ID");
            entity.Property(e => e.LotSwing)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("LOT_SWING");
            entity.Property(e => e.LotUnit)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("LOT_UNIT");
            entity.Property(e => e.LotWidth)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("LOT_WIDTH");
            entity.Property(e => e.ModelName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("MODEL_NAME");
            entity.Property(e => e.Notes).IsUnicode(false);
            entity.Property(e => e.OverLot)
                .HasColumnType("datetime")
                .HasColumnName("OVER_LOT");
            entity.Property(e => e.Phase)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("PHASE");
            entity.Property(e => e.PlanCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("PLAN_CODE");
            entity.Property(e => e.PlanName)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("PLAN_NAME");
            entity.Property(e => e.PossessionStatus)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("POSSESSION_STATUS");
            entity.Property(e => e.ProdTimeStamp)
                .HasMaxLength(8)
                .IsFixedLength();
            entity.Property(e => e.ProjectMgr)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("PROJECT_MGR");
            entity.Property(e => e.ProjectedTakeDownDate)
                .HasColumnType("date")
                .HasColumnName("PROJECTED_TAKE_DOWN_DATE");
            entity.Property(e => e.RecordTimeStampe)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Restrictions)
                .HasMaxLength(4000)
                .IsUnicode(false)
                .HasColumnName("RESTRICTIONS");
            entity.Property(e => e.SalesContact)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("SALES_CONTACT");
            entity.Property(e => e.StatsStickStartDate)
                .HasColumnType("datetime")
                .HasColumnName("STATS_STICK_START_DATE");
            entity.Property(e => e.StickBuilingNum)
                .HasMaxLength(30)
                .IsUnicode(false);
            entity.Property(e => e.SubdivisionClass)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("SUBDIVISION_CLASS");
            entity.Property(e => e.SubdivisionId).HasColumnName("SUBDIVISION_ID");
            entity.Property(e => e.Supervisor)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("SUPERVISOR");
            entity.Property(e => e.TakedownDate)
                .HasColumnType("datetime")
                .HasColumnName("TAKEDOWN_DATE");
            entity.Property(e => e.TakedownType)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("TAKEDOWN_TYPE");
            entity.Property(e => e.UpdateDateTime).HasColumnType("datetime");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UserContact1)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT1");
            entity.Property(e => e.UserContact2)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT2");

            entity.HasOne(d => d.Subdivision).WithMany(p => p.Jobs)
                .HasForeignKey(d => d.SubdivisionId)
                .HasConstraintName("FK_Job_Subdivision");
        });

        modelBuilder.Entity<JobAttachment>(entity =>
        {
            entity.HasKey(e => e.AttachmentId);

            entity.ToTable("JOB_ATTACHMENTS");

            entity.Property(e => e.AttachmentId).HasColumnName("ATTACHMENT_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DoctypeId).HasColumnName("DOCTYPE_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JobNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JOB_NUMBER");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("NAME");
            entity.Property(e => e.Path)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("PATH");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Doctype).WithMany(p => p.JobAttachments)
                .HasForeignKey(d => d.DoctypeId)
                .HasConstraintName("FK_JOB_ATTACHMENTS_DOCTYPES");

            entity.HasOne(d => d.JobNumberNavigation).WithMany(p => p.JobAttachments)
                .HasForeignKey(d => d.JobNumber)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_JOB_ATTACHMENTS_Job");
        });

        modelBuilder.Entity<JobAttachmentNotify>(entity =>
        {
            entity.HasKey(e => e.AttachmentNotifyId).HasName("JOB_ATTACHMENT_NOTIFY_PK");

            entity.ToTable("JOB_ATTACHMENT_NOTIFY");

            entity.Property(e => e.AttachmentNotifyId).HasColumnName("ATTACHMENT_NOTIFY_ID");
            entity.Property(e => e.AttachmentDate)
                .HasColumnType("date")
                .HasColumnName("ATTACHMENT_DATE");
            entity.Property(e => e.AttachmentId).HasColumnName("ATTACHMENT_ID");
            entity.Property(e => e.ContactId).HasColumnName("CONTACT_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DateModified)
                .HasColumnType("datetime")
                .HasColumnName("DATE_MODIFIED");
            entity.Property(e => e.DateViewed)
                .HasColumnType("datetime")
                .HasColumnName("DATE_VIEWED");
            entity.Property(e => e.EmailContent)
                .IsUnicode(false)
                .HasColumnName("EMAIL_CONTENT");
            entity.Property(e => e.EmailTitle)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("EMAIL_TITLE");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.Notes)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("NOTES");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Status).HasColumnName("STATUS");
            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.Userid)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USERID");

            entity.HasOne(d => d.Attachment).WithMany(p => p.JobAttachmentNotifies)
                .HasForeignKey(d => d.AttachmentId)
                .HasConstraintName("FK_JOB_ATTACHMENT_NOTIFY_JOB_ATTACHMENTS");
        });

        modelBuilder.Entity<JobContact>(entity =>
        {
            entity.ToTable("JOB_CONTACT");

            entity.Property(e => e.JobContactId).HasColumnName("JOB_CONTACT_ID");
            entity.Property(e => e.AreaProductionManager)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("AREA_PRODUCTION_MANAGER");
            entity.Property(e => e.AssistantConstructionManager)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("ASSISTANT_CONSTRUCTION_MANAGER");
            entity.Property(e => e.ConstructionManager)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("CONSTRUCTION_MANAGER");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DirectorOfConstruction)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("DIRECTOR_OF_CONSTRUCTION");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JobNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JOB_NUMBER");
            entity.Property(e => e.Other)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("OTHER");
            entity.Property(e => e.ProtectFromMaster)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PROTECT_FROM_MASTER");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SeniorConstructionManager)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SENIOR_CONSTRUCTION_MANAGER");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UserId)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_ID");
            entity.Property(e => e.VpoApprovalLevel).HasColumnName("VPO_APPROVAL_LEVEL");

            entity.HasOne(d => d.JobNumberNavigation).WithMany(p => p.JobContacts)
                .HasForeignKey(d => d.JobNumber)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_JOB_CONTACT_Job");

            entity.HasOne(d => d.User).WithMany(p => p.JobContacts)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_JOB_CONTACT_USERS");
        });

        modelBuilder.Entity<JobCustomer>(entity =>
        {
            entity.ToTable("JOB_CUSTOMER");

            entity.Property(e => e.JobCustomerId).HasColumnName("JOB_CUSTOMER_ID");
            entity.Property(e => e.Cancelled).HasColumnName("CANCELLED");
            entity.Property(e => e.Createdby)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("CREATEDBY");
            entity.Property(e => e.Createddatetime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime")
                .HasColumnName("CREATEDDATETIME");
            entity.Property(e => e.CustomerId).HasColumnName("CUSTOMER_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JobNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JOB_NUMBER");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Transfer).HasColumnName("TRANSFER");
            entity.Property(e => e.TransfferedTo)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("TRANSFFERED_TO");
            entity.Property(e => e.Updatedby)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("UPDATEDBY");
            entity.Property(e => e.Updateddatetime)
                .HasColumnType("datetime")
                .HasColumnName("UPDATEDDATETIME");

            entity.HasOne(d => d.Customer).WithMany(p => p.JobCustomers)
                .HasForeignKey(d => d.CustomerId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_JOB_CUSTOMER_CUSTOMER");

            entity.HasOne(d => d.JobNumberNavigation).WithMany(p => p.JobCustomers)
                .HasForeignKey(d => d.JobNumber)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_JOB_CUSTOMER_Job");
        });

        modelBuilder.Entity<MasterAttributeGroup>(entity =>
        {
            entity.HasKey(e => e.AttributeGroupId).HasName("PK_MasterAttributeGroup");

            entity.ToTable("MASTER_ATTRIBUTE_GROUP");

            entity.Property(e => e.AttributeGroupId).HasColumnName("ATTRIBUTE_GROUP_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Description)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("DESCRIPTION");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStampe)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Seq).HasColumnName("SEQ");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
            entity.Property(e => e.UseText).HasColumnName("USE_TEXT");
        });

        modelBuilder.Entity<MasterAttributeItem>(entity =>
        {
            entity.HasKey(e => e.AttributeItemId).HasName("PK_MasterAttributeItem");

            entity.ToTable("MASTER_ATTRIBUTE_ITEM");

            entity.Property(e => e.AttributeItemId).HasColumnName("ATTRIBUTE_ITEM_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Description)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("DESCRIPTION");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStampe)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Seq).HasColumnName("SEQ");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
        });

        modelBuilder.Entity<MasterItem>(entity =>
        {
            entity.HasKey(e => e.MasterItemId).HasName("PK_MasterItems");

            entity.ToTable("MASTER_ITEMS");

            entity.HasIndex(e => new { e.BomClassId, e.IsActive }, "nci_wi_MasterItems_6F673033362D253433E95AAA0D4775C6");

            entity.Property(e => e.MasterItemId).HasColumnName("MASTER_ITEM_ID");
            entity.Property(e => e.BomClassId).HasColumnName("BOM_CLASS_ID");
            entity.Property(e => e.CalcBasis).HasColumnName("CALC_BASIS");
            entity.Property(e => e.CalcPercent).HasColumnName("CALC_PERCENT");
            entity.Property(e => e.CnvFctr).HasColumnName("CNV_FCTR");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DeletedFromPe)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("DELETED_FROM_PE");
            entity.Property(e => e.EstDbOwner).HasColumnName("EST_DB_OWNER");
            entity.Property(e => e.ExcludeFromPo)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("EXCLUDE_FROM_PO");
            entity.Property(e => e.Formula)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("FORMULA");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.ItemDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("ITEM_DESC");
            entity.Property(e => e.ItemNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("ITEM_NOTES");
            entity.Property(e => e.ItemNumber)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("ITEM_NUMBER");
            entity.Property(e => e.JcCategory)
                .HasMaxLength(3)
                .IsUnicode(false)
                .HasColumnName("JC_CATEGORY");
            entity.Property(e => e.JcPhase)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JC_PHASE");
            entity.Property(e => e.MasterItemPhaseId).HasColumnName("MASTER_ITEM_PHASE_ID");
            entity.Property(e => e.Multdiv)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("MULTDIV");
            entity.Property(e => e.OldItemNumber)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("OLD_ITEM_NUMBER");
            entity.Property(e => e.OrderUnit)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("ORDER_UNIT");
            entity.Property(e => e.PeCategoryCode)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PE_CATEGORY_CODE");
            entity.Property(e => e.PeUnitPrice).HasColumnName("PE_UNIT_PRICE");
            entity.Property(e => e.PeUnitPriceDtCg)
                .HasColumnType("smalldatetime")
                .HasColumnName("PE_UNIT_PRICE_DT_CG");
            entity.Property(e => e.PlanSpecific)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PLAN_SPECIFIC");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.RndDir)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("RND_DIR");
            entity.Property(e => e.RndUnit).HasColumnName("RND_UNIT");
            entity.Property(e => e.TakeoffUnit)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("TAKEOFF_UNIT");
            entity.Property(e => e.Taxable)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("TAXABLE");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UseWaste)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("USE_WASTE");
            entity.Property(e => e.Waste).HasColumnName("WASTE");
            entity.Property(e => e.Wbs1)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_1");
            entity.Property(e => e.Wbs10)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_10");
            entity.Property(e => e.Wbs11)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_11");
            entity.Property(e => e.Wbs12)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_12");
            entity.Property(e => e.Wbs13)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_13");
            entity.Property(e => e.Wbs14)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_14");
            entity.Property(e => e.Wbs15)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_15");
            entity.Property(e => e.Wbs16)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_16");
            entity.Property(e => e.Wbs17)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_17");
            entity.Property(e => e.Wbs18)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_18");
            entity.Property(e => e.Wbs19)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_19");
            entity.Property(e => e.Wbs2)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_2");
            entity.Property(e => e.Wbs20)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_20");
            entity.Property(e => e.Wbs21)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_21");
            entity.Property(e => e.Wbs22)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_22");
            entity.Property(e => e.Wbs23)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_23");
            entity.Property(e => e.Wbs24)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_24");
            entity.Property(e => e.Wbs25)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_25");
            entity.Property(e => e.Wbs26)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_26");
            entity.Property(e => e.Wbs27)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_27");
            entity.Property(e => e.Wbs28)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_28");
            entity.Property(e => e.Wbs29)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_29");
            entity.Property(e => e.Wbs3)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_3");
            entity.Property(e => e.Wbs30)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_30");
            entity.Property(e => e.Wbs31)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_31");
            entity.Property(e => e.Wbs32)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_32");
            entity.Property(e => e.Wbs33)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_33");
            entity.Property(e => e.Wbs34)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_34");
            entity.Property(e => e.Wbs35)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_35");
            entity.Property(e => e.Wbs36)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_36");
            entity.Property(e => e.Wbs37)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_37");
            entity.Property(e => e.Wbs38)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_38");
            entity.Property(e => e.Wbs39)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_39");
            entity.Property(e => e.Wbs4)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_4");
            entity.Property(e => e.Wbs40)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_40");
            entity.Property(e => e.Wbs5)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_5");
            entity.Property(e => e.Wbs6)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_6");
            entity.Property(e => e.Wbs7)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_7");
            entity.Property(e => e.Wbs8)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_8");
            entity.Property(e => e.Wbs9)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("WBS_9");

            entity.HasOne(d => d.BomClass).WithMany(p => p.MasterItems)
                .HasForeignKey(d => d.BomClassId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MasterItems_BOM_CLASS");

            entity.HasOne(d => d.MasterItemPhase).WithMany(p => p.MasterItems)
                .HasForeignKey(d => d.MasterItemPhaseId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MasterItems_MasterItemPhases");
        });

        modelBuilder.Entity<MasterItemPhasis>(entity =>
        {
            entity.HasKey(e => e.MasterItemPhaseId).HasName("PK_MasterItemPhases");

            entity.ToTable("MASTER_ITEM_PHASES");

            entity.Property(e => e.MasterItemPhaseId).HasColumnName("MASTER_ITEM_PHASE_ID");
            entity.Property(e => e.AsmHeaderId).HasColumnName("ASM_HEADER_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DeletedFromPe)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("DELETED_FROM_PE");
            entity.Property(e => e.EstDbOwner).HasColumnName("EST_DB_OWNER");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JcPhaseCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JC_PHASE_CODE");
            entity.Property(e => e.MasterItemGroupId).HasColumnName("MASTER_ITEM_GROUP_ID");
            entity.Property(e => e.MatrixDesc1)
                .HasMaxLength(8)
                .IsUnicode(false)
                .HasColumnName("MATRIX_DESC_1");
            entity.Property(e => e.MatrixDesc2)
                .HasMaxLength(8)
                .IsUnicode(false)
                .HasColumnName("MATRIX_DESC_2");
            entity.Property(e => e.PhaseCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("PHASE_CODE");
            entity.Property(e => e.PhaseDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("PHASE_DESC");
            entity.Property(e => e.PhaseNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("PHASE_NOTES");
            entity.Property(e => e.PhaseUnitDesc)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("PHASE_UNIT_DESC");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.AsmHeader).WithMany(p => p.MasterItemPhases)
                .HasForeignKey(d => d.AsmHeaderId)
                .HasConstraintName("fk_HEADERIDITEM");
        });

        modelBuilder.Entity<MasterOption>(entity =>
        {
            entity.HasKey(e => e.OptionId).HasName("PK_MasterOptions");

            entity.ToTable("MASTER_OPTION");

            entity.HasIndex(e => e.OptionCode, "uniqueOptionCode").IsUnique();

            entity.Property(e => e.OptionId).HasColumnName("OPTION_ID");
            entity.Property(e => e.Active).HasColumnName("ACTIVE");
            entity.Property(e => e.ActiveDepositSched).HasColumnName("ACTIVE_DEPOSIT_SCHED");
            entity.Property(e => e.AddPriceToBase).HasColumnName("ADD_PRICE_TO_BASE");
            entity.Property(e => e.AgentModPrice).HasColumnName("AGENT_MOD_PRICE");
            entity.Property(e => e.AgentModQty).HasColumnName("AGENT_MOD_QTY");
            entity.Property(e => e.AgentModType).HasColumnName("AGENT_MOD_TYPE");
            entity.Property(e => e.Amount)
                .HasColumnType("decimal(9, 0)")
                .HasColumnName("AMOUNT");
            entity.Property(e => e.BeforeAfter)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("BEFORE_AFTER");
            entity.Property(e => e.CascadeOptionSettings).HasColumnName("CASCADE_OPTION_SETTINGS");
            entity.Property(e => e.CascadeUsersCanMod).HasColumnName("CASCADE_USERS_CAN_MOD");
            entity.Property(e => e.ConstrStageId).HasColumnName("CONSTR_STAGE_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CustChoiceReq).HasColumnName("CUST_CHOICE_REQ");
            entity.Property(e => e.CustomerTranslation)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("CUSTOMER_TRANSLATION");
            entity.Property(e => e.DaysFromTrigger).HasColumnName("DAYS_FROM_TRIGGER");
            entity.Property(e => e.DepositDesc)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("DEPOSIT_DESC");
            entity.Property(e => e.DepositTypeId).HasColumnName("DEPOSIT_TYPE_ID");
            entity.Property(e => e.DollarAmt).HasColumnName("DOLLAR_AMT");
            entity.Property(e => e.Elevation).HasColumnName("ELEVATION");
            entity.Property(e => e.EscrowStageId).HasColumnName("ESCROW_STAGE_ID");
            entity.Property(e => e.FloorOption).HasColumnName("FLOOR_OPTION");
            entity.Property(e => e.ImageUpload)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("IMAGE_UPLOAD");
            entity.Property(e => e.ImageUrl)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("IMAGE_URL");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.MaxQty)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("MAX_QTY");
            entity.Property(e => e.OptionCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("OPTION_CODE");
            entity.Property(e => e.OptionDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("OPTION_DESC");
            entity.Property(e => e.OptionGroupId).HasColumnName("OPTION_GROUP_ID");
            entity.Property(e => e.OptionLongDesc)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("OPTION_LONG_DESC");
            entity.Property(e => e.OptionSelectionType)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("OPTION_SELECTION_TYPE");
            entity.Property(e => e.PrintOption).HasColumnName("PRINT_OPTION");
            entity.Property(e => e.ProductSpecifications)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("PRODUCT_SPECIFICATIONS");
            entity.Property(e => e.RecordTimeStampe)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Restrictions)
                .IsUnicode(false)
                .HasColumnName("RESTRICTIONS");
            entity.Property(e => e.Room).HasColumnName("ROOM");
            entity.Property(e => e.SqFt).HasColumnName("SQ_FT");
            entity.Property(e => e.TriggerEventId).HasColumnName("TRIGGER_EVENT_ID");
            entity.Property(e => e.UnitCost)
                .HasColumnType("decimal(8, 2)")
                .HasColumnName("UNIT_COST");
            entity.Property(e => e.UnitMeasureId).HasColumnName("UNIT_MEASURE_ID");
            entity.Property(e => e.UnitPrice)
                .HasColumnType("decimal(8, 2)")
                .HasColumnName("UNIT_PRICE");
            entity.Property(e => e.UnitQty)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("UNIT_QTY");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");

            entity.HasOne(d => d.OptionGroup).WithMany(p => p.MasterOptions)
                .HasForeignKey(d => d.OptionGroupId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MasterOption_OptionGroups");
        });

        modelBuilder.Entity<MasterPlan>(entity =>
        {
            entity.HasKey(e => e.MasterPlanId).HasName("PK_MasterPlan");

            entity.ToTable("MASTER_PLAN");

            entity.Property(e => e.MasterPlanId).HasColumnName("MASTER_PLAN_ID");
            entity.Property(e => e.AsmGroup).HasColumnName("ASM_GROUP_");
            entity.Property(e => e.BaseHousePlan)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("BASE_HOUSE_PLAN");
            entity.Property(e => e.Bathrooms)
                .HasColumnType("decimal(4, 2)")
                .HasColumnName("BATHROOMS");
            entity.Property(e => e.Bedrooms).HasColumnName("BEDROOMS");
            entity.Property(e => e.ConstructionDays)
                .HasColumnType("decimal(5, 2)")
                .HasColumnName("CONSTRUCTION_DAYS");
            entity.Property(e => e.Cost)
                .HasColumnType("decimal(7, 0)")
                .HasColumnName("COST");
            entity.Property(e => e.CreadedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.DefaultMarginPct).HasColumnName("DEFAULT_MARGIN_PCT");
            entity.Property(e => e.DefaultPrice)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("DEFAULT_PRICE");
            entity.Property(e => e.DeletedFromPe)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("DELETED_FROM_PE");
            entity.Property(e => e.Description)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("DESCRIPTION");
            entity.Property(e => e.ElevationCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("ELEVATION_CODE");
            entity.Property(e => e.ElevationNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("ELEVATION_NOTES");
            entity.Property(e => e.GroupPhaseCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("GROUP_PHASE_CODE");
            entity.Property(e => e.GroupPhaseDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("GROUP_PHASE_DESC");
            entity.Property(e => e.GroupPhaseNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("GROUP_PHASE_NOTES");
            entity.Property(e => e.HasGarage).HasColumnName("HAS_GARAGE");
            entity.Property(e => e.ImangeCode)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.ItemGroupCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("ITEM_GROUP_CODE");
            entity.Property(e => e.MasterItemGroupId).HasColumnName("MASTER_ITEM_GROUP_ID");
            entity.Property(e => e.MinLotSize).HasColumnName("MIN_LOT_SIZE");
            entity.Property(e => e.PlanModelNum)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("PLAN_MODEL_NUM");
            entity.Property(e => e.PlanName)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("PLAN_NAME");
            entity.Property(e => e.PlanNum)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("PLAN_NUM");
            entity.Property(e => e.PlanSize).HasColumnName("PLAN_SIZE");
            entity.Property(e => e.PlanTypeId).HasColumnName("PLAN_TYPE_ID");
            entity.Property(e => e.RecordTimeStampe)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SquareFeet).HasColumnName("SQUARE_FEET");
            entity.Property(e => e.SsmasterPlanId).HasColumnName("SSMasterPlanID");
            entity.Property(e => e.Stories)
                .HasColumnType("decimal(4, 2)")
                .HasColumnName("STORIES");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
            entity.Property(e => e.WmsmasterPlanId).HasColumnName("WMSMasterPlanID");
            entity.Property(e => e.WmsplanNum)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("WMSPlanNum");

            entity.HasOne(d => d.PlanType).WithMany(p => p.MasterPlans)
                .HasForeignKey(d => d.PlanTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MasterPlan_PLAN_TYPES");
        });

        modelBuilder.Entity<Material>(entity =>
        {
            entity.HasKey(e => e.MateriaId).HasName("PK__Material__0D019D813B1E1D06");

            entity.ToTable("Material");

            entity.Property(e => e.MateriaId).HasColumnName("MateriaID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValueSql("((1))");
            entity.Property(e => e.Material1)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("Material");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
        });

        modelBuilder.Entity<MaterialColorScheme>(entity =>
        {
            entity.ToTable("MaterialColorScheme");

            entity.Property(e => e.MaterialColorSchemeId).HasColumnName("MaterialColorSchemeID");
            entity.Property(e => e.ColorSchemeId).HasColumnName("ColorSchemeID");
            entity.Property(e => e.ColorSchemeNum)
                .HasMaxLength(150)
                .IsUnicode(false);
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.MaterialId).HasColumnName("MaterialID");
            entity.Property(e => e.PlanOptionId).HasColumnName("PLAN_OPTION_ID");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");

            entity.HasOne(d => d.ColorScheme).WithMany(p => p.MaterialColorSchemes)
                .HasForeignKey(d => d.ColorSchemeId)
                .HasConstraintName("FK_MaterialColorScheme_ColorScheme");

            entity.HasOne(d => d.Material).WithMany(p => p.MaterialColorSchemes)
                .HasForeignKey(d => d.MaterialId)
                .HasConstraintName("FK_MaterialColorScheme_Material");

            entity.HasOne(d => d.PlanOption).WithMany(p => p.MaterialColorSchemes)
                .HasForeignKey(d => d.PlanOptionId)
                .HasConstraintName("FK_MaterialColorScheme_AVAILABLE_PLAN_OPTION");
        });

        modelBuilder.Entity<Milestone>(entity =>
        {
            entity.ToTable("MILESTONE");

            entity.Property(e => e.MilestoneId).HasColumnName("MILESTONE_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DivId).HasColumnName("DIV_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.MilestoneName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("MILESTONE_NAME");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.ReportingId).HasColumnName("REPORTING_ID");
            entity.Property(e => e.Seq).HasColumnName("SEQ");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<OptionGroup>(entity =>
        {
            entity.HasKey(e => e.OptionGroupId).HasName("PK_OptionGroups");

            entity.ToTable("OPTION_GROUPS");

            entity.Property(e => e.OptionGroupId).HasColumnName("OPTION_GROUP_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DeftMarkupAmount).HasColumnName("DEFT_MARKUP_AMOUNT");
            entity.Property(e => e.DeftMarkupType).HasColumnName("DEFT_MARKUP_TYPE");
            entity.Property(e => e.DivId).HasColumnName("DIV_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.Note)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("NOTE");
            entity.Property(e => e.OptCatCode)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("OPT_CAT_CODE");
            entity.Property(e => e.OptionGroupLetter)
                .HasMaxLength(5)
                .IsUnicode(false)
                .HasColumnName("OPTION_GROUP_LETTER");
            entity.Property(e => e.OptionGroupName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("OPTION_GROUP_NAME");
            entity.Property(e => e.PayCommission).HasColumnName("PAY_COMMISSION");
            entity.Property(e => e.RecordTimeStampe)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SsoptionGroupId).HasColumnName("SSOptionGroupID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
            entity.Property(e => e.WmsoptionGroupId).HasColumnName("WMSOptionGroupID");
        });

        modelBuilder.Entity<OptionType>(entity =>
        {
            entity.HasKey(e => e.OptionTypeId).HasName("PK__OptionTy__96A2FCEEF6883FB6");

            entity.ToTable("OPTION_TYPE");

            entity.Property(e => e.OptionTypeId).HasColumnName("OPTION_TYPE_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.OptionType1)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("OPTION_TYPE");
            entity.Property(e => e.RecordTimeStampe)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
        });

        modelBuilder.Entity<Pactivity>(entity =>
        {
            entity.ToTable("PACTIVITY");

            entity.Property(e => e.PactivityId).HasColumnName("PACTIVITY_ID");
            entity.Property(e => e.Activity)
                .HasMaxLength(36)
                .IsUnicode(false)
                .HasColumnName("ACTIVITY");
            entity.Property(e => e.AutoCreateInvoice)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("AUTO_CREATE_INVOICE");
            entity.Property(e => e.BomClassId).HasColumnName("BOM_CLASS_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Description)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("DESCRIPTION");
            entity.Property(e => e.DivId).HasColumnName("DIV_ID");
            entity.Property(e => e.IncludeSelectionsOnPo)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("INCLUDE_SELECTIONS_ON_PO");
            entity.Property(e => e.InvApprovalRole).HasColumnName("INV_APPROVAL_ROLE");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JccategoryId).HasColumnName("JCCATEGORY_ID");
            entity.Property(e => e.JccostcodeId).HasColumnName("JCCOSTCODE_ID");
            entity.Property(e => e.MasterItemId).HasColumnName("MasterItemID");
            entity.Property(e => e.MasterPactivityId).HasColumnName("MASTER_PACTIVITY_ID");
            entity.Property(e => e.Notes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("NOTES");
            entity.Property(e => e.Pecategory)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PECATEGORY");
            entity.Property(e => e.Poindex)
                .HasMaxLength(9)
                .IsUnicode(false)
                .HasColumnName("POINDEX");
            entity.Property(e => e.Printlocations)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PRINTLOCATIONS");
            entity.Property(e => e.Printschedule)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PRINTSCHEDULE");
            entity.Property(e => e.ProtectFromMaster)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PROTECT_FROM_MASTER");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Releasecode)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("RELEASECODE");
            entity.Property(e => e.SactivityId).HasColumnName("SACTIVITY_ID");
            entity.Property(e => e.Taxable)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("TAXABLE");
            entity.Property(e => e.TradeId).HasColumnName("TRADE_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.BomClass).WithMany(p => p.Pactivities)
                .HasForeignKey(d => d.BomClassId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PACTIVITY_BOM_CLASS");

            entity.HasOne(d => d.Jccategory).WithMany(p => p.Pactivities)
                .HasForeignKey(d => d.JccategoryId)
                .HasConstraintName("fk_JCCATEGORYPactivity");

            entity.HasOne(d => d.Jccostcode).WithMany(p => p.Pactivities)
                .HasForeignKey(d => d.JccostcodeId)
                .HasConstraintName("fk_JCCOSTCODEPactivity");

            entity.HasOne(d => d.MasterItem).WithMany(p => p.Pactivities)
                .HasForeignKey(d => d.MasterItemId)
                .HasConstraintName("fk_MasterItemIDPactivity");

            entity.HasOne(d => d.Sactivity).WithMany(p => p.Pactivities)
                .HasForeignKey(d => d.SactivityId)
                .HasConstraintName("FK_PACTIVITY_SACTIVITY");

            entity.HasOne(d => d.Trade).WithMany(p => p.Pactivities)
                .HasForeignKey(d => d.TradeId)
                .HasConstraintName("FK_PACTIVITY_TRADE");
        });

        modelBuilder.Entity<PactivityAreaSupplier>(entity =>
        {
            entity.HasKey(e => new { e.PactivityId, e.SubdivisionId }).HasName("PK_PACTIVITY_AREA_SUPPLIER_1");

            entity.ToTable("PACTIVITY_AREA_SUPPLIER");

            entity.Property(e => e.PactivityId).HasColumnName("PACTIVITY_ID");
            entity.Property(e => e.SubdivisionId).HasColumnName("SUBDIVISION_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Pactivity).WithMany(p => p.PactivityAreaSuppliers)
                .HasForeignKey(d => d.PactivityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PACTIVITY_AREA_SUPPLIER_PACTIVITY");

            entity.HasOne(d => d.SubNumberNavigation).WithMany(p => p.PactivityAreaSuppliers)
                .HasForeignKey(d => d.SubNumber)
                .HasConstraintName("fk_SUB_NUMBERIdPactivityAS");

            entity.HasOne(d => d.Subdivision).WithMany(p => p.PactivityAreaSuppliers)
                .HasForeignKey(d => d.SubdivisionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PACTIVITY_AREA_SUPPLIER_SUBDIVISION");
        });

        modelBuilder.Entity<PhasePlan>(entity =>
        {
            entity.HasKey(e => e.PhasePlanId).HasName("PK_PhasePlan");

            entity.ToTable("PHASE_PLAN");

            entity.HasIndex(e => new { e.SubdivisionId, e.MasterPlanId, e.Phase }, "uniquePhasePlan").IsUnique();

            entity.Property(e => e.PhasePlanId).HasColumnName("PHASE_PLAN_ID");
            entity.Property(e => e.AreaElevationSource).HasColumnName("AREA_ELEVATION_SOURCE");
            entity.Property(e => e.AssociatedEstimate)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("ASSOCIATED_ESTIMATE");
            entity.Property(e => e.BathRoom).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CurrCost).HasColumnName("CURR_COST");
            entity.Property(e => e.CurrDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("CURR_DATE");
            entity.Property(e => e.CurrMargin).HasColumnName("CURR_MARGIN");
            entity.Property(e => e.CurrSelling).HasColumnName("CURR_SELLING");
            entity.Property(e => e.ImageCode)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.LastCost).HasColumnName("LAST_COST");
            entity.Property(e => e.LastDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("LAST_DATE");
            entity.Property(e => e.LastMargin).HasColumnName("LAST_MARGIN");
            entity.Property(e => e.LastSelling).HasColumnName("LAST_SELLING");
            entity.Property(e => e.MarginLumpSum).HasColumnName("MARGIN_LUMP_SUM");
            entity.Property(e => e.MarginMarketValue).HasColumnName("MARGIN_MARKET_VALUE");
            entity.Property(e => e.MarginPercent).HasColumnName("MARGIN_PERCENT");
            entity.Property(e => e.MarginType).HasColumnName("MARGIN_TYPE");
            entity.Property(e => e.MarketingPackageId).HasColumnName("MarketingPackageID");
            entity.Property(e => e.MasterPlanId).HasColumnName("MASTER_PLAN_ID");
            entity.Property(e => e.NextCost).HasColumnName("NEXT_COST");
            entity.Property(e => e.NextDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("NEXT_DATE");
            entity.Property(e => e.NextMargin).HasColumnName("NEXT_MARGIN");
            entity.Property(e => e.NextSelling).HasColumnName("NEXT_SELLING");
            entity.Property(e => e.Phase)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("PHASE");
            entity.Property(e => e.PhasePlanCost)
                .HasColumnType("decimal(18, 2)")
                .HasColumnName("PHASE_PLAN_COST");
            entity.Property(e => e.PhasePlanPrice)
                .HasColumnType("decimal(18, 2)")
                .HasColumnName("PHASE_PLAN_PRICE");
            entity.Property(e => e.PlanTypeId).HasColumnName("PLAN_TYPE_ID");
            entity.Property(e => e.RecordTimeStampe)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SqFt).HasColumnName("SQ_FT");
            entity.Property(e => e.SubdivisionId).HasColumnName("SUBDIVISION_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
            entity.Property(e => e.WarningCount).HasColumnName("WARNING_COUNT");

            entity.HasOne(d => d.MasterPlan).WithMany(p => p.PhasePlans)
                .HasForeignKey(d => d.MasterPlanId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PhasePlan_MasterPlan");

            entity.HasOne(d => d.Subdivision).WithMany(p => p.PhasePlans)
                .HasForeignKey(d => d.SubdivisionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PhasePlan_Subdivision");
        });

        modelBuilder.Entity<PlanOptionAttributeItem>(entity =>
        {
            entity.HasKey(e => e.PlanOptAttrItemId).HasName("PK_PlanOptionAttributeItem");

            entity.ToTable("PLAN_OPTION_ATTRIBUTE_ITEM");

            entity.HasIndex(e => e.AttrGroupItemId, "idxAttrGroupItemID");

            entity.HasIndex(e => new { e.PlanOptionId, e.AttrGroupItemId }, "uniquePlanAttributes").IsUnique();

            entity.Property(e => e.PlanOptAttrItemId).HasColumnName("PLAN_OPT_ATTR_ITEM_ID");
            entity.Property(e => e.AttrGroupItemId).HasColumnName("ATTR_GROUP_ITEM_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.PlanOptionId).HasColumnName("PLAN_OPTION_ID");
            entity.Property(e => e.Price)
                .HasColumnType("money")
                .HasColumnName("PRICE");
            entity.Property(e => e.PriceApprovedBy)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("PRICE_APPROVED_BY");
            entity.Property(e => e.PriceApprovedDate)
                .HasColumnType("date")
                .HasColumnName("PRICE_APPROVED_DATE");
            entity.Property(e => e.RecordTimeStampe)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
        });

        modelBuilder.Entity<PlanType>(entity =>
        {
            entity.ToTable("PLAN_TYPES");

            entity.Property(e => e.PlanTypeId).HasColumnName("PLAN_TYPE_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Description)
                .HasMaxLength(254)
                .IsUnicode(false)
                .HasColumnName("DESCRIPTION");
            entity.Property(e => e.DivId).HasColumnName("DIV_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.PlanTypeCode)
                .HasMaxLength(25)
                .IsUnicode(false)
                .HasColumnName("PLAN_TYPE_CODE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<Poapproval>(entity =>
        {
            entity.ToTable("POAPPROVAL");

            entity.Property(e => e.PoapprovalId).HasColumnName("POAPPROVAL_ID");
            entity.Property(e => e.Approvalseq).HasColumnName("APPROVALSEQ");
            entity.Property(e => e.Approvedby)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("APPROVEDBY");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Invdate)
                .HasColumnType("smalldatetime")
                .HasColumnName("INVDATE");
            entity.Property(e => e.Invdescription)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("INVDESCRIPTION");
            entity.Property(e => e.Invnetamount).HasColumnName("INVNETAMOUNT");
            entity.Property(e => e.Invnumber)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("INVNUMBER");
            entity.Property(e => e.Invretention).HasColumnName("INVRETENTION");
            entity.Property(e => e.Invtaxamount).HasColumnName("INVTAXAMOUNT");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.PoheaderId).HasColumnName("POHEADER_ID");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Poheader).WithMany(p => p.Poapprovals)
                .HasForeignKey(d => d.PoheaderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_POAPPROVAL_POHEADER");
        });

        modelBuilder.Entity<Podetail>(entity =>
        {
            entity.ToTable("PODETAIL");

            entity.Property(e => e.PodetailId).HasColumnName("PODETAIL_ID");
            entity.Property(e => e.AuthToDateAmt).HasColumnName("AUTH_TO_DATE_AMT");
            entity.Property(e => e.AuthToDatePc).HasColumnName("AUTH_TO_DATE_PC");
            entity.Property(e => e.ChangeToPodetailId).HasColumnName("CHANGE_TO_PODETAIL_ID");
            entity.Property(e => e.CostsId).HasColumnName("COSTS_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.ItemNumber)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("ITEM_NUMBER");
            entity.Property(e => e.LastAuthAmount).HasColumnName("LAST_AUTH_AMOUNT");
            entity.Property(e => e.LastAuthDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("LAST_AUTH_DATE");
            entity.Property(e => e.LastAuthDisc).HasColumnName("LAST_AUTH_DISC");
            entity.Property(e => e.LastAuthDraw)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("LAST_AUTH_DRAW");
            entity.Property(e => e.LastAuthTax).HasColumnName("LAST_AUTH_TAX");
            entity.Property(e => e.MasterItemsId).HasColumnName("MASTER_ITEMS_ID");
            entity.Property(e => e.OptionDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("OPTION_DESC");
            entity.Property(e => e.OptionNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("OPTION_NUMBER");
            entity.Property(e => e.Optionselections)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("OPTIONSELECTIONS");
            entity.Property(e => e.Pactivity)
                .HasMaxLength(36)
                .IsUnicode(false)
                .HasColumnName("PACTIVITY");
            entity.Property(e => e.PendAuthAmount).HasColumnName("PEND_AUTH_AMOUNT");
            entity.Property(e => e.PendAuthDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PEND_AUTH_DATE");
            entity.Property(e => e.PendAuthDisc).HasColumnName("PEND_AUTH_DISC");
            entity.Property(e => e.PendAuthDraw)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("PEND_AUTH_DRAW");
            entity.Property(e => e.PendAuthPc).HasColumnName("PEND_AUTH_PC");
            entity.Property(e => e.PendAuthTax).HasColumnName("PEND_AUTH_TAX");
            entity.Property(e => e.PhaseCode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("PHASE_CODE");
            entity.Property(e => e.Poamount).HasColumnName("POAMOUNT");
            entity.Property(e => e.Poapnnumber)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("POAPNNUMBER");
            entity.Property(e => e.Pocategory)
                .HasMaxLength(3)
                .IsUnicode(false)
                .HasColumnName("POCATEGORY");
            entity.Property(e => e.PochangeorderId).HasColumnName("POCHANGEORDER_ID");
            entity.Property(e => e.Pocostcode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("POCOSTCODE");
            entity.Property(e => e.Pocutlengths).HasColumnName("POCUTLENGTHS");
            entity.Property(e => e.PodetailoptionsId).HasColumnName("PODETAILOPTIONS_ID");
            entity.Property(e => e.PoheaderId).HasColumnName("POHEADER_ID");
            entity.Property(e => e.Poitemdesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("POITEMDESC");
            entity.Property(e => e.Poitemno).HasColumnName("POITEMNO");
            entity.Property(e => e.Poitemnotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("POITEMNOTES");
            entity.Property(e => e.PojccdetailId).HasColumnName("POJCCDETAIL_ID");
            entity.Property(e => e.Poqtyoflengths).HasColumnName("POQTYOFLENGTHS");
            entity.Property(e => e.Pounit)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("POUNIT");
            entity.Property(e => e.Pounitcost).HasColumnName("POUNITCOST");
            entity.Property(e => e.Pounitqty).HasColumnName("POUNITQTY");
            entity.Property(e => e.PurchasingContDetailId).HasColumnName("PURCHASING_CONT_DETAIL_ID");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SortKey1)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SORT_KEY_1");
            entity.Property(e => e.SortKey2)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SORT_KEY_2");
            entity.Property(e => e.SupplierCostreservationId).HasColumnName("SUPPLIER_COSTRESERVATION_ID");
            entity.Property(e => e.Taxamount).HasColumnName("TAXAMOUNT");
            entity.Property(e => e.Taxgroup)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("TAXGROUP");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.VarianceAmount).HasColumnName("VARIANCE_AMOUNT");
            entity.Property(e => e.VarianceAuthToDateAmt).HasColumnName("VARIANCE_AUTH_TO_DATE_AMT");
            entity.Property(e => e.VarianceAuthToDateTax).HasColumnName("VARIANCE_AUTH_TO_DATE_TAX");
            entity.Property(e => e.VarianceJcCategory)
                .HasMaxLength(3)
                .IsUnicode(false)
                .HasColumnName("VARIANCE_JC_CATEGORY");
            entity.Property(e => e.VarianceLastAuthAmount).HasColumnName("VARIANCE_LAST_AUTH_AMOUNT");
            entity.Property(e => e.VarianceLastAuthDisc).HasColumnName("VARIANCE_LAST_AUTH_DISC");
            entity.Property(e => e.VarianceLastAuthTax).HasColumnName("VARIANCE_LAST_AUTH_TAX");
            entity.Property(e => e.VariancePendAuthAmount).HasColumnName("VARIANCE_PEND_AUTH_AMOUNT");
            entity.Property(e => e.VariancePendAuthDisc).HasColumnName("VARIANCE_PEND_AUTH_DISC");
            entity.Property(e => e.VariancePendAuthTax).HasColumnName("VARIANCE_PEND_AUTH_TAX");
            entity.Property(e => e.VariancePojccdetailId).HasColumnName("VARIANCE_POJCCDETAIL_ID");
            entity.Property(e => e.VarianceTaxAmount).HasColumnName("VARIANCE_TAX_AMOUNT");
            entity.Property(e => e.Warrantydays).HasColumnName("WARRANTYDAYS");
            entity.Property(e => e.Warrantyitem)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("WARRANTYITEM");
            entity.Property(e => e.Warrantytype).HasColumnName("WARRANTYTYPE");

            entity.HasOne(d => d.Costs).WithMany(p => p.Podetails)
                .HasForeignKey(d => d.CostsId)
                .HasConstraintName("FK_PODETAIL_COSTS");

            entity.HasOne(d => d.Podetailoptions).WithMany(p => p.Podetails)
                .HasForeignKey(d => d.PodetailoptionsId)
                .HasConstraintName("FK_PODETAIL_PODETAILOPTIONS");

            entity.HasOne(d => d.Poheader).WithMany(p => p.Podetails)
                .HasForeignKey(d => d.PoheaderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PODETAIL_POHEADER");

            entity.HasOne(d => d.Pojccdetail).WithMany(p => p.Podetails)
                .HasForeignKey(d => d.PojccdetailId)
                .HasConstraintName("FK_PODETAIL_POJCCDETAIL");
        });

        modelBuilder.Entity<Podetailoption>(entity =>
        {
            entity.HasKey(e => e.PodetailoptionsId);

            entity.ToTable("PODETAILOPTIONS");

            entity.Property(e => e.PodetailoptionsId).HasColumnName("PODETAILOPTIONS_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.OptionDesc)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("OPTION_DESC");
            entity.Property(e => e.OptionExtendedDesc)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("OPTION_EXTENDED_DESC");
            entity.Property(e => e.OptionLongDesc)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("OPTION_LONG_DESC");
            entity.Property(e => e.OptionNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("OPTION_NOTES");
            entity.Property(e => e.OptionNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("OPTION_NUMBER");
            entity.Property(e => e.OptionSelections)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("OPTION_SELECTIONS");
            entity.Property(e => e.PoheaderId).HasColumnName("POHEADER_ID");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Poheader).WithMany(p => p.Podetailoptions)
                .HasForeignKey(d => d.PoheaderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PODETAILOPTIONS_POHEADER");
        });

        modelBuilder.Entity<Poheader>(entity =>
        {
            entity.HasKey(e => e.PoheaderId).HasName("PK__POHEADER__3F3AD019F93DCA93");

            entity.ToTable("POHEADER");

            entity.HasIndex(e => new { e.Pojobnumber, e.Ponumber }, "nci_wi_POHEADER_88165FDA5BC460B3917E7ECF63F09261");

            entity.Property(e => e.PoheaderId).HasColumnName("POHEADER_ID");
            entity.Property(e => e.ApprovalHold)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("APPROVAL_HOLD");
            entity.Property(e => e.ApprovalHoldReason)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("APPROVAL_HOLD_REASON");
            entity.Property(e => e.Approvedby)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("APPROVEDBY");
            entity.Property(e => e.Cancelledby)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("CANCELLEDBY");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DateModified)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_MODIFIED");
            entity.Property(e => e.DocId)
                .HasMaxLength(36)
                .IsUnicode(false)
                .HasColumnName("DOC_ID");
            entity.Property(e => e.IntegrationPo).HasColumnName("INTEGRATION_PO");
            entity.Property(e => e.IntegrationPonumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("INTEGRATION_PONUMBER");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.Issuedby)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("ISSUEDBY");
            entity.Property(e => e.JccOnHold)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("JCC_ON_HOLD");
            entity.Property(e => e.JobClosedHold)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("JOB_CLOSED_HOLD");
            entity.Property(e => e.LastAcceptInvAmt).HasColumnName("LAST_ACCEPT_INV_AMT");
            entity.Property(e => e.LastAcceptInvDte)
                .HasColumnType("smalldatetime")
                .HasColumnName("LAST_ACCEPT_INV_DTE");
            entity.Property(e => e.LastAcceptInvNo)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("LAST_ACCEPT_INV_NO");
            entity.Property(e => e.LastAcceptRetent).HasColumnName("LAST_ACCEPT_RETENT");
            entity.Property(e => e.LienWaiverHold)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("LIEN_WAIVER_HOLD");
            entity.Property(e => e.LienWaiverId).HasColumnName("LIEN_WAIVER_ID");
            entity.Property(e => e.MasterPo).HasColumnName("MASTER_PO");
            entity.Property(e => e.MaximumRetention).HasColumnName("MAXIMUM_RETENTION");
            entity.Property(e => e.PendClose)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PEND_CLOSE");
            entity.Property(e => e.PendInvAcntDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PEND_INV_ACNT_DATE");
            entity.Property(e => e.PendInvAmount).HasColumnName("PEND_INV_AMOUNT");
            entity.Property(e => e.PendInvDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PEND_INV_DATE");
            entity.Property(e => e.PendInvDescrip)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("PEND_INV_DESCRIP");
            entity.Property(e => e.PendInvDisc).HasColumnName("PEND_INV_DISC");
            entity.Property(e => e.PendInvDiscDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PEND_INV_DISC_DATE");
            entity.Property(e => e.PendInvNotes)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("PEND_INV_NOTES");
            entity.Property(e => e.PendInvNumber)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("PEND_INV_NUMBER");
            entity.Property(e => e.PendInvPayDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PEND_INV_PAY_DATE");
            entity.Property(e => e.PendInvRetention).HasColumnName("PEND_INV_RETENTION");
            entity.Property(e => e.PendInvTax).HasColumnName("PEND_INV_TAX");
            entity.Property(e => e.PoSelections)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("PO_SELECTIONS");
            entity.Property(e => e.Podateapproved)
                .HasColumnType("smalldatetime")
                .HasColumnName("PODATEAPPROVED");
            entity.Property(e => e.Podatecancelled)
                .HasColumnType("smalldatetime")
                .HasColumnName("PODATECANCELLED");
            entity.Property(e => e.Podateissued)
                .HasColumnType("smalldatetime")
                .HasColumnName("PODATEISSUED");
            entity.Property(e => e.Podateprinted)
                .HasColumnType("smalldatetime")
                .HasColumnName("PODATEPRINTED");
            entity.Property(e => e.Podescription)
                .HasMaxLength(35)
                .IsUnicode(false)
                .HasColumnName("PODESCRIPTION");
            entity.Property(e => e.Pojobnumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("POJOBNUMBER");
            entity.Property(e => e.Ponotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("PONOTES");
            entity.Property(e => e.Ponumber)
                .HasMaxLength(12)
                .IsUnicode(false)
                .HasColumnName("PONUMBER");
            entity.Property(e => e.PosentTimestamp)
                .HasColumnType("smalldatetime")
                .HasColumnName("POSENT_TIMESTAMP");
            entity.Property(e => e.Postatus).HasColumnName("POSTATUS");
            entity.Property(e => e.Pototal).HasColumnName("POTOTAL");
            entity.Property(e => e.Potype).HasColumnName("POTYPE");
            entity.Property(e => e.Povendor)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("POVENDOR");
            entity.Property(e => e.Printable)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PRINTABLE");
            entity.Property(e => e.PurchasingContCoId).HasColumnName("PURCHASING_CONT_CO_ID");
            entity.Property(e => e.PurchasingContractsId).HasColumnName("PURCHASING_CONTRACTS_ID");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Releasecode)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("RELEASECODE");
            entity.Property(e => e.Retention).HasColumnName("RETENTION");
            entity.Property(e => e.RetentionType)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("RETENTION_TYPE");
            entity.Property(e => e.SactivityId).HasColumnName("SACTIVITY_ID");
            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.TaskCompleteBy)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("TASK_COMPLETE_BY");
            entity.Property(e => e.TaskCompleteDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("TASK_COMPLETE_DATE");
            entity.Property(e => e.Taxamount).HasColumnName("TAXAMOUNT");
            entity.Property(e => e.TbdChange)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("TBD_CHANGE");
            entity.Property(e => e.TradeAcceptedHold)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("TRADE_ACCEPTED_HOLD");
            entity.Property(e => e.TradeAcceptedOverrideuser)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("TRADE_ACCEPTED_OVERRIDEUSER");
            entity.Property(e => e.TradeArchived)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("TRADE_ARCHIVED");
            entity.Property(e => e.TradeDateAccepted)
                .HasColumnType("smalldatetime")
                .HasColumnName("TRADE_DATE_ACCEPTED");
            entity.Property(e => e.TradeDateArchived)
                .HasColumnType("smalldatetime")
                .HasColumnName("TRADE_DATE_ARCHIVED");
            entity.Property(e => e.TradeDateViewed)
                .HasColumnType("smalldatetime")
                .HasColumnName("TRADE_DATE_VIEWED");
            entity.Property(e => e.TradeId).HasColumnName("TRADE_ID");
            entity.Property(e => e.TradeUserAccepted).HasColumnName("TRADE_USER_ACCEPTED");
            entity.Property(e => e.TradeUserArchived)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("TRADE_USER_ARCHIVED");
            entity.Property(e => e.TradeUserViewed)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("TRADE_USER_VIEWED");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UserModified)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_MODIFIED");
            entity.Property(e => e.VpoNumber).HasColumnName("VPO_NUMBER");
            entity.Property(e => e.VpoVendorinvoice)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("VPO_VENDORINVOICE");
            entity.Property(e => e.WcompOnHold)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("WCOMP_ON_HOLD");

            entity.HasOne(d => d.PojobnumberNavigation).WithMany(p => p.Poheaders)
                .HasForeignKey(d => d.Pojobnumber)
                .HasConstraintName("fk_JobPoheader");

            entity.HasOne(d => d.PostatusNavigation).WithMany(p => p.Poheaders)
                .HasForeignKey(d => d.Postatus)
                .HasConstraintName("FK_POHEADER_POSTATUS");

            entity.HasOne(d => d.Sactivity).WithMany(p => p.Poheaders)
                .HasForeignKey(d => d.SactivityId)
                .HasConstraintName("fk_SACTIVITYIDPoheader");

            entity.HasOne(d => d.SubNumberNavigation).WithMany(p => p.Poheaders)
                .HasForeignKey(d => d.SubNumber)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_SUBNUMBERPoheader");

            entity.HasOne(d => d.Trade).WithMany(p => p.Poheaders)
                .HasForeignKey(d => d.TradeId)
                .HasConstraintName("fk_TRADEIDPoheader");
        });

        modelBuilder.Entity<Pojccdetail>(entity =>
        {
            entity.ToTable("POJCCDETAIL");

            entity.HasIndex(e => new { e.InvPendingSend, e.IsActive }, "nci_wi_POJCCDETAIL_DD3C3D277586E853E880C636AA326901");

            entity.Property(e => e.PojccdetailId).HasColumnName("POJCCDETAIL_ID");
            entity.Property(e => e.AtdAmountIncTax).HasColumnName("ATD_AMOUNT_INC_TAX");
            entity.Property(e => e.AtdTaxAmount).HasColumnName("ATD_TAX_AMOUNT");
            entity.Property(e => e.AtdUnits).HasColumnName("ATD_UNITS");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.InvPendingSend)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("INV_PENDING_SEND");
            entity.Property(e => e.InvPendingSendStatus)
                .HasMaxLength(2)
                .IsUnicode(false)
                .HasColumnName("INV_PENDING_SEND_STATUS");
            entity.Property(e => e.InvSessionId).HasColumnName("INV_SESSION_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JccAmountIncTax).HasColumnName("JCC_AMOUNT_INC_TAX");
            entity.Property(e => e.JccCostedTaxAmount).HasColumnName("JCC_COSTED_TAX_AMOUNT");
            entity.Property(e => e.JccItemClosed)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("JCC_ITEM_CLOSED");
            entity.Property(e => e.JccItemDatetimeClosed)
                .HasColumnType("smalldatetime")
                .HasColumnName("JCC_ITEM_DATETIME_CLOSED");
            entity.Property(e => e.JccItemDatetimeExported)
                .HasColumnType("smalldatetime")
                .HasColumnName("JCC_ITEM_DATETIME_EXPORTED");
            entity.Property(e => e.JccItemDesc)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("JCC_ITEM_DESC");
            entity.Property(e => e.JccItemExported)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("JCC_ITEM_EXPORTED");
            entity.Property(e => e.JccItemUserClosed)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("JCC_ITEM_USER_CLOSED");
            entity.Property(e => e.JccItemUserExported)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("JCC_ITEM_USER_EXPORTED");
            entity.Property(e => e.JccItemnumber).HasColumnName("JCC_ITEMNUMBER");
            entity.Property(e => e.JccNoncostedTaxAmount).HasColumnName("JCC_NONCOSTED_TAX_AMOUNT");
            entity.Property(e => e.JccTaxAmount).HasColumnName("JCC_TAX_AMOUNT");
            entity.Property(e => e.JccTaxGroup)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("JCC_TAX_GROUP");
            entity.Property(e => e.JccUnitCost).HasColumnName("JCC_UNIT_COST");
            entity.Property(e => e.JccUnitDesc)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("JCC_UNIT_DESC");
            entity.Property(e => e.JccUnits).HasColumnName("JCC_UNITS");
            entity.Property(e => e.LastDraw)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("LAST_DRAW");
            entity.Property(e => e.PendAmountIncTax).HasColumnName("PEND_AMOUNT_INC_TAX");
            entity.Property(e => e.PendDiscount).HasColumnName("PEND_DISCOUNT");
            entity.Property(e => e.PendDraw)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("PEND_DRAW");
            entity.Property(e => e.PendTaxAmount).HasColumnName("PEND_TAX_AMOUNT");
            entity.Property(e => e.PendUnits).HasColumnName("PEND_UNITS");
            entity.Property(e => e.PoPendingSend)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PO_PENDING_SEND");
            entity.Property(e => e.PoPendingSendStatus)
                .HasMaxLength(2)
                .IsUnicode(false)
                .HasColumnName("PO_PENDING_SEND_STATUS");
            entity.Property(e => e.Pocategory)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("POCATEGORY");
            entity.Property(e => e.Pocostcode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("POCOSTCODE");
            entity.Property(e => e.Poextra)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("POEXTRA");
            entity.Property(e => e.PoheaderId).HasColumnName("POHEADER_ID");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SessionId).HasColumnName("SESSION_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UserStamp)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_STAMP");

            entity.HasOne(d => d.Poheader).WithMany(p => p.Pojccdetails)
                .HasForeignKey(d => d.PoheaderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_POJCCDETAIL_POHEADER");
        });

        modelBuilder.Entity<Postatus>(entity =>
        {
            entity.ToTable("POSTATUS");

            entity.Property(e => e.PostatusId).HasColumnName("POSTATUS_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.Postatus1)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("POSTATUS");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<ReferenceType>(entity =>
        {
            entity.ToTable("REFERENCE_TYPE");

            entity.Property(e => e.ReferenceTypeId).HasColumnName("REFERENCE_TYPE_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.ReferenceType1)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("REFERENCE_TYPE");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<Sactivity>(entity =>
        {
            entity.ToTable("SACTIVITY");

            entity.Property(e => e.SactivityId).HasColumnName("SACTIVITY_ID");
            entity.Property(e => e.ActivityName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("ACTIVITY_NAME");
            entity.Property(e => e.ChecklistId).HasColumnName("CHECKLIST_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DefaultDuration).HasColumnName("DEFAULT_DURATION");
            entity.Property(e => e.DefaultLagtime).HasColumnName("DEFAULT_LAGTIME");
            entity.Property(e => e.DefaultNote)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("DEFAULT_NOTE");
            entity.Property(e => e.DivId).HasColumnName("DIV_ID");
            entity.Property(e => e.DownloadToPalm)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("DOWNLOAD_TO_PALM");
            entity.Property(e => e.GenPitBudget)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("GEN_PIT_BUDGET");
            entity.Property(e => e.GrossLag)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("GROSS_LAG");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.ReportingId).HasColumnName("REPORTING_ID");
            entity.Property(e => e.Seq).HasColumnName("SEQ");
            entity.Property(e => e.TradeId).HasColumnName("TRADE_ID");
            entity.Property(e => e.UpdateSchedules)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("UPDATE_SCHEDULES");
            entity.Property(e => e.UpdateTemplates)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("UPDATE_TEMPLATES");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.Workdays)
                .HasMaxLength(14)
                .IsUnicode(false)
                .HasColumnName("WORKDAYS");

            entity.HasOne(d => d.Trade).WithMany(p => p.Sactivities)
                .HasForeignKey(d => d.TradeId)
                .HasConstraintName("FK_SACTIVITY_TRADE");
        });

        modelBuilder.Entity<Salesconfig>(entity =>
        {
            entity.ToTable("SALESCONFIG");

            entity.Property(e => e.SalesconfigId).HasColumnName("SALESCONFIG_ID");
            entity.Property(e => e.Baseprice).HasColumnName("BASEPRICE");
            entity.Property(e => e.Canceldate)
                .HasColumnType("smalldatetime")
                .HasColumnName("CANCELDATE");
            entity.Property(e => e.Closingdate)
                .HasColumnType("smalldatetime")
                .HasColumnName("CLOSINGDATE");
            entity.Property(e => e.ConfigSource)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("CONFIG_SOURCE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Estimatedsettlementdate)
                .HasColumnType("smalldatetime")
                .HasColumnName("ESTIMATEDSETTLEMENTDATE");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.IsApproved)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_APPROVED");
            entity.Property(e => e.IsDeleted)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_DELETED");
            entity.Property(e => e.JobNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JOB_NUMBER");
            entity.Property(e => e.LotPremium).HasColumnName("LOT_PREMIUM");
            entity.Property(e => e.LotPremiumIncentive).HasColumnName("LOT_PREMIUM_INCENTIVE");
            entity.Property(e => e.LotSwing)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("LOT_SWING");
            entity.Property(e => e.OptionIncentive).HasColumnName("OPTION_INCENTIVE");
            entity.Property(e => e.OwnerFirstName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("OWNER_FIRST_NAME");
            entity.Property(e => e.OwnerLastName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("OWNER_LAST_NAME");
            entity.Property(e => e.OwnerMiddleName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("OWNER_MIDDLE_NAME");
            entity.Property(e => e.OwnerNamePrefix)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("OWNER_NAME_PREFIX");
            entity.Property(e => e.OwnerNameSuffix)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("OWNER_NAME_SUFFIX");
            entity.Property(e => e.Owneraddress1)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("OWNERADDRESS1");
            entity.Property(e => e.Owneraddress2)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("OWNERADDRESS2");
            entity.Property(e => e.Owneremail)
                .HasMaxLength(254)
                .IsUnicode(false)
                .HasColumnName("OWNEREMAIL");
            entity.Property(e => e.Ownerfax)
                .HasMaxLength(13)
                .IsUnicode(false)
                .HasColumnName("OWNERFAX");
            entity.Property(e => e.Ownermobile)
                .HasMaxLength(13)
                .IsUnicode(false)
                .HasColumnName("OWNERMOBILE");
            entity.Property(e => e.Ownername)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("OWNERNAME");
            entity.Property(e => e.Ownerphone1)
                .HasMaxLength(13)
                .IsUnicode(false)
                .HasColumnName("OWNERPHONE1");
            entity.Property(e => e.Ownerphone2)
                .HasMaxLength(13)
                .IsUnicode(false)
                .HasColumnName("OWNERPHONE2");
            entity.Property(e => e.Ownerpostcode)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("OWNERPOSTCODE");
            entity.Property(e => e.Ownerstate)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("OWNERSTATE");
            entity.Property(e => e.Ownersuburb)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("OWNERSUBURB");
            entity.Property(e => e.PeeHeaderId).HasColumnName("PEE_HEADER_ID");
            entity.Property(e => e.PhasePlanId).HasColumnName("PHASE_PLAN_ID");
            entity.Property(e => e.Prevstatus)
                .HasMaxLength(18)
                .IsUnicode(false)
                .HasColumnName("PREVSTATUS");
            entity.Property(e => e.PriceIncentive).HasColumnName("PRICE_INCENTIVE");
            entity.Property(e => e.Ratificationdate)
                .HasColumnType("smalldatetime")
                .HasColumnName("RATIFICATIONDATE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Saledate)
                .HasColumnType("smalldatetime")
                .HasColumnName("SALEDATE");
            entity.Property(e => e.SalesContact)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("SALES_CONTACT");
            entity.Property(e => e.SalesIncentives).HasColumnName("SALES_INCENTIVES");
            entity.Property(e => e.SalesagentEmail)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("SALESAGENT_EMAIL");
            entity.Property(e => e.SalesagentName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("SALESAGENT_NAME");
            entity.Property(e => e.SalesagentPhone)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SALESAGENT_PHONE");
            entity.Property(e => e.SentToPurchasing)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SENT_TO_PURCHASING");
            entity.Property(e => e.SsAction)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SS_ACTION");
            entity.Property(e => e.SsClientId).HasColumnName("SS_CLIENT_ID");
            entity.Property(e => e.SsConfigurationid).HasColumnName("SS_CONFIGURATIONID");
            entity.Property(e => e.SsLotid).HasColumnName("SS_LOTID");
            entity.Property(e => e.SsSalesagentId).HasColumnName("SS_SALESAGENT_ID");
            entity.Property(e => e.Status)
                .HasMaxLength(18)
                .IsUnicode(false)
                .HasColumnName("STATUS");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTim).HasColumnType("smalldatetime");
            entity.Property(e => e.UpfrontIncentive).HasColumnName("UPFRONT_INCENTIVE");
            entity.Property(e => e.UserContact1)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT1");
            entity.Property(e => e.UserContact1FirstName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT1_FIRST_NAME");
            entity.Property(e => e.UserContact1LastName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT1_LAST_NAME");
            entity.Property(e => e.UserContact1MiddleName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT1_MIDDLE_NAME");
            entity.Property(e => e.UserContact1NamePrefix)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT1_NAME_PREFIX");
            entity.Property(e => e.UserContact1NameSuffix)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT1_NAME_SUFFIX");
            entity.Property(e => e.UserContact2)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT2");
            entity.Property(e => e.UserContact2FirstName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT2_FIRST_NAME");
            entity.Property(e => e.UserContact2LastName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT2_LAST_NAME");
            entity.Property(e => e.UserContact2MiddleName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT2_MIDDLE_NAME");
            entity.Property(e => e.UserContact2NamePrefix)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT2_NAME_PREFIX");
            entity.Property(e => e.UserContact2NameSuffix)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("USER_CONTACT2_NAME_SUFFIX");

            entity.HasOne(d => d.JobNumberNavigation).WithMany(p => p.Salesconfigs)
                .HasForeignKey(d => d.JobNumber)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SALESCONFIG_JOB");

            entity.HasOne(d => d.PhasePlan).WithMany(p => p.Salesconfigs)
                .HasForeignKey(d => d.PhasePlanId)
                .HasConstraintName("FK_SALESCONFIG_PHASE_PLAN");
        });

        modelBuilder.Entity<Salesconfigco>(entity =>
        {
            entity.ToTable("SALESCONFIGCO");

            entity.Property(e => e.SalesconfigcoId).HasColumnName("SALESCONFIGCO_ID");
            entity.Property(e => e.CoNumber).HasColumnName("CO_NUMBER");
            entity.Property(e => e.CoStatus)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("CO_STATUS");
            entity.Property(e => e.CoStatusdate)
                .HasColumnType("smalldatetime")
                .HasColumnName("CO_STATUSDATE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.IsApproved)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_APPROVED");
            entity.Property(e => e.LotPremium).HasColumnName("LOT_PREMIUM");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SalesconfigId).HasColumnName("SALESCONFIG_ID");
            entity.Property(e => e.SentToPurchasing)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SENT_TO_PURCHASING");
            entity.Property(e => e.SsChangeorderid).HasColumnName("SS_CHANGEORDERID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTim).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Salesconfig).WithMany(p => p.Salesconfigcos)
                .HasForeignKey(d => d.SalesconfigId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SALESCONFIGCO_SALESCONFIG");
        });

        modelBuilder.Entity<Salesconfigcooption>(entity =>
        {
            entity.HasKey(e => e.SalesconfigcooptionsId);

            entity.ToTable("SALESCONFIGCOOPTIONS");

            entity.HasIndex(e => new { e.IsActive, e.SalesconfigcoId }, "nci_wi_SALESCONFIGCOOPTIONS_A78EA6253A9C374697BD7F9CB39AD8EA");

            entity.Property(e => e.SalesconfigcooptionsId).HasColumnName("SALESCONFIGCOOPTIONS_ID");
            entity.Property(e => e.AssociatedEstimate)
                .HasMaxLength(254)
                .IsUnicode(false)
                .HasColumnName("ASSOCIATED_ESTIMATE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.EstheaderId).HasColumnName("ESTHEADER_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JcCategory)
                .HasMaxLength(3)
                .IsUnicode(false)
                .HasColumnName("JC_CATEGORY");
            entity.Property(e => e.PeeHeaderId).HasColumnName("PEE_HEADER_ID");
            entity.Property(e => e.PlanOptionId).HasColumnName("PLAN_OPTION_ID");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SalesconfigcoAction)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SALESCONFIGCO_ACTION");
            entity.Property(e => e.SalesconfigcoId).HasColumnName("SALESCONFIGCO_ID");
            entity.Property(e => e.SalesconfigcoNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("SALESCONFIGCO_NOTES");
            entity.Property(e => e.SalesconfigcoPrice).HasColumnName("SALESCONFIGCO_PRICE");
            entity.Property(e => e.SalesconfigcoQuantityChange).HasColumnName("SALESCONFIGCO_QUANTITY_CHANGE");
            entity.Property(e => e.SalesconfigcoSelections)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("SALESCONFIGCO_SELECTIONS");
            entity.Property(e => e.ScDescription)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("SC_DESCRIPTION");
            entity.Property(e => e.SsOptioncode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SS_OPTIONCODE");
            entity.Property(e => e.UpdatedB)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.PlanOption).WithMany(p => p.Salesconfigcooptions)
                .HasForeignKey(d => d.PlanOptionId)
                .HasConstraintName("FK_SALESCONFIGCOOPTIONS_AVAILABLE_PLAN_OPTION");

            entity.HasOne(d => d.Salesconfigco).WithMany(p => p.Salesconfigcooptions)
                .HasForeignKey(d => d.SalesconfigcoId)
                .HasConstraintName("FK_SALESCONFIGCOOPTIONS_SALESCONFIGCO");
        });

        modelBuilder.Entity<Salesconfigoption>(entity =>
        {
            entity.HasKey(e => e.SalesconfigoptionsId);

            entity.ToTable("SALESCONFIGOPTIONS");

            entity.Property(e => e.SalesconfigoptionsId).HasColumnName("SALESCONFIGOPTIONS_ID");
            entity.Property(e => e.AssociatedEstimate)
                .HasMaxLength(254)
                .IsUnicode(false)
                .HasColumnName("ASSOCIATED_ESTIMATE");
            entity.Property(e => e.ContractId).HasColumnName("ContractID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.EstheaderId).HasColumnName("ESTHEADER_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.IsDeletedOption)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_DELETED_OPTION");
            entity.Property(e => e.IsRollbackOption)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_ROLLBACK_OPTION");
            entity.Property(e => e.IsTransferredOption)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_TRANSFERRED_OPTION");
            entity.Property(e => e.MasterOptionId).HasColumnName("MASTER_OPTION_ID");
            entity.Property(e => e.Notes).IsUnicode(false);
            entity.Property(e => e.OptionNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("OPTION_NOTES");
            entity.Property(e => e.OptionPrice).HasColumnName("OPTION_PRICE");
            entity.Property(e => e.OptionQuantity).HasColumnName("OPTION_QUANTITY");
            entity.Property(e => e.OptionSelections)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("OPTION_SELECTIONS");
            entity.Property(e => e.PeeHeaderId).HasColumnName("PEE_HEADER_ID");
            entity.Property(e => e.PlanOptionId).HasColumnName("PLAN_OPTION_ID");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SalesconfigId).HasColumnName("SALESCONFIG_ID");
            entity.Property(e => e.ScDescription)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("SC_DESCRIPTION");
            entity.Property(e => e.SsOptioncode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SS_OPTIONCODE");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.PlanOption).WithMany(p => p.Salesconfigoptions)
                .HasForeignKey(d => d.PlanOptionId)
                .HasConstraintName("FK_SALESCONFIGOPTIONS_AVAILABLE_PLAN_OPTION");

            entity.HasOne(d => d.Salesconfig).WithMany(p => p.Salesconfigoptions)
                .HasForeignKey(d => d.SalesconfigId)
                .HasConstraintName("FK_SALESCONFIGOPTIONS_SALESCONFIG");
        });

        modelBuilder.Entity<Schedule>(entity =>
        {
            entity.ToTable("SCHEDULE");

            entity.Property(e => e.ScheduleId).HasColumnName("SCHEDULE_ID");
            entity.Property(e => e.ActualDuration).HasColumnName("ACTUAL_DURATION");
            entity.Property(e => e.ActualEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("ACTUAL_END_DATE");
            entity.Property(e => e.ActualStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("ACTUAL_START_DATE");
            entity.Property(e => e.BaseCalduration).HasColumnName("BASE_CALDURATION");
            entity.Property(e => e.BaseDuration).HasColumnName("BASE_DURATION");
            entity.Property(e => e.BaseEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("BASE_END_DATE");
            entity.Property(e => e.BaseStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("BASE_START_DATE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DateCreated)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_CREATED");
            entity.Property(e => e.DateModified)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_MODIFIED");
            entity.Property(e => e.DateToEnd)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_TO_END");
            entity.Property(e => e.DateToStart)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_TO_START");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JobNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JOB_NUMBER");
            entity.Property(e => e.LockBaseDates)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("LOCK_BASE_DATES");
            entity.Property(e => e.LockPreSch).HasColumnName("LOCK_PRE_SCH");
            entity.Property(e => e.PlusminusDays).HasColumnName("PLUSMINUS_DAYS");
            entity.Property(e => e.PreSchApproveDate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_APPROVE_DATE");
            entity.Property(e => e.PreSchApproved).HasColumnName("PRE_SCH_APPROVED");
            entity.Property(e => e.PreSchApprovedby)
                .HasMaxLength(40)
                .IsUnicode(false)
                .HasColumnName("PRE_SCH_APPROVEDBY");
            entity.Property(e => e.PreSchBaseEndDate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_BASE_END_DATE");
            entity.Property(e => e.PreSchBaseStartDate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_BASE_START_DATE");
            entity.Property(e => e.PreSchCreatedby)
                .HasMaxLength(40)
                .IsUnicode(false)
                .HasColumnName("PRE_SCH_CREATEDBY");
            entity.Property(e => e.PreSchCreateddate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_CREATEDDATE");
            entity.Property(e => e.PreSchDuration).HasColumnName("PRE_SCH_DURATION");
            entity.Property(e => e.PreSchEndDate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_END_DATE");
            entity.Property(e => e.PreSchLagTime).HasColumnName("PRE_SCH_LAG_TIME");
            entity.Property(e => e.PreSchModifiedby)
                .HasMaxLength(40)
                .IsUnicode(false)
                .HasColumnName("PRE_SCH_MODIFIEDBY");
            entity.Property(e => e.PreSchModifieddate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_MODIFIEDDATE");
            entity.Property(e => e.PreSchStartDate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_START_DATE");
            entity.Property(e => e.ProjCalduration).HasColumnName("PROJ_CALDURATION");
            entity.Property(e => e.ProjDuration).HasColumnName("PROJ_DURATION");
            entity.Property(e => e.ProjEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PROJ_END_DATE");
            entity.Property(e => e.ProjStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PROJ_START_DATE");
            entity.Property(e => e.Published)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PUBLISHED");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.StickBuildingNum)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("STICK_BUILDING_NUM");
            entity.Property(e => e.TemplateId).HasColumnName("TEMPLATE_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UserCreated)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_CREATED");
            entity.Property(e => e.UserModified)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_MODIFIED");

            entity.HasOne(d => d.JobNumberNavigation).WithMany(p => p.Schedules)
                .HasForeignKey(d => d.JobNumber)
                .HasConstraintName("FK_SCHEDULE_JOB");

            entity.HasOne(d => d.Template).WithMany(p => p.Schedules)
                .HasForeignKey(d => d.TemplateId)
                .HasConstraintName("FK_SCHEDULE_TEMPLATE");
        });

        modelBuilder.Entity<ScheduleArea>(entity =>
        {
            entity.ToTable("SCHEDULE_AREA");

            entity.Property(e => e.ScheduleAreaId)
                .ValueGeneratedNever()
                .HasColumnName("SCHEDULE_AREA_ID");
            entity.Property(e => e.ActualDuration).HasColumnName("ACTUAL_DURATION");
            entity.Property(e => e.ActualEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("ACTUAL_END_DATE");
            entity.Property(e => e.ActualStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("ACTUAL_START_DATE");
            entity.Property(e => e.BaseCalduration).HasColumnName("BASE_CALDURATION");
            entity.Property(e => e.BaseDuration).HasColumnName("BASE_DURATION");
            entity.Property(e => e.BaseEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("BASE_END_DATE");
            entity.Property(e => e.BaseStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("BASE_START_DATE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DateCreated)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_CREATED");
            entity.Property(e => e.DateModified)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_MODIFIED");
            entity.Property(e => e.DateToEnd)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_TO_END");
            entity.Property(e => e.DateToStart)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_TO_START");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.PlusminusDays).HasColumnName("PLUSMINUS_DAYS");
            entity.Property(e => e.ProjCalduration).HasColumnName("PROJ_CALDURATION");
            entity.Property(e => e.ProjDuration).HasColumnName("PROJ_DURATION");
            entity.Property(e => e.ProjEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PROJ_END_DATE");
            entity.Property(e => e.ProjStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PROJ_START_DATE");
            entity.Property(e => e.Published)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PUBLISHED");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.ScheduleAreaDesc)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("SCHEDULE_AREA_DESC");
            entity.Property(e => e.ScheduleAreaSeq).HasColumnName("SCHEDULE_AREA_SEQ");
            entity.Property(e => e.ScheduleMasterId).HasColumnName("SCHEDULE_MASTER_ID");
            entity.Property(e => e.SubdivisionId).HasColumnName("SUBDIVISION_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UserCreated)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_CREATED");
            entity.Property(e => e.UserModified)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_MODIFIED");

            entity.HasOne(d => d.ScheduleMaster).WithMany(p => p.ScheduleAreas)
                .HasForeignKey(d => d.ScheduleMasterId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SCHEDULE_AREA_SCHEDULE_MASTER");

            entity.HasOne(d => d.Subdivision).WithMany(p => p.ScheduleAreas)
                .HasForeignKey(d => d.SubdivisionId)
                .HasConstraintName("FK_SCHEDULE_AREA_SUBDIVISION");
        });

        modelBuilder.Entity<ScheduleChain>(entity =>
        {
            entity.ToTable("SCHEDULE_CHAIN");

            entity.Property(e => e.ScheduleChainId)
                .ValueGeneratedNever()
                .HasColumnName("SCHEDULE_CHAIN_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SactivityId).HasColumnName("SACTIVITY_ID");
            entity.Property(e => e.ScheduleMasterId).HasColumnName("SCHEDULE_MASTER_ID");
            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Sactivity).WithMany(p => p.ScheduleChains)
                .HasForeignKey(d => d.SactivityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SCHEDULE_CHAIN_SACTIVITY");

            entity.HasOne(d => d.ScheduleMaster).WithMany(p => p.ScheduleChains)
                .HasForeignKey(d => d.ScheduleMasterId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SCHEDULE_CHAIN_SCHEDULE_MASTER");
        });

        modelBuilder.Entity<ScheduleChainSchedAid>(entity =>
        {
            entity.ToTable("SCHEDULE_CHAIN_SCHED_AID");

            entity.Property(e => e.ScheduleChainSchedAidId)
                .ValueGeneratedNever()
                .HasColumnName("SCHEDULE_CHAIN_SCHED_AID_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.ScheduleAid).HasColumnName("SCHEDULE_AID");
            entity.Property(e => e.ScheduleChainId).HasColumnName("SCHEDULE_CHAIN_ID");
            entity.Property(e => e.ScheduleChainSeq).HasColumnName("SCHEDULE_CHAIN_SEQ");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.ScheduleChain).WithMany(p => p.ScheduleChainSchedAids)
                .HasForeignKey(d => d.ScheduleChainId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SCHEDULE_CHAIN_SCHED_AID_SCHEDULE_CHAIN");
        });

        modelBuilder.Entity<ScheduleMaster>(entity =>
        {
            entity.ToTable("SCHEDULE_MASTER");

            entity.Property(e => e.ScheduleMasterId)
                .ValueGeneratedNever()
                .HasColumnName("SCHEDULE_MASTER_ID");
            entity.Property(e => e.ActualDuration).HasColumnName("ACTUAL_DURATION");
            entity.Property(e => e.ActualEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("ACTUAL_END_DATE");
            entity.Property(e => e.ActualStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("ACTUAL_START_DATE");
            entity.Property(e => e.Autocreatephases)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("AUTOCREATEPHASES");
            entity.Property(e => e.Autocreateworkareas)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("AUTOCREATEWORKAREAS");
            entity.Property(e => e.BaseCalduration).HasColumnName("BASE_CALDURATION");
            entity.Property(e => e.BaseDuration).HasColumnName("BASE_DURATION");
            entity.Property(e => e.BaseEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("BASE_END_DATE");
            entity.Property(e => e.BaseStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("BASE_START_DATE");
            entity.Property(e => e.ComputingProjected).HasColumnName("COMPUTING_PROJECTED");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DateCreated)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_CREATED");
            entity.Property(e => e.DateModified)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_MODIFIED");
            entity.Property(e => e.DateToEnd)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_TO_END");
            entity.Property(e => e.DateToStart)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_TO_START");
            entity.Property(e => e.DivId).HasColumnName("DIV_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.PlusminusDays).HasColumnName("PLUSMINUS_DAYS");
            entity.Property(e => e.ProjCalduration).HasColumnName("PROJ_CALDURATION");
            entity.Property(e => e.ProjDuration).HasColumnName("PROJ_DURATION");
            entity.Property(e => e.ProjEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PROJ_END_DATE");
            entity.Property(e => e.ProjStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PROJ_START_DATE");
            entity.Property(e => e.Published)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PUBLISHED");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.ScheduleMasterDesc)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("SCHEDULE_MASTER_DESC");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UserCreated)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_CREATED");
            entity.Property(e => e.UserModified)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_MODIFIED");
        });

        modelBuilder.Entity<ScheduleMasterPred>(entity =>
        {
            entity.HasKey(e => new { e.ScheduleMasterId, e.ScheduleAid, e.PredScheduleAid });

            entity.ToTable("SCHEDULE_MASTER_PRED");

            entity.Property(e => e.ScheduleMasterId).HasColumnName("SCHEDULE_MASTER_ID");
            entity.Property(e => e.ScheduleAid).HasColumnName("SCHEDULE_AID");
            entity.Property(e => e.PredScheduleAid).HasColumnName("PRED_SCHEDULE_AID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.ScheduleMaster).WithMany(p => p.ScheduleMasterPreds)
                .HasForeignKey(d => d.ScheduleMasterId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SCHEDULE_MASTER_PRED_SCHEDULE_MASTER");
        });

        modelBuilder.Entity<ScheduleMasterSchedule>(entity =>
        {
            entity.ToTable("SCHEDULE_MASTER_SCHEDULE");

            entity.Property(e => e.ScheduleMasterScheduleId)
                .ValueGeneratedNever()
                .HasColumnName("SCHEDULE_MASTER_SCHEDULE_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.ScheduleId).HasColumnName("SCHEDULE_ID");
            entity.Property(e => e.ScheduleMasterScheduleSeq).HasColumnName("SCHEDULE_MASTER_SCHEDULE_SEQ");
            entity.Property(e => e.SchedulePhaseId).HasColumnName("SCHEDULE_PHASE_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Schedule).WithMany(p => p.ScheduleMasterSchedules)
                .HasForeignKey(d => d.ScheduleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SCHEDULE_MASTER_SCHEDULE_SCHEDULE");

            entity.HasOne(d => d.SchedulePhase).WithMany(p => p.ScheduleMasterSchedules)
                .HasForeignKey(d => d.SchedulePhaseId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SCHEDULE_MASTER_SCHEDULE_SCHEDULE_PHASE");
        });

        modelBuilder.Entity<ScheduleMilestone>(entity =>
        {
            entity.HasKey(e => e.ScheduleMid);

            entity.ToTable("SCHEDULE_MILESTONE");

            entity.Property(e => e.ScheduleMid).HasColumnName("SCHEDULE_MID");
            entity.Property(e => e.ActualDuration).HasColumnName("ACTUAL_DURATION");
            entity.Property(e => e.ActualEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("ACTUAL_END_DATE");
            entity.Property(e => e.ActualStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("ACTUAL_START_DATE");
            entity.Property(e => e.BaseEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("BASE_END_DATE");
            entity.Property(e => e.BaseStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("BASE_START_DATE");
            entity.Property(e => e.Calduration).HasColumnName("CALDURATION");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Duration).HasColumnName("DURATION");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.MilestoneId).HasColumnName("MILESTONE_ID");
            entity.Property(e => e.PlusminusDays).HasColumnName("PLUSMINUS_DAYS");
            entity.Property(e => e.PreSchBaseEndDate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_BASE_END_DATE");
            entity.Property(e => e.PreSchBaseStartDate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_BASE_START_DATE");
            entity.Property(e => e.PreSchDuration).HasColumnName("PRE_SCH_DURATION");
            entity.Property(e => e.PreSchEndDate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_END_DATE");
            entity.Property(e => e.PreSchStartDate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_START_DATE");
            entity.Property(e => e.ProjEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PROJ_END_DATE");
            entity.Property(e => e.ProjStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PROJ_START_DATE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SchEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("SCH_END_DATE");
            entity.Property(e => e.SchStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("SCH_START_DATE");
            entity.Property(e => e.ScheduleId).HasColumnName("SCHEDULE_ID");
            entity.Property(e => e.Seq).HasColumnName("SEQ");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Milestone).WithMany(p => p.ScheduleMilestones)
                .HasForeignKey(d => d.MilestoneId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SCHEDULE_MILESTONE_MILESTONE");

            entity.HasOne(d => d.Schedule).WithMany(p => p.ScheduleMilestones)
                .HasForeignKey(d => d.ScheduleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SCHEDULE_MILESTONE_SCHEDULE");
        });

        modelBuilder.Entity<SchedulePhase>(entity =>
        {
            entity.ToTable("SCHEDULE_PHASE");

            entity.Property(e => e.SchedulePhaseId)
                .ValueGeneratedNever()
                .HasColumnName("SCHEDULE_PHASE_ID");
            entity.Property(e => e.ActualDuration).HasColumnName("ACTUAL_DURATION");
            entity.Property(e => e.ActualEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("ACTUAL_END_DATE");
            entity.Property(e => e.ActualStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("ACTUAL_START_DATE");
            entity.Property(e => e.BaseCalduration).HasColumnName("BASE_CALDURATION");
            entity.Property(e => e.BaseDuration).HasColumnName("BASE_DURATION");
            entity.Property(e => e.BaseEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("BASE_END_DATE");
            entity.Property(e => e.BaseStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("BASE_START_DATE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DateCreated)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_CREATED");
            entity.Property(e => e.DateModified)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_MODIFIED");
            entity.Property(e => e.DateToEnd)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_TO_END");
            entity.Property(e => e.DateToStart)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_TO_START");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.PhaseReleaseId).HasColumnName("PHASE_RELEASE_ID");
            entity.Property(e => e.PlusminusDays).HasColumnName("PLUSMINUS_DAYS");
            entity.Property(e => e.ProjCalduration).HasColumnName("PROJ_CALDURATION");
            entity.Property(e => e.ProjDuration).HasColumnName("PROJ_DURATION");
            entity.Property(e => e.ProjEndDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PROJ_END_DATE");
            entity.Property(e => e.ProjStartDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PROJ_START_DATE");
            entity.Property(e => e.Published)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PUBLISHED");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SchedulePhaseDesc)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("SCHEDULE_PHASE_DESC");
            entity.Property(e => e.SchedulePhaseSeq).HasColumnName("SCHEDULE_PHASE_SEQ");
            entity.Property(e => e.ScheduleSubdivisionId).HasColumnName("SCHEDULE_SubdivisionID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UserCreated)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_CREATED");
            entity.Property(e => e.UserModified)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_MODIFIED");

            entity.HasOne(d => d.ScheduleSubdivision).WithMany(p => p.SchedulePhases)
                .HasForeignKey(d => d.ScheduleSubdivisionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SCHEDULE_PHASE_SCHEDULE_AREA1");
        });

        modelBuilder.Entity<ScheduleSactivity>(entity =>
        {
            entity.HasKey(e => e.ScheduleAid).HasName("PK_SCHEDULE_SACTIVITYNew");

            entity.ToTable("SCHEDULE_SACTIVITY");

            entity.Property(e => e.ScheduleAid).HasColumnName("SCHEDULE_AID");
            entity.Property(e => e.ActualDuration).HasColumnName("ACTUAL_DURATION");
            entity.Property(e => e.ActualEndDate)
                .HasColumnType("datetime")
                .HasColumnName("ACTUAL_END_DATE");
            entity.Property(e => e.ActualStartDate)
                .HasColumnType("datetime")
                .HasColumnName("ACTUAL_START_DATE");
            entity.Property(e => e.AdjustReminderWithProjStart)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("ADJUST_REMINDER_WITH_PROJ_START");
            entity.Property(e => e.BaseEndDate)
                .HasColumnType("datetime")
                .HasColumnName("BASE_END_DATE");
            entity.Property(e => e.BaseStartDate)
                .HasColumnType("datetime")
                .HasColumnName("BASE_START_DATE");
            entity.Property(e => e.Calduration).HasColumnName("CALDURATION");
            entity.Property(e => e.Complete)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("COMPLETE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Duration).HasColumnName("DURATION");
            entity.Property(e => e.Excludefromschedule)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("EXCLUDEFROMSCHEDULE");
            entity.Property(e => e.GenPitBudget)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("GEN_PIT_BUDGET");
            entity.Property(e => e.GrossLag)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("GROSS_LAG");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.IsLocked)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_LOCKED");
            entity.Property(e => e.LagTime).HasColumnName("LAG_TIME");
            entity.Property(e => e.Note)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("NOTE");
            entity.Property(e => e.NotifyDate)
                .HasColumnType("datetime")
                .HasColumnName("NOTIFY_DATE");
            entity.Property(e => e.PlusminusDays).HasColumnName("PLUSMINUS_DAYS");
            entity.Property(e => e.PreSchBaseEndDate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_BASE_END_DATE");
            entity.Property(e => e.PreSchBaseStartDate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_BASE_START_DATE");
            entity.Property(e => e.PreSchDuration).HasColumnName("PRE_SCH_DURATION");
            entity.Property(e => e.PreSchEndDate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_END_DATE");
            entity.Property(e => e.PreSchLagTime).HasColumnName("PRE_SCH_LAG_TIME");
            entity.Property(e => e.PreSchStartDate)
                .HasColumnType("datetime")
                .HasColumnName("PRE_SCH_START_DATE");
            entity.Property(e => e.ProjEndDate)
                .HasColumnType("datetime")
                .HasColumnName("PROJ_END_DATE");
            entity.Property(e => e.ProjStartDate)
                .HasColumnType("datetime")
                .HasColumnName("PROJ_START_DATE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Reminder)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("REMINDER");
            entity.Property(e => e.ReminderDate)
                .HasColumnType("datetime")
                .HasColumnName("REMINDER_DATE");
            entity.Property(e => e.ReminderText)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("REMINDER_TEXT");
            entity.Property(e => e.SaChecklistId).HasColumnName("SA_CHECKLIST_ID");
            entity.Property(e => e.SactivityId).HasColumnName("SACTIVITY_ID");
            entity.Property(e => e.SchDatesPinned).HasColumnName("SCH_DATES_PINNED");
            entity.Property(e => e.SchEndDate)
                .HasColumnType("datetime")
                .HasColumnName("SCH_END_DATE");
            entity.Property(e => e.SchStartDate)
                .HasColumnType("datetime")
                .HasColumnName("SCH_START_DATE");
            entity.Property(e => e.ScheduleId).HasColumnName("SCHEDULE_ID");
            entity.Property(e => e.ScheduleMid).HasColumnName("SCHEDULE_MID");
            entity.Property(e => e.Seq).HasColumnName("SEQ");
            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.SupplierNote)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("SUPPLIER_NOTE");
            entity.Property(e => e.TradeCrew)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("TRADE_CREW");
            entity.Property(e => e.UpdateDate)
                .HasColumnType("datetime")
                .HasColumnName("UPDATE_DATE");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UserAlpha1)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("USER_ALPHA1");
            entity.Property(e => e.UserAlpha2)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("USER_ALPHA2");
            entity.Property(e => e.UserAlpha3)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("USER_ALPHA3");
            entity.Property(e => e.UserAlpha4)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("USER_ALPHA4");
            entity.Property(e => e.UserAlpha5)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("USER_ALPHA5");
            entity.Property(e => e.UserAlpha6)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("USER_ALPHA6");
            entity.Property(e => e.UserCurrency1).HasColumnName("USER_CURRENCY1");
            entity.Property(e => e.UserCurrency2).HasColumnName("USER_CURRENCY2");
            entity.Property(e => e.UserCurrency3).HasColumnName("USER_CURRENCY3");
            entity.Property(e => e.UserCurrency4).HasColumnName("USER_CURRENCY4");
            entity.Property(e => e.UserCurrency5).HasColumnName("USER_CURRENCY5");
            entity.Property(e => e.UserCurrency6).HasColumnName("USER_CURRENCY6");
            entity.Property(e => e.UserDate1)
                .HasColumnType("datetime")
                .HasColumnName("USER_DATE1");
            entity.Property(e => e.UserDate2)
                .HasColumnType("datetime")
                .HasColumnName("USER_DATE2");
            entity.Property(e => e.UserDate3)
                .HasColumnType("datetime")
                .HasColumnName("USER_DATE3");
            entity.Property(e => e.UserDate4)
                .HasColumnType("datetime")
                .HasColumnName("USER_DATE4");
            entity.Property(e => e.UserDate5)
                .HasColumnType("datetime")
                .HasColumnName("USER_DATE5");
            entity.Property(e => e.UserDate6)
                .HasColumnType("datetime")
                .HasColumnName("USER_DATE6");
            entity.Property(e => e.VarianceCode)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("VARIANCE_CODE");

            entity.HasOne(d => d.Sactivity).WithMany(p => p.ScheduleSactivities)
                .HasForeignKey(d => d.SactivityId)
                .HasConstraintName("FK_SCHEDULE_SACTIVITY_SACTIVITY1");

            entity.HasOne(d => d.Schedule).WithMany(p => p.ScheduleSactivities)
                .HasForeignKey(d => d.ScheduleId)
                .HasConstraintName("FK_SCHEDULE_SACTIVITY_SCHEDULE");

            entity.HasOne(d => d.ScheduleM).WithMany(p => p.ScheduleSactivities)
                .HasForeignKey(d => d.ScheduleMid)
                .HasConstraintName("FK_SCHEDULE_SACTIVITY_SCHEDULE_MILESTONE");

            entity.HasOne(d => d.SubNumberNavigation).WithMany(p => p.ScheduleSactivities)
                .HasForeignKey(d => d.SubNumber)
                .HasConstraintName("FK_SCHEDULE_SACTIVITY_SUPPLIER");

            entity.HasOne(d => d.VarianceCodeNavigation).WithMany(p => p.ScheduleSactivities)
                .HasForeignKey(d => d.VarianceCode)
                .HasConstraintName("FK_SCHEDULE_SACTIVITY_VARIANCE");
        });

        modelBuilder.Entity<ScheduleSactivityPred>(entity =>
        {
            entity.HasKey(e => new { e.ScheduleAid, e.PredSactivityId }).HasName("SCHEDULE_SACTIVITY_PRED_PK");

            entity.ToTable("SCHEDULE_SACTIVITY_PRED");

            entity.HasIndex(e => e.ScheduleAid, "SCHEDULE_SACTIVITY_PRED_FK1");

            entity.HasIndex(e => e.PredSactivityId, "SCHEDULE_SACTIVITY_PRED_FK2");

            entity.Property(e => e.ScheduleAid).HasColumnName("SCHEDULE_AID");
            entity.Property(e => e.PredSactivityId).HasColumnName("PRED_SACTIVITY_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.PredScheduleAid).HasColumnName("PRED_SCHEDULE_AID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.PredSactivity).WithMany(p => p.ScheduleSactivityPreds)
                .HasForeignKey(d => d.PredSactivityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SCHEDULE_SACTIVITY_PRED_SACTIVITY");

            entity.HasOne(d => d.ScheduleA).WithMany(p => p.ScheduleSactivityPreds)
                .HasForeignKey(d => d.ScheduleAid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SCHEDULE_SACTIVITY_PRED_SCHEDULE_SACTIVITY");
        });

        modelBuilder.Entity<Subdivision>(entity =>
        {
            entity.HasKey(e => e.SubdivisionId).HasName("PK_Subdivision");

            entity.ToTable("SUBDIVISION");

            entity.Property(e => e.SubdivisionId).HasColumnName("SUBDIVISION_ID");
            entity.Property(e => e.AcquireDate)
                .HasColumnType("datetime")
                .HasColumnName("ACQUIRE_DATE");
            entity.Property(e => e.Acreage)
                .HasColumnType("decimal(8, 2)")
                .HasColumnName("ACREAGE");
            entity.Property(e => e.AddLotPremiumToBasePrice).HasColumnName("ADD_LOT_PREMIUM_TO_BASE_PRICE");
            entity.Property(e => e.AddModScatteredLots).HasColumnName("ADD_MOD_SCATTERED_LOTS");
            entity.Property(e => e.Address1)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("ADDRESS1");
            entity.Property(e => e.Address2)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("ADDRESS2");
            entity.Property(e => e.AgentsModOthersCustomer).HasColumnName("AGENTS_MOD_OTHERS_CUSTOMER");
            entity.Property(e => e.AgentsSeeOthersCustomer).HasColumnName("AGENTS_SEE_OTHERS_CUSTOMER");
            entity.Property(e => e.AllowApprovalOfAllOptions).HasColumnName("ALLOW_APPROVAL_OF_ALL_OPTIONS");
            entity.Property(e => e.AllowBackupReserv).HasColumnName("ALLOW_BACKUP_RESERV");
            entity.Property(e => e.AllowCounterOffers).HasColumnName("ALLOW_COUNTER_OFFERS");
            entity.Property(e => e.AllowDateEffPricing).HasColumnName("ALLOW_DATE_EFF_PRICING");
            entity.Property(e => e.AllowDupOptions).HasColumnName("ALLOW_DUP_OPTIONS");
            entity.Property(e => e.AllowFollowup).HasColumnName("ALLOW_FOLLOWUP");
            entity.Property(e => e.AllowLotTransfers).HasColumnName("ALLOW_LOT_TRANSFERS");
            entity.Property(e => e.AllowMultiStepApproval).HasColumnName("ALLOW_MULTI_STEP_APPROVAL");
            entity.Property(e => e.AllowNegOption).HasColumnName("ALLOW_NEG_OPTION");
            entity.Property(e => e.AllowRentVsBuy).HasColumnName("ALLOW_RENT_VS_BUY");
            entity.Property(e => e.ApplyVat).HasColumnName("APPLY_VAT");
            entity.Property(e => e.ApplyVatRebate).HasColumnName("APPLY_VAT_REBATE");
            entity.Property(e => e.AutoFreeExpiredLots).HasColumnName("AUTO_FREE_EXPIRED_LOTS");
            entity.Property(e => e.CableName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("CABLE_NAME");
            entity.Property(e => e.CablePhone)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("CABLE_PHONE");
            entity.Property(e => e.CanAddSpec).HasColumnName("CAN_ADD_SPEC");
            entity.Property(e => e.CanClearSpec).HasColumnName("CAN_CLEAR_SPEC");
            entity.Property(e => e.CanDeleteProspect).HasColumnName("CAN_DELETE_PROSPECT");
            entity.Property(e => e.CanModOptions).HasColumnName("CAN_MOD_OPTIONS");
            entity.Property(e => e.CanModSpec).HasColumnName("CAN_MOD_SPEC");
            entity.Property(e => e.ChangeBasePrice).HasColumnName("CHANGE_BASE_PRICE");
            entity.Property(e => e.ChangeIncentivesAfterContract).HasColumnName("CHANGE_INCENTIVES_AFTER_CONTRACT");
            entity.Property(e => e.ChangeLotPremium).HasColumnName("CHANGE_LOT_PREMIUM");
            entity.Property(e => e.ChangeOptPriceAfterContract).HasColumnName("CHANGE_OPT_PRICE_AFTER_CONTRACT");
            entity.Property(e => e.ChangePriceAllOption).HasColumnName("CHANGE_PRICE_ALL_OPTION");
            entity.Property(e => e.ChangePriceCustOpt).HasColumnName("CHANGE_PRICE_CUST_OPT");
            entity.Property(e => e.ChangeQtyAllOption).HasColumnName("CHANGE_QTY_ALL_OPTION");
            entity.Property(e => e.ChangeQtyCustOpt).HasColumnName("CHANGE_QTY_CUST_OPT");
            entity.Property(e => e.ChangeSpecBasePrice).HasColumnName("CHANGE_SPEC_BASE_PRICE");
            entity.Property(e => e.ChangeTypeAllOption).HasColumnName("CHANGE_TYPE_ALL_OPTION");
            entity.Property(e => e.ChangeTypeCustOpt).HasColumnName("CHANGE_TYPE_CUST_OPT");
            entity.Property(e => e.City)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("CITY");
            entity.Property(e => e.Contracted)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("CONTRACTED");
            entity.Property(e => e.Country)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("COUNTRY");
            entity.Property(e => e.CountryId).HasColumnName("COUNTRY_ID");
            entity.Property(e => e.County)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("COUNTY");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CustPortalCssUrl)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("CUST_PORTAL_CSS_URL");
            entity.Property(e => e.CustPortalLogoUrl)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("CUST_PORTAL_LOGO_URL");
            entity.Property(e => e.DefaultOptionTypeId).HasColumnName("DEFAULT_OPTION_TYPE_ID");
            entity.Property(e => e.DefaultPassword)
                .HasMaxLength(16)
                .IsUnicode(false)
                .HasColumnName("DEFAULT_PASSWORD");
            entity.Property(e => e.DocsFolder)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("DOCS_FOLDER");
            entity.Property(e => e.ElectricName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("ELECTRIC_NAME");
            entity.Property(e => e.ElectricPhone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("ELECTRIC_PHONE");
            entity.Property(e => e.Email)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("EMAIL");
            entity.Property(e => e.EmailPosToSuperviser)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("EMAIL_POS_TO_SUPERVISER");
            entity.Property(e => e.EngineerName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("ENGINEER_NAME");
            entity.Property(e => e.EngineerPhone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("ENGINEER_PHONE");
            entity.Property(e => e.EntityName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("ENTITY_NAME");
            entity.Property(e => e.EntityNum)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("ENTITY_NUM");
            entity.Property(e => e.EstHazardInsurType).HasColumnName("EST_HAZARD_INSUR_TYPE");
            entity.Property(e => e.EstHazardInsurValue)
                .HasColumnType("decimal(8, 2)")
                .HasColumnName("EST_HAZARD_INSUR_VALUE");
            entity.Property(e => e.EstPropertyTax)
                .HasColumnType("decimal(4, 2)")
                .HasColumnName("EST_PROPERTY_TAX");
            entity.Property(e => e.ExcludeFromReports).HasColumnName("EXCLUDE_FROM_REPORTS");
            entity.Property(e => e.ExpireDays).HasColumnName("EXPIRE_DAYS");
            entity.Property(e => e.Fax)
                .HasMaxLength(21)
                .IsUnicode(false)
                .HasColumnName("FAX");
            entity.Property(e => e.GasName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("GAS_NAME");
            entity.Property(e => e.GasPhone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("GAS_PHONE");
            entity.Property(e => e.HideRetainOptBySoc).HasColumnName("HIDE_RETAIN_OPT_BY_SOC");
            entity.Property(e => e.HideRevertToDirt).HasColumnName("HIDE_REVERT_TO_DIRT");
            entity.Property(e => e.HoaFees)
                .HasColumnType("decimal(7, 2)")
                .HasColumnName("HOA_FEES");
            entity.Property(e => e.HoaName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("HOA_NAME");
            entity.Property(e => e.HoaPhone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("HOA_PHONE");
            entity.Property(e => e.HoaType).HasColumnName("HOA_TYPE");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.KeepHistoryLotTransfer).HasColumnName("KEEP_HISTORY_LOT_TRANSFER");
            entity.Property(e => e.LanguageSetId).HasColumnName("LANGUAGE_SET_ID");
            entity.Property(e => e.LastChanged)
                .HasColumnType("datetime")
                .HasColumnName("LAST_CHANGED");
            entity.Property(e => e.LastUser).HasColumnName("LAST_USER");
            entity.Property(e => e.Latitude)
                .HasColumnType("decimal(9, 6)")
                .HasColumnName("LATITUDE");
            entity.Property(e => e.LegalName)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("LEGAL_NAME");
            entity.Property(e => e.LockOptionDesc).HasColumnName("LOCK_OPTION_DESC");
            entity.Property(e => e.LockSpecBasePrice).HasColumnName("LOCK_SPEC_BASE_PRICE");
            entity.Property(e => e.LockSpecOptionPrice).HasColumnName("LOCK_SPEC_OPTION_PRICE");
            entity.Property(e => e.Longitude)
                .HasColumnType("decimal(9, 6)")
                .HasColumnName("LONGITUDE");
            entity.Property(e => e.MarketingDesc)
                .IsUnicode(false)
                .HasColumnName("MARKETING_DESC");
            entity.Property(e => e.MarketingName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("MARKETING_NAME");
            entity.Property(e => e.MaxIncentiveType).HasColumnName("MAX_INCENTIVE_TYPE");
            entity.Property(e => e.MaxIncentiveValue)
                .HasColumnType("decimal(8, 2)")
                .HasColumnName("MAX_INCENTIVE_VALUE");
            entity.Property(e => e.MaxLoanAmt)
                .HasColumnType("decimal(7, 0)")
                .HasColumnName("MAX_LOAN_AMT");
            entity.Property(e => e.MiscellaneousFee)
                .HasColumnType("decimal(8, 2)")
                .HasColumnName("MISCELLANEOUS_FEE");
            entity.Property(e => e.Phone)
                .HasMaxLength(21)
                .IsUnicode(false)
                .HasColumnName("PHONE");
            entity.Property(e => e.ProtectFromMaster)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PROTECT_FROM_MASTER");
            entity.Property(e => e.PublicDate)
                .HasColumnType("datetime")
                .HasColumnName("PUBLIC_DATE");
            entity.Property(e => e.PublicReportNum)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("PUBLIC_REPORT_NUM");
            entity.Property(e => e.QuickTrafficDemoId).HasColumnName("QUICK_TRAFFIC_DEMO_ID");
            entity.Property(e => e.RealtorCommission)
                .HasColumnType("decimal(4, 2)")
                .HasColumnName("REALTOR_COMMISSION");
            entity.Property(e => e.RecordTimeStampe)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.RefuseName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("REFUSE_NAME");
            entity.Property(e => e.RefusePhone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("REFUSE_PHONE");
            entity.Property(e => e.SelectOptionsByRoom).HasColumnName("SELECT_OPTIONS_BY_ROOM");
            entity.Property(e => e.Service1)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("SERVICE1");
            entity.Property(e => e.Service1phone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("SERVICE1PHONE");
            entity.Property(e => e.Service2)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("SERVICE2");
            entity.Property(e => e.Service2phone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("SERVICE2PHONE");
            entity.Property(e => e.Service3)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("SERVICE3");
            entity.Property(e => e.Service3phone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("SERVICE3PHONE");
            entity.Property(e => e.Service4)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("SERVICE4");
            entity.Property(e => e.Service4phone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("SERVICE4PHONE");
            entity.Property(e => e.Service5)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("SERVICE5");
            entity.Property(e => e.Service5phone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("SERVICE5PHONE");
            entity.Property(e => e.Service6)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("SERVICE6");
            entity.Property(e => e.Service6phone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("SERVICE6PHONE");
            entity.Property(e => e.Service7)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("SERVICE7");
            entity.Property(e => e.Service7phone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("SERVICE7PHONE");
            entity.Property(e => e.Service8)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("SERVICE8");
            entity.Property(e => e.Service8phone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("SERVICE8PHONE");
            entity.Property(e => e.SetupFee)
                .HasColumnType("decimal(8, 2)")
                .HasColumnName("SETUP_FEE");
            entity.Property(e => e.SewerName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("SEWER_NAME");
            entity.Property(e => e.SewerPhone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("SEWER_PHONE");
            entity.Property(e => e.StandardSubdivisionName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("STANDARD_SUBDIVISION_NAME");
            entity.Property(e => e.State)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("STATE");
            entity.Property(e => e.StateId).HasColumnName("STATE_ID");
            entity.Property(e => e.SubdivisionClass)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("SUBDIVISION_CLASS");
            entity.Property(e => e.SubdivisionName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("SUBDIVISION_NAME");
            entity.Property(e => e.SubdivisionNum)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUBDIVISION_NUM");
            entity.Property(e => e.SubdivisionStatus)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("SUBDIVISION_STATUS");
            entity.Property(e => e.TargetUnits).HasColumnName("TARGET_UNITS");
            entity.Property(e => e.TelephoneName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("TELEPHONE_NAME");
            entity.Property(e => e.TelephonePhone)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("TELEPHONE_PHONE");
            entity.Property(e => e.TextMsgAccountId)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("TEXT_MSG_ACCOUNT_ID");
            entity.Property(e => e.TextMsgPassword)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("TEXT_MSG_PASSWORD");
            entity.Property(e => e.TextMsgUserId)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("TEXT_MSG_USER_ID");
            entity.Property(e => e.TotalLots).HasColumnName("TOTAL_LOTS");
            entity.Property(e => e.TwitterWidgetId)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("TWITTER_WIDGET_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
            entity.Property(e => e.UseElectronicDocStorage).HasColumnName("USE_ELECTRONIC_DOC_STORAGE");
            entity.Property(e => e.UseElectronicSignature).HasColumnName("USE_ELECTRONIC_SIGNATURE");
            entity.Property(e => e.VmhVmb)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("VMH_VMB");
            entity.Property(e => e.WarrantyEnabled)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("WARRANTY_ENABLED");
            entity.Property(e => e.WarrantyShortName)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("WARRANTY_SHORT_NAME");
            entity.Property(e => e.WaterName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("WATER_NAME");
            entity.Property(e => e.WaterPhone)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("WATER_PHONE");
            entity.Property(e => e.WorkZoneId).HasColumnName("WORK_ZONE_ID");
            entity.Property(e => e.Workdays)
                .HasMaxLength(14)
                .IsUnicode(false)
                .HasColumnName("WORKDAYS");
            entity.Property(e => e.Zip)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("ZIP");
            entity.Property(e => e.Zoning)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("ZONING");
        });

        modelBuilder.Entity<SubdivisionContact>(entity =>
        {
            entity.HasKey(e => e.SubdivisionContactId).HasName("AREA_CONTACT_PK");

            entity.ToTable("SUBDIVISION_CONTACT");

            entity.Property(e => e.SubdivisionContactId)
                .ValueGeneratedNever()
                .HasColumnName("SUBDIVISION_CONTACT_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SiteContact1)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasDefaultValueSql("('F')")
                .HasColumnName("SITE_CONTACT_1");
            entity.Property(e => e.SiteContact2)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasDefaultValueSql("('F')")
                .HasColumnName("SITE_CONTACT_2");
            entity.Property(e => e.SiteContact3)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasDefaultValueSql("('F')")
                .HasColumnName("SITE_CONTACT_3");
            entity.Property(e => e.SiteContact4)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasDefaultValueSql("('F')")
                .HasColumnName("SITE_CONTACT_4");
            entity.Property(e => e.SiteContact5)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasDefaultValueSql("('F')")
                .HasColumnName("SITE_CONTACT_5");
            entity.Property(e => e.SiteContact6)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasDefaultValueSql("('F')")
                .HasColumnName("SITE_CONTACT_6");
            entity.Property(e => e.SubdivisionId).HasColumnName("SUBDIVISION_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.UserId)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_ID");
            entity.Property(e => e.VpoApprovalLevel).HasColumnName("VPO_APPROVAL_LEVEL");

            entity.HasOne(d => d.Subdivision).WithMany(p => p.SubdivisionContacts)
                .HasForeignKey(d => d.SubdivisionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SUBDIVISION_CONTACT_Subdivision");

            entity.HasOne(d => d.User).WithMany(p => p.SubdivisionContacts)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SUBDIVISION_CONTACT_USERS");
        });

        modelBuilder.Entity<Supplier>(entity =>
        {
            entity.HasKey(e => e.SubNumber).HasName("PK_Supplier");

            entity.ToTable("SUPPLIER");

            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.AbnNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("ABN_NUMBER");
            entity.Property(e => e.Create2ndLienWaiverHold)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("CREATE_2ND_LIEN_WAIVER_HOLD");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DiscountDays).HasColumnName("DISCOUNT_DAYS");
            entity.Property(e => e.DiscountPercent).HasColumnName("DISCOUNT_PERCENT");
            entity.Property(e => e.EdiGscode)
                .HasMaxLength(15)
                .IsUnicode(false)
                .HasColumnName("EDI_GSCODE");
            entity.Property(e => e.EdiInterchange)
                .HasMaxLength(17)
                .IsUnicode(false)
                .HasColumnName("EDI_INTERCHANGE");
            entity.Property(e => e.EdiOrders)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("EDI_ORDERS");
            entity.Property(e => e.Ediorderbatchno).HasColumnName("EDIORDERBATCHNO");
            entity.Property(e => e.Email)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("EMAIL");
            entity.Property(e => e.Fax)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("FAX");
            entity.Property(e => e.GlInsExpiryDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("GL_INS_EXPIRY_DATE");
            entity.Property(e => e.GlInsRequired)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("GL_INS_REQUIRED");
            entity.Property(e => e.IsActive)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_ACTIVE");
            entity.Property(e => e.IsActive1)
                .HasDefaultValueSql("((1))")
                .HasColumnName("IsActive");
            entity.Property(e => e.MasterAgreementExpiryDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("MASTER_AGREEMENT_EXPIRY_DATE");
            entity.Property(e => e.MasterAgreementRequired)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("MASTER_AGREEMENT_REQUIRED");
            entity.Property(e => e.MiscDeduct2Rate).HasColumnName("MISC_DEDUCT_2_RATE");
            entity.Property(e => e.MiscDeductRate).HasColumnName("MISC_DEDUCT_RATE");
            entity.Property(e => e.OrderMode).HasColumnName("ORDER_MODE");
            entity.Property(e => e.OrganizationId).HasColumnName("ORGANIZATION_ID");
            entity.Property(e => e.OrganizationUserId).HasColumnName("ORGANIZATION_USER_ID");
            entity.Property(e => e.PaymentDays).HasColumnName("PAYMENT_DAYS");
            entity.Property(e => e.Phone)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("PHONE");
            entity.Property(e => e.PmtDaysType)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("PMT_DAYS_TYPE");
            entity.Property(e => e.PoPlugSupplier)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("PO_PLUG_SUPPLIER");
            entity.Property(e => e.PrefilledTaxAmount)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("PREFILLED_TAX_AMOUNT");
            entity.Property(e => e.ReceivesForm1099)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("RECEIVES_FORM_1099");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Retainage).HasColumnName("RETAINAGE");
            entity.Property(e => e.ShortName)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SHORT_NAME");
            entity.Property(e => e.SubAddress)
                .HasMaxLength(33)
                .IsUnicode(false)
                .HasColumnName("SUB_ADDRESS");
            entity.Property(e => e.SubAddress2)
                .HasMaxLength(33)
                .IsUnicode(false)
                .HasColumnName("SUB_ADDRESS_2");
            entity.Property(e => e.SubApNumber)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("SUB_AP_NUMBER");
            entity.Property(e => e.SubCity)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("SUB_CITY");
            entity.Property(e => e.SubContact)
                .HasMaxLength(60)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT");
            entity.Property(e => e.SubContact1Pos)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_1_POS");
            entity.Property(e => e.SubContact2)
                .HasMaxLength(60)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_2");
            entity.Property(e => e.SubContact2Dc)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_2_DC");
            entity.Property(e => e.SubContact2Email)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_2_EMAIL");
            entity.Property(e => e.SubContact2Isadmin)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_2_ISADMIN");
            entity.Property(e => e.SubContact2Mobile)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_2_MOBILE");
            entity.Property(e => e.SubContact2MobileSp).HasColumnName("SUB_CONTACT_2_MOBILE_SP");
            entity.Property(e => e.SubContact2Password)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_2_PASSWORD");
            entity.Property(e => e.SubContact2Pos)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_2_POS");
            entity.Property(e => e.SubContact2Purch)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_2_PURCH");
            entity.Property(e => e.SubContact2Sched)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_2_SCHED");
            entity.Property(e => e.SubContact3)
                .HasMaxLength(60)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_3");
            entity.Property(e => e.SubContact3Dc)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_3_DC");
            entity.Property(e => e.SubContact3Email)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_3_EMAIL");
            entity.Property(e => e.SubContact3Isadmin)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_3_ISADMIN");
            entity.Property(e => e.SubContact3Mobile)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_3_MOBILE");
            entity.Property(e => e.SubContact3MobileSp).HasColumnName("SUB_CONTACT_3_MOBILE_SP");
            entity.Property(e => e.SubContact3Password)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_3_PASSWORD");
            entity.Property(e => e.SubContact3Pos)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_3_POS");
            entity.Property(e => e.SubContact3Purch)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_3_PURCH");
            entity.Property(e => e.SubContact3Sched)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_3_SCHED");
            entity.Property(e => e.SubContactDc)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_DC");
            entity.Property(e => e.SubContactEmail)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_EMAIL");
            entity.Property(e => e.SubContactIsadmin)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_ISADMIN");
            entity.Property(e => e.SubContactMobile)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_MOBILE");
            entity.Property(e => e.SubContactMobileSp).HasColumnName("SUB_CONTACT_MOBILE_SP");
            entity.Property(e => e.SubContactPassword)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_PASSWORD");
            entity.Property(e => e.SubContactPurch)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_PURCH");
            entity.Property(e => e.SubContactSched)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_SCHED");
            entity.Property(e => e.SubContactWrty)
                .HasMaxLength(60)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_WRTY");
            entity.Property(e => e.SubContactWrtyDc)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_WRTY_DC");
            entity.Property(e => e.SubContactWrtyEmail)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_WRTY_EMAIL");
            entity.Property(e => e.SubContactWrtyMobile)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_WRTY_MOBILE");
            entity.Property(e => e.SubContactWrtyMobileSp).HasColumnName("SUB_CONTACT_WRTY_MOBILE_SP");
            entity.Property(e => e.SubContactWrtyPos)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("SUB_CONTACT_WRTY_POS");
            entity.Property(e => e.SubCountry)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("SUB_COUNTRY");
            entity.Property(e => e.SubFax)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_FAX");
            entity.Property(e => e.SubFax2)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_FAX_2");
            entity.Property(e => e.SubFax3)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_FAX_3");
            entity.Property(e => e.SubFaxWrty)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_FAX_WRTY");
            entity.Property(e => e.SubName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("SUB_NAME");
            entity.Property(e => e.SubNotes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("SUB_NOTES");
            entity.Property(e => e.SubPhone)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_PHONE");
            entity.Property(e => e.SubPhone2)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_PHONE_2");
            entity.Property(e => e.SubPhone3)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_PHONE_3");
            entity.Property(e => e.SubPhoneWrty)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_PHONE_WRTY");
            entity.Property(e => e.SubPostcode)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("SUB_POSTCODE");
            entity.Property(e => e.SubState)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("SUB_STATE");
            entity.Property(e => e.SubType1)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUB_TYPE_1");
            entity.Property(e => e.SubType2)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("SUB_TYPE_2");
            entity.Property(e => e.SubType3)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("SUB_TYPE_3");
            entity.Property(e => e.SubType4)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("SUB_TYPE_4");
            entity.Property(e => e.Subtaxexempt)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUBTAXEXEMPT");
            entity.Property(e => e.Subtaxgroup)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("SUBTAXGROUP");
            entity.Property(e => e.Supplierarcode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("SUPPLIERARCODE");
            entity.Property(e => e.TermsAccepted)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("TERMS_ACCEPTED");
            entity.Property(e => e.TermsAcceptedDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("TERMS_ACCEPTED_DATE");
            entity.Property(e => e.TermsAcceptedUser)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("TERMS_ACCEPTED_USER");
            entity.Property(e => e.TradePortalSupplier)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("TRADE_PORTAL_SUPPLIER");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
            entity.Property(e => e.UsesReservedCosts)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("USES_RESERVED_COSTS");
            entity.Property(e => e.VehicleInsExpiryDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("VEHICLE_INS_EXPIRY_DATE");
            entity.Property(e => e.VehicleInsRequired)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("VEHICLE_INS_REQUIRED");
            entity.Property(e => e.WarrantyEnabled)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("WARRANTY_ENABLED");
            entity.Property(e => e.WcompInsExpiryDate)
                .HasColumnType("smalldatetime")
                .HasColumnName("WCOMP_INS_EXPIRY_DATE");
            entity.Property(e => e.WcompInsRequired)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("WCOMP_INS_REQUIRED");
        });

        modelBuilder.Entity<SupplierContact>(entity =>
        {
            entity.ToTable("SUPPLIER_CONTACT");

            entity.Property(e => e.SupplierContactId).HasColumnName("SUPPLIER_CONTACT_ID");
            entity.Property(e => e.ContactId).HasColumnName("CONTACT_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.SupplierContactIsadmin)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUPPLIER_CONTACT_ISADMIN");
            entity.Property(e => e.SupplierContactNumber).HasColumnName("SUPPLIER_CONTACT_NUMBER");
            entity.Property(e => e.SupplierContactPurch)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUPPLIER_CONTACT_PURCH");
            entity.Property(e => e.SupplierContactSched)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUPPLIER_CONTACT_SCHED");
            entity.Property(e => e.SupplierContactTitle)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("SUPPLIER_CONTACT_TITLE");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Contact).WithMany(p => p.SupplierContacts)
                .HasForeignKey(d => d.ContactId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SUPPLIER_CONTACT_CONTACT");

            entity.HasOne(d => d.SubNumberNavigation).WithMany(p => p.SupplierContacts)
                .HasForeignKey(d => d.SubNumber)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SUPPLIER_CONTACT_SUPPLIER");
        });

        modelBuilder.Entity<SupplierTradeType>(entity =>
        {
            entity.HasKey(e => new { e.SubNumber, e.SubTypeId });

            entity.ToTable("SUPPLIER_TRADE_TYPE");

            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.SubTypeId).HasColumnName("SUB_TYPE_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");

            entity.HasOne(d => d.SubNumberNavigation).WithMany(p => p.SupplierTradeTypes)
                .HasForeignKey(d => d.SubNumber)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SUPPLIER_TRADE_TYPE_SUPPLIER");

            entity.HasOne(d => d.SubType).WithMany(p => p.SupplierTradeTypes)
                .HasForeignKey(d => d.SubTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SUPPLIER_TRADE_TYPE_SUPPLIER_TYPE");
        });

        modelBuilder.Entity<SupplierType>(entity =>
        {
            entity.HasKey(e => e.SubTypeId);

            entity.ToTable("SUPPLIER_TYPE");

            entity.Property(e => e.SubTypeId).HasColumnName("SUB_TYPE_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SubTypeName)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("SUB_TYPE_NAME");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
        });

        modelBuilder.Entity<TbBuiltOption>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__tbBuiltO__3214EC27D288368F");

            entity.ToTable("tbBuiltOptions", "cm");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AvailableOptionIdNotused).HasColumnName("AvailableOptionID_notused");
            entity.Property(e => e.ChangeDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ClientId).HasColumnName("ClientID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Crgroup)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("CRGroup");
            entity.Property(e => e.CustomerDesc).HasColumnType("ntext");
            entity.Property(e => e.ExternalCode)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.FastcustomRatified).HasColumnName("FASTCustomRatified");
            entity.Property(e => e.InternalRefId).HasColumnName("InternalRefID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.JobNumber)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("JOB_NUMBER");
            entity.Property(e => e.LastChanged).HasColumnType("smalldatetime");
            entity.Property(e => e.LotId).HasColumnName("LotID");
            entity.Property(e => e.OpAttrGroupItemId).HasColumnName("OP_ATTR_GROUP_ITEM_ID");
            entity.Property(e => e.OptionCode)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.OptionDesc)
                .HasMaxLength(150)
                .IsUnicode(false);
            entity.Property(e => e.OptionGroupId).HasColumnName("OptionGroupID");
            entity.Property(e => e.OptionLongDesc).HasColumnType("ntext");
            entity.Property(e => e.OptionTypeId).HasColumnName("OptionTypeID");
            entity.Property(e => e.OrigUniqueOptionId).HasColumnName("OrigUniqueOptionID");
            entity.Property(e => e.PlanCode)
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.PlanOptionId).HasColumnName("PLAN_OPTION_ID");
            entity.Property(e => e.Price).HasColumnType("decimal(12, 2)");
            entity.Property(e => e.PriceDelta).HasColumnType("numeric(12, 2)");
            entity.Property(e => e.PrintDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Qty).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.QtyDelta).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Restrictions).HasColumnType("ntext");
            entity.Property(e => e.RoomPlanId).HasColumnName("RoomPlanID");
            entity.Property(e => e.SelectedUserId).HasColumnName("SelectedUserID");
            entity.Property(e => e.UniqueOptionId).HasColumnName("UniqueOptionID");
            entity.Property(e => e.UnitCost).HasColumnType("decimal(8, 2)");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<Template>(entity =>
        {
            entity.ToTable("TEMPLATE");

            entity.Property(e => e.TemplateId).HasColumnName("TEMPLATE_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.Createddatetime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("CREATEDDATETIME");
            entity.Property(e => e.DateCreated)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_CREATED");
            entity.Property(e => e.Description)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("DESCRIPTION");
            entity.Property(e => e.DivId).HasColumnName("DIV_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.TemplateName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("TEMPLATE_NAME");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.Updateddatetime)
                .HasColumnType("smalldatetime")
                .HasColumnName("UPDATEDDATETIME");
            entity.Property(e => e.UserCreated)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_CREATED");
        });

        modelBuilder.Entity<TemplateMilestone>(entity =>
        {
            entity.HasKey(e => e.TemplateMid);

            entity.ToTable("TEMPLATE_MILESTONE");

            entity.Property(e => e.TemplateMid).HasColumnName("TEMPLATE_MID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.Createddatetime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("CREATEDDATETIME");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.MilestoneId).HasColumnName("MILESTONE_ID");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Seq).HasColumnName("SEQ");
            entity.Property(e => e.TemplateId).HasColumnName("TEMPLATE_ID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.Updateddatetime)
                .HasColumnType("smalldatetime")
                .HasColumnName("UPDATEDDATETIME");

            entity.HasOne(d => d.Milestone).WithMany(p => p.TemplateMilestones)
                .HasForeignKey(d => d.MilestoneId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TEMPLATE_MILESTONE_MILESTONE");

            entity.HasOne(d => d.Template).WithMany(p => p.TemplateMilestones)
                .HasForeignKey(d => d.TemplateId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TEMPLATE_MILESTONE_TEMPLATE");
        });

        modelBuilder.Entity<TemplateSactivity>(entity =>
        {
            entity.HasKey(e => e.TemplateAid);

            entity.ToTable("TEMPLATE_SACTIVITY");

            entity.Property(e => e.TemplateAid).HasColumnName("TEMPLATE_AID");
            entity.Property(e => e.ChecklistId).HasColumnName("CHECKLIST_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.Createddatetime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("CREATEDDATETIME");
            entity.Property(e => e.Duration).HasColumnName("DURATION");
            entity.Property(e => e.GenPitBudget)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("GEN_PIT_BUDGET");
            entity.Property(e => e.GrossLag)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("GROSS_LAG");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.LagTime).HasColumnName("LAG_TIME");
            entity.Property(e => e.Note)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("NOTE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SactivityId).HasColumnName("SACTIVITY_ID");
            entity.Property(e => e.Seq).HasColumnName("SEQ");
            entity.Property(e => e.TemplateIdNotused).HasColumnName("TEMPLATE_ID_notused");
            entity.Property(e => e.TemplateMid).HasColumnName("TEMPLATE_MID");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.Updateddatetime)
                .HasColumnType("smalldatetime")
                .HasColumnName("UPDATEDDATETIME");
            entity.Property(e => e.UserAlpha1)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("USER_ALPHA1");
            entity.Property(e => e.UserAlpha2)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("USER_ALPHA2");
            entity.Property(e => e.UserAlpha3)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("USER_ALPHA3");
            entity.Property(e => e.UserAlpha4)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("USER_ALPHA4");
            entity.Property(e => e.UserAlpha5)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("USER_ALPHA5");
            entity.Property(e => e.UserAlpha6)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("USER_ALPHA6");
            entity.Property(e => e.UserCurrency1).HasColumnName("USER_CURRENCY1");
            entity.Property(e => e.UserCurrency2).HasColumnName("USER_CURRENCY2");
            entity.Property(e => e.UserCurrency3).HasColumnName("USER_CURRENCY3");
            entity.Property(e => e.UserCurrency4).HasColumnName("USER_CURRENCY4");
            entity.Property(e => e.UserCurrency5).HasColumnName("USER_CURRENCY5");
            entity.Property(e => e.UserCurrency6).HasColumnName("USER_CURRENCY6");
            entity.Property(e => e.UserDate1)
                .HasColumnType("smalldatetime")
                .HasColumnName("USER_DATE1");
            entity.Property(e => e.UserDate2)
                .HasColumnType("smalldatetime")
                .HasColumnName("USER_DATE2");
            entity.Property(e => e.UserDate3)
                .HasColumnType("smalldatetime")
                .HasColumnName("USER_DATE3");
            entity.Property(e => e.UserDate4)
                .HasColumnType("smalldatetime")
                .HasColumnName("USER_DATE4");
            entity.Property(e => e.UserDate5)
                .HasColumnType("smalldatetime")
                .HasColumnName("USER_DATE5");
            entity.Property(e => e.UserDate6)
                .HasColumnType("smalldatetime")
                .HasColumnName("USER_DATE6");

            entity.HasOne(d => d.Sactivity).WithMany(p => p.TemplateSactivities)
                .HasForeignKey(d => d.SactivityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TEMPLATE_SACTIVITY_SACTIVITY");

            entity.HasOne(d => d.TemplateM).WithMany(p => p.TemplateSactivities)
                .HasForeignKey(d => d.TemplateMid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TEMPLATE_SACTIVITY_TEMPLATE_MILESTONE");
        });

        modelBuilder.Entity<TemplateSactivityPred>(entity =>
        {
            entity.HasKey(e => new { e.TemplateAid, e.PredSactivityId }).HasName("TEMPLATE_SACTIVITY_PRED_PK");

            entity.ToTable("TEMPLATE_SACTIVITY_PRED");

            entity.Property(e => e.TemplateAid).HasColumnName("TEMPLATE_AID");
            entity.Property(e => e.PredSactivityId).HasColumnName("PRED_SACTIVITY_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.Createddatetime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("CREATEDDATETIME");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.Updateddatetime)
                .HasColumnType("smalldatetime")
                .HasColumnName("UPDATEDDATETIME");

            entity.HasOne(d => d.PredSactivity).WithMany(p => p.TemplateSactivityPreds)
                .HasForeignKey(d => d.PredSactivityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TEMPLATE_SACTIVITY_PRED_SACTIVITY");

            entity.HasOne(d => d.TemplateA).WithMany(p => p.TemplateSactivityPreds)
                .HasForeignKey(d => d.TemplateAid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TEMPLATE_SACTIVITY_PRED_TEMPLATE_SACTIVITY");
        });

        modelBuilder.Entity<Trade>(entity =>
        {
            entity.ToTable("TRADE");

            entity.Property(e => e.TradeId).HasColumnName("TRADE_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CubitId)
                .HasMaxLength(128)
                .IsUnicode(false)
                .HasColumnName("CUBIT_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.TermsFilename)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("TERMS_FILENAME");
            entity.Property(e => e.TradeDesc)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("TRADE_DESC");
            entity.Property(e => e.TradeName)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("TRADE_NAME");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("datetime");
            entity.Property(e => e.Workdays)
                .HasMaxLength(14)
                .IsUnicode(false)
                .HasColumnName("WORKDAYS");
        });

        modelBuilder.Entity<TradeSupplier>(entity =>
        {
            entity.HasKey(e => new { e.TradeId, e.SubNumber, e.SubdivisionId });

            entity.ToTable("TRADE_SUPPLIER");

            entity.Property(e => e.TradeId).HasColumnName("TRADE_ID");
            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.SubdivisionId).HasColumnName("SUBDIVISION_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DefaultSupplier)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("DEFAULT_SUPPLIER");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");

            entity.HasOne(d => d.SubNumberNavigation).WithMany(p => p.TradeSuppliers)
                .HasForeignKey(d => d.SubNumber)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TRADE_SUPPLIER_SUPPLIER");

            entity.HasOne(d => d.Subdivision).WithMany(p => p.TradeSuppliers)
                .HasForeignKey(d => d.SubdivisionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TRADE_SUPPLIER_Subdivision");

            entity.HasOne(d => d.Trade).WithMany(p => p.TradeSuppliers)
                .HasForeignKey(d => d.TradeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TRADE_SUPPLIER_TRADE");
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.UserId).HasName("PK_USERS_1");

            entity.ToTable("USERS");

            entity.Property(e => e.UserId)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_ID");
            entity.Property(e => e.Accesstobmtsupport)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("ACCESSTOBMTSUPPORT");
            entity.Property(e => e.Administrator)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("ADMINISTRATOR");
            entity.Property(e => e.ApprovalLimit).HasColumnName("APPROVAL_LIMIT");
            entity.Property(e => e.CorrigoUserId).HasColumnName("CORRIGO_USER_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.Createddatetime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("CREATEDDATETIME");
            entity.Property(e => e.DateInactivated)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_INACTIVATED");
            entity.Property(e => e.DateStamp)
                .HasColumnType("smalldatetime")
                .HasColumnName("DATE_STAMP");
            entity.Property(e => e.EmailAddress)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("EMAIL_ADDRESS");
            entity.Property(e => e.Extension)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("EXTENSION");
            entity.Property(e => e.FirstName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("FIRST_NAME");
            entity.Property(e => e.IsActive)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_ACTIVE");
            entity.Property(e => e.IsActive1)
                .IsRequired()
                .HasDefaultValueSql("((1))")
                .HasColumnName("IsActive");
            entity.Property(e => e.IsProtected)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_PROTECTED");
            entity.Property(e => e.LastName)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("LAST_NAME");
            entity.Property(e => e.MobilePhone)
                .HasMaxLength(14)
                .IsUnicode(false)
                .HasColumnName("MOBILE_PHONE");
            entity.Property(e => e.MobileSchedUser)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("MOBILE_SCHED_USER");
            entity.Property(e => e.Notes)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("NOTES");
            entity.Property(e => e.Picture).HasColumnName("PICTURE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Supervisor)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUPERVISOR");
            entity.Property(e => e.SuptPortalUser)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("SUPT_PORTAL_USER");
            entity.Property(e => e.Title)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("TITLE");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.Updateddatetime)
                .HasColumnType("smalldatetime")
                .HasColumnName("UPDATEDDATETIME");
            entity.Property(e => e.UserInactivated)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_INACTIVATED");
            entity.Property(e => e.UserStamp)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("USER_STAMP");
            entity.Property(e => e.VpoPortalUser)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("VPO_PORTAL_USER");
            entity.Property(e => e.WorkPhone)
                .HasMaxLength(14)
                .IsUnicode(false)
                .HasColumnName("WORK_PHONE");
        });

        modelBuilder.Entity<Variance>(entity =>
        {
            entity.HasKey(e => e.VarianceCode);

            entity.ToTable("VARIANCE");

            entity.Property(e => e.VarianceCode)
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("VARIANCE_CODE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DivId).HasColumnName("DIV_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.VarianceDesc)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("VARIANCE_DESC");
        });

        modelBuilder.Entity<Worksheet>(entity =>
        {
            entity.ToTable("WORKSHEET");

            entity.Property(e => e.WorksheetId).HasColumnName("WORKSHEET_ID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.DivId).HasColumnName("DIV_ID");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.IsTemporary)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("IS_TEMPORARY");
            entity.Property(e => e.LockedBy)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("LOCKED_BY");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.WorksheetCategory)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("WORKSHEET_CATEGORY");
            entity.Property(e => e.WorksheetCreatedBy)
                .HasMaxLength(38)
                .IsUnicode(false)
                .HasColumnName("WORKSHEET_CREATED_BY");
            entity.Property(e => e.WorksheetCreatedOn)
                .HasColumnType("smalldatetime")
                .HasColumnName("WORKSHEET_CREATED_ON");
            entity.Property(e => e.WorksheetDesc)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("WORKSHEET_DESC");
            entity.Property(e => e.WorksheetName)
                .HasMaxLength(70)
                .IsUnicode(false)
                .HasColumnName("WORKSHEET_NAME");
        });

        modelBuilder.Entity<WorksheetOpt>(entity =>
        {
            entity.ToTable("WORKSHEET_OPT");

            entity.HasIndex(e => new { e.IsActive, e.WorksheetPlanId }, "nci_wi_WORKSHEET_OPT_9841CCEA469C092B7F849D592310C82A");

            entity.Property(e => e.WorksheetOptId).HasColumnName("WORKSHEET_OPT_ID");
            entity.Property(e => e.Costprice).HasColumnName("COSTPRICE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.Marketvalue).HasColumnName("MARKETVALUE");
            entity.Property(e => e.Markup).HasColumnName("MARKUP");
            entity.Property(e => e.Markuppercent).HasColumnName("MARKUPPERCENT");
            entity.Property(e => e.Markuptype).HasColumnName("MARKUPTYPE");
            entity.Property(e => e.PlanOptionId).HasColumnName("PLAN_OPTION_ID");
            entity.Property(e => e.Pricedate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PRICEDATE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Sellprice).HasColumnName("SELLPRICE");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.WorksheetPlanId).HasColumnName("WORKSHEET_PLAN_ID");

            entity.HasOne(d => d.PlanOption).WithMany(p => p.WorksheetOpts)
                .HasForeignKey(d => d.PlanOptionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WORKSHEET_OPT_AVAILABLE_PLAN_OPTION");

            entity.HasOne(d => d.WorksheetPlan).WithMany(p => p.WorksheetOpts)
                .HasForeignKey(d => d.WorksheetPlanId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WORKSHEET_OPT_WORKSHEET_PLAN");
        });

        modelBuilder.Entity<WorksheetOptAct>(entity =>
        {
            entity.ToTable("WORKSHEET_OPT_ACT");

            entity.HasIndex(e => new { e.IsActive, e.WorksheetOptId }, "nci_wi_WORKSHEET_OPT_ACT_3E117078EDF1185D3532D7B5608DAE6C");

            entity.Property(e => e.WorksheetOptActId).HasColumnName("WORKSHEET_OPT_ACT_ID");
            entity.Property(e => e.Activity)
                .HasMaxLength(36)
                .IsUnicode(false)
                .HasColumnName("ACTIVITY");
            entity.Property(e => e.Costprice).HasColumnName("COSTPRICE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Errors).HasColumnName("ERRORS");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.Lumpsum)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("LUMPSUM");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.Warnings).HasColumnName("WARNINGS");
            entity.Property(e => e.WorksheetOptId).HasColumnName("WORKSHEET_OPT_ID");

            entity.HasOne(d => d.SubNumberNavigation).WithMany(p => p.WorksheetOptActs)
                .HasForeignKey(d => d.SubNumber)
                .HasConstraintName("FK_WORKSHEET_OPT_ACT_SUPPLIER");

            entity.HasOne(d => d.WorksheetOpt).WithMany(p => p.WorksheetOptActs)
                .HasForeignKey(d => d.WorksheetOptId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WORKSHEET_OPT_ACT_WORKSHEET_OPT");
        });

        modelBuilder.Entity<WorksheetPlan>(entity =>
        {
            entity.ToTable("WORKSHEET_PLAN");

            entity.Property(e => e.WorksheetPlanId).HasColumnName("WORKSHEET_PLAN_ID");
            entity.Property(e => e.Costprice).HasColumnName("COSTPRICE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.Marketvalue).HasColumnName("MARKETVALUE");
            entity.Property(e => e.Markup).HasColumnName("MARKUP");
            entity.Property(e => e.Markuppercent).HasColumnName("MARKUPPERCENT");
            entity.Property(e => e.Markuptype).HasColumnName("MARKUPTYPE");
            entity.Property(e => e.PhasePlanId).HasColumnName("PHASE_PLAN_ID");
            entity.Property(e => e.Pricedate)
                .HasColumnType("smalldatetime")
                .HasColumnName("PRICEDATE");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Sellprice).HasColumnName("SELLPRICE");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.WorksheetId).HasColumnName("WORKSHEET_ID");

            entity.HasOne(d => d.PhasePlan).WithMany(p => p.WorksheetPlans)
                .HasForeignKey(d => d.PhasePlanId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WORKSHEET_PLAN_PHASE_PLAN");

            entity.HasOne(d => d.Worksheet).WithMany(p => p.WorksheetPlans)
                .HasForeignKey(d => d.WorksheetId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WORKSHEET_PLAN_WORKSHEET");
        });

        modelBuilder.Entity<WorksheetPlanAct>(entity =>
        {
            entity.ToTable("WORKSHEET_PLAN_ACT");

            entity.Property(e => e.WorksheetPlanActId).HasColumnName("WORKSHEET_PLAN_ACT_ID");
            entity.Property(e => e.Activity)
                .HasMaxLength(36)
                .IsUnicode(false)
                .HasColumnName("ACTIVITY");
            entity.Property(e => e.Costprice).HasColumnName("COSTPRICE");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Errors).HasColumnName("ERRORS");
            entity.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.Lumpsum)
                .HasMaxLength(1)
                .IsUnicode(false)
                .HasColumnName("LUMPSUM");
            entity.Property(e => e.RecordTimeStamp)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SubNumber).HasColumnName("SUB_NUMBER");
            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedDateTime).HasColumnType("smalldatetime");
            entity.Property(e => e.Warnings).HasColumnName("WARNINGS");
            entity.Property(e => e.WorksheetPlanId).HasColumnName("WORKSHEET_PLAN_ID");

            entity.HasOne(d => d.SubNumberNavigation).WithMany(p => p.WorksheetPlanActs)
                .HasForeignKey(d => d.SubNumber)
                .HasConstraintName("FK_WORKSHEET_PLAN_ACT_SUPPLIER");

            entity.HasOne(d => d.WorksheetPlan).WithMany(p => p.WorksheetPlanActs)
                .HasForeignKey(d => d.WorksheetPlanId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WORKSHEET_PLAN_ACT_WORKSHEET_PLAN");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
