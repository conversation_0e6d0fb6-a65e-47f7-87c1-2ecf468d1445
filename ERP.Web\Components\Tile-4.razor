﻿@inject BudgetService BudgetService

<div class="card-body">
    <div class="row d-flex justify-content-center">
        <div class="col">
            <p class="text-dark mb-0 fw-semibold">Issued Budget</p>
            <h3 class="m-0">@TotalIssuedBudget</h3>
            <p class="mb-0 text-truncate text-muted">
                <span class="text-success">
                    Last Month
                </span>
            </p>
        </div>
        <div class="col-auto align-self-center">
            <div class="report-main-icon bg-light-alt">
                <img width="64" height="64" src="https://img.icons8.com/dusk/64/accounting.png" alt="estimate" class="align-self-center text-muted icon-sm" />
            </div>
        </div>
    </div>
</div>

@code {
    public string? TotalIssuedBudget { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        var totalIssuedBudget = await BudgetService.GetIssuedBudget();

        if (totalIssuedBudget != null)
        {
            if (totalIssuedBudget.Value != null)
            {
                TotalIssuedBudget = totalIssuedBudget.Value.TotalIssuedBudget;
            }
        }
    }
}
