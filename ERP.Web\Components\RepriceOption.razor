﻿@using ERP.Data.Models
@inject SalesPriceService SalesPriceService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <h4 class="page-title">Reprice Option</h4>
    </WindowTitle>
    <WindowContent>
        <p>Reprice Option: @RepriceOption1.OptionCode - @RepriceOption1.OptionName</p>
        <EditForm Model="@WorksheetOptsToReprice" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />            
            <label class="form-label">Reprice Date</label>
            <TelerikDatePicker @bind-Value="@WorksheetOptsToReprice.RepriceDate"></TelerikDatePicker>
            <br />
            <div class="customSeparator">
                <button type="submit" class="btn btn-primary">Reprice</button>
                <button type="button" @onclick="CancelReprice" class="btn btn-secondary">Cancel</button>
                <div style=@ShowLoading>Submitting. Please wait...</div>
                <div style=@ShowError>Something went wrong.</div>
            </div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public RepriceWorksheetOptsModel WorksheetOptsToReprice { get; set; } = new RepriceWorksheetOptsModel(){WorksheetOptIds = new List<int>()};
    public string ShowLoading{ get; set; } = "display:none";
    public string ShowError { get; set; } = "display:none";


    [Parameter]
    public WorksheetOptModel? RepriceOption1 { get; set; }

    [Parameter]
    public EventCallback<ResponseModel<RepriceWorksheetOptsModel>> HandleAddSubmit { get; set; }

    public void Show()
    {
        IsModalVisible = true;
    }      

    private async void HandleValidAddSubmit()
    {
        ShowLoading = "";
        WorksheetOptsToReprice.RepriceDate = WorksheetOptsToReprice.RepriceDate ?? DateTime.Now; //Fill in current date as default
        var response = new ResponseModel<RepriceWorksheetOptsModel>();
        if (RepriceOption1.WorksheetOptId != 1)
        {
            response = await SalesPriceService.RepriceWorksheetOptsAsync(WorksheetOptsToReprice);
        }
        else
        {
            //base hosue
            var plansToReprice = new RepriceWorksheetPlansModel()
                {
                    WorksheetPlanIds = new List<int>() { RepriceOption1.WorksheetPlanId },
                    RepriceDate = WorksheetOptsToReprice.RepriceDate
                };
            var responsePlan = await SalesPriceService.RepriceWorksheetPlansAsync(plansToReprice);
            response.IsSuccess = responsePlan.IsSuccess;
            response.Message = responsePlan.Message;
        }
        
        ShowLoading = "display:none";       
        await HandleAddSubmit.InvokeAsync(response);
    }
    protected override async Task OnParametersSetAsync()
    {
        WorksheetOptsToReprice.WorksheetOptIds = new List<int>() { RepriceOption1.WorksheetOptId };//TODO: fix to show option name or base hosue
    }
    async void CancelReprice()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;        
    }
}
