﻿@page "/configuremasteroptionstile"
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject NavigationManager NavManager
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling")]
@using ERP.Data.Models.Dto;
@using Telerik.DataSource
@using Telerik.DataSource.Extensions

<div class="container-fluid flex">
    <div class="row d-flex">
        <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.8rem; color:#fff">Manage Master Options</h7>
                </div>
            </div>
        </div>
        <TelerikButton Icon=FontIcon.X Title="Delete tile" Class="k-flat" OnClick="@(() => Add())">Show Panel 1</TelerikButton>
        <TelerikTileLayout Columns="5"
                           ColumnWidth="300px"
                           RowHeight="500px"
                           Reorderable="true"
                           Resizable="true"
                           OnResize="@ItemResize">
            <TileLayoutItems>

                    @if(Panel1IsVisible == true)
                    {
                        <TileLayoutItem HeaderText="One">
                            <Content>
                                <h4>Option Group</h4>
                                @if (MasterOptionGroupData == null)
                                {
                                    <p><em>Loading...</em></p>
                                    <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingGroup />
                                }

                                else
                                {
                                    <TelerikButton Icon=FontIcon.X Title="Delete tile" Class="k-flat" OnClick="@(() => Remove())"></TelerikButton>
                                    <TelerikGrid Data=@MasterOptionGroupData
                                                 FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                                 Height="1000px" RowHeight="60" PageSize="20"
                                                 Size="@ThemeConstants.Grid.Size.Medium"
                                                 Sortable="true"
                                                 Resizable="true"
                                                 Groupable="false"
                                                 OnRowClick="@OnOptionGroupRowClickHandler"
                                                 SelectionMode="GridSelectionMode.Single"
                                                 EditMode="@GridEditMode.Popup"
                                                 OnUpdate="@UpdateGroupHandler"
                                                 OnEdit="@EditGroupHandler"
                                                 OnDelete="@DeleteGroupHandler"
                                                 OnCreate="@CreateGroupHandler"
                                                 OnCancel="@CancelGroupHandler"
                                                 ConfirmDelete="true"
                                    @ref="@GroupGridRef">
                                        <GridToolBarTemplate>
                                            <GridCommandButton Command="Add" Icon="@FontIcon.Plus">Add Group</GridCommandButton>
                                            <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                        </GridToolBarTemplate>
                                        <GridColumns>
                                            <GridColumn Field="OptionGroupName" Title="Option Group" Editable="true" Groupable="false" />
                                            <GridColumn Field="OptionGroupLetter" Title="Option Group Letter" Editable="true" Groupable="false" Width="0" />
                                            <GridColumn Field="DeftMarkupType" Title="Default Markup Type" Editable="true" Groupable="false" Width="0" />
                                            <GridColumn Field="DeftMarkupAmount" Title="Default Markup Amount" Editable="true" Groupable="false" Width="0" />
                                            <GridColumn Field="OptionGroupId" Visible="false" Editable="false" Groupable="false" />
                                            <GridCommandColumn>
                                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil"></GridCommandButton>
                                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash"></GridCommandButton>
                                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true">Cancel</GridCommandButton>
                                            </GridCommandColumn>
                                        </GridColumns>
                                        <DetailTemplate>
                                            @{
                                                var item = context as OptionGroupDto;
                                                <p>Description: @item.OptionGroupName</p>
                                                <p>Letter: @item.OptionGroupLetter</p>
                                                <p>Markup Type: @item.DeftMarkupType</p>
                                                <p>Markup Amount: @item.DeftMarkupAmount</p>
                                            }
                                        </DetailTemplate>
                                        <GridSettings>
                                            <GridPopupEditSettings Width="400px"
                                                                   Height="500px"
                                                                   Title="Option Group">
                                            </GridPopupEditSettings>
                                        </GridSettings>
                                        <NoDataTemplate>
                                            <p>@Message</p>
                                        </NoDataTemplate>
                                    </TelerikGrid>

                                }
                            </Content>
                        </TileLayoutItem>
                    }

               
                <TileLayoutItem HeaderText="Two">
                    <Content>
                        <h4>Options for Group: @SelectedGroup?.OptionGroupName</h4>
                        @if (MasterOptionData == null)
                        {
                            <p><em>Select a group to see options</em></p>
                            <div style=@optionLoadingStyle>Loading...</div>
                            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingOption />
                        }
                        else
                        {
                            <TelerikGrid Data="@MasterOptionData"
                                     FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                     Height="1000px" RowHeight="60"
                                     Pageable="true" PageSize="20"
                                     SelectionMode="GridSelectionMode.Single"
                                     EditMode="@GridEditMode.Popup"
                                     Sortable="true"
                                     Resizable="true"
                                     Groupable="false"
                                     OnRowClick="@OnOptionRowClickHandler"
                                     OnUpdate="@UpdateOptionHandler"
                                     OnEdit="@EditOptionHandler"
                                     OnDelete="@DeleteOptionHandler"
                                     OnCreate="@CreateOptionHandler"
                                     OnCancel="@CancelOptionHandler"
                                     ConfirmDelete="true"
                                     @ref="@OptionGridRef">
                                <GridColumns>
                                    <GridColumn Field="OptionDesc" Title="Option Description" Editable="true" Groupable="false" />
                                    <GridColumn Field="OptionCode" Title="Option Code" Editable="true" Groupable="false" Width="0" />
                                    <GridColumn Field="OptionGroupId" Editable="false" Groupable="false" Visible="false" />
                                    <GridColumn Field="AsmHeaderId" Visible="false" Editable="false" Groupable="false" />
                                    <GridColumn Field="BoolIsElevation" Title="Elevation" Editable="true" EditorType="@GridEditorType.CheckBox" Groupable="false" Width="0" />
                                    <GridColumn Field="BoolIsBaseHouse" Title="Base House" Editable="true" EditorType="@GridEditorType.CheckBox" Groupable="false" Width="0" />
                                    <GridColumn Field="Notes" Editable="true" Groupable="false" EditorType="@GridEditorType.TextArea" Width="0" />
                                    <GridCommandColumn>
                                        <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                        <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil"></GridCommandButton>
                                        <GridCommandButton Command="Delete" Icon="@FontIcon.Trash"></GridCommandButton>
                                        <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true">Cancel</GridCommandButton>
                                    </GridCommandColumn>
                                </GridColumns>
                                <DetailTemplate>
                                    @{
                                        var item = context as MasterOptionHeaderModel;
                                        <p>Option Code: @item.OptionCode</p>
                                        <p>Description: @item.OptionDesc</p>
                                        <p>Option Size: @item.OptionSize</p>
                                        <p>Unit: @item.UnitQty</p>
                                        <p>Elevation: @item.IsElevation</p>
                                        <p>Base House: @item.IsBaseHouse</p>
                                        <p>Notes: @item.Notes</p>
                                    }
                                </DetailTemplate>
                                <GridToolBarTemplate>
                                    <GridCommandButton Command="Add" Icon="@FontIcon.Plus">Add Option</GridCommandButton>
                                    <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                </GridToolBarTemplate>
                                <GridSettings>
                                    <GridPopupEditSettings Width="400px"
                                                       Height="400px"
                                                       Title="Option">
                                    </GridPopupEditSettings>
                                </GridSettings>
                                <NoDataTemplate>
                                    <p>@Message</p>
                                </NoDataTemplate>
                            </TelerikGrid>

                        }
                    </Content>
                </TileLayoutItem>
                <TileLayoutItem HeaderText="Three">
                    <Content>
                        <h4>Items for Option: @SelectedOption?.OptionDesc</h4>
                        @if (MasterItemData == null)
                        {
                            <p><em>Select an option to see items</em></p>
                            <div style=@itemLoadingStyle>Loading...</div>
                            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingItem />
                        }
                        else
                        {
                            <TelerikGrid Data=@MasterItemData
                                     FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                     Height="1000px" RowHeight="60" PageSize="20"
                                     Sortable="true"
                                     Resizable="true"
                                     Groupable="false"
                                     Size="@ThemeConstants.Grid.Size.Small"
                                     SelectionMode="GridSelectionMode.Single"
                                     EditMode="@GridEditMode.Popup"
                                     OnUpdate="@UpdateItemHandler"
                                     OnEdit="@EditItemHandler"
                                     OnDelete="@DeleteItemHandler"
                                     OnCreate="@CreateItemHandler"
                                     OnCancel="@CancelItemHandler"
                                     ConfirmDelete="true"
                                     @ref="@ItemGridRef">
                                <GridColumns>
                                    <GridColumn Field="ItemDesc" Title="Item" Editable="true" Groupable="false" />
                                    <GridColumn Field="ItemNumber" Title="Item Code" Editable="true" Groupable="false" Width="0" />
                                    <GridColumn Field="Factor" Title="Quantity" Editable="true" Groupable="false" Width="0" />
                                    <GridColumn Field="TakeoffUnit" Title="Takeoff Unit" Editable="true" Groupable="false" Width="0">
                                        <EditorTemplate>
                                            @{
                                                SelectedItem = context as ModelManagerItemModel;
                                                <TelerikDropDownList @bind-Value="@SelectedItem.TakeoffUnit"
                                                             Data="@TakeoffUnitOptions"
                                                             Width="100%">
                                                </TelerikDropDownList>
                                            }
                                        </EditorTemplate>
                                    </GridColumn>
                                    <GridColumn Field="ItemNotes" Title="Item Notes" Editable="true" EditorType="@GridEditorType.TextArea" Groupable="false" Width="0" />
                                    <GridColumn Field="MasterItemId" Visible="false" Editable="false" Groupable="false" />
                                    <GridCommandColumn>
                                        <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                        <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil"></GridCommandButton>
                                        <GridCommandButton Command="Delete" Icon="@FontIcon.Trash"></GridCommandButton>
                                        <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true">Cancel</GridCommandButton>
                                    </GridCommandColumn>
                                </GridColumns>
                                <DetailTemplate>
                                    @{
                                        var item = context as ModelManagerItemModel;
                                        <p>Description: @item.ItemDesc</p>
                                        <p>Code: @item.ItemNumber</p>
                                        <p>Purchasing Activity: @item.BomClassName</p>
                                        <p>Takeoff Unit: @item.TakeoffUnit</p>
                                        <p>Quantity: @item.Factor</p>
                                        <p>Notes: @item.ItemNotes</p>
                                    }
                                </DetailTemplate>
                                <GridToolBarTemplate>
                                    <GridCommandButton Command="MyToolbarCommand" Icon="@FontIcon.Plus" OnClick="@MyCommandFromToolbar">Add Item</GridCommandButton>
                                    <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                </GridToolBarTemplate>
                                <GridSettings>
                                    <GridPopupEditSettings Width="400px"
                                                       Height="400px"
                                                       Title="Item">
                                    </GridPopupEditSettings>
                                </GridSettings>
                                <NoDataTemplate>
                                    <p>@Message</p>
                                </NoDataTemplate>
                            </TelerikGrid>
                        }
                    </Content>
                </TileLayoutItem>
            </TileLayoutItems>
        </TelerikTileLayout>
        
    </div>
</div>

@code {
    private TelerikGrid<OptionGroupDto>? GroupGridRef { get; set; }
    private TelerikGrid<MasterOptionHeaderModel>? OptionGridRef { get; set; }
    private TelerikGrid<ModelManagerItemModel>? ItemGridRef { get; set; }
    public List<OptionGroupDto>? MasterOptionGroupData { get; set; }
    public List<MasterOptionHeaderModel>? MasterOptionData { get; set; }
    public List<ModelManagerItemModel>? MasterItemData { get; set; }

    public MasterOptionHeaderModel SelectedOption { get; set; }
    public OptionGroupDto SelectedGroup { get; set; }
    public ModelManagerItemModel SelectedItem { get; set; }
    public List<string> TakeoffUnitOptions = new List<string> { "LS", "EA", "LF", "SF", "TON", "UKN" };
    protected ERP.Web.Components.AddItemToOption? AddItemModal { get; set; }
    public int SelectedMasterPlanId { get; set; } = 0;
    private string optionLoadingStyle = "display:none";
    private string itemLoadingStyle = "display:none";
    public bool IsLoadingGroup { get; set; } = false;
    public bool IsLoadingOption { get; set; } = false;
    public bool IsLoadingItem { get; set; } = false;

    public string? Message { get; set; } = "No data to display";
    public bool? ShowError;
    public bool? Panel1IsVisible { get; set; } = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        IsLoadingGroup = true;
        var getGroups = await OptionService.GetOptionGroupsAsync();
        ShowError = getGroups.IsSuccess;
        Message = getGroups.Message;
        MasterOptionGroupData = getGroups.Value;
        IsLoadingGroup = false;
    }
    private void Remove()
    {
        Panel1IsVisible = false;
    }
    private void Add()
    {
        Panel1IsVisible = true;
    }
    protected async Task OnOptionGroupRowClickHandler(GridRowClickEventArgs args)
    {
        IsLoadingOption = true;
        MasterOptionData = null;
        optionLoadingStyle = "";
        SelectedGroup = args.Item as OptionGroupDto;
        MasterOptionData = (await OptionService.GetMasterOptionsByGroupAsync(SelectedGroup.OptionGroupId)).Value;
        MasterItemData = null;//reset the master item data else it shows items from a previously selected trade
        optionLoadingStyle = "display:none";
        IsLoadingOption = false;
    }

    protected async Task OnOptionRowClickHandler(GridRowClickEventArgs args)
    {
        IsLoadingItem = true;
        MasterItemData = null;
        itemLoadingStyle = "";
        SelectedOption = args.Item as MasterOptionHeaderModel;
        MasterItemData = (await ItemService.GetItemsInMasterOptionAsync(SelectedOption.OptionId)).Value;//OptionId here is masteroptionid, not asmheader id
        itemLoadingStyle = "display:none";
        IsLoadingItem = false;
    }

    void EditGroupHandler(GridCommandEventArgs args)
    {
        OptionGroupDto item = (OptionGroupDto)args.Item;
        Console.WriteLine("Edit event is fired.");
    }

    async Task UpdateGroupHandler(GridCommandEventArgs args)
    {
        OptionGroupDto item = (OptionGroupDto)args.Item;
        await OptionService.UpdateOptionGroupAsync(item);
        var getGroups = await OptionService.GetOptionGroupsAsync();
        ShowError = getGroups.IsSuccess;
        Message = getGroups.Message;
        MasterOptionGroupData = getGroups.Value;
    }

    async Task DeleteGroupHandler(GridCommandEventArgs args)
    {
        OptionGroupDto item = (OptionGroupDto)args.Item;
        await OptionService.DeleteOptionGroupAsync(item);
        var getGroups = await OptionService.GetOptionGroupsAsync();
        ShowError = getGroups.IsSuccess;
        Message = getGroups.Message;
        MasterOptionGroupData = getGroups.Value;
    }

    async Task CreateGroupHandler(GridCommandEventArgs args)
    {
        OptionGroupDto group = (OptionGroupDto)args.Item;
        await OptionService.AddOptionGroupAsync(group);
        var getGroups = await OptionService.GetOptionGroupsAsync();
        ShowError = getGroups.IsSuccess;
        Message = getGroups.Message;
        MasterOptionGroupData = getGroups.Value;
    }

    async Task CancelGroupHandler(GridCommandEventArgs args)
    {
        OptionGroupDto item = (OptionGroupDto)args.Item;

        // if necessary, perform actual data source operation here through your service

        Console.WriteLine("Cancel event is fired.");
    }

    void EditOptionHandler(GridCommandEventArgs args)
    {
        MasterOptionHeaderModel item = (MasterOptionHeaderModel)args.Item;
    }

    async Task UpdateOptionHandler(GridCommandEventArgs args)
    {
        MasterOptionHeaderModel item = (MasterOptionHeaderModel)args.Item;
        await OptionService.UpdateMasterOptionAsync(item);
        MasterOptionData = (await OptionService.GetMasterOptionsByGroupAsync(SelectedGroup.OptionGroupId)).Value;
    }

    async Task DeleteOptionHandler(GridCommandEventArgs args)
    {
        MasterOptionHeaderModel item = (MasterOptionHeaderModel)args.Item;
        MasterOptionDto optionToUpdate = new MasterOptionDto()
            {
                OptionId = item.OptionId,
                OptionDesc = item.OptionDesc,
                OptionCode = item.OptionCode,
                OptionGroupId = SelectedGroup.OptionGroupId
            };
        await OptionService.DeleteMasterOptionAsync(optionToUpdate);
        MasterOptionData = (await OptionService.GetMasterOptionsByGroupAsync(SelectedGroup.OptionGroupId)).Value;
    }

    async Task CreateOptionHandler(GridCommandEventArgs args)
    {
        MasterOptionHeaderModel option = (MasterOptionHeaderModel)args.Item;
        option.OptionGroupId = SelectedGroup.OptionGroupId;
        await OptionService.AddMasterOptionAsync(option);
        MasterOptionData = (await OptionService.GetMasterOptionsByGroupAsync(SelectedGroup.OptionGroupId)).Value;
    }

    async Task CancelOptionHandler(GridCommandEventArgs args)
    {
        MasterOptionHeaderModel item = (MasterOptionHeaderModel)args.Item;

        // if necessary, perform actual data source operation here through your service

        Console.WriteLine("Cancel event is fired.");
    }

    void EditItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;

        // prevent opening for edit based on condition
        if (item.MasterItemId < 3)
        {
            args.IsCancelled = true;// the general approach for cancelling an event
        }

        Console.WriteLine("Edit event is fired.");
    }

    async Task UpdateItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
        await ItemService.UpdateItemAsync(item);
        MasterItemData = (await ItemService.GetItemsInMasterOptionAsync(SelectedOption.OptionId)).Value;//OptionId here is masteroptionid, not asmheader id

    }

    async Task DeleteItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
        await ItemService.DeleteMasterItemAsync(item.MasterItemId);
        MasterItemData = (await ItemService.GetItemsInMasterOptionAsync(SelectedOption.OptionId)).Value;//OptionId here is masteroptionid, not asmheader id
    }

    async Task CreateItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
    }

    async Task CancelItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;

        // if necessary, perform actual data source operation here through your service

        Console.WriteLine("Cancel event is fired.");
    }

    private void MyCommandFromToolbar(GridCommandEventArgs args)
    {
        //note - the args.Item object is null because the command item is not associated with an item
        var headerId = SelectedOption.AsmHeaderId;
        AddItemModal.Show();
        StateHasChanged();
    }

    private async void HandleValidAddItemSubmit(ResponseModel responseItem)
    {
        MasterItemData = (await ItemService.GetItemsInMasterOptionAsync(SelectedOption.OptionId)).Value;//OptionId here is masteroptionid, not asmheader id
        AddItemModal.Hide();
        StateHasChanged();
    }

    void ItemResize()
    {
        //ConversionsRef.Refresh();
        //VisitorsRef.Refresh();
        //MostVisitedPagesRef.Refresh();
        //PageViewsRef.Refresh();
    }
}
