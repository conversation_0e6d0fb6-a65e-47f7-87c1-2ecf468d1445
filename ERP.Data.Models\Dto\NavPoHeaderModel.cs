﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class PODataList
    {
        public PurchaseHeaderList? PurchaseHeaderList { get; set; }
        public PurchaseLineList? PurchaseLineList { get; set; }
    }
    public class PurchaseHeaderList
    {
        public int? TableID { get; set; }
        public string? PackageCode { get; set; }
        public List<PurchaseHeader>? PurchaseHeaders { get; set; }
    }
    public class PurchaseHeader
    {
       // public string? id { get; set; }
        public string? No { get; set; }
        public DateTime? PostingDate { get; set; }
        public string? VendorOrderNo { get; set; }
        //public string orderDate { get; set; }
        //public string postingDate { get; set; }
        //public string dueDate { get; set; }
        //public string vendorId { get; set; }
       // public string? apCategoryCode { get; set; }
        public string? DocumentType { get; set; }
        public string? PostingDescription { get; set; }
        public string? BuyFromVendorNo { get; set; }
        public string? VendorInvoiceNo { get; set; }   
        public string? ShortcutDimension1Code { get; set; }//subdivision
        public string? ShortcutDimension2Code { get; set; }//entity
        public string? APCategoryCode { get; set; }
        //public string payToVendorId { get; set; }
        //public string payToVendorNumber { get; set; }
        //public string shipToName { get; set; }
        //public string shipToContact { get; set; }
        //public string buyFromAddressLine1 { get; set; }
        //public string buyFromAddressLine2 { get; set; }
        //public string buyFromCity { get; set; }
        //public string buyFromCountry { get; set; }
        //public string buyFromState { get; set; }
        //public string buyFromPostCode { get; set; }
        //public string shipToAddressLine1 { get; set; }
        //public string shipToAddressLine2 { get; set; }
        //public string shipToCity { get; set; }
        //public string shipToCountry { get; set; }
        //public string shipToState { get; set; }
        //public string shipToPostCode { get; set; }
        //public string currencyId { get; set; }
        //public string currencyCode { get; set; }
        //public bool pricesIncludeTax { get; set; }
        //public string paymentTermsId { get; set; }
        //public string shipmentMethodId { get; set; }
        //public string purchaser { get; set; }
        //public string requestedReceiptDate { get; set; }
        //public int discountAmount { get; set; }
        //public List<BCPoDetail>? purchaseOrderLines { get; set; }
        //public List<DimensionSetLines>? dimensionSetLines { get; set; }
    }
   

}
