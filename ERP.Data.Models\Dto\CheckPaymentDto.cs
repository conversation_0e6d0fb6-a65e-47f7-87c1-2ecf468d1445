﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class CheckPaymentDto : IMapFrom<CheckPayment>
{
    public int CheckPaymentsId { get; set; }

    public string? SupplierShortName { get; set; }//for BC integration, doesn't have subnumber
    public string? BankAccount { get; set; }

    public string? CheckNumber { get; set; }

    public DateTime? CheckDate { get; set; }

    public double? CheckAmount { get; set; }

    public int SubNumber { get; set; }

    public string? InvNumber { get; set; }

    public double? InvAmountPaid { get; set; }

    public int PoheaderId { get; set; }
    public string? PoNumber { get; set; }//for BC integration doesn't have poheaderId

    public double? PoAmountPaid { get; set; }

    public int? PaymentType { get; set; }

    public string? VoidedPayment { get; set; }

    public double? VoidedAmount { get; set; }

    public DateTime? VoidedDate { get; set; }

    public int? AcntDbId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? FoundDuringUpdate { get; set; }

    public int? PaymentId { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public PoheaderDto?  Poheader { get; set; } 

    public SupplierDto?  SubNumberNavigation { get; set; }
   // public List<PoheaderDto>? Invoices { get; set; } //for BC to send multiple invoices being paid with one check
    public void Mapping(Profile profile)
    {
        profile.CreateMap<CheckPaymentDto, CheckPayment>().ReverseMap();
    }
}

//To return data to BC, doesn't include poheader navigation etc
public class ReturnCheckPaymentDto : IMapFrom<CheckPayment>
{
    public int CheckPaymentsId { get; set; }

    public string? SupplierShortName { get; set; }//for BC integration, doesn't have subnumber
    public string? BankAccount { get; set; }

    public string? CheckNumber { get; set; }

    public DateTime? CheckDate { get; set; }

    public double? CheckAmount { get; set; }

    public int SubNumber { get; set; }

    public string? InvNumber { get; set; }

    public double? InvAmountPaid { get; set; }

    public int PoheaderId { get; set; }
    public string? PoNumber { get; set; }//for BC integration doesn't have poheaderId

    public double? PoAmountPaid { get; set; }

    public int? PaymentType { get; set; }

    public string? VoidedPayment { get; set; }

    public double? VoidedAmount { get; set; }

    public DateTime? VoidedDate { get; set; }

    public int? AcntDbId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? FoundDuringUpdate { get; set; }

    public int? PaymentId { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }


    public void Mapping(Profile profile)
    {
        profile.CreateMap<ReturnCheckPaymentDto, CheckPayment>().ReverseMap();
    }
}