﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class TbClient
{
    public int Id { get; set; }

    public int? UserId { get; set; }

    public int? LoanId { get; set; }

    public int? SubdivisionId { get; set; }

    public int? LotId { get; set; }

    public string? JobNumber { get; set; }

    public int? PlanId { get; set; }

    public int? GarageOrientationId { get; set; }

    public int? ColorSchemeId { get; set; }

    public string? HouseBase { get; set; }

    public string? TrimMasonry { get; set; }

    public string? RoofTile { get; set; }

    public DateTime? VisitDate { get; set; }

    public string? Salutation { get; set; }

    public DateTime? LastContact { get; set; }

    public bool? Contingency { get; set; }

    public string? ContingencyAddress1 { get; set; }

    public string? ContingencyAddress2 { get; set; }

    public string? ContingencyCity { get; set; }

    public string? ContingencyState { get; set; }

    public string? ContingencyCountry { get; set; }

    public string? ContingencyZip { get; set; }

    public string? Notes { get; set; }

    public int? Demo1Id { get; set; }

    public string? Demo1Txt { get; set; }

    public int? Demo2Id { get; set; }

    public string Demo2Txt { get; set; } = null!;

    public int? Demo3Id { get; set; }

    public int? Demo4Id { get; set; }

    public int? Demo5Id { get; set; }

    public int? Demo6Id { get; set; }

    public int? Demo7Id { get; set; }

    public int? Demo8Id { get; set; }

    public int? Demo9Id { get; set; }

    public int? Demo10Id { get; set; }

    public int? Demo11Id { get; set; }

    public int? Demo12Id { get; set; }

    public int? Demo13Id { get; set; }

    public int? Demo14Id { get; set; }

    public int? Demo15Id { get; set; }

    public int? Demo16Id { get; set; }

    public int? Demo17Id { get; set; }

    public int? Demo18Id { get; set; }

    public int? Demo19Id { get; set; }

    public int? Demo20Id { get; set; }

    public int? ClientRankId { get; set; }

    public int? ClientStatusId { get; set; }

    public int? OwnershipTitleId { get; set; }

    public int? VestingId { get; set; }

    public DateTime? ExpirationDate { get; set; }

    public short? ContactBefore { get; set; }

    public string? ContingencyCode { get; set; }

    public DateTime? ContractDate { get; set; }

    public DateTime? ApprovalDate { get; set; }

    public string? ApprovedBy { get; set; }

    public DateTime? EstCloseDate { get; set; }

    public DateTime? ActualCloseDate { get; set; }

    public decimal? DueAtClose { get; set; }

    public short? AddendumNum { get; set; }

    public decimal? InterestRate { get; set; }

    public short? LoanTerm { get; set; }

    public decimal? FundingFee { get; set; }

    public decimal? Buydown { get; set; }

    public decimal? MaxLoanAmt { get; set; }

    public decimal? DownPayment { get; set; }

    public bool? PrequalifiedFlag { get; set; }

    public decimal? PrequalifiedAmount { get; set; }

    public decimal? MonthlyRent { get; set; }

    public decimal? RentersInsur { get; set; }

    public decimal? RentAppreciation { get; set; }

    public decimal? LoanAmount { get; set; }

    public decimal? UpfrontCosts { get; set; }

    public decimal? MortgageRate { get; set; }

    public short? RvBloanTerm { get; set; }

    public decimal? AnnualAppreciation { get; set; }

    public decimal? IncomeTaxRate { get; set; }

    public decimal? AnnualPropertyTax { get; set; }

    public decimal? HomeownersInsur { get; set; }

    public decimal? MaintenanceCosts { get; set; }

    public decimal? InterestUpfrontCosts { get; set; }

    public decimal? CondoFees { get; set; }

    public short? HomeOwnershipYrs { get; set; }

    public decimal? SellingCosts { get; set; }

    public decimal? BasePrice { get; set; }

    public decimal? BasePriceChange { get; set; }

    public DateTime? ReserveDate { get; set; }

    public decimal? ReserveAmount { get; set; }

    public decimal? LotPremiumChange { get; set; }

    public decimal? PriceIncentive { get; set; }

    public decimal? OtherIncentives { get; set; }

    public decimal? OptionIncentives { get; set; }

    public decimal? LotPremiumIncentive { get; set; }

    public string? CancelReason { get; set; }

    public int? CancelReasonId { get; set; }

    public DateTime? CancellationDate { get; set; }

    public DateTime? ChangeOrderDate { get; set; }

    public string? FirstUse { get; set; }

    public int? LastUser { get; set; }

    public DateTime? LastChanged { get; set; }

    public bool? RanSpinner { get; set; }

    public string? ContractNotes { get; set; }

    public string? LoanNotes { get; set; }

    public int? LeadSource { get; set; }

    public string? ExternalStatus { get; set; }

    public string? FastrepeatBuyer { get; set; }

    public decimal? PlanCost { get; set; }

    public bool? PrimaryResidence { get; set; }

    public decimal? Vattax { get; set; }

    public decimal? Vatrebate { get; set; }

    public int? Demo21Id { get; set; }

    public int? Demo22Id { get; set; }

    public int? Demo23Id { get; set; }

    public int? Demo24Id { get; set; }

    public int? Demo25Id { get; set; }

    public int? Demo26Id { get; set; }

    public int? Demo27Id { get; set; }

    public int? Demo28Id { get; set; }

    public int? Demo29Id { get; set; }

    public int? Demo30Id { get; set; }

    public int? Demo31Id { get; set; }

    public int? Demo32Id { get; set; }

    public int? Demo33Id { get; set; }

    public int? Demo34Id { get; set; }

    public int? Demo35Id { get; set; }

    public int? Demo36Id { get; set; }

    public int? Demo37Id { get; set; }

    public int? Demo38Id { get; set; }

    public string? Demo39Txt { get; set; }

    public string? Demo40Txt { get; set; }

    public string? Demo41Txt { get; set; }

    public string? Demo42Txt { get; set; }

    public string? Demo43Txt { get; set; }

    public string? Demo44Txt { get; set; }

    public string? Demo45Txt { get; set; }

    public string? Demo46Txt { get; set; }

    public string? Demo47Txt { get; set; }

    public string? Demo48Txt { get; set; }

    public string? Demo49Txt { get; set; }

    public string? Demo50Txt { get; set; }

    public string? Demo51Txt { get; set; }

    public string? Demo52Txt { get; set; }

    public string? Demo53Txt { get; set; }

    public string? Demo54Txt { get; set; }

    public string? Demo55Txt { get; set; }

    public string? Demo56Txt { get; set; }

    public DateTime? OriginalReserveDate { get; set; }

    public DateTime? ReserveExpireDate { get; set; }

    public bool? ReserveExpireWorkflowSent { get; set; }

    public bool? Lockdown { get; set; }

    public DateTime? ContingencyEstCloseDate { get; set; }

    public DateTime? ContingencyActualCloseDate { get; set; }

    public int? ContingencyStateId { get; set; }

    public int? ContingencyCountryId { get; set; }

    public string? CustPortalGuid { get; set; }

    public DateTime? CustPortalTimeOut { get; set; }

    public bool? TempCancellation { get; set; }

    public DateTime? CreateDate { get; set; }

    public DateTime? WwhideUntilUtcdate { get; set; }

    public DateTime? OrigContractDate { get; set; }

    public DateTime? InformalPrintDate { get; set; }

    public DateTime? CancellationApproveDate { get; set; }

    public DateTime? EstMoveOutDate { get; set; }

    public DateTime? WalkInDate { get; set; }

    public string? UserDefinedPreplot1 { get; set; }

    public string? UserDefinedPreplot2 { get; set; }

    public string? UserDefinedPreplot3 { get; set; }

    public bool? HasClientAttachment { get; set; }

    public bool? HasContractAttachment { get; set; }

    public bool? LotToSpec { get; set; }

    public bool? LotTransfer { get; set; }

    public int? ShippingCodeId { get; set; }

    public DateTime Createddatetime { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? Dwuserid { get; set; }

    public bool? IsActive { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public int? IndividualId { get; set; }

    public DateTime? SentDate { get; set; }

    public bool? IsGarageReversed { get; set; }

    public string? Name { get; set; }

    public virtual ICollection<TbBuiltOption> TbBuiltOptions { get; set; } = new List<TbBuiltOption>();

    public virtual ICollection<TbMember> TbMembers { get; set; } = new List<TbMember>();
}
