{"profiles": {"ERP.Web": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "TELERIK_API_KEY": "cJuvrRuRfUOFiNN9jgulIQ==.JnGQMaXS9QBUPg9AoFghE8zVJ+qUQUkaExyB/wTMMgv3Jb9p71tToWOe4zlii6VIwiiG+/xN/cEgL8ReoHU9sw=="}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7218;http://localhost:5051"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}, "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:38009", "sslPort": 44372}}}