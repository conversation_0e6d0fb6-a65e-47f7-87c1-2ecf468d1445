﻿@page "/configuremasteroptions"
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager NavManager
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, Accounting")]
@using ERP.Data.Models.Dto;
@using Telerik.DataSource
@using Telerik.DataSource.Extensions
@inject PlanService PlanService

<style type="text/css">
    /* Gutter */
    .row > * {
        padding-right: calc(var(--bs-gutter-x) * .10);
        padding-left: calc(var(--bs-gutter-x) * .10);
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto",sans-serif;
        font-size: .75rem;
    }

    .k-form .k-form-label, .k-form .k-form-field-wrap {
        display: inline;
    }
</style>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<PageTitle>Manage Master Options</PageTitle>
<div class="container-fluid flex">
    <div class="row d-flex">
        <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Manage Master Options</h7>
                </div>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item"><a href="/configuremasterplans">Plans</a></li>
            <li class="breadcrumb-item active">Manage Options</li>
        </ol>

        <div class="col-lg-3">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Option Group</h7>
                </div>
            </div>
            @if (MasterOptionGroupData == null)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingGroup />
            }

            else
            {
                <TelerikGrid Data=@MasterOptionGroupData
                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                Height="1000px" RowHeight="60" PageSize="20"
                Size="@ThemeConstants.Grid.Size.Small"
                Sortable="true"
                Resizable="true"
                Reorderable = "true"
                Groupable="false"
                OnRowClick="@OnOptionGroupRowClickHandler"
                SelectionMode="GridSelectionMode.Single"
                EditMode="@GridEditMode.Popup"
                OnUpdate="@UpdateGroupHandler"
                OnEdit="@EditGroupHandler"
                OnDelete="@DeleteGroupHandler"
                OnCreate="@CreateGroupHandler"
                OnCancel="@CancelGroupHandler"
                ConfirmDelete="true"
                @ref="@GroupGridRef">
                    <GridToolBarTemplate>
                        @if(AllowEdit)
                        {
                            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Group</GridCommandButton>
                        }
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <GridColumns>
                        <GridColumn Field="OptionGroupName" Title="Option Group" Editable="true" Groupable="false" />
                        <GridColumn Field="OptionGroupLetter" Title="Option Group Letter" Editable="true" Groupable="false" Width="0" />
                        <GridColumn Field="DeftMarkupType" Title="Default Markup Type" Editable="true" Groupable="false" Width="0">
                            <EditorTemplate>
                                @{
                                    SelectedGroup = context as OptionGroupDto;
                                    <TelerikDropDownList @bind-Value="@SelectedGroup.DeftMarkupType"
                                    Data="@MarkupTypes"
                                    TextField="Text"
                                    ValueField="Value"                                                
                                    Width="100%">
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                        </GridColumn> 
                        <GridColumn Field="DeftMarkupAmount" Title="Default Markup Amount" Editable="true" Groupable="false" Width="0" />
                        <GridColumn Field="OptionGroupId" Visible="false" Editable="false" Groupable="false" />
                        <GridCommandColumn>
                            @if (AllowEdit)
                            {
                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                            }
                        </GridCommandColumn>
                    </GridColumns>
                    <DetailTemplate>
                        @{
                            var item = context as OptionGroupDto;
                            <p>Description: @item.OptionGroupName</p>
                            <p>Letter: @item.OptionGroupLetter</p>
                            <p>Markup Type: @(MarkupTypes?.SingleOrDefault(x => x.Value ==item.DeftMarkupType)?.Text)</p>
                            <p>Markup Amount: @item.DeftMarkupAmount</p>
                        }
                    </DetailTemplate>
                    <GridSettings>
                        <GridPopupEditSettings Width="400px"
                        Height="500px"
                        Title="Option Group">
                        </GridPopupEditSettings>
                    </GridSettings>
                    <NoDataTemplate>
                        <p>@Message</p>
                    </NoDataTemplate>
                </TelerikGrid>

            }
        </div>
        <div class="col-lg-4">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Options for Group: @SelectedGroup?.OptionGroupName</h7>
                </div>
            </div>
            @if (MasterOptionData == null)
            {
                <p><em>Select a group to see options</em></p>
                <div style=@optionLoadingStyle>Loading...</div>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingOption />
            }
            else
            {
                <TelerikGrid Data="@MasterOptionData"
                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                Height="1000px" RowHeight="60"
                Pageable="true" PageSize="20"
                SelectionMode="GridSelectionMode.Single"
                Size="@ThemeConstants.Grid.Size.Small"
                EditMode="@GridEditMode.Popup"
                Sortable="true"
                Reorderable="true"
                Resizable="true"
                Groupable="false"
                OnRowClick="@OnOptionRowClickHandler"
                OnUpdate="@UpdateOptionHandler"
                OnEdit="@EditOptionHandler"
                OnDelete="@DeleteOptionHandler"
                OnCreate="@CreateOptionHandler"
                OnCancel="@CancelOptionHandler"
                ConfirmDelete="true"
                @ref="@OptionGridRef">
                    <GridColumns>
                        <GridColumn Field="OptionCode" Title="Option Code" Editable="true" Groupable="false" />
                        <GridColumn Field="OptionDesc" Title="Option Description" Editable="true" Groupable="false" />                      
                        <GridColumn Field="OptionGroupId" Title="Option Group" Editable="true" Groupable="false" Width="0">
                            <EditorTemplate>
                                @{
                                    SelectedOption = context as MasterOptionHeaderModel;
                                    // SelectedOption.OptionGroupId = SelectedGroup.OptionGroupId;
                                    <TelerikDropDownList @bind-Value="@OptionGroupIdForAddingOption"
                                    Data="@MasterOptionGroupData"
                                    TextField="OptionGroupName"
                                    ValueField="OptionGroupId"
                                    Filterable="true"
                                    FilterOperator="@StringFilterOperator.Contains"
                                    Width="100%">
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                        </GridColumn> 
                        <GridColumn Field="AsmHeaderId" Visible="false" Editable="false" Groupable="false" />
                        <GridColumn Field="BoolIsElevation" Title="Elevation" Editable="true" EditorType="@GridEditorType.CheckBox" Groupable="false" Width="0" />
                        <GridColumn Field="BoolIsBaseHouse" Title="Base House" Editable="true" EditorType="@GridEditorType.CheckBox" Groupable="false" Width="0" />
                        <GridColumn Title="Add to Plan(s)" Width="0px">
                            <EditorTemplate>
                                @{
                                    CurrentlySelectedMasterPlans = context as MasterOptionHeaderModel;

                                    <TelerikMultiSelect Class="selected-items-container"
                                    ScrollMode="DropDownScrollMode.Virtual"
                                    ItemHeight="30"
                                    PageSize="20"
                                    TextField="DisplayName"
                                    ValueField="MasterPlanId"
                                    TagMode="@MultiSelectTagMode.Multiple"
                                    MaxAllowedTags="5"
                                    Data="@AllMasterPlans"
                                    ShowClearButton="true"
                                    @bind-Value="@CurrentlySelectedMasterPlans.MasterPlanIds"
                                    AutoClose="false"
                                    Filterable="true"
                                    FilterOperator="StringFilterOperator.Contains"
                                    Placeholder="Select Plans">
                                        <MultiSelectSettings>
                                            <MultiSelectPopupSettings Height="auto" />
                                        </MultiSelectSettings>
                                    </TelerikMultiSelect>
                                }
                            </EditorTemplate>
                        </GridColumn>
                        <GridColumn Field="Notes" Editable="true" Groupable="false" EditorType="@GridEditorType.TextArea" Width="0" />
                        <GridCommandColumn>
                            @if(AllowEdit)
                            {
                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                            }
                        </GridCommandColumn>
                    </GridColumns>
                    <DetailTemplate>
                        @{
                            var item = context as MasterOptionHeaderModel;
                            <p>Option Code: @item.OptionCode</p>
                            <p>Description: @item.OptionDesc</p>
                            <p>Option Size: @item.OptionSize</p>
                            <p>Unit: @item.UnitQty</p>
                            <p>Elevation: @item.IsElevation</p>
                            <p>Base House: @item.IsBaseHouse</p>
                            <p>Notes: @item.Notes</p>
                        }
                    </DetailTemplate>
                    <GridToolBarTemplate>
                        @if(AllowEdit)
                        {
                            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Option</GridCommandButton>
                            <GridCommandButton Command="CopyOption" OnClick="@ShowCopyOption" Icon="@FontIcon.Plus" Class="k-button-success">Copy Option</GridCommandButton>
                        }
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <GridSettings>
                        <GridPopupEditSettings Width="400px"
                        Height="400px"
                        Title="Option">
                        </GridPopupEditSettings>
                    </GridSettings>
                    <NoDataTemplate>
                        <p>@Message</p>
                    </NoDataTemplate>
                </TelerikGrid>

            }
        </div>
        <div class="col-lg-5">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Items for Option: @SelectedOption?.OptionDesc</h7>
                </div>
            </div>
            @if (MasterItemData == null)
            {
                <p><em>Select an option to see items</em></p>
                <div style=@itemLoadingStyle>Loading...</div>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingItem />
            }
            else
            {
                <TelerikGrid Data=@MasterItemData
                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                Height="1000px" RowHeight="60" PageSize="20"
                Sortable="true"
                Resizable="true"
                Groupable="false"
                Reorderable="true"
                Size="@ThemeConstants.Grid.Size.Small"
                SelectionMode="GridSelectionMode.Single"
                EditMode="@GridEditMode.Incell"
                OnUpdate="@UpdateItemHandler"
                OnEdit="@EditItemHandler"
                OnDelete="@DeleteItemHandler"
                OnCreate="@CreateItemHandler"
                OnCancel="@CancelItemHandler"
                ConfirmDelete="true"
                @ref="@ItemGridRef">
                    <GridColumns>
                        <GridColumn Field="ItemDesc" Title="Item" Editable="false" Groupable="false" />
                        <GridColumn Field="ItemNumber" Title="Item Code" Editable="false" Groupable="false" Width="0" />
                        <GridColumn Field="BomClassName" Title="Activity" Editable="true" >
                            <EditorTemplate>
                                @{
                                    SelectedItem = context as ModelManagerItemModel;
                                    <TelerikDropDownList @bind-Value="@SelectedItem.BomClassId"
                                    Data="@AllPactivities"
                                    TextField="Activity"
                                    ValueField="BomClassId"
                                    Filterable="true"
                                    FilterOperator="@StringFilterOperator.Contains"
                                    Width="100%">
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                        </GridColumn> 
                        <GridColumn Field="Factor" Title="Quantity" Editable="true" Groupable="false"  />
                        <GridColumn Field="TakeoffUnit" Title="Takeoff Unit" Editable="true" Groupable="false">
                            <EditorTemplate>
                                @{
                                    SelectedItem = context as ModelManagerItemModel;
                                    <TelerikDropDownList @bind-Value="@SelectedItem.TakeoffUnit"
                                    Data="@TakeoffUnitOptions"
                                    Width="100%">
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                        </GridColumn>
                        <GridColumn Field="OptionItemNotes" Title="Option Item Notes" Editable="true" EditorType="@GridEditorType.TextArea" Groupable="false" Width="0" />                       
                        <GridColumn Field="MasterItemId" Visible="false" Editable="false" Groupable="false" />
                        <GridCommandColumn>
                            @*                            <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                            <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil"></GridCommandButton>*@
                            @if(AllowEdit)
                            {
                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                            }

                        </GridCommandColumn>
                    </GridColumns>
                    <DetailTemplate>
                        @{
                            var item = context as ModelManagerItemModel;
                            <p>Phase Code: @item.PhaseCode</p>//actually this seems to be option code??
                            <p>Description: @item.ItemDesc</p>
                            <p>Code: @item.ItemNumber</p>
                            <p>Purchasing Activity: @item.BomClassName</p>
                            <p>Takeoff Unit: @item.TakeoffUnit</p>
                            <p>Quantity: @item.Factor</p>
                            //<p>Notes: @item.ItemNotes</p>
                            <p>Option Item Notes: @item.OptionItemNotes</p>
                        }
                    </DetailTemplate>
                    <GridToolBarTemplate>
                        @if(AllowEdit)
                        {
                            <GridCommandButton Command="MyToolbarCommand" Icon="@FontIcon.Plus" OnClick="@MyCommandFromToolbar" Class="k-button-add">Add Item</GridCommandButton>
                        }
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <GridSettings>
                        <GridPopupEditSettings Width="400px"
                        Height="400px"
                        Title="Item">
                        </GridPopupEditSettings>
                    </GridSettings>
                    <NoDataTemplate>
                        <p>@Message</p>
                    </NoDataTemplate>
                </TelerikGrid>
            }

        </div>
    </div>
</div>
<ERP.Web.Components.CopyOption @ref="CopyOptionModal" SelectedOptionGroupId="@SelectedOptionGroupId" HandleAddSubmit="HandleValidCopyOptionSubmit"></ERP.Web.Components.CopyOption>
<ERP.Web.Components.AddItemToOption @ref="AddItemModal" Option=@SelectedOption HandleAddSubmit="HandleValidAddItemSubmit"></ERP.Web.Components.AddItemToOption>

@code {
    private TelerikGrid<OptionGroupDto>? GroupGridRef { get; set; }
    private TelerikGrid<MasterOptionHeaderModel>? OptionGridRef { get; set; }
    private TelerikGrid<ModelManagerItemModel>? ItemGridRef { get; set; }
    public List<OptionGroupDto>? MasterOptionGroupData { get; set; }
    public List<MasterOptionHeaderModel>? MasterOptionData { get; set; }
    public List<ModelManagerItemModel>? MasterItemData { get; set; }
    public List<PactivityModel>? AllPactivities { get; set; }
    public MasterOptionHeaderModel SelectedOption { get; set; }
    public OptionGroupDto? SelectedGroup { get; set; }
    public ModelManagerItemModel SelectedItem { get; set; }
    public int SelectedOptionGroupId { get; set; } = 0;
    public List<string> TakeoffUnitOptions = new List<string> { "LS", "EA", "LF", "SF", "TON", "UKN" };
    protected ERP.Web.Components.AddItemToOption? AddItemModal { get; set; }
    protected ERP.Web.Components.CopyOption? CopyOptionModal { get; set; }
    public int SelectedMasterPlanId { get; set; } = 0;
    public int OptionGroupIdForAddingOption { get; set; }
    private string optionLoadingStyle = "display:none";
    private string itemLoadingStyle = "display:none";
    public bool IsLoadingGroup { get; set; } = false;
    public bool IsLoadingOption { get; set; } = false;
    public bool IsLoadingItem { get; set; } = false;
    public List<MarkupTypeSelect>? MarkupTypes { get; set; }
    public string? Message { get; set; } = "No data to display";
    public bool? ShowError;

    public List<MasterPlanDto>? AllMasterPlans { get; set; }
    public MasterOptionHeaderModel CurrentlySelectedMasterPlans { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    private bool AllowEdit { get; set; } = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDataAsync();
        await LoadMasterPlansAsync();
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }

    public class MarkupTypeSelect//TODO: this is reused, put in models
    {
        public string? Text { get; set; }
        public int Value { get; set; }
    }

    private async Task LoadDataAsync()
    {
        IsLoadingGroup = true;

        MarkupTypes = new List<MarkupTypeSelect>()
        {
            new MarkupTypeSelect{ Text = "Markup %", Value = 0},
            new MarkupTypeSelect{ Text = "Market Value", Value = 1},
            new MarkupTypeSelect{ Text = "Margin %", Value = 2},
        };

        var getGroups = await OptionService.GetOptionGroupsAsync();
        AllPactivities = (await ItemService.GetPurchasingActivitiesAsync()).Value;

        ShowError = getGroups.IsSuccess;
        Message = getGroups.Message;
        MasterOptionGroupData = getGroups.Value;

        IsLoadingGroup = false;
    }

    private async Task LoadMasterPlansAsync()
    {
        var getMasterPlans = await PlanService.GetMasterPlansAsync();

        ShowError = getMasterPlans.IsSuccess;
        Message = getMasterPlans.Message;
        AllMasterPlans = getMasterPlans.Value;
    }

    protected async Task OnOptionGroupRowClickHandler(GridRowClickEventArgs args)
    {
        IsLoadingOption = true;
        MasterOptionData = null;
        optionLoadingStyle = "";
        SelectedGroup = args.Item as OptionGroupDto;
        SelectedOptionGroupId = SelectedGroup.OptionGroupId;
        OptionGroupIdForAddingOption = SelectedGroup.OptionGroupId;
        MasterOptionData = (await OptionService.GetMasterOptionsByGroupAsync(SelectedGroup.OptionGroupId)).Value;

        MasterItemData = null;//reset the master item data else it shows items from a previously selected trade

        optionLoadingStyle = "display:none";
        IsLoadingOption = false;
    }

    protected async Task OnOptionRowClickHandler(GridRowClickEventArgs args)
    {
        IsLoadingItem = true;
        MasterItemData = null;
        itemLoadingStyle = "";
        SelectedOption = args.Item as MasterOptionHeaderModel;

        MasterItemData = (await ItemService.GetItemsInMasterOptionAsync(SelectedOption.OptionId)).Value;//OptionId here is masteroptionid, not asmheader id

        itemLoadingStyle = "display:none";
        IsLoadingItem = false;
    }

    void EditGroupHandler(GridCommandEventArgs args)
    {
        OptionGroupDto item = (OptionGroupDto)args.Item;
        Console.WriteLine("Edit event is fired.");
    }

    async Task UpdateGroupHandler(GridCommandEventArgs args)
    {
        OptionGroupDto item = (OptionGroupDto)args.Item;

        var response = await OptionService.UpdateOptionGroupAsync(item);

        var getGroups = await OptionService.GetOptionGroupsAsync();

        MasterOptionGroupData = getGroups.Value;

        ShowSuccessOrErrorMessage(response.Message, response.IsSuccess);
    }

    async Task DeleteGroupHandler(GridCommandEventArgs args)
    {
        OptionGroupDto item = (OptionGroupDto)args.Item;

        var response = await OptionService.DeleteOptionGroupAsync(item);

        var getGroups = await OptionService.GetOptionGroupsAsync();

        MasterOptionGroupData = getGroups.Value;

        ShowSuccessOrErrorMessage(response.Message, response.IsSuccess);
    }

    async Task CreateGroupHandler(GridCommandEventArgs args)
    {
        OptionGroupDto group = (OptionGroupDto)args.Item;

        var response = await OptionService.AddOptionGroupAsync(group);

        var getGroups = await OptionService.GetOptionGroupsAsync();

        MasterOptionGroupData = getGroups.Value;

        ShowSuccessOrErrorMessage(response.Message, response.IsSuccess);
    }

    async Task CancelGroupHandler(GridCommandEventArgs args)
    {
        OptionGroupDto item = (OptionGroupDto)args.Item;
    }

    void EditOptionHandler(GridCommandEventArgs args)
    {
        MasterOptionHeaderModel item = (MasterOptionHeaderModel)args.Item;
    }

    async Task UpdateOptionHandler(GridCommandEventArgs args)
    {
        MasterOptionHeaderModel item = (MasterOptionHeaderModel)args.Item;
        item.OptionGroupId = OptionGroupIdForAddingOption;
        var response = await OptionService.UpdateMasterOptionAsync(item);

        MasterOptionData = (await OptionService.GetMasterOptionsByGroupAsync(SelectedGroup.OptionGroupId)).Value;

        if (item.OptionGroupId != SelectedGroup.OptionGroupId)
        {
            MasterItemData = null;//Clear the selection 
        }

        ShowSuccessOrErrorMessage(response.Message, response.IsSuccess);
    }

    async Task DeleteOptionHandler(GridCommandEventArgs args)
    {
        MasterOptionHeaderModel item = (MasterOptionHeaderModel)args.Item;

        MasterOptionDto optionToUpdate = new MasterOptionDto()
            {
                OptionId = item.OptionId,
                OptionDesc = item.OptionDesc,
                OptionCode = item.OptionCode,
                OptionGroupId = SelectedGroup.OptionGroupId
            };

        var response = await OptionService.DeleteMasterOptionAsync(optionToUpdate);

        MasterOptionData = (await OptionService.GetMasterOptionsByGroupAsync(SelectedGroup.OptionGroupId)).Value;

        ShowSuccessOrErrorMessage(response.Message, response.IsSuccess);
    }

    async Task CreateOptionHandler(GridCommandEventArgs args)
    {
        MasterOptionHeaderModel option = (MasterOptionHeaderModel)args.Item;       
        option.OptionGroupId = OptionGroupIdForAddingOption;

        var response = await OptionService.AddMasterOptionAsync(option);

        MasterOptionData = (await OptionService.GetMasterOptionsByGroupAsync(SelectedGroup.OptionGroupId)).Value;

        ShowSuccessOrErrorMessage(response.Message, response.IsSuccess);
    }

    async Task CancelOptionHandler(GridCommandEventArgs args)
    {
        MasterOptionHeaderModel item = (MasterOptionHeaderModel)args.Item;
    }

    void EditItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
    }

    async Task UpdateItemHandler(GridCommandEventArgs args)
    {        
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;

        //TODO: Add Master Plans
        var asmDetailToUpdate = new AsmDetailDto()
            {
                AsmHeaderId = item.AsmHeaderId,
                MasterItemId = item.MasterItemId,
                AsmDetailId = (int)item.AsmDetailId,
                OptionItemNotes = item.OptionItemNotes,                
                SelectAtTakeoffUnit = item.TakeoffUnit,
                BomClassId = item.BomClassId,
                Factor = item.Factor//quantity
            };

        var response = await ItemService.UpdateAsmDetailAsync(asmDetailToUpdate);

        MasterItemData = (await ItemService.GetItemsInMasterOptionAsync(SelectedOption.OptionId)).Value;//OptionId here is masteroptionid, not asmheader id

        ShowSuccessOrErrorMessage(response.Message, response.IsSuccess);
    }

    async Task DeleteItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;

        var asmDetailToUpdate = new AsmDetailDto()
            {
                AsmDetailId = (int)item.AsmDetailId,
                MasterItemId = item.MasterItemId,               
            };

        var response = await ItemService.DeleteAsmDetailAsync(asmDetailToUpdate);        

        MasterItemData = (await ItemService.GetItemsInMasterOptionAsync(SelectedOption.OptionId)).Value;//OptionId here is masteroptionid, not asmheader id

        ShowSuccessOrErrorMessage(response.Message, response.IsSuccess);
    }

    async Task CreateItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
    }

    async Task CancelItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
    }

    private void MyCommandFromToolbar(GridCommandEventArgs args)
    {
        var headerId = SelectedOption.AsmHeaderId;
        AddItemModal.Show();
        StateHasChanged();
    }

    private void ShowCopyOption(GridCommandEventArgs args)
    {
        CopyOptionModal.Show();
        StateHasChanged();
    }

    private async void HandleValidAddItemSubmit(ResponseModel responseItem)
    {
        MasterItemData = (await ItemService.GetItemsInMasterOptionAsync(SelectedOption.OptionId)).Value;//OptionId here is masteroptionid, not asmheader id
        ShowSuccessOrErrorMessage(responseItem.Message, responseItem.IsSuccess);
        AddItemModal.Hide();
        StateHasChanged();
    }

    private async void HandleValidCopyOptionSubmit(ResponseModel responseItem)
    {
        MasterOptionData = (await OptionService.GetMasterOptionsByGroupAsync(SelectedGroup.OptionGroupId)).Value;
        ShowSuccessOrErrorMessage(responseItem.Message, responseItem.IsSuccess);
        CopyOptionModal.Hide();
        StateHasChanged();
    }

    async void ShowSuccessOrErrorMessage(string message, bool isSuccess)
    {
        // Alert
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
