﻿@page "/jobtasks"
@inject PoService PoService
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject AuthenticationStateProvider AuthenticationStateProvider
@implements IDisposable
@attribute [Authorize(Roles = "Admin, Purchasing, Accounting, ReadOnly")]
@using ERP.Data.Models
@using System.Collections.ObjectModel
@using Telerik.DataSource.Extensions
<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="true" ShowJob="true" ShowSubdivision="true"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
<PageTitle>Job Tasks</PageTitle>
<TelerikTooltip TargetSelector=".tooltip-target" />
<div class="container-fluid flex">
    <div class="col-lg-12">
        <div class="card" style="background-color: #2e5771">
            <div class="card-body" style="padding:0.5rem">
                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Job Tasks</h7>
            </div>
        </div>
    </div>

    <div class="row d-flex">
        @if (displayLoadingSpinner)
        {
            <p><em>Loading...</em></p>
            <TelerikLoader></TelerikLoader>
        }
        <div class="col-lg-12">        
            <TelerikGrid Data=@JobTaskData
                         Size="@ThemeConstants.Grid.Size.Small"
                         ScrollMode="@GridScrollMode.Virtual"
                         FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                         PageSize="40"
                         Height="1000px"
                         RowHeight="40"
                         Sortable="true"
                         Resizable="true"
                         Groupable="false"
                         ConfirmDelete="true"
                     >
                <GridToolBarTemplate>
                    <GridSearchBox DebounceDelay="200"></GridSearchBox>
                </GridToolBarTemplate>
                <GridColumns>
                    <GridColumn Field="JobNumber" Title="Job Number" Editable="false" Groupable="true" />
                    <GridColumn Field="JobTaskNo" Title="Job Task No" Editable="false" Groupable="true" />
                    <GridColumn Field="Description" Title="Description" Editable="false" Groupable="true" />
                    <GridColumn Field="JobPostingGroup" Title="G/L Account" Editable="false" Groupable="true" />
                   @*  <GridColumn Field="CostCode" Title="Cost Code" Editable="false" Groupable="true" /> *@
                </GridColumns>                
            </TelerikGrid>

        </div>
    </div>
</div>


@code {

    public List<JobTaskDto>? JobTaskData { get; set; }
    private string? JobSelected { get; set; }
    private bool displayLoadingSpinner { get; set; } = false;

    protected override async Task OnInitializedAsync()
    {

        SubdivisionJobPickService.OnChanged += JobSelectedHandler;
        if (SubdivisionJobPickService.JobNumber != null)
        {
            JobSelected = SubdivisionJobPickService.JobNumber;
        }
       // var getData = await PoService.GetJobTasksAsync();
        //JobTaskData = getData.Value;
    }

    protected async Task JobSelectedHandler()
    {
        if (JobSelected != SubdivisionJobPickService.JobNumber)
        {
            displayLoadingSpinner = true;
            StateHasChanged();
            JobSelected = SubdivisionJobPickService.JobNumber;
            var getData = await PoService.GetJobTasksByJobAsync(JobSelected);
            JobTaskData = getData.Value;
            displayLoadingSpinner = false;
            StateHasChanged();
        }
    }
    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= JobSelectedHandler;
    }
}

