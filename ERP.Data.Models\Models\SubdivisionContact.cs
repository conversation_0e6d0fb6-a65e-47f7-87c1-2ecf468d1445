﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class SubdivisionContact
{
    public int SubdivisionContactId { get; set; }

    public int SubdivisionId { get; set; }

    public string UserId { get; set; } = null!;

    public string SiteContact1 { get; set; } = null!;

    public string SiteContact2 { get; set; } = null!;

    public string SiteContact3 { get; set; } = null!;

    public string SiteContact4 { get; set; } = null!;

    public string SiteContact5 { get; set; } = null!;

    public string SiteContact6 { get; set; } = null!;

    public int? VpoApprovalLevel { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual Subdivision Subdivision { get; set; } = null!;

    public virtual User User { get; set; } = null!;
}
