﻿namespace ERP.Data.Models.Dto
{
    public class AddCustomEstimateModel
    {
        public int? SubdivisionId { get; set; }
        public string? JobNumber {  get; set; }
        public string? OptionCode { get; set; }
        public string? Description { get; set; }
        public string? EstimatorName { get; set; }
        public string? EsimatorEmail { get; set; }
        public string? Notes { get; set; }
        public double? SalesPrice { get; set; }
        public string? CustomerInfoNotes { get; set; }
        public bool CustomerApproved { get; set; }
        
    }
}
