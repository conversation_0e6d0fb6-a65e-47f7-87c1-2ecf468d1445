﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Salesconfigcooption
{
    public int SalesconfigcooptionsId { get; set; }

    public int? SalesconfigcoId { get; set; }

    public string? SalesconfigcoAction { get; set; }

    public double? SalesconfigcoPrice { get; set; }

    public double? SalesconfigcoQuantityChange { get; set; }

    public string? SalesconfigcoNotes { get; set; }

    public string? AssociatedEstimate { get; set; }

    public string? SsOptioncode { get; set; }

    public string? SalesconfigcoSelections { get; set; }

    public string? ScDescription { get; set; }

    public int? PeeHeaderId { get; set; }

    public string? JcCategory { get; set; }

    public int? EstheaderId { get; set; }

    public int? PlanOptionId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedB { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<Estoption> Estoptions { get; set; } = new List<Estoption>();

    public virtual AvailablePlanOption? PlanOption { get; set; }

    public virtual Salesconfigco? Salesconfigco { get; set; }

    public virtual ICollection<SalesconfigcooptionsAttribute> SalesconfigcooptionsAttributes { get; set; } = new List<SalesconfigcooptionsAttribute>();
}
