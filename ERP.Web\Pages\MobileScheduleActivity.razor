﻿ @page "/MobileScheduleActivity/{schedulemid:int}"
@inject SubdivisionService SubdivisionService
@inject ScheduleService ScheduleService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject SubdivisionJobPickService SubdivisionJobPickService
@using ERP.Data.Models.ExtensionMethods
@using ERP.Data.Models.Dto
@using ERP.Data.Models
@using Telerik.DataSource.Extensions
@using System.Collections.ObjectModel
@using Microsoft.EntityFrameworkCore;

@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector, ConstructionManager")]

<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }
    /*.mytreeclass .k-drag-cell {
                display:none;
            }*/
    /*    .mytreeclass .k-table-th.k-header.k-drag-cell{
            visibility: collapse;
        }
        .mytreeclass .k-drag-cell.k-touch-action-none{
            visibility: collapse;

        }*/
    .mytreeclass .k-drag-col {
        visibility: collapse;
    }

    .mytreeclass .k-header {
        min-width: 200px;
    }

    .mytreeclass {
        overflow-x: auto;
    }

    .k-drag-clue {
        color: white;
    }

    .k-icon.k-icon-with-modifier {
        display: none;
    }

    .SubdivisionTable .k-hierarchy-cell {
        display: none;
    }

    .k-splitbar {
        width: 15px;
        color: black;
        padding-left: 5px;
        padding-right: 5px;
    }

    .pane {
        padding: 8px;
        overflow-x: auto;
    }

    .k-splitter .k-pane {
        overflow-x: auto !important;
    }

    .k-splitter-horizontal {
        overflow-x: auto !important;
    }

    .page-wrapper .page-content {
        /* padding-right: 100px !important;
            margin-right: 100px !important;*/
        /*        width: 95% !important;*/
    }

    .page-wrapper {
        /* padding-right: 100px !important;
            margin-right: 100px !important;
                width: 80% !important;*/
    }

    .k-table-td {
        padding: 4px !important;
        font-size: 11px;
    }

    .k-table-td { /*these settings are to prevent the 0 width columns from affecting the column height*/
        padding: 4px !important;
        font-size: 11px;
        height: 30px !important;
        white-space: nowrap !important;
        overflow: visible;
    }

    input,
    label {
        margin: 0.4rem 0;
    }

    .activityCheckbox {
        width: 20px;
        height: 20px;
    }

    .readonly-box {
        background-color: #e4e7eb; 
    }

    .k-button-solid-base.k-selected {
        border-color: #f4cd64;
        color: black;
        background-color: #f4cd64;
    }

    .k-panelbar > .k-item > .k-link.k-selected, .k-panelbar > .k-panelbar-header > .k-link.k-selected {
        color: black;
        background-color: #f4cd64;
    }

    .k-panelbar > .k-item > .k-link, .k-panelbar > .k-panelbar-header > .k-link {
        color: black;
    }

        .k-panelbar > .k-panelbar-header > .k-link.k-selected:hover {
            background-color: #f4cd64;
        }

    .my-checkbox {
        width: 20px; height: 20px
    }

    .sticky-top {
    position: sticky;
    top: 60px;
    z-index: 950 !important; 
    background-color: #efefef; 
    }

    .k-window telerik-blazor {
        position: absolute;
        z-index: 2000 !important;
    }

    .bottomPosition {
        position: relative;
        z-index: 800 !important;
    }

    .topPosition{
        position: absolute;
        z-index: 1000 !important;
    }

    .k-panelbar .k-item .k-link,
    .k-panelbar .k-item .k-content {
        z-index: 850 !important; 
    }

    .k-dropdown {
        z-index: 800 !important; 
    }

    .k-panelbar-content {
        z-index: 800 !important;
    }

</style>
<TelerikMediaQuery Media="@SmallScreenMediaQuery" OnChange="@((doesMatch) => IsSmallScreen = doesMatch)"></TelerikMediaQuery>
<TelerikMediaQuery Media="@LargeScreenMediaQuery" OnChange="@((doesMatch) => IsLargeScreen = doesMatch)"></TelerikMediaQuery>
<TelerikTooltip TargetSelector=".tooltip-target" />
@if (displaySuccessStatus)
{
    if (isError == false)
    {
        <div class="alert alert-success" role="alert">
            @displaySuccessMessage
        </div>
    }
    else if (isError == true)
    {
        <div class="alert alert-danger" role="alert">
            @displaySuccessMessage
        </div>
    }
}
@if (IsLargeScreen)
{
    <div class="col-lg-12">
        <div class="card" style="background-color:#2e5771">
            <div class="card-body" style="padding:0.5rem">
                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Mobile Vertical View Only - Rotate or Go Back To Main Schedule</h7>
            </div>
        </div>
    </div>

    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/schedule">Schedule</a></li>
        <li class="breadcrumb-item active">Mobile Schedule Activity</li>
    </ol>

    <a href="/schedule"><button class="k-button-success">BACK TO SCHEDULE</button></a>


}
else
{

    <ol class="breadcrumb sticky-top">
        <li class="breadcrumb-item"><a href="/schedule">Schedule</a></li>
        <li class="breadcrumb-item active">Mobile Schedule Activity</li>
    </ol>

    @if (AllActivitiesThisMilestone != null && AllActivitiesThisMilestone.Any())
    {
        <div class="sticky-top">
            <div class="card" style="background-color:#2e5771; align-content:center">
                <div class="card-body" style="padding:0.5rem">
                    <h5 class="page-title" style="font-weight:bold; color:#fff">
                        Job#@AllActivitiesThisMilestone.First().ScheduleM?.Schedule?.JobNumber - @AllActivitiesThisMilestone.First().ScheduleM?.Milestone?.MilestoneName
                        Schedule
                    </h5>
                </div>
            </div>
            <TelerikButton OnClick="@Save" Title="Save" Icon="@FontIcon.Save" Class="tooltip-target k-button-success" />
            <TelerikButton OnClick="@CancelChanges" Title="Cancel Changes" Icon="@FontIcon.Cancel" Class="tooltip-target k-button-danger"></TelerikButton>
            <hr />
            <TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
            <TelerikTextBox @bind-Value="@SearchTerm" OnChange="@FilterActivities" Placeholder="Search Activity or Supplier - Click Enter" Class="mb-2"></TelerikTextBox>
        </div>

        var sortedActivities = FilteredActivities.OrderBy(activity => activity.SchStartDate).ToList();

        <TelerikPanelBar Data="sortedActivities">
            <PanelBarBindings>
                <PanelBarBinding>
                    <HeaderTemplate>
                        @{
                            var activity = context as ScheduleSactivityDto;
                            <div class="col" style="text-align:center">
                                <h3>
                                    @if (activity.ActualEndDate != null && activity.BoolComplete)
                                    {
                                        <div style="display: inline-block; margin-right: 10px; background-color: green; width: 20px; height: 20px; border-radius: 50%;"></div>
                                    }
                                    else if (DateTime.Today > activity.SchEndDate)
                                    {
                                        <div style="display: inline-block; margin-right: 10px; background-color: red; width: 20px; height: 20px; border-radius: 50%;"></div>
                                    }
                                    else
                                    {
                                        <div style="display: inline-block; margin-right: 10px; background-color: yellow; width: 20px; height: 20px; border-radius: 50%;"></div>
                                    }
                                    @activity.Sactivity.ActivityName
                                </h3>
                                <h4 style="white-space: nowrap">Sch. Start: @activity.SchStartDate?.ToString("MM/dd/yyyy") | Sch. End: @activity.SchEndDate?.ToString("MM/dd/yyyy")</h4>

                            </div>

                        }
                    </HeaderTemplate>
                    <ContentTemplate>
                        @{
                            var details = context as ScheduleSactivityDto;

                            <div class="k-card-body bottomPosition">
                                <div class="row">
                                    <div class="col-4 bottomPosition" style="text-align: left; padding-left: 10px">
                                        <label class="form-label" style="font-size: 20px">
                                            <TelerikCheckBox Class="my-checkbox" OnChange="@(() => CheckStartedChanged(details))" @bind-Value="@details.BoolStarted"></TelerikCheckBox>
                                            Started
                                        </label>

                                    </div>
                                    <div class="col-4 bottomPosition" style="text-align: left; padding-left: 10px">
                                        <label class="form-label" style="font-size: 20px">
                                            <TelerikCheckBox Class="my-checkbox" OnChange="@(() => CheckCompleteChanged(details))" @bind-Value="@details.BoolComplete"></TelerikCheckBox>
                                            Complete
                                        </label>
                                    </div>
                                    <div class="col-4 bottomPosition" style="text-align: right; padding-right: 10px">
                                        <label class="form-label" style="font-size: 20px">
                                            <TelerikCheckBox Class="my-checkbox" @bind-Value="details.BoolIgnoreNoWorkDays" OnChange="@(() => SomethingChanged(details))" /> Ignore No Work Days
                                        </label>
                                    </div>
                                </div>
                                <div class="row justify-space-between">
                                    <div class="col-12"><h6>Supplier Name:</h6>@details.SubNumberNavigation?.SubName</div>
                                    <div class="col-6">
                                        <h6>Predecessor:</h6>
                                        @if (details.Predecessors != null && details.Predecessors.Any(x => x.PredSactivity != null))
                                        {
                                            @string.Join(",", details.Predecessors.Where(x => x.PredSactivity != null).Select(x => x.PredSactivity.ActivityName))
                                        }
                                        else
                                        {
                                            <span>N/A</span>
                                        }
                                    </div>
                                    <div class="col-6">
                                        <h6>Variance Code:</h6>
                                        <TelerikDropDownList Data="@VarianceCodes"
                                        TextField="VarianceDesc"
                                        ValueField="VarianceCode"
                                        OnChange="@(() => SomethingChanged(details))"
                                        @bind-Value="details.VarianceCode">
                                        </TelerikDropDownList>                                       
                                    </div>
                                    <!--<div class="col-md-4 col-sm-6 col-xs-6">
                                    <h6>Duration:</h6>
                                    <TelerikNumericTextBox Max="400" Min="-400" Step="1" @bind-Value="@details.Duration" OnChange="@(() => SomethingChanged(details))" Class="readonly-box"></TelerikNumericTextBox>
                                    </div>-->
                                    <!--<div class="col-md-4 col-sm-6 col-xs-6">
                                    <h6>Actual Duration:</h6>
                                    <TelerikNumericTextBox Max="400" Min="-400" Step="1" @bind-Value="@details.ActualDuration" ReadOnly="true" Class="readonly-box"></TelerikNumericTextBox>
                                    </div>-->
                                    <!--<div class="col-md-4 col-sm-6 col-xs-6">
                                    <h6>Lag Time:</h6>
                                    <TelerikNumericTextBox Max="400" Min="-400" Step="1" OnChange="@(() => SomethingChanged(details))" @bind-Value="@details.LagTime"></TelerikNumericTextBox>
                                    </div>-->
                                    <!--<div class="col-md-4 col-sm-6 col-xs-6">
                                    <h6>Plus Minus:</h6>
                                    <TelerikNumericTextBox Max="400" Min="-400" Step="1" @bind-Value="@details.PlusminusDays" ReadOnly="true"></TelerikNumericTextBox>
                                    </div>-->
                                </div>
                                <div class="row justify-space-between">
                                    <!--<div class="col-md-4 col-sm-6 col-xs-6">
                                    <h6>Baseline Start</h6>
                                    <TelerikDatePicker @bind-Value="@details.BaseStartDate"
                                    Min="@Min"
                                    Max="@Max"
                                    Format="MM/dd/yyyy"
                                    DebounceDelay="@DebounceDelay"
                                    ShowWeekNumbers="true"
                                    OnChange="@(() => SomethingChanged(details))"
                                    Enabled="@isBaseDateEditable">
                                    <DatePickerFormatPlaceholder Day="day" Month="month" Year="year" />
                                    </TelerikDatePicker>
                                    <h6>Baseline End</h6>
                                    <TelerikDatePicker @bind-Value="@details.BaseEndDate"
                                    Min="@Min"
                                    Max="@Max"
                                    Format="MM/dd/yyyy"
                                    DebounceDelay="@DebounceDelay"
                                    ShowWeekNumbers="true"
                                    OnChange="@(() => SomethingChanged(details))"
                                    Enabled="@isBaseDateEditable">
                                    <DatePickerFormatPlaceholder Day="day" Month="month" Year="year" />
                                    </TelerikDatePicker>
                                    </div>-->
                                    <div class="col-md-6 col-sm-6 col-xs-6">
                                        <h6>Scheduled Start</h6>
                                        <TelerikDatePicker Value="@details.SchStartDate"
                                        Min="@Min"
                                        Max="@Max"
                                        Format="MM/dd/yyyy"
                                        DebounceDelay="@DebounceDelay"
                                        ShowWeekNumbers="true"
                                        ValueChanged="@((DateTime? schStartDate) => SchStartChanged(schStartDate, details))"
                                        Enabled="@(details.VarianceCode != null)">
                                            <DatePickerFormatPlaceholder Day="day" Month="month" Year="year" />
                                        </TelerikDatePicker>
                                        @if(details.VarianceCode == null)
                                        {
                                            <span style="color:red; font-size: 10px; font-style: italic">Variance must be entered to edit schedule dates</span>
                                        }
                                        <h6>Scheduled End</h6>
                                        <TelerikDatePicker Value="@details.SchEndDate"
                                        Min="@Min"
                                        Max="@Max"
                                        Format="MM/dd/yyyy"
                                        DebounceDelay="@DebounceDelay"
                                        ValueChanged="@((DateTime? schEndDate) => SchEndChanged(schEndDate, details))"
                                        ShowWeekNumbers="true"
                                        Enabled="@(details.VarianceCode != null)">
                                            <DatePickerFormatPlaceholder Day="day" Month="month" Year="year" />
                                        </TelerikDatePicker>
                                        @if (details.VarianceCode == null)
                                        {
                                            <span style="color:red; font-size: 10px; font-style: italic">Variance must be entered to edit schedule dates</span>
                                        }
                                    </div>
                                    <div class="col-md-6 col-sm-6 col-xs-6">
                                        <h6>Actual Start</h6>
                                        <TelerikDatePicker @bind-Value="@details.ActualStartDate"
                                        Min="@Min"
                                        Max="@Max"
                                        Format="MM/dd/yyyy"
                                        DebounceDelay="@DebounceDelay"
                                        ShowWeekNumbers="true"
                                        Enabled="false">
                                            <DatePickerFormatPlaceholder Day="day" Month="month" Year="year" />
                                        </TelerikDatePicker>
                                        <h6>Actual End</h6>
                                        <TelerikDatePicker @bind-Value="@details.ActualEndDate"
                                        Min="@Min"
                                        Max="@Max"
                                        Format="MM/dd/yyyy"
                                        DebounceDelay="@DebounceDelay"
                                        ShowWeekNumbers="true"
                                        Enabled="false">
                                            <DatePickerFormatPlaceholder Day="day" Month="month" Year="year" />
                                        </TelerikDatePicker>
                                    </div>
                                </div>
                            </div>
                            <CardActions Class="justify-space-between">
                                @if (details.UpdatedDateTime == null) @*TODO: CHANGE THIS TO COMPLETED BY NOT UPDATED BY*@
                                {
                                    <div class="col-6" style="text-align:left; font-size:12px">
                                        Created By: @details.CreatedBy / @details.CreatedDateTime
                                    </div>
                                }
                                else
                                {
                                    <div class="col-6" style="text-align:left; font-size:12px">
                                        Last Updated By: @details.UpdatedBy / @details.UpdatedDateTime
                                    </div>
                                }
                                <div class="col-6" style="text-align:right; padding-right:10px">
                                    <TelerikButton Title="View/Edit" OnClick="() => EditActivity(details)" Class="k-button-success">Notes/Edit</TelerikButton>
                                </div>
                                <hr />
                            </CardActions>

                        }
                    </ContentTemplate>
                </PanelBarBinding>
            </PanelBarBindings>
        </TelerikPanelBar>
    }

}
<NavigationLock ConfirmExternalNavigation="@UnsavedChanges" OnBeforeInternalNavigation="BeforeInternalNavigation" />
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<ERP.Web.Components.EditScheduleActivityNotes @ref="EditScheduleActivity" SelectedActivity="@SelectedActivity" HandleAddSubmit="@HandleValidEditActivitySubmit"></ERP.Web.Components.EditScheduleActivityNotes>

@code {


    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    private string? SelectedJob { get; set; }
    private string? JobSelected { get; set; }
    public ScheduleSactivityDto? SelectedActivity { get; set; }

    public ScheduleTemplateTreeModel? ItemToEdit { get; set; }

    protected ERP.Web.Components.EditScheduleActivityNotes? EditScheduleActivity { get; set; }
    public List<VarianceDto> VarianceCodes { get; set; }
    private bool isScheduledStartEditable { get; set; } = true;
    private bool isScheduledEndEditable { get; set; } = true;
    private bool isSupplierEditable { get; set; } = true;
    private bool isBaseDateEditable { get; set; } = false;
    private bool IsSmallScreen { get; set; }
    private bool IsLargeScreen { get; set; }
    private string SmallScreenMediaQuery { get; set; } = "(max-width: 767px)";
    private string LargeScreenMediaQuery { get; set; } = "(min-width: 1199px)";
    private bool userIsDirectorOrAdmin { get; set; } = false;
    private List<DateTime>? holidays { get; set; }

    [Parameter]
    public int scheduleMid { get; set; }

    // Success/Error
    private bool displaySuccessStatus = false;
    private string? displaySuccessMessage;
    private bool isError = false;

    //DatePickers
    private DateTime? SelectedDate { get; set; }
    private DateTime Max = new DateTime(2050, 12, 31);
    private DateTime Min = new DateTime(1950, 1, 1);
    private int DebounceDelay { get; set; } = 200;

    private string? SearchTerm { get; set; }

    private string? UserName { get; set; }
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    //filtering
    List<ScheduleSactivityDto> FilteredActivities = new List<ScheduleSactivityDto>();
    List<ScheduleSactivityDto> AllActivitiesThisMilestone = new List<ScheduleSactivityDto>();
    List<ScheduleSactivityDto> AllActivitiesInSchedule = new List<ScheduleSactivityDto>();

    private bool UnsavedChanges { get; set; } = false;

    private async Task BeforeInternalNavigation(LocationChangingContext context)
    {
        if (UnsavedChanges)
        {
            var proceed = await Dialogs.ConfirmAsync("You have unsaved changes, are you sure you want to leave this page?");

            if (!proceed)
            {
                context.PreventNavigation();
            }
        }
    }
    private async Task FilterActivities()
    {
        FilteredActivities = AllActivitiesThisMilestone.Where(x =>
            x.Sactivity.ActivityName.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ||
            x.SubNumberNavigation.SubName.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase))
            .OrderBy(a => a.Seq)
            .ToList();
    }

    protected override async Task OnInitializedAsync()
    {
        var getCalendarTask = ScheduleService.GetHolidaysAsync();
        var varianceCodesTask = ScheduleService.GetVarianceCodesAsync();
        await Task.WhenAll(new Task[] { varianceCodesTask, getCalendarTask });
        VarianceCodes = varianceCodesTask.Result.Value;
        holidays = getCalendarTask.Result.Value.Select(x => x.WorkDate).ToList();
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        UserName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userroleConstructionDirector = user.User.IsInRole("ConstructionDirector");
        isBaseDateEditable = userRoleAdmin || userroleConstructionDirector;
        userIsDirectorOrAdmin = userRoleAdmin || userroleConstructionDirector;
    }



    protected override async Task OnParametersSetAsync()
    {
        AllActivitiesThisMilestone = (await ScheduleService.GetScheduleActivitiesForMilestoneAsync(scheduleMid)).Value;
        var scheduleId = AllActivitiesThisMilestone.FirstOrDefault()?.ScheduleM.ScheduleId;
        // AllActivitiesInSchedule = (await ScheduleService.GetScheduleActivitiesForScheduleAsync((int)scheduleId)).Value;
        var jobNumber = AllActivitiesThisMilestone.First().ScheduleM?.Schedule?.JobNumber;
        var getData = (await ScheduleService.GetScheduleAsync(jobNumber)).Value;
        var allActivitiesInSchedule = getData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();
        AllActivitiesInSchedule = allActivitiesInSchedule;
        //AllActivitiesThisMilestone = AllActivitiesInSchedule.Where(x => x.ScheduleMid == scheduleMid).ToList();//TODO: fix
        FilteredActivities = AllActivitiesThisMilestone;
    }
    private void EditActivity(ScheduleSactivityDto activity)
    {
        if (activity != null)
        {
            SelectedActivity = activity;
        }
        EditScheduleActivity?.Show();
        StateHasChanged();
    }
    // private async Task UpdateActivity(ScheduleSactivityDto activity)
    // {
    //     if (activity != null)
    //     {
    //         // Saves the updated activity in the backend. Also updates scheduled dates for other activities in the schedule
    //         var response = await ScheduleService.UpdateScheduleSactivityAsync(activity);

    //         // Check if the response fails
    //         if (response != null && response.IsSuccess)
    //         {
    //             // Update the UI to reflect the changes
    //             // You may need to update the state or re-fetch data to ensure the UI reflects the updated activity
    //             // For example, you can update the activity directly in the FilteredActivities list
    //             var index = FilteredActivities.FindIndex(a => a.ScheduleAid == activity.ScheduleAid);
    //             if (index != -1)
    //             {
    //                 FilteredActivities[index] = activity;
    //             }

    //             //SUCCESS
    //             ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
    //             // Optionally, you may call StateHasChanged() to force the UI to re-render
    //             StateHasChanged();

    //             //wait 2 seconds then refresh
    //             await Task.Delay(2000);
    //             NavManager.NavigateTo(NavManager.Uri, forceLoad: true);
    //         }
    //         else
    //         {
    //             // Handle the case where the update fails
    //             // For example, you might display an error message to the user or log the error
    //             ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);

    //             //ERROR
    //         }
    //     }
    // }
    private async Task CheckStartedChanged(ScheduleSactivityDto activity)
    {
        if (activity.BoolStarted == true)
        {
            if (!userIsDirectorOrAdmin && (activity.SchStartDate.Value.Date < DateTime.Now.Date))
            {
                // await Dialogs.AlertAsync("Activity is late! You must select a variance and then adjust the scheduled dates. Contact Construction Director to override this if needed");
                // SelectedActivity.BoolStarted = false;
                //For now just warning, later may prevent them from saving update
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    activity.BoolStarted = false;
                }
                else
                {
                    activity.ActualStartDate = activity.SchStartDate;
                    var findActivityInSchedule = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == activity.ScheduleAid);
                    findActivityInSchedule.BoolStarted = activity.BoolStarted;
                    findActivityInSchedule.ActualStartDate = activity.ActualStartDate;
                }
            }
            else if (userIsDirectorOrAdmin && (activity.SchStartDate.Value.Date < DateTime.Now.Date))
            {
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    activity.BoolStarted = false;
                }
                else
                {
                    activity.ActualStartDate = activity.SchStartDate;
                    var findActivityInSchedule = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == activity.ScheduleAid);
                    findActivityInSchedule.BoolStarted = activity.BoolStarted;
                    findActivityInSchedule.ActualStartDate = activity.ActualStartDate;
                }
            }
            else 
            {
                activity.ActualStartDate = activity.SchStartDate;
                var findActivityInSchedule = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == activity.ScheduleAid);
                findActivityInSchedule.BoolStarted = activity.BoolStarted;
                findActivityInSchedule.ActualStartDate = activity.ActualStartDate;
            }
        }
        else
        {
            //uncheck started
            activity.ActualStartDate = null;
            var findActivityInSchedule = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == activity.ScheduleAid);
            findActivityInSchedule.BoolStarted = activity.BoolStarted;
            findActivityInSchedule.ActualStartDate = activity.ActualStartDate;
        }
    }
    private async Task CheckCompleteChanged(ScheduleSactivityDto activity)
    {
        if (activity.BoolComplete == true)
        {
            if (!userIsDirectorOrAdmin && (CalendarExtension.AddWorkingDays(activity.SchEndDate.Value.Date, 1, holidays) < DateTime.Now.Date || (CalendarExtension.AddWorkingDays(activity.SchEndDate.Value.Date, 1, holidays) == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                //basedateeditable is user is in construction dir role
                // await Dialogs.AlertAsync("Activity is late! You must select a variance and then adjust the scheduled dates. Contact Construction Director to override this if needed");
                // activity.BoolComplete = false;
                //For now just a warning
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    activity.BoolComplete = false;
                }
                else
                {
                    //SelectedActivity.ActualStartDate = SelectedActivity.SchStartDate;
                    activity.ActualEndDate = activity.SchEndDate;
                    var findActivityInSchedule = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == activity.ScheduleAid);
                    findActivityInSchedule.BoolComplete = activity.BoolComplete;
                    findActivityInSchedule.ActualEndDate = activity.SchEndDate;
                    findActivityInSchedule.Complete = activity.BoolComplete ? "T" : "F";
                    findActivityInSchedule.CompletedBy = activity.BoolComplete ? UserName : null;
                }
            }
            else if (userIsDirectorOrAdmin && (CalendarExtension.AddWorkingDays(activity.SchEndDate.Value.Date, 1, holidays) < DateTime.Now.Date || (CalendarExtension.AddWorkingDays(activity.SchEndDate.Value.Date, 1, holidays) == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    activity.BoolComplete = false;
                }
                else
                {
                    //SelectedActivity.ActualStartDate = SelectedActivity.SchStartDate;
                    activity.ActualEndDate = activity.SchEndDate;
                    var findActivityInSchedule = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == activity.ScheduleAid);
                    findActivityInSchedule.BoolComplete = activity.BoolComplete;
                    findActivityInSchedule.ActualEndDate = activity.SchEndDate;
                    findActivityInSchedule.Complete = activity.BoolComplete ? "T" : "F";
                    findActivityInSchedule.CompletedBy = activity.BoolComplete ? UserName : null;
                }
            }
            else 
            {
                activity.ActualEndDate = activity.SchEndDate;
                var findActivityInSchedule = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == activity.ScheduleAid);
                findActivityInSchedule.BoolComplete = activity.BoolComplete;
                findActivityInSchedule.ActualEndDate = activity.SchEndDate;
                findActivityInSchedule.Complete = activity.BoolComplete ? "T" : "F";
                findActivityInSchedule.CompletedBy = activity.BoolComplete ? UserName : null;
            }
        } 
        else
        {
            //uncheck complete
            activity.ActualEndDate = null;
            var findActivityInSchedule = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == activity.ScheduleAid);
            findActivityInSchedule.BoolComplete = activity.BoolComplete;
            findActivityInSchedule.ActualEndDate = activity.ActualEndDate;
        }
    }
    private async Task SomethingChanged(ScheduleSactivityDto activity)
    {
        if (activity != null)
        {
            UnsavedChanges = true;
            var updateBaseDates = false;
            //find what changed, compare with the one in AllActivitiesInSchedule
            var findActivityInSchedule = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == activity.ScheduleAid);
            if (findActivityInSchedule.SchStartDate != activity.SchStartDate)
            {
                //sch start date chagned, change end date based on duration
                if (activity.VarianceCode == null && activity.SchStartDate > findActivityInSchedule.SchStartDate)
                {
                    await Dialogs.AlertAsync("You must enter a variance!");
                    activity.SchStartDate = findActivityInSchedule.SchStartDate;//Cancel change
                    return;
                }
                var checkHoliday = (await ScheduleService.CheckHolidayAsync(activity.SchStartDate.Value)).Value;
                if (checkHoliday && !activity.BoolIgnoreNoWorkDays)
                {
                    var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");
                    if (!confirm)
                    {
                        activity.SchStartDate = findActivityInSchedule.SchStartDate;//Cancel change
                        return;
                    }
                    findActivityInSchedule.IsLocked = "T";
                    findActivityInSchedule.BoolIgnoreNoWorkDays = true;//set ignore no work days if they choose to ignore the holiday
                }
                findActivityInSchedule.SchStartDate = activity.SchStartDate;
                findActivityInSchedule.SchEndDate = findActivityInSchedule.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findActivityInSchedule.SchStartDate, (int)findActivityInSchedule.Duration - 1, holidays) : findActivityInSchedule.SchStartDate.Value.AddDays((int)findActivityInSchedule.Duration - 1);
                var plusminusdaysend = findActivityInSchedule.ActualEndDate != null ? findActivityInSchedule.ActualEndDate.Value : findActivityInSchedule.SchEndDate.Value;
                findActivityInSchedule.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(findActivityInSchedule.BaseEndDate.Value, plusminusdaysend, holidays);
                //recalculate schedule
                var response = await ScheduleService.UpdateScheduleSActivityAsync(findActivityInSchedule, AllActivitiesInSchedule, updateBaseDates);
                var activitiesToUpdate = response.Value;
                //TODO: now need to set the milestone and filtered ones to match
                foreach (var updateActivity in AllActivitiesThisMilestone)
                {
                    var findActivity = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == updateActivity.ScheduleAid);
                    //update the properties, not the reference, else it will fail next time
                    updateActivity.SchStartDate = findActivity.SchStartDate;
                    updateActivity.SchEndDate = findActivity.SchEndDate;
                    // updateActivity.BoolIgnoreNoWorkDays = findActivity.BoolIgnoreNoWorkDays;
                    // updateActivity.LagTime = findActivity.LagTime;
                    // updateActivity.Duration = findActivity.SchStartDate;
                    updateActivity.BaseStartDate = findActivity.BaseStartDate;
                    updateActivity.BaseEndDate = findActivity.BaseEndDate;
                }
                //FilteredActivities = AllActivitiesThisMilestone;
            }
            else if (findActivityInSchedule.SchEndDate != activity.SchEndDate)
            {
                if (activity.VarianceCode == null && activity.SchEndDate > findActivityInSchedule.SchEndDate)
                {
                    await Dialogs.AlertAsync("You must enter a variance!");
                    activity.SchEndDate = findActivityInSchedule.SchEndDate;//Cancel change
                    return;
                }
                findActivityInSchedule.SchEndDate = activity.SchEndDate;
                //recalculate schedule
                var response = await ScheduleService.UpdateScheduleSActivityAsync(activity, AllActivitiesInSchedule, updateBaseDates);
                var activitiesToUpdate = response.Value;
                // set the visible ones to match the recalculated values
                foreach (var updateActivity in AllActivitiesThisMilestone)
                {
                    var findActivity = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == updateActivity.ScheduleAid);
                    //update the properties, not the reference, else it will fail next time
                    updateActivity.SchStartDate = findActivity.SchStartDate;
                    updateActivity.SchEndDate = findActivity.SchEndDate;
                    // updateActivity.BoolIgnoreNoWorkDays = findActivity.BoolIgnoreNoWorkDays;
                    // updateActivity.LagTime = findActivity.LagTime;
                    // updateActivity.Duration = findActivity.SchStartDate;
                    updateActivity.BaseStartDate = findActivity.BaseStartDate;
                    updateActivity.BaseEndDate = findActivity.BaseEndDate;
                }
            }
            else if (findActivityInSchedule.BaseStartDate != activity.BaseStartDate)
            {
                //TODO: confirm dialog if start date holiday
                //base start date chagned, need to change end date based on duration
                findActivityInSchedule.BaseStartDate = activity.BaseStartDate;
                findActivityInSchedule.BaseEndDate = findActivityInSchedule.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findActivityInSchedule.BaseStartDate, (int)findActivityInSchedule.Duration - 1, holidays) : findActivityInSchedule.BaseStartDate.Value.AddDays((int)findActivityInSchedule.Duration - 1);
                var plusminusdaysend = findActivityInSchedule.ActualEndDate != null ? findActivityInSchedule.ActualEndDate.Value : findActivityInSchedule.SchEndDate.Value;
                findActivityInSchedule.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(findActivityInSchedule.BaseEndDate.Value, plusminusdaysend, holidays);
                updateBaseDates = true;
                //recalculate schedule
                var response = await ScheduleService.UpdateScheduleSActivityAsync(activity, AllActivitiesInSchedule, updateBaseDates);
                var activitiesToUpdate = response.Value;
                // set the visible ones to match the recalculated values
                foreach (var updateActivity in AllActivitiesThisMilestone)
                {
                    var findActivity = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == updateActivity.ScheduleAid);
                    //update the properties, not the reference, else it will fail next time
                    updateActivity.SchStartDate = findActivity.SchStartDate;
                    updateActivity.SchEndDate = findActivity.SchEndDate;
                    // updateActivity.BoolIgnoreNoWorkDays = findActivity.BoolIgnoreNoWorkDays;
                    // updateActivity.LagTime = findActivity.LagTime;
                    // updateActivity.Duration = findActivity.SchStartDate;
                    updateActivity.BaseStartDate = findActivity.BaseStartDate;
                    updateActivity.BaseEndDate = findActivity.BaseEndDate;
                }
            }
            else if (findActivityInSchedule.BaseEndDate != activity.BaseEndDate)
            {
                findActivityInSchedule.BaseEndDate = activity.BaseEndDate;
                var plusminusdaysend = findActivityInSchedule.ActualEndDate != null ? findActivityInSchedule.ActualEndDate.Value : findActivityInSchedule.SchEndDate.Value;
                findActivityInSchedule.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(findActivityInSchedule.BaseEndDate.Value, plusminusdaysend, holidays);
                updateBaseDates = true;
                //recalculate schedule
                var response = await ScheduleService.UpdateScheduleSActivityAsync(activity, AllActivitiesInSchedule, updateBaseDates);
                var activitiesToUpdate = response.Value;
                // set the visible ones to match the recalculated values
                foreach (var updateActivity in AllActivitiesThisMilestone)
                {
                    var findActivity = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == updateActivity.ScheduleAid);
                    //update the properties, not the reference, else it will fail next time
                    updateActivity.SchStartDate = findActivity.SchStartDate;
                    updateActivity.SchEndDate = findActivity.SchEndDate;
                    // updateActivity.BoolIgnoreNoWorkDays = findActivity.BoolIgnoreNoWorkDays;
                    // updateActivity.LagTime = findActivity.LagTime;
                    // updateActivity.Duration = findActivity.SchStartDate;
                    updateActivity.BaseStartDate = findActivity.BaseStartDate;
                    updateActivity.BaseEndDate = findActivity.BaseEndDate;
                }
            }
            else if (findActivityInSchedule.Duration != activity.Duration)
            {
                //duration chagned, update sch end date
                findActivityInSchedule.Duration = activity.Duration;
                findActivityInSchedule.SchEndDate = findActivityInSchedule.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findActivityInSchedule.SchStartDate, (int)findActivityInSchedule.Duration - 1, holidays) : findActivityInSchedule.SchStartDate.Value.AddDays((int)findActivityInSchedule.Duration - 1);
                var plusminusdaysend = findActivityInSchedule.ActualEndDate != null ? findActivityInSchedule.ActualEndDate.Value : findActivityInSchedule.SchEndDate.Value;
                findActivityInSchedule.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(findActivityInSchedule.BaseEndDate.Value, plusminusdaysend, holidays);
                //recalculate schedule
                var response = await ScheduleService.UpdateScheduleSActivityAsync(activity, AllActivitiesInSchedule, updateBaseDates);
                var activitiesToUpdate = response.Value;
                // set the visible ones to match the recalculated values
                foreach (var updateActivity in AllActivitiesThisMilestone)
                {
                    var findActivity = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == updateActivity.ScheduleAid);
                    //update the properties, not the reference, else it will fail next time
                    updateActivity.SchStartDate = findActivity.SchStartDate;
                    updateActivity.SchEndDate = findActivity.SchEndDate;
                    // updateActivity.BoolIgnoreNoWorkDays = findActivity.BoolIgnoreNoWorkDays;
                    // updateActivity.LagTime = findActivity.LagTime;
                    // updateActivity.Duration = findActivity.SchStartDate;
                    updateActivity.BaseStartDate = findActivity.BaseStartDate;
                    updateActivity.BaseEndDate = findActivity.BaseEndDate;
                }
            }
            else if (findActivityInSchedule.BoolIgnoreNoWorkDays != activity.BoolIgnoreNoWorkDays)
            {
                //ignore no work days change, update scheduled start or end date
                findActivityInSchedule.IsLocked = activity.BoolIgnoreNoWorkDays ? "T" : "F";
                findActivityInSchedule.BoolIgnoreNoWorkDays = activity.BoolIgnoreNoWorkDays;
                var findPredActivities = AllActivitiesInSchedule.Where(x => activity.PredIds.Contains((int)x.SactivityId)).ToList();
                var predEndDate = findPredActivities.Max(x => x.ActualEndDate != null ? x.ActualEndDate : x.SchEndDate);
                var newStartDate = !activity.BoolIgnoreNoWorkDays ? predEndDate == null ? activity.SchStartDate.Value : CalendarExtension.AddWorkingDays(predEndDate.Value, (int)activity.LagTime + 1, holidays) : predEndDate == null ? activity.SchStartDate.Value : predEndDate.Value.AddDays((int)activity.LagTime + 1);
                activity.SchStartDate = newStartDate;//If no preds, this is the existing start date
                findActivityInSchedule.SchStartDate = newStartDate;
                var newEndDate = !activity.BoolIgnoreNoWorkDays ? CalendarExtension.AddWorkingDays(newStartDate, (int)activity.Duration - 1, holidays) : newStartDate.AddDays((int)findActivityInSchedule.Duration - 1);
                findActivityInSchedule.SchEndDate = newEndDate;
                var plusminusdaysend = findActivityInSchedule.ActualEndDate != null ? findActivityInSchedule.ActualEndDate.Value : findActivityInSchedule.SchEndDate.Value;
                findActivityInSchedule.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(findActivityInSchedule.BaseEndDate.Value, plusminusdaysend, holidays);
                //recalculate schedule
                var response = await ScheduleService.UpdateScheduleSActivityAsync(activity, AllActivitiesInSchedule, updateBaseDates);
                var activitiesToUpdate = response.Value;
                // set the visible ones to match the recalculated values
                foreach (var updateActivity in AllActivitiesThisMilestone)
                {
                    var findActivity = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == updateActivity.ScheduleAid);
                    //update the properties, not the reference, else it will fail next time
                    updateActivity.SchStartDate = findActivity.SchStartDate;
                    updateActivity.SchEndDate = findActivity.SchEndDate;
                    // updateActivity.BoolIgnoreNoWorkDays = findActivity.BoolIgnoreNoWorkDays;
                    // updateActivity.LagTime = findActivity.LagTime;
                    // updateActivity.Duration = findActivity.SchStartDate;
                    updateActivity.BaseStartDate = findActivity.BaseStartDate;
                    updateActivity.BaseEndDate = findActivity.BaseEndDate;
                }
            }
            else if (findActivityInSchedule.LagTime != activity.LagTime)
            {
                //lag time changed, update start and end dates
                findActivityInSchedule.LagTime = activity.LagTime;
                var findPredecessors = activity.PredIds.ToList();
                if (findPredecessors.Any())
                {
                    var findPreds = AllActivitiesInSchedule.Where(x => x.SactivityId != null && findPredecessors.Contains((int)x.SactivityId)).ToList();
                    var findMaxPredecessorEndDate = findPreds.Max(x => x.SchEndDate);
                    //TODO: don't update these if actual date is entered
                    //TODO: what if they are trying to mark it as completed early??
                    findActivityInSchedule.SchStartDate = findActivityInSchedule.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findMaxPredecessorEndDate, (int)findActivityInSchedule.LagTime + 1, holidays) : findMaxPredecessorEndDate.Value.AddDays((int)findActivityInSchedule.LagTime + 1);
                    findActivityInSchedule.SchEndDate = findActivityInSchedule.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findActivityInSchedule.SchStartDate, (int)findActivityInSchedule.Duration - 1, holidays) : findActivityInSchedule.SchStartDate.Value.AddDays((int)findActivityInSchedule.Duration - 1);
                }
                var plusminusdaysend = findActivityInSchedule.ActualEndDate != null ? findActivityInSchedule.ActualEndDate.Value : findActivityInSchedule.SchEndDate.Value;
                findActivityInSchedule.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(findActivityInSchedule.BaseEndDate.Value, plusminusdaysend, holidays);
                //recalculate schedule
                var response = await ScheduleService.UpdateScheduleSActivityAsync(activity, AllActivitiesInSchedule, updateBaseDates);
                var activitiesToUpdate = response.Value;
                // set the visible ones to match the recalculated values
                foreach (var updateActivity in AllActivitiesThisMilestone)
                {
                    var findActivity = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == updateActivity.ScheduleAid);
                    //update the properties, not the reference, else it will fail next time
                    updateActivity.SchStartDate = findActivity.SchStartDate;
                    updateActivity.SchEndDate = findActivity.SchEndDate;
                    // updateActivity.BoolIgnoreNoWorkDays = findActivity.BoolIgnoreNoWorkDays;
                    // updateActivity.LagTime = findActivity.LagTime;
                    // updateActivity.Duration = findActivity.SchStartDate;
                    updateActivity.BaseStartDate = findActivity.BaseStartDate;
                    updateActivity.BaseEndDate = findActivity.BaseEndDate;
                }
            }
            else if (findActivityInSchedule.VarianceCode != activity.VarianceCode)
            {
                findActivityInSchedule.VarianceCode = activity.VarianceCode;
                //recalculate schedule
                try
                {
                    //updates front end, does not save to db
                    var response = await ScheduleService.UpdateScheduleSActivityAsync(activity, AllActivitiesInSchedule, updateBaseDates);
                    var activitiesToUpdate = response.Value;
                    // set the visible ones to match the recalculated values
                    foreach (var updateActivity in AllActivitiesThisMilestone)
                    {
                        var findActivity = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == updateActivity.ScheduleAid);
                        //update the properties, not the reference, else it will fail next time
                        updateActivity.SchStartDate = findActivity.SchStartDate;
                        updateActivity.SchEndDate = findActivity.SchEndDate;
                        // updateActivity.BoolIgnoreNoWorkDays = findActivity.BoolIgnoreNoWorkDays;
                        // updateActivity.LagTime = findActivity.LagTime;
                        // updateActivity.Duration = findActivity.SchStartDate;
                        updateActivity.BaseStartDate = findActivity.BaseStartDate;
                        updateActivity.BaseEndDate = findActivity.BaseEndDate;
                    }

                }
                catch (Exception ex)
                {
                    var debug = ex.Message;
                }
            }
        }                   
    }
    private async Task SchStartChanged(DateTime? newStartDate, ScheduleSactivityDto activity)
    {
        if (activity != null && activity.SchStartDate != newStartDate)
        {
            activity.SchStartDate = newStartDate;
            UnsavedChanges = true;
            var updateBaseDates = false;

            var findActivityInSchedule = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == activity.ScheduleAid);
            if (findActivityInSchedule.SchStartDate != activity.SchStartDate)
            {
                //sch start date chagned, change end date based on duration
                if (activity.VarianceCode == null && activity.SchStartDate > findActivityInSchedule.SchStartDate)
                {
                    await Dialogs.AlertAsync("You must enter a variance!");
                    activity.SchStartDate = findActivityInSchedule.SchStartDate;//Cancel change
                    return;
                }
                var checkHoliday = (await ScheduleService.CheckHolidayAsync(activity.SchStartDate.Value)).Value;
                if (checkHoliday && !activity.BoolIgnoreNoWorkDays)
                {
                    var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");
                    if (!confirm)
                    {
                        activity.SchStartDate = findActivityInSchedule.SchStartDate;//Cancel change
                        return;
                    }
                    activity.IsLocked = "T";
                    activity.BoolIgnoreNoWorkDays = true;
                    findActivityInSchedule.IsLocked = "T";
                    findActivityInSchedule.BoolIgnoreNoWorkDays = true;//set ignore no work days if they choose to ignore the holiday
                }
                findActivityInSchedule.SchStartDate = activity.SchStartDate;
                findActivityInSchedule.SchEndDate = findActivityInSchedule.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findActivityInSchedule.SchStartDate, (int)findActivityInSchedule.Duration - 1, holidays) : findActivityInSchedule.SchStartDate.Value.AddDays((int)findActivityInSchedule.Duration - 1);
                var plusminusdaysend = findActivityInSchedule.ActualEndDate != null ? findActivityInSchedule.ActualEndDate.Value : findActivityInSchedule.SchEndDate.Value;
                findActivityInSchedule.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(findActivityInSchedule.BaseEndDate.Value, plusminusdaysend, holidays);
                //recalculate schedule
                var response = await ScheduleService.UpdateScheduleSActivityAsync(findActivityInSchedule, AllActivitiesInSchedule, updateBaseDates);
                var activitiesToUpdate = response.Value;
                //TODO: now need to set the milestone and filtered ones to match
                foreach (var updateActivity in AllActivitiesThisMilestone)
                {
                    var findActivity = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == updateActivity.ScheduleAid);
                    //update the properties, not the reference, else it will fail next time
                    updateActivity.SchStartDate = findActivity.SchStartDate;
                    updateActivity.SchEndDate = findActivity.SchEndDate;
                    // updateActivity.BoolIgnoreNoWorkDays = findActivity.BoolIgnoreNoWorkDays;
                    // updateActivity.LagTime = findActivity.LagTime;
                    // updateActivity.Duration = findActivity.SchStartDate;
                    updateActivity.BaseStartDate = findActivity.BaseStartDate;
                    updateActivity.BaseEndDate = findActivity.BaseEndDate;
                }
                //FilteredActivities = AllActivitiesThisMilestone;
            }
        }
    }
    private async Task SchEndChanged(DateTime? newEndDate, ScheduleSactivityDto activity)
    {
        if (activity != null && activity.SchEndDate != newEndDate)
        {
            activity.SchEndDate = newEndDate;
            UnsavedChanges = true;
            var updateBaseDates = false;
            var findActivityInSchedule = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == activity.ScheduleAid);
            if (findActivityInSchedule.SchEndDate != activity.SchEndDate)
            { 
                if (activity.VarianceCode == null && activity.SchEndDate > findActivityInSchedule.SchEndDate)
                {
                    await Dialogs.AlertAsync("You must enter a variance!");
                    activity.SchEndDate = findActivityInSchedule.SchEndDate;//Cancel change
                    return;
                }
                var checkHoliday = (await ScheduleService.CheckHolidayAsync(activity.SchStartDate.Value)).Value;
                if (checkHoliday && !activity.BoolIgnoreNoWorkDays)
                {
                    var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");
                    if (!confirm)
                    {
                        activity.SchStartDate = findActivityInSchedule.SchStartDate;//Cancel change
                        return;
                    }
                    activity.IsLocked = "T";
                    activity.BoolIgnoreNoWorkDays = true;
                    findActivityInSchedule.IsLocked = "T";
                    findActivityInSchedule.BoolIgnoreNoWorkDays = true;//set ignore no work days if they choose to ignore the holiday
                }
                findActivityInSchedule.SchEndDate = activity.SchEndDate;
                //recalculate schedule
                var response = await ScheduleService.UpdateScheduleSActivityAsync(activity, AllActivitiesInSchedule, updateBaseDates);
                var activitiesToUpdate = response.Value;
                // set the visible ones to match the recalculated values
                foreach (var updateActivity in AllActivitiesThisMilestone)
                {
                    var findActivity = AllActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == updateActivity.ScheduleAid);
                    //update the properties, not the reference, else it will fail next time
                    updateActivity.SchStartDate = findActivity.SchStartDate;
                    updateActivity.SchEndDate = findActivity.SchEndDate;
                    // updateActivity.BoolIgnoreNoWorkDays = findActivity.BoolIgnoreNoWorkDays;
                    // updateActivity.LagTime = findActivity.LagTime;
                    // updateActivity.Duration = findActivity.SchStartDate;
                    updateActivity.BaseStartDate = findActivity.BaseStartDate;
                    updateActivity.BaseEndDate = findActivity.BaseEndDate;
                }
            }
        }
    }
    private async void HandleValidEditActivitySubmit(ResponseModel<ScheduleSactivityDto> response)
    {
        EditScheduleActivity.Hide();
        if (response != null )
        {
            // Update the UI to reflect the changes
            // You may need to update the state or re-fetch data to ensure the UI reflects the updated activity
            // For example, you can update the activity directly in the FilteredActivities list                
            // var index = FilteredActivities.FindIndex(a => a.ScheduleAid == response.Value.ScheduleAid);                
            // if (index != -1)                
            // {                    
            //     FilteredActivities[index] = response.Value;                  
            // }
            //SUCCESS                
            ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);            
            // Optionally, you may call StateHasChanged() to force the UI to re-render

            //12/19 refresh loses unsaved changes, don't do it
            //wait 2 seconds then refresh                        
            // await Task.Delay(2000);                            
            // NavManager.NavigateTo(NavManager.Uri, forceLoad: true);                 
        }
        
        StateHasChanged();
    }


    // private async Task UpdateItem(ScheduleSactivityDto args)
    // {

    //     if (item.ScheduleSactivityId != null)
    //     {
    //         UnsavedChanges = true; //TODO: check something actually changed
    //         var getCalendar = (await ScheduleService.GetHolidaysAsync()).Value;
    //         var holidays = getCalendar.Select(x => x.WorkDate).ToList();
    //         bool updateBaseDates = false;
    //         //update/complete the activity
    //         //var allActivitiesInSchedule = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Select(x => x.ScheduleSactivity).ToList();

    //         //adjust other fields based on what changed
    //         var updateItem1 = allActivitiesInSchedule.FirstOrDefault(x => x.ScheduleAid == item.ScheduleSactivity.ScheduleAid);
    //         if (editedField == "ScheduleSactivity.BaseStartDate")
    //         {
    //             //TODO: confirm dialog if start date holiday
    //             //base start date chagned, need to change end date based on duration
    //             updateItem1.BaseStartDate = item.ScheduleSactivity.BaseStartDate;
    //             updateItem1.BaseEndDate = updateItem1.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)updateItem1.BaseStartDate, (int)updateItem1.Duration - 1, holidays) : updateItem1.BaseStartDate.Value.AddDays((int)updateItem1.Duration - 1);
    //             var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
    //             updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);
    //             updateBaseDates = true;
    //         }
    //         else if (editedField == "ScheduleSactivity.BaseEndDate")
    //         {
    //             updateItem1.BaseEndDate = item.ScheduleSactivity.BaseEndDate;
    //             var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
    //             updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);
    //             updateBaseDates = true;
    //         }
    //         else if (editedField == "ScheduleSactivity.SchStartDate")
    //         {
    //             //sch start date chagned, change end date based on duration
    //             if (item.ScheduleSactivity.VarianceCode == null && item.ScheduleSactivity.SchStartDate > updateItem1.SchStartDate)
    //             {
    //                 await Dialogs.AlertAsync("You must enter a variance!");
    //                 return;
    //             }
    //             var checkHoliday = (await ScheduleService.CheckHolidayAsync(item.ScheduleSactivity.SchStartDate.Value)).Value;
    //             if (checkHoliday && !item.ScheduleSactivity.BoolIgnoreNoWorkDays)
    //             {
    //                 //TODO: also check what's being updated, it's annoying if it's a different field
    //                 var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");
    //                 if (!confirm)
    //                 {
    //                     return;
    //                 }
    //                 updateItem1.IsLocked = "T";
    //                 updateItem1.BoolIgnoreNoWorkDays = true;//set ignore no work days if they choose to ignore the holiday
    //             }
    //             updateItem1.SchStartDate = item.ScheduleSactivity.SchStartDate;
    //             updateItem1.SchEndDate = updateItem1.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)updateItem1.SchStartDate, (int)updateItem1.Duration - 1, holidays) : updateItem1.SchStartDate.Value.AddDays((int)updateItem1.Duration - 1);
    //             var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
    //             updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);
    //         }
    //         else if (editedField == "ScheduleSactivity.Duration")
    //         {
    //             //duration chagned, update sch end date
    //             updateItem1.Duration = item.ScheduleSactivity.Duration;
    //             updateItem1.SchEndDate = updateItem1.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)updateItem1.SchStartDate, (int)updateItem1.Duration - 1, holidays) : updateItem1.SchStartDate.Value.AddDays((int)updateItem1.Duration - 1);
    //             var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
    //             updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);
    //         }
    //         else if (editedField == "ScheduleSactivity.BoolIgnoreNoWorkDays")
    //         {
    //             //ignore no work days change, update scheduled start or end date
    //             updateItem1.IsLocked = item.ScheduleSactivity.BoolIgnoreNoWorkDays ? "T" : "F";
    //             updateItem1.BoolIgnoreNoWorkDays = item.ScheduleSactivity.BoolIgnoreNoWorkDays;
    //             var findPredActivities = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Where(x => item.PredecessorIds.Contains((int)x.ScheduleSactivity.SactivityId)).ToList();
    //             var predEndDate = findPredActivities.Max(x => x.ScheduleSactivity.ActualEndDate != null ? x.ScheduleSactivity.ActualEndDate : x.ScheduleSactivity.SchEndDate);
    //             var newStartDate = !item.ScheduleSactivity.BoolIgnoreNoWorkDays ? predEndDate == null ? item.ScheduleSactivity.SchStartDate.Value : CalendarExtension.AddWorkingDays(predEndDate.Value, (int)item.ScheduleSactivity.LagTime + 1, holidays) : predEndDate == null ? item.ScheduleSactivity.SchStartDate.Value : predEndDate.Value.AddDays((int)item.ScheduleSactivity.LagTime + 1);
    //             item.ScheduleSactivity.SchStartDate = newStartDate;//If no preds, this is the existing start date
    //             updateItem1.SchStartDate = newStartDate;
    //             var newEndDate = !item.ScheduleSactivity.BoolIgnoreNoWorkDays ? CalendarExtension.AddWorkingDays(newStartDate, (int)item.ScheduleSactivity.Duration - 1, holidays) : newStartDate.AddDays((int)updateItem1.Duration - 1);
    //             updateItem1.SchEndDate = newEndDate;
    //             var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
    //             updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);
    //         }
    //         else if (editedField == "ScheduleSactivity.LagTime")
    //         {
    //             //lag time changed, update start and end dates
    //             updateItem1.LagTime = item.ScheduleSactivity.LagTime;
    //             var findPredecessors = item.PredecessorIds.ToList();
    //             if (findPredecessors.Any())
    //             {
    //                 var findPreds = allActivitiesInSchedule.Where(x => x.SactivityId != null && findPredecessors.Contains((int)x.SactivityId)).ToList();
    //                 var findMaxPredecessorEndDate = findPreds.Max(x => x.SchEndDate);
    //                 //TODO: don't update these if actual date is entered
    //                 //TODO: what if they are trying to mark it as completed early??
    //                 updateItem1.SchStartDate = updateItem1.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)findMaxPredecessorEndDate, (int)updateItem1.LagTime + 1, holidays) : findMaxPredecessorEndDate.Value.AddDays((int)updateItem1.LagTime + 1);
    //                 updateItem1.SchEndDate = updateItem1.IsLocked != "T" ? CalendarExtension.AddWorkingDays((DateTime)updateItem1.SchStartDate, (int)updateItem1.Duration - 1, holidays) : updateItem1.SchStartDate.Value.AddDays((int)updateItem1.Duration - 1);
    //             }
    //             var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
    //             updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);
    //         }
    //         else if (editedField == "ScheduleSactivity.BoolComplete")
    //         {
    //             if (item.ScheduleSactivity.BoolComplete == true)//temp to allow reapprove for testing
    //             {
    //                 if (item.ScheduleSactivity.ActualStartDate == null)
    //                 {
    //                     await Dialogs.AlertAsync("Activity must be started before being completed.");
    //                     item.ScheduleSactivity.BoolComplete = false;
    //                     updateItem1.BoolComplete = false;
    //                     return;
    //                 }
    //                 else if (!userIsDirectorOrAdmin && (CalendarExtension.AddWorkingDays(item.ScheduleSactivity.SchEndDate.Value.Date, 1, holidays) < DateTime.Now.Date || (CalendarExtension.AddWorkingDays(item.ScheduleSactivity.SchEndDate.Value.Date, 1, holidays) == DateTime.Now.Date && DateTime.Now.Hour > 10)))
    //                 {
    //                     //basedateeditable is user is in construction dir role
    //                     await Dialogs.AlertAsync("Activity is late! You must select a variance and then adjust the scheduled dates. Contact Construction Director to override this if needed");
    //                     item.ScheduleSactivity.BoolComplete = false;
    //                     updateItem1.BoolComplete = false;
    //                     return;
    //                 }
    //                 else if (userIsDirectorOrAdmin && (CalendarExtension.AddWorkingDays(item.ScheduleSactivity.SchEndDate.Value.Date, 1, holidays) < DateTime.Now.Date || (CalendarExtension.AddWorkingDays(item.ScheduleSactivity.SchEndDate.Value.Date, 1, holidays) == DateTime.Now.Date && DateTime.Now.Hour > 10)))
    //                 {
    //                     //TODO: Holiday needs to be included here
    //                     var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
    //                     if (!confirm)
    //                     {
    //                         item.ScheduleSactivity.BoolComplete = false;
    //                         updateItem1.BoolComplete = false;
    //                         return;
    //                     }
    //                 }
    //                 updateItem1.ActualStartDate = item.ScheduleSactivity.SchStartDate;
    //                 updateItem1.ActualEndDate = item.ScheduleSactivity.SchEndDate;
    //                 updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, updateItem1.ActualEndDate.Value, holidays);
    //                 updateItem1.ActualDuration = CalendarExtension.WorkingDaysDuration(updateItem1.ActualStartDate.Value, updateItem1.ActualEndDate.Value);
    //                 updateItem1.Calduration = (updateItem1.ActualEndDate.Value - updateItem1.ActualStartDate.Value).Days;
    //             }
    //         }
    //         else if (editedField == "ScheduleSactivity.BoolStarted")
    //         {
    //             if (item.ScheduleSactivity.BoolStarted == true)//temp to allow reapprove for testing
    //             {
    //                 if (!userIsDirectorOrAdmin && (item.ScheduleSactivity.SchStartDate.Value.Date < DateTime.Now.Date || (item.ScheduleSactivity.SchStartDate.Value.Date == DateTime.Now.Date && DateTime.Now.Hour > 10)))
    //                 {
    //                     //TODO: if it's construction director role, allow check complete after the deadline, maybe just warning dialog
    //                     await Dialogs.AlertAsync("You are late! You must select a variance and then adjust the scheduled dates! If the scheduled activity was started on time, but you forgot to mark it, you must contact Construction Director");
    //                     item.ScheduleSactivity.BoolStarted = false;
    //                     updateItem1.BoolStarted = false;
    //                     return;
    //                 }
    //                 else if (userIsDirectorOrAdmin && (item.ScheduleSactivity.SchStartDate.Value.Date < DateTime.Now.Date || (item.ScheduleSactivity.SchStartDate.Value.Date == DateTime.Now.Date && DateTime.Now.Hour > 10)))
    //                 {
    //                     var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
    //                     if (!confirm)
    //                     {
    //                         item.ScheduleSactivity.BoolStarted = false;
    //                         updateItem1.BoolStarted = false;
    //                         return;
    //                     }
    //                 }
    //                 updateItem1.ActualStartDate = item.ScheduleSactivity.SchStartDate;
    //                 updateItem1.BoolStarted = true;
    //                 //TODO: require variance and adjust schedule if it's not started by 10 am on the day it's schedued
    //             }
    //             else
    //             {
    //                 updateItem1.ActualStartDate = null;//reset it
    //             }
    //         }
    //         else if (editedField == "ScheduleSactivity.ActualEndDate")
    //         {
    //             ///actual end changed
    //             if (updateItem1.ActualEndDate != null)
    //             {
    //                 updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, updateItem1.ActualEndDate.Value, holidays);
    //                 updateItem1.ActualDuration = CalendarExtension.WorkingDaysDuration(updateItem1.ActualStartDate.Value, updateItem1.ActualEndDate.Value);
    //                 updateItem1.Calduration = (updateItem1.ActualEndDate.Value - updateItem1.ActualStartDate.Value).Days;
    //             }
    //         }
    //         else if (editedField == "Predecessors")
    //         {
    //             //predecessor Id should be the list of ints, predecessors should be the strings
    //             item.ScheduleSactivity.PredIds = item.PredecessorIds;//TODO: fix
    //             item.ScheduleSactivityPreds = item.PredecessorIds.Select(x => new ScheduleSactivityPredDto()
    //                 {
    //                     ScheduleAid = (int)item.ScheduleSactivityId,
    //                     PredSactivityId = x,
    //                 }).ToList();
    //             updateItem1.Predecessors = item.ScheduleSactivityPreds;
    //             updateItem1.PredIds = item.PredecessorIds;
    //             //todo: don't adjust sch dates if there's actul date
    //             var priorActivities = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivityId != null && x.ScheduleSactivity.Seq < ItemToEdit.ScheduleSactivity.Seq)).Select(x => x.Sactivity);
    //             var selectPreds = priorActivities.Where(x => item.PredecessorIds.Contains(x.SactivityId)).ToList();
    //             item.Predecessors = string.Join(",", selectPreds.Select(x => x.ActivityName));
    //             item.PredecessorIds = selectPreds.Select(x => x.SactivityId).ToList();
    //             var allActivitiesInSchedule2 = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).ToList();
    //             var updateItem2 = allActivitiesInSchedule2.FirstOrDefault(x => x.ScheduleSactivity.ScheduleAid == item.ScheduleSactivity.ScheduleAid);
    //             updateItem2.Predecessors = item.Predecessors;
    //             updateItem2.PredecessorIds = item.PredecessorIds;
    //             var findNewPredActivities = SelectedScheduleData.SelectMany(x => x.Children.Where(x => x.ScheduleSactivity != null)).Where(x => item.PredecessorIds.Contains((int)x.ScheduleSactivity.SactivityId)).ToList();
    //             if (findNewPredActivities.Any())
    //             {
    //                 var predEndDate = findNewPredActivities.Max(x => x.ScheduleSactivity.ActualEndDate != null ? x.ScheduleSactivity.ActualEndDate : x.ScheduleSactivity.SchEndDate);
    //                 var newStartDate = !item.ScheduleSactivity.BoolIgnoreNoWorkDays ? CalendarExtension.AddWorkingDays(predEndDate.Value, (int)item.ScheduleSactivity.LagTime + 1, holidays) : predEndDate.Value.AddDays((int)item.ScheduleSactivity.LagTime + 1);
    //                 item.ScheduleSactivity.SchStartDate = newStartDate;
    //                 updateItem1.SchStartDate = newStartDate;
    //                 var newEndDate = !item.ScheduleSactivity.BoolIgnoreNoWorkDays ? CalendarExtension.AddWorkingDays(newStartDate, (int)item.ScheduleSactivity.Duration - 1, holidays) : newStartDate.AddDays((int)updateItem1.Duration - 1);
    //                 updateItem1.SchEndDate = newEndDate;
    //             }
    //             else
    //             {
    //                 updateItem1.SchStartDate = item.ScheduleSactivity.SchStartDate;
    //                 updateItem1.SchEndDate = item.ScheduleSactivity.SchEndDate;
    //             }
    //             var plusminusdaysend = updateItem1.ActualEndDate != null ? updateItem1.ActualEndDate.Value : updateItem1.SchEndDate.Value;
    //             updateItem1.PlusminusDays = CalendarExtension.PlusMinusDaysDuration(updateItem1.BaseEndDate.Value, plusminusdaysend, holidays);

    //         }
    //         else if (editedField == "ScheduleSactivity.SchEndDate")
    //         {
    //             if (item.ScheduleSactivity.VarianceCode == null && item.ScheduleSactivity.SchEndDate > updateItem1.SchEndDate)
    //             {
    //                 await Dialogs.AlertAsync("You must enter a variance!");
    //                 return;
    //             }
    //             updateItem1.SchEndDate = item.ScheduleSactivity.SchEndDate;
    //         }
    //         else
    //         {
    //             // updateItem1.SchEndDate = item.ScheduleSactivity.SchEndDate;


    //             // updateItem1.ActualStartDate = item.ScheduleSactivity.ActualStartDate;
    //             updateItem1.VarianceCode = item.ScheduleSactivity.VarianceCode;
    //         }

    //         try
    //         {
    //             //updates front end, does not save to db
    //             var response = await ScheduleService.UpdateScheduleSActivityAsync(updateItem1, AllActivitiesInSchedule, updateBaseDates);
    //             var activitiesToUpdate = response.Value;
    //         }
    //         catch (Exception ex)
    //         {
    //             var debug = ex.Message;
    //         }
    //     }
    // }


    public async void Save()
    {
        var activitiesToUpdate = AllActivitiesInSchedule;
        var response = await ScheduleService.UpdateScheduleSactivitysAsync(activitiesToUpdate);
        var milestonesToUpdate = AllActivitiesInSchedule.Where(x => x.ScheduleM != null).Select(x => x.ScheduleM).ToList();
       // var response2 = await ScheduleService.UpdateScheduleMilestonesAsync(milestonesToUpdate);
       //milestones and schedule being saved in update schedule sactivites
        //TODO: update w
        // var response3 = await ScheduleService.UpdateScheduleAsync(SelectedSchedule);

        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        if (response.IsSuccess)
        {
            UnsavedChanges = false;
        }
        StateHasChanged();
    }
    public async void CancelChanges()
    {
        AllActivitiesThisMilestone = (await ScheduleService.GetScheduleActivitiesForMilestoneAsync(scheduleMid)).Value;
        var scheduleId = AllActivitiesThisMilestone.FirstOrDefault()?.ScheduleM.ScheduleId;
        AllActivitiesInSchedule = (await ScheduleService.GetScheduleActivitiesForScheduleAsync((int)scheduleId)).Value;
        AllActivitiesThisMilestone = AllActivitiesInSchedule.Where(x => x.ScheduleMid == scheduleMid).ToList();//TODO: fix
        FilteredActivities = AllActivitiesThisMilestone;
        ShowSuccessOrErrorNotification("Cleared Changes", true);
        UnsavedChanges = false;
        StateHasChanged();
    }
    //NOTIFICATION
    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        if (NotificationReference != null)
        {
            NotificationReference.Show(new NotificationModel
                {
                    Text = message,
                    ThemeColor = (isSuccess ? "success" : "error")
                });
        }
        else
        {
            // Handle the case where NotificationReference is null
            // For example, log an error or display a default notification
        }
    }


}
