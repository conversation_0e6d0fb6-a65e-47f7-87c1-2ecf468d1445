﻿@page "/budget"
@inject BudgetService BudgetService
@inject PoService PoService
@inject TradeService TradeService
@inject SubdivisionService SubdivisionService
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject ProtectedSessionStorage ProtectedSessionStore
@inject AuthenticationStateProvider AuthenticationStateProvider
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ReadOnly, Accounting")]
@using System.Diagnostics;
@using ERP.Data.Models
@using System.Collections.ObjectModel
@using Telerik.DataSource.Extensions
@implements IDisposable

<style>
    .k-checkbox:indeterminate, .k-checkbox.k-indeterminate {
        border-color: #f4cd64;
        color: white;
        background-color: #f4cd64;
    }

    .k-checkbox:checked, .k-checkbox.k-checked {
        border-color: #32a852;
        color: white;
        background-color: #32a852;
    }
    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto",sans-serif;
        font-size: .75rem;
    }

    .k-table-td { /*these settings are to prevent the 0 width columns from affecting the column height*/
        padding: 4px !important;
        font-size: 11px;
        height: 30px !important;
        white-space: nowrap !important;
        overflow: visible;
    }

</style>

<TelerikTooltip TargetSelector=".tooltip-target" />
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<TelerikLoaderContainer Visible="@IsLoading" Text="Please wait..." />
<PageTitle>Purchasing | Budget</PageTitle>

<div class="container-fluid flex">
    <ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="true" ShowJob="true" ShowSubdivision="true"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
     <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Budget</h7>
                </div>
            </div>
     </div>

     <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item active">Budget</li>
     </ol>

    <div class="row d-flex">

        @if (BudgetData == null)
        {
            @* <p><em>Loading...</em></p>*@

        }
        @if(IsBudgetLoading)
        {
            <p><em>Loading...</em></p>
            <TelerikLoader></TelerikLoader>
        }
        else
        {
            <div class="col-6" style="margin-bottom:1rem">
                <TelerikTabStrip>
                    <TabStripTab Title="Budgets">
                        <h4 class="page-title mt-2">Budgets</h4>
                        <TelerikTreeList Data="@BudgetData"
                        OnStateInit="@((TreeListStateEventArgs<CombinedPOBudgetTreeModel> args) => OnStateInitHandler(args))"
                        SelectionMode="@TreeListSelectionMode.Multiple"
                        @bind-SelectedItems="@SelectedItems"
                        IdField="Id"
                        ItemsField="Children"                                             
                        @ref="@BudgetTreeList"
                        OnRowClick="@RowSelectedHandler"
                        EditMode="@TreeListEditMode.Inline"
                        ConfirmDelete="true"
                        OnDelete="@DeleteHandler"
                        Height="800px"
                        Width="100%">
                            <TreeListColumns>
                                @*  <TreeListCheckboxColumn SelectChildren="true" /> *@
                                <TreeListColumn Field="SearchTags" Width="0px"></TreeListColumn>
                                <TreeListColumn Field="IsIssued" Title="" Visible="true" Editable="true" Width="40px">
                                    <Template>
                                        @{
                                            SelectedRow = context as CombinedPOBudgetTreeModel;
                                            bool enabled = SelectedRow.IssueEnabled != false;
                                            bool indeterminate = SelectedRow.Indeterminate;
                                            <TelerikCheckBox Indeterminate="@indeterminate" Enabled=@enabled @bind-Value="SelectedRow.IsIssued" OnChange="@ChangeSelected"></TelerikCheckBox>
                                        }
                                    </Template>
                                </TreeListColumn>
                                <TreeListColumn Field="JobNumber" Title="Job / Option / Item" Expandable="true" Editable="false" Visible="true">
                                    <Template>
                                        @{
                                            var item = context as CombinedPOBudgetTreeModel;
                                            if (item.Estheader != null)
                                            {
                                                if (item.Estheader.ReferenceTypeNavigation != null)
                                                {
                                                    @($"{item.JobNumber} - {item.Estheader.ReferenceTypeNavigation.ReferenceType1} - {item.Estheader.ReferenceNumber} - {item.Estheader.EstimateNumber}")
                                                }
                                            }
                                            else if (item.Estoption != null && item.Estactivity == null && item.Estdetail == null)
                                            {
                                                if (item.HasChildren && item.Children.Any(x => (x.Estactivity != null && x.Estactivity.SelectedVendor == null) || (x.HasChildren && x.Children.Any(y => y.Estdetail != null && !string.IsNullOrWhiteSpace(y.Estdetail.DisplayErrors)))))
                                                {
                                                    <TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Warning" Icon="@FontIcon.WarningTriangle" />
                                                }
                                                @($"{item.Estoption.OptionNumber} - {item.Estoption.OptionDesc}")
                                            }
                                            else if (item.Estactivity != null && item.Estdetail == null)
                                            {
                                                if (item.Estactivity.SelectedVendor == null)
                                                {
                                                    <TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Warning" Icon="@FontIcon.WarningTriangle" />
                                                }
                                                if (item.HasChildren && item.Children.Any(x => !string.IsNullOrWhiteSpace(x.Estdetail.DisplayErrors)))
                                                {
                                                    <TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Warning" Icon="@FontIcon.WarningTriangle" />
                                                }
                                                @($"{item.Estactivity.BomClass}")
                                            }
                                            else if (item.ReleaseCode != null && item.Estdetail == null)
                                            {
                                                @($"Release: {item.ReleaseCode}")
                                            }
                                            else if (item.Estdetail != null)
                                            {
                                                if (!string.IsNullOrWhiteSpace(item.Estdetail.DisplayErrors))
                                                {
                                                    <TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Warning" Icon="@FontIcon.WarningTriangle" />
                                                }

                                                if (string.IsNullOrEmpty(item.Estdetail?.Estoption?.OptionDesc))
                                                {
                                                    @($"{item.Estdetail?.Estoption?.OptionNumber} - {item.Estdetail?.ItemDesc}")
                                                }
                                                else
                                                {
                                                    @($"{item.Estdetail?.Estoption?.OptionDesc}")
                                                }                                              
                                            }
                                        }
                                    </Template>
                                </TreeListColumn>
                                <TreeListColumn Field="EstheaderId" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="EstoptionId" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="OptionNumber" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="OptionDesc" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="EstactivityId" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="BomClass" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="EstdetailId" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="ItemNumber" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="ItemDesc" Visible="false"></TreeListColumn>
                                <TreeListCommandColumn>
                                    @{
                                        var row = context as CombinedPOBudgetTreeModel;
                                        if (row.Estdetail != null)
                                        {
                                            @if (row.IssueEnabled == true && AllowEdit)
                                            {
                                                <TreeListCommandButton Command="Delete" Title="Delete" Class="tooltip-target  k-button-danger" Icon="@FontIcon.Trash"></TreeListCommandButton>
                                                <TreeListCommandButton Title="Copy Item" Class="tooltip-target k-button-success" OnClick="@CopyItem" Icon="@FontIcon.Copy"></TreeListCommandButton>
                                                <TreeListCommandButton OnClick="@RefreshCosts">Refresh Costs</TreeListCommandButton>
                                                <TreeListCommandButton Title="Issue Budget" Class="tooltip-target" OnClick="@IssueBudget">Issue</TreeListCommandButton>
                                            }
                                            else if(AllowEdit && row.Estdetail.EstjcedetailId != null  && (row.Estdetail.Estjcedetail.IsCancelled != "T" || row.Estdetail.Estjcedetail.EstimateExported == "T")) 
                                            {
                                                <TreeListCommandButton Title="Copy Item" Class="tooltip-target k-button-success" OnClick="@CopyItem" Icon="@FontIcon.Copy"></TreeListCommandButton>
                                                <TreeListCommandButton Title="Cancel Budget" Class="tooltip-target" OnClick="@CancelBudget">Cancel</TreeListCommandButton>
                                            }
                                            else if (row.Estdetail.EstjcedetailId != null && (row.Estdetail.Estjcedetail.IsCancelled == "T"))
                                            {
                                                <span style="color:red">Cancelled</span>
                                            }
                                        }

                                        else if (row.Estactivity != null)
                                        {
                                            if (!row.IsIssued && !row.Indeterminate && AllowEdit)
                                            {
                                                <TreeListCommandButton Title="Delete" Command="Delete" Icon="@FontIcon.Trash" Class="tooltip-target k-button-danger"></TreeListCommandButton>
                                            }                            
                                            if (row.IssueEnabled == true && AllowEdit)
                                            {
                                                <TreeListCommandButton OnClick="@RefreshSupplier">Refresh Supplier</TreeListCommandButton>
                                                <TreeListCommandButton OnClick="@RefreshCosts">Refresh Costs</TreeListCommandButton>
                                            }
                                        }
                                        else if (row.Estoption != null)
                                        {
                                            if(AllowEdit)
                                            {
                                                <TreeListCommandButton Title="Add Item" Class="tooltip-target k-button-add" OnClick="@ShowAddItemToBudget" Icon="@FontIcon.Plus"></TreeListCommandButton>
                                                if (!row.IsIssued && !row.Indeterminate)
                                                {
                                                    <TreeListCommandButton Title="Delete" Command="Delete" Class="tooltip-target k-button-danger" Icon="@FontIcon.Trash"></TreeListCommandButton>
                                                }
                                                if (row.IssueEnabled == true)
                                                {
                                                    <TreeListCommandButton OnClick="@RefreshSupplier">Refresh Supplier</TreeListCommandButton>
                                                    <TreeListCommandButton OnClick="@RefreshCosts">Refresh Costs</TreeListCommandButton>
                                                }
                                            }
                                        }
                                        else 
                                        {
                                            if(AllowEdit)
                                            {
                                                <TreeListCommandButton Title="Add Item" Class="tooltip-target k-button-add" OnClick="@ShowAddItemToBudget" Icon="@FontIcon.Plus"></TreeListCommandButton>
                                                if (!row.IsIssued && !row.Indeterminate)
                                                {
                                                    //only delete if there are no issued items
                                                    <TreeListCommandButton Command="Delete" Title="Delete" Class="tooltip-target k-button-danger" Icon="@FontIcon.Trash"></TreeListCommandButton>
                                                }
                                            }
                                        }
                                    }
                                </TreeListCommandColumn>
                            </TreeListColumns>
                            <TreeListToolBarTemplate>
                                <TreeListSearchBox />
                                <TelerikDropDownList Data="@SortByOptions"
                                @bind-Value="SelectedSort"
                                OnChange="@ChangeSortHandler"></TelerikDropDownList>
                                @if(AllowEdit)
                                {
                                    <TreeListCommandButton Title="Issue Budget" Class="tooltip-target k-button-success" Command="IssueBudgets" OnClick="@IssueBudgets">Issue Budgets</TreeListCommandButton>
                                    <TreeListCommandButton Title="Add Estimate" Class="tooltip-target k-button-add" Command="AddEstimate" Icon="@FontIcon.Plus" OnClick="@ShowAddEstimate">Add Estimate</TreeListCommandButton>
                                    <TreeListCommandButton Title="Refresh Selected Costs" Class="tooltip-target" OnClick="@RefreshCosts">Refresh Costs</TreeListCommandButton>
                                    <TreeListCommandButton Title="Refresh Selected Supplier" Class="tooltip-target" OnClick="@RefreshSupplier">Refresh Supplier</TreeListCommandButton>
                                }
                                @{
                                    <span>
                                        @($"Total Budget: {TotalJobBudget:C}  Total Issued: {TotalIssuedBudget:C}")
                                    </span> 
                                }
                                @* <TreeListCommandButton Command="AddItems" OnClick="@ShowAddItemToBudget" >Add Items</TreeListCommandButton> *@
                            </TreeListToolBarTemplate>
                        </TelerikTreeList>

                    </TabStripTab>
                    <TabStripTab Title="Pending / Custom Estimates">
                        <h4 class="page-title mt-2">Pending Estimates</h4>
                        <TelerikTreeList Data="@PendingCustomEstimateData"
                        SelectionMode="@TreeListSelectionMode.Multiple"
                        IdField="Id"
                        ItemsField="Children"
                        @ref="@PendingTreeList"
                        OnRowClick="@RowSelectedHandler"
                        OnStateInit="@((TreeListStateEventArgs<CombinedPOBudgetTreeModel> args) => OnStateInitHandler(args))"
                        EditMode="@TreeListEditMode.Inline"
                        ConfirmDelete="true"
                        OnDelete="@DeleteHandler"
                        Height="800px"
                        Width="100%">
                            <TreeListColumns>
                                <TreeListColumn Field="SearchTags" Width="0px"></TreeListColumn>
                                <TreeListColumn Field="IsIssued" Visible="true" Editable="true" Width="40px">
                                    <Template>
                                        @{
                                            SelectedRow = context as CombinedPOBudgetTreeModel;
                                            bool enabled = SelectedRow.IssueEnabled != false;
                                            bool indeterminate = SelectedRow.Indeterminate;
                                            <TelerikCheckBox Indeterminate="@indeterminate" Enabled=@enabled @bind-Value="SelectedRow.IsIssued" OnChange="@ChangeSelected"></TelerikCheckBox>
                                        }
                                    </Template>
                                </TreeListColumn>
                                <TreeListColumn Field="JobNumber" Title="Job / Option / Activity / Item" Expandable="true" Editable="false" Visible="true">
                                    <Template>
                                        @{
                                            var item = context as CombinedPOBudgetTreeModel;
                                            if (item.Estheader != null)
                                            {
                                                @($"{item.Estheader.JobNumber} - {item.Estheader.EstimateDescPe} - {item.Estheader.ReferenceNumber} - {item.Estheader.ReferenceDesc}")
                                            }
                                            else if (item.Estoption != null && item.Estactivity == null && item.Estdetail == null)
                                            {
                                                @($"{item.Estoption.OptionNumber} - {item.Estoption.OptionDesc}")
                                            }
                                            else if (item.Estactivity!= null && item.Estdetail == null)
                                            {
                                                @($"{item.Estactivity.BomClass}")
                                            }
                                            else if (item.ReleaseCode != null && item.Estdetail == null)
                                            {
                                                @($"Release: {item.ReleaseCode}")
                                            }
                                            else if(item.Estdetail != null)
                                            {
                                                if (string.IsNullOrEmpty(item.Estdetail?.Estoption?.OptionDesc))
                                                {
                                                    @($"{item.Estdetail?.Estoption?.OptionNumber} - {item.Estdetail?.ItemDesc}")
                                                }
                                                else
                                                {
                                                    @($"{item.Estdetail?.Estoption?.OptionDesc}")
                                                }
                                            }
                                        }
                                    </Template>
                                </TreeListColumn>
                                <TreeListColumn Field="EstheaderId" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="EstoptionId" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="OptionNumber" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="OptionDesc" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="EstactivityId" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="BomClass" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="EstdetailId" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="ItemNumber" Visible="false"></TreeListColumn>
                                <TreeListColumn Field="ItemDesc" Visible="false"></TreeListColumn>
                                <TreeListCommandColumn>
                                    @{
                                        var row = context as CombinedPOBudgetTreeModel;
                                        if (row.Estdetail != null && AllowEdit)
                                        {
                                            <TreeListCommandButton Command="Delete" Title="Delete" Class="tooltip-target k-button-danger" Icon="@FontIcon.Trash"></TreeListCommandButton>
                                            <TreeListCommandButton Title="Copy Item" Class="tooltip-target k-button-success" OnClick="@CopyItem" Icon="@FontIcon.Copy"></TreeListCommandButton>
                                        }
                                        else if (row.Estactivity != null && AllowEdit)
                                        {
                                            <TreeListCommandButton Title="Delete" Icon="@FontIcon.Trash" Class="tooltip-target k-button-danger"></TreeListCommandButton>
                                        }
                                        else if (row.Estoption != null && AllowEdit)
                                        {
                                            <TreeListCommandButton Title="Delete" Command="Delete" Class="tooltip-target k-button-danger" Icon="@FontIcon.Trash"></TreeListCommandButton>
                                            <TreeListCommandButton Title="Add Item" Class="tooltip-target k-button-add" OnClick="@ShowAddItemToBudget" Icon="@FontIcon.Plus"></TreeListCommandButton>
                                        }
                                        else
                                        {
                                            if(AllowEdit)
                                            {
                                                <TreeListCommandButton Command="Delete" Title="Delete" Class="tooltip-target k-button-danger" Icon="@FontIcon.Trash"></TreeListCommandButton>
                                            }
                                        }
                                    }
                                </TreeListCommandColumn>
                            </TreeListColumns>
                            <TreeListToolBarTemplate>
                                <TreeListSearchBox />
                                <TelerikDropDownList Data="@SortByOptions"
                                @bind-Value="SelectedSort"
                                OnChange="@ChangeSortHandler"></TelerikDropDownList>
                                @* <TreeListCommandButton Command="IssueBudgets" OnClick="@IssueBudgets">Issue Budgets</TreeListCommandButton> *@
                                @if(AllowEdit)
                                {
                                    <TreeListCommandButton Title="Add Pending/Custom Estimate" Class="tooltip-target k-button-add" Command="AddPendingEstimate" Icon="@FontIcon.Plus" OnClick="@ShowAddPendingEstimate"></TreeListCommandButton>
                                }
                            </TreeListToolBarTemplate>
                        </TelerikTreeList>
                    </TabStripTab>
                </TelerikTabStrip>
            </div>
            <div class="card col-6" style="margin-top:2rem">
                <div class="card-body">
                    <h4 class="page-title mt-2">Details</h4>
                    @if (EditedRow.Estheader != null)
                    {
                        <EditForm Model="@EditedRow" OnValidSubmit="@HandleValidSubmit">
                            <DataAnnotationsValidator />
                            <ValidationSummary />
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Estimate Number: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Enabled="false" @bind-Value="@EditedRow.Estheader.EstimateNumber" Width="100%"></TelerikNumericTextBox>
                                    </div>
                                </div>                       
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Estimate Description: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox @bind-Value="@EditedRow.Estheader.EstimateDescPe" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Estimate Source : </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikDropDownList Data="@EstimateSources" TextField="EstsourceDesc" ValueField="EstsourceId" @bind-Value="@EditedRow.Estheader.EstimateSource" Width="100%"></TelerikDropDownList>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Estimate Total : </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Format="C" @bind-Value="@EditedRow.TotalAmount" Width="100%"></TelerikNumericTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Issued Total : </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.IssuedAmount" Width="100%"></TelerikNumericTextBox>
                                    </div>
                                </div>
                            </div>
                            @* <div>
                <label class="form-label">Associated File: </label>
                <TelerikTextBox @bind-Value="@SelectedRow.ReferenceNumber" Width="200px"></TelerikTextBox>
                </div>*@
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Document Number: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox @bind-Value="@EditedRow.Estheader.ReferenceNumber" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Document Type: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikDropDownList Data="@ReferenceTypes" TextField="ReferenceType1" ValueField="ReferenceTypeId" @bind-Value="@EditedRow.Estheader.ReferenceType" Width="100%"></TelerikDropDownList>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Document Desc: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox @bind-Value="@EditedRow.Estheader.ReferenceDesc" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Selling Price: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Format="C" @bind-Value="@EditedRow.Estheader.EstimateSalesPrice" Width="100%"></TelerikNumericTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Estimator: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox @bind-Value="@EditedRow.Estheader.Estimator" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            @if(AllowEdit)
                            {
                                <button type="submit" class="btn btn-primary">Update</button>
                                <TelerikButton ButtonType="@ButtonType.Button" OnClick="@CancelChanges" Class="btn btn-primary">Cancel</TelerikButton>
                            }
                        </EditForm>
                    }
                    else if (EditedRow.Estoption != null && EditedRow.Estactivity == null && EditedRow.Estdetail == null)
                    {
                        <EditForm Model="@EditedRow" OnValidSubmit="@HandleValidSubmit">
                            <DataAnnotationsValidator />
                            <ValidationSummary />
                            <div class="row p-1" >
                                <div class="col-lg-3">
                                    <label class="form-label">Option Number: </label>
                                </div>                           
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.OptionNumber" Width="100%"></TelerikTextBox>
                                </div>                           
                            </div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Option Description: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.OptionDesc" Width="100%"></TelerikTextBox>
                                </div>                                                        
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Option Qty: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.OptionQty" Width="100%"></TelerikNumericTextBox>
                                    </div>
                                </div>                                                       
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Option Selling Price: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Enabled="false" Format="C" @bind-Value="@EditedRow.Estoption.OptionSalesPrice" Width="100%"></TelerikNumericTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Option Total Cost: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.TotalAmount" Width="100%"></TelerikNumericTextBox>
                                    </div>
                                </div>
                            </div>                            
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Issued Total : </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.IssuedAmount" Width="100%"></TelerikNumericTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Sales Notes: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox @bind-Value="@EditedRow.Estoption.OptionNotes" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Sales Selections: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextArea Enabled="false" @bind-Value="@EditedRow.Estoption.OptionSelections" Width="100%"></TelerikTextArea>
                                    </div>
                                </div>                          
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Base House Code: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.ElevationCode" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Base House Desc: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.ElevationDesc" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>                                               
                            <br/>
                            @if(AllowEdit)
                            {
                                <button type="submit" class="btn btn-primary">Update</button>
                                <TelerikButton ButtonType="@ButtonType.Button" OnClick="@CancelChanges" Class="btn btn-primary">Cancel</TelerikButton>
                            }                       
                        </EditForm>
                    }
                    else if (EditedRow.Estactivity != null && EditedRow.Estdetail == null)
                    {
                        <EditForm Model="@EditedRow" OnValidSubmit="@HandleValidSubmit">
                            <DataAnnotationsValidator />
                            <ValidationSummary />
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Release Code: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estactivity.Releasecode" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Activity: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estactivity.BomClass" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Selected Supplier: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikDropDownList Data="@AllSuppliers"
                                        @bind-Value="@EditedRow.Estactivity.SelectedVendor"
                                        TextField="SubName"
                                        ValueField="SubNumber"
                                        DefaultText="Select Supplier"
                                        Width="100%"
                                        PageSize="40"
                                        Filterable="true"
                                        ItemHeight="30"
                                        ScrollMode="@DropDownScrollMode.Virtual">
                                            <DropDownListSettings>
                                                <DropDownListPopupSettings Height="600px"></DropDownListPopupSettings>
                                            </DropDownListSettings>
                                        </TelerikDropDownList>
                                    </div>
                                </div>
                            </div>
                            <div>                            
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Activity Total: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.TotalAmount" Width="200px"></TelerikNumericTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Issued Total: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.IssuedAmount" Width="200px"></TelerikNumericTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Default Supplier: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikDropDownList Data="@AllSuppliers"
                                        @bind-Value="@EditedRow.Estactivity.DefaultVendor"
                                        TextField="SubName"
                                        ValueField="SubNumber"
                                        DefaultText="Select Supplier"
                                        Width="100%"
                                        PageSize="40"
                                        Filterable="true"
                                        ItemHeight="30"
                                        Enabled="false"
                                        ScrollMode="@DropDownScrollMode.Virtual">
                                            <DropDownListSettings>
                                                <DropDownListPopupSettings Height="600px"></DropDownListPopupSettings>
                                            </DropDownListSettings>
                                        </TelerikDropDownList>
                                    </div>
                                </div>
                            </div>
                            <br/>
                            @if(AllowEdit)
                            {
                                <button type="submit" class="btn btn-primary">Update</button>
                                <TelerikButton ButtonType="@ButtonType.Button" OnClick="@CancelChanges" Class="btn btn-primary">Cancel</TelerikButton>
                            }
                        </EditForm>

                    }
                    else if (EditedRow.Estdetail != null)
                    {
                        <EditForm Model="@EditedRow" OnValidSubmit="@HandleValidSubmit">
                            <DataAnnotationsValidator />
                            <ValidationSummary />
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Phase:  </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.PhaseCode" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Item Number: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.ItemNumber" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Item Description: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.ItemDesc" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Item Notes: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.ItemNotes" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Budget Unit: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.OrdrUnit" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Budget Qty: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Enabled="@EditedRow.IssueEnabled" OnChange="@ChangeUnitPriceQty" @bind-Value="@EditedRow.Estdetail.OrderQty" Width="100%"></TelerikNumericTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Budget Unit Price: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Format="C" Enabled="@EditedRow.IssueEnabled" OnChange="@ChangeUnitPriceQty" @bind-Value="@EditedRow.Estdetail.Price" Width="100%"></TelerikNumericTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Budget Amount: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.Estdetail.Amount" Width="100%"></TelerikNumericTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Lump Sum: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikCheckBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.BoolLump"></TelerikCheckBox>
                                    </div>
                                </div>
                            </div>
                            @*   <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Use Estimate: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikCheckBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.BoolUseEstPrice"></TelerikCheckBox>
                                </div>
                            </div>
                        </div> *@
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label"> Errors: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.DisplayErrors" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Warnings: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.DisplayWarnings" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            @* <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Taxable: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikCheckBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.BoolTaxable"></TelerikCheckBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Tax Details: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.TaxGroupType" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div> *@
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Estimate Number: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikNumericTextBox Enabled="false" @bind-Value="@EditedRow.EstimateNumber" Width="100%"></TelerikNumericTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Option Number: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.ExtraNumber" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Release Code: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estactivity.Releasecode" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Activity: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estactivity.BomClass" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>                      
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Job Cost Code: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikDropDownList Data="@AllJobCostCodes"
                                        @bind-Value="@EditedRow.Estdetail.JcPhase"
                                        TextField="DropdownDescription"
                                        ValueField="CostCode"
                                        DefaultText="Select Cost Code"
                                        Width="100%"
                                        PageSize="40"
                                        Filterable="true"
                                        FilterOperator="@StringFilterOperator.Contains"
                                        ItemHeight="30"
                                        Enabled="@EditedRow.IssueEnabled"
                                        ScrollMode="@DropDownScrollMode.Virtual">
                                            <DropDownListSettings>
                                                <DropDownListPopupSettings Height="600px"></DropDownListPopupSettings>
                                            </DropDownListSettings>
                                        </TelerikDropDownList>
                                        @*  <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.JcPhase" Width="100%"></TelerikTextBox> *@
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Job Cost Category: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikDropDownList Data="@AllJobCostCategories"
                                        @bind-Value="@EditedRow.Estdetail.JcCategory"
                                        TextField="DropdownDescription"
                                        ValueField="Category"
                                        DefaultText="Select Category"
                                        Width="100%"
                                        PageSize="40"
                                        Filterable="true"
                                        ItemHeight="30"
                                        Enabled="@EditedRow.IssueEnabled"
                                        FilterOperator="StringFilterOperator.Contains"
                                        ScrollMode="@DropDownScrollMode.Virtual">
                                            <DropDownListSettings>
                                                <DropDownListPopupSettings Height="600px"></DropDownListPopupSettings>
                                            </DropDownListSettings>
                                        </TelerikDropDownList>
                                        @*  <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.JcCategory" Width="100%"></TelerikTextBox> *@
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Tracking Variance: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikCheckBox Enabled="false" @bind-Value="@EditedRow.Estdetail.TrackingVariance"></TelerikCheckBox>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="row p-1">
                                    <div class="col-lg-3">
                                        <label class="form-label">Variance JC Category: </label>
                                    </div>
                                    <div class="col-lg-9">
                                        <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.VarianceJcCategory" Width="100%"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                            @*  <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Variance Amount: </label>
                                </div>
                                <div class="col-lg-9">
                                    @{
                                        var variance = EditedRow.Estdetail.OrgPrice != null && EditedRow.Estdetail.Price != null ? EditedRow.Estdetail.OrgPrice - EditedRow.Estdetail.Price : 0;
                                         <TelerikNumericTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@variance" Width="100%"></TelerikNumericTextBox>
                                    }                                    
                                </div>
                            </div>
                        </div> *@
                            @*  <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Original Amount: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikNumericTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.OrgPrice" Width="100%"></TelerikNumericTextBox>
                                </div>
                            </div>
                        </div> *@
                            @if (EditedRow.Estdetail.Podetail != null && EditedRow.Estdetail.Podetail.Poheader != null)
                            {

                                <div>                           
                                    <div class="row p-1">                                
                                        <div class="col-lg-3">                                    
                                            <label class="form-label">PO Number: </label>                                
                                        </div>                                
                                        <div class="col-lg-9">                                    
                                            <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.Podetail.Poheader.Ponumber" Width="100%"></TelerikTextBox>                                
                                        </div>                            
                                    </div>                        
                                </div>

                                <div>
                                    <div class="row p-1">
                                        <div class="col-lg-3">
                                            <label class="form-label">PO Status: </label>
                                        </div>
                                        <div class="col-lg-9">
                                            <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.Podetail.Poheader.PostatusNavigation.Postatus1" Width="100%"></TelerikTextBox>
                                        </div>
                                    </div>
                                </div>
                                @*  <div>
                                    <div class="row p-1">
                                        <div class="col-lg-3">
                                            <label class="form-label">VPO Number: </label>
                                        </div>
                                        <div class="col-lg-9">
                                            <TelerikNumericTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.Podetail.Poheader.VpoNumber" Width="100%"></TelerikNumericTextBox>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="row p-1">
                                        <div class="col-lg-3">
                                            <label class="form-label">VPO Invoice Number: </label>
                                        </div>
                                        <div class="col-lg-9">
                                            <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.Podetail.Poheader.VpoVendorinvoice" Width="100%"></TelerikTextBox>
                                        </div>
                                    </div>
                                </div> *@
                            }

                            <br/>
                            @if(AllowEdit)
                            {
                                <button type="submit" class="btn btn-primary">Update</button>
                                <TelerikButton ButtonType="@ButtonType.Button" OnClick="@CancelChanges" Class="btn btn-primary">Cancel</TelerikButton>
                            }    
                        </EditForm>

                    }
                </div>
            </div>
        }       
    </div>
</div>

<ERP.Web.Components.AddItemToBudget SelectedSubdivisionId="@SelectedSubdivisionId" JobNumber="@JobSelected" SelectedEstOptionId="@SelectedEstOptionId" SelectedHeaderId="@SelectedHeaderId" @ref="AddItemToBudgetModal" HandleAddSubmit="HandleValidAddItemsSubmit"></ERP.Web.Components.AddItemToBudget>
<ERP.Web.Components.AddEstimate @ref="AddEstimateModal" SelectedSubdivisionId="@SelectedSubdivisionId" IsPendingCustomEstimate="IsPendingCustomEstimate" JobNumber="@JobSelected" HandleAddSubmit="HandleValidAddEstimateSubmit"></ERP.Web.Components.AddEstimate>
<ERP.Web.Components.SelectDate @ref="SelectDateModal" MessageToUser="Select Date To Refresh Costs" HandleDateSelected="HandleRefreshCostsSubmit"></ERP.Web.Components.SelectDate>
@code {
    //private List<JobDto>? AllJobs { get; set; }
    private List<string>? AllJobsForAutoSelect { get; set; }
    public List<JccategoryDto>? AllJobCostCategories {get; set; }
    public List<JccostcodeDto>? AllJobCostCodes {get; set;}
    private string? SelectedJob { get; set; }
    private string? JobSelected { get; set; }
    public bool? IsPendingCustomEstimate { get; set; }
    public CombinedPOBudgetTreeModel? EditedRow { get; set; } = new CombinedPOBudgetTreeModel();
    public CombinedPOBudgetTreeModel? SelectedRow { get; set; } = new CombinedPOBudgetTreeModel();
    public CombinedPOBudgetTreeModel? RefreshCostClickedRow { get; set; }
    private List<EstheaderDto>? EstHeaders { get; set; }
    private ObservableCollection<CombinedPOBudgetTreeModel>? BudgetData { get; set; }
    private ObservableCollection<CombinedPOBudgetTreeModel>? PendingCustomEstimateData { get; set; }
    private TelerikTreeList<CombinedPOBudgetTreeModel>? BudgetTreeList { get; set; }
    private TelerikTreeList<CombinedPOBudgetTreeModel>? PendingTreeList { get; set; }
    public List<SupplierDto>? AllSuppliers {get; set; }
    public IEnumerable<CombinedPOBudgetTreeModel>? SelectedItems { get; set; } = Enumerable.Empty<CombinedPOBudgetTreeModel>();
    public List<CombinedPOBudgetTreeModel>? SelectedRows { get; set; } = new List<CombinedPOBudgetTreeModel>();
    public ERP.Web.Components.AddItemToBudget? AddItemToBudgetModal { get; set; }
    // public ERP.Web.Components.AddPendingEstimate? AddPendingEstimateModal { get; set; }
    public ERP.Web.Components.AddEstimate? AddEstimateModal { get; set; }
    public ERP.Web.Components.SelectDate? SelectDateModal { get; set; }

    private decimal? TotalJobBudget { get; set;}
    private decimal? TotalIssuedBudget { get; set; }

    private List<ReferenceTypeDto>? ReferenceTypes { get; set; }
    private List<EstimateSourceDto>? EstimateSources { get; set; }
    public int? SelectedEstOptionId {get; set; }
    public int? SelectedHeaderId { get; set; }
    public int? SelectedSubdivisionId { get; set; }
    public List<string>? SortByOptions { get; set; } = new List<string> { "Release/Activity", "Estimate/Option/Activity" };
    public string? SelectedSort { get; set; } = "Estimate/Option/Activity";
    public string? CheckSortChanged { get; set; } = "Estimate/Option/Activity";
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    private bool AllowEdit { get; set; } = true;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }
    private bool IsBudgetLoading { get; set; } = false;
    private bool IsLoading { get; set; } = false;


    protected override async Task OnInitializedAsync()
    {
        SubdivisionJobPickService.OnChanged += JobChangedHandler;
        //TODO: too slow to load      
        //var jobsTask = SubdivisionService.GetAllJobsAsync();
        var suppliersTask = PoService.GetSuppliersAsync(IncludeBlocked: true);
        var refTypesTask = BudgetService.GetReferenceTypesAsync();
        var estSourceTask = BudgetService.GetEstimateSourcesAsync();
        var jcCostCodesTask = PoService.GetJcCostCodesAsync();
        var jcCategoriesTask = PoService.GetJccategoriesAsync();
        await Task.WhenAll(new Task[] {  suppliersTask, refTypesTask, estSourceTask, jcCategoriesTask, jcCostCodesTask });
        AllJobCostCategories = jcCategoriesTask.Result.Value;
        AllJobCostCodes = jcCostCodesTask.Result.Value;
        //AllJobs = jobsTask.Result.Value;
        //AllJobsForAutoSelect = AllJobs.Select(x => x.JobNumber).ToList();
        AllSuppliers = suppliersTask.Result.Value;
        ReferenceTypes = refTypesTask.Result.Value;
        EstimateSources = estSourceTask.Result.Value;
        if(SubdivisionJobPickService.JobNumber != null)
        {
            IsBudgetLoading = true;
            StateHasChanged();
            JobSelected = SubdivisionJobPickService.JobNumber;
            var getJob = await SubdivisionService.GetLotAsync(JobSelected);
            SelectedSubdivisionId = getJob.Value.SubdivisionId;
            var getData = await BudgetService.GetBudgetByJobAsync(JobSelected);
            BudgetData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData.Value.Where(x => x.Estheader.EstimateDescPe != "Pending/Custom Estimate"));
            PendingCustomEstimateData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData.Value.Where(x => x.Estheader.EstimateDescPe == "Pending/Custom Estimate"));            
            TotalIssuedBudget = getData.Value.Sum(x => x.IssuedAmount);
            TotalJobBudget = getData.Value.Sum(x => x.TotalAmount);
            SelectedRows = new List<CombinedPOBudgetTreeModel>();
            SelectedItems = Enumerable.Empty<CombinedPOBudgetTreeModel>();
            EditedRow = new CombinedPOBudgetTreeModel();
            IsLoading = false;
            IsBudgetLoading = false;
            StateHasChanged();
        }
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }
    async Task JobChangedHandler()
    {
        var selected = SubdivisionJobPickService.JobNumber;
        if (JobSelected != selected)//check if actual change, so not calling this multiple times
        {
            IsBudgetLoading = true;
            StateHasChanged();
            JobSelected = selected;
            var getJob = await SubdivisionService.GetLotAsync(JobSelected);
            SelectedSubdivisionId = getJob.Value.SubdivisionId;
            if (SelectedSort == "Release/Activity")
            {
                var getData = await BudgetService.GetBudgetByJobReleaseSortAsync(JobSelected);
                BudgetData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData.Value.Where(x => x.Estheader.EstimateDescPe != "Pending/Custom Estimate"));
                PendingCustomEstimateData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData.Value.Where(x => x.Estheader.EstimateDescPe == "Pending/Custom Estimate"));
                TotalIssuedBudget = getData.Value.Sum(x => x.IssuedAmount);
                TotalJobBudget = getData.Value.Sum(x => x.TotalAmount);
                //StateHasChanged();
            }
            else
            {
                var getData = await BudgetService.GetBudgetByJobAsync(JobSelected);
                BudgetData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData.Value.Where(x => x.Estheader.EstimateDescPe != "Pending/Custom Estimate"));
                PendingCustomEstimateData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData.Value.Where(x => x.Estheader.EstimateDescPe == "Pending/Custom Estimate"));
                TotalIssuedBudget = getData.Value.Sum(x => x.IssuedAmount);
                TotalJobBudget = getData.Value.Sum(x => x.TotalAmount);
                // StateHasChanged();
            }
            SelectedRows = new List<CombinedPOBudgetTreeModel>();
            SelectedItems = Enumerable.Empty<CombinedPOBudgetTreeModel>();
            EditedRow = new CombinedPOBudgetTreeModel();
            IsBudgetLoading = false;
            StateHasChanged();
        }

    } 
    async Task OnStateInitHandler(TreeListStateEventArgs<CombinedPOBudgetTreeModel> args)
    {
        var collapsedItemsState = new TreeListState<CombinedPOBudgetTreeModel>()
            {
            //collapse all items in the TreeList upon initialization of the state
                ExpandedItems = new List<CombinedPOBudgetTreeModel>()
            };
        args.TreeListState = collapsedItemsState;
    }
    protected async Task JobSelectedHandler(object theUserChoice)
    {
        if ((string)theUserChoice != null && JobSelected != ((string)theUserChoice).ToUpper())
        {
            IsBudgetLoading = true;
            JobSelected = ((string)theUserChoice).ToUpper();
            var getData = await BudgetService.GetBudgetByJobAsync(JobSelected);
            BudgetData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData.Value.Where(x => x.Estheader.EstimateDescPe != "Pending/Custom Estimate"));
            PendingCustomEstimateData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData.Value.Where(x => x.Estheader.EstimateDescPe == "Pending/Custom Estimate"));
            TotalIssuedBudget = getData.Value.Sum(x => x.IssuedAmount);
            TotalJobBudget = getData.Value.Sum(x => x.TotalAmount);
            IsBudgetLoading = false;
            StateHasChanged();
        }
    }
    protected async Task RowSelectedHandler(TreeListRowClickEventArgs args)
    {
        EditedRow = args.Item as CombinedPOBudgetTreeModel;
    }
    protected async Task CopyItem(TreeListCommandEventArgs args)
    {
        var itemToCopy = args.Item as CombinedPOBudgetTreeModel;
        if(itemToCopy.Estdetail != null)
        {
            var returnItem = await BudgetService.CopyItemAsync(itemToCopy.Estdetail);
            ShowMessage(returnItem.IsSuccess, returnItem.Message);
            var parents = itemToCopy.Estdetail.Estoption.Estheader.EstimateDescPe != "Pending/Custom Estimate" ? BudgetData.SelectRecursive(x => x.Children) : PendingCustomEstimateData.SelectRecursive(x => x.Children);
            var findParent = parents.Where(x => x.Id == itemToCopy.ParentId).SingleOrDefault();
            var returnItemToAdd = new CombinedPOBudgetTreeModel()
                    {
                    Id = Guid.NewGuid(),
                    ParentId = itemToCopy.ParentId,
                    IsPendingCustomEstimate = itemToCopy.IsPendingCustomEstimate,
                    Estdetail = returnItem.Value,
                    Estactivity = itemToCopy.Estactivity,
                    Estoption = itemToCopy.Estoption,
                    EstimateNumber = itemToCopy.EstimateNumber,
                    ExtraNumber = itemToCopy.ExtraNumber,
                    TotalAmount = itemToCopy.TotalAmount,
                    IssuedAmount = 0,
                    IsIssued = false,
                    IssueEnabled = true,
                    HasChildren = false
                    };
            returnItemToAdd.Estdetail.Podetail = returnItemToAdd.Estdetail.Podetail ?? new PodetailDto() { Poheader = new PoheaderDto() };//fill empty in case of null
            findParent.Children.Add(returnItemToAdd);
            // BudgetData.CollectionChanged();
            BudgetTreeList?.Rebind();
            PendingTreeList?.Rebind();
            AdjustCheckBoxState(itemToCopy);
            ReSumActivityOption(itemToCopy);
        }      
    }
    private void CheckChildren(CombinedPOBudgetTreeModel item, bool checkValue)
    {
        if(item.HasChildren == false)
        {
            item.IsIssued = checkValue;
            item.Indeterminate = checkValue == true ? false : item.Indeterminate;
        }
        else
        {
            foreach (var child in item.Children.Where(x => x.IssueEnabled != false))
            {
                CheckChildren(child, checkValue);
                item.IsIssued = checkValue;
                item.Indeterminate = checkValue == true ? false : item.Children.Any(x => x.Indeterminate == true) || (item.Children.Any(x => x.IsIssued == false && item.Children.Any(x => x.IsIssued == true)));
            }
        }
    }
    private bool DeterimineIndeterminateState(CombinedPOBudgetTreeModel item)
    {
        bool indeterminate = false;
        if (item.HasChildren)
        {
            var children = item.Children.SelectRecursive(x => x.Children).ToList();
            indeterminate = children.Any(x => x.Indeterminate == true) || (children.Any(x => x.IsIssued == true) && children.Any(x => x.IsIssued == false));
        }
        return indeterminate;
    }

    void ChangeSelected(object value)
    {
        //checking parent should check the children
        SelectedRow.IsIssued = (bool)value;
        CheckChildren(SelectedRow, SelectedRow.IsIssued);
        SelectedRow.Indeterminate = (bool)value == true ? false : DeterimineIndeterminateState(SelectedRow);

        var allChildrenToAddOrRemove = new List<CombinedPOBudgetTreeModel> { SelectedRow };
        if (SelectedRow.Estdetail == null)
        {
            allChildrenToAddOrRemove.AddRange(SelectedRow.Children.SelectRecursive(x => x.Children).Where(x => x.IssueEnabled != false).ToList());
        }
        if (SelectedRow.IsIssued == true)
        {
            SelectedRows.AddRange(allChildrenToAddOrRemove);
        }
        else
        {
            SelectedRows.RemoveAll(x => allChildrenToAddOrRemove.Any(y => x == y));
        }
    }
    void ChangeUnitPriceQty(object value)
    {
        EditedRow.Estdetail.Amount = EditedRow.Estdetail.Price * EditedRow.Estdetail.OrderQty;
        EditedRow.Estdetail.BoolLump = true;
        EditedRow.Estdetail.Lump = "T";
        EditedRow.Estdetail.DisplayWarnings = string.IsNullOrWhiteSpace(EditedRow.Estdetail.DisplayWarnings) ? "Using Lump Sum" : EditedRow.Estdetail.DisplayWarnings.Contains("Using Lump Sum") ? EditedRow.Estdetail.DisplayWarnings : EditedRow.Estdetail.DisplayWarnings + ", Using Lump Sum";
        EditedRow.Estdetail.Warnings = !String.IsNullOrWhiteSpace(EditedRow.Estdetail.Warnings) ? (EditedRow.Estdetail.Warnings.Contains("5") ? EditedRow.Estdetail.Warnings : EditedRow.Estdetail.Warnings + "|5") : "5";
        if (!string.IsNullOrWhiteSpace(EditedRow.Estdetail.Errors) && !string.IsNullOrWhiteSpace(EditedRow.Estdetail.DisplayErrors) && EditedRow.Estdetail.Errors.Contains("4"))
        {
            //4 is no cost error, now they have a cost, so remove the error
            var listDisplayErrors = EditedRow.Estdetail.DisplayErrors.Split(',').ToList();
            listDisplayErrors.Remove("Zero Cost Not Allowed");
            listDisplayErrors.Remove(" Zero Cost Not Allowed");
            EditedRow.Estdetail.DisplayErrors = string.Join(", ", listDisplayErrors);
            var listErrors = EditedRow.Estdetail.Errors.Split('|').ToList();
            listErrors.Remove("4");
            EditedRow.Estdetail.Errors = string.Join("|", listErrors);
        }
        //TODO: this also sets tracking variance to true, and on save prompts a select a variance category, and the original amount is saved, but how to tell if theyve updated it multiple times
        //don't do it here, save the tracking variance on update
        // EditedRow.Estdetail.OrgPrice = 1;
        // EditedRow.Estdetail.VarianceJcCategory = "CO";
        // EditedRow.Estdetail.EstimateVarianceItem = "T"; //nope
    }
    protected async Task DeleteHandler(TreeListCommandEventArgs args)
    {
        //TODO: what if budget issued? correction?
        var itemToDelete = args.Item as CombinedPOBudgetTreeModel;
        // DELETING ESTDETAIL
        if (itemToDelete.Estdetail != null && itemToDelete.IsIssued != true)
        {
            var response = await BudgetService.DeleteItemAsync(itemToDelete);
            ShowMessage(response.IsSuccess, response.Message);
            var parents = itemToDelete.Estdetail.Estoption.Estheader.EstimateDescPe != "Pending/Custom Estimate" ? BudgetData.SelectRecursive(x => x.Children) : PendingCustomEstimateData.SelectRecursive(x => x.Children);
            var findParent = parents.Where(x => x.Id == itemToDelete.ParentId).SingleOrDefault();
            findParent.Children.Remove(itemToDelete);
            findParent.ActivityTotal = findParent.Children.Sum(x => x.Estdetail.Amount);//adjust activity sum
            findParent.Estactivity.ActivityTotal = findParent.Children.Sum(x => x.Estdetail.Amount);
            if (SelectedSort == "Estimate/Option/Activity")
            {
                var findGrandParent = parents.SingleOrDefault(x => x.Id == findParent.ParentId);
                findGrandParent.Estoption.TotalCost = findGrandParent.Children.Sum(x => x.Estactivity.ActivityTotal);//adjust option sum
                findGrandParent.TotalCost = findGrandParent.Children.Sum(x => x.Estactivity.ActivityTotal);
            }
            if (!findParent.Children.Any())
            {
                var findGrandParent = parents.Where(x => x.Id == findParent.ParentId).SingleOrDefault();
                findGrandParent.Children.Remove(findParent);
            }
            BudgetTreeList?.Rebind();
            PendingTreeList?.Rebind();
            if(findParent.Children.Count != 0)
            {
                AdjustCheckBoxState(findParent.Children.First());
            }           
        }
        // DELETING ESTACTIVITY
        else if(itemToDelete.Estactivity != null && itemToDelete.IsIssued != true)
        {
            if(SelectedSort == "Estimate/Option/Activity" )
            {
                var parents = itemToDelete.IsPendingCustomEstimate == false ? BudgetData.SelectRecursive(x => x.Children) : PendingCustomEstimateData.SelectRecursive(x => x.Children);
                var findParent = parents.Where(x => x.Id == itemToDelete.ParentId).SingleOrDefault();
                itemToDelete.Estoption = findParent.Estoption;//setting this so it can delete only the items in the same option  
                var response = await BudgetService.DeleteActivityOptionSortAsync(itemToDelete);
                if (response.IsSuccess)
                {
                    findParent.Children.Remove(itemToDelete);
                    if (SelectedSort == "Estimate/Option/Activity")
                    {
                        findParent.Estoption.TotalCost = findParent.Children.Sum(x => x.Estactivity.ActivityTotal);//adjust option sum
                        findParent.TotalCost = findParent.Children.Sum(x => x.Estactivity.ActivityTotal);
                    }

                    if (!findParent.Children.Any())
                    {
                        //remove option if no other activities
                        var findGrandParent = parents.Where(x => x.Id == findParent.ParentId).SingleOrDefault();
                        findGrandParent.Children.Remove(findParent);
                        if (!findGrandParent.Children.Any())
                        {
                            //remove estimate if no other options
                            if (itemToDelete.IsPendingCustomEstimate == true)
                            {
                                PendingCustomEstimateData.Remove(findGrandParent); //if no other options, remove the parent est
                            }
                            else
                            {
                                BudgetData.Remove(findGrandParent); //if no other options, remove the parent est
                            }
                        }
                    }
                    PendingTreeList?.Rebind();
                    BudgetTreeList?.Rebind();
                }
                ShowMessage(response.IsSuccess, response.Message);
            }
            else
            {
                var parents = itemToDelete.IsPendingCustomEstimate == false ? BudgetData.SelectRecursive(x => x.Children) : PendingCustomEstimateData.SelectRecursive(x => x.Children);
                var findParent = parents.Where(x => x.Id == itemToDelete.ParentId).SingleOrDefault();
                // itemToDelete.Estheader = findParent.Estoption;//setting this so it can delete only the items in the same header
                var response = await BudgetService.DeleteActivityAsync(itemToDelete);
                //TODO: be sure only removing items in same release andestimate
                if (response.IsSuccess)
                {
                    findParent.Children.Remove(itemToDelete);
                    if (!findParent.Children.Any())
                    {
                        //remove release if no other activities
                        var findGrandParent = parents.Where(x => x.Id == findParent.ParentId).SingleOrDefault();
                        findGrandParent.Children.Remove(findParent);
                        if (!findGrandParent.Children.Any())
                        {
                            //remove estimate if no other release
                            if (itemToDelete.IsPendingCustomEstimate == true)
                            {
                                PendingCustomEstimateData.Remove(findGrandParent); //if no other options, remove the parent est
                            }
                            else
                            {
                                BudgetData.Remove(findGrandParent); //if no other options, remove the parent est
                            }
                        }
                    }
                    PendingTreeList?.Rebind();
                    BudgetTreeList?.Rebind();
                }
                ShowMessage(response.IsSuccess, response.Message);
            }
        }
        // DELETING ESTOPTION
        else if(itemToDelete.Estoption != null && itemToDelete.IsIssued != true)
        {
            var response = await BudgetService.DeleteOptionAsync(itemToDelete.Estoption);
            if (response.IsSuccess)
            {
                if (itemToDelete.IsPendingCustomEstimate == true)
                {
                    var findParent = PendingCustomEstimateData.Where(x => x.Id == itemToDelete.ParentId).SingleOrDefault();
                    findParent.Children.Remove(itemToDelete);
                    if (!findParent.Children.Any())
                    {
                        PendingCustomEstimateData.Remove(findParent); //if no other options, remove the parent est
                    }
                    PendingTreeList?.Rebind();

                }
                else
                {
                    var findParent = BudgetData.Where(x => x.Id == itemToDelete.ParentId).SingleOrDefault();
                    findParent.Children.Remove(itemToDelete);
                    if (!findParent.Children.Any())
                    {
                        BudgetData.Remove(findParent);//if no other options, remove the parent est
                    }
                    BudgetTreeList?.Rebind();
                }
            }
            ShowMessage(response.IsSuccess, response.Message);
            StateHasChanged();
        }
        else if (itemToDelete.Estheader != null && itemToDelete.IsIssued != true)
        {
            var response = await BudgetService.DeleteHeaderAsync(itemToDelete.Estheader);//TODO: Only allow delete if not any issued. But maybe not even then, wouldn't want to delete the original config?
            if (response.IsSuccess)
            {
                if (itemToDelete.IsPendingCustomEstimate == true)
                {
                    PendingCustomEstimateData.Remove(itemToDelete);
                }
                else
                {
                    BudgetData.Remove(itemToDelete);
                }

            }
            ShowMessage(response.IsSuccess, response.Message);
            StateHasChanged();
        }

    }
    private async Task HandleValidSubmit()
    {
        var response = (dynamic)null;
        if (EditedRow.Estheader != null)
        {
            var estHeaderToUpdate = new EstheaderDto()
                    {
                    EstheaderId = (int)EditedRow.Estheader.EstheaderId,
                    JobNumber = EditedRow.Estheader.JobNumber,
                    EstimateDescPe = EditedRow.Estheader.EstimateDescPe,
                    EstimateSalesPrice = EditedRow.Estheader.EstimateSalesPrice,
                    Estimator = EditedRow.Estheader.Estimator,
                    ReferenceNumber = EditedRow.Estheader.ReferenceNumber,
                    ReferenceDesc = EditedRow.Estheader.ReferenceDesc,
                    ReferenceType = EditedRow.Estheader.ReferenceType,//TODO: seems to be only editable if it s not original estimate
                    Heading1 = EditedRow.Estheader.Heading1,
                    Heading2 = EditedRow.Estheader.Heading2,
                    Heading3 = EditedRow.Estheader.Heading3,
                    JobSize = EditedRow.Estheader.JobSize,
                    JobUnit = EditedRow.Estheader.JobUnit,

                    };
            response = await BudgetService.UpdateEstHeaderAsync(estHeaderToUpdate);
            //TODO: return success/fail
        }
        else if(EditedRow.Estoption != null && EditedRow.Estactivity == null)
        {
            var estOptionToUpdate = new EstoptionDto()
                {
                    EstoptionId = (int)EditedRow.Estoption.EstoptionId,
                    OptionDesc = EditedRow.Estoption.OptionDesc,
                    OptionSalesPrice = EditedRow.Estoption.OptionSalesPrice,
                    OptionQty = EditedRow.Estoption.OptionQty,
                    OptionNotes = EditedRow.Estoption.OptionNotes,
                    OptionSelections = EditedRow.Estoption.OptionSelections
                };
            response = await BudgetService.UpdateEstOptionAsync(estOptionToUpdate);
        }
        else if(EditedRow.Estactivity != null && EditedRow.Estdetail == null)
        {
            //TODO: selected supplier can change, this triggers some extra popup about pulling in new costs
            var estActivityToUpdate = new EstactivityDto()
                {
                    EstactivityId = (int)EditedRow.Estactivity.EstactivityId,
                    UseLocation = EditedRow.Estactivity.BoolUseLocation == true ? "T" : "F",
                    UseWbsSort = EditedRow.Estactivity.BoolUseWbsSort == true ? "T" : "F",
                    Taxable = EditedRow.Estactivity.BoolTaxable == true ? "T" : "F",
                    SelectedVendor = EditedRow.Estactivity.SelectedVendor
                };
            response = await BudgetService.UpdateEstActivityAsync(estActivityToUpdate);
        }
        else if (EditedRow.Estdetail != null)
        {
            var estDetailToUpdate = new EstdetailDto()
                {
                    EstdetailId = (int)EditedRow.Estdetail.EstdetailId,
                    ItemNotes = EditedRow.Estdetail.ItemNotes,
                    ItemDesc = EditedRow.Estdetail.ItemDesc,
                    OrdrUnit = EditedRow.Estdetail.OrdrUnit,
                    OrderQty = EditedRow.Estdetail.OrderQty,
                    Price = EditedRow.Estdetail.Price,
                    Amount = EditedRow.Estdetail.Amount,                   
                    Lump = EditedRow.Estdetail.BoolLump == true ? "T" : "F",
                    UseEstPrice = EditedRow.Estdetail.BoolUseEstPrice == true ? "T" : "F",
                    Taxable = EditedRow.Estdetail.BoolTaxable == true ? "T" : "F",
                    Warnings = EditedRow.Estdetail.Warnings,
                    Errors = EditedRow.Estdetail.Errors,
                    JcCategory = EditedRow.Estdetail.JcCategory,
                    JcPhase = EditedRow.Estdetail.JcPhase
                };
            response = await BudgetService.UpdateEstDetailAsync(estDetailToUpdate);
            if (response.IsSuccess)
            {
                EditedRow.Estdetail = response.Value;
                EditedRow.TotalAmount = (decimal?)EditedRow.Estdetail.Amount;
                ReSumActivityOption(EditedRow);
            }

        }
        // can add more descriptive success/error messages if needed, use a string variable
        if (response.IsSuccess)
        {
            ShowMessage(true, "Successfully submitted");
        }
        else
        {
            ShowMessage(false, "There's an error submitting. If problem persists, <NAME_EMAIL>");
        }
        StateHasChanged();
    }
    private void ReSumActivityOption(CombinedPOBudgetTreeModel item)
    {
        //TODO: check newly added items have parent Id filled, else fix how this finds items parents
        //item should be estdetail level item, 
        //resum activity/option, for use whenever costs change - eg update estdetail, copy item, refresh costs, 
        var parents = item.Estdetail.Estoption.Estheader.EstimateDescPe != "Pending/Custom Estimate" ? BudgetData.SelectRecursive(x => x.Children) : PendingCustomEstimateData.SelectRecursive(x => x.Children);
        var findParent = parents.Where(x => x.Id == item.ParentId).SingleOrDefault();
        findParent.IssuedAmount = findParent.Children.Sum(x => x.IssuedAmount);
        findParent.TotalAmount = findParent.Children.Sum(x => x.TotalAmount);
        findParent.ActivityTotal = findParent.Children.Sum(x => x.Estdetail.Amount);
        findParent.Estactivity.ActivityTotal = findParent.Children.Sum(x => x.Estdetail.Amount);
        if(SelectedSort == "Estimate/Option/Activity")
        {
            var findGrandParent = parents.SingleOrDefault(x => x.Id == findParent.ParentId);
            if(findGrandParent != null)
            {
                findGrandParent.Estoption.TotalCost = findGrandParent.Children.Sum(x => x.Estactivity.ActivityTotal);
                findGrandParent.TotalCost = findGrandParent.Children.Sum(x => x.Estactivity.ActivityTotal);
                findGrandParent.IssuedAmount = findGrandParent.Children.Sum(x => x.IssuedAmount);
                findGrandParent.TotalAmount = findGrandParent.Children.Sum(x => x.TotalAmount);
                var findGreatGrandParent = parents.SingleOrDefault(x => x.Id == findGrandParent.ParentId);
                if (findGreatGrandParent != null)
                {
                    findGreatGrandParent.IssuedAmount = findGreatGrandParent.Children.Sum(x => x.IssuedAmount);
                    findGreatGrandParent.TotalAmount = findGreatGrandParent.Children.Sum(x => x.TotalAmount);
                }
            }          
        }
    }
    private async Task CancelChanges()
    {
        //TODO: refresh the sums on the activity/option
        //refresh the item from db
        if(EditedRow.Estheader != null)
        {
            var getHeader = await BudgetService.GetEstHeaderAsync((int)EditedRow.Estheader.EstheaderId);
            EditedRow.Estheader.EstheaderId = getHeader.Value.EstheaderId;
            EditedRow.EstimateNumber = getHeader.Value.EstimateNumber;
            EditedRow.Estheader = getHeader.Value;
        }
        else if(EditedRow.Estoption != null && EditedRow.Estactivity == null)
        {
            var getOption = await BudgetService.GetEstOptionAsync((int)EditedRow.Estoption.EstoptionId);
            EditedRow.Estoption = getOption.Value;
            EditedRow.Estoption.EstoptionId = getOption.Value.EstoptionId;
            // EditedRow.IsIssued = getOption.IsIssued;
            // EditedRow.Indeterminate = 
            // EditedRow.IssueEnabled = getOption.IsIssued != true;
            EditedRow.TotalCost = getOption.Value.TotalCost;
        }
        else if(EditedRow.Estactivity != null && EditedRow.Estdetail == null)
        {
            var getActivity = await BudgetService.GetEstActivityAsync((int)EditedRow.Estactivity.EstactivityId);
            EditedRow.BomClass = getActivity.Value.BomClass;
            EditedRow.ActivityTotal = getActivity.Value.ActivityTotal;
            EditedRow.Estactivity = getActivity.Value;                    
            EditedRow.ReleaseCode = getActivity.Value.Releasecode;
        }
        else
        {
            var getDetail = await BudgetService.GetEstDetailAsync((int)EditedRow.Estdetail.EstdetailId);
            EditedRow.Estdetail = getDetail.Value;
            // EditedRow.Estdetail.Warnings = getDetail.Lump == "T" ? "Warning: Using Lump Sum" : null;            
            // EditedRow.Estdetail.BoolUseEstPrice = getDetail.BoolUseEstPrice ?? false;
            // EditedRow.Estdetail.BoolTaxable = getDetail.BoolTaxable ?? false;        
        }
    }
    protected async Task RefreshSupplier(TreeListCommandEventArgs args)
    {
        var activitySelected = args.Item as CombinedPOBudgetTreeModel;
        IsLoading = true;
        var refreshSupplierModel = new RefreshSupplierAndCostsModel() { EstActivityIds = new List<int>(), SubdivisionId = SelectedSubdivisionId };

        if (activitySelected != null && activitySelected.Estoption == null) // refresh supplier clicked at activity level
        {
            refreshSupplierModel.EstActivityIds.Add(activitySelected.Estactivity.EstactivityId); 
        }
        else if (activitySelected != null && activitySelected.Estoption != null) // refresh supplier clicked at option level
        {
            refreshSupplierModel.EstActivityIds.AddRange(activitySelected.Children.Select(x => x.Estactivity.EstactivityId).ToList());
        }
        else // refresh supplier for selected rows
        {
            foreach (var item in SelectedRows)
            {
                // if (item.Estoption != null && item.Estactivity == null)
                // {
                //     refreshSupplierModel.EstActivityIds.AddRange(item.Children.Select(x=>x.Estactivity.EstactivityId).ToList());
                // }
                // else 
                if (item.Estactivity != null && item.Estdetail == null)
                {
                    refreshSupplierModel.EstActivityIds.Add(item.Estactivity.EstactivityId);
                }
                // else if (item.Estoption == null && item.Estactivity == null && item.Estdetail == null) // SelectedRow is an estimate
                // {
                //     var childrenActivitiesInEstimate = item.Children.SelectRecursive(x => x.Children).Where(x => x.Estactivity != null && x.Estdetail == null).ToList().Select(x => x.Estactivity.EstactivityId).ToList();
                //     refreshSupplierModel.EstActivityIds.AddRange(childrenActivitiesInEstimate);
                // }
            }
        }
        refreshSupplierModel.EstActivityIds = refreshSupplierModel.EstActivityIds.Distinct().ToList();

        var refreshedVendorResponse = await TradeService.RefreshSupplierAsync(refreshSupplierModel);
        if (refreshedVendorResponse.IsSuccess)
        {
            if (activitySelected != null && activitySelected.Estoption == null)
            {
                var parents = BudgetData.SelectRecursive(x => x.Children);
                var activities = parents.Where(x => x.Estactivity != null && x.Estdetail == null && x.Estactivity.EstactivityId == activitySelected.Estactivity.EstactivityId).ToList();
                foreach (var activity in activities)
                {
                    activity.Estactivity.SelectedVendor = refreshedVendorResponse.Value.SupplierNumber;

                    foreach (var estdetail in activity.Children)
                    {
                        estdetail.Estactivity.SelectedVendor = refreshedVendorResponse.Value.SupplierNumber;
                    }
                }

                if (EditedRow.Estactivity != null && EditedRow.Estactivity.EstactivityId == activitySelected.Estactivity.EstactivityId)
                {
                    EditedRow.Estactivity.SelectedVendor = refreshedVendorResponse.Value.SupplierNumber;
                }

                BudgetTreeList.Rebind();
            }
            else
            {
                JobSelected = null;
                await JobChangedHandler();
                SelectedRows.Clear();
                EditedRow = new CombinedPOBudgetTreeModel(); ;
            }
        }
        IsLoading = false;
        ShowMessage(refreshedVendorResponse.IsSuccess, refreshedVendorResponse.Message);
    }
    protected async Task RefreshCosts(TreeListCommandEventArgs args)
    {
        RefreshCostClickedRow = args.Item as CombinedPOBudgetTreeModel;
        await SelectDateModal.Show();
    }
    private async void HandleRefreshCostsSubmit(DateTime selectedDate)
    {
        await SelectDateModal.Hide();
        if (RefreshCostClickedRow != null) // when Refresh Costs is clicked on any Command Column button
        {
            var refreshCostModel = new RefreshSupplierAndCostsModel()
                {
                    EstOptionIds = RefreshCostClickedRow.Estoption != null && RefreshCostClickedRow.Estdetail == null ? new List<int> { RefreshCostClickedRow.Estoption.EstoptionId } : new List<int>(),
                    EstActivityIds = RefreshCostClickedRow.Estactivity != null && RefreshCostClickedRow.Estdetail == null ? new List<int> { RefreshCostClickedRow.Estactivity.EstactivityId } : new List<int>(),
                    EstDetailIds = RefreshCostClickedRow.Estdetail != null ? new List<int> { RefreshCostClickedRow.Estdetail.EstdetailId } : new List<int>(),
                    SelectedDate = selectedDate
                };
            var response = await BudgetService.RefreshCostsAsync(refreshCostModel);
            ShowMessage(response.IsSuccess, response.Message);
            var parents = BudgetData.SelectRecursive(x => x.Children);

            var activities = new List<CombinedPOBudgetTreeModel>();

            if (RefreshCostClickedRow.Estoption != null && RefreshCostClickedRow.Estactivity == null)
            {
                var estoption = parents.SingleOrDefault(x => x.Estoption != null && x.Estactivity == null && x.Estoption.EstoptionId == RefreshCostClickedRow.Estoption.EstoptionId);
                activities = estoption?.Children;
            }
            else
            {
                activities = parents.Where(x => x.Estactivity != null && x.Estdetail == null && x.Estactivity.EstactivityId == RefreshCostClickedRow.Estactivity.EstactivityId).ToList();
            }

            foreach (var activity in activities)
            {
                double totalAmount = 0d;
                foreach (var estDetail in activity.Children)
                {
                    var detail = await BudgetService.GetEstDetailAsync(estDetail.Estdetail.EstdetailId);
                    estDetail.Estdetail.Price = detail.Value.Price;
                    estDetail.Estdetail.Amount = detail.Value.Amount;
                    estDetail.Estdetail.DisplayErrors = detail.Value.DisplayErrors;
                    totalAmount += detail.Value.Amount == null ? 0d : (double)detail.Value.Amount;
                    if (RefreshCostClickedRow.Estdetail != null && EditedRow.Id == RefreshCostClickedRow.Id && EditedRow.Id == estDetail.Id)
                    {
                        EditedRow.Estdetail.Price = detail.Value.Price;
                        EditedRow.Estdetail.Amount = detail.Value.Amount;
                        EditedRow.Estdetail.DisplayErrors = detail.Value.DisplayErrors;
                    }
                }
                activity.Estactivity.ActivityTotal = totalAmount;
                activity.ActivityTotal = totalAmount;
                //sum at option level
                if (SelectedSort == "Estimate/Option/Activity")
                {
                    var findGrandParent = parents.SingleOrDefault(x => x.Id == activity.ParentId);
                    findGrandParent.Estoption.TotalCost = findGrandParent.Children.Sum(x => x.Estactivity.ActivityTotal);
                    findGrandParent.TotalCost = findGrandParent.Children.Sum(x => x.Estactivity.ActivityTotal);
                }
                if (RefreshCostClickedRow.Estdetail == null && EditedRow.Id == RefreshCostClickedRow.Id && EditedRow.Id == activity.Id)
                {
                    EditedRow.ActivityTotal = totalAmount;
                }
            }
        }
        else //when RefreshCosts is clicked on Tool Bar for Selected Items
        {
            var refreshCostModel = new RefreshSupplierAndCostsModel()
            {
                EstOptionIds = new List<int>(),
                EstActivityIds = new List<int>(),
                EstDetailIds = new List<int>(),
                SelectedDate = selectedDate
            };
            foreach (var item in SelectedRows)
            {
                // if (item.Estoption != null && item.Estactivity == null)
                // {
                //     refreshCostModel.EstOptionIds.Add(item.Estoption.EstoptionId);
                // }
                // else if (item.Estactivity != null && item.Estdetail == null)
                // {
                //     refreshCostModel.EstActivityIds.Add(item.Estactivity.EstactivityId);
                // }
                // else 
                if (item.Estdetail != null)
                {
                    refreshCostModel.EstDetailIds.Add(item.Estdetail.EstdetailId);
                }
                // else if (item.Estoption == null && item.Estactivity == null && item.Estdetail == null) // SelectedRow is an estimate
                // {
                //     foreach (var child in item.Children)
                //     {
                //         refreshCostModel.EstOptionIds.Add(child.Estoption.EstoptionId);
                //     }
                // }
            }

            var response = await BudgetService.RefreshCostsAsync(refreshCostModel);
            ShowMessage(response.IsSuccess, response.Message);

            JobSelected = null;
            await JobChangedHandler();
            SelectedRows.Clear();
        }
        BudgetTreeList.Rebind();
        StateHasChanged();
    }
    async Task IssueBudgets(TreeListCommandEventArgs args)
    {        

        var isConfirmed = await Dialogs.ConfirmAsync("Issue Selected Budgets? Note that budgets with errors will not be issued.");
        if (isConfirmed)
        {
            //TODO: fix if it's sort by release/activityt
            //TODO: spinner/progress indicattor
            //remove rows with errors from being submitted
            SelectedRows.RemoveAll(x => x.Estdetail == null || !String.IsNullOrWhiteSpace(x.Estdetail.Errors));
            var response = await BudgetService.IssueBudgetsAsync(SelectedRows);//note selected rows actually only contains the parents, 
                                                                               //the children appear checked but do not get added to the selected. the contoller method finds children again, this was to accomodate load on expand, which is not now being used
                                                                               //TODO: disable the newly checked ones. , and parents/children as needed
            if (response.IsSuccess) 
            {
                foreach (var item in SelectedRows)
                {
                    //TODO: this adjustment in checkboxes should really be based on the response
                    if (item.HasChildren == true)
                    {
                        foreach (var child in item.Children)
                        {
                            if (child.HasChildren)
                            {
                                foreach (var grandChild in child.Children)
                                {
                                    if (grandChild.HasChildren)
                                    {
                                        foreach (var greatGrandChild in grandChild.Children)
                                        {
                                            if (greatGrandChild.Estdetail != null && greatGrandChild.IsIssued == true && string.IsNullOrWhiteSpace(greatGrandChild.Estdetail.Errors))
                                            {
                                                greatGrandChild.IssueEnabled = false;
                                                greatGrandChild.IsIssued = true;
                                                greatGrandChild.Indeterminate = false;
                                            }
                                            else
                                            {
                                                greatGrandChild.IsIssued = false;
                                                greatGrandChild.IssueEnabled = true;
                                                greatGrandChild.Indeterminate = false;
                                            }
                                        }
                                        grandChild.Indeterminate = grandChild.Children.Any(x => x.Indeterminate == true) || (item.Children.Any(x => x.IsIssued == true) && item.Children.Any(x => x.IsIssued == false));
                                        grandChild.IsIssued = grandChild.Children.All(x => x.IsIssued);
                                        grandChild.IssueEnabled = grandChild.Children.Any(x => x.IssueEnabled);
                                    }
                                    else if (grandChild.Estdetail != null && grandChild.IsIssued == true && string.IsNullOrWhiteSpace(grandChild.Estdetail.Errors))
                                    {
                                        grandChild.IssueEnabled = false;
                                        grandChild.IsIssued = true;
                                        grandChild.Indeterminate = false;
                                    }
                                    else
                                    {
                                        grandChild.IsIssued = false;
                                        grandChild.IssueEnabled = true;
                                        grandChild.Indeterminate = false;
                                    }
                                }
                                child.Indeterminate = child.Children.Any(x => x.Indeterminate == true) || (item.Children.Any(x => x.IsIssued == true) && item.Children.Any(x => x.IsIssued == false));
                                child.IsIssued = child.Children.All(x => x.IsIssued);
                                child.IssueEnabled = child.Children.Any(x => x.IssueEnabled);
                            }
                            else if (child.Estdetail != null && child.IsIssued == true && string.IsNullOrWhiteSpace(child.Estdetail.Errors))
                            {
                                child.IssueEnabled = false;
                                child.IsIssued = true;
                                child.Indeterminate = false;
                            }
                            else
                            {
                                child.IsIssued = false;
                                child.IssueEnabled = true;
                                child.Indeterminate = false;
                            }
                        }
                        //now adjust the parent based on any children issued status
                        item.Indeterminate = item.Children.Any(x => x.Indeterminate == true) || (item.Children.Any(x => x.IsIssued == true) && item.Children.Any(x => x.IsIssued == false));
                        item.IsIssued = item.Children.All(x => x.IsIssued);
                        item.IssueEnabled = item.Children.Any(x => x.IssueEnabled);
                    }
                    else
                    {
                        if (item.Estdetail != null && item.IsIssued == true && string.IsNullOrWhiteSpace(item.Estdetail.Errors))
                        {
                            item.IssueEnabled = false;
                            item.IsIssued = true;
                            item.Indeterminate = false;
                        }
                        else
                        {
                            item.IsIssued = false;
                            item.IssueEnabled = true;
                            item.Indeterminate = false;
                        }
                        AdjustCheckBoxState(item);
                    }
                    //TODO: if all children now issued, have to change the parent state
                }
                SelectedRows = new List<CombinedPOBudgetTreeModel>();//clear the list
                ShowMessage(response.IsSuccess, response.Message);
            }
            else 
            {
                SelectedRows = new List<CombinedPOBudgetTreeModel>();//clear the list
                ShowMessage(response.IsSuccess, response.Message);
            }
            StateHasChanged();
        }
    }
    async Task IssueBudget(TreeListCommandEventArgs args)
    {
        var itemToIssue = (CombinedPOBudgetTreeModel)args.Item;
        if(itemToIssue.Estdetail != null)
        {
            if (!string.IsNullOrWhiteSpace(itemToIssue.Estdetail.Errors))
            {
                //show alert can't issue budget with errors
                await Dialogs.AlertAsync("Estimates with errors cannot be issued. Please correct the errors.");
            }
            else
            {
                var responseItem = await BudgetService.IssueBudgetItemAsync(itemToIssue.Estdetail);
                if (responseItem.IsSuccess) 
                {
                    itemToIssue.IssueEnabled = false;
                    itemToIssue.IsIssued = true;
                    itemToIssue.Estdetail.EstjcedetailId = responseItem.Value.EstjcedetailId;
                    itemToIssue.Estdetail.Estjcedetail = responseItem.Value.Estjcedetail;
                    itemToIssue.IssuedAmount = itemToIssue.TotalAmount;
                    AdjustCheckBoxState(itemToIssue);
                    ReSumActivityOption(itemToIssue);
                }
                ShowMessage(responseItem.IsSuccess, responseItem.Message);
                StateHasChanged();
            }            
        }     
    }
    private void AdjustCheckBoxState(CombinedPOBudgetTreeModel item)
    {
        //TODO: cleaner way to do this
        //for use after items issued/canceled, to adjust the checkbox state of parent/grandparent items
        if (item.Estdetail != null)
        {           
            var parents = item.Estdetail.Estoption.Estheader.EstimateDescPe != "Pending/Custom Estimate" ? BudgetData.SelectRecursive(x => x.Children) : PendingCustomEstimateData.SelectRecursive(x => x.Children);
            var findParent = parents.Where(x => x.Id == item.ParentId).SingleOrDefault();
            var findSiblings = findParent.Children;
            if (findSiblings.All(x => x.IsIssued == true))
            {
                findParent.IssueEnabled = false;
                findParent.IsIssued = true;
                findParent.Indeterminate = false;
            }
            else if (findSiblings.Any(x => x.IsIssued == true || x.IsIssued == null))
            {
                findParent.IsIssued = false;
                findParent.IssueEnabled = true;
                findParent.Indeterminate = true;
            }
            else
            {
                findParent.IsIssued = false;
                findParent.IssueEnabled = true; 
                findParent.Indeterminate = false;
            }            
            var findGrandParent = parents.Where(x => x.Id == findParent.ParentId).SingleOrDefault();
            var findAuntsAndUncles = findGrandParent.Children;
            if (findAuntsAndUncles.All(x => x.IsIssued == true))
            {
                findGrandParent.IssueEnabled = false;
                findGrandParent.Indeterminate = false;
                findGrandParent.IsIssued = true;
            }
            else if (findAuntsAndUncles.Any(x => x.IsIssued == true || x.IsIssued == null))
            {
                findGrandParent.IsIssued = false;
                findGrandParent.Indeterminate = true;
                findGrandParent.IssueEnabled = true;
            }
            else
            {
                findGrandParent.IsIssued = false;
                findGrandParent.IssueEnabled = true;
                findGrandParent.Indeterminate = false;
            }
            var findGreatGrandParent = parents.Where(x => x.Id == findGrandParent.ParentId).SingleOrDefault();
            // var findGreatGrandParent = item.IsPendingCustomEstimate == true ? PendingCustomEstimateData.Where(x => x.Id == findGrandParent.ParentId).SingleOrDefault() : BudgetData.Where(x => x.Id == findGrandParent.ParentId).SingleOrDefault();
            var findGreatAuntsAndUncles = findGreatGrandParent.Children;
            if (findGreatAuntsAndUncles.All(x => x.IsIssued == true))
            {
                findGreatGrandParent.IssueEnabled = false;
                findGreatGrandParent.IsIssued = true;
            }
            else if (findGreatAuntsAndUncles.Any(x => x.IsIssued == true || x.IsIssued == null))
            {
                findGreatGrandParent.IsIssued = false;
                findGreatGrandParent.IssueEnabled = true;
                findGrandParent.Indeterminate = true;
            }
            else
            {
                findGreatGrandParent.IsIssued = false;
                findGreatGrandParent.IssueEnabled = true;
                findGrandParent.Indeterminate = false;
            }
        }
    }    
    async Task CancelBudget(TreeListCommandEventArgs args)
    {
        var itemToCancel = (CombinedPOBudgetTreeModel)args.Item;
        if(itemToCancel != null && itemToCancel.Estdetail != null)
        {
            var responseItem = await BudgetService.CancelBudgetItemAsync(itemToCancel.Estdetail);
            if (responseItem.IsSuccess)
            {
                if (responseItem.Value.EstjcedetailId == null)
                {
                    //this case the initail item had not been sent to NAV, so ok to just reissue
                    itemToCancel.IssueEnabled = true;
                    itemToCancel.IsIssued = false;
                    itemToCancel.Estdetail.EstjcedetailId = responseItem.Value.EstdetailId;
                    itemToCancel.Estdetail.Estjcedetail = responseItem.Value.Estjcedetail;
                }
                else
                {
                    //the initial item had been sent to nav, so the cancel offset item has to be sent to NAV too before it can be reissued
                    itemToCancel.IssueEnabled = false;//not necessarily. only issued enabled true if cancelation sent to nav, or if initial send was not sent
                    itemToCancel.IsIssued = true;//not sure about this
                    itemToCancel.Estdetail.EstjcedetailId = responseItem.Value.EstdetailId;
                    itemToCancel.Estdetail.Estjcedetail = responseItem.Value.Estjcedetail;
                }

                AdjustCheckBoxState(itemToCancel);
            }
            ShowMessage(responseItem.IsSuccess, responseItem.Message);
        }

    }
    private async Task ShowAddItemToBudget(TreeListCommandEventArgs args)
    {
        var option = args.Item as CombinedPOBudgetTreeModel;
        if(option != null && option.Estoption != null)
        {
            SelectedEstOptionId = (int)option.Estoption.EstoptionId;
            SelectedHeaderId = (int)option.Estoption.EstheaderId;
            await AddItemToBudgetModal.Show();
        }
        else if(option.Estheader != null)
        {
            SelectedHeaderId = (int)option.Estheader.EstheaderId;
            await AddItemToBudgetModal.Show();
        }
    }
    private async void HandleValidAddItemsSubmit(ResponseModel<List<EstdetailDto>> responseItems)
    {
        //TODO: check how this works on release sort
        //TODO: update the sum in the activities and options
        //TODO: show added data, adjust checkbox state
        await AddItemToBudgetModal.Hide();
        foreach(var responseItem in responseItems.Value)
        {
            var treelistItemToAdd = new CombinedPOBudgetTreeModel()
                {
                    Estdetail = responseItem,
                    Estactivity = responseItem.Estactivity,
                    IssuedAmount = 0,
                    TotalAmount = (decimal?)responseItem.Amount,
                    Id = Guid.NewGuid(),
                    HasChildren = false,
                    IsIssued = false,
                    IssueEnabled = true,
                    Indeterminate = false,
                };
            if (SelectedSort == "Estimate/Option/Activity")
            {
                //find the activity in the option if it exists, if not add it,
                var findOption = BudgetData.SelectMany(x => x.Children).ToList().SingleOrDefault(x => x.Estoption != null && x.Estoption.EstoptionId == responseItem.Estoption.EstoptionId);
                if (findOption != null)
                {
                    var findActivity = findOption.Children.Where(x => x.Estactivity.EstactivityId == responseItem.Estactivity.EstactivityId).SingleOrDefault();
                    if (findActivity != null)
                    {
                        findActivity.Children.Add(treelistItemToAdd);
                        treelistItemToAdd.ParentId = findActivity.Id;
                        ReSumActivityOption(treelistItemToAdd);
                    }
                    else
                    {
                        //add the activity
                        var addActivity = new CombinedPOBudgetTreeModel()
                            {
                                Estactivity = responseItem.Estactivity,
                                ActivityTotal = treelistItemToAdd.Estdetail.Amount,
                                ParentId = findOption.Id,
                                Id = Guid.NewGuid(),
                                HasChildren = true,
                                Children = new List<CombinedPOBudgetTreeModel>() { treelistItemToAdd },
                                IsIssued = false,
                                IssueEnabled = true,
                                Indeterminate = false,
                                IsPendingCustomEstimate = responseItem.Estoption.Estheader.EstimateDescPe != "Pending/Custom Estimate" ? false : true
                            };
                        treelistItemToAdd.ParentId = addActivity.Id;
                        addActivity.Estactivity.ActivityTotal = treelistItemToAdd.Estdetail.Amount;
                        findOption.Children.Add(addActivity);
                        findOption.Estoption.TotalCost = findOption.Children.Sum(x => x.Estactivity?.ActivityTotal);
                        findOption.TotalCost = findOption.Children.Sum(x => x.Estactivity?.ActivityTotal);
                        ReSumActivityOption(treelistItemToAdd);
                    }
                }
                else
                {
                    var findEstHeader = BudgetData.SingleOrDefault(x => x.Estheader != null && x.Estheader.EstheaderId == responseItem.Estoption.EstheaderId);
                    if (findEstHeader != null)
                    {
                        var addActivity = new CombinedPOBudgetTreeModel()
                            {
                                Estactivity = responseItem.Estactivity,
                                Id = Guid.NewGuid(),
                                HasChildren = true,
                                Children = new List<CombinedPOBudgetTreeModel>() { treelistItemToAdd },
                                IsIssued = false,
                                IssueEnabled = true,
                                Indeterminate = false,
                            };
                        treelistItemToAdd.ParentId = addActivity.Id;
                        addActivity.Estactivity.ActivityTotal = treelistItemToAdd.Estdetail.Amount;
                        addActivity.ActivityTotal = treelistItemToAdd.Estdetail.Amount;
                        var addEstOption = new CombinedPOBudgetTreeModel()
                            {
                                Id = Guid.NewGuid(),
                                ParentId = findEstHeader.Id,
                                HasChildren = true,
                                Estoption = responseItem.Estoption,
                                TotalCost = treelistItemToAdd.Estdetail.Amount,
                                IsIssued = false,
                                IssueEnabled = true,
                                Indeterminate = false,
                                Children = new List<CombinedPOBudgetTreeModel>() { addActivity }
                            };
                        addEstOption.Estoption.TotalCost = treelistItemToAdd.Estdetail.Amount;
                        addActivity.ParentId = addEstOption.Id;
                        findEstHeader.Children.Add(addEstOption);
                        addEstOption.ParentId = findEstHeader.Id;
                        ReSumActivityOption(treelistItemToAdd);
                    }
                }
            }
            else
            {
                //release sort
                //TODO: release sort doesn't have option, so there's no button to add items -- fix
                // var findRelease = BudgetData.SelectMany(x => x.Children).ToList().SingleOrDefault(x => x.ReleaseCode != null && x.ReleaseCode == responseItem.Estactivity.Releasecode);
                // if (findRelease != null)
                // {
                //     var findActivity = findRelease.Children.Where(x => x.Estactivity.EstactivityId == responseItem.Estactivity.EstactivityId).SingleOrDefault();
                //     if (findActivity != null)
                //     {
                //         findActivity.Children.Add(treelistItemToAdd);
                //         treelistItemToAdd.ParentId = findActivity.Id;
                //         ReSumActivityOption(treelistItemToAdd);
                //     }
                //     else
                //     {
                //         //add the activity
                //         var addActivity = new CombinedPOBudgetTreeModel()
                //             {
                //                 Estactivity = responseItem.Estactivity,
                //                 ActivityTotal = treelistItemToAdd.Estdetail.Amount,
                //                 ParentId = findRelease.Id,
                //                 Id = Guid.NewGuid(),
                //                 HasChildren = true,
                //                 Children = new List<CombinedPOBudgetTreeModel>() { treelistItemToAdd },
                //                 IsIssued = false,
                //                 IssueEnabled = true,
                //                 Indeterminate = false,
                //             };
                //         addActivity.Estactivity.ActivityTotal = treelistItemToAdd.Estdetail.Amount;
                //         findRelease.Children.Add(addActivity);
                //     }
                // }
            }
        }
        
        BudgetTreeList.Rebind();
        StateHasChanged();
    }
    private async Task ShowAddPendingEstimate(TreeListCommandEventArgs args)
    {
        //await AddPendingEstimateModal.Show();
        IsPendingCustomEstimate = true;
        await AddEstimateModal.Show();

    }
    private async Task ShowAddEstimate(TreeListCommandEventArgs args)
    {
        IsPendingCustomEstimate = false;
        await AddEstimateModal.Show();

    }
    // private async void HandleValidAddPendingEstimateSubmit(ResponseModel<CombinedPOBudgetTreeModel> responseBudget)
    // {   
    //     ShowMessage(responseBudget.IsSuccess, responseBudget.Message);
    //     if (responseBudget.IsSuccess)
    //     {
    //         if (SelectedSort == "Release/Activity")
    //         {
    //             var releaseSortData = ConvertToReleaseSort(responseBudget.Value);
    //             PendingCustomEstimateData.Add(responseBudget.Value);
    //         }
    //         else
    //         {
    //             PendingCustomEstimateData.Add(responseBudget.Value);
    //         }
    //     }
    //     await AddPendingEstimateModal.Hide();
    //     StateHasChanged();
    // }
    private async void HandleValidAddEstimateSubmit(ResponseModel<CombinedPOBudgetTreeModel> responseBudget)
    {
        if (responseBudget.IsSuccess)
        {
            if(responseBudget.Value.IsPendingCustomEstimate == true)
            {
                if (SelectedSort == "Release/Activity")
                {
                    var releaseSortData = ConvertMulitpleToReleaseSort(responseBudget.Value);
                    PendingCustomEstimateData.Add(releaseSortData);
                }
                else
                {
                    PendingCustomEstimateData.Add(responseBudget.Value);
                }
            }
            else
            {
                if (SelectedSort == "Release/Activity")
                {
                    var releaseSortData = ConvertMulitpleToReleaseSort(responseBudget.Value);
                    BudgetData.Add(releaseSortData);
                }
                else
                {
                    BudgetData.Add(responseBudget.Value);
                }
            }
            
        }
        ShowMessage(responseBudget.IsSuccess, responseBudget.Message);
        await AddEstimateModal.Hide();
        StateHasChanged();
    }
    private CombinedPOBudgetTreeModel ConvertToReleaseSort(CombinedPOBudgetTreeModel treeItemToSort)
    {
        //take one estimate from est/opt/act sort to release sort- this only works if only one option has been added
        var optionChildrenToRelease = treeItemToSort.Children.Where(x => x.Estoption != null).ToList();
        foreach(var option in optionChildrenToRelease)
        {
            option.Estoption = null;//just to convert the option row to a release row
        }
        return treeItemToSort;
    }
    private CombinedPOBudgetTreeModel ConvertMulitpleToReleaseSort(CombinedPOBudgetTreeModel treeItemToSort)
    {
        //take one estimate from est/opt/act sort to release sort- this only works if only one option has been added
        var optionChildrenToRelease = treeItemToSort.Children.Where(x => x.Estoption != null).ToList();
        var releases = treeItemToSort.Children.SelectMany(x => x.Children).DistinctBy(x => x.ReleaseCode).ToList();
        treeItemToSort.Children = releases.Select(x => new CombinedPOBudgetTreeModel()
        {
            ReleaseCode = x.ReleaseCode,
            ParentId = treeItemToSort.Id,
            HasChildren = true
        }).OrderBy(x => x.ReleaseCode).ToList();

        foreach (var release in treeItemToSort.Children)
        {
            //TODO: set parent id;
            release.Children = optionChildrenToRelease.SelectMany(x => x.Children.Where(x => x.Estactivity != null && x.ReleaseCode == release.ReleaseCode)).ToList();
            foreach(var activity in release.Children)
            {
                activity.ParentId = release.Id;
            }
        }
        return treeItemToSort;
    }
    protected async Task ChangeSortHandler(object theUserChoice)
    {
        if(CheckSortChanged != SelectedSort)
        {
            CheckSortChanged = SelectedSort;
            if (JobSelected != null)
            {
                if (SelectedSort == "Release/Activity")
                {
                    var getData = await BudgetService.GetBudgetByJobReleaseSortAsync(JobSelected);
                    BudgetData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData.Value.Where(x => x.Estheader.EstimateDescPe != "Pending/Custom Estimate"));
                    PendingCustomEstimateData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData.Value.Where(x => x.Estheader.EstimateDescPe == "Pending/Custom Estimate"));
                }
                else
                {
                    var getData = await BudgetService.GetBudgetByJobAsync(JobSelected);
                    BudgetData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData.Value.Where(x => x.Estheader.EstimateDescPe != "Pending/Custom Estimate"));
                    PendingCustomEstimateData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData.Value.Where(x => x.Estheader.EstimateDescPe == "Pending/Custom Estimate"));
                }
            }
        }
    }
    async void ShowMessage(bool success, string message)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success? ThemeConstants.Notification.ThemeColor.Success: ThemeConstants.Notification.ThemeColor.Error
            });       
    }
    public void Dispose()
    {
        this.SubdivisionJobPickService.OnChanged -= JobChangedHandler;
    }
    
}


