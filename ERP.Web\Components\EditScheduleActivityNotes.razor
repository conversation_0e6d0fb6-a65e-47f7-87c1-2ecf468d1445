﻿
@inject ScheduleService ScheduleService
@inject PoService PoService
@inject AuthenticationStateProvider AuthenticationStateProvider
@using ERP.Data.Models.ExtensionMethods
<style>
 /*    .k-animation-container {
        position: absolute;
        z-index: 300000 !important;
    } */
</style>
<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="400px"
               Height="550px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <h4 class="page-title">Schedule Activity</h4>
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@SelectedActivity" OnValidSubmit="@HandleValidSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />           
            <div>
                <label class="form-label">Schedule Activity</label>
                <TelerikTextBox Enabled="false" @bind-Value="@SelectedActivity.Sactivity.ActivityName"></TelerikTextBox>
            </div> 
            <br />
            @if(SelectedActivity.SubNumberNavigation != null)
            {

                <div style="margin-top:1em">
                    <label class="form-label">Supplier</label>
                    <TelerikTextBox Enabled="false" @bind-Value="@SelectedActivity.SubNumberNavigation.SubName"></TelerikTextBox>
                </div>
            }
                     
            <div style="margin-top:1em">
                <label class="form-label">Supplier Note  </label>
                <TelerikTextArea Enabled="AllowEdit" @bind-Value="@SelectedActivity.SupplierNote"></TelerikTextArea>
            </div>
            <br/>
            <div style="margin-top:1em">
                <label class="form-label">Internal Note:  </label>
                <TelerikTextArea Enabled = "AllowEdit" @bind-Value="@SelectedActivity.Note"></TelerikTextArea>                           
            </div>
            <br />  
            <br />
            <div class="customSeparator">
                @if(AllowEdit)
                {
                    <button type="submit" class="btn btn-primary">Update</button>
                    <button type="button" @onclick="CancelUpdate" class="btn btn-secondary">Cancel</button>
                }
               
                <div style=@ShowLoading>Submitting. Please wait...</div>
                <div style=@ShowError>Something went wrong.</div>
            </div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    public bool IsModalVisible { get; set; }
    public string ShowLoading{ get; set; } = "display:none";
    public string ShowError { get; set; } = "display:none";
    private bool AllowEdit { get; set; } = true;
    [Parameter]
    public EventCallback<ResponseModel<ScheduleSactivityDto>> HandleAddSubmit { get; set; }
    [Parameter]
    public ScheduleSactivityDto? SelectedActivity { get; set; }
   
    public async Task Show()
    {
        IsModalVisible = true;
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }
    
    private async void HandleValidSubmit()
    {
        ShowLoading = "";       
        var response = await ScheduleService.UpdateScheduleSactivityNotesAsync(SelectedActivity);
        ShowLoading = "display:none";
        await HandleAddSubmit.InvokeAsync(response);
    }
    async void CancelUpdate()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;        
    }
}
