﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class MaterialColorPredefined
{
    public int MaterialColorPredefinedId { get; set; }

    public int MaterialId { get; set; }

    public int ColorSchemeId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public string? ImageLink { get; set; }

    public virtual ColorScheme ColorScheme { get; set; } = null!;

    public virtual Material Material { get; set; } = null!;

    public virtual ICollection<MaterialColorScheme> MaterialColorSchemes { get; set; } = new List<MaterialColorScheme>();
}
