﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class AddendaModel
    {
        public int? MasterItemId { get; set; }
        public string? Activity {  get; set; }
        public string? OptionCode { get; set; }
        public string? ItemDesc { get; set; }    
        public string? HouseTypeName { get; set; }
        public string? HouseType { get; set;  }
        public double? Qty { get; set; }
        public string? OrderUnit { get; set; }
        public double? UnitCost { get; set; }
        public double? TotalCost { get; set; }
    }
}
