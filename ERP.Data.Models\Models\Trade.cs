﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Trade
{
    public int TradeId { get; set; }

    public string? TradeName { get; set; }

    public string? TradeDesc { get; set; }

    public string? Workdays { get; set; }

    public string? TermsFilename { get; set; }

    public string? CubitId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<Estactivity> Estactivities { get; set; } = new List<Estactivity>();

    public virtual ICollection<Pactivity> Pactivities { get; set; } = new List<Pactivity>();

    public virtual ICollection<Poheader> Poheaders { get; set; } = new List<Poheader>();

    public virtual ICollection<Sactivity> Sactivities { get; set; } = new List<Sactivity>();

    public virtual ICollection<TradeSupplier> TradeSuppliers { get; set; } = new List<TradeSupplier>();
}
