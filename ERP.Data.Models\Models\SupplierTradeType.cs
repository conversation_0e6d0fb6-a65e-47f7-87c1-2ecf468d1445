﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class SupplierTradeType
{
    public int SubNumber { get; set; }

    public int SubTypeId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual Supplier SubNumberNavigation { get; set; } = null!;

    public virtual SupplierType SubType { get; set; } = null!;
}
