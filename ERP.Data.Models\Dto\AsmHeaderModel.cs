﻿namespace ERP.Data.Models.Dto
{
    public class AsmHeaderModel
    {
        public int AsmHeaderId { get; set; }

        public int? MasterPlanId { get; set; }

        public int? AsmGroupId { get; set; }

        public int? HomeAreaId { get; set; }
        public string? HomeAreaName { get; set; }
        public int? PeHeader { get; set; }
        public int? OptionGroupId { get; set; }
        public string? OptionGroupName { get; set; }
        public string? MasterOptionCode { get; set; }
        public string? AssemblyCode { get; set; }

        public string? AssemblyDesc { get; set; }
        public string? DisplayDesc { get; set; }//show both code and description for search

        public string? AssemblyNotes { get; set; }

        public string? AssemblyUnit { get; set; }

        public string? DeletedFromPe { get; set; }

        public string? Formula { get; set; }

        public string? Calculation { get; set; }

        public int? EstDbOwner { get; set; }

        public int? TlpeOptionCategoryId { get; set; }

        public int? OptionScope { get; set; }

        public int? AssemblySize { get; set; }

        public string? IsElevation { get; set; }
        public bool BoolIsElevation { get; set; }

        public string? IsBaseHouse { get; set; }
        public bool BoolIsBaseHouse { get; set; }
        public DateTime CreatedDateTime { get; set; }

        public DateTime? UpdatedDateTime { get; set; }

        public string? UpdatedBy { get; set; }

        public string? PeDatetimestamp { get; set; }

        public string? PeUpdated { get; set; }

        public string? CreatedBy { get; set; }

        public bool? IsActive { get; set; }
        public string? DisplayName { get; set; }

        public List<int>? MasterPlanIds { get; set; } // Added 09/13. Check to make sure it's not breaking API.
    }
}
