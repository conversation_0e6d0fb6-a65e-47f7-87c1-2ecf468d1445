﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class MasterOptionDto : IMapFrom<MasterOption>
{
    public int OptionId { get; set; }

    public int OptionGroupId { get; set; }

    public int? UnitMeasureId { get; set; }

    public string? OptionCode { get; set; }

    public string? OptionDesc { get; set; }

    public string? OptionLongDesc { get; set; }

    public string? Restrictions { get; set; }

    public decimal? UnitPrice { get; set; }

    public decimal? UnitCost { get; set; }

    public decimal? UnitQty { get; set; }

    public bool? AddPriceToBase { get; set; }

    public bool? PrintOption { get; set; }

    public bool? CustChoiceReq { get; set; }

    public bool? AgentModType { get; set; }

    public bool? AgentModQty { get; set; }

    public bool? AgentModPrice { get; set; }

    public bool? FloorOption { get; set; }

    public bool? Elevation { get; set; }

    public bool? Active { get; set; }

    public decimal? MaxQty { get; set; }

    public string? ImageUrl { get; set; }

    public string? OptionSelectionType { get; set; }

    public bool? Room { get; set; }

    public bool? CascadeOptionSettings { get; set; }

    public bool? CascadeUsersCanMod { get; set; }

    public string? DepositDesc { get; set; }

    public decimal? Amount { get; set; }

    public short? DaysFromTrigger { get; set; }

    public int? DepositTypeId { get; set; }

    public string? BeforeAfter { get; set; }

    public int? TriggerEventId { get; set; }

    public int? ConstrStageId { get; set; }

    public int? EscrowStageId { get; set; }

    public bool? DollarAmt { get; set; }

    public bool? ActiveDepositSched { get; set; }

    public int? SqFt { get; set; }

    public DateTime? CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public List<MasterAttributeGroupDto>? MasterAttributeGroups { get; set; }

    public List<OptionAttributeGroupItemDto>? OptionAttributeGroupItems { get; set; }

    public int? OptionAttributeGroupItemId { get; set; }

    public string? DisplayText { get; set; }
}
