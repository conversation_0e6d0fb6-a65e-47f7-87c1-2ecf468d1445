﻿using DocumentFormat.OpenXml.Spreadsheet;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Models;
using ERP.Web.Pages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using NLog;
using System.Data.SqlClient;
using System.Diagnostics;
using Telerik.Blazor.Components;
using Telerik.DataSource;
using Telerik.DataSource.Extensions;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class ColorSchemeService
    {
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public ColorSchemeService(IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, AuthenticationStateProvider authenticationStateProvider)
        {
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _authenticationStateProvider = authenticationStateProvider;
        }
        public async Task<ResponseModel<List<ColorSchemeDto>>> GetColorsAsync()
        {
            //Get distinct colors from color schemes, for a dropdown
            var responseModel = new ResponseModel<List<ColorSchemeDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/colorscheme/GetColors");
                var responseString = await response.Content.ReadAsStringAsync();
                responseModel = JsonConvert.DeserializeObject<ResponseModel<List<ColorSchemeDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }
        public async Task<ResponseModel<List<ColorSchemeDto>>> GetColorsAsync(int materialId)
        {
            //Get distinct colors from color schemes by material, for a dropdown
            var responseModel = new ResponseModel<List<ColorSchemeDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/colorscheme/Colors/{materialId}");
                var responseString = await response.Content.ReadAsStringAsync();
                responseModel = JsonConvert.DeserializeObject<ResponseModel<List<ColorSchemeDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }
        public async Task<ResponseModel<List<MaterialDto>>> GetMaterialsAsync()
        {
            //Get distinct materials from color schemes, for a dropdown
            var responseModel = new ResponseModel<List<MaterialDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/colorscheme/getmaterials");
                var responseString = await response.Content.ReadAsStringAsync();
                responseModel = JsonConvert.DeserializeObject<ResponseModel<List<MaterialDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }
        public async Task<ResponseModel<List<ColorSchemeElevationTreeModel>>> GetColorSchemesAsync()
        {
           
            var responseModel = new ResponseModel<List<ColorSchemeElevationTreeModel>>();
           
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/colorscheme/getmastercolorschemes");
                var responseString = await response.Content.ReadAsStringAsync();
                responseModel = JsonConvert.DeserializeObject<ResponseModel<List<ColorSchemeElevationTreeModel>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }
        public async Task<ResponseModel<List<ColorSchemeElevationTreeModel>>> GetAllColorSchemesAsync()
        {

            var responseModel = new ResponseModel<List<ColorSchemeElevationTreeModel>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/colorscheme/getallcolorschemes");
                var responseString = await response.Content.ReadAsStringAsync();
                responseModel = JsonConvert.DeserializeObject<ResponseModel<List<ColorSchemeElevationTreeModel>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }
        public async Task<ResponseModel<List<ColorSchemeElevationTreeModel>>> GetElevationsAsync()
        {
            var responseModel = new ResponseModel<List<ColorSchemeElevationTreeModel>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/colorscheme/GetSubdivisionPlanElevations");
                var responseString = await response.Content.ReadAsStringAsync();
                responseModel = JsonConvert.DeserializeObject<ResponseModel<List<ColorSchemeElevationTreeModel>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }
        public async Task<ResponseModel<List<MaterialColorPredefinedDto>>> GetPredefinedMaterialColorCombinationsAsync()
        {
            var combinations = new List<MaterialColorPredefinedDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/colorscheme/GetPredefinedMaterialColorCombinations/");
                var responseString = await response.Content.ReadAsStringAsync();

                var result = JsonConvert.DeserializeObject<ResponseModel<List<MaterialColorPredefinedDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<MaterialColorPredefinedDto>>() { Value = combinations, IsSuccess = false, Message = "Failed to get Predefined Material Color combinations" };
        }

        public async Task<ResponseModel<MaterialColorPredefinedDto>> AddPredefinedMaterialColorCombinationAsync(MaterialColorPredefinedDto predefinedMaterialColor)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<MaterialColorPredefinedDto, ResponseModel<MaterialColorPredefinedDto>>(
                            "DownstreamApi", predefinedMaterialColor,
                             options => {
                                 options.RelativePath = "api/colorscheme/AddPredefinedMaterialColorCombination/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<MaterialColorPredefinedDto>() { IsSuccess = false, Message = "Failed to add" };
        }

        public async Task<ResponseModel<MaterialColorPredefinedDto>> UpdatePredefinedMaterialColorCombinationAsync(MaterialColorPredefinedDto predefinedMaterialColor)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<MaterialColorPredefinedDto, ResponseModel<MaterialColorPredefinedDto>>(
                            "DownstreamApi", predefinedMaterialColor,
                             options => {
                                 options.RelativePath = "api/colorscheme/UpdatePredefinedMaterialColorCombination/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<MaterialColorPredefinedDto>() { IsSuccess = false, Message = "Failed to update" };
        }
        public async Task<ResponseModel<List<MaterialColorSchemeDto>>> AddSchemesAsync(List<MaterialColorSchemeDto> schemesToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<MaterialColorSchemeDto>, ResponseModel<List<MaterialColorSchemeDto>>>(
                            "DownstreamApi", schemesToAdd,
                             options => {
                                 options.RelativePath = "api/colorscheme/AddSchemes/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<MaterialColorSchemeDto>>() { IsSuccess = false, Message = "Failed to update" };
        }
        public async Task<ResponseModel<ColorSchemeElevationTreeModel>> UpdateSchemeAsync(ColorSchemeElevationTreeModel schemeToUpdte)
        {
            try
            {
                //All this should do is rename the scheme
                var response = await _downstreamAPI.PostForUserAsync<ColorSchemeElevationTreeModel, ResponseModel<ColorSchemeElevationTreeModel>>(
                            "DownstreamApi", schemeToUpdte,
                             options => {
                                 options.RelativePath = "api/colorscheme/UpdateScheme/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ColorSchemeElevationTreeModel>() { IsSuccess = false, Message = "Failed to update" };
        }
        public async Task<ResponseModel<ColorSchemeElevationTreeModel>> DeleteSchemeFromPlanAsync(ColorSchemeElevationTreeModel schemeToDelete)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ColorSchemeElevationTreeModel, ResponseModel<ColorSchemeElevationTreeModel>>(
                            "DownstreamApi", schemeToDelete,
                             options => {
                                 options.RelativePath = "api/colorscheme/DeleteSchemeFromPlan/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ColorSchemeElevationTreeModel>() { IsSuccess = false, Message = "Failed to update" };
        }
        public async Task<ResponseModel<ColorSchemeElevationTreeModel>> DeleteSchemeAsync(ColorSchemeElevationTreeModel schemeToDelete)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ColorSchemeElevationTreeModel, ResponseModel<ColorSchemeElevationTreeModel>>(
                            "DownstreamApi", schemeToDelete,
                             options => {
                                 options.RelativePath = "api/colorscheme/DeleteScheme/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ColorSchemeElevationTreeModel>() { IsSuccess = false, Message = "Failed to update" };
        }
        public async Task<ResponseModel<ColorSchemeElevationTreeModel>> AddSchemeAsync(ColorSchemeElevationTreeModel schemeToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ColorSchemeElevationTreeModel, ResponseModel<ColorSchemeElevationTreeModel>>(
                            "DownstreamApi", schemeToAdd,
                             options => {
                                 options.RelativePath = "api/colorscheme/AddScheme/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ColorSchemeElevationTreeModel>() { IsSuccess = false, Message = "Failed to add" };
        }
        public async Task<ResponseModel<ColorSchemeElevationTreeModel>> CopySchemeAsync(ColorSchemeElevationTreeModel schemeToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ColorSchemeElevationTreeModel, ResponseModel<ColorSchemeElevationTreeModel>>(
                            "DownstreamApi", schemeToAdd,
                             options => {
                                 options.RelativePath = "api/colorscheme/copyscheme/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ColorSchemeElevationTreeModel>() { IsSuccess = false, Message = "Failed to add" };
        }
        public async Task<ResponseModel<MaterialColorSchemeDto>> AddMaterialColorSchemeAsync(MaterialColorSchemeDto schemeToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<MaterialColorSchemeDto, ResponseModel<MaterialColorSchemeDto>>(
                            "DownstreamApi", schemeToAdd,
                             options => {
                                 options.RelativePath = "api/colorscheme/AddMaterialColorToMasterScheme/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<MaterialColorSchemeDto>() { IsSuccess = false, Message = "Failed to update" };
        }
        public async Task<List<string>> GetPlansForSchemeAsync(string colorSchemeName)
        {
            var responseModel = new List<string>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/colorscheme/GetPlansForScheme/{colorSchemeName}");
                var responseString = await response.Content.ReadAsStringAsync();
                responseModel = JsonConvert.DeserializeObject<List<string>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }
        public async Task<ResponseModel<MaterialColorSchemeDto>> UpdateMasterColorSchemeAsync(MaterialColorSchemeDto schemeToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<MaterialColorSchemeDto, ResponseModel<MaterialColorSchemeDto>>(
                            "DownstreamApi", schemeToAdd,
                             options => {
                                 options.RelativePath = "api/colorscheme/UpdteMasterScheme/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);    
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<MaterialColorSchemeDto>() { IsSuccess = false, Message = "Failed to update" };
        }
        public async Task<ResponseModel<MaterialColorSchemeDto>> DeleteMasterColorSchemeAsync(MaterialColorSchemeDto schemeToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<MaterialColorSchemeDto, ResponseModel<MaterialColorSchemeDto>>(
                            "DownstreamApi", schemeToAdd,
                             options => {
                                 options.RelativePath = "api/colorscheme/DeleteMasterScheme/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<MaterialColorSchemeDto>() { IsSuccess = false, Message = "Failed to delete" };
        }
        public async Task<ResponseModel<ElevationPlanSubdivisionModel>> AddColorSchemeToOptionAsync(ElevationPlanSubdivisionModel schemeToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ElevationPlanSubdivisionModel, ResponseModel<ElevationPlanSubdivisionModel>>(
                            "DownstreamApi", schemeToAdd,
                             options => {
                                 options.RelativePath = "api/colorscheme/AddSchemeToOption/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ElevationPlanSubdivisionModel>() { IsSuccess = false, Message = "Failed to add color scheme to option" };
        }
        public async Task<ResponseModel<ElevationPlanSubdivisionModel>> AddColorSchemeToPlanAsync(ElevationPlanSubdivisionModel schemeToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ElevationPlanSubdivisionModel, ResponseModel<ElevationPlanSubdivisionModel>>(
                            "DownstreamApi", schemeToAdd,
                             options => {
                                 options.RelativePath = "api/colorscheme/AddSchemeToPlan/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ElevationPlanSubdivisionModel>() { IsSuccess = false, Message = "Failed to add color scheme to plan" };
        }
        public async Task<ResponseModel<ElevationPlanSubdivisionModel>> AddColorSchemeToSubdivisionAsync(ElevationPlanSubdivisionModel schemeToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ElevationPlanSubdivisionModel, ResponseModel<ElevationPlanSubdivisionModel>>(
                            "DownstreamApi", schemeToAdd,
                             options => {
                                 options.RelativePath = "api/colorscheme/AddSchemeToSubdivision/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<ElevationPlanSubdivisionModel>() { IsSuccess = false, Message = "Failed to add color scheme to subdivision" };
        }
        public async Task<ResponseModel<List<ColorSchemeLoadTemplateDto>>> ImportExcel(FileSelectFileInfo file)
        {
            var ImportedColorScheme = new List<ColorSchemeLoadTemplateDto>();
            try
            {
                var fileData = new byte[file.Stream.Length];
                await file.Stream.ReadAsync(fileData);
                //TODO: messge to user on any error parsing file
                using (var ms = new MemoryStream(fileData))
                {
                    using (var excelWorkbook = new ClosedXML.Excel.XLWorkbook(ms))
                    {
                        var nonEmptyDataRows = excelWorkbook.Worksheet(1).RowsUsed();

                        foreach (var dataRow in nonEmptyDataRows)
                        {
                            //for row number check
                            if (dataRow.RowNumber() >= 2)
                            {
                                ImportedColorScheme.Add(new ColorSchemeLoadTemplateDto()
                                {
                                    SubdivsionName = dataRow.Cell(1).IsEmpty() ? null : (string?)dataRow.Cell(1).Value,                                    
                                    SubdivisonNumber = dataRow.Cell(2).IsEmpty() ? null : (string?)dataRow.Cell(2).Value,                                   
                                    //Plan Name is column 3
                                    PlanNumber = dataRow.Cell(4).IsEmpty() ? null : dataRow.Cell(4).Value.ToString(),
                                    OptionName = dataRow.Cell(5).IsEmpty() ? null : (string?)dataRow.Cell(5).Value,
                                    OptionCode = dataRow.Cell(6).IsEmpty() ? null : (string?)dataRow.Cell(6).Value,                                 
                                    ColorSchemeNum = dataRow.Cell(7).IsEmpty() ? null : (string?)dataRow.Cell(7).Value,
                                    Material = dataRow.Cell(8).IsEmpty() ? null : (string?)dataRow.Cell(8).Value,
                                    ColorScheme = dataRow.Cell(9).IsEmpty() ? null : (string?)dataRow.Cell(9).Value,
                                    //CanBeReplaced = dataRow.Cell(9).IsEmpty() ? null : dataRow.Cell(9).Value.ToString()
                                });
                            }
                        }
                    }
                    ms.Close();
                }

                return new ResponseModel<List<ColorSchemeLoadTemplateDto>>() { Value = ImportedColorScheme, IsSuccess = true };
            }

            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<ColorSchemeLoadTemplateDto>>() { IsSuccess = false, Message = "Failed to import Excel file" };
        }
        public async Task<ResponseModel<List<ColorSchemeLoadTemplateDto>>> SaveColorSchemeImportAsync(List<ColorSchemeLoadTemplateDto> colorschemesToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<ColorSchemeLoadTemplateDto>, ResponseModel<List<ColorSchemeLoadTemplateDto>>>(
                           "DownstreamApi", colorschemesToAdd,
                            options => {
                                options.RelativePath = "api/colorScheme/LoadColorScheme/";
                            });
                //int count = 0;
                //while (count < colorschemesToAdd.Count)
                //{
                //    //var colorSchemesToSend = colorschemesToAdd.Skip(count).Take(10000).ToList();
                //    //var response = await _downstreamAPI.PutForUserAsync<List<ColorSchemeLoadTemplateDto>, List<ColorSchemeLoadTemplateDto>>(
                //    //        "DownstreamApi", colorSchemesToSend,
                //    //         options => {
                //    //             options.RelativePath = "api/colorScheme/LoadColorScheme/";
                //    //             options.HttpMethod = HttpMethod.Post;
                //    //         });
                //    //  count += 10000;

                //}
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ColorSchemeLoadTemplateDto>>() { IsSuccess = false, Message = "Failed to upload color schemes. If problem persists, <NAME_EMAIL>" };
        }
        public async Task<ResponseModel<List<ColorGridModel>>> GetColorGridAsync()
        {
            var responseModel  = new ResponseModel<List<ColorGridModel>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/colorscheme/GetTable");
                var responseString = await response.Content.ReadAsStringAsync();
                responseModel = JsonConvert.DeserializeObject<ResponseModel<List<ColorGridModel>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }
        public async Task<ResponseModel<ColorGridModel>> UpdateGridColorAsync(ColorGridModel schemeToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ColorGridModel, ResponseModel<ColorGridModel>>(
                            "DownstreamApi", schemeToAdd,
                             options => {
                                 options.RelativePath = "api/colorscheme/UpdateGrid/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ColorGridModel>() { IsSuccess = false, Message = "Failed to update" };
        }
        public async Task<ResponseModel<ColorGridModel>> DeleteGridColorAsync(ColorGridModel schemeToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ColorGridModel, ResponseModel<ColorGridModel>>(
                            "DownstreamApi", schemeToAdd,
                             options => {
                                 options.RelativePath = "api/colorscheme/DeleteGrid/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<ColorGridModel>() { IsSuccess = false, Message = "Failed to update" };
        }
        public async Task<ResponseModel<List<ColorGridModel>>> DeleteGridColorsAsync(List<ColorGridModel> schemesToDelete)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<ColorGridModel>, ResponseModel<List<ColorGridModel>>>(
                            "DownstreamApi", schemesToDelete,
                             options => {
                                 options.RelativePath = "api/colorscheme/DeleteGrids/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ColorGridModel>>() { IsSuccess = false, Message = "Failed to update" };
        }
        public async Task<ResponseModel<List<ColorGridModel>>> UpdateGridColorsAsync(List<ColorGridModel> schemesToDelete)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<ColorGridModel>, ResponseModel<List<ColorGridModel>>>(
                            "DownstreamApi", schemesToDelete,
                             options => {
                                 options.RelativePath = "api/colorscheme/UpdateGrids/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ColorGridModel>>() { IsSuccess = false, Message = "Failed to update" };
        }

        public async Task<ResponseModel<List<MaterialColorPredefinedDto>>> ImportPredefinedMaterialColorExcel(FileSelectFileInfo file)
        {
            var ImportedPredefinedMaterialColors = new ResponseModel<List<MaterialColorPredefinedDto>>() { Value = new List<MaterialColorPredefinedDto>() };

            try
            {
                var fileData = new byte[file.Stream.Length];
                await file.Stream.ReadAsync(fileData);

                using (var ms = new MemoryStream(fileData))
                {
                    using (var excelWorkbook = new ClosedXML.Excel.XLWorkbook(ms))
                    {
                        var headerRow = excelWorkbook.Worksheet(1).Row(1);
                        var nonEmptyDataRows = excelWorkbook.Worksheet(1).RowsUsed().Skip(1);
                        foreach (var dataRow in nonEmptyDataRows)
                        {
                            var predefinedMaterialColor = new MaterialColorPredefinedDto
                            {
                                MaterialColorPredefinedId = dataRow.Cell(1).IsEmpty() ? -1 : (int)dataRow.Cell(1).Value,
                                MaterialId = -1, //fetched while saving
                                ColorSchemeId = -1, // fetched while saving
                                Material = new MaterialDto { Material1 = dataRow.Cell(2).IsEmpty() ? null : dataRow.Cell(2).Value.ToString() },
                                ColorScheme = new ColorSchemeDto { ColorScheme1 = dataRow.Cell(3).IsEmpty() ? null : dataRow.Cell(3).Value.ToString() },
                                IsActive = dataRow.Cell(5).IsEmpty() ? true : (bool)dataRow.Cell(5).Value
                            };
                            ImportedPredefinedMaterialColors.Value.Add(predefinedMaterialColor);
                        }
                    }
                    ms.Close();
                }
                return ImportedPredefinedMaterialColors;
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return ImportedPredefinedMaterialColors;
        }

        public async Task<ResponseModel<List<MaterialColorPredefinedDto>>> SavePredefinedMaterialColorsImportAsync(List<MaterialColorPredefinedDto> colorschemesToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<MaterialColorPredefinedDto>, ResponseModel<List<MaterialColorPredefinedDto>>>(
                           "DownstreamApi", colorschemesToAdd,
                            options => {
                                options.RelativePath = "api/colorScheme/LoadPredefinedMaterialColors/";
                            });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<MaterialColorPredefinedDto>>() { IsSuccess = false, Message = "Failed to upload color schemes. If problem persists, <NAME_EMAIL>" };
        }

    }
}
