﻿using System.ComponentModel.DataAnnotations;

namespace ERP.Data.Models.Dto
{
    public class AddPlanToSubdivisionPhaseModel
    {
       // [Required(ErrorMessage = "Plan is required")]
        public int? PlanId { get; set; }

        [Required(ErrorMessage = "Subdivision is required")]
        public int? SubdivisionId { get; set; }

        // [Required(ErrorMessage = "Phase is required")]
        public string? PhaseName { get; set; }
        public List<int>? PlanToAddIds { get; set; }
    }
}
