﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ERP.Data.Models.Validation;
namespace ERP.Data.Models.Dto;

public class SubdivisionContactModel
{


    [StringLength(30, ErrorMessage = "First Name must be 30 characters or less")]
    public string? FirstName { get; set; }

    [StringLength(30, ErrorMessage = "Last Name must be 30 characters or less")]
    public string? LastName { get; set; }

    public string? Address1 { get; set; }

    public string? Address2 { get; set; }

    public string? City { get; set; }

    public string? State { get; set; }

    public string? Postcode { get; set; }
    [CustomPhoneValidation]
    public string? WorkPhone { get; set; }

    [CustomPhoneValidation]
    public string? HomePhone { get; set; }
    [CustomPhoneValidation]
    public string? MobilePhone { get; set; }

    public string? Fax { get; set; }

    [CustomEmailValidation]
    public string? Email { get; set; }

    public string? Country { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }


    public int SubdivisionContactId { get; set; }

    public int? SubdivisionId { get; set; }

    public string? UserId { get; set; }

    public string? SiteContact1 { get; set; }
    public bool IsSiteContact1 { get; set; }
    public string? SiteContact2 { get; set; }
    public bool IsSiteContact2 { get; set; }
    public string? SiteContact3 { get; set; }
    public bool IsSiteContact3 { get; set; }
    public string? SiteContact4 { get; set; }
    public bool IsSiteContact4 { get; set; }
    public string? SiteContact5 { get; set; }
    public bool IsSiteContact5 { get; set; }
    public string? SiteContact6 { get; set; }
    public bool IsSiteContact6 { get; set; }
    public int? VpoApprovalLevel { get; set; }



}
