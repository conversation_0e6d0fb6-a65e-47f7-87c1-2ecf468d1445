﻿using Microsoft.Identity.Web;
using NLog;

namespace ERP.Web.Data
{
    public class SubdivisionJobPickService
    {
        private string? _jobNumber;
        private int? _subdivisionId;
        private int? _supplierNumber;
        public string? JobNumber
        {
            get
            {
                return _jobNumber;
            }
            set
            {
                _jobNumber = value;
                NotifyDataChanged();
            }
        }

        public int? SubdivisionId
        {
            get
            {
                return _subdivisionId;
            }
            set
            {
                _subdivisionId = value;
                NotifyDataChanged();
            }
        }
        public int? SupplierNumber
        {
            get
            {
                return _supplierNumber;
            }
            set
            {
                _supplierNumber = value;
                NotifyDataChanged();
            }
        }
        public Func<Task>? OnChanged {  get; set; }
       // public event Action OnChange;//This doesn't work async
        private async Task NotifyDataChanged() => await OnChanged?.Invoke();
       // private async Task NotifyDataChanged() =>  OnChange?.Invoke();
    }
}
