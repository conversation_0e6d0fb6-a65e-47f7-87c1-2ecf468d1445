﻿@inject BudgetService BudgetService

<div class="card-header">
    <div class="row align-items-center">
        <div class="col">
            <h4 class="card-title">House Costs</h4>
        </div><!--end col-->
    </div>  <!--end row-->
</div><!--end card-header-->
<div class="card-body">
    <div class="table-responsive mt-2">
        <table class="table border-dashed mb-0">
            <thead>
                <tr>
                    <th>Community</th>
                    <th>Plan</th>
                    <th>Building</th>
                    <th class="text-end">Cost</th>
                </tr>
            </thead>
            <tbody>
                @if (Tiles.Any())
                {
                    foreach (var item in Tiles)
                    {
                        <tr>
                            <td>@item.Community</td>
                            <td>@item.HousePlan</td>
                            <td>@item.Building</td>
                            <td class="text-end">@item.TotalHouseCost</td>
                        </tr>
                    }
                }
            </tbody>
        </table><!--end /table-->
    </div><!--end /div-->
</div>

@code {
    public class CommunityTileDto
    {
        public string? Community { get; set; }
        public string? HousePlan { get; set; }
        public string? Building { get; set; }
        public string? TotalHouseCost { get; set; }
    }

    public List<CommunityTileDto>? Tiles { get; set; } = new List<CommunityTileDto>();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        var getHouseCosts = await BudgetService.GetPricingAssumptionCost();

        if (getHouseCosts != null)
        {
            if (getHouseCosts.Value != null)
            {
                if (getHouseCosts.Value.HouseCostTiles != null && getHouseCosts.Value.HouseCostTiles.Any())
                {
                    foreach (var community in getHouseCosts.Value.HouseCostTiles)
                    {
                        Tiles.Add(new CommunityTileDto
                            {
                                Community = community.Community,
                                HousePlan = community.HousePlan,
                                Building = community.Building,
                                TotalHouseCost = community.TotalHouseCost
                            });
                    }
                }
            }
        }
    }
}
