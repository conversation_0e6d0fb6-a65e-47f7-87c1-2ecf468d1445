﻿using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class ColorSchemeElevationTreeModel
{
    public Guid? Id { get; set; }
    public List<ColorSchemeElevationTreeModel>? Children { get; set; }
    public bool HasChilden { get; set; }
    public int ColorShemeId { get; set; }
    public string? ColorSchemeName { get; set; }
    public string? OriginalColorSchemeName { get; set; }//need extra field to hold old name, since else can't track to change as no id
    public List<MaterialColorSchemeDto>? MaterialColors { get; set; }
    public List<MaterialColorPredefinedDto>? MaterialColorsPredefined { get; set; }
    public string? ColorScheme1 { get; set; }
    public MaterialColorSchemeDto? MaterialColorScheme { get; set; }
    public SubdivisionDto? Subdivision { get; set; }
    public PhasePlanDto? PhasePlan { get; set; }
    public AvailablePlanOptionDto? PlanOption { get; set; }
    public List<PhasePlanDto?>? PhasePlans { get; set; }
    public List<AvailablePlanOptionDto>? PlanOptions { get; set; }
    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }


}
