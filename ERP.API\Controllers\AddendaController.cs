﻿using AutoMapper;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NLog;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class AddendaController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        public AddendaController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
        }

        [HttpGet("{subivisionId}/{supplierNum}/{costDate}")]
        public async Task<IActionResult> GetAddendaAsync(int subivisionId, int supplierNum, string costDate) 
        {
            try
            {
                var parseDate = DateTime.Parse(costDate);
                var effectiveDate = parseDate.Date;
                var getOptionItems = (
                               from a in _context.AvailablePlanOptions.Where(x => x.SubdivisionId == subivisionId && x.IsActive == true)
                                   //from b in _context.PhasePlans.Where(x => x.IsActive == true && x.SubdivisionId == subivisionId) //on a.PhasePlanId equals b.PhasePlanId
                                   //join a in planOptions on b.PhasePlanId equals a.PhasePlanId
                               join c in _context.MasterPlans.Where(x => x.IsActive == true) on a.MasterPlanId equals c.MasterPlanId
                               //join d in _context.MasterOptions on a.MasterOptionId equals d.OptionId
                               join e in _context.AsmHeaders.Where(x => x.IsActive == true) on new { optId = a.MasterOptionId ?? 0, planId = c.MasterPlanId } equals new { optId = e.MasterOptionId ?? 0, planId = e.MasterPlanId ?? 0 }
                               
                               join f in _context.AsmDetails.Where(x => x.IsActive == true) on e.AsmHeaderId equals f.AsmHeaderId
                               join g in _context.MasterItems.Where(x => x.IsActive == true) on f.MasterItemId equals g.MasterItemId
                               join j in _context.BomClasses on g.BomClassId equals j.BomClassId
                               join k in _context.Pactivities.Where(x => x.DivId == 1) on j.BomClassId equals k.BomClassId
                               //// join l in _context.PactivityAreaSuppliers.Where(x => x.SubNumber == supplierNum && x.SubdivisionId == subivisionId) on k.PactivityId equals l.PactivityId
                               from h in _context.Costs.Where(x => x.SubNumber == supplierNum && x.MasterItemId == g.MasterItemId && x.SubdivisionId == subivisionId && x.IsActive == true).DefaultIfEmpty()
                               from i in _context.Costs.Where(x => x.SubNumber == supplierNum && x.MasterItemId == g.MasterItemId && x.SubdivisionId == 1 && x.IsActive == true).DefaultIfEmpty()//To get division default if no cost for the subdivisio
                               select new
                               {
                                   Id = Guid.NewGuid(),
                                   MasterItemId = g.MasterItemId,
                                   HouseType = c.PlanNum,
                                   HouseTypeName = $"{c.PlanNum}-{c.PlanName}",
                                   ItemDesc = $"{e.AssemblyCode}-{e.AssemblyDesc}-{g.ItemDesc}",
                                   OptionCode = e.AssemblyCode,
                                   OrderUnit = g.OrderUnit,
                                   Qty = f.Factor,
                                   UnitCost = h != null ? (h.LastCost3Expired != null && h.LastCost3Expired.Value.Date > effectiveDate ? h.LastCost3 : h.LastCost2Expired != null && h.LastCost2Expired.Value.Date > effectiveDate ? h.LastCost2 : h.LastCostExpired != null && h.LastCostExpired.Value.Date > effectiveDate ? h.LastCost1 : h.NextCostDue == null || h.NextCostDue.Value.Date > effectiveDate ? h.UnitCost : h.NextCost2Due == null || h.NextCost2Due.Value.Date > effectiveDate ? h.NextCost : h.NextCost2) : i != null ? (i.LastCost3Expired != null && i.LastCost3Expired.Value.Date > effectiveDate ? i.LastCost3 : i.LastCost2Expired != null && i.LastCost2Expired.Value.Date > effectiveDate ? i.LastCost2 : i.LastCostExpired != null && i.LastCostExpired.Value.Date > effectiveDate ? i.LastCost1 : i.NextCostDue == null || i.NextCostDue.Value.Date > effectiveDate ? i.UnitCost : i.NextCost2Due == null || i.NextCost2Due.Value.Date > effectiveDate ? i.NextCost : i.NextCost2) : null,//TODO: get history if before lastcost3 start date
                                   TotalCost = h != null ? (h.LastCost3Expired != null && h.LastCost3Expired.Value.Date > effectiveDate ? h.LastCost3 : h.LastCost2Expired != null && h.LastCost2Expired.Value.Date > effectiveDate ? h.LastCost2 : h.LastCostExpired != null && h.LastCostExpired.Value.Date > effectiveDate ? h.LastCost1 : (h.NextCostDue == null || h.NextCostDue.Value.Date > effectiveDate) ? h.UnitCost : (h.NextCost2Due == null || h.NextCost2Due.Value.Date > effectiveDate) ? h.NextCost : h.NextCost2) * f.Factor : i != null ? (i.LastCost3Expired != null && i.LastCost3Expired.Value.Date > effectiveDate ? i.LastCost3 : i.LastCost2Expired != null && i.LastCost2Expired.Value.Date > effectiveDate ? i.LastCost2 : i.LastCostExpired != null && i.LastCostExpired.Value.Date > effectiveDate ? i.LastCost1 : (i.NextCostDue == null || i.NextCostDue.Value.Date > effectiveDate) ? i.UnitCost : (i.NextCost2Due == null || i.NextCost2Due.Value.Date > effectiveDate) ? i.NextCost : i.NextCost2) * f.Factor : null,
                                   Activity = j.BomClass1,
                                   DivisionCost = h == null,
                                   NeedHistory = h != null ? (h.LastCost3Expired != null && effectiveDate < h.LastCost3Expired.Value.Date) : (i.LastCost3Expired != null && effectiveDate < i.LastCost3Expired.Value.Date) ? true : false,
                                   NoCost = h == null && i == null
                               }).ToList();


                var getBaseHouseItems = (
                               //from a in _context.AvailablePlanOptions.Where(x => x.SubdivisionId == subivisionId && x.IsActive == true)
                               from b in _context.PhasePlans.Where(x => x.IsActive == true && x.SubdivisionId == subivisionId) 
                               join c in _context.MasterPlans.Where(x => x.IsActive == true) on b.MasterPlanId equals c.MasterPlanId
                               //join d in _context.MasterOptions on a.MasterOptionId equals d.OptionId
                               join e in _context.AsmHeaders.Where(x => x.IsActive == true) on new { optId = 1, planId = c.MasterPlanId } equals new { optId = e.MasterOptionId ?? 0, planId = e.MasterPlanId ?? 0 }
                               join f in _context.AsmDetails.Where(x => x.IsActive == true) on e.AsmHeaderId equals f.AsmHeaderId
                               join g in _context.MasterItems.Where(x => x.IsActive == true) on f.MasterItemId equals g.MasterItemId
                               join j in _context.BomClasses on g.BomClassId equals j.BomClassId
                               join k in _context.Pactivities.Where(x => x.DivId == 1) on j.BomClassId equals k.BomClassId
                               //// join l in _context.PactivityAreaSuppliers.Where(x => x.SubNumber == supplierNum && x.SubdivisionId == subivisionId) on k.PactivityId equals l.PactivityId
                               from h in _context.Costs.Where(x => x.SubNumber == supplierNum && x.MasterItemId == g.MasterItemId && x.SubdivisionId == subivisionId && x.IsActive == true).DefaultIfEmpty()
                               from i in _context.Costs.Where(x => x.SubNumber == supplierNum && x.MasterItemId == g.MasterItemId && x.SubdivisionId == 1 && x.IsActive == true).DefaultIfEmpty()//To get division default if no cost for the subdivisio
                               select new
                               {
                                   Id = Guid.NewGuid(),
                                   MasterItemId = g.MasterItemId,
                                   HouseType = c.PlanNum,
                                   HouseTypeName = $"{c.PlanNum}-{c.PlanName}",
                                   ItemDesc = $"{e.AssemblyCode}-{e.AssemblyDesc}-{g.ItemDesc}",
                                   OptionCode = e.AssemblyCode,
                                   OrderUnit = g.OrderUnit,
                                   Qty = f.Factor,//not sure
                                   UnitCost = h != null ? (h.LastCost3Expired != null && h.LastCost3Expired.Value.Date > effectiveDate ? h.LastCost3 : h.LastCost2Expired != null && h.LastCost2Expired.Value.Date > effectiveDate ? h.LastCost2 : h.LastCostExpired != null && h.LastCostExpired.Value.Date > effectiveDate ? h.LastCost1 : h.NextCostDue == null || h.NextCostDue.Value.Date > effectiveDate ? h.UnitCost : h.NextCost2Due == null || h.NextCost2Due.Value.Date > effectiveDate ? h.NextCost : h.NextCost2) : i != null ? (i.LastCost3Expired != null && i.LastCost3Expired.Value.Date > effectiveDate ? i.LastCost3 : i.LastCost2Expired != null && i.LastCost2Expired.Value.Date > effectiveDate ? i.LastCost2 : i.LastCostExpired != null && i.LastCostExpired.Value.Date > effectiveDate ? i.LastCost1 : i.NextCostDue == null || i.NextCostDue.Value.Date > effectiveDate ? i.UnitCost : i.NextCost2Due == null || i.NextCost2Due.Value.Date > effectiveDate ? i.NextCost : i.NextCost2) : null,//TODO: get history if before lastcost3 start date
                                   TotalCost = h != null ? (h.LastCost3Expired != null && h.LastCost3Expired.Value.Date > effectiveDate ? h.LastCost3 : h.LastCost2Expired != null && h.LastCost2Expired.Value.Date > effectiveDate ? h.LastCost2 : h.LastCostExpired != null && h.LastCostExpired.Value.Date > effectiveDate ? h.LastCost1 : (h.NextCostDue == null || h.NextCostDue.Value.Date > effectiveDate) ? h.UnitCost : (h.NextCost2Due == null || h.NextCost2Due.Value.Date > effectiveDate) ? h.NextCost : h.NextCost2) * f.Factor : i != null ? (i.LastCost3Expired != null && i.LastCost3Expired.Value.Date > effectiveDate ? i.LastCost3 : i.LastCost2Expired != null && i.LastCost2Expired.Value.Date > effectiveDate ? i.LastCost2 : i.LastCostExpired != null && i.LastCostExpired.Value.Date > effectiveDate ? i.LastCost1 : (i.NextCostDue == null || i.NextCostDue.Value.Date > effectiveDate) ? i.UnitCost : (i.NextCost2Due == null || i.NextCost2Due.Value.Date > effectiveDate) ? i.NextCost : i.NextCost2) * f.Factor : null,
                                   Activity = j.BomClass1,
                                   DivisionCost = h == null,
                                   NeedHistory = h != null ? (h.LastCost3Expired != null && effectiveDate < h.LastCost3Expired.Value.Date) : (i.LastCost3Expired != null && effectiveDate < i.LastCost3Expired.Value.Date) ? true : false,
                                   NoCost = h == null && i == null
                               }).ToList();

                getOptionItems.AddRange(getBaseHouseItems);

                getOptionItems.RemoveAll(x => x.NoCost);

                // it might be that effective date is before lastcost3 expired, but after the expired date of the first cost in the history, in which case need to use last cost, or it might be before the lastcost3 expired, but there are no costs in history -- need to fix this 
                var getHistoryDivisionCost = (from a in getOptionItems.Where(x => x.NeedHistory && x.DivisionCost)
                                              join b in _context.CostsHistories.Where(x => x.SubNumber == supplierNum && x.SubdivisionId == 1 && x.CostExpired > effectiveDate) on a.MasterItemId equals b.MasterItemsId into costhistorygroup
                                              from costhistory in costhistorygroup
                                              select new
                                              {
                                                  id = a.Id,
                                                  masterItemId = a.MasterItemId,
                                                  cost = costhistorygroup.MaxBy(x => x.CostExpired)?.CostExpired < effectiveDate ? costhistorygroup.MaxBy(x => x.CostExpired)?.OldCost : a.UnitCost,
                                              }).Distinct().ToList();
                var getHistorySubdivcost = (from a in getOptionItems.Where(x => x.NeedHistory && !x.DivisionCost)
                                            join b in _context.CostsHistories.Where(x => x.SubNumber == supplierNum && x.SubdivisionId == subivisionId && x.CostExpired != null && x.CostExpired.Value.Date > effectiveDate) on a.MasterItemId equals b.MasterItemsId into costhistorygroup
                                            from costhistory in costhistorygroup
                                            select new
                                            {
                                                id = a.Id,
                                                masterItemId = a.MasterItemId,
                                                cost = costhistorygroup.MaxBy(x => x.CostExpired)?.CostExpired < effectiveDate ? costhistorygroup.MaxBy(x => x.CostExpired)?.OldCost : a.UnitCost,
                                            }).Distinct().ToList();

                var allItemsWithCost = getOptionItems.Select(x => new AddendaModel()
                {
                    MasterItemId = x.MasterItemId,
                    HouseType = x.HouseType,
                    HouseTypeName = x.HouseTypeName,
                    ItemDesc = x.ItemDesc,
                    OptionCode = x.OptionCode,
                    OrderUnit = x.OrderUnit,
                    Qty = x.Qty,
                    UnitCost = x.NeedHistory && x.DivisionCost ? getHistoryDivisionCost.FirstOrDefault(y => y.id == x.Id)?.cost != null ? getHistoryDivisionCost.FirstOrDefault(y => y.id == x.Id)?.cost : x.UnitCost : x.NeedHistory && !x.DivisionCost ? getHistorySubdivcost.FirstOrDefault(y => y.id == x.Id)?.cost != null ? getHistorySubdivcost.FirstOrDefault(y => y.id == x.Id)?.cost : x.UnitCost : x.UnitCost,
                    TotalCost = x.NeedHistory && x.DivisionCost ? getHistoryDivisionCost.FirstOrDefault(y => y.id == x.Id)?.cost != null ? getHistoryDivisionCost.FirstOrDefault(y => y.id == x.Id)?.cost * x.Qty : x.UnitCost * x.Qty : x.NeedHistory && !x.DivisionCost ? getHistorySubdivcost.FirstOrDefault(y => y.id == x.Id)?.cost != null ? getHistorySubdivcost.FirstOrDefault(y => y.id == x.Id)?.cost * x.Qty : x.UnitCost * x.Qty : x.UnitCost * x.Qty,
                    Activity = x.Activity
                }).ToList();


                return Ok(new ResponseModel<List<AddendaModel>>() { IsSuccess = true, Value = allItemsWithCost });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<AddendaModel>> { IsSuccess = false, Message = $"Failed to get addenda data. Error: {ex.Message} {ex.InnerException?.Message}", Value = null });
            }
            
        }
        [HttpPost]
        public async Task<IActionResult> AddendaAsync([FromBody] SelectAddendaModel model )
        {
            try
            {
                
                model.CostDate = model.CostDate ?? DateTime.Now;
                var effectiveDate = model.CostDate.Value.Date;
                var getOptionItems = (
                               from a in _context.AvailablePlanOptions.Where(x => x.SubdivisionId == model.SubdivsionId && x.IsActive == true && model.SelectedPhasePlanIds.Contains(x.PhasePlanId))//only selected phase plans and selected subdivision
                                   //from b in _context.PhasePlans.Where(x => x.IsActive == true && x.SubdivisionId == subivisionId) //on a.PhasePlanId equals b.PhasePlanId
                                   //join a in planOptions on b.PhasePlanId equals a.PhasePlanId
                               join c in _context.MasterPlans.Where(x => x.IsActive == true) on a.MasterPlanId equals c.MasterPlanId
                               //join d in _context.MasterOptions on a.MasterOptionId equals d.OptionId
                               join e in _context.AsmHeaders.Where(x => x.IsActive == true) on new { optId = a.MasterOptionId ?? 0, planId = c.MasterPlanId } equals new { optId = e.MasterOptionId ?? 0, planId = e.MasterPlanId ?? 0 }

                               join f in _context.AsmDetails.Where(x => x.IsActive == true) on e.AsmHeaderId equals f.AsmHeaderId
                               join g in _context.MasterItems.Where(x => x.IsActive == true) on f.MasterItemId equals g.MasterItemId
                               join j in _context.BomClasses on g.BomClassId equals j.BomClassId
                               join k in _context.Pactivities.Where(x => x.DivId == 1) on j.BomClassId equals k.BomClassId
                               //// join l in _context.PactivityAreaSuppliers.Where(x => x.SubNumber == supplierNum && x.SubdivisionId == subivisionId) on k.PactivityId equals l.PactivityId
                               from h in _context.Costs.Where(x => x.SubNumber == model.SupplierId && x.MasterItemId == g.MasterItemId && x.SubdivisionId == model.SubdivsionId && x.IsActive == true).DefaultIfEmpty()
                               from i in _context.Costs.Where(x => x.SubNumber == model.SupplierId && x.MasterItemId == g.MasterItemId && x.SubdivisionId == 1 && x.IsActive == true).DefaultIfEmpty()//To get division default if no cost for the subdivisio
                               select new
                               {
                                   Id = Guid.NewGuid(),
                                   MasterItemId = g.MasterItemId,
                                   HouseType = c.PlanNum,
                                   HouseTypeName = $"{c.PlanNum}-{c.PlanName}",
                                   ItemDesc = $"{e.AssemblyCode}-{e.AssemblyDesc}-{g.ItemDesc}",
                                   OptionCode = e.AssemblyCode,
                                   OrderUnit = g.OrderUnit,
                                   Qty = f.Factor,
                                   UnitCost = h != null ? (h.LastCost3Expired != null && h.LastCost3Expired.Value.Date > effectiveDate ? h.LastCost3 : h.LastCost2Expired != null && h.LastCost2Expired.Value.Date > effectiveDate ? h.LastCost2 : h.LastCostExpired != null && h.LastCostExpired.Value.Date > effectiveDate ? h.LastCost1 : h.NextCostDue == null || h.NextCostDue.Value.Date > effectiveDate ? h.UnitCost : h.NextCost2Due == null || h.NextCost2Due.Value.Date > effectiveDate ? h.NextCost : h.NextCost2) : i != null ? (i.LastCost3Expired != null && i.LastCost3Expired.Value.Date > effectiveDate ? i.LastCost3 : i.LastCost2Expired != null && i.LastCost2Expired.Value.Date > effectiveDate ? i.LastCost2 : i.LastCostExpired != null && i.LastCostExpired.Value.Date > effectiveDate ? i.LastCost1 : i.NextCostDue == null || i.NextCostDue.Value.Date > effectiveDate ? i.UnitCost : i.NextCost2Due == null || i.NextCost2Due.Value.Date > effectiveDate ? i.NextCost : i.NextCost2) : null,//TODO: get history if before lastcost3 start date
                                   TotalCost = h != null ? (h.LastCost3Expired != null && h.LastCost3Expired.Value.Date > effectiveDate ? h.LastCost3 : h.LastCost2Expired != null && h.LastCost2Expired.Value.Date > effectiveDate ? h.LastCost2 : h.LastCostExpired != null && h.LastCostExpired.Value.Date > effectiveDate ? h.LastCost1 : (h.NextCostDue == null || h.NextCostDue.Value.Date > effectiveDate) ? h.UnitCost : (h.NextCost2Due == null || h.NextCost2Due.Value.Date > effectiveDate) ? h.NextCost : h.NextCost2) * f.Factor : i != null ? (i.LastCost3Expired != null && i.LastCost3Expired.Value.Date > effectiveDate ? i.LastCost3 : i.LastCost2Expired != null && i.LastCost2Expired.Value.Date > effectiveDate ? i.LastCost2 : i.LastCostExpired != null && i.LastCostExpired.Value.Date > effectiveDate ? i.LastCost1 : (i.NextCostDue == null || i.NextCostDue.Value.Date > effectiveDate) ? i.UnitCost : (i.NextCost2Due == null || i.NextCost2Due.Value.Date > effectiveDate) ? i.NextCost : i.NextCost2) * f.Factor : null,
                                   Activity = j.BomClass1,
                                   DivisionCost = h == null,
                                   NeedHistory = h != null ? (h.LastCost3Expired != null && effectiveDate < h.LastCost3Expired.Value.Date) : (i.LastCost3Expired != null && effectiveDate < i.LastCost3Expired.Value.Date) ? true : false,
                                   NoCost = h == null && i == null
                               }).ToList();


                var getBaseHouseItems = (
                               //from a in _context.AvailablePlanOptions.Where(x => x.SubdivisionId == subivisionId && x.IsActive == true)
                               from b in _context.PhasePlans.Where(x => x.IsActive == true && x.SubdivisionId == model.SubdivsionId && model.SelectedPhasePlanIds.Contains(x.PhasePlanId))
                               join c in _context.MasterPlans.Where(x => x.IsActive == true) on b.MasterPlanId equals c.MasterPlanId
                               //join d in _context.MasterOptions on a.MasterOptionId equals d.OptionId
                               join e in _context.AsmHeaders.Where(x => x.IsActive == true) on new { optId = 1, planId = c.MasterPlanId } equals new { optId = e.MasterOptionId ?? 0, planId = e.MasterPlanId ?? 0 }
                               join f in _context.AsmDetails.Where(x => x.IsActive == true) on e.AsmHeaderId equals f.AsmHeaderId
                               join g in _context.MasterItems.Where(x => x.IsActive == true) on f.MasterItemId equals g.MasterItemId
                               join j in _context.BomClasses on g.BomClassId equals j.BomClassId
                               join k in _context.Pactivities.Where(x => x.DivId == 1) on j.BomClassId equals k.BomClassId
                               //// join l in _context.PactivityAreaSuppliers.Where(x => x.SubNumber == supplierNum && x.SubdivisionId == subivisionId) on k.PactivityId equals l.PactivityId
                               from h in _context.Costs.Where(x => x.SubNumber == model.SupplierId && x.MasterItemId == g.MasterItemId && x.SubdivisionId == model.SubdivsionId && x.IsActive == true).DefaultIfEmpty()
                               from i in _context.Costs.Where(x => x.SubNumber == model.SupplierId && x.MasterItemId == g.MasterItemId && x.SubdivisionId == 1 && x.IsActive == true).DefaultIfEmpty()//To get division default if no cost for the subdivisio
                               select new
                               {
                                   Id = Guid.NewGuid(),
                                   MasterItemId = g.MasterItemId,
                                   HouseType = c.PlanNum,
                                   HouseTypeName = $"{c.PlanNum}-{c.PlanName}",
                                   ItemDesc = $"{e.AssemblyCode}-{e.AssemblyDesc}-{g.ItemDesc}",
                                   OptionCode = e.AssemblyCode,
                                   OrderUnit = g.OrderUnit,
                                   Qty = f.Factor,//not sure
                                   UnitCost = h != null ? (h.LastCost3Expired != null && h.LastCost3Expired.Value.Date > effectiveDate ? h.LastCost3 : h.LastCost2Expired != null && h.LastCost2Expired.Value.Date > effectiveDate ? h.LastCost2 : h.LastCostExpired != null && h.LastCostExpired.Value.Date > effectiveDate ? h.LastCost1 : h.NextCostDue == null || h.NextCostDue.Value.Date > effectiveDate ? h.UnitCost : h.NextCost2Due == null || h.NextCost2Due.Value.Date > effectiveDate ? h.NextCost : h.NextCost2) : i != null ? (i.LastCost3Expired != null && i.LastCost3Expired.Value.Date > effectiveDate ? i.LastCost3 : i.LastCost2Expired != null && i.LastCost2Expired.Value.Date > effectiveDate ? i.LastCost2 : i.LastCostExpired != null && i.LastCostExpired.Value.Date > effectiveDate ? i.LastCost1 : i.NextCostDue == null || i.NextCostDue.Value.Date > effectiveDate ? i.UnitCost : i.NextCost2Due == null || i.NextCost2Due.Value.Date > effectiveDate ? i.NextCost : i.NextCost2) : null,//TODO: get history if before lastcost3 start date
                                   TotalCost = h != null ? (h.LastCost3Expired != null && h.LastCost3Expired.Value.Date > effectiveDate ? h.LastCost3 : h.LastCost2Expired != null && h.LastCost2Expired.Value.Date > effectiveDate ? h.LastCost2 : h.LastCostExpired != null && h.LastCostExpired.Value.Date > effectiveDate ? h.LastCost1 : (h.NextCostDue == null || h.NextCostDue.Value.Date > effectiveDate) ? h.UnitCost : (h.NextCost2Due == null || h.NextCost2Due.Value.Date > effectiveDate) ? h.NextCost : h.NextCost2) * f.Factor : i != null ? (i.LastCost3Expired != null && i.LastCost3Expired.Value.Date > effectiveDate ? i.LastCost3 : i.LastCost2Expired != null && i.LastCost2Expired.Value.Date > effectiveDate ? i.LastCost2 : i.LastCostExpired != null && i.LastCostExpired.Value.Date > effectiveDate ? i.LastCost1 : (i.NextCostDue == null || i.NextCostDue.Value.Date > effectiveDate) ? i.UnitCost : (i.NextCost2Due == null || i.NextCost2Due.Value.Date > effectiveDate) ? i.NextCost : i.NextCost2) * f.Factor : null,
                                   Activity = j.BomClass1,
                                   DivisionCost = h == null,
                                   NeedHistory = h != null ? (h.LastCost3Expired != null && effectiveDate < h.LastCost3Expired.Value.Date) : (i.LastCost3Expired != null && effectiveDate < i.LastCost3Expired.Value.Date) ? true : false,
                                   NoCost = h == null && i == null
                               }).ToList();

                getOptionItems.AddRange(getBaseHouseItems);

                getOptionItems.RemoveAll(x => x.NoCost);

                // it might be that effective date is before lastcost3 expired, but after the expired date of the first cost in the history, in which case need to use last cost, or it might be before the lastcost3 expired, but there are no costs in history -- need to fix this 
                var getHistoryDivisionCost = (from a in getOptionItems.Where(x => x.NeedHistory && x.DivisionCost)
                                              join b in _context.CostsHistories.Where(x => x.SubNumber == model.SupplierId && x.SubdivisionId == 1 && x.CostExpired > effectiveDate) on a.MasterItemId equals b.MasterItemsId into costhistorygroup
                                              from costhistory in costhistorygroup
                                              select new
                                              {
                                                  id = a.Id,
                                                  masterItemId = a.MasterItemId,
                                                  cost = costhistorygroup.MaxBy(x => x.CostExpired)?.CostExpired < effectiveDate ? costhistorygroup.MaxBy(x => x.CostExpired)?.OldCost : a.UnitCost,
                                              }).Distinct().ToList();
                var getHistorySubdivcost = (from a in getOptionItems.Where(x => x.NeedHistory && !x.DivisionCost)
                                            join b in _context.CostsHistories.Where(x => x.SubNumber == model.SupplierId && x.SubdivisionId == model.SubdivsionId && x.CostExpired != null && x.CostExpired.Value.Date > effectiveDate) on a.MasterItemId equals b.MasterItemsId into costhistorygroup
                                            from costhistory in costhistorygroup
                                            select new
                                            {
                                                id = a.Id,
                                                masterItemId = a.MasterItemId,
                                                cost = costhistorygroup.MaxBy(x => x.CostExpired)?.CostExpired < effectiveDate ? costhistorygroup.MaxBy(x => x.CostExpired)?.OldCost : a.UnitCost,
                                            }).Distinct().ToList();

                var allItemsWithCost = getOptionItems.Select(x => new AddendaModel()
                {
                    MasterItemId = x.MasterItemId,
                    HouseType = x.HouseType,
                    HouseTypeName = x.HouseTypeName,
                    ItemDesc = x.ItemDesc,
                    OptionCode = x.OptionCode,
                    OrderUnit = x.OrderUnit,
                    Qty = x.Qty,
                    UnitCost = x.NeedHistory && x.DivisionCost ? getHistoryDivisionCost.FirstOrDefault(y => y.id == x.Id)?.cost != null ? getHistoryDivisionCost.FirstOrDefault(y => y.id == x.Id)?.cost : x.UnitCost : x.NeedHistory && !x.DivisionCost ? getHistorySubdivcost.FirstOrDefault(y => y.id == x.Id)?.cost != null ? getHistorySubdivcost.FirstOrDefault(y => y.id == x.Id)?.cost : x.UnitCost : x.UnitCost,
                    TotalCost = x.NeedHistory && x.DivisionCost ? getHistoryDivisionCost.FirstOrDefault(y => y.id == x.Id)?.cost != null ? getHistoryDivisionCost.FirstOrDefault(y => y.id == x.Id)?.cost * x.Qty : x.UnitCost * x.Qty : x.NeedHistory && !x.DivisionCost ? getHistorySubdivcost.FirstOrDefault(y => y.id == x.Id)?.cost != null ? getHistorySubdivcost.FirstOrDefault(y => y.id == x.Id)?.cost * x.Qty : x.UnitCost * x.Qty : x.UnitCost * x.Qty,
                    Activity = x.Activity
                }).ToList();


                return Ok(new ResponseModel<List<AddendaModel>>() { IsSuccess = true, Value = allItemsWithCost });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<AddendaModel>> { IsSuccess = false, Message = $"Failed to get addenda data. Error: {ex.Message} {ex.InnerException?.Message}", Value = null });
            }

        }
    }
}
