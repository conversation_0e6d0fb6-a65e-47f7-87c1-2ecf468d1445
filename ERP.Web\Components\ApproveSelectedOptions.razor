﻿@inject SalesConfigService SalesConfigService
@inject SelectedOptionsService SelectedOptionsService
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject SubdivisionService SubdivisionService
@inject IJSRuntime JsRuntime
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling")]
@using System.Collections.ObjectModel
@using System.ComponentModel
@using Telerik.DataSource.Extensions
@using ERP.Data.Models
@using Telerik.Blazor.Components.Grid
@using Telerik.Documents.SpreadsheetStreaming
<style>
    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }

    .mostRecent .k-grid-checkbox {
        color: mediumpurple;
    }

    .structuralOption {
        background-color: lightblue !important;
    }

    .nonStructuralOption {
        background-color: lightyellow !important;
    }

    .builderApproved{
        background-color: #d7f5e3 !important;
    }

    .mostRecent {
        color: mediumpurple;
    }

    .notRecent .k-grid-checkbox {
        display: none;
    }

    .noApproveCheckbox .k-grid-checkbox {
        display: none;
    }

    .highlightCellBackGround {
        background-color: yellow;
    }

    /*the following selectors target the locked/frozen columns*/
    /*===*/
    .k-grid .k-master-row.myCustomRowFormatting .k-grid-content-sticky,
    .k-grid .k-master-row.myCustomRowFormatting.k-alt .k-grid-content-sticky
    /*===*/ {
        background-color: inherit;
    }

    .k-grid .k-master-row.myCustomRowFormatting:hover {
        background-color: green !important;
    }

    .k-grid .k-master-row.myCustomRowFormatting {
        background-color: yellow;
    }

</style>

<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<TelerikTooltip TargetSelector=".tooltip-target" />

<div class="container-fluid">

        @if (SelectedOptionsData == null && !loading)
    {
        <p>Select a job to see options</p>
    }
    else if (loading)
    {
        <p><em>Loading...</em></p>
        <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />

    }
    else
    {
        <TelerikTabStrip ActiveTabIndex="@ActiveTabIndex" ActiveTabIndexChanged="@TabChangedHandler">
            <TabStripTab Title="Selected Options">
                <TelerikGrid Data="@SelectedOptionsData"
                SelectionMode="@GridSelectionMode.Multiple"
                SelectedItems="@SelectedItems"
                Groupable="true"
                PageSize="50"
                Resizable="true"
                Width="100%"
                RowHeight=40
                OnRowClick="@OnRowClickHandler"
                OnRowRender="@OnRowRenderHandler"
                Height="600px"
                Sortable="true"
                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                EditMode="@GridEditMode.Inline"
                @ref="@SelectedOptionsGrid">
                    <GridColumns>
                        <GridColumn Field="JobNumber" Editable="false" Visible="false"></GridColumn>
                        <GridColumn Field="BuilderApproved" Title="" Visible="true" Editable="true" Width="40px" Filterable="false" Sortable="false">
                            <Template>
                                @{
                                    var SelectedRow = context as TbBuiltOptionDto;
                                    if (SelectedRow.NeedsApproval == true)
                                    {
                                        <TelerikCheckBox Enabled=@true @bind-Value="@SelectedRow.BoolBuilderApproved"></TelerikCheckBox>

                                    }
                                    else
                                    {
                                        //show disabled checkbox if it was approved
                                        if (SelectedRow.BuilderApproved == 1)
                                        {
                                            <TelerikCheckBox Enabled=@false @bind-Value="@SelectedRow.BoolBuilderApproved"></TelerikCheckBox>
                                        }
                                    }
                                }
                            </Template>
                            <HeaderTemplate>
                                @{
                                    <TelerikCheckBox @bind-Value="@SelectAllCheckBoxValue"
                                    Indeterminate="@(SelectAllCheckBoxValue == null)"
                                    OnChange="@ToggleSelectAll" />

                                }
                            </HeaderTemplate>
                        </GridColumn>

                        <GridColumn Field="OptionCode" Title="Option Code" Editable="false" Width="100px" FilterMenuType="@FilterMenuType.CheckBoxList"></GridColumn>
                        <GridColumn Field="OptionDesc" Title="Option Desc" Editable="false" Width="200px"></GridColumn>
                        <GridColumn Field="Id" Title="Change" Editable="false" Width="200px">
                            <Template>
                                @{
                                    var item = context as TbBuiltOptionDto;
                                    if (item.Removed || item.Qty == 0)
                                    {
                                        <span style="color:red"><strong>Remove</strong></span>
                                    }
                                    else if (item.QtyDelta != 0 && (item.QtyDelta == item.Qty || item.QtyDelta == null))
                                    {
                                        if (item.BuildAttributeItems != null && item.BuildAttributeItems.Any())
                                        {
                                            <TelerikButton OnClick="()=> ShowAttributes(item)" Title="show attributes" Class="k-button-success">Selections</TelerikButton>
                                            <span style="color:blue"> - </span>
                                        }
                                        <span style="color:blue"><strong>Add</strong></span>
                                    }
                                    else if (item.QtyDelta != 0 && item.QtyDelta != item.Qty)
                                    {
                                        if (item.BuildAttributeItems != null && item.BuildAttributeItems.Any())
                                        {
                                            <TelerikButton OnClick="()=>ShowAttributes(item)" Title="show attributes" Class="k-button-success">Selections</TelerikButton>
                                            <span style="color:purple"> - </span>
                                        }
                                        <span style="color:purple"><strong>Change Qty</strong></span>
                                    }
                                    else if (item.QtyDelta == 0 && !item.Removed && item.BuildAttributeItems != null && item.BuildAttributeItems.Any())
                                    {
                                        if (item.BuildAttributeItems != null && item.BuildAttributeItems.Any())
                                        {
                                            <TelerikButton OnClick="()=>ShowAttributes(item)" Title="show attributes" Class="k-button-success">Selections</TelerikButton>
                                            <span style="color:green"> - </span>
                                        }
                                        <span style="color:green"><strong>Contract Option From Spec/Selections/Other Change</strong></span>
                                    }
                                    else
                                    {
                                        <span style="color:green"><strong>Contract Option From Spec/Selections/Other Change</strong></span>
                                    }
                                }
                            </Template>
                        </GridColumn>
                        @*                         <GridCommandColumn>
                    <GridCommandButton OnClick="ShowAttributes"  Title="show attributes">Attribute</GridCommandButton>
                    </GridCommandColumn> *@
                        <GridColumn Field="Removed" Title="Removed" Editable="false" Visible="false"></GridColumn>
                        <GridColumn Field="MostRecent" Title="MostRecent" Editable="false" Visible="false"></GridColumn>
                        <GridColumn Field="Exported" Title="Approved" Editable="false" Visible="false"></GridColumn>
                        @* <GridColumn Field="BuilderApproved" Title="BuilderApproved" Editable="false" Visible="false"></GridColumn> *@
                        <GridColumn Field="Qty" Title="Quantity" Editable="false" Width="60px"></GridColumn>
                        <GridColumn Field="CreatedDateTime" Title="Date" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Width="100px"></GridColumn>
                        <GridColumn Field="QtyDelta" Title="Quantity Delta" Editable="false" Visible="false"></GridColumn>
                        <GridColumn Field="Price" Title="Price" Editable="false" Visible="false"></GridColumn>
                        <GridColumn Field="PriceDelta" Title="Price Delta" Editable="false" Visible="false"></GridColumn>
                        <GridColumn Field="OptionNotes" Title="Option Notes" Editable="false" Visible="false"></GridColumn>
                        <GridColumn Field="OptionSelections" Title="Option Selections" Editable="false" Visible="false"></GridColumn>
                        <GridColumn Field="UnitCost" Title="Unit Cost" Editable="false" DisplayFormat="{0:C2}" Visible="false"></GridColumn>
                        <GridColumn Field="ListPrice" Title="Unit List Price" DisplayFormat="{0:C2}" Editable="false" Visible="false"></GridColumn>
                        <GridColumn Field="OptionType.OptionType1" Title="Type" Editable="false" Width="70px"></GridColumn>
                        @*  <GridColumn Field="BoolFromSpec" Title="Spec Option" Visible="true" Editable="false" Width="50px"></GridColumn> *@
                        @* <GridColumn Field="CancelledOption" Title="Cancelled Option" Visible="true" Editable="false" Width="50px"></GridColumn> *@
                    </GridColumns>
                    <GridToolBarTemplate>
                        <GridSearchBox DebounceDelay="200">
                        </GridSearchBox>
                        <GridCommandButton Command="ApproveSelected" Title="Approve Selected" Icon="@FontIcon.Check" OnClick="ApproveSelected" Class="k-button-success">Approve Selected</GridCommandButton>
                        <TelerikToggleButton @bind-Selected="@ToggleShowAll" OnClick="@ToggleNeedApprovalOnly" Class="k-button-add">All/Need Approval Only: <strong>@ShowHideNeedApprove</strong></TelerikToggleButton>
                        <TelerikButton Title="Approved Options PDF"  OnClick="DownloadApprovedOptionsPdf">Approved Options PDF</TelerikButton>
                        <TelerikButton Title="Selected Options PDF" OnClick="DownloadSelectedOptionsPdf">Selected Options PDF</TelerikButton>
                        <TelerikButton Title="Available/Selected Standard Options PDF" OnClick="DownloadAllOptionsPdf">Available Standard Options PDF</TelerikButton>
                        <GridCommandButton Command="ExcelExport" Icon="@FontIcon.FileExcel" Class="k-button-add">Export to Excel</GridCommandButton>
                        <span style="vertical-align: middle;">
                            <div style="display: inline-block; margin-right: 5px; background-color: #d7f5e3; width: 20px; height: 20px; border-radius: 50%; border: 1px solid black;"></div> - Approved
                            <div style="display: inline-block; margin-right: 5px; margin-left: 10px;background-color: lightblue; width: 20px; height: 20px; border-radius: 50%; border: 1px solid black;"></div> - Structural
                            <div style="display: inline-block; margin-right: 5px; margin-left: 10px;background-color: lightyellow; width: 20px; height: 20px; border-radius: 50%; border: 1px solid black;"></div> - Non Structural
                        </span>                      
                    </GridToolBarTemplate>
                    <GridExport>
                        <GridExcelExport FileName="SelectedOptions" OnBeforeExport="@OnExcelBeforeExport" AllPages="true" />
                    </GridExport>
                </TelerikGrid>
            </TabStripTab>
            <TabStripTab Title="All Options">
                <TelerikGrid Data="@AllOptionsData"
                Groupable="true"
                PageSize="50"
                Resizable="true"
                Width="100%"
                RowHeight=40
                Height="600px"
                Sortable="true"
                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                OnRowRender="@AllOptionsOnRowRenderHandler"
                @ref="@AllOptionsGrid">
                    <GridColumns>
                        <GridColumn Field="OptionCode" Title="Option Code" Editable="false" Width="100px"></GridColumn>
                        <GridColumn Field="ModifiedOptionDesc" Title="Option Description" Editable="false" Width="200px"></GridColumn>
                        <GridColumn Field="OptionTypeNavigation.OptionType1" Title="Option Type" Editable="false" Width="200px"></GridColumn>
                    </GridColumns>

                </TelerikGrid>
            </TabStripTab>
        </TelerikTabStrip>

    }
</div>
<ShowAttributes SelectedOption="@ItemToEdit" @ref="ShowAttributesModal"></ShowAttributes>
@code {
    [Parameter]
    public string JobNumber { get; set; }
    public ObservableCollection<TbBuiltOptionDto>? SelectedOptionsData { get; set; }
    public List<AvailablePlanOptionDto>? AllOptionsData { get; set; }
    public ObservableCollection<TbBuiltOptionDto>? AllSelectedOptionsData { get; set; }
    private TelerikGrid<TbBuiltOptionDto>? SelectedOptionsGrid { get; set; }
    private TelerikGrid<AvailablePlanOptionDto>? AllOptionsGrid { get; set; }
    public ShowAttributes? ShowAttributesModal { get; set; }
    public TbBuiltOptionDto? ItemToEdit { get; set; }
    public TbBuiltOptionDto? ItemDetails { get; set; } = new TbBuiltOptionDto();
    public IEnumerable<TbBuiltOptionDto>? SelectedItems { get; set; } = Enumerable.Empty<TbBuiltOptionDto>();
    public List<BuyerDto>? BuyerData { get; set; }
    public CustomerDto? ERPCustomerData { get; set; } = new CustomerDto();
    public bool WindowIsVisible { get; set; } = true;
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    public int ActiveTabIndex { get; set; }
    private bool loading { get; set; } = false;
    private bool ShowAll { get; set; } = false;
    private bool ToggleShowAll { get; set; } = true;
    private string? ShowHideNeedApprove { get; set; } = "Need Approval";
    private bool ShowChangedOnly { get; set; } = false;
    private string? ShowHideChangedOnly { get; set; } = "Show Changed Only";
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        if (JobNumber != null)
        {
            loading = true;

            //var getSelectedOptionsTask = SelectedOptionsService.GetAllSelectedOptionsByJobAsync(JobNumber);
            var getSelectedOptionsTask = SelectedOptionsService.GetSelectedOptionsByJobAsync(JobNumber);
            var getBuyerTask = SelectedOptionsService.GetBuyersByJobAsync(JobNumber);
            var getExistingERPCustomerTask = SelectedOptionsService.GetJobCustomerAsync(JobNumber);
            var getAllOptionsTask = SelectedOptionsService.GetAllOptionsByJobAsync(JobNumber);
            await Task.WhenAll(new Task[] { getBuyerTask, getSelectedOptionsTask, getExistingERPCustomerTask, getAllOptionsTask });
            if (!getSelectedOptionsTask.Result.IsSuccess || !getBuyerTask.Result.IsSuccess || !getExistingERPCustomerTask.Result.IsSuccess)
            {
                ShowSuccessOrErrorNotification(getSelectedOptionsTask.Result.Message, getSelectedOptionsTask.Result.IsSuccess);
                ShowSuccessOrErrorNotification(getExistingERPCustomerTask.Result.Message, getExistingERPCustomerTask.Result.IsSuccess);
                ShowSuccessOrErrorNotification(getBuyerTask.Result.Message, getBuyerTask.Result.IsSuccess);
                ShowSuccessOrErrorNotification(getAllOptionsTask.Result.Message, getAllOptionsTask.Result.IsSuccess);
            }

            AllOptionsData = getAllOptionsTask.Result.Value;
            var getSelectedOptionsData = getSelectedOptionsTask.Result.Value;
            SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(getSelectedOptionsData);
            AllSelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(getSelectedOptionsData);

            if (!ShowAll)
            {
                SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData.Where(x => x.NeedsApproval == true));
            }
            else
            {
                SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData);
            }
            BuyerData = getBuyerTask.Result.Value;
            ERPCustomerData = getExistingERPCustomerTask.Result.Value;
            loading = false;
            StateHasChanged();
        }
    }

    void AllOptionsOnRowRenderHandler(GridRowRenderEventArgs args)
    {
        var option = args.Item as AvailablePlanOptionDto;
        if (AllSelectedOptionsData != null)
        {
            if (AllSelectedOptionsData.Where(x => x.MostRecent == true && x.Removed == false).Any(x => x.OptionCode == option.OptionCode))
            {
                args.Class = "myCustomRowFormatting";
            }
        }
    }

    void OnRowRenderHandler(GridRowRenderEventArgs args)
    {
        TbBuiltOptionDto item = args.Item as TbBuiltOptionDto;
        if (item != null && item.OptionCode != null && item.NeedsApproval == true)
        {
            if (item.OptionGroup != null && item.OptionGroup.OptionGroupLetter != null && item.OptionGroup.OptionGroupLetter.StartsWith("A") || item.OptionGroup.OptionGroupLetter.StartsWith("B"))
            {
                args.Class = "structuralOption";
            }
            else
            {
                args.Class = "nonStructuralOption";

            }
        }

        if (item != null && item.BoolBuilderApproved == true)
        {
            args.Class = "builderApproved";
        }

    }
    void SelectedItemsChanged(IEnumerable<TbBuiltOptionDto> data)
    {
        foreach (var item in data)
        {
            if (item.MostRecent == true)
            {
                if (SelectedItems.Any(x => x.Id == item.Id))
                {
                    SelectedItems = SelectedItems.Where(x => x.Id != item.Id);//deselect, it was alredy selected
                }
                else
                {
                    SelectedItems = SelectedItems.Concat(new[] { item });//add to selected
                }

                SelectedItems = new List<TbBuiltOptionDto>(SelectedItems);
            }
            else
            {
                //don't add
            }
        }
    }

    async Task TabChangedHandler(int newIndex)
    {
        var a = 4;
        ActiveTabIndex = newIndex;
    }

    async Task OnRowClickHandler(GridRowClickEventArgs args)
    {
        var currItem = args.Item as TbBuiltOptionDto;
        var addItems = new List<TbBuiltOptionDto>();
        var currentSelected = SelectedItems.ToList();
        if (currItem.NeedsApproval == true)
        {
            if (SelectedItems.Any(x => x.Id == currItem.Id))
            {
                currItem.BoolBuilderApproved = false;
                SelectedItems = SelectedItems.Where(x => x.Id != currItem.Id);//deselect, it was alredy selected
            }
            else
            {
                currItem.BoolBuilderApproved = true;
                currentSelected.Add(currItem);
                SelectedItems = currentSelected;

                //SelectedItems = SelectedItems.Concat(new[] { currItem });//add to selected
            }

            SelectedItems = new List<TbBuiltOptionDto>(SelectedItems);
            args.ShouldRender = true;
        }
        else
        {
            //don't add
        }
    }
    async Task ToggleShowChangedOnly()
    {
        ShowHideChangedOnly = ShowChangedOnly ? "Show Changed Only" : "All";
        if (ShowChangedOnly)
        {
            SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData.Where(x => x.BuilderApproved == 0));
        }
        else
        {
            SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData);
        }
    }
    async Task ToggleNeedApprovalOnly()
    {
        ShowAll = ToggleShowAll;
        ShowHideNeedApprove = !ShowAll ? "Need Approval" : "All";
        if (!ShowAll)
        {
            SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData.Where(x => x.NeedsApproval == true));
        }
        else
        {
            SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData);
        }
    }
    protected async Task ApproveSelected()
    {
        var response = await SelectedOptionsService.ApproveSelectedOptionsAsync(SelectedItems.ToList());
        if (response.IsSuccess)
        {
            // foreach (var item in SelectedItems)
            // {
            //     item.NeedsApproval = false;
            //     item.BoolBuilderApproved = true;
            //     item.BuilderApproved = 1;
            //     //TODO: does it need an undo if she makes a mistake?
            // }
            //refresh
            SelectedItems = Enumerable.Empty<TbBuiltOptionDto>();
            var getSelectedOptions = await SelectedOptionsService.GetSelectedOptionsByJobAsync(JobNumber);
            var getSelectedOptionsData = getSelectedOptions.Value;
            SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(getSelectedOptionsData);
            AllSelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(getSelectedOptionsData);
            if (!ShowAll)
            {
                SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData.Where(x => x.NeedsApproval == true));
            }
            else
            {
                SelectedOptionsData = new ObservableCollection<TbBuiltOptionDto>(AllSelectedOptionsData);
            }
            SelectedItems = Enumerable.Empty<TbBuiltOptionDto>();
        }
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
    }
    protected async Task ShowAttributes(TbBuiltOptionDto item)
    {
        //var item = args.Item as TbBuiltOptionDto;
        if(item != null)
        {
            ItemToEdit = item;
            await ShowAttributesModal.Show();
        }

    }
    private void ShowSuccessOrErrorNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }

    private void ToggleSelectAll()
    {
        // if (SelectAllCheckBoxValue.HasValue && SelectAllCheckBoxValue.Value)
        // {
        //     SelectAllCheckBoxValue = false;
        // }
        // else
        // {
        //     SelectAllCheckBoxValue = true;
        // }
        var test = SelectAllCheckBoxValue;
        if (SelectAllCheckBoxValue == true)
        {
            foreach (var item in SelectedOptionsData.Where(x => x.NeedsApproval == true))
            {
                item.BoolBuilderApproved = true;
                item.BuilderApproved = 1;
            }
        }
        if (SelectAllCheckBoxValue == false)
        {
            foreach (var item in SelectedOptionsData.Where(x => x.NeedsApproval == true))
            {
                item.BoolBuilderApproved = false;
                item.BuilderApproved = 0;
            }
        }
    }

    private bool? SelectAllCheckBoxValue
    {
        get
        {
            if (IsAllDataSelected())
            {
                return true;
            }
            else if (IsAnyDataSelected())
            {
                return null;
            }

            return false;
        }

        set
        {
            if (value.HasValue && value.Value == true)
            {
                SelectedItems = SelectedOptionsData.Where(x => x.NeedsApproval == true);
            }
            else
            {
                SelectedItems = new List<TbBuiltOptionDto>();
            }
        }
    }

    private bool IsAnyDataSelected()
    {
        return SelectedItems.Count() > 0 && SelectedItems.Count() < SelectedOptionsData.Where(x => x.NeedsApproval == true).Count();
    }

    private bool IsAllDataSelected()
    {
        return SelectedItems.Count() == SelectedOptionsData.Where(x => x.NeedsApproval == true).Count();
    }

    async Task DownloadAllOptionsPdf()
    {
        if (AllSelectedOptionsData != null)
        {
            var getJobTask = SubdivisionService.GetLotAsync(JobNumber);
            var getBuyerTask = SelectedOptionsService.GetBuyersByJobAsync(JobNumber);
            var getAllOptionsTask = SelectedOptionsService.GetAllOptionsByJobAsync(JobNumber);
            await Task.WhenAll(new Task[] { getBuyerTask, getJobTask, getAllOptionsTask });
            var getJob = getJobTask.Result.Value;
            var getBuyers = getBuyerTask.Result.Value;
            var allOptions = getAllOptionsTask.Result.Value;
            var selectedOptions = AllSelectedOptionsData.Where(x => x.MostRecent == true && x.Removed == false).ToList();

            await GenerateDocumentAndDownload(selectedOptions, getJob, getBuyers, $"AllOptions-{getJob.JobNumber}.pdf", SelectedApprovedOrAllOptions.All, allOptions);
        }
    }

    async Task DownloadSelectedOptionsPdf()
    {
        if (AllSelectedOptionsData != null)
        {
            var getJobTask = SubdivisionService.GetLotAsync(JobNumber);
            var getBuyerTask =  SelectedOptionsService.GetBuyersByJobAsync(JobNumber);
            await Task.WhenAll(new Task[]{ getBuyerTask, getJobTask});
            var getJob = getJobTask.Result.Value;
            var getBuyers = getBuyerTask.Result.Value;
            var selectedOptions = AllSelectedOptionsData.Where(x => x.MostRecent == true && x.Removed == false).GroupBy(x => x.OptionCode).Select(x => x.OrderByDescending(y => y.CreatedDateTime).First()).ToList();
            await GenerateDocumentAndDownload(selectedOptions, getJob, getBuyers, $"SelectedOptions-{getJob.JobNumber}.pdf", SelectedApprovedOrAllOptions.Selected);
        }
    }
    async Task DownloadApprovedOptionsPdf()
    {
        if (AllSelectedOptionsData != null)
        {
            var getJobTask = SubdivisionService.GetLotAsync(JobNumber);
            var getBuyerTask = SelectedOptionsService.GetBuyersByJobAsync(JobNumber);
            await Task.WhenAll(new Task[] { getBuyerTask, getJobTask });
            var getJob = getJobTask.Result.Value;
            var getBuyers = getBuyerTask.Result.Value;
            //TODO: Is this getting the right options? Approved ones is not really correct, need to sum up the correct qty, etc, if there has been an add and delete
            //Use most recent, but not if it's deleted
            var approvedOptions = AllSelectedOptionsData.Where(x => x.BuilderApproved == 1 && x.MostRecent == true && x.Removed == false).GroupBy(x => x.OptionCode).Select(x => x.OrderByDescending(y => y.CreatedDateTime).First()).ToList();
            await GenerateDocumentAndDownload(approvedOptions, getJob, getBuyers, $"ApprovedOptions-{getJob.JobNumber}.pdf", SelectedApprovedOrAllOptions.Approved);
        }
    }
    async Task GenerateDocumentAndDownload(List<TbBuiltOptionDto> options, JobDto job, List<BuyerDto> buyers, string fileName, SelectedApprovedOrAllOptions selectedOrApprovedOrAll, List<AvailablePlanOptionDto> allOptions = null)
    {
        byte[] fileData = ERP.Web.DocumentProcessing.GenerateReleasePdf.GenerateReleaseFile(options, job, buyers, selectedOrApprovedOrAll, allOptions);
        DemoFileExporter.Save(JsRuntime, fileData, "application/pdf", fileName);
    }

    public enum SelectedApprovedOrAllOptions
    {
        [Description("SELECTED")] Selected,
        [Description("APPROVED")] Approved,
        [Description("ALL")] All
    }
    private async Task OnExcelBeforeExport(GridBeforeExcelExportEventArgs args)
    {

        // Customize the Width of the first exported column
        // // Customize the Width of the exported column
        // args.Columns[0].Width = "100px";
        // args.Columns[1].Width = "100px";
        // args.Columns[2].Width = "100px";
        // args.Columns[3].Width = "100px";
        // args.Columns[4].Width = "150px";
        // args.Columns[5].Width = "150px";
        // args.Columns[6].Width = "100px";
        // args.Columns[7].Width = "100px";
        // args.Columns[8].Width = "100px";
        // args.Columns[9].Width = "100px";
        // args.Columns[10].Width = "100px";
        // args.Columns[11].Width = "100px";
        // args.Columns[12].Width = "100px";
        // // Change the format of the Price column
        // // BuiltInNumberFormats is part of the Telerik.Documents.SpreadsheetStreaming namespace


        // // Change the format of the ReleaseDate column
        // args.Columns[8].NumberFormat = BuiltInNumberFormats.GetShortDate();
        // args.Columns[9].NumberFormat = BuiltInNumberFormats.GetShortDate();
        // args.Columns[11].NumberFormat = BuiltInNumberFormats.GetShortDate();


        // var hiddenColunnsToAdd = new List<GridExcelExportColumn>()
        // {
        //     new GridExcelExportColumn(){Title = "Lot Section Code", Field = "LotSectionCode" },
        //     new GridExcelExportColumn(){Title = "Lot Swing", Field = "LotSwing" },
        //     new GridExcelExportColumn(){Title = "Garage Orientation", Field = "GarageOrientation.Name" },
        //     new GridExcelExportColumn(){Title = "Lot Size", Field = "LotSize" },
        //     new GridExcelExportColumn(){Title = "Lot Width", Field = "LotWidth" },
        //     new GridExcelExportColumn(){Title = "Phase", Field = "Phase" },
        //     new GridExcelExportColumn(){Title = "Building Num", Field = "BuildingNum" },
        //     new GridExcelExportColumn(){Title = "Stick Num", Field = "StickBuildingNum" },
        //     new GridExcelExportColumn(){Title = "Home Orientation", Field = "HomeOrientation" },
        //     new GridExcelExportColumn(){Title = "Parking Num", Field = "ParkingNum" },
        //     new GridExcelExportColumn(){Title = "Storage Num", Field = "StorageNum" },
        //     new GridExcelExportColumn(){Title = "Notes", Field = "Notes" }
        // };
        // args.Columns.AddRange(hiddenColunnsToAdd);
    }
}
