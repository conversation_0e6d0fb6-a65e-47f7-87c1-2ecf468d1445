﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class MasterOptionAttributeItemDto
    {
        public int MasterOptionAttributeAssignmentId { get; set; }
        public int MasterOptionId { get; set; }
        public int? AttributeGroupAssignmentId { get; set; }
        public string? UpdatedBy { get; set; }
        public DateTime? UpdatedDateTime { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime CreatedDateTime { get; set; }
        public string? AttributeGroupDescription { get; set; }
        public int? AttributeGroupId { get; set; }
    }
}
