﻿@page "/constructiondailytasks"
@inject SubdivisionService SubdivisionService
@inject ScheduleService ScheduleService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject PoService PoService
@inject BudgetService BudgetService
@inject IJSRuntime JsRuntime
@attribute [Authorize(Roles = "Admin, ConstructionDirector, ConstructionManager")]
@using ERP.Data.Models.Dto
@using ERP.Data.Models
@using Telerik.DataSource.Extensions
@using System.Collections.ObjectModel
@inject LocalStorage LocalStorage

<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }

    .mytreeclass .k-drag-col {
        visibility: collapse;
    }

    .mytreeclass .k-header {
        min-width: 200px;
    }

    .mytreeclass {
        overflow-x: auto;
    }

    .k-drag-clue {
        color: white;
    }

    .k-icon.k-icon-with-modifier {
        display: none;
    }

    .SubdivisionTable .k-hierarchy-cell {
        display: none;
    }

    .k-splitbar {
        width: 15px;
        color: black;
        padding-left: 5px;
        padding-right: 5px;
    }

    .pane {
        padding: 8px;
        overflow-x: auto;
    }

    .k-splitter .k-pane {
        overflow-x: auto !important;
    }

    .k-splitter-horizontal {
        overflow-x: auto !important;
    }

    .page-wrapper .page-content {
        /* padding-right: 100px !important;
                                                                                                                                                                                                                                    margin-right: 100px !important;*/
        /*        width: 95% !important;*/
    }

    .page-wrapper {
        /* padding-right: 100px !important;
                                                                                                                                                                                                                                    margin-right: 100px !important;
                                                                                                                                                                                                                                        width: 80% !important;*/
    }

    .k-table-td {
        padding: 4px !important;
        font-size: 11px;
    }

    .k-table-td { /*these settings are to prevent the 0 width columns from affecting the column height*/
        padding: 4px !important;
        font-size: 11px;
        height: 30px !important;
        white-space: nowrap !important;
        overflow: visible;
    }

    .negativeValuesRowFormatting {
        background-color: #ffd6d6 !important;
    }

    .positiveValuesRowFormatting {
        background-color: #d7f5e3 !important;
    }

    .k-button-solid-base.k-selected {
        border-color: #f4cd64;
        color: black;
        background-color: #f4cd64;
    }

    .k-panelbar > .k-item > .k-link.k-selected, .k-panelbar > .k-panelbar-header > .k-link.k-selected {
        color: black;
        background-color: #f4cd64;
    }

    .k-panelbar > .k-item > .k-link, .k-panelbar > .k-panelbar-header > .k-link {
        color: black;
    }

        .k-panelbar > .k-panelbar-header > .k-link.k-selected:hover {
            background-color: #f4cd64;
        }

    .sticky-top {
        position: sticky;
        top: 60px;
        z-index: 995;
        background-color: #efefef;
    }

    .button-spacer {
        margin-left: 4px;
    }

    .filter-date-text {
        font-size: 12px;
    }

</style>

<TelerikMediaQuery Media="@SmallScreenMediaQuery" OnChange="@((doesMatch) => IsSmallScreen = doesMatch)"></TelerikMediaQuery>
<TelerikMediaQuery Media="@LargeScreenMediaQuery" OnChange="@((doesMatch) => IsLargeScreen = doesMatch)"></TelerikMediaQuery>
<div class="card sticky-top" style="background-color:#2e5771">
    <div class="card-body" style="padding:0.5rem">
        <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">My Activities Today - @MySubdivisions</h7>
    </div>
</div>
<TelerikTooltip TargetSelector=".tooltip-target" />
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

@if (IsLargeScreen)
{
    <div class="row">
        @if (IsLoading)
        {
            <p><em>Loading...</em></p>
            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />
        }
        else
        {
            <TelerikGrid @ref="GridRef"
            FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
            Sortable="true"
            OnUpdate="@UpdateItem"
            EditMode="GridEditMode.Incell"
            SelectionMode="GridSelectionMode.Single"
            OnStateInit="@( (GridStateEventArgs<ScheduleSactivityDto> args) => OnGridStateInit(args) )"
            ConfirmDelete="true"
            OnRead="@OnMyScheduleActivitiesRead"
            TItem="@ScheduleSactivityDto"
            ScrollMode="@GridScrollMode.Virtual"
            Height="650px"
            RowHeight="40"
            Width="100%"
            PageSize="20">
                <GridColumns>
                    <GridColumn Field="ScheduleM.Schedule.JobNumber" Title="Job" FilterMenuType="FilterMenuType.CheckBoxList" Editable="false" Groupable="true" Filterable="false" Width="100px" Locked="true" />
                    <GridColumn Field="ScheduleM.Schedule.JobNumberNavigation.Subdivision.SubdivisionName" FilterMenuType="FilterMenuType.CheckBoxList" Title="Subdivision" Editable="false" Groupable="true" Filterable="false" Width="150px" />
                    <GridColumn Field="BoolStarted" Title="Started" Editable="false" Filterable="false" Groupable="false" Width="75px">
                        <Template>
                            @{
                                var itemToEdit = context as ScheduleSactivityDto;
                                if (itemToEdit != null)
                                {
                                    <TelerikCheckBox Enabled="AllowEdit" OnChange="() => ChangeStarted(itemToEdit)" @bind-Value="itemToEdit.BoolStarted"></TelerikCheckBox>
                                }
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn Field="Complete" Title="Complete" Editable="false" Filterable="false" Groupable="true" Width="75px">
                        <Template>
                            @{
                                var itemToEdit = context as ScheduleSactivityDto;
                                if (itemToEdit != null)
                                {
                                    <TelerikCheckBox Enabled="AllowEdit" OnChange="() => ChangeComplete(itemToEdit)" @bind-Value="itemToEdit.BoolComplete"></TelerikCheckBox>
                                }
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn Field="Sactivity.ActivityName" Title="Activity" Editable="false" Groupable="true" Width="200px">
                        <Template>
                            @{
                                var item = context as ScheduleSactivityDto;
                                <TelerikButton Title="View/Edit" OnClick="() => EditActivity(item)" Class="k-button-success">Edit</TelerikButton>
                                @($" - {item.Sactivity.ActivityName}")

                            }
                        </Template>
                    </GridColumn>
                    <GridColumn Field="VarianceCode" Title="Variance" Editable="false" FilterMenuType="FilterMenuType.CheckBoxList" Groupable="true" Width="100px">
                        <Template>
                            @{
                                var item = context as ScheduleSactivityDto;
                                if (item != null)
                                {
                                    <span>@item.VarianceCode</span>
                                }
                            }
                        </Template>
                        <EditorTemplate>
                            ItemToEdit = context as ScheduleSactivityDto;
                            if (ItemToEdit != null && ItemToEdit.ScheduleSactivity != null)
                            {
                            <TelerikDropDownList Data="@VarianceCodes"
                            TextField="VarianceDesc"
                            ValueField="VarianceCode"
                            @bind-Value="ItemToEdit.VarianceCode">
                            </TelerikDropDownList>
                            }
                            }
                        </EditorTemplate>
                    </GridColumn>
                    <GridColumn Field="SchStartDate" Title="Scheduled Start" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Groupable="true" Filterable="false" Width="125px" />
                    <GridColumn Field="SchEndDate" Title="Scheduled End" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Groupable="true" Filterable="false" Width="125px" />
                    <GridColumn Field="ActualStartDate" Title="Actual Start" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Groupable="true" Width="125px" />
                    <GridColumn Field="ActualEndDate" Title="Actul End" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Groupable="true" Width="125px" />
                    <GridColumn Field="ScheduleM.Schedule.JobNumberNavigation.LotNumber" Title="Lot" Editable="false" Groupable="true" Filterable="false" Width="100px" />
                    <GridColumn Field="ScheduleM.Schedule.JobNumberNavigation.JobAddress1" Title="Address" Editable="false" Groupable="true" Width="125px" />
                    <GridCommandColumn Width="200px">
                        @{
                            var item = context as ScheduleSactivityDto;
                            if (item.HasPo)
                            {
                                <GridCommandButton Class="tooltip-target k-button-success" Icon="@FontIcon.Download" Title="PO" OnClick="ViewPo"></GridCommandButton>
                            }
                            @*   <GridCommandButton Class="tooltip-target k-button-success" Icon="@FontIcon.Download" Title="PO" OnClick="ViewPo"></GridCommandButton> *@
                            <GridCommandButton Class="tooltip-target k-button-success" Title="Job Details" OnClick="SelectJobDetails" Command="Details">Details</GridCommandButton>
                            <GridCommandButton Class="tooltip-target k-button-success" Title="Schedule" OnClick="SelectSchedule" Command="Schedule">Schedule</GridCommandButton>
                        }
                    </GridCommandColumn>
                </GridColumns>
                <GridToolBarTemplate>
                    <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    @if (AllowEdit)
                    {
                        <GridCommandButton Command="Save" OnClick="@Save" Title="Save Changes" Icon="@FontIcon.Save" Class=" tooltip-target k-button-success"></GridCommandButton>
                        <GridCommandButton Command="Cancel" OnClick="@Cancel" Title="Cancel Changes" Icon="@FontIcon.Cancel" Class=" tooltip-target k-button-danger"></GridCommandButton>
                    }
                    <TelerikButton OnClick="@SaveLayout" Class="mr-xs">Save Layout</TelerikButton>
                    <TelerikButton OnClick="@LoadLayout" Class="mr-xs">Load Last Layout</TelerikButton>
                    <TelerikButton OnClick="@ResetLayout" Class="mr-xs">Reset Layout</TelerikButton>
                    <TelerikMultiSelect Data="@Subdivisions"
                    @bind-Value="@SelectedSubdivisions"
                    AutoClose="false"
                    TagMode="MultiSelectTagMode.Single"
                    MaxAllowedTags="1"
                    Placeholder="Filter by Subdivision"
                    OnChange="OnSubdivisionFilter"
                    Width="200px">
                    </TelerikMultiSelect>
                    <TelerikMultiSelect @ref="@JobFilterMultiSelectRef"
                    Data="@JobsToFilterBy"
                    ValueField="JobNumber"
                    TextField="JobAndLotNumString"
                    @bind-Value="@SelectedJobNumbers"
                    AutoClose="false"
                    PersistFilterOnSelect="true"
                    TagMode="MultiSelectTagMode.Single"
                    MaxAllowedTags="1"
                    Filterable="true"
                    FilterOperator="@StringFilterOperator.Contains"
                    Placeholder="Filter by Job"
                    OnChange="OnJobFilter"
                    Width="200px">
                    </TelerikMultiSelect>
                    <TelerikPopup @ref="@SchStartDatePopupRef"
                    AnchorSelector=".popup-target-sch-start"
                    AnimationType="@AnimationType.SlideDown"
                    AnimationDuration="200"
                    Width="250px"
                    Height="105px">
                        <div style="text-align: center;">
                            <div class="filter-date-text">
                                Scheduled Start is on or before
                            </div>
                            <TelerikDatePicker Width="200px" @bind-Value="SchStartDateFilter"></TelerikDatePicker>
                            <div style="padding-top: 5px;">
                                <TelerikButton OnClick="OnSchStartDateFilter">Filter</TelerikButton>
                                <TelerikButton OnClick="OnClearSchStartDateFilter">Clear</TelerikButton>
                            </div>
                        </div>
                    </TelerikPopup>

                    <TelerikButton OnClick="@( () => ShowHideDateFilterPopup(SchStartDatePopupRef) )"
                    Class="popup-target-sch-start">
                        @SchStartDateFilterButtonText
                    </TelerikButton>

                    <TelerikPopup @ref="@SchEndDatePopupRef"
                    AnchorSelector=".popup-target-sch-end"
                    AnimationType="@AnimationType.SlideDown"
                    AnimationDuration="200"
                    Width="250px"
                    Height="105px">
                        <div style="text-align: center;">
                            <div class="filter-date-text">
                                Scheduled End is on or before
                            </div>
                            <TelerikDatePicker Width="200px" @bind-Value="SchEndDateFilter"></TelerikDatePicker>
                            <div style="padding-top: 5px;">
                                <TelerikButton OnClick="OnSchEndDateFilter">Filter</TelerikButton>
                                <TelerikButton OnClick="OnClearSchEndDateFilter">Clear</TelerikButton>
                            </div>
                        </div>
                    </TelerikPopup>

                    <TelerikButton OnClick="@( () => ShowHideDateFilterPopup(SchEndDatePopupRef) )"
                    Class="popup-target-sch-end">
                        @SchEndDateFilterButtonText
                    </TelerikButton>
                </GridToolBarTemplate>
            </TelerikGrid>
        }
        <br />
        <br />
    </div>
}
else
{
    if (MyScheduleActivitiesForSmallScreen == null || IsLoading)
    {
        <p><em>Loading...</em></p>
        <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />
    }
    else
    {
        <div class="sticky-top">
            @if (AllowEdit)
            {
                <TelerikButton OnClick="@Save" Title="Save" Icon="@FontIcon.Save" Class="tooltip-target k-button-success" />
                <TelerikButton OnClick="@Cancel" Title="Cancel Changes" Icon="@FontIcon.Cancel" Class="tooltip-target k-button-danger"></TelerikButton>
            }

            <hr />
        </div>
        <TelerikPanelBar Data="MyScheduleActivitiesForSmallScreen">
            <PanelBarBindings>
                <PanelBarBinding>
                    <HeaderTemplate>
                        @{
                            var item = context as ScheduleSactivityDto;
                            <div class="row justify-content-center" style="width:100%; text-align:center;">
                                <div class="col-12" style="text-align:center; width:100%; font-size: 14px">@item.ScheduleM.Schedule.JobNumber</div>
                                <div class="col-12" style="text-align:center; width:100%; font-size: 14px">@item.Sactivity.ActivityName</div>
                                <div class="col-12" style="text-align:center; width:100%; font-size: 12px">Sch. Start: @item.SchStartDate?.ToString("MM/dd/yyyy") - Sch. End: @item.SchEndDate?.ToString("MM/dd/yyyy") </div>
                            </div>
                        }
                    </HeaderTemplate>
                    <ContentTemplate>
                        @{
                            var details = context as ScheduleSactivityDto;
                            <div class="k-card-body">
                                <div class="row">
                                    <div class="col-4" style="text-align: left; padding-left: 10px">
                                        <label class="form-label" style="font-size: 15px">
                                            <TelerikCheckBox Enabled="AllowEdit" Class="my-checkbox" OnChange="@(() => ChangeComplete(details))" @bind-Value="@details.BoolComplete"></TelerikCheckBox>
                                            Complete
                                        </label>
                                    </div>
                                    <div class="col-4" style="text-align: left; padding-right: 10px">
                                        <label class="form-label" style="font-size: 15px">
                                            <TelerikCheckBox Enabled="AllowEdit" Class="my-checkbox" OnChange="@(() => ChangeStarted(details))" @bind-Value="@details.BoolStarted"></TelerikCheckBox>
                                            Started
                                        </label>
                                    </div>
                                    <div class="col-4" style="text-align: left; padding-right: 10px">
                                        <label class="form-label" style="font-size: 15px">
                                            <TelerikButton Enabled="AllowEdit" Title="View/Edit" OnClick="() => EditActivity(details)" Class="k-button-success">Edit</TelerikButton>
                                        </label>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4 col-sm-6 col-xs-6">
                                        <h6>Variance:</h6>
                                        <TelerikDropDownList Data="@VarianceCodes"
                                        TextField="VarianceDesc"
                                        ValueField="VarianceCode"
                                        @bind-Value="details.VarianceCode">
                                        </TelerikDropDownList>
                                    </div>
                                    <div class="col-md-4 col-sm-6 col-xs-6">
                                        <h6>Actual Start:</h6>
                                        <TelerikDatePicker @bind-Value="@details.ActualStartDate"
                                        Min="@Min"
                                        Max="@Max"
                                        Format="MM/dd/yyyy"
                                        DebounceDelay="@DebounceDelay"
                                        ShowWeekNumbers="true"
                                        Enabled="false">
                                            <DatePickerFormatPlaceholder Day="day" Month="month" Year="year" />
                                        </TelerikDatePicker>
                                    </div>
                                    <div class="col-md-4 col-sm-6 col-xs-6">
                                        <h6>Actual End:</h6>
                                        <TelerikDatePicker @bind-Value="@details.ActualEndDate"
                                        Min="@Min"
                                        Max="@Max"
                                        Format="MM/dd/yyyy"
                                        DebounceDelay="@DebounceDelay"
                                        ShowWeekNumbers="true"
                                        Enabled="false">
                                            <DatePickerFormatPlaceholder Day="day" Month="month" Year="year" />
                                        </TelerikDatePicker>
                                    </div>

                                    <br />
                                    <hr class="mt-2" />
                                </div>
                            </div>
                        }
                    </ContentTemplate>
                </PanelBarBinding>
            </PanelBarBindings>
        </TelerikPanelBar>
    }

}

<EditScheduleActivity @ref="EditScheduleActivity" SelectedActivity="@SelectedActivity" HandleAddSubmit="@HandleValidEditActivitySubmit"></EditScheduleActivity>
<NavigationLock ConfirmExternalNavigation="@UnsavedChanges" OnBeforeInternalNavigation="BeforeInternalNavigation" />

@code {

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    public List<ScheduleSactivityDto>? MyScheduleActivities { get; set; } = new List<ScheduleSactivityDto>();
    public List<ScheduleSactivityDto>? UpdatedScheduleActivities { get; set; } = new List<ScheduleSactivityDto>();//so don't send the whole batch to save only what's changed
    public List<ScheduleSactivityDto>? MyScheduleActivitiesForSmallScreen { get; set; } = new List<ScheduleSactivityDto>();
    private List<ScheduleSactivityDto> FilteredData { get; set; } = new List<ScheduleSactivityDto>();
    private bool UnsavedChanges { get; set; } = false;
    public ScheduleSactivityDto? ItemToEdit { get; set; } = new ScheduleSactivityDto();
    public List<VarianceDto>? VarianceCodes { get; set; }
    public List<JobDto?> AllJobsForUser { get; set; } = new List<JobDto?>();
    public List<JobDto?> JobsToFilterBy { get; set; } = new List<JobDto?>();
    public List<string?> JobNumbers { get; set; } = new List<string?>();
    public List<string?> Subdivisions { get; set; } = new List<string?>();
    public List<string?> SelectedJobNumbers { get; set; } = new List<string?>();
    public List<string?> SelectedSubdivisions { get; set; } = new List<string?>();
    public TelerikMultiSelect<JobDto, string?> JobFilterMultiSelectRef { get; set; } = new TelerikMultiSelect<JobDto, string?>();
    private bool userIsDirector { get; set; } = false;
    private bool IsSmallScreen { get; set; }
    private bool IsLargeScreen { get; set; }
    private string SmallScreenMediaQuery { get; set; } = "(max-width: 767px)";
    private string LargeScreenMediaQuery { get; set; } = "(min-width: 1024px)";//Previous 1199px
    public TelerikGrid<ScheduleSactivityDto> GridRef { get; set; } = new TelerikGrid<ScheduleSactivityDto>();
    protected EditScheduleActivity? EditScheduleActivity { get; set; }
    public ScheduleSactivityDto? SelectedActivity { get; set; }
    private string? JobSelected { get; set; }
    public bool IsLoading { get; set; }
    private bool AllowEdit { get; set; } = true;
    private const string SubdivisionNameType = "ScheduleM.Schedule.JobNumberNavigation.Subdivision.SubdivisionName";
    private const string JobNumberType = "ScheduleM.Schedule.JobNumber";
    private const string SchStartDateType = "SchStartDate";
    private const string SchEndDateType = "SchEndDate";
    private TelerikPopup? SchStartDatePopupRef { get; set; }
    private TelerikPopup? SchEndDatePopupRef { get; set; }
    private List<TelerikPopup?> PopupRefs = new List<TelerikPopup?>();
    private bool IsPopupVisible { get; set; } = false;
    private DateTime? SchStartDateFilter { get; set; }
    private DateTime? SchEndDateFilter { get; set; }
    private string SchStartDateFilterButtonText { get; set; } = "Filter By Sch Start Date";
    private string SchEndDateFilterButtonText { get; set; } = "Filter By Sch End Date";
    public string? MySubdivisions { get; set; } = "";

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    // Local Storage
    string stateStorageKey = "erp-daily-tasks";

    //DatePickers
    private DateTime? SelectedDate { get; set; }
    private DateTime Max = new DateTime(2050, 12, 31);
    private DateTime Min = new DateTime(1950, 1, 1);
    private int DebounceDelay { get; set; } = 200;


    private async Task BeforeInternalNavigation(LocationChangingContext context)
    {
        if (UnsavedChanges)
        {
            var proceed = await Dialogs.ConfirmAsync("You have unsaved changes, are you sure you want to leave this page?");

            if (!proceed)
            {
                context.PreventNavigation();
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        IsLoading = true;//Load Spinner

        StateHasChanged();

        await Task.Delay(1);//This hack seems to give it a chance to render and fix the bug on go to schedule and hit back button

        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var varianceCodesTask = ScheduleService.GetVarianceCodesAsync();
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userRoleConstructionDirector = user.User.IsInRole("ConstructionDirector");
        userIsDirector = userRoleAdmin || userRoleConstructionDirector;
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;

        //get all activities for user
        var schedulesTask = ScheduleService.GetScheduleActivitysForConstructionSuperAsync(userName);
        await Task.WhenAll(new Task[] { schedulesTask, varianceCodesTask });
        MyScheduleActivities = schedulesTask.Result.Value;
        MyScheduleActivitiesForSmallScreen = schedulesTask.Result.Value;
        VarianceCodes = varianceCodesTask.Result.Value;
        AllJobsForUser = schedulesTask.Result.Value.Select(j => j.ScheduleM.Schedule.JobNumberNavigation).GroupBy(x => x.JobNumber).Select(y => y.First()).ToList();
        Subdivisions = schedulesTask.Result.Value.Select(j => j.ScheduleM.Schedule.JobNumberNavigation.Subdivision.SubdivisionName).GroupBy(x => x).Select(y => y.First()).ToList();
        MySubdivisions = string.Join(", ", Subdivisions);
        foreach (var job in AllJobsForUser)
        {
            if (job is null)
                continue; 

            job.JobAndLotNumString = job.LotNumber != string.Empty ? job.JobNumber + " -- Lot: " + job.LotNumber : job.JobNumber;
            JobsToFilterBy.Add(job.ShallowCopy());
        }

        IsLoading = false;

        StateHasChanged();
    }

    protected override void OnAfterRender(bool firstRender)
    {
        PopupRefs = new List<TelerikPopup?> { SchStartDatePopupRef, SchEndDatePopupRef };
    }

    private async Task OnGridStateInit(GridStateEventArgs<ScheduleSactivityDto> args)
    {
        // Filter ctivities that start today or earlier
        var state = await LocalStorage.GetItem<GridState<ScheduleSactivityDto>>(stateStorageKey);
        if (state != null)
            args.GridState = state;

        args.GridState.FilterDescriptors.Clear();
        SchStartDateFilterButtonText = "Filtering by Sch Start Dates On or Before: " + DateTime.Now.ToShortDateString();
        SchStartDateFilter = DateTime.Now;

        var todayActivities = new CompositeFilterDescriptor()
            {
                FilterDescriptors = new FilterDescriptorCollection()
                {
                 new FilterDescriptor()
                 {
                    Member = SchStartDateType,
                    MemberType = typeof(DateTime?),
                    Operator = FilterOperator.IsLessThanOrEqualTo,
                    Value =  SchStartDateFilter
                 }
                }
            };

        args.GridState.FilterDescriptors.Add(todayActivities);

    }

    private void ClearFilters(GridState<ScheduleSactivityDto> state, string filterType)
    {
        var compositeFilterDescriptors = state.FilterDescriptors.Select(x => (CompositeFilterDescriptor)(object)x);
        var compositeFilterDescriptorsToRemove = compositeFilterDescriptors.Where(x => x.FilterDescriptors.SelectMemberDescriptors().Where(fd => fd.Member == filterType).Any()).ToList();
        if (compositeFilterDescriptorsToRemove != null)
            foreach (var compositeFilterDescriptor in compositeFilterDescriptorsToRemove)
                state.FilterDescriptors.Remove(compositeFilterDescriptor);
    }

    private async Task OnClearSchStartDateFilter()
    {
        var state = GridRef.GetState();
        ClearFilters(state, SchStartDateType);
        SchStartDateFilter = null;
        SchStartDateFilterButtonText = "Filter By Sch Start Date";
        await GridRef.SetStateAsync(state);
        SchStartDatePopupRef?.Hide();
        IsPopupVisible = false;
    }

    private async Task OnClearSchEndDateFilter()
    {
        var state = GridRef.GetState();
        ClearFilters(state, SchEndDateType);
        SchEndDateFilter = null;
        SchEndDateFilterButtonText = "Filter By Sch End Date";
        await GridRef.SetStateAsync(state);
        SchEndDatePopupRef?.Hide();
        IsPopupVisible = false;
    }

    async Task SelectSchedule(GridCommandEventArgs args)
    {
        var selectedSchedule = args.Item as ScheduleSactivityDto;
        if (selectedSchedule != null)
        {
            NavManager.NavigateTo($"schedule");
            StateHasChanged();
        }
    }

    async Task SelectJobDetails(GridCommandEventArgs args)
    {
        var selectedSchedule = args.Item as ScheduleSactivityDto;
        if (selectedSchedule != null)
        {
            NavManager.NavigateTo($"lotdetails/{selectedSchedule.ScheduleM.Schedule.JobNumber}");
            StateHasChanged();
        }
    }

    private async Task OnSubdivisionFilter()
    {
        var state = GridRef.GetState();

        // On change, remove current filter(s) on subdivisions, and reset the list of possible jobs to filter by. 
        ClearFilters(state, SubdivisionNameType);
        JobsToFilterBy.Clear();

        // if there are any selected value to filter by, add those
        if (SelectedSubdivisions.Count > 0)
        {

            var filterDescriptorsToAdd = new CompositeFilterDescriptor();
            filterDescriptorsToAdd.LogicalOperator = FilterCompositionLogicalOperator.Or;
            foreach (var value in SelectedSubdivisions)
            {
                filterDescriptorsToAdd.FilterDescriptors.Add(
                new FilterDescriptor()
                    {
                        Member = SubdivisionNameType,
                        MemberType = typeof(string),
                        Operator = FilterOperator.Contains,
                        Value = value
                    }
                );

                // If filering by subdivsion(s), only allow the user to filter by job(s) within that subdivision
                JobsToFilterBy.AddRange(AllJobsForUser.Select(x => x).Where(x => x is not null && x.Subdivision is not null && x.Subdivision.SubdivisionName.HasValue() && x.Subdivision.SubdivisionName == value).ToList());
            }

            state.FilterDescriptors.Add(filterDescriptorsToAdd);
        }
        else
        {
            // if the user has cleared the subdivision filter, go back to showing all jobs assigned to the user
            foreach (var job in AllJobsForUser)
            {
                if (job is not null)
                    JobsToFilterBy.Add(job.ShallowCopy());
            }
        }

        JobFilterMultiSelectRef.Rebind();

        await GridRef.SetStateAsync(state);
    }

    private async Task OnJobFilter()
    {
        var state = GridRef.GetState();

        // On change, remove current filters
        ClearFilters(state, JobNumberType);

        // if there are any selected value to filter by, add those
        if (SelectedJobNumbers.Count > 0)
        {
            var filterDescriptorsToAdd = new CompositeFilterDescriptor();
            filterDescriptorsToAdd.LogicalOperator = FilterCompositionLogicalOperator.Or;
            foreach (var value in SelectedJobNumbers)
            {
                filterDescriptorsToAdd.FilterDescriptors.Add(
                new FilterDescriptor()
                    {
                        Member = JobNumberType,
                        MemberType = typeof(string),
                        Operator = FilterOperator.Contains,
                        Value = value
                    }
                );
            }

            state.FilterDescriptors.Add(filterDescriptorsToAdd);
        }

        await GridRef.SetStateAsync(state);
    }

    private async Task OnSchEndDateFilter()
    {
        var state = GridRef.GetState();

        if (SchEndDateFilter is null)
            //TODO: let user know to enter a value
            return;

        // On change, remove current filters
        ClearFilters(state, SchEndDateType);

        var filterDescriptorToAdd = new CompositeFilterDescriptor();
        filterDescriptorToAdd.FilterDescriptors.Add(
        new FilterDescriptor()
            {
                Member = SchEndDateType,
                MemberType = typeof(DateTime?),
                Operator = FilterOperator.IsLessThanOrEqualTo,
                Value = SchEndDateFilter
            }
        );


        state.FilterDescriptors.Add(filterDescriptorToAdd);
        await GridRef.SetStateAsync(state);

        SchEndDateFilterButtonText = "Filtering by Sch End Dates On or Before: " + SchEndDateFilter.Value.ToShortDateString();
        StateHasChanged();
        SchEndDatePopupRef?.Hide();
        IsPopupVisible = false;
    }

    private async Task OnSchStartDateFilter()
    {
        var state = GridRef.GetState();

        if (SchStartDateFilter is null)
            //TODO: let user know to enter a value
            return;

        // On change, remove current filters
        ClearFilters(state, SchStartDateType);

        var filterDescriptorToAdd = new CompositeFilterDescriptor();
        filterDescriptorToAdd.FilterDescriptors.Add(
        new FilterDescriptor()
            {
                Member = SchStartDateType,
                MemberType = typeof(DateTime?),
                Operator = FilterOperator.IsLessThanOrEqualTo,
                Value = SchStartDateFilter
            }
        );


        state.FilterDescriptors.Add(filterDescriptorToAdd);
        await GridRef.SetStateAsync(state);

        SchStartDateFilterButtonText = "Filtering by Sch Start Dates On or Before: " + SchStartDateFilter.Value.ToShortDateString();
        StateHasChanged();
        SchStartDatePopupRef?.Hide();
        IsPopupVisible = false;
    }

    private void ShowHideDateFilterPopup(TelerikPopup? popUpRef)
    {
        // hide all other open popups
        foreach (var pRef in PopupRefs)
            if (pRef != popUpRef)
                pRef?.Hide();

        if (IsPopupVisible)
            popUpRef?.Hide();
        else
            popUpRef?.Show();

        IsPopupVisible = !IsPopupVisible;
    }

    private async Task UpdateItem(GridCommandEventArgs args)
    {
        //TODO: allow update scheduled dates and variance here, update schedule for that job
        //variance and scheduled start editable here??
        var item = args.Item as ScheduleSactivityDto;
        var editedField = args.Field;
        if (item != null)
        {
            UnsavedChanges = true; //TODO: check something actually changed
            var getCalendar = (await ScheduleService.GetHolidaysAsync()).Value;
            var holidays = getCalendar.Select(x => x.WorkDate).ToList();
            bool updateBaseDates = false;

        }
    }

    public async Task ChangeComplete(ScheduleSactivityDto itemToEdit)
    {
        var findItem = MyScheduleActivities.FirstOrDefault(x => x.ScheduleAid == itemToEdit.ScheduleAid);
        if (itemToEdit.BoolComplete && findItem != null)
        {
            if (itemToEdit.ActualStartDate == null)
            {
                Dialogs.AlertAsync("Activity must be started before being completed.");
                findItem.BoolComplete = false;
                return;
            }
            else if (!userIsDirector && (itemToEdit.SchEndDate < DateTime.Now.Date.AddDays(-1)))
            {
                //basedateeditable is user is in construction dir role
                // await Dialogs.AlertAsync("You are late! You must select a variance and then adjust the scheduled dates! If the scheduled activity was completed on time, but you forgot to mark it, you must contact Construction Director");
                // findItem.BoolComplete = false;
                //for now just warn if late
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    findItem.BoolComplete = false;
                }
                else
                {
                    findItem.ActualEndDate = findItem.SchEndDate;
                    UpdatedScheduleActivities.Add(findItem);
                    UnsavedChanges = true;
                }
            }
            else if (userIsDirector && (itemToEdit.SchStartDate.Value.Date < DateTime.Now.Date))
            {
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    findItem.BoolComplete = false;
                }
                else
                {
                    findItem.ActualEndDate = findItem.SchEndDate;
                    UpdatedScheduleActivities.Add(findItem);
                    UnsavedChanges = true;
                }
            }
            else
            {
                findItem.ActualEndDate = findItem.SchEndDate;
                UpdatedScheduleActivities.Add(findItem);
                UnsavedChanges = true;
            }
        }
        else
        {
            findItem.ActualEndDate = null;
            UnsavedChanges = true;
        }
        GridRef.Rebind();
    }

    public async Task ChangeStarted(ScheduleSactivityDto itemToEdit)
    {
        var findItem = MyScheduleActivities.FirstOrDefault(x => x.ScheduleAid == itemToEdit.ScheduleAid);
        if (itemToEdit.BoolStarted && findItem != null)
        {
            if (!userIsDirector && (itemToEdit.SchStartDate.Value.Date < DateTime.Now.Date))
            {
                //await Dialogs.AlertAsync("You are late! You must select a variance and then adjust the scheduled dates! If the scheduled activity was started on time, but you forgot to mark it, you must contact Construction Director");
                //findItem.BoolStarted = false;
                //for now just warn if late
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    findItem.BoolStarted = false;
                }
                else
                {
                    itemToEdit.ActualStartDate = itemToEdit.SchStartDate;
                    findItem.ActualStartDate = itemToEdit.SchStartDate;
                    UpdatedScheduleActivities.Add(findItem);
                    UnsavedChanges = true;
                }

            }
            else if (userIsDirector && (itemToEdit.SchStartDate.Value.Date < DateTime.Now.Date))
            {
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    findItem.BoolStarted = false;
                }
                else
                {
                    itemToEdit.ActualStartDate = itemToEdit.SchStartDate;
                    findItem.ActualStartDate = itemToEdit.SchStartDate;
                    UpdatedScheduleActivities.Add(findItem);
                    UnsavedChanges = true;
                }
            }
            else
            {
                itemToEdit.ActualStartDate = itemToEdit.SchStartDate;
                findItem.ActualStartDate = itemToEdit.SchStartDate;
                UpdatedScheduleActivities.Add(findItem);
                UnsavedChanges = true;
            }
        }
        else
        {
            findItem.ActualEndDate = null;
            findItem.ActualStartDate = null;
            UpdatedScheduleActivities.Add(findItem);
            UnsavedChanges = true;
        }
        GridRef.Rebind();
    }

    public async void Save()
    {
        IsLoading = true;//Load Spinner
        var activitiesToUpdate = UpdatedScheduleActivities;
        var response = await ScheduleService.UpdateScheduleSactivitysAsync(activitiesToUpdate);

        if (response.IsSuccess)
        {
            UnsavedChanges = false;

            UpdatedScheduleActivities = new List<ScheduleSactivityDto>();//clear the list
            //reload the data to remove the approved ones -- too slow
            // var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            // var userName = user.User.Identity.Name.Split('@')[0];

            // var schedulesTask = await ScheduleService.GetScheduleActivitysForConstructionSuperAsync(userName);

            // MyScheduleActivities = schedulesTask.Value;
            // MyScheduleActivitiesForSmallScreen = schedulesTask.Value;
        }

        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        IsLoading = false;
        StateHasChanged();
    }

    public async void Cancel()
    {
        //refresh the data
        UnsavedChanges = false;
        UpdatedScheduleActivities = new List<ScheduleSactivityDto>();//clear the list
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];

        IsLoading = true;

        if (!string.IsNullOrWhiteSpace(JobSelected))
        {
            var schedulesTask = await ScheduleService.GetScheduleActivitysForConstructionSuperByJobNumberAsync(userName, JobSelected);

            MyScheduleActivities = schedulesTask.Value;
            MyScheduleActivitiesForSmallScreen = schedulesTask.Value;
        }
        else
        {
            var schedulesTask = await ScheduleService.GetScheduleActivitysForConstructionSuperAsync(userName);

            MyScheduleActivities = schedulesTask.Value;
            MyScheduleActivitiesForSmallScreen = schedulesTask.Value;
        }

        IsLoading = false;

        ShowSuccessOrErrorNotification("Edits Cancelled", true);

        StateHasChanged();
    }

    private void EditActivity(ScheduleSactivityDto activity)
    {
        if (activity != null)
        {
            SelectedActivity = activity;
        }
        EditScheduleActivity?.Show();
        StateHasChanged();
    }

    private async void HandleValidEditActivitySubmit(ResponseModel<ScheduleSactivityDto> responseActivity)
    {
        EditScheduleActivity.Hide();
        if (responseActivity.IsSuccess)
        {
            //reload the data to remove the approved ones
            var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userName = user.User.Identity.Name.Split('@')[0];
            MyScheduleActivities = (await ScheduleService.GetScheduleActivitysForConstructionSuperAsync(userName)).Value;
        }
        ShowSuccessOrErrorNotification(responseActivity.Message, responseActivity.IsSuccess);
        StateHasChanged();
    }

    async Task ViewPo(GridCommandEventArgs args)
    {
        var activity = args.Item as ScheduleSactivityDto;
        if (activity != null)
        {
            var getPos = await PoService.GetPOForScheduleActivityAsync(activity.ScheduleAid);
            if (getPos?.Value != null && getPos.Value.Count != 0)
            {
                var details = getPos.Value;
                var groupedByHeder = details.GroupBy(x => x.PoheaderId).ToList();
                foreach (var po in groupedByHeder)
                {
                    var header = po.First().Poheader;
                    await GenerateDocumentAndDownload(header, po.Select(x => x).ToList());
                }
            }

        }
    }

    async Task GenerateDocumentAndDownload(PoheaderDto poHeader, List<PodetailDto>
        poDetails)
    {
        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(poHeader, poDetails);
        DemoFileExporter.Save(JsRuntime, fileData, "application/pdf", $"PO-{poHeader.Ponumber}.pdf");
    }

    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }

    // Grid State Actions
    private async Task SaveLayout()
    {
        var gridState = GridRef.GetState();
        await LocalStorage.SetItem(stateStorageKey, gridState);
    }

    private async Task LoadLayout()
    {
        GridState<ScheduleSactivityDto>
            storedState = await LocalStorage.GetItem<GridState<ScheduleSactivityDto>>(stateStorageKey);
        if (storedState != null)
        {
            await GridRef.SetStateAsync(storedState);
        }
    }

    private async void ResetLayout()
    {
        await GridRef.SetStateAsync(null);
        await LocalStorage.RemoveItem(stateStorageKey);
        await ResetFilters();
    }

    private async Task ResetFilters()
    {
        SelectedJobNumbers = new List<string?>();
        SelectedSubdivisions = new List<string?>();
        await OnClearSchStartDateFilter();
        await OnClearSchEndDateFilter();
        await SetDefaultFilters();
    }

    private async Task SetDefaultFilters()
    {
        GridState<ScheduleSactivityDto> state = GridRef.GetState();

        SchStartDateFilterButtonText = "Filtering by Sch Start Dates On or Before: " + DateTime.Now.ToShortDateString();
        SchStartDateFilter = DateTime.Now;
        var todayActivities = new CompositeFilterDescriptor()
            {
                FilterDescriptors = new FilterDescriptorCollection()
                {
                 new FilterDescriptor()
                 {
                    Member = SchStartDateType,
                    MemberType = typeof(DateTime?),
                    Operator = FilterOperator.IsLessThanOrEqualTo,
                    Value = SchStartDateFilter
                 }
                }
            };

        state.FilterDescriptors.Add(todayActivities);
        await GridRef.SetStateAsync(state);
    }
    void ReloadPage()
    {
        JsRuntime.InvokeVoidAsync("window.location.reload");
    }

    public void Dispose()
    {

    }

    private async Task OnMyScheduleActivitiesRead(GridReadEventArgs args)
    {
        var result = await MyScheduleActivities.ToDataSourceResultAsync(args.Request);
        args.Data = result.Data;
        args.Total = result.Total;
    }
}

