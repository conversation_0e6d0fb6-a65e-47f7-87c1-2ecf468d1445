﻿@page "/openschedules"
@inject SubdivisionService SubdivisionService
@inject ScheduleService ScheduleService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject PoService PoService
@inject BudgetService BudgetService
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector, ConstructionManager")]
@implements IDisposable
@using ERP.Data.Models.Dto
@using ERP.Data.Models
@using Telerik.DataSource.Extensions
@using System.Collections.ObjectModel

<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }
    /*.mytreeclass .k-drag-cell {
            display:none;
        }*/
/*    .mytreeclass .k-table-th.k-header.k-drag-cell{
        visibility: collapse;
    }
    .mytreeclass .k-drag-cell.k-touch-action-none{
        visibility: collapse;

    }*/
    .mytreeclass .k-drag-col{
        visibility: collapse;
    }
    .mytreeclass .k-header {
        min-width: 200px;
    }

    .mytreeclass  {
       overflow-x: auto;
    }
    .k-drag-clue {
        color: white;
    }

    .k-icon.k-icon-with-modifier {
        display: none;
    }

    .SubdivisionTable .k-hierarchy-cell {
        display: none;
    }
    
    .k-splitbar {
        width: 15px;
        color: black;
        padding-left:5px;
        padding-right:5px;
    }

    .pane {
        padding: 8px;
        overflow-x: auto;
    }

    .k-splitter .k-pane {
        overflow-x: auto !important;
    }

    .k-splitter-horizontal{
        overflow-x: auto !important;
    }
    .page-wrapper .page-content{     
       /* padding-right: 100px !important;
        margin-right: 100px !important;*/ 
/*        width: 95% !important;*/
    }
    .page-wrapper{
       /* padding-right: 100px !important;
        margin-right: 100px !important;
            width: 80% !important;*/
    }
    .k-table-td{
        padding: 4px !important;
        font-size: 11px;
    }

    .k-table-td { /*these settings are to prevent the 0 width columns from affecting the column height*/
        padding: 4px !important;
        font-size: 11px;
        height: 30px !important;
        white-space: nowrap !important; 
        overflow: visible;
    }

    .negativeValuesRowFormatting {
        background-color: #ffd6d6 !important;
    }

    .positiveValuesRowFormatting {
        background-color: #d7f5e3 !important;
    }
</style>
<TelerikMediaQuery Media="@SmallScreenMediaQuery" OnChange="@((doesMatch) => IsSmallScreen = doesMatch)"></TelerikMediaQuery>
<TelerikMediaQuery Media="@LargeScreenMediaQuery" OnChange="@((doesMatch) => IsLargeScreen = doesMatch)"></TelerikMediaQuery>
<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="false" ShowJob="false" ShowSubdivision="false" @ref="subdivisionJobPickerMenuBar"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
   <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Open Schedules</h7>
                </div>
            </div>
<TelerikTooltip TargetSelector=".tooltip-target" />
@if (displaySuccessStatus)
{
    if (isError == false)
    {
        <div class="alert alert-success" role="alert">
            @displaySuccessMessage
        </div>
    }
    else if (isError == true)
    {
        <div class="alert alert-danger" role="alert">
            @displaySuccessMessage
        </div>
    }
}
@if (IsLargeScreen)
{
    <div class="row">
        <TelerikGrid Data="@AllOpenSchedules"
                     FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                     Height="1000px" RowHeight="60" PageSize="50"
                     Pageable = "true"
                     Sortable="true"
                     Resizable="true"
                     Reorderable="true"
                     Groupable="true"
                     OnStateInit="@( (GridStateEventArgs<ScheduleDto> args) => OnGridStateInit(args) )"
                     OnRowRender="@OnRowRenderHandler"
                     SelectionMode="GridSelectionMode.Single"
                     ConfirmDelete="true">
            <GridColumns>
                <GridColumn Field="JobNumberNavigation.Subdivision.SubdivisionName" Title="Subdivision" Editable="false" Groupable="true" FilterMenuType="@FilterMenuType.CheckBoxList" />
                <GridColumn Field="JobNumber" Title="Job" Editable="false" Groupable="true" />
                <GridColumn Field="JobNumberNavigation.LotNumber" Title="Lot" Editable="false" Groupable="true"/>
                <GridColumn Field="JobNumberNavigation.JobAddress1" Title="Address" Editable="false" Groupable="true" />
               @*  <GridCommandColumn Context="dataItem" Width="120px">
                    @{
                        var item = dataItem as ScheduleDto;
                        <a href=@($"/lotdetails/{item.JobNumber}") class="btn btn-outline-primary">Job Details</a>
                    }
                </GridCommandColumn> *@
                
                <GridCommandColumn>
                    <GridCommandButton Class="tooltip-target k-button-success" Title="Job Details" OnClick="SelectJobDetails"  Command="Details">Job Details</GridCommandButton>
                    <GridCommandButton Class="tooltip-target k-button-success" Title="Schedule" OnClick = "SelectSchedule" Command="Schedule">Schedule</GridCommandButton>
                </GridCommandColumn>
            </GridColumns>
            <GridToolBarTemplate>
                <GridSearchBox DebounceDelay="200"></GridSearchBox>
                <TelerikToggleButton Class="tooltip-target" Title="Filter My Schedules" @bind-Selected="@FilterMySchedules" OnClick="ToggleFilter">All Open/My Schedules: <strong>@ToggleText</strong></TelerikToggleButton>
            </GridToolBarTemplate>
        </TelerikGrid>
        
        <br />
        <br />
    </div>
}
else
{
    //SMALL SCREEN
  <div class="row">
        <TelerikGrid Data="@AllOpenSchedules"
                     FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                     Height="1000px" RowHeight="60" PageSize="20"
                     ScrollMode="@GridScrollMode.Virtual"
                     Sortable="true"
                     Resizable="true"
                     Reorderable="true"
                     Groupable="false"
                     OnRowRender="@OnRowRenderHandler"
                     SelectionMode="GridSelectionMode.Single"
                     ConfirmDelete="true">
            <GridColumns>
                <GridColumn Field="JobNumber" Title="Job" Editable="false" Groupable="true" />
                <GridColumn Field="JobNumberNavigation.LotNumber" Title="Lot" Editable="false" Groupable="true" Width="75px" />
                <GridCommandColumn>
                    <GridCommandButton Class="tooltip-target k-button-success" Title="Job Details" OnClick="SelectJobDetails"  Command="Details">Job Details</GridCommandButton>
                    <span class="d-block d-sm-none mt-1"></span>
                    <GridCommandButton Class="tooltip-target k-button-success" Title="Schedule" OnClick = "SelectSchedule" Command="Schedule">Schedule</GridCommandButton>
                </GridCommandColumn>
            </GridColumns>
            <GridToolBarTemplate>
                <GridSearchBox DebounceDelay="200"></GridSearchBox>
                <TelerikToggleButton Class="tooltip-target" Title="Filter My Schedules" @bind-Selected="@FilterMySchedules" OnClick="ToggleFilter">All Open/My Schedules: <strong>@ToggleText</strong></TelerikToggleButton>
            </GridToolBarTemplate>
        </TelerikGrid>
        
        <br />
        <br />
    </div>

}

@code {

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }


    public SubdivisionJobPickerMenuBar? subdivisionJobPickerMenuBar { get; set; }

    private bool IsSmallScreen { get; set; }
    private bool IsLargeScreen { get; set; }
    private string SmallScreenMediaQuery { get; set; } = "(max-width: 767px)";
    private string LargeScreenMediaQuery { get; set; } = "(min-width: 1199px)";

    private string ToggleText { get; set; } = "My Schedules";
    private bool FilterMySchedules { get; set; } = false;

    // Success/Error
    private bool displaySuccessStatus = false;
    private string? displaySuccessMessage;
    private bool isError = false;

    public List<ScheduleDto>? AllOpenSchedules { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        SubdivisionJobPickService.OnChanged += JobChangedHandler;
        var openSchedulesTask = ScheduleService.GetSchedulesAsync(true);
        var schedulesByContact = ScheduleService.GetSchedulesByContactAsync(userName, true);
        await Task.WhenAll(new Task[]{openSchedulesTask, schedulesByContact});
        AllOpenSchedules = schedulesByContact.Result.Value;
        var contactSchedules = schedulesByContact.Result.Value;
       
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userroleConstructionDirector = user.User.IsInRole("ConstructionDirector");

    }
    private async Task OnGridStateInit(GridStateEventArgs<ScheduleDto> args)
    {
        args.GridState.GroupDescriptors.Add(new GroupDescriptor
            {
                Member = "JobNumberNavigation.Subdivision.SubdivisionName",
                MemberType = typeof(string)
            });
    }
    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= JobChangedHandler;
    }
    async Task ToggleFilter()
    {
        if (FilterMySchedules == true)
        {
            var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userName = user.User.Identity.Name.Split('@')[0];
            var schedulesByContact = await ScheduleService.GetSchedulesByContactAsync(userName, true);
            AllOpenSchedules = schedulesByContact.Value;
            ToggleText = "My Schedules Only";
        }
        else
        {
            var openSchedulesTask = await ScheduleService.GetSchedulesAsync(true);
            AllOpenSchedules = openSchedulesTask.Value;
            ToggleText = "All Open";
        }
    }
    async Task SelectSchedule(GridCommandEventArgs args)
    {
        var selectedSchedule = args.Item as ScheduleDto;
        if(selectedSchedule != null)
        {
            SubdivisionJobPickService.JobNumber = selectedSchedule.JobNumber;
            subdivisionJobPickerMenuBar.StoreSubdivJobSupplier();
            NavManager.NavigateTo($"schedule");
            StateHasChanged();
        }
        //var selected = SubdivisionJobPickService.JobNumber;
    }
    async Task SelectJobDetails(GridCommandEventArgs args)
    {
        var selectedSchedule = args.Item as ScheduleDto;
        if (selectedSchedule != null)
        {
            SubdivisionJobPickService.JobNumber = selectedSchedule.JobNumber;
            subdivisionJobPickerMenuBar.StoreSubdivJobSupplier();
            NavManager.NavigateTo($"lotdetails/{selectedSchedule.JobNumber}");
            StateHasChanged();
        }
        //var selected = SubdivisionJobPickService.JobNumber;
    }
    async Task JobChangedHandler()
    {
        var selected = SubdivisionJobPickService.JobNumber;       
    }      
    void OnRowRenderHandler(GridRowRenderEventArgs args)
    {
        ScheduleDto item = args.Item as ScheduleDto;
       
        args.Class =  item.DateToEnd <= item.BaseEndDate  ? "positiveValuesRowFormatting" : "negativeValuesRowFormatting";
    }

}
