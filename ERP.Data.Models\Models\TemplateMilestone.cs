﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class TemplateMilestone
{
    public int TemplateMid { get; set; }

    public int TemplateId { get; set; }

    public int MilestoneId { get; set; }

    public int? Seq { get; set; }

    public DateTime Createddatetime { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public string? UpdatedBy { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual Milestone Milestone { get; set; } = null!;

    public virtual Template Template { get; set; } = null!;

    public virtual ICollection<TemplateSactivity> TemplateSactivities { get; set; } = new List<TemplateSactivity>();
}
