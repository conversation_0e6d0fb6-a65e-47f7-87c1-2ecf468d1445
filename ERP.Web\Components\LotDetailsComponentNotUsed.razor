﻿@using ERP.Data.Models.Dto;
@inject SubdivisionService SubdivisionService
@inject NavigationManager NavManager

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="80%"
               Height="80%"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Lot Details - @lotDetails?.JobNumber
    </WindowTitle>

    <WindowContent>
        <ErrorBoundary>
            <ChildContent>        
            <h3>Lot Details for lot @lotDetails.JobNumber</h3>
        <EditForm Model="@lotDetails" OnValidSubmit="@HandleValidSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />
                    <TelerikTabStrip ActiveTabIndex="@ActiveTabIndex" ActiveTabIndexChanged="@TabChangedHandler">
                <TabStripTab Title="Lot Details">
                    <h6>Job Details</h6>
                    <p>Job Number:&nbsp; @lotDetails?.JobNumber</p>
                    <p>Lot Number: @lotDetails?.LotNumber</p>
                    <p>Job Address: @lotDetails?.JobAddress1</p>
                    <p>Job Address2: @lotDetails?.JobAddress2</p>
                    <p>Job City: @lotDetails?.JobCity</p>
                    <p>Job State: @lotDetails?.JobState</p>
                    <p>Job Zip: @lotDetails?.JobZipCode</p>
                    <p>Job County: @lotDetails?.JobCounty</p>
                    <p>Plan Name: @lotDetails?.PlanName</p>
                </TabStripTab>
                <TabStripTab Title="More Lot Details">
                    <h6>More Job Details </h6>
                    <p><label>LotAvailability </label><InputNumber @bind-Value="lotDetails.LotAvailability"></InputNumber></p>
                    <p><label>LotCost </label><InputNumber @bind-Value="lotDetails.LotCost"></InputNumber></p>
                    <p><label>Job Posting Group </label><InputText @bind-Value="lotDetails.JobPostingGroup"></InputText></p>
                    <p><label>LotPremium </label><InputNumber @bind-Value="lotDetails.LotPremium"></InputNumber></p>
                    <p><label>LotPremium </label><InputNumber @bind-Value="lotDetails.LotPremium"></InputNumber></p>
                    <p><label>LotStatus </label><InputNumber @bind-Value="lotDetails.LotStatus"></InputNumber></p>
                    <p><label>LotWidth </label><InputText @bind-Value="lotDetails.LotWidth"></InputText></p>
                    <p><label>LotSwing </label><InputText @bind-Value="lotDetails.LotSwing"></InputText></p>
                    <p><label>Model </label><InputText @bind-Value="lotDetails.ModelName"></InputText></p>
                    <p><label>Plan Name </label><InputText @bind-Value="lotDetails.PlanName"></InputText></p>
                    <p><label>Plan Code </label><InputText @bind-Value="lotDetails.PlanCode"></InputText></p>

                </TabStripTab>
                <TabStripTab Title="Third Tab of Details">
                    <div>
                        <div class="row">
                            <div class="col-md-4">
                                <h6>Site Contacts</h6>
                                <p><label>Supervisor </label><InputText @bind-Value="lotDetails.Supervisor"></InputText></p>
                                <p><label>Project Manager </label><InputText @bind-Value="lotDetails.ProjectMgr"></InputText></p>
                                <p><label>Field Super </label><InputText @bind-Value="lotDetails.FieldSuper"></InputText></p>
                                <p><label>Sales Contact </label><InputText @bind-Value="lotDetails.SalesContact"></InputText></p>
                                <p><label>User Contact 1 </label><InputText @bind-Value="lotDetails.UserContact1"></InputText></p>
                                <p><label>User Contact 2 </label><InputText @bind-Value="lotDetails.UserContact2"></InputText></p>
                            </div>

                            <div class="col-md-8">
                                <p>Apply these contacts to other lots? TODO: make possible to select from other subdivisions as well</p>
                                <TelerikMultiSelect
                                    Context="multiSelectContext"
                                    @ref="MultiSelectRef"
                                    Data="@AvailableJobs"
                                    @bind-Value="@SelectedJobs"
                                    AutoClose="false"
                                    Placeholder="Select the jobs to apply the same site contacts">
                                    <HeaderTemplate>
                                         <label style="padding: 4px 8px;">
                                          <TelerikCheckBox TValue="bool"
                                            Value="@IsAllSelected()"
                                            ValueChanged="@( (bool v) => ToggleSelectAll(v) )">
                                            </TelerikCheckBox>
                                             &nbsp;Select All
                                        </label>
                                    </HeaderTemplate>
                                    <ItemTemplate> 
                                            <input type="checkbox"
                                               class="k-checkbox k-checkbox-md"
                                               checked="@GetChecked(multiSelectContext)">                                               
                                        @multiSelectContext
                                    </ItemTemplate>
                                </TelerikMultiSelect>

                            </div>
                        </div>
                    </div>
                </TabStripTab>
            </TelerikTabStrip>
            <button type="submit" class="btn btn-primary">Update</button>
            </EditForm>

            </ChildContent>
            <ErrorContent>
                <div class="alert alert-danger">
                    Oops! There's an issue with this Lot
                </div>
            </ErrorContent>
        </ErrorBoundary>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {
    [Parameter]
    public string JobNumber { get; set; }
    public bool IsModalVisible { get; set; }
    public JobDto lotDetails { get; set; } = new JobDto();
    public List<JobSelected>? JobsThisSubdivision { get; set; } = new List<JobSelected>();
    public List<JobDto> SelectLots { get; set; } = new List<JobDto>();
    public string? SelectedLot { get; set; }
    public bool SelectAllThisSubdivision { get; set; } = false;
    public int ActiveTabIndex { get; set; }
    private TelerikMultiSelect<string, string> MultiSelectRef;
    private List<string> SelectedJobs { get; set; } = new List<string>();
    private List<string> AvailableJobs { get; set; } = new List<string>();


    public async Task Show()
    {
        IsModalVisible = true;
    }
    public void Hide()
    {
        IsModalVisible = false;
    }
    public class JobSelected
    {
        public bool Selected { get; set; }
        public string JobNumber { get; set; }
    }
    void TabChangedHandler(int newIndex)
    {
        ActiveTabIndex = newIndex;
    }
    protected override async Task OnParametersSetAsync()
    {
        if(JobNumber != null)
        {
            lotDetails = (await SubdivisionService.GetLotAsync(JobNumber)).Value;
        }     
        if (lotDetails != null)
        {
            SelectLots = (await SubdivisionService.GetAllJobsAsync()).Value.Select(x => new JobDto() { JobNumber = x.JobNumber, LotNumber = x.LotNumber }).ToList();
            JobsThisSubdivision = (await SubdivisionService.GetJobsAsync(lotDetails.SubdivisionId ?? 0)).Value.Select(x => new JobSelected() { JobNumber = x.JobNumber, Selected = false }).ToList();
            AvailableJobs = JobsThisSubdivision.Select(x => x.JobNumber).ToList();
        }
    }

    private void SelectAllHandler()
    {
        if (SelectAllThisSubdivision == false)
        {
            foreach (var job in JobsThisSubdivision)
            {
                job.Selected = true;
            }
        }
        else
        {
            foreach (var job in JobsThisSubdivision)
            {
                job.Selected = false;
            }
        }
    }

    private async void HandleValidSubmit()
    {
        var selectedJobs = JobsThisSubdivision.Where(x => x.Selected == true).ToList();
        await SubdivisionService.UpdateLotAsync(lotDetails);
        if (SelectedJobs.Count > 0)
        {
            await SubdivisionService.UpdateLotsAsync(lotDetails, SelectedJobs);
        }
    }


    //switch to different lot
    public async void OnChangeHandler(object theUserChoice)
    {
        string selectedLot = (string)theUserChoice;
        if (string.IsNullOrWhiteSpace(selectedLot))
        {
            return;
        }
        NavManager.NavigateTo($"lotdetails/{selectedLot}");
        StateHasChanged();
    }
    public async void LotSelected(string selectedLot)
    {
        if (string.IsNullOrWhiteSpace(selectedLot))
        {
            return;
        }
        SelectedLot = selectedLot;
    }


    void ToggleSelectAll(bool selectAll)
    {
        SelectedJobs.Clear();

        if (selectAll)
        {
            SelectedJobs.AddRange(AvailableJobs);
        }

        MultiSelectRef.Rebind();
    }

    bool IsAllSelected()
    {
        return SelectedJobs.Count == AvailableJobs.Count;
    }

    // for the item checkboxes
    bool GetChecked(string text)
    {
        return SelectedJobs.Contains(text);
    }
}
