﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ScheduleMaster
{
    public int ScheduleMasterId { get; set; }

    public string? ScheduleMasterDesc { get; set; }

    public int? DivId { get; set; }

    public string? UserCreated { get; set; }

    public DateTime? DateCreated { get; set; }

    public string? UserModified { get; set; }

    public DateTime? DateModified { get; set; }

    public DateTime? DateToStart { get; set; }

    public DateTime? DateToEnd { get; set; }

    public DateTime? BaseStartDate { get; set; }

    public DateTime? BaseEndDate { get; set; }

    public DateTime? ProjStartDate { get; set; }

    public DateTime? ProjEndDate { get; set; }

    public DateTime? ActualStartDate { get; set; }

    public DateTime? ActualEndDate { get; set; }

    public int? PlusminusDays { get; set; }

    public int? BaseDuration { get; set; }

    public int? ProjDuration { get; set; }

    public int? BaseCalduration { get; set; }

    public int? ProjCalduration { get; set; }

    public string? Published { get; set; }

    public int? ActualDuration { get; set; }

    public string? Autocreateworkareas { get; set; }

    public string? Autocreatephases { get; set; }

    public int? ComputingProjected { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<ScheduleArea> ScheduleAreas { get; set; } = new List<ScheduleArea>();

    public virtual ICollection<ScheduleChain> ScheduleChains { get; set; } = new List<ScheduleChain>();

    public virtual ICollection<ScheduleMasterPred> ScheduleMasterPreds { get; set; } = new List<ScheduleMasterPred>();
}
