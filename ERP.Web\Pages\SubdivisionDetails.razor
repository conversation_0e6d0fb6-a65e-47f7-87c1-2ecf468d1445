﻿ @page "/subdivisiondetails/{subdivisionid:int}"
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector, ConstructionManager")]
@using ERP.Data.Models.Dto;
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject PlanService PlanService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
<style>
    .k-button-danger {
        border-color: #e6a7a7;
        color: #000000;
        background-color: #e6a7a7;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>

<PageTitle>Subdivision Details | @subdivisionDetails?.SubdivisionName</PageTitle>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<div class="col-lg-12">
    <div class="card" style="background-color: #2e5771">
        <div class="card-body" style="padding:0.5rem">
            <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Subdivision Details For: @subdivisionDetails?.SubdivisionName</h7>
        </div>
    </div>
</div>

<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="/">Home</a></li>
    <li class="breadcrumb-item"><a href="/subdivisions">Subdivisions</a></li>
    <li class="breadcrumb-item active">Details</li>
</ol>

<ErrorBoundary>
    <ChildContent>
        <div class="card col-3">
            <div class="card-header">
                <h7 class="page-title" style="font-weight:bold">View Lots</h7>
            </div>
            <div class="card-body">
                <TelerikDropDownList Data="@JobsThisSubdivision"
                @bind-Value="@SelectedJobNum"
                                     TextField="JobNumber"
                                     ValueField="JobNumber"
                                     DefaultText="Select A Job"
                                     ScrollMode="@DropDownScrollMode.Virtual"
                                     PageSize="10"
                                     Filterable="true"
                                     ItemHeight="35"
                                     OnChange="@OnJobSelected"
                                     FilterOperator="@StringFilterOperator.Contains">
                    <DropDownListSettings>
                        <DropDownListPopupSettings Height="350px"></DropDownListPopupSettings>
                    </DropDownListSettings>
                </TelerikDropDownList>
            </div>
        </div>
        <TelerikTabStrip ActiveTabIndex="@ActiveTabIndex" ActiveTabIndexChanged="@TabChangedHandler">
            <TabStripTab Title="Subdivision Details">
                <div class="card-body">
                    <div class="row">
                        <div class="col-4">
                            <div class="mb-3 row">
                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Subdivision Number:</label>
                                <div class="col-sm-8">
                                    <TelerikTextBox Enabled="false" @bind-Value="subdivisionDetails.SubdivisionNum" Width="200px"></TelerikTextBox>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Subdivision Name:</label>
                                <div class="col-sm-8">
                                    <TelerikTextBox Enabled="false" @bind-Value="subdivisionDetails.SubdivisionName" Width="300px"></TelerikTextBox>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Marketing Name:</label>
                                <div class="col-sm-8">
                                    <TelerikTextBox @bind-Value="subdivisionDetails.MarketingName" Width="300px"></TelerikTextBox>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Entity Name:</label>
                                <div class="col-sm-8">
                                    <TelerikTextBox @bind-Value="subdivisionDetails.EntityName" Enabled="false" Width="300px"></TelerikTextBox>
                                </div>
                            </div> 
                            <div class="mb-3 row">
                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Entity Name 2:</label>
                                <div class="col-sm-8">
                                    <TelerikTextArea @bind-Value="subdivisionDetails.EntityName2" Width="300px"></TelerikTextArea>
                                </div>
                            </div> 
                            <div class="mb-3 row">
                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Entity Signature Block:</label>
                                <div class="col-sm-8">
                                    <TelerikTextArea @bind-Value="subdivisionDetails.EntitySignatureBlock" Width="300px"></TelerikTextArea>
                                </div>
                            </div>
                            
                            
                            @*                                <div class="mb-3 row">
                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Status:</label>
                            <div class="col-sm-8">
                            <TelerikTextBox @bind-Value="subdivisionDetails.SubdivisionStatus" Width="200px"></TelerikTextBox>
                            </div>
                            </div>*@
                            @*                                <div class="mb-3 row">
                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Address:</label>
                            <div class="col-sm-8">
                            <TelerikTextBox @bind-Value="subdivisionDetails.Address1" Width="200px"></TelerikTextBox>
                            </div>
                            </div>
                            <div class="mb-3 row">
                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Address2:</label>
                            <div class="col-sm-8">
                            <TelerikTextBox @bind-Value="subdivisionDetails.Address2" Width="200px"></TelerikTextBox>
                            </div>
                            </div>*@

                        </div>
                        <div class="col-3">
                            <div class="mb-3 row">
                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">City:</label>
                                <div class="col-sm-8">
                                    <TelerikTextBox @bind-Value="subdivisionDetails.City" Width="150px"></TelerikTextBox>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">State:</label>
                                <div class="col-sm-8">
                                    <TelerikTextBox @bind-Value="subdivisionDetails.State" Width="150px"></TelerikTextBox>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Zip:</label>
                                <div class="col-sm-8">
                                    <TelerikTextBox @bind-Value="subdivisionDetails.Zip" Width="150px"></TelerikTextBox>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">County:</label>
                                <div class="col-sm-8">
                                    <TelerikTextBox @bind-Value="subdivisionDetails.County" Width="150px"></TelerikTextBox>
                                </div>
                            </div> 
                            <div class="mb-3 row">
                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Active Adult:</label>
                                <div class="col-sm-8">
                                    <TelerikCheckBox @bind-Value="subdivisionDetails.IsActiveAdult"></TelerikCheckBox>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Blocked:</label>
                                <div class="col-sm-8">
                                    <TelerikCheckBox Enabled="false" @bind-Value="subdivisionDetails.BoolBlocked"></TelerikCheckBox>
                                </div>
                            </div>
                            @*                                <div class="mb-3 row">
                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Email:</label>
                            <div class="col-sm-8">
                            <TelerikTextBox @bind-Value="subdivisionDetails.Email" Width="200px"></TelerikTextBox>
                            </div>
                            </div>
                            <div class="mb-3 row">
                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Phone:</label>
                            <div class="col-sm-8">
                            <TelerikTextBox @bind-Value="subdivisionDetails.Phone" Width="200px"></TelerikTextBox>
                            </div>
                            </div>*@
                            @*<div class="mb-3 row">
                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Fax:</label>
                            <div class="col-sm-8">
                            <TelerikTextBox @bind-Value="subdivisionDetails.Fax" Width="200px"></TelerikTextBox>
                            </div>
                            </div>*@
                        </div>
                       @*  <div class="col-5">
                            <div class="mb-3 row">
                                <label>Phase/ Building num/ Stick num for all jobs in this subdivision</label>
                            </div>
                            <TelerikGrid @ref="@JobDetailsGridRef"
                                         Data="JobsThisSubdivision"
                                         SelectionMode="@GridSelectionMode.Multiple"
                                         @bind-SelectedItems="@SelectedJobs"
                                         Groupable="true"
                                         FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                         FilterMenuType="Telerik.Blazor.FilterMenuType.CheckBoxList"
                                         Height="400px">
                                <GridToolBarTemplate>
                                    <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                    <TelerikButton OnClick="UpdateSelectedJobs">Update Selected Jobs</TelerikButton>
                                </GridToolBarTemplate>
                                <GridColumns>
                                    <GridCheckboxColumn SelectAll="true" SelectAllMode="GridSelectAllMode.All" />
                                    <GridColumn Field="JobNumber" Title="Job Number" Filterable="false" Groupable="false" />
                                    <GridColumn Field="Phase" Title="Phase" />
                                    <GridColumn Field="BuildingNum" Title="Building Number" />
                                    <GridColumn Field="StickBuilingNum" Title="Stick Number" />
                                </GridColumns>
                            </TelerikGrid>
                        </div> *@
                    </div>
                    @if(AllowEdit)
                    {
                        <div class="mb-3 row">
                            <div class="col-1"></div>
                            <div class="col">
                                <button type="button" @onclick="HandleValidSubmit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                                    <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                                        <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                            <!--!-->
                                            <!--!-->
                                            <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                                        </svg>
                                    </span> Update
                                </button>
                            </div>
                        </div>
                    }
                </div>
            </TabStripTab>
            @*<TabStripTab Title="More Subdivision Details">
            <div class="card-body">
            <div class="row">
            <div class="col-lg-6">
            <div class="mb-3 row">
            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">EstHazardInsureValue:</label>
            <div class="col-sm-8">
            <TelerikNumericTextBox @bind-Value="subdivisionDetails.EstHazardInsurValue" Width="200px"></TelerikNumericTextBox>
            </div>
            </div>
            <div class="mb-3 row">
            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Setup Fees:</label>
            <div class="col-sm-8">
            <TelerikNumericTextBox @bind-Value="subdivisionDetails.SetupFee" Width="200px"></TelerikNumericTextBox>
            </div>
            </div>
            <div class="mb-3 row">
            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">HOA Fees:</label>
            <div class="col-sm-8">
            <TelerikNumericTextBox @bind-Value="subdivisionDetails.HoaFees" Width="200px"></TelerikNumericTextBox>
            </div>
            </div>
            <div class="mb-3 row">
            <div class="col-sm-12">
            <button type="submit" class="btn btn-primary">Update</button>
            </div>
            </div>
            </div>
            <div class="col-lg-6">
            <div class="mb-3 row">
            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">HOA Name:</label>
            <div class="col-sm-8">
            <TelerikTextBox @bind-Value="subdivisionDetails.HoaName" Width="200px"></TelerikTextBox>
            </div>
            </div>
            <div class="mb-3 row">
            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Acreage:</label>
            <div class="col-sm-8">
            <TelerikNumericTextBox @bind-Value="subdivisionDetails.Acreage" Width="200px"></TelerikNumericTextBox>
            </div>
            </div>
            </div>
            </div>
            </div>
            </TabStripTab>*@
            <TabStripTab Title="Plans">
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-5">
                            <div class="card-header" style="padding:4px; margin-bottom:4px">
                                <div style="text-align:center">
                                    <h7 class="page-title" style="font-weight:bold">Available Plans</h7>
                                </div>
                            </div>
                            @if (AvailablePlansThisSubdiv != null)
                            {
                                <TelerikGrid Data=@AvailablePlansThisSubdiv
                                             Groupable="true"
                                             FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                             Height="1000px" RowHeight="60" PageSize="20"
                                             Sortable="true"
                                             Resizable="true"
                                             SelectionMode="GridSelectionMode.Single"
                                             EditMode="@GridEditMode.Popup"
                                             OnRowClick="@OnPlanRowClickHandler"
                                             OnUpdate="@UpdatePlanHandler"
                                             OnEdit="@EditPlanHandler"
                                             OnDelete="@DeletePlanHandler"
                                             OnCancel="@CancelPlanHandler"
                                             ConfirmDelete="true"
                                             @ref="@PlanGridRef">
                                    <GridToolBarTemplate>
                                        @if(AllowEdit)
                                        {
                                            <GridCommandButton Command="AddPlanCommand" Icon="@FontIcon.Plus" OnClick="@AddPlanFromToolbar" Class="k-button-add">Add Plan From Master</GridCommandButton>
                                            <GridCommandButton Command="CopyPlanCommand" Icon="@FontIcon.Plus" OnClick="@CopyPlanFromToolbar" Class="k-button-success">Copy Plan From Subdivision</GridCommandButton>
                                        }
                                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                    </GridToolBarTemplate>
                                    <GridColumns>
                                        <GridColumn Field="MasterPlan.PlanName" Title="Plan Name" Editable="true" Groupable="false" />
                                        <GridColumn Field="MasterPlan.PlanNum" Title="Plan Number" Editable="true" Groupable="false" />
                                        <GridColumn Field="PlanTypeDescription" Title="Plan Type" Editable="false" Groupable="false" />
                                        @*<GridColumn Field="MasterPlan.PlanTypeId" Title="Plan Type" Editable="true" Groupable="false" Width="0">
                                    <EditorTemplate Context="availablePlanTypes">
                                    @{
                                    CurrentPlanType = availablePlanTypes as PhasePlanModel;

                                    <TelerikDropDownList Data="@ListofPlanTypes" DefaultText="Select Type"
                                    @bind-Value="@CurrentPlanType.MasterPlan.PlanTypeId"
                                    TextField="@nameof(CurrentPlanType.MasterPlan.Description)" ValueField="@nameof(CurrentPlanType.PlanTypeId)"
                                    Width="100%">
                                    <DropDownListSettings>
                                    <DropDownListPopupSettings Height="400px" />
                                    </DropDownListSettings>
                                    </TelerikDropDownList>
                                    }
                                    </EditorTemplate>
                                    </GridColumn>*@
                                        <GridColumn Field="MasterPlan.PlanSize" Title="Model Size" Editable="false" Groupable="false" Width="0" />
                                        <GridColumn Field="MasterPlan.MasterPlanId" Visible="false" Editable="false" Groupable="false" />
                                        <GridColumn Field="PhasePlanId" Visible="false" Editable="false" Groupable="false" />
                                        <GridColumn Field="Phase" Visible="true" Editable="false" Groupable="true" />
                                        <GridCommandColumn Context="gridCommandContext">
                                            @if(AllowEdit)
                                            {
                                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true" Class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                                                    <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                                                        <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                                            <!--!-->
                                                            <!--!-->
                                                            <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                                                        </svg>
                                                    </span> Update
                                                </GridCommandButton>
                                                @*<GridCommandButton Command="Edit" Icon="@FontIcon.Pencil"></GridCommandButton>*@
                                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">
                                                    <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel k-button-danger" aria-hidden="true">
                                                        <!--!-->
                                                        <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                                            <!--!-->
                                                            <!--!-->
                                                            <path d="M256 32c-50.3 0-96.8 16.6-134.1 44.6-17.2 12.8-32.4 28.1-45.3 45.3C48.6 159.2 32 205.7 32 256c0 123.7 100.3 224 224 224 50.3 0 96.8-16.6 134.1-44.6 17.2-12.8 32.4-28.1 45.3-45.3 28-37.4 44.6-83.8 44.6-134.1 0-123.7-100.3-224-224-224zm0 384c-88.2 0-160-71.8-160-160 0-32.6 9.8-62.9 26.6-88.2l221.6 221.6C318.9 406.2 288.6 416 256 416zm133.4-71.8L167.8 122.6C193.1 105.8 223.4 96 256 96c88.2 0 160 71.8 160 160 0 32.6-9.8 62.9-26.6 88.2z"></path><!--!-->
                                                        </svg><!--!-->
                                                        <!--!-->
                                                    </span> Cancel
                                                </GridCommandButton>
                                            }
                                        </GridCommandColumn>
                                    </GridColumns>
                                    <GridSettings>
                                        <GridPopupEditSettings Width="400px"
                                                               Height="500px"
                                                               Title="Plan">
                                        </GridPopupEditSettings>
                                    </GridSettings>
                                </TelerikGrid>

                            }
                        </div>
                        <div class="col-lg-7">
                            <div class="card-header" style="padding:4px; margin-bottom:4px">
                                <div style="text-align:center">
                                    <h7 class="page-title" style="font-weight:bold">Options in Selected Plan: @SelectedPlan?.MasterPlan.PlanName</h7>
                                </div>
                            </div>
                            @if (OptionData == null)
                            {
                                <p><em>Select a plan to see options</em></p>
                                <div style=@loadingOptionStyle>Loading...</div>
                            }

                            else
                            {
                                <TelerikGrid Data=@OptionData
                                             FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                             Height="1000px" RowHeight="60" PageSize="20"
                                             Sortable="true"
                                             Resizable="true"
                                             Groupable="true"
                                             Context="optionContext"
                                             SelectionMode="GridSelectionMode.Single"
                                             EditMode="@GridEditMode.Popup"
                                             OnUpdate="@UpdateOptionHandler"
                                             OnEdit="@EditOptionHandler"
                                             OnDelete="@DeleteOptionHandler"
                                             OnCancel="@CancelOptionHandler"
                                             ConfirmDelete="true"
                                             @ref="@OptionGridRef">
                                    <GridColumns>
                                        <GridColumn Field="DisplayOptionCodeWithPlanNum" Title="Option Code" Editable="false" Groupable="false" />
                                        <GridColumn Field="ModifiedOptionDesc" Title="Description" Editable="true" Groupable="false" />
                                        <GridColumn Field="OptionGroup.OptionGroupName" Title="Option Group" Editable="true" Groupable="true">
                                            <EditorTemplate Context="availableOptionGroup">
                                                @{
                                                    CurrentOptionGroupName = availableOptionGroup as AvailablePlanOptionDto;

                                                    <TelerikDropDownList Data="@ListofOptionGroups" DefaultText="Select Group"
                                                                         @bind-Value="@CurrentOptionGroupName.OptionGroupId"
                                                                         TextField="@nameof(CurrentOptionGroupName.OptionGroup.OptionGroupName)" ValueField="@nameof(CurrentOptionGroupName.OptionGroup.OptionGroupId)"
                                                                         Width="100%">
                                                        <DropDownListSettings>
                                                            <DropDownListPopupSettings Height="400px" />
                                                        </DropDownListSettings>
                                                    </TelerikDropDownList>
                                                }
                                            </EditorTemplate>
                                        </GridColumn>

                                        <GridColumn Field="OptionType.OptionType1" Title="Option Type" Editable="true" Groupable="true" Context="availableOptionGroup">
                                            <EditorTemplate Context="availableOptionType">
                                                @{
                                                    

                                                    <TelerikDropDownList Data="@OptionTypes"
                                                                         TextField="OptionType1"
                                                                         ValueField="OptionTypeId"
                                                                         @bind-Value="@CurrentOptionGroupName.OptionTypeId"
                                                                         Width="100%">
                                                        <DropDownListSettings>
                                                            <DropDownListPopupSettings Height="75px" />
                                                        </DropDownListSettings>
                                                    </TelerikDropDownList>

                                                }
                                            </EditorTemplate>
                                        </GridColumn>

                                        <GridCommandColumn Context="optionGridCommandContext">
                                            @if(AllowEdit)
                                            {
                                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true" Class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                                                    <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                                                        <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                                            <!--!-->
                                                            <!--!-->
                                                            <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                                                        </svg>
                                                    </span> Update
                                                </GridCommandButton>
                                                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-base">
                                                    <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel" aria-hidden="true">
                                                        <!--!-->
                                                        <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                                            <!--!-->
                                                            <!--!-->
                                                            <path d="M256 32c-50.3 0-96.8 16.6-134.1 44.6-17.2 12.8-32.4 28.1-45.3 45.3C48.6 159.2 32 205.7 32 256c0 123.7 100.3 224 224 224 50.3 0 96.8-16.6 134.1-44.6 17.2-12.8 32.4-28.1 45.3-45.3 28-37.4 44.6-83.8 44.6-134.1 0-123.7-100.3-224-224-224zm0 384c-88.2 0-160-71.8-160-160 0-32.6 9.8-62.9 26.6-88.2l221.6 221.6C318.9 406.2 288.6 416 256 416zm133.4-71.8L167.8 122.6C193.1 105.8 223.4 96 256 96c88.2 0 160 71.8 160 160 0 32.6-9.8 62.9-26.6 88.2z"></path><!--!-->
                                                        </svg><!--!-->
                                                        <!--!-->
                                                    </span> Cancel
                                                </GridCommandButton>
                                            }
                                        </GridCommandColumn>
                                    </GridColumns>
                                    <DetailTemplate>
                                        @{
                                            var item = optionContext as AvailablePlanOptionDto;
                                            <p>Code: @item.DisplayOptionCodeWithPlanNum</p>
                                            <p>Desc: @item.ModifiedOptionDesc</p>
                                            <p>OptionGroup: @item.OptionGroup.OptionGroupName</p>
                                            <p>OptionType: @item.OptionTypeNavigation.OptionType1</p>

                                        }
                                    </DetailTemplate>
                                    <GridToolBarTemplate>
                                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                    </GridToolBarTemplate>
                                    <GridSettings>
                                        <GridPopupEditSettings Width="400px"
                                                               Height="400px"
                                                               Title="Option Details">
                                        </GridPopupEditSettings>
                                    </GridSettings>
                                </TelerikGrid>

                            }
                        </div>
                    </div>
                </div>
            </TabStripTab>
            @*<TabStripTab Title="Subdivision Contacts">
            <div class="card-body">
            <div class="row">
            <div class="col-lg-6">
            <TelerikGrid Data=@SubdivisionContacts
            FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
            Height="600px" RowHeight="60" PageSize="20"
            Sortable="true"
            Resizable="true"
            SelectionMode="GridSelectionMode.Single"
            EditMode="@GridEditMode.Popup"
            OnUpdate="@UpdateContactHandler"
            OnEdit="@EditContactHandler"
            OnDelete="@DeleteContactHandler"
            OnCreate="@CreateContactHandler"
            OnCancel="@CancelContactHandler"
            ConfirmDelete="true"
            @ref="@ContactGridRef">
            <GridColumns>
            <GridColumn Field="SubdivisionContactId" Visible="false" Editable="false" Groupable="false" />
            <GridColumn Field="SubdivisionId" Visible="false" Editable="false" Groupable="false" />
            <GridColumn Field="UserId" Visible="true" Editable="false" Groupable="false" />
            <GridColumn Field="FirstName" Title="First Name" Visible="true" Editable="true" Groupable="false" />
            <GridColumn Field="LastName" Title="Last Name" Visible="true" Editable="true" Groupable="false" />
            <GridColumn Field="Email" Title="Email" Visible="true" Editable="true" Groupable="false" />
            <GridColumn Field="IsSiteContact1" Title="IsSiteContact1" EditorType="@GridEditorType.CheckBox" Visible="true" Editable="true" Groupable="false" Width="0" />
            <GridColumn Field="IsSiteContact2" Title="IsSiteContact2" EditorType="@GridEditorType.CheckBox" Visible="true" Editable="true" Groupable="false" Width="0" />
            <GridColumn Field="IsSiteContact3" Title="IsSiteContact3" EditorType="@GridEditorType.CheckBox" Visible="true" Editable="true" Groupable="false" Width="0" />
            <GridColumn Field="IsSiteContact4" Title="IsSiteContact4" EditorType="@GridEditorType.CheckBox" Visible="true" Editable="true" Groupable="false" Width="0" />
            <GridColumn Field="IsSiteContact5" Title="IsSiteContact5" EditorType="@GridEditorType.CheckBox" Visible="true" Editable="true" Groupable="false" Width="0" />
            <GridColumn Field="IsSiteContact6" Title="IsSiteContact6" EditorType="@GridEditorType.CheckBox" Visible="true" Editable="true" Groupable="false" Width="0" />
            <GridCommandColumn Context="gridContext2">
            <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
            <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil"></GridCommandButton>
            <GridCommandButton Command="Delete" Icon="@FontIcon.Trash"></GridCommandButton>
            <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true">Cancel</GridCommandButton>
            </GridCommandColumn>
            </GridColumns>
            <GridToolBarTemplate>
            <GridCommandButton Command="MyAddCommand" Icon="@FontIcon.Plus" OnClick="@AddContactFromToolbar">Add Contact</GridCommandButton>
            <GridSearchBox DebounceDelay="200"></GridSearchBox>
            </GridToolBarTemplate>
            <GridSettings>
            <GridPopupEditSettings Width="600px"
            Height="600px"
            Title="Contact">
            </GridPopupEditSettings>
            </GridSettings>
            </TelerikGrid>
            </div>
            <div class="col-lg-6">
            <div class="mb-6 row">
            <div style="margin-bottom:8px; text-align:left">Apply these contacts to other subdivisions?</div>
            <TelerikMultiSelect Context="multiSelectContext"
            @ref="MultiSelectRef"
            Data="@AvailableSubdivs"
            @bind-Value="@SelectedSubdivs"
            AutoClose="false"
            Placeholder="Select the jobs to apply the same site contacts">
            <HeaderTemplate>
            <label style="padding: 4px 8px;">
            <TelerikCheckBox TValue="bool"
            Value="@IsAllSelected()"
            ValueChanged="@( (bool v) => ToggleSelectAll(v) )">
            </TelerikCheckBox>
            &nbsp;Select All
            </label>
            </HeaderTemplate>
            <ItemTemplate>
            <input type="checkbox"
            class="k-checkbox k-checkbox-md"
            checked="@GetChecked(multiSelectContext)">
            @multiSelectContext
            </ItemTemplate>
            </TelerikMultiSelect>
            </div>
            <br />
            <div>@UpdateMessage</div>
            <button type="button" class="btn btn-primary" @onclick="UpdateSelectedSubdivs">Update Selected Subdivision Contacts</button>
            </div>
            </div>
            </div>
            </TabStripTab>*@
        </TelerikTabStrip>
        <br />
    </ChildContent>
    <ErrorContent>
        <div class="alert alert-danger">
            Oops! There's an issue with this Subdivision
        </div>
    </ErrorContent>
</ErrorBoundary>
<ERP.Web.Components.CopyPlanToSubdivision @ref="CopyPlanModal" SubdivisionId=SubdivisionId HandleAddSubmit="HandleValidCopyPlanSubmit"></ERP.Web.Components.CopyPlanToSubdivision>
<ERP.Web.Components.AddMasterPlanToSubdivision @ref="AddPlanModal" SubdivisionId=SubdivisionId HandleAddSubmit="HandleValidAddPlanSubmit"></ERP.Web.Components.AddMasterPlanToSubdivision>
<ERP.Web.Components.UpdateJobs @ref="UpdateJobsModal" SelectedJobs="SelectedJobs" SubdivisionId=@SubdivisionId HandleUpdateSubmit="HandleValidUpdateSubmit"></ERP.Web.Components.UpdateJobs>
<ERP.Web.Components.AddSubdivisionContact @ref="AddSubdivisionContactModal" SubdivisionId=@SubdivisionId HandleAddSubmit="HandleValidAddContactSubmit"></ERP.Web.Components.AddSubdivisionContact>

@code {

    [Parameter]
    public int SubdivisionId { get; set; }
    public SubdivisionDto subdivisionDetails { get; set; } = new SubdivisionDto();
    public List<string>? Phases { get; set; }
    public List<string>? AllJobPhases { get; set; }
    public List<string>? AllJobBuildingNums { get; set; }
    public List<string>? AllJobSticks { get; set; }
    public List<JobDto>? AllJobs { get; set; } = new List<JobDto>();
    public List<JobDto>? JobsThisSubdivision { get; set; } = new List<JobDto>();
    public List<PhasePlanModel>? AvailablePlansThisSubdiv { get; set; }
    public List<SubdivisionPlanModel>? phasePlans { get; set; }
    public int ActiveTabIndex { get; set; }
    private List<MasterPlanDto>? allPlans { get; set; }
    public int? SelectedPlanNum { get; set; }
    public int? SelectedPhasePlanNum { get; set; }
    public string? SelectedJobNum { get; set; }
    public string pleaseWaitStyle { get; set; } = "display:none";
    public IEnumerable<BreadcrumbItem> BreadCrumbItems = new List<BreadcrumbItem>();
    public PhasePlanModel? SelectedPlan { get; set; }
    private TelerikGrid<PhasePlanModel>? PlanGridRef { get; set; }
    private TelerikGrid<AvailablePlanOptionDto>? OptionGridRef { get; set; }
    private TelerikGrid<JobDto>? JobDetailsGridRef { get; set; }
    public List<AvailablePlanOptionDto>? OptionData { get; set; }
    protected ERP.Web.Components.CopyPlanToSubdivision? CopyPlanModal { get; set; }
    protected ERP.Web.Components.AddMasterPlanToSubdivision? AddPlanModal { get; set; }
    private string loadingOptionStyle = "display:none";
    public List<SubdivisionContactModel>? SubdivisionContacts { get; set; }
    private TelerikGrid<SubdivisionContactModel>? ContactGridRef { get; set; }
    private TelerikMultiSelect<string, string> MultiSelectRef;
    private List<string> SelectedSubdivs { get; set; } = new List<string>();
    private List<string> AvailableSubdivs { get; set; } = new List<string>();
    protected ERP.Web.Components.AddSubdivisionContact? AddSubdivisionContactModal { get; set; }
    protected ERP.Web.Components.UpdateJobs? UpdateJobsModal { get; set; }
    private string UpdateMessage { get; set; } = "";
    public IEnumerable<JobDto> SelectedJobs { get; set; } = new List<JobDto>();
    public bool IsValidSelection { get; set; } = false;
    private bool AllowEdit { get; set; } = true;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    // Dropdown logic
    AvailablePlanOptionDto CurrentOptionGroupName { get; set; }
    List<OptionGroupDto>? ListofOptionGroups;
    List<OptionTypeDto>? OptionTypes;

    // Dropdown logic
    AvailablePlanOptionDto CurrentOptionTypeName { get; set; }
    List<OptionTypeDto>? ListofTypes;

    PhasePlanModel CurrentPlanType { get; set; }
    List<PlanTypeDto>? ListofPlanTypes;
    // End dropdown logic

    protected override async Task OnInitializedAsync()
    {
        pleaseWaitStyle = "display:none";
        var subdivisionDetailsTask = SubdivisionService.GetSubdivisionAsync(SubdivisionId);
        var allJobsTask = SubdivisionService.GetAllJobsAsync();
        var jobsTask = SubdivisionService.GetJobsAsync(SubdivisionId);
        var phasePlanTask = PlanService.GetPhasePlansAsync(SubdivisionId);
        var allPlansTask = PlanService.GetMasterPlansAsync();
        var allPhasePlanTask = PlanService.GetPhasePlansAsync();
        var phasesTask = SubdivisionService.GetSubdivisionPhasesAsync(SubdivisionId);
        var optionsGroupsTask = OptionService.GetDistinctOptionGroupsAsync();
        var planTypesTask = PlanService.GetPlanTypesAsync();
        var optionTypesTask = OptionService.GetOptionTypesAsync();
        await Task.WhenAll(new Task[] { subdivisionDetailsTask, allJobsTask, jobsTask, phasePlanTask, allPlansTask, allPhasePlanTask, phasesTask, optionsGroupsTask, optionTypesTask, planTypesTask });
        subdivisionDetails = subdivisionDetailsTask.Result;
        JobsThisSubdivision = jobsTask.Result.Value;
        AllJobs = allJobsTask.Result.Value;
        var phasePlanResult = phasePlanTask.Result;
        AvailablePlansThisSubdiv = phasePlanResult.Value; // not null as returning empty list in case of failure
        allPlans = allPlansTask.Result.Value;
        phasePlans = allPhasePlanTask.Result.Value;
        Phases = phasesTask.Result;
        //SubdivisionContacts = await SubdivisionService.GetSubdivisionContactsAsync(SubdivisionId);
        // Dropdown logic
        ListofOptionGroups = optionsGroupsTask.Result.Value;
        OptionTypes = optionTypesTask.Result;
        var planTypesResponse = planTypesTask.Result;
        ListofPlanTypes = planTypesResponse.Value ?? new List<PlanTypeDto>();
        // End dropdown logic
        var baseUrl = "~/";
        BreadCrumbItems = new List<BreadcrumbItem>
        {
            new BreadcrumbItem {  Url = "/", Icon = FontIcon.Home },
            new BreadcrumbItem { Text = "Subdivisions", Url = $"Subdivisions" },
            new BreadcrumbItem { Text = "Details", Url = $"SubdivisionDetails" },
        };
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }

    protected async Task OnPlanRowClickHandler(GridRowClickEventArgs args)
    {
        loadingOptionStyle = "";
        OptionData = null;
        SelectedPlan = args.Item as PhasePlanModel;
        OptionData = (await OptionService.GetAvailablePlanOptionsByPlanAsync(SelectedPlan.PhasePlanId)).Value;
        loadingOptionStyle = "display:none";
    }

    void TabChangedHandler(int newIndex)
    {
        ActiveTabIndex = newIndex;
    }

    void OnChangeHandler(object theUserInput)
    {
        // the handler receives an object that you may need to cast to the type of the component
        // if you do not provide a Value, you must provide the Type parameter to the component
        // result = string.Format("The user selected: {0}", (theUserInput as string));
    }

    private async void HandleValidSubmit()
    {
        var checkStatusResponse = await SubdivisionService.UpdateSubdivisionAsync(subdivisionDetails, true);
        ShowSuccessOrErrorNotification(checkStatusResponse.Message, checkStatusResponse.IsSuccess);
    }

    //navigate to different lot
    public async void OnJobSelected(object theUserChoice)
    {
        string selectedLot = (string)theUserChoice;

        if (string.IsNullOrWhiteSpace(selectedLot))
        {
            return;
        }
        NavManager.NavigateTo($"lotdetails/{selectedLot}");
    }

    void EditOptionHandler(GridCommandEventArgs args)
    {
        AvailablePlanOptionDto item = (AvailablePlanOptionDto)args.Item;
    }

    async Task UpdateOptionHandler(GridCommandEventArgs args)
    {
        AvailablePlanOptionDto option = (AvailablePlanOptionDto)args.Item;
        var response = await OptionService.UpdateAvailablePlanOptionAsync(option);
        OptionData = (await OptionService.GetAvailablePlanOptionsByPlanAsync(SelectedPlan.PhasePlanId)).Value;
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
    }

    async Task DeleteOptionHandler(GridCommandEventArgs args)
    {
        AvailablePlanOptionDto item = (AvailablePlanOptionDto)args.Item;
        var response = await OptionService.DeleteAvailablePlanOptionAsync(item.PlanOptionId);        
        OptionData = (await OptionService.GetAvailablePlanOptionsByPlanAsync(SelectedPlan.PhasePlanId)).Value;
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
    }

    async Task CancelOptionHandler(GridCommandEventArgs args)
    {
        AvailablePlanOptionDto item = (AvailablePlanOptionDto)args.Item;
        // if necessary, perform actual data source operation here through your service
    }

    void EditPlanHandler(GridCommandEventArgs args)
    {
        PhasePlanModel item = (PhasePlanModel)args.Item;
    }

    async Task UpdatePlanHandler(GridCommandEventArgs args)
    {
        PhasePlanModel item = (PhasePlanModel)args.Item;
        var responseItem = await PlanService.UpdatePhasePlanAsync(item);
        var phasePlanResult = await PlanService.GetPhasePlansAsync(SubdivisionId);
        AvailablePlansThisSubdiv = phasePlanResult.Value;
        ShowSuccessOrErrorNotification(responseItem.Message, responseItem.IsSuccess);
    }

    async Task DeletePlanHandler(GridCommandEventArgs args)
    {
        PhasePlanModel item = (PhasePlanModel)args.Item;
        var responseItem = await PlanService.DeletePhasePlanAsync(item.PhasePlanId);
        var phasePlanResult = await PlanService.GetPhasePlansAsync(SubdivisionId);
        AvailablePlansThisSubdiv = phasePlanResult.Value;
        OptionData = null;
        ShowSuccessOrErrorNotification(responseItem.Message, responseItem.IsSuccess);
    }

    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }

    async Task CancelPlanHandler(GridCommandEventArgs args)
    {
        PhasePlanModel item = (PhasePlanModel)args.Item;
        // if necessary, perform actual data source operation here through your service
    }
    private void CopyPlanFromToolbar(GridCommandEventArgs args)
    {
        //note - the args.Item object is null because the command item is not associated with an item
        CopyPlanModal.Show();
        StateHasChanged();
    }

    private async void HandleValidCopyPlanSubmit(ResponseModel responseItem)
    {
        var phasePlanResult = await PlanService.GetPhasePlansAsync(SubdivisionId);
        AvailablePlansThisSubdiv = phasePlanResult.Value;
        CopyPlanModal.Hide();
        if (responseItem != null)
        {
            ShowSuccessOrErrorNotification(responseItem.Message, responseItem.IsSuccess);
        }
        StateHasChanged();
    }

    private void AddPlanFromToolbar(GridCommandEventArgs args)
    {
        //note - the args.Item object is null because the command item is not associated with an item
        AddPlanModal.Show();
        StateHasChanged();
    }

    private async void HandleValidAddPlanSubmit(ResponseModel responseItem)
    {
        var phasePlanResult = await PlanService.GetPhasePlansAsync(SubdivisionId);
        AvailablePlansThisSubdiv = phasePlanResult.Value;
        await AddPlanModal.Hide();

        if (responseItem != null)
        {
            ShowSuccessOrErrorNotification(responseItem.Message, responseItem.IsSuccess);
        }
        StateHasChanged();
    }

    void EditContactHandler(GridCommandEventArgs args)
    {
        SubdivisionContactModel item = (SubdivisionContactModel)args.Item;
    }

    async Task UpdateContactHandler(GridCommandEventArgs args)
    {
        SubdivisionContactModel item = (SubdivisionContactModel)args.Item;
        var updateResponse = await SubdivisionService.UpdateSubdivisionContactAsync(item);
        SubdivisionContacts = (await SubdivisionService.GetSubdivisionContactsAsync(SubdivisionId)).Value;
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
    }

    async Task DeleteContactHandler(GridCommandEventArgs args)
    {
        SubdivisionContactModel item = (SubdivisionContactModel)args.Item;
        var deleteResponse = await SubdivisionService.DeleteSubdivisionContactAsync(item);
        SubdivisionContacts = (await SubdivisionService.GetSubdivisionContactsAsync(SubdivisionId)).Value;
        ShowSuccessOrErrorNotification(deleteResponse.Message, deleteResponse.IsSuccess);
    }

    async Task CreateContactHandler(GridCommandEventArgs args)
    {
        SubdivisionContactModel item = (SubdivisionContactModel)args.Item;
        var addResponse = await SubdivisionService.AddSubdivisionContactAsync(item);
        SubdivisionContacts = (await SubdivisionService.GetSubdivisionContactsAsync(SubdivisionId)).Value;
        ShowSuccessOrErrorNotification(addResponse.Message, addResponse.IsSuccess);
    }

    async Task CancelContactHandler(GridCommandEventArgs args)
    {
        SubdivisionContactModel item = (SubdivisionContactModel)args.Item;
    }

    public async void UpdateSelectedSubdivs()
    {
        UpdateMessage = "Updating. Please wait...";
        //var updateJob = new UpdateJob()
        //    {
        //        JobNumber = JobNumber,
        //        JobsToUpdate = SelectedJobs
        //    };
        //await SubdivisionService.UpdateMultipleSubdivisionsContactsAsync(updateJob);
        UpdateMessage = "Success";
        StateHasChanged();
    }

    void ToggleSelectAll(bool selectAll)
    {
        SelectedSubdivs.Clear();

        if (selectAll)
        {
            SelectedSubdivs.AddRange(AvailableSubdivs);
        }

        MultiSelectRef.Rebind();
    }

    bool IsAllSelected()
    {
        return SelectedSubdivs.Count == AvailableSubdivs.Count;
    }

    // for the item checkboxes
    bool GetChecked(string text)
    {
        return SelectedSubdivs.Contains(text);
    }

    private async void HandleValidAddContactSubmit(ResponseModel<SubdivisionContactModel> responseItem)
    {
        SubdivisionContacts = (await SubdivisionService.GetSubdivisionContactsAsync(SubdivisionId)).Value;
        AddSubdivisionContactModal.Hide();
        ShowSuccessOrErrorNotification(responseItem.Message, responseItem.IsSuccess);
        StateHasChanged();
    }

    private void AddContactFromToolbar(GridCommandEventArgs args)
    {
        AddSubdivisionContactModal.Show();
        StateHasChanged();
    }

    async void UpdateSelectedJobs()
    {
        AllJobPhases = AllJobs.Select(x => x.Phase).Distinct().ToList();
        AllJobBuildingNums = AllJobs.Select(x => x.BuildingNum).Distinct().ToList();
        AllJobSticks = AllJobs.Select(x => x.StickBuilingNum).Distinct().ToList();
        UpdateJobsModal.Show();
        StateHasChanged();
    }

    private async void HandleValidUpdateSubmit(ResponseModel<UpdateJob> updateResponse)
    {
        if (updateResponse.IsSuccess)
        {
            JobsThisSubdivision = (await SubdivisionService.GetJobsAsync(SubdivisionId)).Value;
            JobDetailsGridRef.Rebind();           
        }
        SelectedJobs = new List<JobDto>();
        UpdateJobsModal.Hide();
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
        StateHasChanged();
    }

    public class BreadcrumbItem
    {
        public FontIcon? Icon { get; set; }
        public string Text { get; set; }
        public string Url { get; set; }
    }
}
