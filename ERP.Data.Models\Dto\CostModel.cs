﻿using ERP.Data.Models.Validation;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ERP.Data.Models.Dto;

public class CostModel
{


    public int CostsId { get; set; }
    public CostEditType EditType { get; set; }
    [DateInFutureValidation]
    public DateTime? CostEffectiveDate { get; set; }
    public int MasterItemId { get; set; }
    public string? ItemDesc { get; set; }
    public string? ItemNumber { get; set; }
    public string? PurchasingActivity { get; set; }
    public int? MasterItemPhaseId { get; set; }
    public string? PhaseCode { get; set; }
    public string? OptionDesc { get; set; }
    public int SubNumber { get; set; }
    public string? SupplierName { get; set; }
    public bool? IsSupplierActive { get; set; }

    public int? SubdivisionId { get; set; }
    public List<int>? Subdivisions { get; set; }
    public string? SubdivisionName { get; set; }
    public string? TakeoffUnit { get; set; }
    public int? TlpeItemId { get; set; }
    public string? Unit { get; set; }

    public double? UnitCost { get; set; }

    public double? NextCost { get; set; }

    [DateInFutureValidation]
    public DateTime? NextCostDue { get; set; }

    public double? NextCost2 { get; set; }
    [DateInFutureValidation]
    public DateTime? NextCost2Due { get; set; }

    public double? LastCost1 { get; set; }

    public DateTime? LastCostExpired { get; set; }

    public double? LastCost2 { get; set; }

    public DateTime? LastCost2Expired { get; set; }

    public double? LastCost3 { get; set; }

    public DateTime? LastCost3Expired { get; set; }

    public string? SupProductCode { get; set; }

    public string? SupAltDesc { get; set; }

    public string? SupUnit { get; set; }

    public double? SupOrigCost { get; set; }

    public double? SupOrigCostLast { get; set; }

    public double? SupOrigCostLast2 { get; set; }

    public double? SupOrigCostLast3 { get; set; }

    public double? SupOrigCostNext { get; set; }

    public double? SupOrigCostNext2 { get; set; }

    public string? IncludesTax { get; set; }

    public string? ItemTaxGroup { get; set; }

    public string? Warrantyitem { get; set; }

    public int? Warrantydays { get; set; }

    public int? Warrantytype { get; set; }

    public int? SupplierContractsId { get; set; }

    public int? SupplierContCoId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

}

public enum CostEditType
{
    Current,
    Next,
    Future
}