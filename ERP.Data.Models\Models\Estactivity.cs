﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Estactivity
{
    public int EstactivityId { get; set; }

    public string? JobNumber { get; set; }

    public string? BomClass { get; set; }

    public string? Releasecode { get; set; }

    public int? SelectedVendor { get; set; }

    public int? DefaultVendor { get; set; }

    public string? UseLocation { get; set; }

    public string? UseWbsSort { get; set; }

    public string? Taxable { get; set; }

    public string? TaxGroup { get; set; }

    public int? TradeId { get; set; }

    public int? SactivityId { get; set; }

    public int? Poseq { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CubitId { get; set; }

    public int? SubNumber { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<Estdetail> Estdetails { get; set; } = new List<Estdetail>();

    public virtual Job? JobNumberNavigation { get; set; }

    public virtual Sactivity? Sactivity { get; set; }

    public virtual ICollection<ScheduleSactivityLink> ScheduleSactivityLinks { get; set; } = new List<ScheduleSactivityLink>();

    public virtual Trade? Trade { get; set; }
}
