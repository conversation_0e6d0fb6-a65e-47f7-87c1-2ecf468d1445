﻿using AutoMapper;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using DocumentFormat.OpenXml.InkML;
using ERP.API.Data;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Options;
using NLog;
using NLog.Fluent;
using System.Data;
using System.Linq;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class CostController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private ExportService _service;
        private string contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        private string fileName = "authors.xlsx";
        private readonly IMapper _mapper;
        public CostController(IConfiguration configuration, ErpDevContext context, ExportService service, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _service = service;
            _mapper = mapper;
        }

       
        [HttpGet]
        public async Task<IActionResult> GetPurchasingActivitiesAsync()
        {
            try
            {
                //Div = 1 is supposed to be division, div = 2 is area. But they have the same bom classes, hence same items. 
                var activities = await _context.Pactivities.Where(x => x.IsActive == true && x.DivId == 1).Select(x => new PactivityModel()
                {
                    PactivityId = x.PactivityId,
                    Activity = x.Activity,
                    TradeName = x.Trade.TradeName
                }).OrderBy(x => x.Activity).ToListAsync();
                return Ok(new ResponseModel<List<PactivityModel>> { Value = activities, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<PactivityModel>>() { IsSuccess = false, Message = "failed to get purchaing activities", Value = null });
            }
        }

        [HttpGet("{activityId}")]
        public async Task<IActionResult> GetItemsPhasesInActivityAsync(int activityId)
        {
            try
            {
                var getPactivityBomClass = await _context.Pactivities.Where(x => x.PactivityId == activityId).SingleOrDefaultAsync();
                var bomClass = getPactivityBomClass.BomClassId;
                var items = await _context.MasterItems.Include("MasterItemPhase").Where(x => x.BomClassId == bomClass).Select(x => x.MasterItemPhase).ToListAsync();
                return Ok(new ResponseModel<List<MasterItemPhasis>>() { Value = items, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MasterItemPhasis>>() { IsSuccess = false, Message = "failed to get items in activity", Value = null });
            }
        }

        [HttpGet("{activityId}")]
        public async Task<IActionResult> GetItemsInActivityAsync(int activityId)
        {
            try
            {
                var getPactivityBomClass = await _context.Pactivities.Where(x => x.PactivityId == activityId).SingleOrDefaultAsync();
                var bomClass = getPactivityBomClass.BomClassId;
                var items = await _context.MasterItems.Where(x => x.BomClassId == bomClass && x.IsActive == true).Select(x => new ModelManagerItemModel()
                {
                    ItemDesc = x.ItemDesc,
                    ItemNumber = x.ItemNumber,
                    MasterItemId = x.MasterItemId,
                    MasterItemPhaseId = x.MasterItemPhaseId,
                    PhaseCode = x.MasterItemPhase.PhaseCode,
                    PhaseDesc = x.MasterItemPhase.PhaseDesc,
                    TakeoffUnit = x.TakeoffUnit,
                    IsPlanSpecific = x.PlanSpecific == "T",
                    IsLumpSum = x.PlanSpecific == "T",
                }).ToListAsync();
                return Ok(new ResponseModel<List<ModelManagerItemModel>>() { Value = items, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ModelManagerItemModel>>() { IsSuccess = false, Message = "failed to get items in activity", Value = null });
            }
        }

        [HttpGet("{itemId}")]
        public async Task<IActionResult> GetCostsForItemAsync(int itemId)
        {
            try
            {
                var costs = await _context.Costs.Include("MasterItem").Include(x => x.SubNumberNavigation).Where(x => x.MasterItemId == itemId && x.IsActive == true).Select(x => new CostModel()
                {
                    CostsId = x.CostsId,
                    MasterItemId = x.MasterItemId,
                    ItemDesc = x.MasterItem.ItemDesc,
                    ItemNumber = x.MasterItem.ItemNumber,
                    SubdivisionId = x.SubdivisionId,
                    SubdivisionName = x.Subdivision.SubdivisionName,
                    SubNumber = x.SubNumber,
                    SupplierName = x.SubNumberNavigation.SubName,
                    IsSupplierActive = x.SubNumberNavigation.IsActive1 ?? false,
                    LastCost1 = x.LastCost1,
                    LastCost2 = x.LastCost2,
                    LastCost3 = x.LastCost3,
                    NextCost = x.NextCost,
                    NextCost2 = x.NextCost2,
                    Unit = x.MasterItem.TakeoffUnit, 
                    UnitCost = x.UnitCost,
                    NextCostDue = x.NextCostDue,
                    NextCost2Due = x.NextCost2Due,
                    LastCostExpired = x.LastCostExpired,
                    LastCost2Expired = x.LastCost2Expired,
                    LastCost3Expired = x.LastCost3Expired,
                    IncludesTax = x.IncludesTax
                }).ToListAsync();
                return Ok(new ResponseModel<List<CostModel>>() { Value = costs, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CostModel>>() { IsSuccess = false, Message = "failed to get costs for item", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> GetCostsForItemsAsync([FromBody] List<int> itemIds)
        {
            try
            {
                var costs = await _context.Costs.Include("MasterItem").Include(x =>x.SubNumberNavigation).Where(x => itemIds.Contains(x.MasterItemId) && x.IsActive == true).Select(x => new CostModel()
                {
                    CostsId = x.CostsId,
                    MasterItemId = x.MasterItemId,
                    ItemDesc = x.MasterItem.ItemDesc,
                    ItemNumber = x.MasterItem.ItemNumber,
                    IsSupplierActive = x.SubNumberNavigation.IsActive1 ?? false,
                    SubdivisionId = x.SubdivisionId,
                    SubdivisionName = x.Subdivision.SubdivisionName,
                    SubNumber = x.SubNumber,
                    SupplierName = x.SubNumberNavigation.SubName,
                    LastCost1 = x.LastCost1,
                    LastCost2 = x.LastCost2,
                    LastCost3 = x.LastCost3,
                    NextCost = x.NextCost,
                    NextCost2 = x.NextCost2,
                    Unit = x.MasterItem.TakeoffUnit, 
                    UnitCost = x.UnitCost,
                    NextCostDue = x.NextCostDue,
                    NextCost2Due = x.NextCost2Due,
                    LastCostExpired = x.LastCostExpired,
                    LastCost2Expired = x.LastCost2Expired,
                    LastCost3Expired = x.LastCost3Expired,
                    IncludesTax = x.IncludesTax
                }).ToListAsync();
                return Ok(new ResponseModel<List<CostModel>>() { Value = costs, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CostModel>>() { IsSuccess = false, Message = "failed to get costs for items", Value = null });
            }
        }

        [HttpGet]
        public async Task<IActionResult> CheckRollOverDueCostsAsync()
        {
            try
            {
                var rolloverDueCosts = await _context.Costs.Where(x => x.IsActive == true && x.NextCostDue != null && x.NextCostDue.Value.Date <= DateTime.UtcNow.Date).ToListAsync();

                var isAnyCostDue = rolloverDueCosts.Count > 0;
                var response = new ResponseModel<bool>() { IsSuccess = true, Value = isAnyCostDue };
                return Ok(response);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<bool>() { IsSuccess = false, Value = false, Message = "Failed to check if there are any Costs that are due" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> RolloverAllCostsAsync()
        {
            try
            {
                var RolloverDate = DateTime.Now;
                //Select the update costs - only the ones that need update
                var updateCosts1 = _context.Costs.Where(x => x.IsActive == true && x.NextCostDue != null && x.NextCostDue.Value.Date <= RolloverDate.Date );
                var listUpdateCost = updateCosts1.ToList();
                var updateBy = User.Identity.Name.Split('@')[0];
                int countCostsToUpdate = updateCosts1.Count();
                //write to history if needed 
                var costsNeedHistory = updateCosts1.Where(x => x.LastCost3 != null);
                DataTable CostsHistoryDataTable = new DataTable("CostsHistory");
                DataColumn CostsId = new DataColumn("CostsId", typeof(int));
                CostsHistoryDataTable.Columns.Add(CostsId);
                DataColumn MasterItemId = new DataColumn("MasterItemId", typeof(int));
                CostsHistoryDataTable.Columns.Add(MasterItemId);
                DataColumn SubNumber = new DataColumn("SubNumber", typeof(int));
                CostsHistoryDataTable.Columns.Add(SubNumber);
                DataColumn SubdivisionId = new DataColumn("SubdivisionId", typeof(int));
                CostsHistoryDataTable.Columns.Add(SubdivisionId);
                DataColumn OldCost = new DataColumn("OldCost", typeof(decimal));
                CostsHistoryDataTable.Columns.Add(OldCost);
                DataColumn CostExpired = new DataColumn("CostExpired", typeof(DateTime));
                CostsHistoryDataTable.Columns.Add(CostExpired);
                DataColumn CostRolledOverBy = new DataColumn("CostRolledOverBy", typeof(string));
                CostsHistoryDataTable.Columns.Add(CostRolledOverBy);
                DataColumn CreatedBy = new DataColumn("CreatedBy", typeof(string));
                CostsHistoryDataTable.Columns.Add(CreatedBy);

                string createBy = User.Identity.Name.Split('@')[0];
                foreach (var cost in costsNeedHistory)
                {
                    CostsHistoryDataTable.Rows.Add(cost.CostsId, cost.MasterItemId, cost.SubNumber, cost.SubdivisionId, cost.LastCost3, cost.LastCost3Expired, createBy, createBy);
                    //TODO: store all history in the history table, don't wait for "last cost". In fact, could the current cost be put in the history?
                    //below would save from current costs to the history table (user updatecosts1 not costs need hsit), would need to run one time query to update the existing ones to be that way
                    //CostsHistoryDataTable.Rows.Add(cost.CostsId, cost.MasterItemId, cost.SubNumber, cost.SubdivisionId, cost.UnitCost, cost.NextCost2Due, createBy, createBy);
                }

                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    using (var bulkCopy = new SqlBulkCopy(connection))
                    {
                        bulkCopy.DestinationTableName = "COSTS_HISTORY";
                        bulkCopy.ColumnMappings.Add("CostsId", "COSTS_ID");
                        bulkCopy.ColumnMappings.Add("MasterItemId", "MASTER_ITEMS_ID");
                        bulkCopy.ColumnMappings.Add("SubNumber", "SUB_NUMBER");
                        bulkCopy.ColumnMappings.Add("SubdivisionId", "SUBDIVISION_ID");
                        bulkCopy.ColumnMappings.Add("OldCost", "OLD_COST");
                        bulkCopy.ColumnMappings.Add("CostExpired", "COST_EXPIRED");
                        bulkCopy.ColumnMappings.Add("CostRolledOverBy", "COST_ROLLED_OVER_BY");
                        bulkCopy.ColumnMappings.Add("CreatedBy", "CreatedBy");
                        try
                        {
                            bulkCopy.WriteToServer(CostsHistoryDataTable);
                        }
                        catch (Exception ex)
                        {
#if DEBUG
                            _logger.Debug(ex);
#else
                    _logger.Error(ex);
#endif
                        }
                    }
                }

                //now update the costs
                int? nextCost2Null = null;
                DateTime? nextCost2Due = null;
                //update the costs
                await updateCosts1.ExecuteUpdateAsync(s => s.SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now).SetProperty(b => b.LastCost3, b => b.LastCost2).SetProperty(b => b.LastCost2, b => b.LastCost1).SetProperty(b => b.LastCost1, b => b.UnitCost).SetProperty(b => b.UnitCost, b => b.NextCost).SetProperty(b => b.NextCost, b => b.NextCost2).SetProperty(b => b.NextCost2, nextCost2Null).SetProperty(b => b.LastCost3Expired, b => b.LastCost2Expired).SetProperty(b => b.LastCost2Expired, b => b.LastCostExpired).SetProperty(b => b.LastCostExpired, b => b.NextCostDue).SetProperty(b => b.NextCostDue, b => b.NextCost2Due).SetProperty(b => b.NextCost2Due, nextCost2Due));

                var updatedCosts = _mapper.Map<List<CostDto>>(listUpdateCost);
                return Ok(new ResponseModel<List<CostDto>>() { Value = updatedCosts, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CostDto>>() { IsSuccess = false, Message = "failed to rollover all costs", Value = null });
            }
        }
        [HttpGet("{subdivisionId}/{activityId}")]
        public async Task<IActionResult> GetAllCostsForSubdivisionAndActivityAsync(int subdivisionId, int activityId)
        {
            try
            {
                var getBomClasses = await _context.Pactivities.Where(x => x.PactivityId == activityId).Select(x => x.BomClassId).ToListAsync();
                var costs = await _context.Costs.Where(x => x.SubdivisionId == subdivisionId && x.IsActive == true).Select(x => new CostModel()
                {
                    CostsId = x.CostsId,
                    MasterItemId = x.MasterItemId,
                    ItemDesc = x.MasterItem.ItemDesc,
                    ItemNumber = x.MasterItem.ItemNumber,
                    SubdivisionId = x.SubdivisionId,
                    SubNumber = x.SubNumber,
                    MasterItemPhaseId = x.MasterItem.MasterItemPhaseId,
                    PhaseCode = x.MasterItem.MasterItemPhase.PhaseCode,
                    SupplierName = x.SubNumberNavigation.SubName,
                    LastCost1 = x.LastCost1,
                    LastCost2 = x.LastCost2,
                    LastCost3 = x.LastCost3,
                    NextCost = x.NextCost,
                    NextCost2 = x.NextCost2,
                    Unit = x.MasterItem.TakeoffUnit, 
                    UnitCost = x.UnitCost,
                    LastCost2Expired = x.LastCost2Expired,
                    LastCost3Expired = x.LastCost3Expired,
                    NextCostDue = x.NextCostDue,
                }).ToListAsync();
                return Ok(new ResponseModel<List<CostModel>>() { Value = costs, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CostModel>>() { IsSuccess = false, Message = "failed to get all costs for subdivision and activity", Value = null });
            }
        }

        [HttpGet("{activityId}")]
        public async Task<IActionResult> GetAllCostsForActivityAsync(int activityId)
        {
            try
            {
                var getBomClasses = await _context.Pactivities.Where(x => x.PactivityId == activityId).Select(x => x.BomClassId).ToListAsync();
                var costs = await _context.Costs.Where(x => getBomClasses.Contains(x.MasterItem.BomClassId) && x.IsActive == true).Select(x => new CostModel()
                {
                    CostsId = x.CostsId,
                    MasterItemId = x.MasterItemId,
                    ItemDesc = x.MasterItem.ItemDesc,
                    ItemNumber = x.MasterItem.ItemNumber,
                    SubdivisionId = x.SubdivisionId,
                    SubNumber = x.SubNumber,
                    MasterItemPhaseId = x.MasterItem.MasterItemPhaseId,
                    PhaseCode = x.MasterItem.MasterItemPhase.PhaseCode,
                    SupplierName = x.SubNumberNavigation.SubName,
                    LastCost1 = x.LastCost1,
                    LastCost2 = x.LastCost2,
                    LastCost3 = x.LastCost3,
                    NextCost = x.NextCost,
                    NextCost2 = x.NextCost2,
                    Unit = x.MasterItem.TakeoffUnit, 
                    UnitCost = x.UnitCost,
                    LastCost2Expired = x.LastCost2Expired,
                    LastCost3Expired = x.LastCost3Expired,
                    NextCostDue = x.NextCostDue,
                }).ToListAsync();
                return Ok(new ResponseModel<List<CostModel>> { Value = costs, IsSuccess = true});
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CostModel>> { IsSuccess = false, Message = "Failed to get all costs for activity", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> GetAllCostsForActivitiesAsync([FromBody] List<int> activityIds)
        {
            try
            {

                var getBomClasses = await _context.Pactivities.Where(x => activityIds.Contains(x.PactivityId)).Select(x => x.BomClassId).ToListAsync();
                var costs = await _context.Costs.Where(x => getBomClasses.Contains(x.MasterItem.BomClassId) && x.IsActive == true).Select(x => new CostModel()
                {
                    CostsId = x.CostsId,
                    MasterItemId = x.MasterItemId,
                    ItemDesc = x.MasterItem.ItemDesc,
                    ItemNumber = x.MasterItem.ItemNumber,
                    SubdivisionId = x.SubdivisionId,
                    SubNumber = x.SubNumber,
                    MasterItemPhaseId = x.MasterItem.MasterItemPhaseId,
                    PhaseCode = x.MasterItem.MasterItemPhase.PhaseCode,
                    SupplierName = x.SubNumberNavigation.SubName,
                    LastCost1 = x.LastCost1,
                    LastCost2 = x.LastCost2,
                    LastCost3 = x.LastCost3,
                    NextCost = x.NextCost,
                    NextCost2 = x.NextCost2,
                    Unit = x.MasterItem.TakeoffUnit, 
                    UnitCost = x.UnitCost,
                    LastCost2Expired = x.LastCost2Expired,
                    LastCost3Expired = x.LastCost3Expired,
                    NextCostDue = x.NextCostDue,
                }).ToListAsync();
                return Ok(new ResponseModel<List<CostModel>> { Value = costs, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CostModel>> { IsSuccess = false, Message = "Failed to get all costs for activities", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> GetAllCostsForSupplierAndActivitiesAsync([FromBody] SupplierandActivitiesListSelectModel model)
        {
            try
            {
                var getBomClasses = await _context.Pactivities.Where(x => model.SelectedActivities.Contains(x.PactivityId)).Select(x => x.BomClassId).ToListAsync();
                var costs = await _context.Costs.Where(x => getBomClasses.Contains(x.MasterItem.BomClassId) && x.SubNumber == model.SubNumber).Select(x => new CostModel()
                {
                    CostsId = x.CostsId,                    
                    MasterItemId = x.MasterItemId,
                    ItemDesc = x.MasterItem.ItemDesc,
                    ItemNumber = x.MasterItem.ItemNumber,
                    SubdivisionId = x.SubdivisionId,
                    SubNumber = x.SubNumber,
                    MasterItemPhaseId = x.MasterItem.MasterItemPhaseId,
                    PhaseCode = x.MasterItem.MasterItemPhase.PhaseCode,
                    SupplierName = x.SubNumberNavigation.SubName,
                    LastCost1 = x.LastCost1,
                    LastCost2 = x.LastCost2,
                    LastCost3 = x.LastCost3,
                    NextCost = x.NextCost,
                    NextCost2 = x.NextCost2,
                    Unit = x.MasterItem.TakeoffUnit, 
                    UnitCost = x.UnitCost,
                    LastCost2Expired = x.LastCost2Expired,
                    LastCost3Expired = x.LastCost3Expired,
                    NextCostDue = x.NextCostDue,
                }).ToListAsync();
                return Ok(new ResponseModel<List<CostModel>>() { Value = costs, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CostModel>> { IsSuccess = false, Message = "Failed to get all costs for supplier and activities", Value = null });
            }
        }
        private async Task<List<CostModel>> GetAllCostsForSupplierAndActivitiesForDateAsync(SupplierandActivitiesListSelectModel model)
        {
            try
            {
                DateTime? selectDate = model.CostAsOfDate ?? DateTime.Now;
                selectDate = selectDate.Value.Date;//Try date only , no time
                if (model.OnlyItemsWithoutCosts)
                {
                    var noCostItems = new List<CostModel>();
                    noCostItems =  await GetItemsWithNoCostsAsync(model);
                    return noCostItems;
                }
                var costs = new List<CostModel>();
                var getBomClasses = await _context.Pactivities.Where(x => model.SelectedActivities.Contains(x.PactivityId)).Select(x => x.BomClassId).ToListAsync();
                if (model.DivisionDefaults == true || model.IncludeDivisionDefaultCosts == true)
                {

                    //get the division default costs (AreaId = 1)
                    costs = await _context.Costs.Include(x => x.CostsHistories).Include(x => x.MasterItem.BomClass).Where(x => getBomClasses.Contains(x.MasterItem.BomClassId) && x.SubNumber == model.SubNumber &&  x.SubdivisionId == 1  && x.IsActive == true).Select(x => new CostModel()
                    {
                        CostsId = x.CostsId,
                        MasterItemId = x.MasterItemId,
                        ItemDesc = x.MasterItem.ItemDesc,
                        ItemNumber = x.MasterItem.ItemNumber,
                        PurchasingActivity = x.MasterItem.BomClass.BomClass1,
                        SubdivisionId = x.SubdivisionId,
                        SubdivisionName = x.Subdivision != null ? x.Subdivision.SubdivisionName : "Default",
                        SubNumber = x.SubNumber,
                        MasterItemPhaseId = x.MasterItem.MasterItemPhaseId,
                        PhaseCode = x.MasterItem.MasterItemPhase.PhaseCode,
                        OptionDesc = x.MasterItem.MasterItemPhase.PhaseDesc,
                        SupplierName = x.SubNumberNavigation.SubName,
                        SupUnit = x.SupUnit,
                        TakeoffUnit = x.MasterItem.TakeoffUnit,
                        Unit = x.MasterItem.TakeoffUnit, 
                        UnitCost = x.LastCost3Expired != null && selectDate < x.LastCost3Expired.Value.Date ? (x.CostsHistories.Where(y => y.CostExpired != null && selectDate < y.CostExpired.Value.Date).OrderByDescending(y => y.CostExpired).FirstOrDefault() != null ? (selectDate < x.CostsHistories.Where(y => y.CostExpired != null && selectDate < y.CostExpired.Value.Date).OrderByDescending(y => y.CostExpired).FirstOrDefault().CostExpired.Value.Date ? x.CostsHistories.Where(y => y.CostExpired != null && selectDate < y.CostExpired.Value.Date).OrderByDescending(y => y.CostExpired).FirstOrDefault().OldCost : x.LastCost3) : x.LastCost3) : ((x.LastCost2Expired != null && selectDate < x.LastCost2Expired.Value.Date) ? x.LastCost2 : ((x.LastCostExpired != null && selectDate < x.LastCostExpired.Value.Date)) ? x.LastCost1 : ((x.NextCostDue == null || selectDate < x.NextCostDue.Value.Date) ? x.UnitCost : ((x.NextCost2Due == null || selectDate < x.NextCost2Due.Value.Date) ? x.NextCost : x.NextCost2))), //check history table if it's before last cost 3 expired
                        CostEffectiveDate = selectDate.Value.Date,
                    }).ToListAsync();
                }
                if(model.DivisionDefaults == false && model.SubdivisionId != null)
                {
                    //get subdivision costs
                    var subdivCosts = await _context.Costs.Include(x => x.CostsHistories).Include(x => x.MasterItem.BomClass).Where(x => getBomClasses.Contains(x.MasterItem.BomClassId) && x.SubNumber == model.SubNumber && x.SubdivisionId == model.SubdivisionId && x.IsActive == true).Select(x => new CostModel()
                    {
                        CostsId = x.CostsId,
                        MasterItemId = x.MasterItemId,
                        ItemDesc = x.MasterItem.ItemDesc,
                        ItemNumber = x.MasterItem.ItemNumber,
                        PurchasingActivity = x.MasterItem.BomClass.BomClass1,
                        SubdivisionId = x.SubdivisionId,
                        SubdivisionName = x.Subdivision != null ? x.Subdivision.SubdivisionName : "Default",
                        SubNumber = x.SubNumber,
                        MasterItemPhaseId = x.MasterItem.MasterItemPhaseId,
                        PhaseCode = x.MasterItem.MasterItemPhase.PhaseCode,
                        OptionDesc = x.MasterItem.MasterItemPhase.PhaseDesc,
                        SupplierName = x.SubNumberNavigation.SubName,
                        SupUnit = x.SupUnit,
                        Unit = x.MasterItem.TakeoffUnit,
                        UnitCost = x.LastCost3Expired != null && selectDate < x.LastCost3Expired.Value.Date ? (x.CostsHistories.Where(y => y.CostExpired != null && selectDate < y.CostExpired.Value.Date).OrderByDescending(y => y.CostExpired).FirstOrDefault() != null ? (selectDate < x.CostsHistories.Where(y => y.CostExpired != null && selectDate < y.CostExpired.Value.Date).OrderByDescending(y => y.CostExpired).FirstOrDefault().CostExpired.Value.Date ? x.CostsHistories.Where(y => y.CostExpired != null && selectDate < y.CostExpired.Value.Date).OrderByDescending(y => y.CostExpired).FirstOrDefault().OldCost : x.LastCost3) : x.LastCost3) : ((x.LastCost2Expired != null && selectDate < x.LastCost2Expired.Value.Date) ? x.LastCost2 : ((x.LastCostExpired != null && selectDate < x.LastCostExpired.Value.Date)) ? x.LastCost1 : ((x.NextCostDue == null || selectDate < x.NextCostDue.Value.Date) ? x.UnitCost : ((x.NextCost2Due == null || selectDate < x.NextCost2Due.Value.Date) ? x.NextCost : x.NextCost2))), //check history table if it's before last cost 3 expired
                        CostEffectiveDate = selectDate
                    }).ToListAsync();
                    costs.AddRange(subdivCosts);
                }
                                               
                if (model.IncludeItemsWithoutCosts == true)
                {
                    var noCostItems = new List<CostModel>();
                    noCostItems = await GetItemsWithNoCostsAsync(model);
                    costs.AddRange(noCostItems);
                }

                return costs;
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return null;
            }
        }

        private async Task<List<CostModel>> GetItemsWithNoCostsAsync(SupplierandActivitiesListSelectModel model)
        {
            try
            {
                var selectDate = model.CostAsOfDate ?? DateTime.Now;                
                var getSupplier = await _context.Suppliers.SingleOrDefaultAsync(x => x.SubNumber == model.SubNumber);
                var getBomClasses = await _context.Pactivities.Where(x => model.SelectedActivities.Contains(x.PactivityId)).Select(x => x.BomClassId).ToListAsync();
                string stringBomClassIds = string.Join(",", getBomClasses);
                string stringSubdivisionIds = "";
                if (model.DivisionDefaults == true && (model.SubdivisionId == null || model.SubdivisionId == 1))
                {
                    //division defaults only
                    stringSubdivisionIds = "1";

                }
                else if ((model.DivisionDefaults == true || model.IncludeDivisionDefaultCosts == true) && model.SubdivisionId != null && model.SubdivisionId != 1)
                {
                    //both division default and specific subdivision
                    stringSubdivisionIds = $"1,{model.SubdivisionId.ToString()}";
                }
                else if (model.IncludeDivisionDefaultCosts == false && model.SubdivisionId != null && model.SubdivisionId != 1)
                {
                    //specific subdivision only
                    stringSubdivisionIds = model.SubdivisionId.ToString();
                }
                
                var itemsNoCostsList = new List<CostModel>();
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();                   
                    //var query = $"SELECT t1.MASTER_ITEM_ID, t1.ITEM_DESC, t1.ITEM_NUMBER, t1.TAKEOFF_UNIT, p.PHASE_CODE, p.MASTER_ITEM_PHASE_ID, p.PHASE_DESC, b.BOM_CLASS FROM dbo.MASTER_ITEMS t1 LEFT JOIN dbo.COSTS t2 ON t2.MASTER_ITEM_ID = t1.MASTER_ITEM_ID and t2.IsActive = 1 and t2.SUB_NUMBER = @subNumber and t2.SUBDIVISION_ID in (@stringSubdivisionIds)  JOIN dbo.MASTER_ITEM_PHASES p on p.MASTER_ITEM_PHASE_ID = t1.MASTER_ITEM_PHASE_ID  JOIN dbo.BOM_CLASS b on t1.BOM_CLASS_ID = b.BOM_CLASS_ID WHERE t2.MASTER_ITEM_ID IS NULL and t1.BOM_CLASS_ID in (@stringBomClassIds)";
                    var query = $"SELECT t1.MASTER_ITEM_ID, t1.ITEM_DESC, t1.ITEM_NUMBER, t1.TAKEOFF_UNIT, p.PHASE_CODE, p.MASTER_ITEM_PHASE_ID, p.PHASE_DESC, b.BOM_CLASS FROM dbo.MASTER_ITEMS t1 LEFT JOIN dbo.COSTS t2 ON t2.MASTER_ITEM_ID = t1.MASTER_ITEM_ID and t2.IsActive = 1 and t2.SUB_NUMBER = @subNumber and t2.SUBDIVISION_ID in ({stringSubdivisionIds})  JOIN dbo.MASTER_ITEM_PHASES p on p.MASTER_ITEM_PHASE_ID = t1.MASTER_ITEM_PHASE_ID  JOIN dbo.BOM_CLASS b on t1.BOM_CLASS_ID = b.BOM_CLASS_ID WHERE t2.MASTER_ITEM_ID IS NULL and t1.BOM_CLASS_ID in ({stringBomClassIds})";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.AddWithValue("@subNumber", model.SubNumber);
                   // command.Parameters.AddWithValue("@stringSubdivisionIds", stringSubdivisionIds);
                   // command.Parameters.AddWithValue("@stringBomClassIds", stringBomClassIds);
                    var reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        itemsNoCostsList.Add(new CostModel()
                        {
                            CostsId = 0,
                            MasterItemId = reader.GetValue(0) != DBNull.Value ? (int)reader.GetValue(0) : 0,
                            ItemDesc = reader.GetValue(1) != DBNull.Value ? (string)reader.GetValue(1) : null,
                            ItemNumber = reader.GetValue(2) != DBNull.Value ? (string)reader.GetValue(2) : null,
                            MasterItemPhaseId = reader.GetValue(5) != DBNull.Value ? (int)reader.GetValue(5) : 0,
                            PhaseCode = reader.GetValue(4) != DBNull.Value ? (string)reader.GetValue(4) : null,
                            OptionDesc = reader.GetValue(6) != DBNull.Value ? (string)reader.GetValue(6) : null,
                            PurchasingActivity = reader.GetValue(7) != DBNull.Value ? (string)reader.GetValue(7) : null,
                            SubdivisionId = 0,
                            SubdivisionName =  "No Cost Item",
                            SubNumber = model.SubNumber,
                            SupplierName = getSupplier != null ? getSupplier.SubName : null,
                            Unit = reader.GetValue(3) != DBNull.Value ? (string)reader.GetValue(3) : null,             
                            CostEffectiveDate = selectDate
                        });
                                              
                    }
                }         
                return itemsNoCostsList;
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return null;
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddCostAsync([FromBody] CostModel costToAdd)
        {
            //if already a cost this supplier this subdivision, update it, don't add a new one 
            var costEffective = costToAdd.CostEffectiveDate ?? DateTime.Now;
            try
            {
                foreach(var subdivId in costToAdd.Subdivisions)
                {
                    var findCost = _context.Costs.Where(x => x.SubdivisionId == subdivId && x.SubNumber == costToAdd.SubNumber && x.MasterItemId == costToAdd.MasterItemId && x.IsActive == true).ToList();
                    if (findCost.Any())
                    {
                        foreach (var updateCost in findCost)
                        {
                            updateCost.SupProductCode = costToAdd.SupProductCode;
                            updateCost.Warrantydays = costToAdd.Warrantydays;
                            updateCost.Warrantyitem = costToAdd.Warrantyitem;
                            updateCost.Warrantytype = costToAdd.Warrantytype;
                            updateCost.IncludesTax = costToAdd.IncludesTax;
                            updateCost.ItemTaxGroup = costToAdd.ItemTaxGroup;
                            updateCost.NextCostDue = costToAdd.EditType == CostEditType.Next ? costEffective : null;
                            updateCost.NextCost = costToAdd.EditType == CostEditType.Next ? costToAdd.UnitCost ?? 0 : null;
                            updateCost.UnitCost = costToAdd.EditType == CostEditType.Current ? costToAdd.UnitCost ?? 0: null;
                            updateCost.NextCost2 = costToAdd.NextCost2;
                            updateCost.NextCost2Due = costToAdd.NextCost2Due;
                            updateCost.LastCost1 = costToAdd.LastCost1;
                            updateCost.LastCost2 = costToAdd.LastCost2;
                            updateCost.LastCost3 = costToAdd.LastCost3;
                            updateCost.LastCostExpired = costToAdd.EditType == CostEditType.Current ? costEffective : null;
                            updateCost.LastCost2Expired = costToAdd.LastCost2Expired;
                            updateCost.LastCost3Expired = costToAdd.LastCost3Expired;
                            updateCost.UpdatedBy = User.Identity.Name.Split('@')[0];
                            updateCost.UpdatedDateTime = DateTime.Now;
                            _context.Costs.Update(updateCost);
                            
                        }//what if there is more than one? there really shouldn't be
                        _context.SaveChanges();
                    }
                    else
                    {
                        var addCost = new Cost();
                        addCost.MasterItemId = costToAdd.MasterItemId;
                        addCost.SubdivisionId = subdivId;
                        addCost.SubNumber = costToAdd.SubNumber;
                        addCost.SupProductCode = costToAdd.SupProductCode;
                        addCost.Warrantydays = costToAdd.Warrantydays;
                        addCost.Warrantyitem = costToAdd.Warrantyitem;
                        addCost.Warrantytype = costToAdd.Warrantytype;
                        addCost.IncludesTax = costToAdd.IncludesTax;
                        addCost.ItemTaxGroup = costToAdd.ItemTaxGroup;
                        addCost.NextCostDue = costToAdd.EditType == CostEditType.Next ? costEffective : null;
                        addCost.NextCost = costToAdd.EditType == CostEditType.Next ? costToAdd.UnitCost ?? 0 : null;
                        addCost.UnitCost = costToAdd.EditType == CostEditType.Current ? costToAdd.UnitCost ?? 0 : null;
                        addCost.NextCost2 = costToAdd.NextCost2;
                        addCost.NextCost2Due = costToAdd.NextCost2Due;
                        addCost.LastCost1 = costToAdd.LastCost1;
                        addCost.LastCost2 = costToAdd.LastCost2;
                        addCost.LastCost3 = costToAdd.LastCost3;
                        addCost.LastCostExpired = costToAdd.EditType == CostEditType.Current ? costEffective : null;
                        addCost.LastCost2Expired = costToAdd.LastCost2Expired;
                        addCost.LastCost3Expired = costToAdd.LastCost3Expired;
                        addCost.UpdatedBy = User.Identity.Name.Split('@')[0];
                        addCost.UpdatedDateTime = DateTime.Now;
                        _context.Costs.Add(addCost);
                        await _context.SaveChangesAsync();
                    }
                }                                
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<CostModel>() { IsSuccess = false, Message = "Failed to add cost", Value = null });
            }
            return Ok(new ResponseModel<CostModel>() { Value = costToAdd, IsSuccess = true, Message = "Added cost" });
        }

        [HttpPost]
        public async Task<IActionResult> AddCostsAsync([FromBody] List<CostModel> costsToUpdate)
        {           
            return Ok(true);
        }

        [HttpPost]
        public async Task<IActionResult> RemoveAreaCostsAsync([FromBody] List<CostModel> importCosts)
        {
            try
            {
                var subNum = importCosts.FirstOrDefault()?.SubNumber;
                var masterItemIds = importCosts.Select(x => x.MasterItemId).ToList();
                var allCostsThisSupplierOtherSubdiv = _context.Costs.Where(x => x.SubNumber == subNum && x.SubdivisionId != null && x.SubdivisionId != 1 && masterItemIds.Contains(x.MasterItemId));//all costs this supplier with same master item and supplier, but for a specific subdivision (not division default)
                var updateBy = User.Identity.Name.Split('@')[0];
                await allCostsThisSupplierOtherSubdiv.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));//Mark inactive 

                return Ok(new ResponseModel<List<CostModel>>() { Value = importCosts, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CostModel>>() { IsSuccess = false, Message = "Failed to remove area costs", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ApplyCostsAsync([FromBody] List<CostModel> importCosts)
        {
            try
            {
                importCosts = importCosts.Where(x => x.UnitCost != null).ToList();//exclude any blank/null costs from importing
                if (importCosts.Count > 0) 
                {
                    var effectiveDate = importCosts.First().EditType == CostEditType.Current ? DateTime.Now : importCosts.First().CostEffectiveDate ?? DateTime.Now.Date;//TODO: require something in the future is picked

                    //Bulk insert and bulk update, first check if cost exists for the subdivision, item, and supplier, if not insert new
                    var subNum = importCosts.First().SubNumber;
                    var subdivId = importCosts.First().SubdivisionId;
                    var masterItemIds = importCosts.Select(x => x.MasterItemId).ToList();
                    var allCostsThisSupplierSubdiv = _context.Costs.Where(x => x.SubNumber == subNum && x.SubdivisionId == subdivId && x.IsActive == true);//all costs this supplier and subdivision (or area)
                    var costsExist = allCostsThisSupplierSubdiv.Where(x => masterItemIds.Contains(x.MasterItemId) && x.IsActive == true).ToList(); //all costs filtered for items to be updted.
                    var costsToUpdate = importCosts.Where(x => costsExist.Select(y => y.MasterItemId).Contains(x.MasterItemId)).ToList();//there could be more than one cost for same item 
                    var costsToInsert = importCosts.Where(x => !costsExist.Select(y => y.MasterItemId).Contains(x.MasterItemId)).ToList();

                    foreach (var cost in costsToInsert)
                    {
                        cost.SubdivisionId = importCosts.FirstOrDefault().SubdivisionId;
                        cost.LastCostExpired = cost.EditType == CostEditType.Current ? effectiveDate : null;
                        cost.NextCostDue = cost.EditType == CostEditType.Next ? effectiveDate : null;
                        cost.NextCost = cost.EditType == CostEditType.Next ? cost.UnitCost : null;
                        cost.UnitCost = cost.EditType == CostEditType.Current ? cost.UnitCost : null;
                    }

                    bool checkSuccessInsert = await InsertCosts(costsToInsert);//Insert the ones where there was no cost
                    await UpdateCosts(costsToUpdate, effectiveDate);//update the ones needing update
                    return Ok(new ResponseModel<List<CostModel>>() { Value = importCosts, IsSuccess = true });
                }
                else
                {
                    return Ok(new ResponseModel<List<CostModel>>() { Value = importCosts, IsSuccess = false, Message = "No costs to import" });
                }
                
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CostModel>>() { IsSuccess = false, Message = "Failed to apply costs", Value = null });
            }
        }

//        [HttpPost]
//        public async Task<IActionResult> CopyCostsToSubdivisionAsync([FromBody] CopyCostToSubdivisionModel copyCostModel)
//        {

//            //actually would need to select by plan, disabling for now
//            try
//            {
//                //first check if cost exists for the subdivision, item, and supplier, if not insert new    
//                //costs in the copy from subidivsion
//                var findCostsFromSubdivision = _context.Costs.Include(x => x.CostsHistories).Where(x => x.SubdivisionId == copyCostModel.FromSubdivisionId && x.IsActive == true).ToList();
//                //costs in the copy to Subdivision                   
//                var findCostsToSubdivision = _context.Costs.Where(x => x.SubdivisionId == copyCostModel.ToSubdivisionId && x.IsActive == true).ToList();

//                //these should be the ones that existed in the to subdivision, hence need to update, not add new
//                var costsToUpdate = from costs in findCostsFromSubdivision
//                                    join cost2 in findCostsToSubdivision
//                                    on new { costs.MasterItemId, costs.SubNumber } equals new { cost2.MasterItemId, cost2.SubNumber }
//                                    select new CostModel()
//                                    {
//                                        CostsId = cost2.CostsId,
//                                        SubdivisionId = cost2.SubdivisionId,
//                                        MasterItemId = cost2.MasterItemId,
//                                        LastCost1 = costs.LastCost1,
//                                        LastCost2 = costs.LastCost2,
//                                        LastCost3 = costs.LastCost3,
//                                        LastCost2Expired = costs.LastCost2Expired,
//                                        LastCost3Expired = costs.LastCost3Expired,
//                                        LastCostExpired = costs.LastCostExpired,
//                                        UnitCost = costs.UnitCost,
//                                        NextCost = costs.NextCost,
//                                        NextCost2 = costs.NextCost2,
//                                        NextCostDue = costs.NextCostDue,
//                                        NextCost2Due = costs.NextCostDue,
//                                        SubNumber = costs.SubNumber,
//                                        UpdatedBy = User.Identity.Name.Split('@')[0],
//                                        UpdatedDateTime = DateTime.Now
//                                    };

//                //below should be the ones where the cost did not exist in the to subdivision, so add them
//                var costsToInsert = findCostsFromSubdivision.Where(x => !costsToUpdate.Select(y => new { y.MasterItemId, y.SubNumber }).Contains(new { x.MasterItemId, x.SubNumber })).Select(x => new CostModel()
//                {
//                    SubdivisionId = copyCostModel.ToSubdivisionId,
//                    MasterItemId = x.MasterItemId,
//                    LastCost1 = x.LastCost1,
//                    LastCost2 = x.LastCost2,
//                    LastCost3 = x.LastCost3,
//                    LastCost2Expired = x.LastCost2Expired,
//                    LastCost3Expired = x.LastCost3Expired,
//                    LastCostExpired = x.LastCostExpired,
//                    UnitCost = x.UnitCost,
//                    NextCost = x.NextCost,
//                    NextCost2 = x.NextCost2,
//                    NextCostDue = x.NextCostDue,
//                    NextCost2Due = x.NextCostDue,
//                    SubNumber = x.SubNumber,
//                    CreatedBy = User.Identity.Name.Split('@')[0],
//                    CreatedDateTime = DateTime.Now
//                }).ToList();
//                //TODO: insert the history if needed
                
//                bool success = await InsertCosts(costsToInsert);

//                await UpdateCopyCosts(costsToUpdate.ToList());//update the ones needing update 

//                //return the copied costs.
//                var responseCostsCopied = _mapper.Map<List<CostDto>>(findCostsFromSubdivision);
//                var responseModel = new ResponseModel<List<CostDto>>() { Value = responseCostsCopied, Message = "Success", IsSuccess = true };
//                return Ok(responseModel);
//            }
//            catch (Exception ex)
//            {
//#if DEBUG
//                _logger.Debug(ex);
//#else
//                _logger.Error(ex);
//#endif
//                return StatusCode(500, new ResponseModel<List<CostDto>>() { IsSuccess = false, Message = "Failed to copy costs to subdivision", Value = null });
//            }
//        }
        private async Task UpdateCopyCosts(List<CostModel> costsToUpdate)
        {
            try
            {
                //TODO: update history if needed

                var sessionId = Guid.NewGuid();

                //insert costs to update to temp table
                await InsertTempCosts(costsToUpdate, sessionId);
                //update the real ones from the temp table
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    var query = $"Update  c SET c.LAST_COST_3 = c2.LAST_COST_3, c.LAST_COST_2 = c2.LAST_COST_2, c.LAST_COST_1 = c2.LAST_COST_1, c.LAST_COST_3_EXPIRED = c2.LAST_COST_3_EXPIRED, c.LAST_COST_2_EXPIRED = c2.LAST_COST_2_EXPIRED, c.LAST_COST_EXPIRED = c2.LAST_COST_EXPIRED, c.UNIT_COST = c2.UNIT_COST, c.UpdatedBy = c2.CreatedBy FROM xxxCOSTS c2 INNER JOIN COSTS c on c.COSTS_ID = c2.COSTS_ID where c2.SessionId = '{sessionId}'";

                    var command = new SqlCommand(query, connection);
                    await command.ExecuteNonQueryAsync();
                    //truncate the temp table
                    var deleteQuery = $"DELETE FROM xxxCOSTS where SessionId = '{sessionId}'";
                    var deleteCommand = new SqlCommand(deleteQuery, connection);
                    await deleteCommand.ExecuteNonQueryAsync();
                }
            }
            catch (Exception ex)
            {
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif
            }
        }

        private async Task UpdateCosts(List<CostModel> costsToUpdate, DateTime effectiveDate)
        {
            try
            {
                //update the ones to update
                var subNubmer = costsToUpdate.FirstOrDefault()?.SubNumber;
                var costEditType = costsToUpdate.FirstOrDefault()?.EditType ?? CostEditType.Current;
                var subdivisionId = costsToUpdate.FirstOrDefault()?.SubdivisionId;
                var findCosts = from x in _context.Costs.Where(x => x.IsActive == true && x.SubNumber == subNubmer && x.SubdivisionId == subdivisionId).ToList()
                                join y in costsToUpdate on x.MasterItemId equals y.MasterItemId
                                select x;
                var updateCosts = from x in _context.Costs.Where(x => x.IsActive == true && x.SubNumber == subNubmer && x.SubdivisionId == subdivisionId).ToList()
                                  join y in costsToUpdate on x.MasterItemId equals y.MasterItemId
                                  select new CostModel()
                                  {
                                      CostsId = x.CostsId,
                                      MasterItemId = x.MasterItemId,
                                      NextCostDue = effectiveDate,
                                      NextCost = y.UnitCost,//if updating next cosst
                                      UnitCost = y.UnitCost,
                                      LastCost3 = x.LastCost2,//shifting the costs to last cost, etc //TODO: fix for not updating current
                                      LastCost2 = x.LastCost1,
                                      LastCost1 = x.UnitCost,
                                      LastCost3Expired = x.LastCost2Expired,
                                      LastCost2Expired = x.LastCostExpired,
                                      LastCostExpired = effectiveDate,
                                      UpdatedDateTime = DateTime.Now,
                                      UpdatedBy = User.Identity.Name.Split('@')[0]
                                  };

                var addToHistory = new List<CostsHistory>();
                if (costEditType == CostEditType.Next)//next cost 
                {
                    //There shouldn't be any need to update the history, since this is updating a next cost, not pushing the last cost to last cost 2 etc. 
                    var sessionId = Guid.NewGuid();
                    await InsertTempCosts(updateCosts, sessionId);

                    var conn = _configuration.GetConnectionString("ERPConnection");
                    using (var connection = new SqlConnection(conn))
                    {
                        connection.Open();
                        var query = $"Update c SET c.NEXT_COST = c2.UNIT_COST, c.NEXT_COST_DUE = c2.NEXT_COST_DUE, c.UpdatedBy = c2.CreatedBy FROM xxxCOSTS c2 INNER JOIN COSTS c on c.COSTS_ID = c2.COSTS_ID where c2.SessionId = '{sessionId}'";
                        var command = new SqlCommand(query, connection);
                        await command.ExecuteNonQueryAsync();
                        //delete the temp results
                        //var deleteQuery = $"DELETE FROM xxxCOSTS where SessionId = '{sessionId}'";
                        //var deleteCommand = new SqlCommand(deleteQuery, connection);
                        //await deleteCommand.ExecuteNonQueryAsync();
                    }
                }
                else //editing current cost
                {
                    foreach (var item in findCosts)
                    {
                        if (item.LastCost3 != null)
                        {
                            addToHistory.Add(new CostsHistory()
                            {
                                CostExpired = item.LastCost3Expired,
                                OldCost = item.LastCost3,
                                SubdivisionId = item.SubdivisionId,
                                SubNumber = item.SubNumber,
                                CreatedBy = User.Identity.Name.Split('@')[0],
                                CostRolledOverBy = User.Identity.Name.Split('@')[0],
                                CostsId = item.CostsId,
                                MasterItemsId = item.MasterItemId,
                            });
                        }
                    }
                    await _context.CostsHistories.BulkInsertAsync(addToHistory);
                    // bulk insert any history ones
                    var sessionId = Guid.NewGuid();
                    //var result = InsertCostsHistory(addToHistory);

                    //insert costs to update to temp table
                    await InsertTempCosts(updateCosts, sessionId);
                    //update the real ones from the temp table
                    var conn = _configuration.GetConnectionString("ERPConnection");
                    using (var connection = new SqlConnection(conn))
                    {
                        connection.Open();
                        var query = $"Update  c SET c.LAST_COST_3 = c2.LAST_COST_3, c.LAST_COST_2 = c2.LAST_COST_2, c.LAST_COST_1 = c2.LAST_COST_1, c.LAST_COST_3_EXPIRED = c2.LAST_COST_3_EXPIRED, c.LAST_COST_2_EXPIRED = c2.LAST_COST_2_EXPIRED, c.LAST_COST_EXPIRED = c2.LAST_COST_EXPIRED, c.UNIT_COST = c2.UNIT_COST, c.UpdatedBy = c2.CreatedBy FROM xxxCOSTS c2 INNER JOIN COSTS c on c.COSTS_ID = c2.COSTS_ID where c2.SessionId = '{sessionId}'";

                        var command = new SqlCommand(query, connection);
                        await command.ExecuteNonQueryAsync();
                        //truncate the temp table
                        //var deleteQuery = $"DELETE FROM xxxCOSTS where SessionId = '{sessionId}'";
                        //var deleteCommand = new SqlCommand(deleteQuery, connection);
                        //await deleteCommand.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
            }
        }

        private bool InsertCostsHistory(List<CostsHistory> costsHistoriesToInsert)
        {
            try
            {
                DataTable HistoryDataTable = new DataTable("CostsHistory");
                DataColumn CostsId = new DataColumn("CostsId", typeof(int));
                HistoryDataTable.Columns.Add(CostsId);
                DataColumn MasterItemId = new DataColumn("MasterItemId", typeof(int));
                HistoryDataTable.Columns.Add(MasterItemId);
                DataColumn SubNumber = new DataColumn("SubNumber", typeof(int));
                HistoryDataTable.Columns.Add(SubNumber);
                DataColumn SubdivisionId = new DataColumn("SubdivisionId", typeof(int));
                HistoryDataTable.Columns.Add(SubdivisionId);
                DataColumn CostExpired = new DataColumn("CostExpired", typeof(DateTime));
                HistoryDataTable.Columns.Add(CostExpired);
                DataColumn OldCost = new DataColumn("OldCost", typeof(decimal));
                HistoryDataTable.Columns.Add(OldCost);
                DataColumn CostRolledOverBy = new DataColumn("CostRolledOverBy", typeof(string));
                HistoryDataTable.Columns.Add(CostRolledOverBy);
                DataColumn CreatedBy = new DataColumn("CreatedBy", typeof(string));
                HistoryDataTable.Columns.Add(CreatedBy);

                string createBy = User.Identity.Name.Split('@')[0];
                foreach (var cost in costsHistoriesToInsert)
                {
                    HistoryDataTable.Rows.Add(cost.CostsId, cost.MasterItemsId, cost.SubNumber, cost.SubdivisionId, cost.CostExpired, cost.OldCost, createBy, createBy);
                }
                //do the bulk insert 
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    using (var bulkCopy = new SqlBulkCopy(connection))
                    {
                        bulkCopy.DestinationTableName = "COSTS_HISTORY";
                        bulkCopy.ColumnMappings.Add("CostsId", "COSTS_ID");
                        bulkCopy.ColumnMappings.Add("MasterItemId", "MASTER_ITEMS_ID");
                        bulkCopy.ColumnMappings.Add("SubNumber", "SUB_NUMBER");
                        bulkCopy.ColumnMappings.Add("SubdivisionId", "SUBDIVISION_ID");
                        bulkCopy.ColumnMappings.Add("OldCost", "OLD_COST");
                        bulkCopy.ColumnMappings.Add("CostExpired", "COST_EXPIRED");
                        bulkCopy.ColumnMappings.Add("CreatedBy", "CreatedBy");
                        bulkCopy.ColumnMappings.Add("CostRolledOverBy", "COST_ROLLED_OVER_BY");
                        try
                        {
                            bulkCopy.WriteToServer(HistoryDataTable);
                        }
                        catch (Exception ex)
                        {
                            var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                            _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                        }
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return false;
            }
        }

        private async Task<bool> InsertCosts(IEnumerable<CostModel> costsToInsert)
        {
            try
            {
                DataTable CostsDataTable = new DataTable("Costs");
                DataColumn MasterItemId = new DataColumn("MasterItemId", typeof(int));
                CostsDataTable.Columns.Add(MasterItemId);
                DataColumn SubNumber = new DataColumn("SubNumber", typeof(int));
                CostsDataTable.Columns.Add(SubNumber);
                DataColumn SubdivisionId = new DataColumn("SubdivisionId", typeof(int));
                CostsDataTable.Columns.Add(SubdivisionId);
                DataColumn LastCost = new DataColumn("LastCost", typeof(decimal));
                CostsDataTable.Columns.Add(LastCost);
                DataColumn LastCost2 = new DataColumn("LastCost2", typeof(decimal));
                CostsDataTable.Columns.Add(LastCost2);
                DataColumn LastCost3 = new DataColumn("LastCost3", typeof(decimal));
                CostsDataTable.Columns.Add(LastCost3);
                DataColumn LastCostExpired = new DataColumn("LastCostExpired", typeof(DateTime));
                CostsDataTable.Columns.Add(LastCostExpired);
                DataColumn LastCost2Expired = new DataColumn("LastCost2Expired", typeof(DateTime));
                CostsDataTable.Columns.Add(LastCost2Expired);
                DataColumn LastCost3Expired = new DataColumn("LastCost3Expired", typeof(DateTime));
                CostsDataTable.Columns.Add(LastCost3Expired);
                DataColumn UnitCost = new DataColumn("UnitCost", typeof(decimal));
                CostsDataTable.Columns.Add(UnitCost);
                DataColumn NextCost = new DataColumn("NextCost", typeof(decimal));
                CostsDataTable.Columns.Add(NextCost);
                DataColumn NextCostDue = new DataColumn("NextCostDue", typeof(string));
                CostsDataTable.Columns.Add(NextCostDue);
                DataColumn CreatedBy = new DataColumn("CreatedBy", typeof(string));
                CostsDataTable.Columns.Add(CreatedBy);

                string createBy = User.Identity.Name.Split('@')[0];
                foreach (var cost in costsToInsert)
                {
                    CostsDataTable.Rows.Add(cost.MasterItemId, cost.SubNumber, cost.SubdivisionId, cost.LastCost1, cost.LastCost2, cost.LastCost3, cost.LastCostExpired, cost.LastCost2Expired, cost.LastCost3Expired, cost.UnitCost, cost.NextCost, cost.NextCostDue, createBy);
                }

                //do the bulk insert 
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    using (var bulkCopy = new SqlBulkCopy(connection))
                    {
                        bulkCopy.DestinationTableName = "COSTS";
                        bulkCopy.ColumnMappings.Add("MasterItemId", "MASTER_ITEM_ID");
                        bulkCopy.ColumnMappings.Add("SubNumber", "SUB_NUMBER");
                        bulkCopy.ColumnMappings.Add("SubdivisionId", "SUBDIVISION_ID");
                        bulkCopy.ColumnMappings.Add("UnitCost", "UNIT_COST");
                        bulkCopy.ColumnMappings.Add("NextCost", "NEXT_COST");
                        bulkCopy.ColumnMappings.Add("NextCostDue", "NEXT_COST_DUE");
                        bulkCopy.ColumnMappings.Add("LastCost", "LAST_COST_1");
                        bulkCopy.ColumnMappings.Add("LastCost2", "LAST_COST_2");
                        bulkCopy.ColumnMappings.Add("LastCost3", "LAST_COST_3");
                        bulkCopy.ColumnMappings.Add("LastCostExpired", "LAST_COST_EXPIRED");
                        bulkCopy.ColumnMappings.Add("LastCost2Expired", "LAST_COST_2_EXPIRED");
                        bulkCopy.ColumnMappings.Add("LastCost3Expired", "LAST_COST_3_EXPIRED");
                        bulkCopy.ColumnMappings.Add("CreatedBy", "CreatedBy");
                        try
                        {
                            bulkCopy.WriteToServer(CostsDataTable);
                        }
                        catch (Exception ex)
                        {
                            var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                            _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return false;
            }
        }

        private async Task<bool> InsertTempCosts(IEnumerable<CostModel> costsToInsert, Guid sessionId)
        {
            try
            {

                DataTable CostsDataTable = new DataTable("Costs");
                DataColumn CostsId = new DataColumn("CostsId", typeof(int));
                CostsDataTable.Columns.Add(CostsId);
                DataColumn MasterItemId = new DataColumn("MasterItemId", typeof(int));
                CostsDataTable.Columns.Add(MasterItemId);
                DataColumn SubNumber = new DataColumn("SubNumber", typeof(int));
                CostsDataTable.Columns.Add(SubNumber);
                DataColumn SubdivisionId = new DataColumn("SubdivisionId", typeof(int));
                CostsDataTable.Columns.Add(SubdivisionId);
                DataColumn LastCostExpired = new DataColumn("LastCostExpired", typeof(DateTime));
                CostsDataTable.Columns.Add(LastCostExpired);
                DataColumn LastCost2Expired = new DataColumn("LastCost2Expired", typeof(DateTime));
                CostsDataTable.Columns.Add(LastCost2Expired);
                DataColumn LastCost3Expired = new DataColumn("LastCost3Expired", typeof(DateTime));
                CostsDataTable.Columns.Add(LastCost3Expired);
                DataColumn LastCost = new DataColumn("LastCost", typeof(decimal));
                CostsDataTable.Columns.Add(LastCost);
                DataColumn LastCost2 = new DataColumn("LastCost2", typeof(decimal));
                CostsDataTable.Columns.Add(LastCost2);
                DataColumn LastCost3 = new DataColumn("LastCost3", typeof(decimal));
                CostsDataTable.Columns.Add(LastCost3);
                DataColumn UnitCost = new DataColumn("UnitCost", typeof(decimal));
                CostsDataTable.Columns.Add(UnitCost);
                DataColumn NextCost = new DataColumn("NextCost", typeof(decimal));
                CostsDataTable.Columns.Add(NextCost);
                DataColumn NextCostDue = new DataColumn("NextCostDue", typeof(DateTime));
                CostsDataTable.Columns.Add(NextCostDue);
                DataColumn CreatedBy = new DataColumn("CreatedBy", typeof(string));
                CostsDataTable.Columns.Add(CreatedBy);
                DataColumn CreatedDateTime = new DataColumn("CreatedDateTime", typeof(DateTime));
                CostsDataTable.Columns.Add(CreatedDateTime);
                DataColumn IsActive = new DataColumn("IsActive", typeof(bool));
                CostsDataTable.Columns.Add(IsActive);
                DataColumn SessionId = new DataColumn("SessionId", typeof(string));
                CostsDataTable.Columns.Add(SessionId);
                string createBy = User.Identity.Name.Split('@')[0];
                foreach (var cost in costsToInsert)
                {
                    CostsDataTable.Rows.Add(cost.CostsId, cost.MasterItemId, cost.SubNumber, cost.SubdivisionId,cost.LastCostExpired, cost.LastCost2Expired, cost.LastCost3Expired, cost.LastCost1, cost.LastCost2, cost.LastCost3, cost.UnitCost, cost.NextCost, cost.NextCostDue, createBy, DateTime.Now, true, sessionId.ToString());
                }

                //do the bulk insert 
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    using (var bulkCopy = new SqlBulkCopy(connection))
                    {
                        bulkCopy.DestinationTableName = "xxxCOSTS";
                        bulkCopy.ColumnMappings.Add("CostsId", "COSTS_ID");
                        bulkCopy.ColumnMappings.Add("MasterItemId", "MASTER_ITEM_ID");
                        bulkCopy.ColumnMappings.Add("SubNumber", "SUB_NUMBER");
                        bulkCopy.ColumnMappings.Add("SubdivisionId", "SUBDIVISION_ID");
                        bulkCopy.ColumnMappings.Add("UnitCost", "UNIT_COST");
                        bulkCopy.ColumnMappings.Add("NextCost", "NEXT_COST");
                        bulkCopy.ColumnMappings.Add("NextCostDue", "NEXT_COST_DUE");
                        bulkCopy.ColumnMappings.Add("LastCost", "LAST_COST_1");
                        bulkCopy.ColumnMappings.Add("LastCost2", "LAST_COST_2");
                        bulkCopy.ColumnMappings.Add("LastCost3", "LAST_COST_3");
                        bulkCopy.ColumnMappings.Add("LastCostExpired", "LAST_COST_EXPIRED");
                        bulkCopy.ColumnMappings.Add("LastCost2Expired", "LAST_COST_2_EXPIRED");
                        bulkCopy.ColumnMappings.Add("LastCost3Expired", "LAST_COST_3_EXPIRED");
                        bulkCopy.ColumnMappings.Add("CreatedBy", "CreatedBy");
                        bulkCopy.ColumnMappings.Add("CreatedDateTime", "CreatedDateTime");
                        bulkCopy.ColumnMappings.Add("IsActive", "IsActive");
                        bulkCopy.ColumnMappings.Add("SessionId", "SessionId");
                        try
                        {
                            bulkCopy.WriteToServer(CostsDataTable);
                        }
                        catch (Exception ex)
                        {
                            var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                            _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return false;
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateCostAsync([FromBody] CostModel costToUpdate)
        {                        
            try
            {
                var findCost = await _context.Costs.SingleOrDefaultAsync(x => x.CostsId == costToUpdate.CostsId);
                //If editing by current costs, move the current costs to the last costs,
                if (findCost.UnitCost != costToUpdate.UnitCost || findCost.LastCostExpired != costToUpdate.LastCostExpired)//current cost being edited
                {
                    if (findCost.LastCost3 != null)
                    {
                        //if there's more than 3 last costs, need to write to the history table
                        var newCostHistory = new CostsHistory()
                        {
                            CostsId = costToUpdate.CostsId,
                            MasterItemsId = costToUpdate.MasterItemId,
                            SubNumber = costToUpdate.SubNumber,
                            SubdivisionId = costToUpdate.SubdivisionId,
                            OldCost = costToUpdate.LastCost3,
                            CostExpired = costToUpdate.LastCost3Expired, //fix
                            CostRolledOverBy = User.Identity.Name.Split('@')[0],
                            CreatedBy = User.Identity.Name.Split('@')[0]
                        };
                        _context.CostsHistories.Add(newCostHistory);
                        await _context.SaveChangesAsync();
                    }
                    findCost.LastCost3 = findCost.LastCost2;
                    findCost.LastCost2 = findCost.LastCost1;
                    findCost.LastCost1 = findCost.UnitCost;
                    findCost.UnitCost = costToUpdate.UnitCost;
                    findCost.LastCost3Expired = costToUpdate.LastCost2Expired;
                    findCost.LastCost2Expired = costToUpdate.LastCostExpired;
                    findCost.LastCostExpired =  DateTime.Now.Date;//2/25/25 Current cost start date is not editable, set to today
                }
                
                if (findCost.NextCost != costToUpdate.NextCost || findCost.NextCostDue != costToUpdate.NextCostDue)
                {
                    //if editing by next cost, don't change unit cost, just set the next one
                    findCost.NextCost = costToUpdate.NextCost;
                    findCost.NextCostDue = costToUpdate.NextCostDue;//TODO: validate it's in the future
                }
                //else if (costToUpdate.EditType == CostEditType.Future)//future not used for now
                //{
                //    //if editing future cost, don't change unit cost, just set the next2 one
                //    findCost.NextCost2 = costToUpdate.NextCost2;
                //    findCost.NextCost2Due = costToUpdate.NextCost2Due;
                //}

                findCost.UpdatedBy = User.Identity.Name.Split('@')[0];
                findCost.UpdatedDateTime = DateTime.Now;
                _context.Costs.Update(findCost);
                await _context.SaveChangesAsync();
                var returnCost = _mapper.Map<CostDto>(findCost);
                return Ok(new ResponseModel<CostDto>() { Value = returnCost, IsSuccess = true, Message = "Updated costs" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<CostDto>() { IsSuccess = false, Message = "Failed to update cost", Value = null });
            }

            
        }

        [HttpPut]
        public async Task<IActionResult> DeleteCostAsync([FromBody] CostModel costToUpdate)
        {
            try
            {
                var findCost = await _context.Costs.SingleOrDefaultAsync(x => x.CostsId == costToUpdate.CostsId);
                findCost.IsActive = false;
                findCost.UpdatedBy = User.Identity.Name.Split('@')[0];
                findCost.UpdatedDateTime = DateTime.Now;
                _context.Costs.Update(findCost);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<CostModel>() { Message = "Deleted cost", IsSuccess = true, Value = costToUpdate });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<bool>() { IsSuccess = false, Message = "Failed to delete cost", Value = false });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteCostsAsync([FromBody] List<CostModel> costsToUpdate)
        {
            //bulk update multiple costs
            var findCosts = _context.Costs.Where(x => costsToUpdate.Select(y => y.CostsId).Contains(x.CostsId));
            var updateBy = User.Identity.Name.Split('@')[0];
            await findCosts.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));
            return Ok(new ResponseModel<List<CostModel>>() { Message = "Deleted costs", IsSuccess = true, Value = costsToUpdate});
        }


        [HttpPost]
        public async Task<IActionResult> DownloadCostsExport([FromBody] SupplierandActivitiesListSelectModel model)
        {
            try
            {
                var costs = await GetAllCostsForSupplierAndActivitiesForDateAsync(model);
                var author = User.Identity.Name.Split('@')[0];
                var myFile = File(_service.CreateCostsExport((List<CostModel>) costs, author), contentType, fileName);
                var fileBytes = myFile.FileContents;
                return Ok(new ResponseModel<byte[]>() { Value = fileBytes, IsSuccess = true });//returns the byte array, won't work for large files
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<byte[]>() { IsSuccess = false, Message = "Failed to download costs export", Value = null });
            }
        }
    }    
}
