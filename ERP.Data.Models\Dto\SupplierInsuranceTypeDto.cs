﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class SupplierInsuranceTypeDto: IMapFrom<SupplierInsuranceTypeDto>
{
    public string? InsuranceTypeCode { get; set; } 

    public string? Description { get; set; }

    public DateTime Createddatetime { get; set; }

    public string? Createdby { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? Updatedby { get; set; }

    public bool? IsActive { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<SupplierInsuranceTypeDto, SupplierInsuranceType>().ReverseMap();
    }

    // public byte[] RecordTimeStamp { get; set; } = null!;

    // public virtual ICollection<SupplierInsurance> SupplierInsurances { get; set; } = new List<SupplierInsurance>();
}
