﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class AsmHeader
{
    public int AsmHeaderId { get; set; }

    public int? MasterPlanId { get; set; }

    public int? AsmGroupId { get; set; }

    public int? MasterOptionId { get; set; }

    public int? HomeAreaId { get; set; }

    public int? PeHeader { get; set; }

    public string? AssemblyCode { get; set; }

    public string? AssemblyDesc { get; set; }

    public string? AssemblyNotes { get; set; }

    public string? AssemblyUnit { get; set; }

    public string? DeletedFromPe { get; set; }

    public string? Formula { get; set; }

    public string? Calculation { get; set; }

    public int? EstDbOwner { get; set; }

    public int? TlpeOptionCategoryId { get; set; }

    public int? OptionScope { get; set; }

    public int? AssemblySize { get; set; }

    public string? IsElevation { get; set; }

    public string? IsBaseHouse { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? PeDatetimestamp { get; set; }

    public string? PeUpdated { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual ICollection<AsmDetail> AsmDetails { get; set; } = new List<AsmDetail>();

    public virtual HomeArea? HomeArea { get; set; }

    public virtual ICollection<MasterItemPhasis> MasterItemPhases { get; set; } = new List<MasterItemPhasis>();

    public virtual MasterOption? MasterOption { get; set; }

    public virtual MasterPlan? MasterPlan { get; set; }
}
