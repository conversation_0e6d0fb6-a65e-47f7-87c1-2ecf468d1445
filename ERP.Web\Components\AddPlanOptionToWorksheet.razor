﻿@using ERP.Data.Models.Dto;
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject PlanService PlanService
@inject SalesPriceService SalesPriceService
<style>
   /* .k-input-values{
        overflow-y:auto;
    }*/
</style>
<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               MaxHeight="600px"
               MaxWidth="600px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <h4 class="page-title">Add Plan/Options to Selected Worksheet</h4>
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@PlanToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />
            <label class="form-label">Subdivision</label>
            <TelerikDropDownList Data="@AllSubdivisions"
                                 @bind-Value="@PlanToAdd.SubdivisionId"
                                 DefaultText="Select A Subdivision"
                                 ScrollMode="@DropDownScrollMode.Virtual"
                                 TextField="SubdivisionName"
                                 ValueField="SubdivisionId"
                                 PageSize="10"
                                 Filterable="true"
                                 ItemHeight="35"
                                 OnChange="@OnSubdivisionChoiceChangeHandler"
                                 FilterOperator="@StringFilterOperator.Contains"
                                 Class="dropdownControl">
                <DropDownListSettings>
                    <DropDownListPopupSettings Height="350px"></DropDownListPopupSettings>
                </DropDownListSettings>
            </TelerikDropDownList>
            <br />                      
            <label class="form-label">Plan</label>
            <TelerikDropDownList Data="@PhasePlans"
                                 @bind-Value="@PlanToAdd.PlanId"
                                 TextField="PlanName"
                                 ValueField="PhasePlanId"
                                 DefaultText="Select A Plan"
                                 ScrollMode="@DropDownScrollMode.Virtual"
                                 PageSize="10"
                                 Filterable="true"
                                 ItemHeight="35"
                                 OnChange="@OnPlanChoiceChangeHandler"
                                 FilterOperator="@StringFilterOperator.Contains"
                                 Class="dropdownControl">
                <DropDownListSettings>
                    <DropDownListPopupSettings Height="350px"></DropDownListPopupSettings>
                </DropDownListSettings>
            </TelerikDropDownList>

            <br />
            <label class="form-label">Options</label>
            <TelerikMultiSelect Data="@Options"
                                Context="multiSelectContext"
                                 @bind-Value="@OptionsToAdd"
                                 @ref="MultiSelectRef"
                                 TextField="ModifiedOptionDesc"
                                 ValueField="PlanOptionId"
                                 Placeholder="Select An Option"
                                 ScrollMode="@DropDownScrollMode.Virtual"
                                 PageSize="10"
                                 AutoClose="true"
                                 Filterable="true"
                                 ItemHeight="35"
                                TagMode="@MultiSelectTagMode.Multiple"
                                MaxAllowedTags="5"
                                 FilterOperator="@StringFilterOperator.Contains"
                                 Class="dropdownControl">
                                                                         
                                 <HeaderTemplate>                                            
                                     <label style="padding: 4px 8px;">                                                
                                         <TelerikCheckBox TValue="bool"                                                                 
                                             Value="@IsAllSelected()"                                                                 
                                             ValueChanged="@( (bool v) => ToggleSelectAll(v) )">                                                
                                         </TelerikCheckBox>                                               
                                         &nbsp;Select All                                        
                                     </label>                                     
                                 </HeaderTemplate>                                        
                                 <ItemTemplate>                                 
                                     <input type="checkbox"                                     
                                         class="k-checkbox k-checkbox-md"                                         
                                         checked="@GetChecked(multiSelectContext.PlanOptionId)">                                         
                                         @multiSelectContext.ModifiedOptionDesc                                        
                                     </ItemTemplate>
            </TelerikMultiSelect>
            <div style="@ShowLoading">Adding. Please wait...</div>
            <div class="customSeparator">
                <button type="submit" class="btn btn-primary">Add</button>
                <button type="button" @onclick="CancelAddPlan" class="btn btn-secondary">Cancel</button>
            </div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public WorksheetTreeModel PlanToAdd { get; set; } = new WorksheetTreeModel();
    public List<PhasePlanModel>? PhasePlans;
    public List<SubdivisionDto>? AllSubdivisions;
    public List<AvailablePlanOptionDto>? Options = new List<AvailablePlanOptionDto>();
    public string? SubdivisionName { get; set; }
    private TelerikMultiSelect<AvailablePlanOptionDto, int>? MultiSelectRef;
    public List<int>? OptionsToAdd { get; set; } = new List<int>();
    private int? planIdSelected { get; set; }
    private int? subdivIdSelected { get; set; }
    public string ShowLoading { get; set; } = "display:none";
    public string ShowError { get; set; } = "display:none";
    public int SelectedSubdivisionId { get; set; }

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    [Parameter]
    public int SelectedWorksheetId {get; set;}

    [Parameter]
    public EventCallback<ResponseModel<List<WorksheetOptModel>>> HandleAddSubmit { get; set; }

    public async Task Show()
    {
        IsModalVisible = true;
        var data = await SubdivisionService.GetSubdivisionsAsync();
        AllSubdivisions = data.Value;       
        StateHasChanged();
    }  
    private async void OnSubdivisionChoiceChangeHandler(object newVal)
    {
        if (subdivIdSelected != PlanToAdd.SubdivisionId)//This check added to prevent double calls to the database, since the onchange handler is called both on selection change and on blur
        {
            //get options in plan
            if (PlanToAdd?.SubdivisionId != null)
            {
                var phasePlansResult = await PlanService.GetPhasePlansAsync((int)PlanToAdd.SubdivisionId);
                PhasePlans = phasePlansResult.Value; // not null as returning empty list in case of failure
                StateHasChanged();
            }
            subdivIdSelected = PlanToAdd?.SubdivisionId;
        }
    }

    private async void OnPlanChoiceChangeHandler(object newVal)
    {

        if (planIdSelected != PlanToAdd.PlanId)//This check added to prevent double calls to the database, since the onchange handler is called both on selection change and on blur
        {
            //get options in plan
            if (PlanToAdd?.PlanId != null)
            {
                Options = (await SalesPriceService.GetAvailablePlanOptionsByPlanAsync((int)PlanToAdd.PlanId)).Value;
                StateHasChanged();
            }
            planIdSelected = PlanToAdd?.PlanId;
        }       
    }
    private async void HandleValidAddSubmit()
    {
        ShowLoading = "";
        var plansModel = new List<WorksheetPlanModel>();
        plansModel.Add(new WorksheetPlanModel()
            {
                WorksheetId = SelectedWorksheetId,
                PhasePlanId = (int)PlanToAdd.PlanId
            });
        var addPlanResponse = await SalesPriceService.AddPlansToWorksheetAsync(plansModel);

        if (addPlanResponse.IsSuccess)
        {
            //TODO: require some selection
            var optionsModel = new List<WorksheetOptModel>();
            foreach (var optionId in OptionsToAdd)
            {
                optionsModel.Add(new WorksheetOptModel()
                    {
                        WorksheetId = SelectedWorksheetId,
                        PlanOptionId = optionId,
                        PhasePlanId = (int)PlanToAdd.PlanId
                    });
            }

            var addOptionsResponse = await SalesPriceService.AddOptionsToWorksheetAsync(optionsModel);

            if (addOptionsResponse.IsSuccess)
            {
                ShowLoading = "display:none";
                await HandleAddSubmit.InvokeAsync(addOptionsResponse);
            }
            else
            {
                await Dialogs.AlertAsync(addPlanResponse.Message);
            }
        }
        else
        {
            await Dialogs.AlertAsync(addPlanResponse.Message);
        }

    }
    async void CancelAddPlan()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;
    }
    bool IsAllSelected()
    {
        return OptionsToAdd.Count == Options.Count;

        // in this example we do a simple count check for performance
        // all items in the dropdown should be in the data anyway
        // caveat: virtualization does not work that way, but for it selecting all
        // would be a completely different feature anyway that will require asking the server for data
        // so it is beyond the scope of this article as it depends heavily on the use case and needs
    }
    // for the item checkboxes
    bool GetChecked(int id)
    {
        return OptionsToAdd.Contains(id);
    }
    void ToggleSelectAll(bool selectAll)
    {
        OptionsToAdd.Clear();

        if (selectAll)
        {
            OptionsToAdd.AddRange(Options.Select(x => x.PlanOptionId));
        }

        MultiSelectRef.Rebind();
    }
}
