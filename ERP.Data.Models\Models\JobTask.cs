﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class JobTask
{
    public string JobNumber { get; set; } = null!;

    public string JobTaskNo { get; set; } = null!;

    public string Description { get; set; } = null!;

    public int JobTaskType { get; set; }

    public string JobPostingGroup { get; set; } = null!;

    public string? CostCode { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual Job JobNumberNavigation { get; set; } = null!;
}
