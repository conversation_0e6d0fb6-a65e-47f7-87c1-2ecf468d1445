﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class SupplierInsuranceType
{
    public string InsuranceTypeCode { get; set; } = null!;

    public string Description { get; set; } = null!;

    public DateTime Createddatetime { get; set; }

    public string? Createdby { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? Updatedby { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual ICollection<SupplierInsurance> SupplierInsurances { get; set; } = new List<SupplierInsurance>();
}
