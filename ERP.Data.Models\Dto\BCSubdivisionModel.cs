﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    //Job with just fields needed for BC
    public class BCSubdivisionModel
    {
        public int SubdivisionId { get; set; }

        [Required(ErrorMessage = "The Subdivision Name field is required")]
        public string? SubdivisionName { get; set; }

        [Required(ErrorMessage = "The Subdivision Num field is required")]
        public string? SubdivisionNum { get; set; }

        public DateTime CreatedDateTime { get; set; }

        public string? CreatedBy { get; set; }

        public DateTime? UpdatedDateTime { get; set; }

        public string? UpdatedBy { get; set; }

        public bool? IsActive { get; set; }

        public bool? LinkWithErp { get; set; }
        public bool? Blocked { get; set; }
        //public bool BoolBlocked { get; set; }
       
    }

}
