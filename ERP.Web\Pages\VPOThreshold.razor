﻿@page "/vpothreshold"
@using ERP.Data.Models
@using Telerik.DataSource.Extensions
@using Telerik.SvgIcons
@inject PoService PoService
@inject SubdivisionService SubdivisionService

<style type="text/css">
    /* Gutter */
    .row > * {
    padding-right: calc(var(--bs-gutter-x) * .10);
    padding-left: calc(var(--bs-gutter-x) * .10);
    }
</style>

<PageTitle>VPO Threshold Levels</PageTitle>

<div class="container-fluid flex">
    <div class="col-lg-12">
        <div class="card" style="background-color: #2e5771">
            <div class="card-body" style="padding:0.5rem">
                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">VPO Threshold Levels</h7>
            </div>
        </div>
    </div>

    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item active">VPO Threshold Levels</li>
    </ol>

    <TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

    <div class="row-d-flex">
        <div class="col-lg-12">

            @if (VpoGroupsData == null)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingOptions />
            }
            else
            {
                <TelerikGrid Data="@VpoGroupsData"
                TItem="@VpoGroupDto"
                OnStateInit="@OnVpoGroupDtoGridStateInit"
                @ref="@VpoGroupsGridRef">
                    <GridColumns>
                        <GridColumn Field="GroupDesc" Title="Description"></GridColumn>
                    </GridColumns>
                    <DetailTemplate Context="vpoGroupsItem">
                        @{
                            VpoGroupDto vpoGroup = (VpoGroupDto)vpoGroupsItem;

                            <TelerikGrid Data="@vpoGroup.VpoApprovalSeqs"
                            OnDelete="Delete"
                            EditMode="@GridEditMode.Popup">
                                <GridToolBarTemplate>
                                @if(AllowEdit)
                                {
                                        <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Sequence</GridCommandButton>
                                }
                                    
                                    <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                </GridToolBarTemplate>
                                <GridSettings>
                                    <GridPopupEditSettings Width="550px" MaxHeight="95vh" MaxWidth="95vw"></GridPopupEditSettings>
                                    <GridPopupEditFormSettings Context="VpoApprovalSeqsContext">
                                        <FormTemplate>
                                            @{
                                                EditVpoApprovalSeq = VpoApprovalSeqsContext.Item as VpoApprovalSeqDto;
                                                EditVpoApprovalSeq.VpoGroupId = vpoGroup.VpoGroupId;
                                                <TelerikForm Model="@EditVpoApprovalSeq"
                                                ColumnSpacing="10px"
                                                Columns="1"
                                                ButtonsLayout="@FormButtonsLayout.Stretch"
                                                OnValidSubmit="@OnValidSubmit">
                                                    <FormItems>
                                                        <FormItem Field="LevelCode" LabelText="Level Code" Enabled="true">
                                                            <Template>
                                                                <label for="LevelCode">Level Code</label>
                                                                <TelerikDropDownList Data="@vpoApprovalHierarchies"
                                                                DefaultText="Select LevelCode"
                                                                TextField="LevelDesc" ValueField="LevelCode"
                                                                @bind-Value="@EditVpoApprovalSeq.LevelCode"
                                                                Id="LevelCode">
                                                                </TelerikDropDownList>
                                                            </Template>
                                                        </FormItem>
                                                        <FormItem>
                                                            <Template>
                                                                <label for="seq">Sequence</label>
                                                                <TelerikDropDownList Data="@SequenceData"
                                                                DefaultText="Select Sequence"
                                                                TextField="SequenceText" ValueField="SequenceValue"
                                                                @bind-Value="@EditVpoApprovalSeq.Seq"
                                                                Id="seq">
                                                                </TelerikDropDownList>
                                                            </Template>
                                                        </FormItem>
                                                        <FormItem Field="Threshold" LabelText="Threshold" Enabled="true"></FormItem>
                                                        <FormItem>
                                                            <Template>
                                                                <label for="user">Users</label>
                                                                <TelerikDropDownList Data="@AllUsers"
                                                                @bind-Value="@EditVpoApprovalSeq.EmpUserId"
                                                                TextField="FullName"
                                                                ValueField="UserId"
                                                                DefaultText="Select User"
                                                                Width="100%">
                                                                </TelerikDropDownList>
                                                            </Template>
                                                        </FormItem>
                                                        <FormItem>
                                                            <Template>
                                                                <label for="role">Role</label>
                                                                <TelerikDropDownList Data="@AllRoles"
                                                                @bind-Value="@EditVpoApprovalSeq.RoleId"
                                                                TextField="RoleName"
                                                                ValueField="RoleId"
                                                                DefaultText="Select Role"
                                                                Width="100%">
                                                                </TelerikDropDownList>
                                                            </Template>
                                                        </FormItem>
                                                    </FormItems>
                                                </TelerikForm>
                                            }
                                        </FormTemplate>
                                    </GridPopupEditFormSettings>
                                </GridSettings>
                                <GridColumns>
                                    <GridColumn Field="Seq" Title="Sequence" />
                                    <GridColumn Field="LevelCode" Title="Level Code" />
                                    <GridColumn Field="Threshold" Title="Threshold" DisplayFormat="{0:c0}" />
                                    <GridColumn Field="EmpUserId" Title="Employee" />
                                    <GridColumn Field="RoleDescription" Title="Role" />
                                    <GridCommandColumn>
                                    @if(AllowEdit)
                                    {
                                            <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                            <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                            <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                            <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                                    }
                                    </GridCommandColumn>
                                </GridColumns>
                            </TelerikGrid>
                        }
                    </DetailTemplate>
                </TelerikGrid>
            }

        </div>
    </div>
</div>

@code {
    public List<VpoGroupDto>? VpoGroupsData { get; set; }
    public List<VpoApprovalHierarchyDto>? vpoApprovalHierarchies { get; set; }
    private TelerikGrid<VpoGroupDto>? VpoGroupsGridRef { get; set; }
    private VpoApprovalSeqDto EditVpoApprovalSeq { get; set; }   
    private IEnumerable<SequenceModel> SequenceData { get; set; }
    public List<UserDto>? AllUsers;
    public List<RoleDto>? AllRoles;

    private bool AllowEdit { get; set; } = false;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    private string loadingActivityStyle = "display:none";
    private string loadingItemStyle = "display:none";
    public bool IsLoadingOptions { get; set; } = false;
    public bool IsLoadingActivity { get; set; } = false;
    public bool IsLoadingItem { get; set; } = false;

    private void OnVpoGroupDtoGridStateInit(GridStateEventArgs<VpoGroupDto> args)
    {
        args.GridState.ExpandedItems = VpoGroupsData.Where(x => x.VpoGroupId == 1).ToList();
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        IsLoadingItem = true;
        vpoApprovalHierarchies = (await PoService.GetVpoHierarchiesAsync()).Value;
        var vpoGroupsData = await PoService.GetVpoGroupsAsync();
        VpoGroupsData = vpoGroupsData.Value;

        SequenceData = Enumerable.Range(1, 10).Select(x => new SequenceModel { SequenceText = x.ToString(), SequenceValue = x });

        AllUsers = (await SubdivisionService.GetUserContactsFromAzureAsync()).Value.OrderBy(x => x.FullName).ToList();

        AllRoles = (await PoService.GetAllRolesAsync()).Value;

        IsLoadingItem = false;
    }
    //not used
    // private async void CreateSequence(GridCommandEventArgs args, VpoGroupDto vpoGroup)
    // {
    //     var item = (VpoApprovalSeqDto)args.Item;
    //     var data = await PoService.AddVpoApprovalSeqAsync(item);

    //     await ExitEditAsync();

    //     var vpoGroupsData = await PoService.GetVpoGroupsAsync();
    //     VpoGroupsData = vpoGroupsData.Value;
    // }
    private async void Delete(GridCommandEventArgs args)
    {
        var item = (VpoApprovalSeqDto)args.Item;
        var data = await PoService.DeleteVpoApprovalSeqAsync(item);

        var vpoGroupsData = await PoService.GetVpoGroupsAsync();
        VpoGroupsData = vpoGroupsData.Value;
        StateHasChanged();
        //VpoGroupsGridRef?.Rebind();
        //await ExitEditAsync();
    }
    private async Task ExitEditAsync()
    {
        var state = VpoGroupsGridRef?.GetState();
        state.OriginalEditItem = null;
        state.EditItem = null;
        state.InsertedItem = null;

        await VpoGroupsGridRef?.SetStateAsync(state);
    }

    private async Task OnValidSubmit()
    {
        VpoApprovalSeqDto result;

        if (EditVpoApprovalSeq.VpoApprovalId != 0)
        {
            result = await PoService.EditVpoApprovalSeqAsync(EditVpoApprovalSeq);
        }
        else
        {
            result = await PoService.AddVpoApprovalSeqAsync(EditVpoApprovalSeq);
        }

        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.SuccessMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        await ExitEditAsync();

        var vpoGroupsData = await PoService.GetVpoGroupsAsync();
        VpoGroupsData = vpoGroupsData.Value;
    }
}
