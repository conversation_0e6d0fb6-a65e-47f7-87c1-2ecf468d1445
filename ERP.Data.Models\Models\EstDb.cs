﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class EstDb
{
    public int EstDbId { get; set; }

    public string? EstDbDesc { get; set; }

    public string? EstDbPath { get; set; }

    public string? EstDbDefault { get; set; }

    public int? EstDbOwner { get; set; }

    public int? MasterEstDbId { get; set; }

    public string? EstDbStatus { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }
}
