﻿
using AutoMapper;
using Azure;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using DocumentFormat.OpenXml.InkML;
using ERP.API.Utilities;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Azure;
using Microsoft.Graph;
using Microsoft.Identity.Abstractions;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using NLog;
using NLog.Fluent;
using System.Data;
using System.Diagnostics;
using System.Text;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class SubdivisionController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        private readonly GraphServiceClient _graphServiceClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly IWebHostEnvironment _env;
        public SubdivisionController(IConfiguration configuration, ErpDevContext context, IMapper mapper, GraphServiceClient graphServiceClient, IDownstreamApi downstreamAPI, IWebHostEnvironment env)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _graphServiceClient = graphServiceClient;
            _downstreamAPI = downstreamAPI;
            _env = env;
        }

        [HttpGet]
        [AuthorizeForScopes(Scopes = new string[] { "user.read" })]
        public async Task<ResponseModel> DoNothingAsync()
        {
            //string licenseErrorMessage;
            //if (!Z.EntityFramework.Extensions.LicenseManager.ValidateLicense(out licenseErrorMessage))
            //{
            //    throw new Exception(licenseErrorMessage);
            //}
            //test nothing changed 
            try
            {
                //testing timeout
                //await Task.Delay(TimeSpan.FromMinutes(10));

                //var identityClientId = "cc0795f0-8790-41ae-8131-b38f65dc383c";
                //var credentialOptions = new DefaultAzureCredentialOptions
                //{
                //    ManagedIdentityClientId = identityClientId,
                //    ExcludeEnvironmentCredential = true, //did not work
                //    ExcludeWorkloadIdentityCredential = true,// did not work
                //};
                //// var newCredential = new ManagedIdentityCredential(clientId, credentialOptions);
                ////var newCredential = new ManagedIdentityCredential(clientId);
                ////var credential = new DefaultAzureCredential();
                //var credential = new DefaultAzureCredential(credentialOptions);
                //var client = new SecretClient(
                //    new Uri("https://erpkeyvault.vault.azure.net/"),
                //    credential);
                ////KeyVaultSecret keyVaultSecret = client.GetSecret("DocusignPrivateKey");
                ////KeyVaultSecret keyVaultSecret = client.GetSecret("VMlifeDocusignIntegrationPrivateKey");
                //KeyVaultSecret keyVaultSecret = client.GetSecret("VMlifeDocusignProductionPrivateKey");
                //string secretValue = keyVaultSecret.Value;
                //var privateKeyBytes = Encoding.UTF8.GetBytes(secretValue);

            }            
            catch(Exception ex)
            {
                var debug = ex.Message;
                throw;
            }
            return new ResponseModel() { IsSuccess = true, Message = "Yay" };
        }
        private async Task<string> GetBCTokenAsync()
        {

            var clientId = _configuration.GetSection("BusinessCentral:clientId").Value;
            var clientSecret = _configuration.GetSection("BusinessCentral:clientSecret").Value;
            var tenantId = _configuration.GetSection("BusinessCentral:tenantId").Value;
            var token_url = "https://login.microsoftonline.com/" + tenantId + "/oauth2/v2.0/token";

            var client = new HttpClient();

            var content = new StringContent(
                "grant_type=client_credentials" +
                "&scope=https://api.businesscentral.dynamics.com/.default" +
                "&client_id=" + System.Web.HttpUtility.UrlEncode(clientId) +
                "&client_secret=" + System.Web.HttpUtility.UrlEncode(clientSecret));

            content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");

            var response = await client.PostAsync(token_url, content);
            var tokenresponse = await response.Content.ReadAsStringAsync();
            var access_token_Object = JsonConvert.DeserializeObject<dynamic>(tokenresponse);
            var access_token = access_token_Object.access_token;

            return access_token;
        }
        [HttpGet]
       // [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetSubdivisionsAsync()
        {
            try
            {
                List<Subdivision> subdivisions = new List<Subdivision>();
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    var query = "SELECT SUBDIVISION_ID, SUBDIVISION_NAME, SUBDIVISION_NUM, MARKETING_NAME FROM dbo.SUBDIVISION WHERE IsActive = 1 and (Blocked is null or Blocked = 0) ORDER BY 2";
                    var command = new SqlCommand(query, connection);
                    var reader = await command.ExecuteReaderAsync();

                    while (reader.Read())
                    {
                        subdivisions.Add(new Subdivision()
                        {
                            SubdivisionId = reader.GetValue(0) != DBNull.Value ? Convert.ToInt32(reader.GetValue(0)) : 0,
                            SubdivisionName = reader.GetValue(1) != DBNull.Value ? (string)(reader.GetValue(1)) : "",
                            SubdivisionNum = reader.GetValue(2) != DBNull.Value ?(string)(reader.GetValue(2)) : "",
                            MarketingName = reader.GetValue(3) != DBNull.Value ? (string)(reader.GetValue(3)) : ""
                        });                       
                    }
                }
                var subdivDto = _mapper.Map<List<SubdivisionDto>>(subdivisions);
                return new OkObjectResult(new ResponseModel<List<SubdivisionDto>> { Value = subdivDto, IsSuccess = true });
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SubdivisionDto>> { IsSuccess = false, Message = "Failed to get subdivisions", Value = null });
            }

        }

        [HttpGet("{subdivisionId}")]
        public async Task<IActionResult?> GetSubdivisionAsync(int subdivisionId)
        {
            //var subdivision = new SubdivisionDto();
            //var conn = _configuration.GetConnectionString("ERPConnection");
            //using (var connection = new SqlConnection(conn))
            //{
            //    connection.Open();
            //    var query = $"SELECT SUBDIVISION_ID, SUBDIVISION_NAME, SUBDIVISION_NUM, MARKETING_NAME FROM dbo.SUBDIVISION WHERE SUBDIVISION_ID = {subdivisionId}";
            //    var command = new SqlCommand(query, connection);
            //    var reader = await command.ExecuteReaderAsync();

            //    while (reader.Read())
            //    {
            //        subdivision = new SubdivisionDto()
            //        {
            //            SubdivisionId = reader.GetValue(0) != DBNull.Value ? Convert.ToInt32(reader.GetValue(0)) : 0,
            //            SubdivisionName = reader.GetValue(1) != DBNull.Value ? (string)(reader.GetValue(1)) : "",
            //            SubdivisionNum = reader.GetValue(2) != DBNull.Value ? (string)(reader.GetValue(2)) : "",
            //            MarketingName = reader.GetValue(3) != DBNull.Value ? (string)(reader.GetValue(3)) : ""
            //            //TODO: get all the fields needed
            //        };
            //    }
            //}

            try
            {
                var subdivison = await _context.Subdivisions.SingleOrDefaultAsync(x => x.SubdivisionId == subdivisionId);
                var subdivDto = _mapper.Map<SubdivisionDto>(subdivison);
                subdivDto.IsActiveAdult = subdivison.IsActiveAdult == true;
                subdivDto.BoolBlocked = subdivDto.Blocked == true;
                return Ok(subdivDto);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return BadRequest(ex.Message);
            }
        }
        [HttpGet("{subdivisionId}")]
        public async Task<IActionResult> GetSubdivisionPhasesAsync(int subdivisionId)
        {
            try
            {
                var phases = await _context.PhasePlans.AsNoTrackingWithIdentityResolution().Where(x => x.SubdivisionId == subdivisionId).Select(x => x.Phase).Distinct().ToListAsync();
                return Ok(phases);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return BadRequest(ex.Message);
            }
        }
        [HttpPut("{includeDetails}")]
        public async Task<IActionResult> UpdateSubdivisionAsync([FromBody] SubdivisionDto updateSubdivision, bool? includeDetails)
        {
            try
            {
                var findSubdivision = await _context.Subdivisions.SingleOrDefaultAsync(x => x.SubdivisionId == updateSubdivision.SubdivisionId);
                //findSubdivision.SubdivisionName = updateSubdivision.SubdivisionName;//name and num come from nav and should not be updated
                //findSubdivision.SubdivisionNum = updateSubdivision.SubdivisionNum;
                //findSubdivision.EntityName = updateSubdivision.EntityName;//entity name from ETL

                findSubdivision.MarketingName = updateSubdivision.MarketingName;
                findSubdivision.UpdatedBy = User.Identity.Name.Split('@')[0];
                findSubdivision.UpdatedDateTime = DateTime.Now;
                if (includeDetails == true)
                {
                    findSubdivision.EntityName2 = updateSubdivision.EntityName2;//entity name 2 hould be from handshake
                    findSubdivision.EntitySignatureBlock = updateSubdivision.EntitySignatureBlock;//should be handshake
                    findSubdivision.SubdivisionStatus = updateSubdivision.SubdivisionStatus;
                    findSubdivision.Address1 = updateSubdivision.Address1;
                    findSubdivision.Address2 = updateSubdivision.Address2;
                    findSubdivision.City = updateSubdivision.City;
                    findSubdivision.State = updateSubdivision.State;
                    findSubdivision.Zip = updateSubdivision.Zip;
                    findSubdivision.County = updateSubdivision.County;
                    findSubdivision.Email = updateSubdivision.Email;
                    findSubdivision.Phone = updateSubdivision.Phone;
                    findSubdivision.Fax = updateSubdivision.Fax;
                    findSubdivision.Acreage = updateSubdivision.Acreage;
                    findSubdivision.SetupFee = updateSubdivision.SetupFee;
                    findSubdivision.HoaFees = updateSubdivision.HoaFees;
                    findSubdivision.HoaName = updateSubdivision.HoaName;
                    findSubdivision.HoaPhone = updateSubdivision.HoaPhone;                 
                    findSubdivision.IsActiveAdult = updateSubdivision.IsActiveAdult;
                }
                
                _context.Subdivisions.Update(findSubdivision);
                _context.SaveChanges();
                return new OkObjectResult(new ResponseModel<SubdivisionDto> { Value = updateSubdivision, IsSuccess = true, Message = "Updated Subdivision successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SubdivisionDto> { IsSuccess = false, Message = "Failed to update Subdivision", Value = null });
            }
        }
        /// <summary>
        /// update from bc
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SubdivisionAsync([FromBody] BCSubdivisionModel model)
        {
            try
            {
                //update or create
                var findSubdivision = await _context.Subdivisions.SingleOrDefaultAsync(x => x.SubdivisionNum == model.SubdivisionNum);
                if(findSubdivision != null)
                {
                    findSubdivision.SubdivisionName = model.SubdivisionName ?? findSubdivision.SubdivisionName;
                    findSubdivision.SubdivisionNum = model.SubdivisionNum ?? findSubdivision.SubdivisionNum;
                    //findSubdivision.EntityName = updateSubdivision.EntityName ?? findSubdivision.EntityName;
                    //findSubdivision.EntityName2 = updateSubdivision.EntityName2 ?? findSubdivision.EntityName2;
                    findSubdivision.IsActive = model.IsActive ?? findSubdivision.IsActive;                
                    findSubdivision.Blocked = model.Blocked ?? findSubdivision.Blocked;
                    findSubdivision.LinkWithErp = model.LinkWithErp ?? findSubdivision.LinkWithErp;
                    findSubdivision.IsActive =  model.LinkWithErp == false ? false : true;//deactivate if link with erp is false
                    findSubdivision.UpdatedBy = model.UpdatedBy ?? findSubdivision.UpdatedBy;
                    _context.Subdivisions.Update(findSubdivision);
                    _context.SaveChanges();
                   // var responseSubdivision = _mapper.Map<SubdivisionDto>(findSubdivision);
                    return new OkObjectResult(new ResponseModel<BCSubdivisionModel> { Value = model, IsSuccess = true, Message = "Updated Subdivision successfully" });
                }
                else
                {
                    var addSubdvision = new Subdivision()
                    {
                        SubdivisionName = model.SubdivisionName,
                        SubdivisionNum = model.SubdivisionNum,
                        LinkWithErp = model.LinkWithErp,
                        Blocked = model.Blocked,
                        CreatedBy = model.CreatedBy,
                        IsActive = model.LinkWithErp == false ? false : true//deactivate if link with erp is false
                    };
                    _context.Subdivisions.Add(addSubdvision);
                    await _context.SaveChangesAsync();

                    return new OkObjectResult(new ResponseModel<BCSubdivisionModel> { Value = model, IsSuccess = true, Message = "Created Subdivision successfully" });
                }
                
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                //log to bc api log
                string baseRequestURL = _env.IsDevelopment() ? "https://vmerpservices-dev.azurewebsites.net/api/" : "https://vmerpservices.azurewebsites.net/api/";
                var jsonItem = JsonConvert.SerializeObject(model);
                var newLog = new ErpBcApiLog()
                {
                    Method = "POST",
                    Type = "Subdivision",
                    RequestUrl = $"{baseRequestURL}subdivsion/Subdivision",
                    RequestBody = jsonItem,
                    ResponseBody = $"Failed to add or update Subdivision. Error: {ex.Message} {ex.InnerException?.Message}",
                    ResponseCode = "500",
                    CreatedBy = model?.CreatedBy,
                    CreatedDateTime = DateTime.Now,
                };
                _context.ErpBcApiLogs.Add(newLog);
                await _context.SaveChangesAsync();
                return StatusCode(500, new ResponseModel<BCSubdivisionModel> { IsSuccess = false, Message = $"Failed to create Subdivision Error: {ex.Message} {ex.InnerException?.Message}", Value = null });
            }
        }               
        [HttpPost]
        public async Task<IActionResult> AddSubdivisionAsync([FromBody] SubdivisionDto updateSubdivisionDto)
        {
            try
            {
                //won't be used, subdivision comes from bc
                //check subdiv num does not exist
                var findSubdivNum = _context.Subdivisions.Where(x => x.SubdivisionNum == updateSubdivisionDto.SubdivisionNum && x.IsActive == true).FirstOrDefault();

                if (findSubdivNum != null)
                {
                    return new OkObjectResult(new ResponseModel<SubdivisionDto> { Value = updateSubdivisionDto, IsSuccess = true , Message = "This subdivision exists already" });
                }
                else
                {
                    updateSubdivisionDto.IsActive = true;

                    var updateSubdiv = _mapper.Map<Subdivision>(updateSubdivisionDto);
                    _context.Subdivisions.Add(updateSubdiv);
                    _context.SaveChanges();
                    return new OkObjectResult(new ResponseModel<SubdivisionDto> { Value = updateSubdivisionDto, IsSuccess = true, Message = "Added Subdivision successfully"});
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SubdivisionDto> { IsSuccess = false, Message = "Failed to add Subdivision", Value = null });
            }
        }
        
        [HttpGet("{subdivisionId}")]
        public async Task<IActionResult> GetJobsAsync(int subdivisionId)
        {
            try
            {
                var conn = _configuration.GetConnectionString("ERPConnection");
                var jobsDto = new List<JobDto>();
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    var query = $"SELECT j.SUBDIVISION_ID, j.JOB_NUMBER, j.LOT_NUMBER, j.JOB_DESC, j.JOB_ADDRESS1, s.SUBDIVISION_NAME, s.SUBDIVISION_NUM, j.PHASE, j.BUILDINGNUM, j.STICKBUILINGNUM, j.JOB_POSTING_GROUP, j.LOT_WIDTH, j.LOT_SWING, j.JOB_COUNTY, j.NOTES, g.GARAGEORIENTATIONID, g.NAME, j.JOB_ADDRESS2, j.JOB_CITY, j.JOB_STATE, j.JOB_ZIP_CODE, j.LOT_SECTION_CODE, j.LOT_SIZE, j.HOME_ORIENTATION_PER_PLAN, j.PARKING_NUM, j.STORAGE_NUM, j.JOB_CONSTRUCTION_TYPE_ID, jct.DESCRIPTION, j.IsSS, j.GARAGE_NUM, j.PROJECTED_LOT_TAKEDOWN, j.PROJECTED_SALES_RELEASE_DATE, j.GENERAL_OPTION_BUDGET, j.HOMESITE_OPTION_SPEND_BUDGET, j.LOT_COST FROM dbo.JOB j join dbo.SUBDIVISION s on j.SUBDIVISION_ID = s.SUBDIVISION_ID left join dbo.GARAGE_ORIENTATION g on j.GARAGEORIENTATIONID = g.GARAGEORIENTATIONID left join dbo.JOB_CONSTRUCTION_TYPE jct on j.JOB_CONSTRUCTION_TYPE_ID = jct.JOB_CONSTRUCTION_TYPE_ID WHERE j.SUBDIVISION_ID = @subdivisionId and j.IsActive = 1";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.AddWithValue("@subdivisionId", subdivisionId); 
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        jobsDto.Add(new JobDto()
                        {
                            SubdivisionId = reader.GetValue(0) != DBNull.Value ? Convert.ToInt32(reader.GetValue(0)) : 0,
                            JobNumber = reader.GetValue(1) != DBNull.Value ? (string)(reader.GetValue(1)) : "",
                            LotNumber = reader.GetValue(2) != DBNull.Value ? (string)(reader.GetValue(2)) : "",
                            JobDesc = reader.GetValue(3) != DBNull.Value ? (string)(reader.GetValue(3)) : "",
                            JobAddress1 = reader.GetValue(4) != DBNull.Value ? (string)(reader.GetValue(4)) : "",
                            JobAddress2 = reader.GetValue(17) != DBNull.Value ? (string)(reader.GetValue(17)) : "",
                            JobCity = reader.GetValue(18) != DBNull.Value ? (string)(reader.GetValue(18)) : "",
                            JobState = reader.GetValue(19) != DBNull.Value ? (string)(reader.GetValue(19)) : "",
                            JobZipCode = reader.GetValue(20) != DBNull.Value ? (string)(reader.GetValue(20)) : "",
                            Subdivision = new SubdivisionDto()
                            {
                                SubdivisionId = reader.GetValue(0) != DBNull.Value ? Convert.ToInt32(reader.GetValue(0)) : 0,
                                SubdivisionName = reader.GetValue(5) != DBNull.Value ? (string)(reader.GetValue(5)) : "",
                                SubdivisionNum = reader.GetValue(6) != DBNull.Value ? (string)(reader.GetValue(6)) : ""
                            },
                            Phase = reader.GetValue(7) != DBNull.Value ? (string)(reader.GetValue(7)) : "",
                            BuildingNum = reader.GetValue(8) != DBNull.Value ? (string)(reader.GetValue(8)) : "",
                            StickBuilingNum = reader.GetValue(9) != DBNull.Value ? (string)(reader.GetValue(9)) : "",
                            JobPostingGroup = reader.GetValue(10) != DBNull.Value ? (string)(reader.GetValue(10)) : "",
                            LotWidth = reader.GetValue(11) != DBNull.Value ? (string)(reader.GetValue(11)) : "",
                            LotSwing = reader.GetValue(12) != DBNull.Value ? (string)(reader.GetValue(12)) : "",
                            JobCounty = reader.GetValue(13) != DBNull.Value ? (string)(reader.GetValue(13)) : "",
                            Notes = reader.GetValue(14) != DBNull.Value ? (string)(reader.GetValue(14)) : "",
                            GarageOrientationId = reader.GetValue(15) != DBNull.Value ? (int)(reader.GetValue(15)) : 0,
                            GarageOrientation = new GarageOrientationDto()
                            {
                                GarageOrientationId = reader.GetValue(15) != DBNull.Value ? (int)(reader.GetValue(15)) : 0,
                                Name = reader.GetValue(16) != DBNull.Value ? (string)(reader.GetValue(16)) : ""
                            },
                            LotSectionCode = reader.GetValue(21) != DBNull.Value ? (string)(reader.GetValue(21)) : "",
                            LotSize = reader.GetValue(22) != DBNull.Value ? (decimal)(reader.GetValue(22)) : 0,
                            LotCost = reader.GetValue(34) != DBNull.Value ? (decimal)(reader.GetValue(34)) : 0,
                            HomeOrientationPerPlan = reader.GetValue(23) != DBNull.Value ? (bool?)(reader.GetValue(23)) : null,
                            BoolHomeOrientationPerPlan = reader.GetValue(23) != DBNull.Value ? (bool)(reader.GetValue(23)) == true : false,
                            ParkingNum = reader.GetValue(24) != DBNull.Value ? (string)(reader.GetValue(24)) : null,
                            StorageNum = reader.GetValue(25) != DBNull.Value ? (string)(reader.GetValue(25)) : null,
                            GarageNum = reader.GetValue(29) != DBNull.Value ? (string)(reader.GetValue(29)) : null,
                            JobConstructionType = new JobConstructionTypeDto()
                            {
                                JobConstructionTypeId = reader.GetValue(26) != DBNull.Value ? (int)(reader.GetValue(26)) : 0,
                                Description = reader.GetValue(27) != DBNull.Value ? (string)(reader.GetValue(27)) : ""
                            },
                            IsSs = reader.GetValue(28) != DBNull.Value ? (bool?)(reader.GetValue(28)) : null,
                            BoolIsSs = reader.GetValue(28) != DBNull.Value ? (bool)(reader.GetValue(28)) : false,
                            GeneralOptionBudget = reader.GetValue(32) != DBNull.Value ? (decimal?)(reader.GetValue(32)) : null,
                            HomesiteOptionSpendBudget = reader.GetValue(33) != DBNull.Value ? (decimal?)(reader.GetValue(33)) : null,
                            ProjectedSalesReleaseDate = reader.GetValue(31) != DBNull.Value ? (DateTime?)(reader.GetValue(31)) : null,
                            ProjectedLotTakedown = reader.GetValue(30) != DBNull.Value ? (DateTime?)(reader.GetValue(30)) : null,
                        });
                    }
                }
                //var jobDto = _mapper.Map<List<JobDto>>(jobs);
                //var jobs = await _context.Jobs.Where(x => x.IsActive == true).ToListAsync();
                //var jobsDto = _mapper.Map<List<JobDto>>(jobs);
                return new OkObjectResult(new ResponseModel<List<JobDto>> { Value = jobsDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobDto>> { IsSuccess = false, Message = "Error while fetching jobs", Value = new List<JobDto>() });
            }
        }
        
        [HttpGet]
        public async Task<IActionResult> JobsAsync()
        {
            try
            {
                var conn = _configuration.GetConnectionString("ERPConnection");
                var jobsDto = new List<JobDto>();
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    var query = "SELECT j.SUBDIVISION_ID, j.JOB_NUMBER, j.LOT_NUMBER, j.JOB_DESC, j.JOB_ADDRESS1, s.SUBDIVISION_NAME, s.SUBDIVISION_NUM, j.PHASE, j.BUILDINGNUM, j.STICKBUILINGNUM, j.JOB_POSTING_GROUP, j.LOT_WIDTH, j.LOT_SWING, j.JOB_COUNTY, j.NOTES, g.GARAGEORIENTATIONID, g.NAME, j.JOB_ADDRESS2, j.JOB_CITY, j.JOB_STATE, j.JOB_ZIP_CODE, j.LOT_SECTION_CODE, j.LOT_SIZE, j.HOME_ORIENTATION_PER_PLAN, j.PARKING_NUM, j.STORAGE_NUM, j.JOB_CONSTRUCTION_TYPE_ID, jct.DESCRIPTION, j.IsSS, j.GARAGE_NUM, j.PROJECTED_LOT_TAKEDOWN, j.PROJECTED_SALES_RELEASE_DATE, j.GENERAL_OPTION_BUDGET, j.HOMESITE_OPTION_SPEND_BUDGET, j.LOT_COST  FROM dbo.JOB j join dbo.SUBDIVISION s on j.SUBDIVISION_ID = s.SUBDIVISION_ID left join dbo.GARAGE_ORIENTATION g on j.GARAGEORIENTATIONID = g.GARAGEORIENTATIONID left join dbo.JOB_CONSTRUCTION_TYPE jct on j.JOB_CONSTRUCTION_TYPE_ID = jct.JOB_CONSTRUCTION_TYPE_ID WHERE j.IsActive = 1 ORDER BY 2";
                   
                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        string jobNum = reader.GetValue(1) != DBNull.Value ? (string)(reader.GetValue(1)) : "";
                        string jobDesc = reader.GetValue(3) != DBNull.Value ? (string)(reader.GetValue(3)) : "";
                        jobsDto.Add(new JobDto()
                        {
                            SubdivisionId = reader.GetValue(0) != DBNull.Value ? Convert.ToInt32(reader.GetValue(0)) : 0,
                            JobNumber = reader.GetValue(1) != DBNull.Value ? (string)(reader.GetValue(1)) : "",
                            LotNumber = reader.GetValue(2) != DBNull.Value ? (string)(reader.GetValue(2)) : "",
                            JobDesc = reader.GetValue(3) != DBNull.Value ? (string)(reader.GetValue(3)) : "",
                            JobAddress1 = reader.GetValue(4) != DBNull.Value ? (string)(reader.GetValue(4)) : "",
                            JobAddress2 = reader.GetValue(17) != DBNull.Value ? (string)(reader.GetValue(17)) : "",
                            JobCity = reader.GetValue(18) != DBNull.Value ? (string)(reader.GetValue(18)) : "",
                            JobState = reader.GetValue(19) != DBNull.Value ? (string)(reader.GetValue(19)) : "",
                            JobZipCode = reader.GetValue(20) != DBNull.Value ? (string)(reader.GetValue(20)) : "",
                            Subdivision = new SubdivisionDto()
                            {
                                SubdivisionId = reader.GetValue(0) != DBNull.Value ? Convert.ToInt32(reader.GetValue(0)) : 0,
                                SubdivisionName = reader.GetValue(5) != DBNull.Value ? (string)(reader.GetValue(5)) : "",
                                SubdivisionNum = reader.GetValue(6) != DBNull.Value ? (string)(reader.GetValue(6)) : ""
                            },
                            Phase = reader.GetValue(7) != DBNull.Value ? (string)(reader.GetValue(7)) : "",
                            BuildingNum = reader.GetValue(8) != DBNull.Value ? (string)(reader.GetValue(8)) : "",
                            StickBuilingNum = reader.GetValue(9) != DBNull.Value ? (string)(reader.GetValue(9)) : "",
                            JobPostingGroup = reader.GetValue(10) != DBNull.Value ? (string)(reader.GetValue(10)) : "",
                            LotWidth = reader.GetValue(11) != DBNull.Value ? (string)(reader.GetValue(11)) : "",
                            LotSwing = reader.GetValue(12) != DBNull.Value ? (string)(reader.GetValue(12)) : "",
                            JobCounty = reader.GetValue(13) != DBNull.Value ? (string)(reader.GetValue(13)) : "",
                            Notes = reader.GetValue(14) != DBNull.Value ? (string)(reader.GetValue(14)) : "",
                            GarageOrientationId = reader.GetValue(15) != DBNull.Value ? (int)(reader.GetValue(15)) : 0,
                            GarageOrientation = new GarageOrientationDto()
                            {
                                GarageOrientationId = reader.GetValue(15) != DBNull.Value ? (int)(reader.GetValue(15)) : 0,
                                Name = reader.GetValue(16) != DBNull.Value ? (string)(reader.GetValue(16)) : ""
                            },
                            LotSectionCode = reader.GetValue(21) != DBNull.Value ? (string)(reader.GetValue(21)) : "",
                            LotSize = reader.GetValue(22) != DBNull.Value ? (decimal)(reader.GetValue(22)) : 0,
                            LotCost = reader.GetValue(34) != DBNull.Value ? (decimal)(reader.GetValue(34)) : 0,
                            HomeOrientationPerPlan = reader.GetValue(23) != DBNull.Value ? (bool)(reader.GetValue(23)) : null,
                            BoolHomeOrientationPerPlan = reader.GetValue(23) != DBNull.Value ? (bool)(reader.GetValue(23)) == true : false,
                            ParkingNum = reader.GetValue(24) != DBNull.Value ? (string)(reader.GetValue(24)) : null,
                            StorageNum = reader.GetValue(25) != DBNull.Value ? (string)(reader.GetValue(25)) : null,
                            GarageNum = reader.GetValue(29) != DBNull.Value ? (string)(reader.GetValue(29)) : null,
                            JobConstructionType = new JobConstructionTypeDto()
                            {
                                JobConstructionTypeId = reader.GetValue(26) != DBNull.Value ? (int)(reader.GetValue(26)) : 0,
                                Description = reader.GetValue(27) != DBNull.Value ? (string)(reader.GetValue(27)) : ""
                            },
                            IsSs = reader.GetValue(28) != DBNull.Value ? (bool?)(reader.GetValue(28)) : null,
                            BoolIsSs = reader.GetValue(28) != DBNull.Value ? (bool)(reader.GetValue(28)) : false,
                            GeneralOptionBudget = reader.GetValue(32) != DBNull.Value ? (decimal?)(reader.GetValue(32)) : null,
                            HomesiteOptionSpendBudget = reader.GetValue(33) != DBNull.Value ? (decimal?)(reader.GetValue(33)) : null,
                            ProjectedSalesReleaseDate = reader.GetValue(31) != DBNull.Value ? (DateTime?)(reader.GetValue(31)) : null,
                            ProjectedLotTakedown = reader.GetValue(30) != DBNull.Value ? (DateTime?)(reader.GetValue(30)) : null,
                            DisplayDescription = $"{jobNum} {jobDesc}",
                        });
                    }
                }
                //var jobDto = _mapper.Map<List<JobDto>>(jobs);
                //var jobs = await _context.Jobs.Where(x => x.IsActive == true).ToListAsync();
                //var jobsDto = _mapper.Map<List<JobDto>>(jobs);
                return new OkObjectResult(new ResponseModel<List<JobDto>> { Value = jobsDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobDto>> { IsSuccess = false, Message = "Error while fetching all jobs", Value = new List<JobDto>() });
            }
        }

        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> GetLotAsync(string jobNumber)
        {
            try
            {
                var lot = await _context.Jobs.AsNoTracking().Include("Subdivision").Include(x => x.LotStatusNavigation).Include(x=>x.GarageOrientation).AsNoTracking().SingleOrDefaultAsync(x => x.JobNumber == jobNumber);
                var jobDtoObject = _mapper.Map<JobDto>(lot);
                //TODO: maybe want cancelled customer? 
                var getCustomer = _context.JobCustomers.AsNoTracking().Include("Customer").Where(x => x.JobNumber == jobNumber && x.Cancelled != true && x.IsActive == true).Select(x => x.Customer).FirstOrDefault();
                var customerDtoObject = _mapper.Map<CustomerDto>(getCustomer);
                var getSalesConfig = _context.Salesconfigs.AsNoTracking().Include("PhasePlan.MasterPlan").Where(x => x.JobNumber == jobNumber && x.IsActive == true && x.Canceldate == null && x.Status == "sale" && x.SsAction == "U" && x.IsApproved == "T").FirstOrDefault();//TODO: check this is the right one, not cancelled, not spec, ?//cancelled one should be marked "D"
                var salesConfigDto = _mapper.Map<SalesconfigDto>(getSalesConfig);
                var getContractForJob = _context.Contracts.AsNoTrackingWithIdentityResolution().AsSplitQuery().Include(x => x.Envelopes).Include(c=>c.SelectedFloorplan).ThenInclude(c=>c.MasterPlan).Where(x => x.SelectedLotId == jobNumber && x.Cancellation == null && x.Envelopes.Any(y => y.VoidedDate == null && y.SignedDate != null && y.DocuSignSignatureTemplate.TemplateTypeId == 1)).ToList();//template type id = 1 is Sales Agreement
                if (getContractForJob.Any())
                {
                    jobDtoObject.PlanName = getContractForJob.First().SelectedFloorplan?.MasterPlan.PlanName;
                }
                else
                {
                    var specForJob = _context.Specs.AsNoTrackingWithIdentityResolution().Include(x => x.SelectedFloorplan).ThenInclude(x => x.MasterPlan).Where(x => x.JobId == jobNumber).ToList();
                    if (specForJob.Any())
                    {
                        jobDtoObject.PlanName = specForJob.First().SelectedFloorplan?.MasterPlan.PlanName;
                    }
                }
                jobDtoObject.PlanName = jobDtoObject.PlanName ?? lot?.PlanName;
                jobDtoObject.Customer = customerDtoObject ?? new CustomerDto();
                jobDtoObject.SalesConfig = salesConfigDto ?? new SalesconfigDto() { JobNumber = jobNumber};
                jobDtoObject.BoolBlocked = jobDtoObject.Blocked == true;
                jobDtoObject.BoolHomeOrientationPerPlan = jobDtoObject.HomeOrientationPerPlan == true;
                jobDtoObject.BoolIsSs = jobDtoObject.IsSs == true;
                return new OkObjectResult(new ResponseModel<JobDto> { Value = jobDtoObject, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<JobDto> { IsSuccess = false, Message = "Error while fetching job" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetUserContactsAsync()
        {
            try
            {
                var users = await _context.Users.AsNoTracking().Where(x => x.IsActive1 == true).ToListAsync();
                var usersDto = users.Select(x => new UserDto()
                {
                    UserId = x.UserId,
                    FirstName = x.FirstName,
                    LastName = x.LastName,
                    FullName = x.FirstName + " " + x.LastName,
                    EmailAddress = x.EmailAddress,
                }).ToList();
                //var usersDto = _mapper.Map<List<UserDto>>(users);
                return new OkObjectResult(new ResponseModel<List<UserDto>> { Value = usersDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<UserDto>> { IsSuccess = false, Message = "Failed to fetch User Contacts", Value = null });
            }
        }        
        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> GetJobContactsAsync(string jobNumber)
        {
            try
            {
                var contacts = await _context.JobContacts.AsNoTracking().Include(x => x.Role).Include(x => x.User).Where(x => x.JobNumber == jobNumber && x.IsActive == true).ToListAsync();
                var contactsDto = _mapper.Map<List<JobContactDto>>(contacts);   
                return new OkObjectResult(new ResponseModel<List<JobContactDto>> { Value = contactsDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobContactDto>> { IsSuccess = false, Message = "Error while fetching Job Contacts", Value = null });
            }
        }
        
        [HttpPost]
        public async Task<IActionResult> AddJobContactAsync([FromBody] JobContactDto contactToAdd)
        {
            try
            {

                //TODO: link to azure ad id? to get most recent email if it changed??

                // Check if contact already exists
                var checkIfContactExists = await _context.JobContacts.Where(x => x.UserId == contactToAdd.UserId && x.JobNumber == contactToAdd.JobNumber && x.IsActive == true).ToListAsync();

                if (checkIfContactExists.Any())
                {
                    return new OkObjectResult(new ResponseModel<JobContactDto> { Value = null, IsSuccess = true, Message = "Job Contact already exists" });
                }
                else
                {
                    var findUser = await _context.Users.SingleOrDefaultAsync(x => x.UserId == contactToAdd.UserId);
                    if(findUser == null)
                    {
                        var addUser = new User()
                        {
                            FirstName = contactToAdd.User.FirstName,
                            LastName = contactToAdd.User.LastName,
                            UserId = contactToAdd.User.UserId,
                            EmailAddress = contactToAdd.User.EmailAddress,
                        };
                        _context.Users.Add(addUser);
                        await _context.SaveChangesAsync();
                        findUser = addUser;
                    }
                    else
                    {
                        //update if needed
                        findUser.FirstName = contactToAdd.User.FirstName;
                        findUser.LastName = contactToAdd.User.LastName;
                        findUser.EmailAddress = contactToAdd.User.EmailAddress;
                        _context.Users.Update(findUser);
                        await _context.SaveChangesAsync();
                    }

                    var addContact = new JobContact();
                    addContact.UserId = findUser.UserId;
                    addContact.JobNumber = contactToAdd.JobNumber;
                    addContact.RoleId = contactToAdd.RoleId;
                    addContact.CreatedBy = User.Identity.Name.Split('@')[0];
                    _context.JobContacts.Add(addContact);
                    _context.SaveChanges();
                    return new OkObjectResult(new ResponseModel<JobContactDto> { Value = contactToAdd, IsSuccess = true , Message = "Job Contact added successfully"});
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<JobContactDto> { IsSuccess = false, Message = "Error while adding job contact", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateJobContactAsync([FromBody] JobContactDto updateContact)
        {
            try
            {
                var findContact = await _context.JobContacts.Include("User").Include(x => x.Role).SingleOrDefaultAsync(x => x.JobContactId == updateContact.JobContactId);
                findContact.JobNumber = updateContact.JobNumber;
                findContact.RoleId = updateContact.RoleId;                
                findContact.UpdatedBy = User.Identity.Name.Split('@')[0];
                findContact.UpdatedDateTime = DateTime.Now;
                _context.JobContacts.Update(findContact);
                _context.SaveChanges();
                return new OkObjectResult(new ResponseModel<JobContactDto> { Value = updateContact, IsSuccess = true, Message = "Job Contact updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<JobContactDto> { IsSuccess = false, Message = "Error while updating job contact", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteJobContactAsync([FromBody] JobContactDto updateContact)
        {
            try
            {
                var findContact = await _context.JobContacts.SingleOrDefaultAsync(x => x.JobContactId == updateContact.JobContactId);
                findContact.IsActive = false;
                findContact.UpdatedBy = User.Identity.Name.Split('@')[0];
                findContact.UpdatedDateTime = DateTime.Now;
                _context.JobContacts.Update(findContact);
                _context.SaveChanges();
                return new OkObjectResult(new ResponseModel<JobContactDto> { Value = updateContact, IsSuccess = true, Message = "Job Contact deleted successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<JobContactDto> { IsSuccess = false, Message = "Error while deleting job contact", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> RolesAsync()
        {
            try
            {
                var roles = await _context.Roles.AsNoTracking().Where(x => x.IsActive == true).ToListAsync();
                var rolesDto = _mapper.Map<List<RoleDto>>(roles);
                return Ok(new ResponseModel<List<RoleDto>> { Value = rolesDto, IsSuccess = true, Message = "Success getting roles" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<RoleDto>>() { IsSuccess = false, Message = "Error while getting roles", Value = null });
            }
        }
        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> GetSubdivisionContactsAsync(int subdivId)
        {
            try
            {
                var contacts = await _context.SubdivisionContacts.AsNoTracking().Where(x => x.SubdivisionId == subdivId && x.IsActive == true).Select(x => new SubdivisionContactModel()
                {
                    SubdivisionContactId = x.SubdivisionContactId,
                    SubdivisionId = x.SubdivisionId,
                    UserId = x.UserId,
                    FirstName = x.User.FirstName,
                    LastName = x.User.LastName,
                    Email = x.User.EmailAddress,
                    SiteContact1 = x.SiteContact1,
                    IsSiteContact1 = x.SiteContact1 == "T",
                    SiteContact2 = x.SiteContact2,
                    IsSiteContact2 = x.SiteContact2 == "T",
                    SiteContact3 = x.SiteContact3,
                    IsSiteContact3 = x.SiteContact3 == "T",
                    SiteContact4 = x.SiteContact4,
                    IsSiteContact4 = x.SiteContact4 == "T",
                    SiteContact5 = x.SiteContact5,
                    IsSiteContact5 = x.SiteContact5 == "T",
                    SiteContact6 = x.SiteContact6,
                    IsSiteContact6 = x.SiteContact6 == "T" ? true : false,
                    VpoApprovalLevel = x.VpoApprovalLevel
                }).ToListAsync();
                return new OkObjectResult(new ResponseModel<List<SubdivisionContactModel>> { Value = contacts, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SubdivisionContactModel>> { IsSuccess = false, Message = "Error while deleting subdivision contacts", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddSubdivisionContactAsync([FromBody] SubdivisionContactModel contactToAdd)
        {
            try
            {
                var addContact = new SubdivisionContact();
                addContact.UserId = contactToAdd.UserId;
                addContact.SubdivisionId = (int)contactToAdd.SubdivisionId;
                addContact.SiteContact1 = contactToAdd.IsSiteContact1 ? "T" : "F";
                addContact.SiteContact2 = contactToAdd.IsSiteContact2 ? "T" : "F";
                addContact.SiteContact3 = contactToAdd.IsSiteContact3 ? "T" : "F";
                addContact.SiteContact4 = contactToAdd.IsSiteContact4 ? "T" : "F";
                addContact.SiteContact5 = contactToAdd.IsSiteContact5 ? "T" : "F";
                addContact.SiteContact6 = contactToAdd.IsSiteContact6 ? "T" : "F";
                addContact.CreatedBy = User.Identity.Name.Split('@')[0];
                _context.SubdivisionContacts.Add(addContact);
                _context.SaveChanges();
                return new OkObjectResult(new ResponseModel<SubdivisionContactModel> { Value = contactToAdd, IsSuccess = true, Message = "Subdivision contact added successfully" });
            }
            catch (Exception ex) 
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SubdivisionContactModel> { IsSuccess = false, Message = "Error while adding subdivision contact", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateSubdivisionContactAsync([FromBody] SubdivisionContactModel updateContact)
        {
            try
            {
                var findContact = await _context.SubdivisionContacts.Include("User").SingleOrDefaultAsync(x => x.SubdivisionContactId == updateContact.SubdivisionContactId);
                findContact.SubdivisionId = (int)updateContact.SubdivisionId;
                findContact.SiteContact1 = updateContact.IsSiteContact1 ? "T" : "F";
                findContact.SiteContact2 = updateContact.IsSiteContact2 ? "T" : "F";
                findContact.SiteContact3 = updateContact.IsSiteContact3 ? "T" : "F";
                findContact.SiteContact4 = updateContact.IsSiteContact4 ? "T" : "F";
                findContact.SiteContact5 = updateContact.IsSiteContact5 ? "T" : "F";
                findContact.SiteContact6 = updateContact.IsSiteContact6 ? "T" : "F";
                findContact.User.FirstName = updateContact.FirstName;//Will this update the user table?
                findContact.User.LastName = updateContact.LastName;
                findContact.User.EmailAddress = updateContact.Email;
                findContact.UpdatedBy = User.Identity.Name.Split('@')[0];
                findContact.UpdatedDateTime = DateTime.Now;
                _context.SubdivisionContacts.Update(findContact);
                _context.SaveChanges();
                return new OkObjectResult(new ResponseModel<SubdivisionContactModel> { Value = updateContact, IsSuccess = true, Message = "Subdivision contact updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SubdivisionContactModel> { IsSuccess = false, Message = "Error while updating subdivision contact", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteSubdivisionContactAsync([FromBody] SubdivisionContactModel updateContact)
        {
            try
            {
                var findContact = await _context.SubdivisionContacts.SingleOrDefaultAsync(x => x.SubdivisionContactId == updateContact.SubdivisionContactId);
                findContact.IsActive = false;
                findContact.UpdatedBy = User.Identity.Name.Split('@')[0];
                findContact.UpdatedDateTime = DateTime.Now;
                _context.SubdivisionContacts.Update(findContact);
                _context.SaveChanges();
                return new OkObjectResult(new ResponseModel<SubdivisionContactModel> { Value = updateContact, IsSuccess = true, Message = "Subdivision contact deleted successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SubdivisionContactModel> { IsSuccess = false, Message = "Error while deleting subdivision contact", Value = null });
            }
        }
        [HttpPost]
        public async Task<ResponseModel<JobDto>> AddJobAsync([FromBody] JobDto updateJob)
        {
            try
            {
                var findLot = await _context.Jobs.FirstOrDefaultAsync(x => x.JobNumber == updateJob.JobNumber);
                if(findLot != null)
                {
                    return new ResponseModel<JobDto>() { IsSuccess = false, Message = "Job number exists", Value = updateJob};
                }
                var newLot = new Job()
                {
                    JobAddress1 = updateJob.JobAddress1,
                    JobAddress2 = updateJob.JobAddress2,
                    LotNumber = updateJob.LotNumber,
                    JobDesc = updateJob.JobDesc,
                    JobNumber = updateJob.JobNumber,
                    SubdivisionId = updateJob.SubdivisionId,
                    JobCity = updateJob.JobCity,
                    JobZipCode = updateJob.JobZipCode,
                    JobCounty = updateJob.JobCounty,
                    LotAvailability = updateJob.LotAvailability,
                    LotCost = updateJob.LotCost,
                    JobPostingGroup = updateJob.JobPostingGroup,
                    LotPremium = updateJob.LotPremium,
                    //LotWidth = updateJob.LotWidth,//3/31 /25 lot width to come from vm inventory
                    LotSwing = updateJob.LotSwing,
                    LotUnit = updateJob.LotUnit,
                    LotStatus = updateJob.LotStatus,
                    PlanName = updateJob.PlanName,
                    PlanCode = updateJob.PlanCode,
                    Phase = updateJob.Phase,
                    ModelName = updateJob.ModelName,
                    LotSectionCode = updateJob.LotSectionCode,
                    Supervisor = updateJob.Supervisor,
                    FieldSuper = updateJob.FieldSuper,
                    ProjectMgr = updateJob.ProjectMgr,
                    UserContact1 = updateJob.UserContact1,
                    UserContact2 = updateJob.UserContact2,
                    SalesContact = updateJob.SalesContact,
                    StickBuilingNum = updateJob.StickBuilingNum,
                    BuildingNum = updateJob.BuildingNum,
                    CreatedBy = User.Identity.Name.Split('@')[0]
                };
                _context.Jobs.Add(newLot);
                await _context.SaveChangesAsync();
                var responseJob = _mapper.Map<JobDto>(newLot);   
                return new ResponseModel<JobDto>() { IsSuccess = true, Message = "Job added", Value = responseJob };                
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return new ResponseModel<JobDto>() { IsSuccess = false, Message = "Failed to add job", Value = updateJob }; ;
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateLotAsync([FromBody] JobDto updateJob)
        {
            try
            {
                var findLot = await _context.Jobs.SingleOrDefaultAsync(x => x.JobNumber == updateJob.JobNumber);
                findLot.JobAddress1 = updateJob.JobAddress1;//won't be update from ERP, only from bc
                findLot.JobAddress2 = updateJob.JobAddress2;//12/13/24: They changed their minds, the address fields will be from ERP
                findLot.JobCity = updateJob.JobCity;
                findLot.JobState = updateJob.JobState;
                findLot.JobZipCode = updateJob.JobZipCode;
                findLot.JobCounty = updateJob.JobCounty;
                //findLot.JobDesc = updateJob.JobDesc;
                findLot.LotNumber = updateJob.LotNumber;
                findLot.LotAvailability = updateJob.LotAvailability;
                findLot.LotCost = updateJob.LotCost;
                findLot.JobPostingGroup = updateJob.JobPostingGroup;
                findLot.LotPremium = updateJob.LotPremium;
                //findLot.LotWidth = updateJob.LotWidth;//3/31/25 lot width to come from vm inventory
                findLot.LotSwing = updateJob.LotSwing;
                findLot.LotUnit = updateJob.LotUnit;
               // findLot.LotStatus = updateJob.LotStatus;from cms
                findLot.LotSize = updateJob.LotSize;
                findLot.PlanName = updateJob.PlanName;
                findLot.PlanCode = updateJob.PlanCode;
                findLot.Phase = updateJob.Phase;
                findLot.ModelName = updateJob.ModelName;
                findLot.LotSectionCode = updateJob.LotSectionCode;
                findLot.GarageOrientationId = updateJob.GarageOrientationId == 0 ? null : updateJob.GarageOrientationId;
                findLot.JobConstructionTypeId = updateJob.JobConstructionTypeId;
                findLot.Notes = updateJob.Notes;
                findLot.HomeOrientationPerPlan = updateJob.HomeOrientationPerPlan;
                findLot.IsSs = updateJob.BoolIsSs;
                findLot.ParkingNum = updateJob.ParkingNum;
                findLot.StorageNum = updateJob.StorageNum;
                findLot.GarageNum = updateJob.GarageNum;
                //findLot.Supervisor = updateJob.Supervisor;//not used anymore
                //findLot.FieldSuper = updateJob.FieldSuper;
                //findLot.ProjectMgr = updateJob.ProjectMgr;
                //findLot.UserContact1 = updateJob.UserContact1;
                //findLot.UserContact2 = updateJob.UserContact2;
                //findLot.SalesContact = updateJob.SalesContact;
                findLot.StickBuilingNum = updateJob.StickBuilingNum;
                findLot.BuildingNum = updateJob.BuildingNum;
                findLot.ProjectedLotTakedown = updateJob.ProjectedLotTakedown;
                findLot.ProjectedSalesReleaseDate = updateJob.ProjectedSalesReleaseDate;
                findLot.GeneralOptionBudget = updateJob.GeneralOptionBudget;
                findLot.HomesiteOptionSpendBudget = updateJob.HomesiteOptionSpendBudget;
                findLot.UpdatedBy = User.Identity.Name.Split('@')[0];
                findLot.UpdateDateTime = DateTime.Now;
                _context.Jobs.Update(findLot);
                _context.SaveChanges();
                return new OkObjectResult(new ResponseModel<JobDto> { Value = updateJob, IsSuccess = true, Message = "Job updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<JobDto> { IsSuccess = false, Message = "Error while updating Job", Value = updateJob });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateMultipleJobsContactsAsync([FromBody] UpdateJob updateJob)
        {
            try
            {
                //Updates Site Contacts on multiple lots
                var findOtherJobs = _context.Jobs.Where(x => updateJob.JobsToUpdate.Contains(x.JobNumber) && x.JobNumber != updateJob.JobNumber);// not the one being copied, else it will be deactivated before it's copied
                var updateBy = User.Identity.Name.Split('@')[0];

                var findContactsToDeactivate = _context.JobContacts.Where(x => findOtherJobs.Select(y => y.JobNumber).Contains(x.JobNumber));
                await findContactsToDeactivate.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));//Deactivate old contacts, except in the job being copied

                //bulk insert new ones
                var findContactsToCopy = _context.JobContacts.Where(x => x.JobNumber == updateJob.JobNumber && x.IsActive == true);

                DataTable JobContactsDataTable = new DataTable("JobContacts");
                DataColumn JobNumber = new DataColumn("JobNumber", typeof(string));
                JobContactsDataTable.Columns.Add(JobNumber);
                DataColumn UserId = new DataColumn("UserId", typeof(string));
                JobContactsDataTable.Columns.Add(UserId);
                DataColumn RoleId = new DataColumn("RoleId", typeof(int));
                JobContactsDataTable.Columns.Add(RoleId);
                DataColumn CreatedBy = new DataColumn("CreatedBy", typeof(string));
                JobContactsDataTable.Columns.Add(CreatedBy);

                string createBy = User.Identity.Name.Split('@')[0];
                foreach (var job in findOtherJobs.ToList())
                {
                    foreach (var contact in findContactsToCopy.ToList())
                    {
                        JobContactsDataTable.Rows.Add(job.JobNumber, contact.UserId, contact.RoleId, createBy);
                    }
                }

                //do the bulk insert 
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    using (var bulkCopy = new SqlBulkCopy(connection))
                    {
                        bulkCopy.DestinationTableName = "JOB_CONTACT";
                        bulkCopy.ColumnMappings.Add("JobNumber", "JOB_NUMBER");
                        bulkCopy.ColumnMappings.Add("UserId", "USER_ID");
                        bulkCopy.ColumnMappings.Add("RoleId", "Role_ID");
                        bulkCopy.ColumnMappings.Add("CreatedBy", "CreatedBy");

                        bulkCopy.WriteToServer(JobContactsDataTable);
                    }
                }

                return new OkObjectResult(new ResponseModel<UpdateJob> { Value = updateJob, IsSuccess = true, Message = "Multiple job contacts updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<UpdateJob> { IsSuccess = false, Message = "Error while updating multiple job contacts", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateLotsAsync([FromBody] UpdateJob updateJob)
        {
            try
            {
                //Updates phase/stick/building number on multiplejobs
                var findJobs = _context.Jobs.Where(x => updateJob.JobsToUpdate.Contains(x.JobNumber));
                var updateBy = User.Identity.Name.Split('@')[0];
                await findJobs.ExecuteUpdateAsync(s => s.SetProperty(b => b.Phase, updateJob.Phase).SetProperty(b => b.BuildingNum, updateJob.BuildingNum).SetProperty(b => b.StickBuilingNum, updateJob.Stick).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdateDateTime, DateTime.Now));
                return new OkObjectResult(new ResponseModel<UpdateJob> { Value = updateJob, IsSuccess = true, Message = "Lots updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<UpdateJob> { IsSuccess = false, Message = "Error while updating lots", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateJobsAsync([FromBody] List<JobDto> jobsToBeUpdated)
        {
            try
            {
                //This is for update a batch of jobs from ERP from excel. It will only update the fields that do not need to sync with BC and that can be updated in excel
                var user = User.Identity.Name.Split('@')[0];
                var getGarageOrientations = _context.GarageOrientations.Where(x => x.IsActive == true).ToList();
                var getBuildTypes = _context.JobConstructionTypes.Where(x => x.IsActive == true).ToList();
                var schedules = _context.Schedules.Where(x => x.IsActive == true).ToList();
                var jobs = _context.Jobs.Include(x => x.GarageOrientation).Where(x => x.IsActive == true).ToList();
                //join to get garage orientation ids
                var jobsToUpdate = (from a in jobsToBeUpdated
                                    from j in jobs.Where(x =>x.JobNumber == a.JobNumber).DefaultIfEmpty()
                                    from s in schedules.Where(x => x.JobNumber == a.JobNumber).DefaultIfEmpty()
                                    from b in getGarageOrientations.Where(x => x.IsActive == true && (s == null || s.IniSchApproved == null || s.IniSchApproved == false ? a.GarageOrientation.Name == x.Name: j.GarageOrientation?.Name == x.Name)).DefaultIfEmpty()
                                    from c in getBuildTypes.Where(x => x.IsActive == true && a.JobConstructionType.Description == x.Description).DefaultIfEmpty()
                                    select new Job()
                                    {
                                        JobNumber = a.JobNumber,
                                        LotNumber = a.LotNumber,
                                        JobAddress1 = a.JobAddress1,
                                        JobAddress2 = a.JobAddress2,
                                        JobCity = a.JobCity,
                                        JobState = a.JobState,
                                        JobZipCode = a.JobZipCode,                                        
                                        LotSectionCode = a.LotSectionCode,
                                        LotSwing = a.LotSwing,
                                        //LotWidth = a.LotWidth,//3/31/25 Lot Width to come from ETL from VM Inventory
                                        LotSize = a.LotSize,
                                        Phase = a.Phase,
                                        BuildingNum = a.BuildingNum,
                                        StickBuilingNum = a.StickBuilingNum,
                                        Notes = a.Notes,
                                        JobCounty = a.JobCounty,                                        
                                        GarageOrientationId = b?.GarageOrientationId,
                                        JobConstructionTypeId = c?.JobConstructionTypeId,
                                        HomeOrientationPerPlan = s == null || s.IniSchApproved == null || s.IniSchApproved == false ? a.HomeOrientationPerPlan : j.HomeOrientationPerPlan, // do not update if schedule is released
                                        ParkingNum = a.ParkingNum,
                                        StorageNum = a.StorageNum,
                                        GarageNum = a.GarageNum,
                                        ProjectedSalesReleaseDate = a.ProjectedSalesReleaseDate,
                                        ProjectedLotTakedown = a.ProjectedLotTakedown,
                                        GeneralOptionBudget = a.GeneralOptionBudget,
                                        HomesiteOptionSpendBudget = a.HomesiteOptionSpendBudget,
                                        IsSs = a.IsSs,
                                        UpdateDateTime = DateTime.Now,
                                        UpdatedBy = user,
                                    }).ToList();

                await _context.BulkUpdateAsync(jobsToUpdate, options =>
                options.ColumnInputExpression = c => new { c.LotNumber, c.LotSectionCode, c.LotSwing, c.LotWidth, c.LotSize, c.Phase, c.BuildingNum, c.StickBuilingNum, c.UpdateDateTime, c.UpdatedBy, c.Notes, c.GarageOrientationId, c.JobAddress1, c.JobAddress2, c.JobCity, c.JobState, c.JobZipCode, c.JobCounty, c.StorageNum, c.ParkingNum, c.GarageNum, c.HomeOrientationPerPlan, c.JobConstructionTypeId, c.IsSs, c.HomesiteOptionSpendBudget, c.GeneralOptionBudget, c.ProjectedLotTakedown, c.ProjectedSalesReleaseDate });

                return new OkObjectResult(new ResponseModel<List<JobDto>> { Value = jobsToBeUpdated, IsSuccess = true, Message = "Lots updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobDto>> { IsSuccess = false, Message = "Error while updating Jobs", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateFieldsInLotsAsync([FromBody] UpdateJob updateJob)
        {
            try
            {
                //Updates Phase / Building Num / Stick Num on multiple lots
                var findJobs = _context.Jobs.Where(x => updateJob.JobsToUpdate.Contains(x.JobNumber));
                var updateBy = User.Identity.Name.Split('@')[0];
                if (updateJob.Phase != null && updateJob.Phase != "")
                {
                    await findJobs.ExecuteUpdateAsync(s => s.SetProperty(b => b.Phase, updateJob.Phase).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdateDateTime, DateTime.Now));
                }
                if (updateJob.BuildingNum != null && updateJob.BuildingNum != "")
                {
                    await findJobs.ExecuteUpdateAsync(s => s.SetProperty(b => b.BuildingNum, updateJob.BuildingNum).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdateDateTime, DateTime.Now));
                }
                if (updateJob.Stick != null && updateJob.Stick != "")
                {
                    await findJobs.ExecuteUpdateAsync(s => s.SetProperty(b => b.StickBuilingNum, updateJob.Stick).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdateDateTime, DateTime.Now));
                }
                return new OkObjectResult(new ResponseModel<UpdateJob> { Value = updateJob, IsSuccess = true, Message = "Phase / Building Num / Stick Num updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<UpdateJob> { IsSuccess = false, Message = "Error while updating Phase / Building Num / Stick Num", Value = null });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetSubdivisionPlanOptionTreeAsync()
        {
            try
            {
                var getSubdivisions = _context.AvailablePlanOptions.AsNoTrackingWithIdentityResolution().Include("PhasePlan.Subdivision").Where(x => x.IsActive == true && x.PhasePlan.IsActive == true && x.PhasePlan.Subdivision.IsActive == true).ToList().GroupBy(x => new { PhasePlan = x.PhasePlan, Subdivision = x.PhasePlan.Subdivision }).ToList().GroupBy(x => x.Key.Subdivision).ToList();//This won't get communities with no plans, or plans with no options
                var model = getSubdivisions.Select(x => new NewCombinedTreeModel()
                {
                    Id = Guid.NewGuid(),
                    Subdivision = _mapper.Map<SubdivisionDto>(x.Key),
                    HasChildren = true,
                    Children = x.Select(y => new NewCombinedTreeModel()
                    {
                        Id = Guid.NewGuid(),
                        PhasePlan = _mapper.Map<PhasePlanDto>(y.Key.PhasePlan),
                        HasChildren = true,
                        Children = y.Select(z => new NewCombinedTreeModel()
                        {
                            Id = Guid.NewGuid(),
                            AvailablePlanOption = _mapper.Map<AvailablePlanOptionDto>(z),
                            HasChildren = false,
                        }).ToList()
                    }).ToList(),
                }).ToList();
                return new OkObjectResult(new ResponseModel<List<NewCombinedTreeModel>> { Value = model, IsSuccess = true });
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<NewCombinedTreeModel>> { IsSuccess = false, Message = "Failed to get subdivisions", Value = null });
            }
        }

        [HttpGet("{subdivisionId}/{includeBlocked}")]
        public async Task<IActionResult> GetJobBySubdivisionAsync(int subdivisionId, bool? includeBlocked = true)
        {
            try
            {
                var jobs = new List<JobDto>();
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();
                    var query = includeBlocked == true ? "SELECT JOB_NUMBER, SUBDIVISION_ID, JOB_POSTING_GROUP, JOB_DESC FROM [dbo].[JOB] where SUBDIVISION_ID = @subdivisionId and IsActive = 1;" : "SELECT JOB_NUMBER, SUBDIVISION_ID, JOB_POSTING_GROUP, JOB_DESC FROM [dbo].[JOB] where SUBDIVISION_ID = @subdivisionId and IsActive = 1 and (Blocked = 0 or Blocked is null);";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@subdivisionId", SqlDbType.Int).Value = subdivisionId;
                    var reader = await command.ExecuteReaderAsync();

                    while (reader.Read())
                    {
                        string jobNum = reader.GetValue(0) != DBNull.Value ? (string)(reader.GetValue(0)) : "";
                        string jobDesc = reader.GetValue(3) != DBNull.Value ? (string)(reader.GetValue(3)) : "";
                        jobs.Add(new JobDto()
                        {
                            JobNumber = reader.GetValue(0) != DBNull.Value ? reader.GetValue(0).ToString() : string.Empty,
                            SubdivisionId = reader.GetValue(1) != DBNull.Value ? Convert.ToInt32(reader.GetValue(1).ToString()) : 0,
                            JobPostingGroup = reader.GetValue(2) != DBNull.Value ? reader.GetValue(2).ToString() : string.Empty,
                            JobDesc = reader.GetValue(3) != DBNull.Value ? reader.GetValue(3).ToString() : string.Empty,
                            DisplayDescription = $"{jobNum} {jobDesc}"
                        });
                    }
                }
                return new OkObjectResult(new ResponseModel<List<JobDto>> { Value = jobs, IsSuccess = true });
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SubdivisionDto>> { IsSuccess = false, Message = "Failed to get jobs", Value = null });
            }

        }
    }
}
