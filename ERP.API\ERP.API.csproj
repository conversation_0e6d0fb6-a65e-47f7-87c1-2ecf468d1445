﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>88284918-beb9-4f71-bf5d-724fc00e1e4a</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Models\**" />
    <Content Remove="Models\**" />
    <EmbeddedResource Remove="Models\**" />
    <None Remove="Models\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="wwwroot\assets\images\vmlogo.png" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Azure.Identity" Version="1.12.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.17.0" />
    <PackageReference Include="ClosedXML" Version="0.101.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="7.0.1" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="7.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Graph" Version="5.43.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="2.17.1" />
    <PackageReference Include="Microsoft.Identity.Web.DownstreamApi" Version="2.17.1" />
    <PackageReference Include="Microsoft.Identity.Web.GraphServiceClient" Version="2.17.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog" Version="5.1.3" />
    <PackageReference Include="NLog.Database" Version="5.1.3" />
    <PackageReference Include="SendGrid" Version="9.29.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.2.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.7.0" />
    <PackageReference Include="System.Formats.Asn1" Version="9.0.4" />
    <PackageReference Include="System.Text.Json" Version="8.0.4" />
    <PackageReference Include="Telerik.Documents.Core" Version="2024.3.806" />
    <PackageReference Include="Telerik.Documents.Fixed" Version="2024.3.806" />
    <PackageReference Include="Telerik.Documents.ImageUtils" Version="2024.3.806" />
    <PackageReference Include="Z.EntityFramework.Extensions.EFCore" Version="7.102.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ERP.Data.Models\ERP.Data.Models.csproj" />
  </ItemGroup>

</Project>
