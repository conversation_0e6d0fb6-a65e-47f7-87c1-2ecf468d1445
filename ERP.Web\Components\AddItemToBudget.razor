﻿@inject PlanService PlanService
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject BudgetService BudgetService
@inject PoService PoService
<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="500px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Item to Budget 
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@ItemToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <p>
                <label>Variance Category</label>
                <TelerikTextBox Enabled="@false" @bind-Value="@VarianceCategory"
                                     Width="100%">
                </TelerikTextBox>
            </p>

            <p>
                <label>List Items by:</label>
                <TelerikDropDownList @bind-Value="@SelectedItemDisplayCategory"
                                     Data="@ItemDisplayCategoriesList"
                                     TextField="DisplayCategory"
                                     ValueField="DisplayCategoryId"
                                     DefaultText="Select Category to list Items"
                                     Width="100%">
                </TelerikDropDownList>
            </p>
            
          @if (SelectedItemDisplayCategory == 1)
            {
                <p>
                    <label>Master Plan</label>
                    @if (AllMasterPlans != null && AllMasterPlans.Count != 0)
                    {
                        <TelerikDropDownList @bind-Value="@SelectedMasterPlanId"
                                             Data="@AllMasterPlans"
                                             TextField="PlanName"
                                             ValueField="MasterPlanId"
                                             DefaultText="Select Master Plan"
                                             Filterable="true"
                                             FilterOperator="StringFilterOperator.Contains"
                                             OnChange="@CascadePlanOptions"
                                             ScrollMode="@DropDownScrollMode.Virtual"
                                             ItemHeight="30"
                                             PageSize="20"
                                             Width="100%" Context="planDropdownContext">
                            <ItemTemplate>@planDropdownContext.PlanNum - @planDropdownContext.PlanName </ItemTemplate>
                        </TelerikDropDownList>
                    }
                    else
                    {
                    <p>No plans are assigned to this subdivision</p>
                    }
                </p>
                @if (SelectedMasterPlanId != 0)
                {
                    <p>
                        <label>Master Plan Options</label>
                        <TelerikMultiSelect @bind-Value="@SelectedAsmHeaderIds"
                                            Data="@AllOptionsInMasterPlan"
                                            TextField="DisplayDesc"
                                            ValueField="AsmHeaderId"
                                            Placeholder="Select options"
                                            OnChange="@CascadeItems"
                                            Filterable="true"
                                            AutoClose="false"
                                            FilterOperator="StringFilterOperator.Contains"
                                            Width="100%" Context="optinDropdownContext">
                            <ItemTemplate>@optinDropdownContext.AssemblyCode - @optinDropdownContext.AssemblyDesc </ItemTemplate>
                        </TelerikMultiSelect>
                    </p>
                }
            @if (SelectedAsmHeaderIds != null && SelectedAsmHeaderIds.Count != 0 && AllItemsOptionSort != null)
                {
                    <p>
                        <label>Item</label>
                        <TelerikMultiSelect @bind-Value="@SelectedAsmDetailIds"
                                            @ref="OptionSortSelectItemsRef"
                                            TextField="ItemDesc"
                                            ValueField="AsmDetailId"
                                            Filterable="true"
                                            AutoClose="false"
                                            FilterOperator="StringFilterOperator.Contains"
                                            Placeholder="Select items"
                                            Data="@AllItemsOptionSort"
                                            Width="100%" Context="itemDropdownContext">
                            <HeaderTemplate>
                                <label style="padding: 4px 8px;">
                                    <TelerikCheckBox TValue="bool"
                                                     Value="@IsAllOptionItemsSelected()"
                                                     ValueChanged="@( (bool v) => ToggleOptionSortSelectAll(v) )">
                                    </TelerikCheckBox>
                                    &nbsp;Select All
                                </label>
                            </HeaderTemplate>
                            <ItemTemplate>
                                <input type="checkbox"
                                       class="k-checkbox k-checkbox-md"
                                       checked="@GetSelectedOptionSortChecked((int)itemDropdownContext.AsmDetailId)">
                                @itemDropdownContext.ItemNumber - @itemDropdownContext.ItemDesc
                            </ItemTemplate>
                        </TelerikMultiSelect>
                    </p>
                }
            }
          @if (SelectedItemDisplayCategory == 2)
            {
               
               <p>
                        <label>Activity</label>
                        <TelerikMultiSelect @bind-Value="@SelectedActivityIds"
                                            Data="@PurchasingActivityData"
                                            TextField="DropdownDescription"
                                            ValueField="PactivityId"
                                            Placeholder="Select activities"
                                            OnChange="@CascadeItems"
                                            Filterable="true"
                                        AutoClose="false"
                                            FilterOperator="StringFilterOperator.Contains"
                                            Width="100%">
                        </TelerikMultiSelect>
                    </p>
                @if (SelectedActivityIds.Count != 0)
                {
                    <p>
                        <label>Item</label>
                        <TelerikMultiSelect @bind-Value="@SelectedMasterItemIds"
                                            @ref="TradeSortSelectItemsRef"
                                            TextField="ItemDesc"
                                            ValueField="MasterItemId"
                                            AutoClose="false"
                                            Filterable="true"
                                            FilterOperator="StringFilterOperator.Contains"
                                            Placeholder="Select items"
                                            Data="@AllItemsTradeSort"
                                            Width="100%" Context="itemDropdownContext">
                            <HeaderTemplate>
                                <label style="padding: 4px 8px;">
                                    <TelerikCheckBox TValue="bool"
                                                     Value="@IsAllTradeActivityItemsSelected()"
                                                     ValueChanged="@( (bool v) => ToggleTradeActivitySelectAll(v) )">
                                    </TelerikCheckBox>
                                    &nbsp;Select All
                                </label>
                            </HeaderTemplate>
                            <ItemTemplate>
                                <input type="checkbox"
                                       class="k-checkbox k-checkbox-md"
                                       checked="@GetTradeActivityItemsChecked(itemDropdownContext.MasterItemId)">
                                @itemDropdownContext.ItemNumber - @itemDropdownContext.ItemDesc
                            </ItemTemplate>
                        </TelerikMultiSelect>
                    </p>
                }
            }
                        
            <button type="submit" class="btn btn-primary">Update</button>                   
            <button type="button" @onclick="CancelAddItem" class="btn btn-secondary">Cancel</button> 
            <div style=@submittingStyle>Adding items. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public ModelManagerItemModel ItemToAdd { get; set; } = new ModelManagerItemModel();

    [Parameter]
    public int? SelectedEstOptionId { get; set; }
    [Parameter]
    public string? JobNumber { get; set; }

    [Parameter]
    public int? SelectedHeaderId { get; set; }

    [Parameter]
    public int? SelectedSubdivisionId { get; set; }

    [Parameter]
    public EventCallback<ResponseModel<List<EstdetailDto>>> HandleAddSubmit { get; set; }

    public List<TradeDto>? AllTrades { get; set; }
    public List<MasterPlanDto>? AllMasterPlans { get; set; }
    public List<MasterOptionHeaderModel>? AllMasterOptions { get; set; }
    public List<AsmHeaderModel>? AllOptionsInMasterPlan { get; set; }
    public int SelectedTradeId;
    public List<PactivityModel>? AllActivitiesInTrade { get; set; }
    private List<PactivityDto> PurchasingActivityData { get; set; }
    public int SelectedActivityId;
    public List<int>? SelectedActivityIds { get; set; } = new List<int>();
    public List<int>? SelectedMasterItemIds { get; set; } = new List<int>();
    public List<int>? SelectedAsmDetailIds { get; set; } = new List<int>();
    public List<int>? SelectedAsmHeaderIds { get; set; } = new List<int>();
    public List<int>? SelectedTradeIds { get; set; } = new List<int>();
    public List<ModelManagerItemModel>? AllItemsOptionSort { get; set; } = new List<ModelManagerItemModel>();
    public List<ModelManagerItemModel>? AllItemsTradeSort { get; set; } = new List<ModelManagerItemModel>();
    public int SelectedItemId;
    public int SelectedOptionId;
    public int SelectedMasterPlanId;
    public List<string>? AllVarianceCategories { get; set; }
    public string SelectedVarianceCategory { get; set; } = "CO";
    public string VarianceCategory { get; set; } = "CO";
    public List<AsmHeaderModel>? AllASMHeaders { get; set; }
    public List<int>? SelectedAsmHeaders { get; set; }

    private TelerikMultiSelect<ModelManagerItemModel, int>? TradeSortSelectItemsRef;
    private TelerikMultiSelect<ModelManagerItemModel, int>? OptionSortSelectItemsRef;

    public List<ItemDisplayCategory> ItemDisplayCategoriesList { get; set; } = new List<ItemDisplayCategory>()
    {
        new ItemDisplayCategory()
        {
            DisplayCategory = "Plan / Option / Item",
            DisplayCategoryId = 1
        },
        new ItemDisplayCategory()
        {
            DisplayCategory = "Activity / Item",
            DisplayCategoryId = 2
        }
    };
    public int SelectedItemDisplayCategory { get; set; } = 2;
    private string submittingStyle = "display:none";
    protected override async Task OnParametersSetAsync()
    {
        var varianceCategoriesTask = BudgetService.GetVarianceCategoriesAsync();
        if (JobNumber != null)
        {
            if (SelectedSubdivisionId != null)
            {
                var activityTask = PoService.GetPurchasingActivitiesByJob(JobNumber);
                var plansTask = PlanService.GetPhasePlansInSubdivisionAsync((int)SelectedSubdivisionId);
                await Task.WhenAll(plansTask, activityTask, varianceCategoriesTask);
                AllMasterPlans = plansTask.Result.Value.Select(x => x.MasterPlan).ToList();
                PurchasingActivityData = activityTask.Result.Value;
                AllVarianceCategories = varianceCategoriesTask.Result.Value;
            }
            else
            {
                var activityTask = PoService.GetPurchasingActivitiesByJob(JobNumber);
                var plansTask = PlanService.GetMasterPlansAsync();
                await Task.WhenAll(plansTask, activityTask, varianceCategoriesTask);
                AllMasterPlans = plansTask.Result.Value;
                PurchasingActivityData = activityTask.Result.Value;
                AllVarianceCategories = varianceCategoriesTask.Result.Value;
            }
        }
    }
    public async Task Show()
    {
        IsModalVisible = true;
       // var tradeTask = ItemService.GetTradesAsync();
        // var varianceCategoriesTask = BudgetService.GetVarianceCategoriesAsync();
        // if(SelectedSubdivisionId != null)
        // {
        //     var plansTask =  PlanService.GetPhasePlansInSubdivisionAsync((int)SelectedSubdivisionId);
        //     await Task.WhenAll(tradeTask, varianceCategoriesTask, plansTask);
            
        //     AllMasterPlans = plansTask.Result.Value.Select(x => x.MasterPlan).ToList();
        // }
        // else
        // {
        //     var plansTask = PlanService.GetMasterPlansAsync();
        //     await Task.WhenAll(tradeTask, varianceCategoriesTask, plansTask);
        //     AllMasterPlans = plansTask.Result.Value;
        // }
        // AllVarianceCategories = varianceCategoriesTask.Result.Value;
       // AllTrades = tradeTask.Result.Value;
        SelectedTradeIds = new List<int>();
        SelectedAsmHeaderIds = new List<int>();
        SelectedAsmDetailIds = new List<int>();
        SelectedActivityIds = new List<int>();
        StateHasChanged();
    }
    private async Task LoadAllMasterOptionsAsync()
    {
        AllMasterOptions = (await OptionService.GetAllMasterOptionsAsync()).Value;
    }
    private async Task LoadAllMasterPlansAsync()
    {
        var allMasterPlansResponse = await PlanService.GetMasterPlansAsync();
        AllMasterPlans = allMasterPlansResponse.Value;
    }
    private async Task LoadTradesAsync()
    {
        AllTrades = (await ItemService.GetTradesAsync()).Value;
    }
    private async Task CascadePlanOptions()
    {
        AllOptionsInMasterPlan = (await ItemService.GetAssembliesInPlanIncludeInactiveAsync(SelectedMasterPlanId)).Value;
        SelectedOptionId = 0;
    }
    private async Task CascadeActivities(object newVal)
    {
        // AllActivitiesInTrade = (await ItemService.GetActivityByTradeAsync(SelectedTradeId)).Value;
        // SelectedActivityId = 0;

        var activityList = new List<PactivityModel>();
        foreach (var id in SelectedTradeIds)//todo : parallel foreach
        {
            var getItems = await ItemService.GetActivityByTradeAsync(id);
            activityList.AddRange(getItems.Value);
        }
        AllActivitiesInTrade = activityList;
    }
    private async Task CascadeItems(object newVal)
    {
        //TODO: check if newVal is changed, it calls the below twice
        //TODO: if a item removed at the trade level was already selcted, need to remove the cascaded items
        switch (SelectedItemDisplayCategory)
        {
            case 1:
                //AllItems = await ItemService.GetItemsInAssemblyAsync(SelectedOptionId);//OptionId here is asmheader id, not master optionid
                //AllItems = new List<ModelManagerItemModel>();
                var itemList = new List<ModelManagerItemModel>();
                // foreach (var id in SelectedAsmHeaderIds)//todo : parallel foreach
                // {
                //     var getItems = await ItemService.GetItemsInAssemblyAsync(id);
                //     itemList.AddRange(getItems.Value);
                // }
                await Parallel.ForEachAsync(SelectedAsmHeaderIds, async(id, token) =>
                {
                    var getItems = await ItemService.GetItemsInAssemblyAsync(id);
                    itemList.AddRange(getItems.Value);
                });
                AllItemsOptionSort = itemList;
                break;
            case 2:
                var itemList2 = new List<ModelManagerItemModel>();
                // foreach (var id in SelectedActivityIds)//todo : parallel foreach
                // {
                //     var getItems = await ItemService.GetItemsInActivityAsync(id);
                //     itemList2.AddRange(getItems.Value);
                // }
                await Parallel.ForEachAsync(SelectedActivityIds, async (id, token) =>
                {
                    var getItems = await ItemService.GetItemsInActivityAsync(id);
                    itemList2.AddRange(getItems.Value);
                });
                AllItemsTradeSort = itemList2;
                break;
            default:
                break;
        }
    }
    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        var items = new List<ModelManagerItemModel>();       
        var detailsToAdd = new List<EstdetailDto>();
        if (SelectedItemDisplayCategory == 2 && SelectedMasterItemIds != null && SelectedMasterItemIds.Any())
        {
            foreach (var item in SelectedMasterItemIds)
            {
                detailsToAdd.Add(new EstdetailDto()
                    {
                        //What is SelectedESTOPtionId???? 
                        EstactivityId = SelectedItemDisplayCategory == 1 ? null : SelectedActivityId,//wrong //setting this field to null when Plan/Option/Item is selected
                        EstoptionId = SelectedEstOptionId,//TODO: allow pick so they can add at the header level - it should go to base house or add base house if it doesn't exist
                        EstitemheaderId = SelectedHeaderId,
                        MasterItemsId = item,
                        VarianceJcCategory = VarianceCategory,
                        JcCategory = VarianceCategory,//TODO: not sure if it's supposed to be jc category or variance jc category, maybe it's different if it's a pending estimate??
                        OrderQty = 1//TODO: allow pick it
                    });
            }
            var responseItem = await BudgetService.AddEstdetailsAsync(detailsToAdd);
            await HandleAddSubmit.InvokeAsync(responseItem);
        }
        else if (SelectedItemDisplayCategory == 1 && SelectedAsmDetailIds != null && SelectedAsmDetailIds.Any())
        {
            //why is this duplicating above?
            //TODO: if the selected option is not already in the budget, it should be added, they may need to add new option, not put the items in existing wrong option
            foreach (var item in SelectedAsmDetailIds)//These are actually master item ids, fix it
            {
                var findItem = AllItemsOptionSort.FirstOrDefault(x => x.AsmDetailId == item);
                detailsToAdd.Add(new EstdetailDto()
                    {
                        //Selected EStOptionId is the option it's being added into. If picking from plan/option takeoff, should it only allow from that same plan/option?
                        //EstactivityId = SelectedItemDisplayCategory == 1 ? null : SelectedActivityId,//wrong //setting this field to null when Plan/Option/Item is selected
                        //EstoptionId = SelectedEstOptionId,//TODO: allow pick so they can add at the header level
                        Estoption = new EstoptionDto()
                        {
                            AsmHeaderId = AllOptionsInMasterPlan.FirstOrDefault(x => x.AsmHeaderId == findItem.AsmHeaderId)?.AsmHeaderId
                        },
                        EstitemheaderId = SelectedHeaderId,
                        MasterItemsId = findItem.MasterItemId,
                        VarianceJcCategory = VarianceCategory,
                        JcCategory = VarianceCategory,//TODO: not sure if it's supposed to be jc category or variance jc category, maybe it's different if it's a pending estimate??
                        OrderQty = 1//TODO: allow pick it
                    });
            }
            var responseItem = await BudgetService.AddEstOptionsAndEstdetailsAsync(detailsToAdd);
            await HandleAddSubmit.InvokeAsync(responseItem);
        }

        //var responseItem = await BudgetService.AddItemToBudgetAsync(itemToAdd);
        submittingStyle = "display:none";
        
    }
    async void CancelAddItem()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;        
    }

    public class ItemDisplayCategory
    {
        public string DisplayCategory { get; set; }
        public int DisplayCategoryId { get; set; }
    }
    bool GetSelectedOptionSortChecked(int id)
    {
        return SelectedAsmDetailIds.Contains(id);
    }
    bool IsAllOptionItemsSelected()
    {
        return SelectedAsmDetailIds.Count == AllItemsOptionSort.Count;
    }
    void ToggleOptionSortSelectAll(bool selectAll)
    {
        SelectedAsmDetailIds.Clear();

        if (selectAll)
        {
            SelectedAsmDetailIds.AddRange(AllItemsOptionSort.Select(x => (int)x.AsmDetailId));
        }

        TradeSortSelectItemsRef?.Rebind();
        OptionSortSelectItemsRef?.Rebind();
    }
    bool GetTradeActivityItemsChecked(int id)
    {
        return SelectedMasterItemIds.Contains(id);
    }
    bool IsAllTradeActivityItemsSelected()
    {
        return SelectedMasterItemIds.Count == AllItemsTradeSort.Count;
    }
    void ToggleTradeActivitySelectAll(bool selectAll)
    {
        SelectedMasterItemIds.Clear();

        if (selectAll)
        {
            SelectedMasterItemIds.AddRange(AllItemsTradeSort.Select(x => x.MasterItemId));
        }

        TradeSortSelectItemsRef?.Rebind();
        OptionSortSelectItemsRef?.Rebind();
    }
}
