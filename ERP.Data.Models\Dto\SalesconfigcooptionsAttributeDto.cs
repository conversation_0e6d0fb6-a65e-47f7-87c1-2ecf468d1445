﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class SalesconfigcooptionsAttributeDto : IMapFrom<SalesconfigcooptionsAttribute>
{
    public int SalesconfigcooptionsId { get; set; }

    public int OpAttrGroupItemId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedB { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    //public virtual OptionAttributeGroupItem OpAttrGroupItem { get; set; } = null!;

    //public virtual Salesconfigcooption Salesconfigcooptions { get; set; } = null!;
}
