﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class VarianceDto : IMapFrom<Variance>
{
    public string? VarianceCode { get; set; } 

    public string? VarianceDesc { get; set; }

    public int? DivId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

  //  public byte[] RecordTimeStamp { get; set; } = null!;

   // public virtual ICollection<ScheduleSactivity> ScheduleSactivities { get; set; } = new List<ScheduleSactivity>();
}
