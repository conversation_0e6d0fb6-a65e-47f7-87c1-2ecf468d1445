﻿
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject PlanService PlanService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <span style="font-weight:bold">Copy an Option</span>
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@OptionToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <div class="mb-3">
                <label class="form-label">New Option Name</label>
                <TelerikTextBox @bind-Value="OptionToAdd.OptionDesc" Width="200"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">New Option Code</label>
                <TelerikTextBox @bind-Value="OptionToAdd.OptionCode" Width="200"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Select Group</label>
                <TelerikDropDownList Data="@OptionGroups"
                @bind-Value="@OptionToAdd.OptionGroupId"
                                     TextField="OptionGroupName"
                                     ValueField="OptionGroupId"
                                     Filterable="true"
                                     FilterOperator="@StringFilterOperator.Contains"
                                     OnChange="@OnChangeGroupHandler"
                                     DefaultText="Select Option Group"
                                     Width="100%">
                    <DropDownListSettings>
                        <DropDownListPopupSettings Height="200px"></DropDownListPopupSettings>
                    </DropDownListSettings>
                </TelerikDropDownList>
            </div>
            <div class="mb-3">
                <label class="form-label">Select Option to Copy</label>
                <TelerikDropDownList Data="@AllMasterOptions"
                                     @bind-Value="@OptionToAdd.AsmHeaderId"
                                     TextField="OptionDesc"
                                     ValueField="AsmHeaderId"
                                     Filterable="true"
                                     FilterOperator="@StringFilterOperator.Contains"
                                     DefaultText="Select Option to Copy"
                                     Width="100%">
                    <DropDownListSettings>
                        <DropDownListPopupSettings Height="200px"></DropDownListPopupSettings>
                    </DropDownListSettings>
                </TelerikDropDownList>
            </div>
            <div class="mb-3">
                <label class="form-label">Notes</label>
                <TelerikTextArea @bind-Value="OptionToAdd.Notes"></TelerikTextArea>
            </div>
            <button type="submit" class="btn btn-primary">Add</button>
            <button type="button" @onclick="CancelAddOption" class="btn btn-secondary">Cancel</button>
            <div style="@submittingStyle">Submitting. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public MasterOptionHeaderModel OptionToAdd { get; set; } = new MasterOptionHeaderModel();
    public List<MasterOptionHeaderModel>? AllMasterOptions;
    public List<OptionGroupDto>? OptionGroups { get; set; }
    private string? submittingStyle = "display:none";
    [Parameter]
    public EventCallback<ResponseModel> HandleAddSubmit { get; set; }
    [Parameter]
    public int? SelectedOptionGroupId { get; set; }
    public async Task Show()
    {
        IsModalVisible = true;
        var getGroups = await OptionService.GetOptionGroupsAsync();
        OptionGroups = getGroups.Value;
        //var masterPlanDtos = await PlanService.GetMasterPlansAsync();
        //AllMasterOptions = masterPlanDtos.Select(x => new MasterPlanDto()
        //    {
        //        PlanName = x.PlanNum + " - " + x.PlanName,
        //        MasterPlanId = x.MasterPlanId,
        //        PlanTypeId = x.PlanTypeId
        //    }).ToList();
        StateHasChanged();
    }
    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        OptionToAdd.OptionGroupId = SelectedOptionGroupId;//The one from the option group it's being added into, not the one from the option being copied
        var responseItem = await OptionService.CopyMasterOptionAsync(OptionToAdd);
        submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(responseItem);
    }
    private void CancelAddOption()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;
    }
    public async Task OnChangeGroupHandler(object theUserInput)
    {
        if (theUserInput != null)
        {
            var userInput = (int)theUserInput;
            var getOptions = (await OptionService.GetMasterOptionsByGroupAsync(userInput)).Value;
            AllMasterOptions = getOptions.Select(x => new MasterOptionHeaderModel()
                {
                    AsmHeaderId = x.AsmHeaderId,
                    OptionDesc = x.OptionCode + " - " + x.OptionDesc,
                }).ToList();
        }
    }
}
