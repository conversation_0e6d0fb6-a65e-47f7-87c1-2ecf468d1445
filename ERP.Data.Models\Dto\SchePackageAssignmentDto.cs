﻿using ERP.Data.Models.Abstract;

namespace ERP.Data.Models;

public class SchePackageAssignmentDto : IMapFrom<SchePackageAssignment>
{
    public int ItemAssignmentId { get; set; }

    public int PackageItemId { get; set; }

    public string? DepartmentAssigned { get; set; }

    public string? Manager { get; set; }

    public string? Notes { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }
}
