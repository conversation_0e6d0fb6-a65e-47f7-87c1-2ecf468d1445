﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class TemplateMilestoneDto : IMapFrom<TemplateMilestone>
{
    public int TemplateMid { get; set; }

    public int TemplateId { get; set; }

    public int MilestoneId { get; set; }

    public int? Seq { get; set; }

    public DateTime Createddatetime { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public string? UpdatedBy { get; set; }

    //public byte[] RecordTimeStamp { get; set; } = null!;

    public Template? Template { get; set; } 
}
