﻿using ERP.Data.Models.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class ImportCostModel
    {
        public List<CostModel>? Costs { get; set; }
        [DateInFutureValidation]
        public DateTime EffectiveDate { get; set; }
        public CostEditType? CostEditType { get; set; }
        public int SubdivisionId { get; set; }
        public int? SubNumber { get; set; }  
    }
}
