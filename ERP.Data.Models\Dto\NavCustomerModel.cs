﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{



    public class NavCustomer
    {
        public string? No { get; set; }
        public string? Name { get; set; }
        //public string Address { get; set; }
        //public string Address_2 { get; set; }
        //public string City { get; set; }
        //public string County { get; set; }
        //public string Post_Code { get; set; }
        //public string Phone_No { get; set; }
        //public string MobilePhoneNo { get; set; }
        //public string E_Mail { get; set; }
        public string? CustomerPostingGroup { get; set; }
        public string? GlobalDimension1Code { get; set; }
        public string? GlobalDimension2Code { get; set; }
    }


    public class NavJob
    {
        public string? No { get; set; }
        public string? BilltoCustomerNo { get; set; }
        public string? Description { get; set; }
        public string? GlobalDimension1Code { get; set; }
        public string? GlobalDimension2Code { get; set; }
    }

    
}
