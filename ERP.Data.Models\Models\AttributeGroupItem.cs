﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class AttributeGroupItem
{
    public int AttrGroupItemId { get; set; }

    public int AttributeItemId { get; set; }

    public int AttributeGroupId { get; set; }

    public decimal? PriceChange { get; set; }

    public int Seq { get; set; }

    public bool DefaultSelection { get; set; }

    public bool? IsActive { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? Updatedby { get; set; }

    public byte[] RecordTimeStampe { get; set; } = null!;

    public virtual MasterAttributeGroup AttributeGroup { get; set; } = null!;

    public virtual MasterAttributeItem AttributeItem { get; set; } = null!;

    public virtual ICollection<OptionAttributeItem> OptionAttributeItems { get; set; } = new List<OptionAttributeItem>();
}
