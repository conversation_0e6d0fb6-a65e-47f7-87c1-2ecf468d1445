﻿using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class ElevationPlanSubdivisionModel
{
    public Guid? Id { get; set; }
    public List<ElevationPlanSubdivisionModel>? Children { get; set; }
    public bool Has<PERSON>hilden { get; set; }
    public MaterialColorSchemeDto? MaterialColorScheme { get; set; }
    public SubdivisionDto? Subdivision { get; set; }
    public PhasePlanDto? PhasePlan { get; set; }
    public AvailablePlanOptionDto? PlanOption { get; set; }
    public List<PhasePlanDto?>? PhasePlans { get; set; }
    public List<AvailablePlanOptionDto>? PlanOptions { get; set; }
    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }


}
