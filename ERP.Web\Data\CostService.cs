﻿using Azure.Core;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.ExtendedProperties;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using NLog;
using System.Text;
using Telerik.Blazor.Components;
using Telerik.Blazor.Components.FileSelect;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class CostService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public CostService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<ResponseModel<List<PactivityModel>>> GetPurchasingActivitiesAsync()
        {
            var groups = new ResponseModel<List<PactivityModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/cost/GetPurchasingActivities/");
                var responseString = await response.Content.ReadAsStringAsync();
                groups = JsonConvert.DeserializeObject<ResponseModel<List<PactivityModel>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return groups;
        }

        public async Task<ResponseModel<List<ModelManagerItemModel>>> GetItemsInActivityAsync(int activityId)
        {
            var items = new ResponseModel<List<ModelManagerItemModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/cost/GetItemsInActivity/{activityId}");
                var responseString = await response.Content.ReadAsStringAsync();
                items = JsonConvert.DeserializeObject<ResponseModel<List<ModelManagerItemModel>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return items;
        }

        public async Task<ResponseModel<List<CostModel>>> GetCostsForItemAsync(int itemId)
        {
            var costs = new ResponseModel<List<CostModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/cost/GetCostsForItem/{itemId}");
                var responseString = await response.Content.ReadAsStringAsync();
                costs = JsonConvert.DeserializeObject<ResponseModel<List<CostModel>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return costs;
        }
        public async Task<ResponseModel<List<CostModel>>> GetCostsForItemsAsync(List<int> itemIds)
        {
            var costs = new ResponseModel<List<CostModel>>();
            try
            {
                costs = await _downstreamAPI.PostForUserAsync<List<int>, ResponseModel<List<CostModel>>>(
                           "DownstreamApi", itemIds,
                            options => {
                                options.RelativePath = "api/cost/GetCostsForItems/";
                            });
                return costs;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return costs;
        }
        public async Task<ResponseModel<List<CostModel>>> GetCostsForItem(int itemId)
        {
            //this is syncronous version of above to work with telerik items selected handler. 
            //this seems wrong. Is there a correct way to syncronously run async code? 
            var costs = new ResponseModel<List<CostModel>>();
            try
            {
                var response = _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/cost/GetCostsForItem/{itemId}").GetAwaiter().GetResult();
                var responseString =  response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
                costs = JsonConvert.DeserializeObject<ResponseModel<List<CostModel>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return costs;
        }

        public async Task<ResponseModel<bool>> CheckRolloverDueCostsAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/cost/CheckRollOverDueCosts/");
                var responseString = await response.Content.ReadAsStringAsync();
                var isAnyRollOverDueCosts = JsonConvert.DeserializeObject<ResponseModel<bool>>(responseString);
                return isAnyRollOverDueCosts;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<bool>();
        }

        public async Task<ResponseModel<List<CostDto>>> RolloverAllCostsAsync()
        {    
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/cost/RolloverAllCosts/");
                var responseString = await response.Content.ReadAsStringAsync();
                var rolledOverCosts = JsonConvert.DeserializeObject<ResponseModel<List<CostDto>>>(responseString);
                return rolledOverCosts;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<CostDto>>();
        }

        public async Task<ResponseModel<CostModel>> AddCostAsync(CostModel costToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<CostModel, ResponseModel<CostModel>>(
                            "DownstreamApi", costToAdd,
                             options => {
                                 options.RelativePath = "api/cost/AddCost/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<CostModel>() { Value = costToAdd, IsSuccess = false };
        }

        public async Task<ResponseModel<List<CostModel>>> ApplyCostsAsync(List<CostModel> costsToAdd)
        {
            var costs = new List<CostModel>();
            try
            {
                //Try sending in batches of 10000 because large file makes request too large?
                int count = 0;
                while(count < costsToAdd.Count)
                {
                    var costsToSend = costsToAdd.Skip(count).Take(10000).ToList();
                    var response = await _downstreamAPI.PostForUserAsync<List<CostModel>, ResponseModel<List<CostModel>>>(
                            "DownstreamApi", costsToSend,
                             options => {
                                 options.RelativePath = "api/cost/ApplyCosts/";
                             });
                    count += 10000;
                    costs.AddRange(response.Value);
                }
                return new ResponseModel<List<CostModel>> () { Value = costs, IsSuccess = true, Message = "Success. Applied imported costs." };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<CostModel>>() { Value = null, IsSuccess = false, Message = "Failed to import costs. Please try again. Contact BI if problem persists." };
        }

//        public async Task<ResponseModel<List<CostDto>>> CopyCostsToSubdivisionAsync(CopyCostToSubdivisionModel costToCopy)
//        {
//            var costs = new CopyCostToSubdivisionModel();
//            try
//            {
//                var response = await _downstreamAPI.PostForUserAsync<CopyCostToSubdivisionModel, ResponseModel<List<CostDto>>>(
//                            "DownstreamApi", costToCopy,
//                             options => {
//                                 options.RelativePath = "api/cost/CopyCostsToSubdivision/";
//                             });
//                return response;

//            }
//            catch (MicrosoftIdentityWebChallengeUserException ex)
//            {
//                _consentHandler.HandleException(ex);
//            }
//            catch (Exception ex)
//            {
//#if DEBUG
//                _logger.Debug(ex);
//#else
//                    _logger.Error(ex);
//#endif
                
//            }

//            return new ResponseModel<List<CostDto>>() { IsSuccess = false, Message = "Something went wrong" };
//        }
        public async Task<ResponseModel<List<CostModel>>> RemoveAreaCostsAsync(List<CostModel> costsToAdd)
        {
            var costs = new ResponseModel<List<CostModel>>();
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<CostModel>, ResponseModel<List<CostModel>>>(
                            "DownstreamApi", costsToAdd,
                             options => {
                                 options.RelativePath = "api/cost/RemoveAreaCosts/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return costs;
        }
        public async Task<ResponseModel<CostDto>> UpdateCostAsync(CostModel costToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<CostModel, ResponseModel<CostDto>>(
                            "DownstreamApi", costToUpdate,
                             options => {
                                 options.RelativePath = "api/cost/UpdateCost/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<CostDto>() { Value = null, IsSuccess = false, Message = "Error updating costs." };
        }

       
        public async Task<ResponseModel<CostModel>> DeleteCostAsync(CostModel costToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<CostModel, ResponseModel<CostModel>>(
                            "DownstreamApi", costToUpdate,
                             options => {
                                 options.RelativePath = "api/cost/DeleteCost/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<CostModel>() { IsSuccess = false, Message = "Error deleting cost" };
        }

        public async Task<ResponseModel<List<CostModel>>> DeleteCostsAsync(List<CostModel> costToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<CostModel>, ResponseModel<List<CostModel>>>(
                            "DownstreamApi", costToUpdate,
                             options => {
                                 options.RelativePath = "api/cost/DeleteCosts/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<CostModel>>() { IsSuccess = false, Message = "Error deleting costs"};
        }

        public async Task<ResponseModel<byte[]>> DownloadExcelCostsAsync(SupplierandActivitiesListSelectModel model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<SupplierandActivitiesListSelectModel, ResponseModel<byte[]>>(
                           "DownstreamApi", model,
                            options => {
                                options.RelativePath = "api/cost/DownloadCostsExport/";
                            });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<byte[]>() { Value = null, IsSuccess = false };
        }

        public async Task<ResponseModel<List<CostModel>>> ImportExcel(FileSelectFileInfo file)
        {
            
            try
            {
                var importedCosts = new List<CostModel>();
                var fileData = new byte[file.Stream.Length];
                await file.Stream.ReadAsync(fileData);

                using (var ms = new MemoryStream(fileData))
                {
                    using (var excelWorkbook = new ClosedXML.Excel.XLWorkbook(ms))
                    {
                        var nonEmptyDataRows = excelWorkbook.Worksheet(1).RowsUsed();

                        foreach (var dataRow in nonEmptyDataRows)
                        {
                            //for row number check
                            if (dataRow.RowNumber() >= 2)
                            {
                                //to get column # 3's data
                                //var cell = dataRow.Cell(3).Value;
                                importedCosts.Add(new CostModel()
                                {
                                    MasterItemId = dataRow.Cell(1).IsEmpty()? 0 :(int)dataRow.Cell(1).Value,
                                    SubNumber = dataRow.Cell(2).IsEmpty()? 0 : (int)dataRow.Cell(2).Value,
                                    CostsId = dataRow.Cell(5).IsEmpty()? 0: (int)dataRow.Cell(5).Value,
                                    ItemDesc = dataRow.Cell(9).IsEmpty() ? "" : (string)dataRow.Cell(9).Value,
                                    //ItemNumber = dataRow.Cell(8).IsEmpty()? null : (string)dataRow.Cell(8).Value,//Excel turned string value "003" into number 3 when cell clicked, making this not work
                                    ItemNumber = dataRow.Cell(10).IsEmpty() ? null : dataRow.Cell(10).Value.ToString(),
                                    UnitCost = dataRow.Cell(11).IsEmpty() ? null : (double?)dataRow.Cell(11).Value
                                });
                            }
                        }
                    }
                    ms.Close();
                } 
                return new ResponseModel<List<CostModel>>() { Value = importedCosts, IsSuccess = true, Message = "imported costs"};
            }

            catch(Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

                return new ResponseModel<List<CostModel>>() { Value = null, IsSuccess = false, Message = "imported costs failed" };
            }

        }
    }
}
