﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ERP.Data.Models.Dto;

public class SubdivisionDto : IMapFrom<Subdivision>
{
    public int SubdivisionId { get; set; }

    public int? AreaId { get; set; }

    public DateTime? AcquireDate { get; set; }

    public decimal? Acreage { get; set; }

    public bool? AddLotPremiumToBasePrice { get; set; }

    public bool? AddModScatteredLots { get; set; }

    public string? Address1 { get; set; }

    public string? Address2 { get; set; }

    public bool? AgentsModOthersCustomer { get; set; }

    public bool? AgentsSeeOthersCustomer { get; set; }

    public bool? AllowApprovalOfAllOptions { get; set; }

    public bool? AllowBackupReserv { get; set; }

    public bool? AllowCounterOffers { get; set; }

    public bool? AllowDateEffPricing { get; set; }

    public bool? AllowDupOptions { get; set; }

    public bool? AllowFollowup { get; set; }

    public bool? AllowLotTransfers { get; set; }

    public bool? AllowMultiStepApproval { get; set; }

    public bool? AllowNegOption { get; set; }

    public bool? AllowRentVsBuy { get; set; }

    public bool? ApplyVat { get; set; }

    public bool? ApplyVatRebate { get; set; }

    public bool? AutoFreeExpiredLots { get; set; }

    public string? CableName { get; set; }

    public string? CablePhone { get; set; }

    public bool? CanAddSpec { get; set; }

    public bool? CanClearSpec { get; set; }

    public bool? CanDeleteProspect { get; set; }

    public bool? CanModOptions { get; set; }

    public bool? CanModSpec { get; set; }

    public bool? ChangeBasePrice { get; set; }

    public bool? ChangeIncentivesAfterContract { get; set; }

    public bool? ChangeLotPremium { get; set; }

    public bool? ChangeOptPriceAfterContract { get; set; }

    public bool? ChangePriceAllOption { get; set; }

    public bool? ChangePriceCustOpt { get; set; }

    public bool? ChangeQtyAllOption { get; set; }

    public bool? ChangeQtyCustOpt { get; set; }

    public bool? ChangeSpecBasePrice { get; set; }

    public bool? ChangeTypeAllOption { get; set; }

    public bool? ChangeTypeCustOpt { get; set; }

    public string? City { get; set; }

    public string? Contracted { get; set; }

    public string? Country { get; set; }

    public int? CountryId { get; set; }

    public string? County { get; set; }

    public string? CustPortalCssUrl { get; set; }

    public string? CustPortalLogoUrl { get; set; }

    public int? DefaultOptionTypeId { get; set; }

    public string? DefaultPassword { get; set; }

    public string? DocsFolder { get; set; }

    public string? ElectricName { get; set; }

    public string? ElectricPhone { get; set; }

    public string? Email { get; set; }

    public string? EmailPosToSuperviser { get; set; }

    public string? EngineerName { get; set; }

    public string? EngineerPhone { get; set; }

    public string? EntityName { get; set; }

    public string? EntityNum { get; set; }

    public bool? EstHazardInsurType { get; set; }

    public decimal? EstHazardInsurValue { get; set; }

    public decimal? EstPropertyTax { get; set; }

    public bool? ExcludeFromReports { get; set; }

    public int? ExpireDays { get; set; }

    public string? Fax { get; set; }

    public string? GasName { get; set; }

    public string? GasPhone { get; set; }

    public bool? HideRetainOptBySoc { get; set; }

    public bool? HideRevertToDirt { get; set; }

    public decimal? HoaFees { get; set; }

    public string? HoaName { get; set; }

    public string? HoaPhone { get; set; }

    public bool? HoaType { get; set; }

    public bool? KeepHistoryLotTransfer { get; set; }

    public int? LanguageSetId { get; set; }

    public DateTime? LastChanged { get; set; }

    public int? LastUser { get; set; }

    public decimal? Latitude { get; set; }

    public string? LegalName { get; set; }

    public bool? LockOptionDesc { get; set; }

    public bool? LockSpecBasePrice { get; set; }

    public bool? LockSpecOptionPrice { get; set; }

    public decimal? Longitude { get; set; }

    public string? MarketingDesc { get; set; }

    public string? MarketingName { get; set; }

    public bool? MaxIncentiveType { get; set; }

    public decimal? MaxIncentiveValue { get; set; }

    public decimal? MaxLoanAmt { get; set; }

    public decimal? MiscellaneousFee { get; set; }

    public string? Phone { get; set; }

    public string? ProtectFromMaster { get; set; }

    public DateTime? PublicDate { get; set; }

    public string? PublicReportNum { get; set; }

    public int? QuickTrafficDemoId { get; set; }

    public decimal? RealtorCommission { get; set; }

    public string? RefuseName { get; set; }

    public string? RefusePhone { get; set; }

    public bool? SelectOptionsByRoom { get; set; }

    public string? Service1 { get; set; }

    public string? Service1phone { get; set; }

    public string? Service2 { get; set; }

    public string? Service2phone { get; set; }

    public string? Service3 { get; set; }

    public string? Service3phone { get; set; }

    public string? Service4 { get; set; }

    public string? Service4phone { get; set; }

    public string? Service5 { get; set; }

    public string? Service5phone { get; set; }

    public string? Service6 { get; set; }

    public string? Service6phone { get; set; }

    public string? Service7 { get; set; }

    public string? Service7phone { get; set; }

    public string? Service8 { get; set; }

    public string? Service8phone { get; set; }

    public decimal? SetupFee { get; set; }

    public string? SewerName { get; set; }

    public string? SewerPhone { get; set; }

    public bool? ShouldSupressRulesForUnavailableOptions { get; set; }

    public string? StandardSubdivisionName { get; set; }

    public string? State { get; set; }

    public int? StateId { get; set; }

    public string? SubdivisionClass { get; set; }

    [Required(ErrorMessage = "The Subdivision Name field is required")]
    public string? SubdivisionName { get; set; }

    [Required(ErrorMessage = "The Subdivision Num field is required")]
    public string? SubdivisionNum { get; set; }

    public string? SubdivisionStatus { get; set; }

    public int? TargetUnits { get; set; }

    public string? TelephoneName { get; set; }

    public string? TelephonePhone { get; set; }

    public string? TextMsgAccountId { get; set; }

    public string? TextMsgPassword { get; set; }

    public string? TextMsgUserId { get; set; }

    public int? TotalLots { get; set; }

    public string? TwitterWidgetId { get; set; }

    public bool? UseElectronicDocStorage { get; set; }

    public bool? UseElectronicSignature { get; set; }

    public string? VmhVmb { get; set; }

    public string? WarrantyEnabled { get; set; }

    public string? WarrantyShortName { get; set; }

    public string? WaterName { get; set; }

    public string? WaterPhone { get; set; }

    public string? Workdays { get; set; }

    public int? WorkZoneId { get; set; }

    public string? Zip { get; set; }

    public string? Zoning { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public string? ErrorMessage { get; set; }

    public bool? IsActiveAdult { get; set; }
    public bool? LinkWithErp { get; set; }
    public string? CommunityLabel { get; set; }
    public bool? Blocked { get; set; }
    public bool BoolBlocked { get; set; }
    public string? EntityName2 { get; set; }
    public string? EntitySignatureBlock { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<SubdivisionDto, Subdivision>().ReverseMap();
    }

    //public virtual ICollection<Cost> Costs { get; set; } = new List<Cost>();

    //public virtual ICollection<Job> Jobs { get; set; } = new List<Job>();

    //public virtual ICollection<PhasePlan> PhasePlans { get; set; } = new List<PhasePlan>();

    //public virtual ICollection<SubdivisionContact> SubdivisionContacts { get; set; } = new List<SubdivisionContact>();

    //public virtual ICollection<TradeSupplier> TradeSuppliers { get; set; } = new List<TradeSupplier>();
}
