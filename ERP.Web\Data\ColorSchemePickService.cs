﻿using ERP.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Web.Data
{
    public class ColorSchemePickService
    {
        private bool? _isChanged;
        private ElevationPlanSubdivisionModel _subdivisionPlanElevation;

        public bool? IsChanged
        {
            get
            {
                return _isChanged;
            }
            set
            {
                _isChanged = value;
                NotifyChanged();
            }
        }

        public ElevationPlanSubdivisionModel SubdivisionPlanElevation
        {
            get
            {
                return _subdivisionPlanElevation;
            }
            set
            {
                _subdivisionPlanElevation = value;
                NotifyChanged();
            }
        }

        public Func<Task>? OnDataChanged { get; set; }

        private async Task NotifyChanged() => await OnDataChanged?.Invoke();
    }
}
