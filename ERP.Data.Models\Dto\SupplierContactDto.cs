﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace ERP.Data.Models;

public class SupplierContactDto : IMapFrom<SupplierContact>
{
    public int SupplierContactId { get; set; }
    public string? SupplierShortName { get; set; }//this is what BC uses for Id
    public int SubNumber { get; set; }
    public string? BcContactId { get; set; }
    public int ContactId { get; set; }
    public string? ContactKey { get; set; }//BC won't have id

    public string? SupplierContactTitle { get; set; }

    public string? SupplierContactPurch { get; set; }

    public string? SupplierContactSched { get; set; }

    public string? SupplierContactIsadmin { get; set; }

    public int? SupplierContactNumber { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public ContactDto? Contact { get; set; } 

    public SupplierDto? SubNumberNavigation { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<SupplierContactDto, SupplierContact>().ReverseMap();
    }
}
