﻿@using Microsoft.Graph
@using System.Security.Cryptography

<a class="nav-link dropdown-toggle waves-effect waves-light nav-user" data-bs-toggle="dropdown" href="#" role="button"
   aria-haspopup="false" aria-expanded="false">
    <span class="ms-1 nav-user-name hidden-sm" style="margin-right: 4px">@UserDisplayName</span>
    @* <span><a href="MicrosoftIdentity/Account/SignOut">Log out</a></span> *@
    @if (PhotoBytes != null)
    {
        <img width="48" height="48" src="data:image;base64,@PhotoBytes" class="rounded-circle thumb-sm" />
    }
</a>
@code {
    [Inject]
    public GraphServiceClient? Client { get; set; }
    public string? PhotoBytes { get; set; }
    public string? UserDisplayName { get; set; }

    protected override async Task OnInitializedAsync()
    {
        // Chameleon, Hedgehog, Puma, Velociraptor, Capybara
        string[] profileImages =
        {
      "iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAAAsTAAALEwEAmpwYAAAFhElEQVR4nO1YXWwUVRQeSfxPjCbGBzXhRX0hnTu7szM7JWi7hZhqYlS6s2W77f7/FU0UgmApSR8pM9vt/7bb/cPGEJSCpfXJB6mojyaNCA9ofFAjJJgoPEBLgWvO7N7dmdnpdgtFNma/5KSTe+7tfN+555x7dyiqjjrqqKOOOuqoo/bQnW3kwimrHEzzF8NpYTmSs+Jo1opDGevtUJpfDkzxV3yT3HfuScsRf3rr8/8pub4+alPHkCnqHDL9qR+P5ARXJCtciOYEXK1FsgIOTHF/uCfZQ/eVuDggPC7GUK8YQ1dFGWHXsBkT3+6j3JZwVlgkpMIZKw5M8dg3wWFvwoLDWTVhZScMxYSmuGvuMXbPhhIPJ9mHRRm974jR/wBxYkAO/JEMb4/krNdCaestiCYQcQ4yxXlgIIaQDCR5Zaw9zuDOURb7kyUfMf8kd75zmn7unsm3yfROUUaX1WTAdsUZHM0KODghONwJ9qJjgMG+Sa5IwDVi1sz3Jy1FH8xT+zqGTMa7keJvuEcQf1fEdx5hXnbE0IL6RY4Yg51DJuweY5W8hZc4JObvEhFz8eWecYuGpCdREgAppfZ1jbGadcFUaUfCGestd8LyxrrIixLy2iW0REhBxMJp45zV7wzkvpICSW2UQTRZA4T1qRgt+NoHGCzGkCKkKCLN33INm4XqyMt0QEmRQZMmEquZXgBEV9n+tFUz7houCejUpVew8B6/LrW6Rktrginuekdi2zOVyQ80NIgxtAIvgJ7dfbQR98634MMLrbj/TCvunbMpY5UEOAdL+eyIGec5PKvXRArp6BrWjsN64ivs6vmKAtpjaBG6AlkAhOVv39QYjFUSABZK5yMKYsgYdByyBhqAfjyc0e5YvvBLqUXMPW7eZ0jeOci+s2uQWVFPhsgD6TbPDtzm3a489y+0rinAPc6Wp0qMUeoIDJ6LqTWSL3wock3KFcZJYfsmCqmZ4pfDSfaJcgFx8wV9oULaKAK823Gbb4fyfPjM2gJg66FoobUa+dXWHmeUcwH+qteTZkC6GYyRw883wU1ryDvirNmTYJf023W3KXSv5i10JX0rhhopdKUVOFyLArpGzZ8oly2dAKWI52zKTlRbxBthHUMm5YQ28pG6cCdU96ZQij9XzaWrmjZ6v81Z6HL+JPd7vnV+Lj7SO9eCD5624Y+/aMYfzTThvZ+9ij84tg2/N70Vd+e0ES9ZI95zfFv5SwqHUDhTQZxqTrRwuYPI7opr2+hqppwbWQF3jXKvKCL2nWj6+tB8Cza0uRbcM9uMD5xqxvtnmhQ7cLJJEXxwzlb2z9VXhtUIqOdE1dfqjDV/Eq8hgBxw3gQXL6bRvpnXTvTO24xFrGIfHivfARLVSgJIh4kamP6iZ2QgMp9G/E+abrT/ZJMAu3HgVPOlntnmpZ5Z282euebbB0/b7hyatylF3DNrU9IMilmUy6NVTX1UqqlItrq6Cqd5ODCvUxuB7oywI5ITftOTWe8cPTpl+klRpltEif5eLyCQ5EDsHWojEM1afzGK5nrnrIbW4ZcetUv0YlkdZa1VrV8TkZxwdi1y1cx5exA9bZdRRpThlx/9lyjRe4nPIdNv6QXABwKqVuAa5p8SJfpnTapI6Ffid8bYZ/UCginuKlUrEGWUKBGnvxQH0Atqv14A3J/8Se4HqlZgl+grhNy7/Q0v6v1tEnpd34q7xs27qVqBKKNlQm6nbNqs/95kl9A3xA+fc0Jp/gaFqU1UrUCU0I+qCJ8VJcR4+jY/1taPtthlNKO/SrgnLMY/bB4URJmOVnOAecZZ7EtYvqJqDph6yC6hiTUugMvehOVTqpYhSnSrKNOzdgldtkv0zfYB5pJrxHTOk+COd0xxzIPmV0cdddTxP8O/4dQyTUWSOFoAAAAASUVORK5CYII=",
      "iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAAAsTAAALEwEAmpwYAAADvklEQVR4nO2YXWgUVxiGt9JEEFsoWMRbrwrFlhaLIDFzvk3AZM+ZjbHZEkOp2SAh2T1nk5gIRU3OpIlQtcGLWHChtCGlIdGmQpuiRDEXTSHuTErFn1x41+iFojaF+hfNfmV212RJdjc/O5tJYV54L2ZYzj7v+b5z5sy4XI4cOXLkyJEFEhS+4RTeT77HKTkcou5y11qT8IAQHqVk9lqFTwQD5Iz8Y94XJSXrOYNw/B5MHvLufMP8XcCnbOSMyIAK79rJ/5qg8JcJJxiMcAqtnJGHieu4KXmcfM0pXOYUOgUl9+L3SNuq0dbTgrc4g3Ocun21jG2IzXAy7ArMKbkpfb58szKCuVVBlYqcBRDewg+S/vi5YBDNNkCiKk84gxeJNjuXswBBBn4rgDOakmFLoTklvYKRs4LC+djCzHEATslzcy2FKNQKBi1BqpDsAjByJeeznskqKcsqgGCk284AAZ+ycdnQUsp1wdLCbUFKGjiD+7ZWgJIO4YGdjbt3bTG37SXOOly0FZqlMpk6yJRNSwrAPYTaDwzz3bKsFhIMnq4BaHzlkOrevCT4kOp+T1Dyg93AYp45JT8HS5XtGeGDjHxsN6jIFIKRZyGmFGTuf0pO2g0qUlYA/l7SQy3W/7MnxbXho1Ul0TtDzVOoawJxka2Uq7CPUzJjN7RIuNPP8NGVzxENLW5d/oRGOC8lfOyhYdEJ0wp3i3L897cjc/Bz7k49+0x5J3ZwszlESAX8seNTnInIVPBmFaJotO9I20aJStgC37Hfg7cHGxdAR76tw8qCD1H/ri4RQruJhtyQJgB8vdrgTXuK8Jfj1Tg91pZy1k1wM4DRkwgQ9wj+ceztVMeIVWuhxrIi7D1ciQ8uJS3UZVlO/XqiemDkdCB+ajVfuFfjxaW5vAj7WquyANdmHW7ymdWrma1C7JMHhdO5AD9Z68XRcC0+/b01a3Ccq8JXC9eB+YXAAuAGrxtP1e/BC11+vDPUYiG0Zu5Gj9DQggsebuZnDs7g+kp7+ssDKg5oVXjte45PRo9aBKvNxIDN3UeX/Tiu+XFMvpn+8wmFfk5JRFByVzCYafC6oy17i6cP7S1++cVnHuyqK8PwwQrsa6vC4VM1ON4TwMmhZnx5Nc3evZjHtRdoyGHUtSaMyGKMyK04Il93WSHUpZZlqaOoy/toaBNoyFHUtUEc18Koyz/n9fI0GvIMRto/whsy3xL4FQSYSPuASTV2pL0UdXkr23GsDGC2g39Z4xvhvNhJ09AeZjOOI0eOHDly5Po/6z86gDs0rCeLXgAAAABJRU5ErkJggg==",
      "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",
      "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",
      "iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAAAsTAAALEwEAmpwYAAAEMElEQVR4nO2YXYgbVRTHp34gSCtYRRQriC8q+IFKoT7om4IVfbCuT351JhvNujNJdjfdjybnRGNyb3ZnEre7bU23m9ybUJUtCD7oQ0V9KIVSfBAffLJCqyhi+7CY0hbpXrlxN5l8dTOTZRNh/nBeLtlz/79zzty9M4riyZMnT548OVQpHX2QpfB5pd+Vy/lvLhC8v5SOPVMkMLAWjMCfnKJg0/iY0k/iqYk7OIW3GIUCp3CWU7gmjbYLRuMBpR9UoLiLU/ycUbhqN2iOBETo1d0iF9/XBgCucQJzx8jE7T0zzwi+zgistDKYT8bE/P6Rth2oghD4i1MYRMQbNh3gwFjg8kKbCruI72U3G/eQYIUkPiSLxSl+xCh8zQj+xAiWbd38e3XtBCeYYml8OYd467oAVtC3svjB5EYBrJrB04ziNKNwmFP4jlFYdpcLLnGKn5TS+GT7DkTeO88IbCjARgcjsMIIfFqcwbuaAHhqaqjXBnnnnf09n4rtrAMoId7GKP7w/4GA5ZYjZbzywoVE4G3BSO9N8vVH6tcFC7fXARST+AgjOMUJzHAKC5zAcUbhG0bhXJ9CZJVOJI9ATjDea8O8KeBSYRrv7ghCCGULo3Cy96axMUaUTpWn0UcZhX/6a4zwhOJEjIC1SeMhOgKg8YuOAI6mI9s4xd96XXm+GkcTk5cdAVS6QPG1XhtfiI+LTGhQzI8HDzoGqEDIC1ePzOdgTFhBTWRD/rLr2y8j+GxPRub98Yp5y9BENugnilulI+o22cbNNM8IiGzYXzEvw9TVJ1wDUN23IxvyiwKJdW+Mdva7w9GRqnlL18pLAwM3ugbIBrWHZaIDY4Hr3p3yH0bFwQlDZMPvCCvoE5mgr1LFuX3DIgejgjkoQH31tbNKN7J03661ZLOj71ba27hhIRkV2dBgrWotIhMarDyU65lfTEzW/Z1paD92BWAa2nP2hHNjQ00QRyByXfP2ODQVWv/ksQPo6sXuOmCoexpM/MEoftbw0J1ykvOYiXdygj+3Avg4NtoMHn7z3i4AtL0NCYslijvsL+mc4JXZWf0WJ3nzif33MYK/NAIciY+XmwCG1YhrANNQg/ZkM7r6hlxvunanYk87zb1I8AFO8Xx9N2OnTF39sg5A1066BrB0LW57oFYyIf89cl1+BmEUL9jGKOwmPyOxp+oB0CcUZYtlqLkagHrGNYBpaN+2qwQn8IVtjPJu8udTsZ21/xOwvIS4Va7PDKsvdg0wPzS01TTUK7V2qnvqAChO2KrnahNeea2t5ji0tm4Z2u6uABDxJtNQZ23H2Tm5Vg8ASVvry/JtzskeRRJ9XL6017oYrV4ZMrr2kq3zpx2Zl8eWHBdbgqvyNGr4PLPUcIKccXRBJPhV7Tut/HD83whaYW27aWgJ09CWq3vre0OOADx58uTJk9JC/wL2vb6RqcioWQAAAABJRU5ErkJggg=="
    };

        try
        {
            if (Client != null)
            {
                var getProfile = await Client.Me.GetAsync();

                var photo = await Client.Me.Photo.Content.GetAsync();
                var user = await Client.Me.GetAsync();

                using StreamReader? reader = photo is null
                    ? null
                    : new StreamReader(new CryptoStream(photo, new ToBase64Transform(), CryptoStreamMode.Read));
                this.PhotoBytes = reader is null
                    ? null
                    : await reader.ReadToEndAsync();

                if (user != null)
                {
                    UserDisplayName = user.DisplayName;
                }
                else
                {
                    UserDisplayName = string.Empty;
                }
            }
        }
        catch (Exception)
        {
            Random rand = new Random();
            int index = rand.Next(profileImages.Length);
            PhotoBytes = profileImages[index];

            if (index == 0)
            {
                UserDisplayName = "Anonymous Chameleon";
            }
            else if (index == 1)
            {
                UserDisplayName = "Anonymous Hedgehog";
            }
            else if (index == 2)
            {
                UserDisplayName = "Anonymous Puma";
            }
            else if (index == 3)
            {
                UserDisplayName = "Anonymous Velociraptor";
            }
            else if (index == 4)
            {
                UserDisplayName = "Anonymous Capybara";
            }
        }
    }
}