﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class VpoApprovalSeqDto : IMapFrom<VpoApprovalSeq>
    {
        public int VpoApprovalId { get; set; }

        public int VpoGroupId { get; set; }

        public string LevelCode { get; set; }

        public int? Seq { get; set; }

        public decimal? Threshold { get; set; }

        public string? EmpUserId { get; set; }

        public int? RoleId { get; set; }

        public DateTime CreatedDateTime { get; set; }

        public DateTime? UpdatedDateTime { get; set; }

        public string? UpdatedBy { get; set; }

        public string? CreatedBy { get; set; }

        public bool IsActive { get; set; }

        public string? SuccessMessage { get; set; }

        public string? ErrorMessage { get; set; }

        public string? RoleDescription { get; set; }
    }
}
