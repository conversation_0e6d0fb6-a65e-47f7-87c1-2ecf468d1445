﻿
@inject SalesPriceService SalesPriceService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <h4 class="page-title">Save Worksheet As</h4>
    </WindowTitle>
    <WindowContent>
        <p>Save a copy of @WorksheetToCopy?.WorksheetName</p>
        <EditForm Model="@WorksheetToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />
            <label class="form-label">New Worksheet Name </label>
            <TelerikTextBox @bind-Value="@WorksheetToAdd.WorksheetName"></TelerikTextBox>
            <br />
            <label class="form-label">New Worksheet Description </label>
            <TelerikTextBox @bind-Value="@WorksheetToAdd.WorksheetDesc"></TelerikTextBox>
            <br />
            <div class="customSeparator">
                <button type="submit" class="btn btn-primary">Add</button>
                <button type="button" @onclick="CancelAddWorksheet" class="btn btn-secondary">Cancel</button>
                <div style=@ShowLoading>Submitting. Please wait...</div>
                <div style=@ShowError>Something went wrong.</div>
            </div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public WorksheetDto WorksheetToAdd { get; set; } = new WorksheetDto();
    public string ShowLoading{ get; set; } = "display:none";
    public string ShowError { get; set; } = "display:none";

    [Parameter]
    public WorksheetDto? WorksheetToCopy { get; set; }

    [Parameter]
    public EventCallback<ResponseModel<WorksheetDto>> HandleAddSubmit { get; set; }

    public void Show()
    {
        IsModalVisible = true;

    }      

    private async void HandleValidAddSubmit()
    {
        ShowLoading = "";
        WorksheetToAdd.WorksheetId = WorksheetToCopy.WorksheetId;//set the new worksheet Id to the one to copy from so it knows where to get the items
        var response = await SalesPriceService.CopyWorksheetAsync(WorksheetToAdd);
        ShowLoading = "display:none";       
        await HandleAddSubmit.InvokeAsync(response);
    }
    async void CancelAddWorksheet()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;        
    }
}
