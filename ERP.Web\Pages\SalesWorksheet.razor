﻿@page "/salesworksheet"

@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject PlanService PlanService
@inject SalesPriceService SalesPriceService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JSRuntimeService
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, Accounting")]
@using ERP.Data.Models.Dto
@using ERP.Data.Models
@using ERP.Web.DocumentProcessing
@using System.Diagnostics
@using Telerik.DataSource.Extensions
@using System.Collections.ObjectModel
@using Telerik.Windows.Documents.Spreadsheet.FormatProviders
@using Telerik.Windows.Documents.Spreadsheet.FormatProviders.OpenXml.Xlsx
@using Telerik.Windows.Documents.Spreadsheet.Model

<style type="text/css">
    .mytreeclass .k-grid-norecords {
        height: 400px; /* Grid Height - 40px  This is for the dropzone size when the grid is empty */ 
    }
    .k-window-title {
        font-weight: bold;
    }
    /*.mytreeclass .k-drag-cell {
            display:none;
        }*/
/*    .mytreeclass .k-table-th.k-header.k-drag-cell{
        visibility: collapse;
    }
    .mytreeclass .k-drag-cell.k-touch-action-none{
        visibility: collapse;

    }*/
    .mytreeclass .k-drag-col{
        visibility: collapse;
    }
    .mytreeclass .k-header {
        min-width: 200px;
    }

    .mytreeclass  {
       overflow-x: auto;
    }
    .k-drag-clue {
        color: white;
    }

    .k-icon.k-icon-with-modifier {
        display: none;
    }

    .SubdivisionTable .k-hierarchy-cell {
        display: none;
    }

    .k-splitbar {
        width: 15px;
        color: black;
        padding-left:5px;
        padding-right:5px;
    }

    .pane {
        padding: 8px;
        overflow-x: auto;
    }

    .k-splitter .k-pane {
        overflow-x: auto !important;
    }

    .k-splitter-horizontal{
        overflow-x: auto !important;
    }
    .page-wrapper .page-content{     
       /* padding-right: 100px !important;
        margin-right: 100px !important;*/ 
/*        width: 95% !important;*/
    }
    .page-wrapper{
       /* padding-right: 100px !important;
        margin-right: 100px !important;
            width: 80% !important;*/
    }
    .k-table-td{
        padding: 4px !important;
        font-size: 11px;
    }

    .k-table-td { /*these settings are to prevent the 0 width columns from affecting the column height*/
        padding: 4px !important;
        font-size: 11px;
        height: 30px !important;
        white-space: nowrap !important; 
        overflow: visible;
    }

    div.k-grid td,
    .k-grid td.k-command-cell {
        padding-top: 1px;
        padding-bottom: 1px;
        line-height: 1px;
    }

        .k-grid td.k-command-cell .k-button {
            margin: 1px;
            padding-top: 1px;
            padding-bottom: 1px;
            line-height: 1px;
            height: auto;
        }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>

<PageTitle>Sales | Worksheets</PageTitle>
<TelerikLoaderContainer Visible="@loading" Text="Please wait..." />
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<TelerikTooltip TargetSelector=".tooltip-target-errors" ShowOn="@TooltipShowEvent.Hover">
    <Template>
        @{
            var dataAttributes = context.DataAttributes;
            <div>
                @if (dataAttributes["cell"] == "errors" && dataAttributes["rowtype"] == "Activity" && dataAttributes["errorcount"] != "0")
                {
                    <p>Number of Errors: @dataAttributes["errorcount"]</p>
                    <p>Reason: @dataAttributes["errorreason"]</p>
                }                
            </div>
        }
    </Template>
</TelerikTooltip>

<div class="card" style="background-color:#2e5771">
    <div class="card-body" style="padding:0.5rem">
        <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Sales Pricing Worksheets</h7>
    </div>
</div>

<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="/">Home</a></li>
    <li class="breadcrumb-item"><a href="/manageprice">Manage Pricing</a></li>
    <li class="breadcrumb-item active">Sales Worksheets</li>
</ol>


<NavigationLock ConfirmExternalNavigation="@UnsavedChanges" OnBeforeInternalNavigation="BeforeInternalNavigation" />
<TelerikTooltip TargetSelector=".tooltip-target" />
<div class="row">
    <TelerikSplitter Width="100%" Height="100%" Orientation="@SplitterOrientation.Horizontal">
        <SplitterPanes>
            <SplitterPane Size="30%" Min="15%" Max="25%" Collapsible="true">
                <div class="pane">
                    <h4 class="card-header">Subdivision / Plan / Option</h4>
                    @if (SubdivisionPlanData == null)
                    {
                        <p><em>Loading...</em></p>
                    }
                    else
                    {
                        <div>
                            <TelerikTreeList Data="@SubdivisionPlanOptionTreeData"
                            SelectionMode="@TreeListSelectionMode.Multiple"
                            ScrollMode="@TreeListScrollMode.Virtual"
                            PageSize="40"
                            IdField="Id"
                            Height="80vh"
                            RowHeight="40"
                            ParentIdField="ParentId"
                            @ref="@SubdivisionPlanTreeList"
                            OnExpand="@OnExpandSubdivTree"
                            OnStateInit="((TreeListStateEventArgs<WorksheetTreeModel> args) => OnSubdivTreeStateInitHandler(args))"
                            RowDraggable="true"
                            OnRowDrop="@((TreeListRowDropEventArgs<WorksheetTreeModel> args) => OnRowDropHandler(args))"
                            Width="100%">
                                <TreeListColumns>
                                    <TreeListCheckboxColumn Width="40px" SelectChildren="true">
                                    </TreeListCheckboxColumn>
                                    <TreeListColumn Field="SubdivisionId" Visible="false"></TreeListColumn>
                                    <TreeListColumn Field="SubdivisionName" Title="Subdivision/Plan/Option" Expandable="true">
                                        <Template>
                                            @{
                                                var item = context as WorksheetTreeModel;
                                                if (item.WorksheetOptionId != null)
                                                {
                                                    @($"{item.OptionCode} - {item.OptionName}")
                                                }
                                                else if (item.PlanId != null)
                                                {
                                                    @($"{item.PlanName} - {item.PlanNumber}")
                                                }
                                                else
                                                {
                                                    @($"{item.SubdivisionName}")
                                                }
                                            }
                                        </Template>
                                    </TreeListColumn>
                                    <TreeListColumn Field="PlanCode" Visible="true" Width="0px"></TreeListColumn>
                                    <TreeListColumn Field="PlanName" Visible="true" Width="0px"></TreeListColumn>
                                    <TreeListColumn Field="PlanNumber" Visible="true" Width="0px"></TreeListColumn>
                                    <TreeListColumn Field="OptionCode" Visible="true" Width="0px"></TreeListColumn>
                                    <TreeListColumn Field="OptionName" Visible="true" Width="0px"></TreeListColumn>
                                    <TreeListColumn Field="PlanId" Visible="false" Width="0px"></TreeListColumn>
                                    <TreeListColumn Field="OptionId" Visible="false" Width="0px"></TreeListColumn>
                                </TreeListColumns>
                                <TreeListSettings>
                                    <TreeListRowDraggableSettings DragClueField="DragClue"></TreeListRowDraggableSettings>
                                </TreeListSettings>
                                <TreeListToolBarTemplate>
                                    <TreeListSearchBox />
                                </TreeListToolBarTemplate>
                            </TelerikTreeList>
                        </div>
                    }
                </div>
            </SplitterPane>
            <SplitterPane Collapsible="false">
                <div class="pane">
                    <h4 class="card-header mb-3">Worksheet @SelectedWorksheet?.WorksheetName</h4>
                    @if(!showWorksheet)
                    {
                        //no worksheet selected, show the dashboard
                        @if (ListAvailableWorksheets == null)
                        {
                            <p><em>Loading...</em></p>
                            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingWorksheets />
                        }
                        else
                        {
                            <TelerikGrid Data="@ListAvailableWorksheets"
                            SelectionMode="@GridSelectionMode.Single"
                            OnRowDoubleClick="@OnWorksheetSelectedHandler">
                                <GridColumns>
                                    <GridColumn Title="Name" Field="WorksheetName" />
                                    <GridColumn Title="Created By" Field="CreatedBy" />
                                    <GridColumn Title="Locked By" Field="LockedBy" />
                                    <GridColumn Title="Created Date" Field="CreatedDateTime" DisplayFormat="{0:MM/dd/yyyy}" />
                                    <GridColumn Title="Updated Date" Field="UpdatedDateTime" DisplayFormat="{0:MM/dd/yyyy}" />
                                </GridColumns>

                                <GridToolBarTemplate>
                                    <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>
                                    <GridCommandButton Command="AddFromToolbar" OnClick="@AddWorksheet" Icon="@FontIcon.Plus" Class="k-button-add">Add</GridCommandButton>
                                </GridToolBarTemplate>
                            </TelerikGrid>
                        }
                    }
                    else
                    {
                        //worksheet is selected show the worksheet hide the dashboard
                        @if (IsSelectedWorksheetLoading)
                        {
                            <p><em>Loading...</em></p>
                            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsSelectedWorksheetLoading />
                        }
                        else
                        {
                            <div>
                                <TelerikTreeList Data="@SelectedWorksheetData"
                                SelectionMode="@TreeListSelectionMode.Multiple"
                                PageSize="40"
                                ItemsField="Children"
                                Class="mytreeclass"
                                Resizable="true"
                                Width="100%"
                                Height="80vh"
                                RowHeight="40"
                                @bind-SelectedItems="@SelectedItems"
                                RowDraggable="true"
                                OnRowDrop="@((TreeListRowDropEventArgs<WorksheetTreeModel> args) => OnRowDropHandler(args))"
                                OnStateInit="((TreeListStateEventArgs<WorksheetTreeModel> args) => OnWorksheetTreeStateInitHandler(args))"
                                OnRowClick="@OnRowClickHandler"
                                OnCollapse="@OnCollapse"
                                OnUpdate="@UpdateItem"
                                OnEdit="@EditItem"
                                EditMode="@TreeListEditMode.Incell" 
                                Sortable="true"
                                @ref="@WorksheetTreeList">
                                    <TreeListColumns>
                                        <TreeListCheckboxColumn Width="40px" SelectAll="true" SelectChildren="true" Locked="true" />
                                        @* <TreeListColumn Field="Id" Title="WorksheetId" Expandable="true"></TreeListColumn>
                                <TreeListColumn Field="PlanId" Title="PlanId" Expandable="true"></TreeListColumn>*@
                                        <TreeListColumn Field="SubdivisionName" Title="Subdivision/Option/Activity" Width="400px" Resizable="true" Expandable="true" Editable="false" Locked="true">
                                            <Template>
                                                @{
                                                    var item = context as WorksheetTreeModel;
                                                    if (item.WorksheetActivityId != null)
                                                    {
                                                        @($"{item.ActivityName} - {item.Vendor}")
                                                    }
                                                    else if (item.WorksheetOptionId != null)
                                                    {
                                                        @($"{item.OptionCode} - {item.OptionName}")
                                                    }
                                                    else
                                                    {
                                                        @($"{item.SubdivisionName} - {item.PlanName} - {item.PlanNumber}")
                                                    }
                                                }
                                            </Template>

                                        </TreeListColumn>
                                        <TreeListColumn Field="PlanName" Editable="false" Width="0px"></TreeListColumn>
                                        <TreeListColumn Field="PlanNumber" Editable="false" Width="0px"></TreeListColumn>
                                        <TreeListColumn Field="OptionName" Editable="false" Width="0px"></TreeListColumn>
                                        <TreeListColumn Field="ActivityName" Title="Activity Name" Editable="false" Width="0px"></TreeListColumn>
                                        <TreeListColumn Field="Vendor" Title="Vendor" Editable="false" Width="0px"></TreeListColumn>
                                        @*<TreeListColumn Field="DisplayRowIndex" Editable="false" Visible="true" Width="40px"></TreeListColumn>*@
                                        <TreeListColumn Field="OptionCode" Title="Option Code" Editable="false" Visible="true" Width="120px"></TreeListColumn>
                                        <TreeListColumn Field="LumpSum" Title="Lump Sum" Editable="false" Width="50px">
                                            <Template>
                                                @{
                                                    ItemToEdit = context as WorksheetTreeModel;
                                                    <TelerikCheckBox Enabled="false" @bind-Value="ItemToEdit.LumpSum"></TelerikCheckBox>
                                                }

                                            </Template>
                                        </TreeListColumn>
                                        <TreeListColumn Field="Errors" Title="Errors" Editable="false" Width="50px">
                                            <Template>
                                                @{
                                                    var item = context as WorksheetTreeModel;
                                                    var rowType = item.WorksheetActivityId != null ? "Activity" : item.WorksheetOptionId != null ? "Option" : "Plan";
                                                    if(rowType == "Activity" && item.ErrorCount > 0)
                                                    {
                                                        <span Class="tooltip-target-errors" data-cell="errors" data-rowtype="@rowType" data-errorcount="@item.ErrorCount" data-errorreason="@item.ErrorReason">@item.ErrorCount</span>
                                                    }
                                                    else
                                                    {
                                                        <span>@item.ErrorCount</span>
                                                    }

                                                }
                                            </Template>
                                        </TreeListColumn>
                                        <TreeListColumn Field="PercentOfTotal" Title="% of Total" DisplayFormat="{0:0.##}" Editable="false" Width="80px"></TreeListColumn>
                                        <TreeListColumn Field="Cost" DisplayFormat="{0:C2}" Title="Cost" Editable="@worksheetEditable" Width="120px">
                                            <EditorTemplate>
                                                @{
                                                    ItemToEdit = context as WorksheetTreeModel;
                                                    if (ItemToEdit?.WorksheetActivityId != null)
                                                    {
                                                        <TelerikNumericTextBox @bind-Value="ItemToEdit.Cost"></TelerikNumericTextBox>
                                                    }
                                                    else
                                                    {
                                                        <TelerikNumericTextBox Enabled="false" @bind-Value="ItemToEdit.Cost"></TelerikNumericTextBox>
                                                        //@(ItemToEdit.Cost != null ? ItemToEdit.Cost.Value.ToString("c") : "")
                                                    }
                                                }
                                            </EditorTemplate>
                                        </TreeListColumn>
                                        <TreeListColumn Field="OptionId" Visible="false" Editable="false"></TreeListColumn>
                                        <TreeListColumn Field="IsActive" Visible="false" Editable="false"></TreeListColumn>
                                        <TreeListColumn Field="SellPrice" DisplayFormat="{0:C2}" Title="Sell Price" Width="120px" Editable="false" />
                                        <TreeListColumn Field="Markup" DisplayFormat="{0:C2}" Title="Markup" Width="120px" Editable="false" />

                                        <TreeListColumn Field="MarkupType" Title="Markup Type" Width="120px" Editable="@worksheetEditable">
                                            <Template>
                                                @{
                                                    var item = context as WorksheetTreeModel;
                                                    var stringMarkupType = MarkupTypes.Where(x => x.Value == item.MarkupType).FirstOrDefault()?.Text;
                                                    @stringMarkupType
                                                }
                                            </Template>
                                            <EditorTemplate>
                                                @{
                                                    ItemToEdit = context as WorksheetTreeModel;
                                                    if (ItemToEdit?.WorksheetOptionId != null)
                                                    {
                                                        <TelerikDropDownList Data="@MarkupTypes"
                                                        @bind-Value="@ItemToEdit.MarkupType"
                                                        TextField="Text"
                                                        ValueField="Value"
                                                        Width="170px">
                                                        </TelerikDropDownList>
                                                    }
                                                    else
                                                    {
                                                        //@ItemToEdit.MarkupType
                                                        <TelerikDropDownList Data="@MarkupTypes"
                                                        Enabled="false"
                                                        @bind-Value="@ItemToEdit.MarkupType"
                                                        TextField="Text"
                                                        ValueField="Value"
                                                        Width="170px">
                                                        </TelerikDropDownList>
                                                    }
                                                }
                                            </EditorTemplate>
                                        </TreeListColumn>
                                        <TreeListColumn Field="MarketValue" DisplayFormat="{0:C2}" Width="120px" Editable="@worksheetEditable" Title="Market Value">
                                            <EditorTemplate>
                                                @{
                                                    ItemToEdit = context as WorksheetTreeModel;
                                                    if (ItemToEdit?.WorksheetOptionId != null && ItemToEdit.MarkupType == 1)
                                                    {
                                                        <TelerikNumericTextBox @bind-Value="ItemToEdit.MarketValue"></TelerikNumericTextBox>
                                                    }
                                                    else
                                                    {
                                                        <TelerikNumericTextBox Enabled="false" @bind-Value="ItemToEdit.MarketValue"></TelerikNumericTextBox>
                                                        //@(ItemToEdit.MarketValue != null ? ItemToEdit.MarketValue.Value.ToString("c") : "")
                                                    }
                                                }
                                            </EditorTemplate>
                                        </TreeListColumn>
                                        <TreeListColumn Field="MarkupPercent" DisplayFormat="{0:0.##}" Width="120px" Editable="@worksheetEditable"  Title="Markup Percent">
                                            <EditorTemplate>
                                                @{
                                                    ItemToEdit = context as WorksheetTreeModel;
                                                    if (ItemToEdit?.WorksheetOptionId != null && ItemToEdit.MarkupType == 0)
                                                    {
                                                        <TelerikNumericTextBox @bind-Value="ItemToEdit.MarkupPercent"></TelerikNumericTextBox>
                                                    }
                                                    else
                                                    {
                                                        <TelerikNumericTextBox Enabled="false" @bind-Value="ItemToEdit.MarkupPercent"></TelerikNumericTextBox>
                                                        //@(ItemToEdit.MarkupPercent != null ? ItemToEdit.MarkupPercent.Value.ToString("f2") : "")
                                                    }
                                                }
                                            </EditorTemplate>
                                        </TreeListColumn>
                                        <TreeListColumn Field="Margin" DisplayFormat="{0:C2}" Width="120px" Title="Margin" Visible = "false" Editable="false"></TreeListColumn>
                                        <TreeListColumn Field="MarginPercent" DisplayFormat="{0:0.##}" Width="120px" Editable="@worksheetEditable" Title="Margin Percent">
                                            <EditorTemplate>
                                                @{
                                                    ItemToEdit = context as WorksheetTreeModel;
                                                    if (ItemToEdit?.WorksheetOptionId != null && ItemToEdit.MarkupType == 2)
                                                    {
                                                        <TelerikNumericTextBox @bind-Value="ItemToEdit.MarginPercent"></TelerikNumericTextBox>
                                                    }
                                                    else
                                                    {
                                                        <TelerikNumericTextBox Enabled="false" @bind-Value="ItemToEdit.MarginPercent"></TelerikNumericTextBox>
                                                        //@(ItemToEdit.MarginPercent != null ? ItemToEdit.MarginPercent.Value.ToString("f2") : "")
                                                    }
                                                }
                                            </EditorTemplate>
                                        </TreeListColumn>
                                        <TreeListColumn Field="PriceDate" DisplayFormat="{0:MM/dd/yyyy}" Title="Price Date" Width="120px" Editable="false"></TreeListColumn>
                                        <TreeListCommandColumn Width="160px" Locked="true">
                                            @{
                                                var row = context as WorksheetTreeModel;
                                                if (row.WorksheetOptionId != null && row.WorksheetActivityId == null)//1 is items in the base house option (ie the plan acts)
                                                {
                                                    <TreeListCommandButton Title="Refresh Costs" Class="tooltip-target k-button-success" Command="RepriceOption" Icon="@FontIcon.ArrowRotateCcw" OnClick="@RepriceOption"></TreeListCommandButton>
                                                    if(row.IsBaseHouse != true && AllowEdit)
                                                    {
                                                        <TreeListCommandButton Title="Apply Pricing" Class="tooltip-target k-button-success" Command="ApplyPricingOption" OnClick="@ApplyPricingOption" Icon="@FontIcon.Check"></TreeListCommandButton>
                                                    }                                                  
                                                }
                                                if (row.OptionName != "BASE HOUSE")
                                                {
                                                    //base house only removed if other options deleted
                                                    <TreeListCommandButton Title="Delete" Class="tooltip-target k-button-danger" Command="DeleteItem" OnClick="@DeleteItemHandler" Icon="@FontIcon.Trash"></TreeListCommandButton>
                                                }
                                            }
                                        </TreeListCommandColumn>
                                    </TreeListColumns>
                                    <TreeListToolBarTemplate>
                                        <TreeListSearchBox DebounceDelay="200">
                                        </TreeListSearchBox>
                                        <TreeListCommandButton Title="Close Worksheet" Command="CloseWorksheet"Class="tooltip-target" OnClick="@CloseWorksheet">Close</TreeListCommandButton>
                                        <TreeListCommandButton Title="Clear Selected" Command="ClearSelected" Class="tooltip-target" OnClick="@ClearSelected">Clear</TreeListCommandButton>                                        
                                        <TreeListCommandButton Title="New Worksheet" Command="AddNewWorksheet" OnClick="@AddWorksheet" Icon="@FontIcon.FileAdd" Class="tooltip-target k-button-add"></TreeListCommandButton>
                                        <TreeListCommandButton Title="Add Plan/Option" Command="AddPlanOptions" OnClick="@AddPlanOptionToHandler" Icon="@FontIcon.Plus" Class="tooltip-target k-button-add"></TreeListCommandButton>
                                        <TreeListCommandButton Title="Save" Command="SaveAll" OnClick="@SaveWorksheetHandler" Icon="@FontIcon.Save" Class="tooltip-target k-button-success"></TreeListCommandButton>
                                        <TreeListCommandButton Title="Save As" Command="SaveAs" OnClick="@SaveAsHandler" Icon="@FontIcon.Save" Class="tooltip-target k-button-success"></TreeListCommandButton>
                                        <TreeListCommandButton Title="Fill Down" Command="FillDown" OnClick="@FillDown" Icon="@FontIcon.ArrowDown" Class="tooltip-target k-button-success"></TreeListCommandButton>
                                        <TreeListCommandButton Title="Delete Worksheet" Command="DeleteWorksheet" OnClick="@DeleteWorksheetHandler" Icon="@FontIcon.Trash" Class="tooltip-target k-button-danger"></TreeListCommandButton>
                                        <TreeListCommandButton Title="Cancel Changes" Command="CancelChanges" OnClick="@CancelChangesWorksheetHandler"  Class="tooltip-target k-button-danger" Icon="@FontIcon.Undo"></TreeListCommandButton>
                                        @{
                                            if (AllowEdit)
                                            {
                                                <TreeListCommandButton Title="Apply Pricing For Selected Options" Command="ApplyPricing" OnClick="@ApplyPricing" Icon="@FontIcon.Check" Class="tooltip-target k-button-success"></TreeListCommandButton>
                                            }
                                        }
                                        <TreeListCommandButton Title="Refresh Costs" Command="RepriceWorksheet" OnClick="@RepriceWorksheet" Icon="@FontIcon.ArrowRotateCcw" Class="tooltip-target k-button-success"></TreeListCommandButton>
                                        <TreeListCommandButton Title="Export To Excel" Command="ExcelExport" OnClick="@ExportWorksheet" Icon="@FontIcon.FileExcel" Class="tooltip-target k-button-add"></TreeListCommandButton>
                                        <TelerikFileSelect @ref="ImportFileRef" Class="import"
                                        AllowedExtensions="@AllowedExtensions"
                                        Multiple="false"
                                        MaxFileSize="@MaxSize"
                                        OnSelect="@OnFileImportHandler">
                                            <SelectFilesButtonTemplate>
                                                <TelerikFontIcon Icon="@FontIcon.Import" />
                                                &nbsp; Import
                                            </SelectFilesButtonTemplate>
                                        </TelerikFileSelect> 
                                        <span>
                                            Worksheet Locked By: @worksheetLockedBy
                                            @if (!worksheetEditable)
                                            {
                                                <TelerikButton ButtonType="ButtonType.Button" OnClick="@BreakLock">Break Lock</TelerikButton>
                                            }
                                        </span>
                                    </TreeListToolBarTemplate>
                                </TelerikTreeList>
                            </div>
                        }
                    }
                </div>
            </SplitterPane>
        </SplitterPanes>
    </TelerikSplitter>
</div>
<ERP.Web.Components.AddPlanOptionToWorksheet @ref="AddPlanOptionToWorksheetModal" SelectedWorksheetId=@SelectedWorksheet.WorksheetId HandleAddSubmit="HandleAddPlanOptionToWorksheetSubmit"></ERP.Web.Components.AddPlanOptionToWorksheet>
<ERP.Web.Components.CreateWorksheet @ref="AddWorksheetModal" HandleAddSubmit="HandleValidAddWorksheetSubmit"></ERP.Web.Components.CreateWorksheet>
<ERP.Web.Components.RepriceOption @ref="RepriceWorksheetOptModal" RepriceOption1=@SelectedWorksheetOpt HandleAddSubmit="HandleValidRepriceOptionSubmit"></ERP.Web.Components.RepriceOption>
<ERP.Web.Components.RepriceWorksheet @ref="RepriceWorksheetModal" RepriceWorksheet1=@SelectedWorksheet HandleAddSubmit="HandleValidRepriceWorksheetSubmit"></ERP.Web.Components.RepriceWorksheet>
<ERP.Web.Components.CopyWorksheet @ref="CopyWorksheetModal" WorksheetToCopy=@SelectedWorksheet HandleAddSubmit="HandleValidCopyWorksheetSubmit"></ERP.Web.Components.CopyWorksheet>
<ApplyPricingSelect @ref="ApplyPriceModal" HandleAddSubmit="HandleApplyPricing" ApplyPrice="ApplyNewPricing"></ApplyPricingSelect>

@code {

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    protected ERP.Web.Components.AddPlanOptionToWorksheet? AddPlanOptionToWorksheetModal { get; set; }
    protected ERP.Web.Components.RepriceWorksheet? RepriceWorksheetModal { get; set; }
    protected ERP.Web.Components.RepriceOption? RepriceWorksheetOptModal { get; set; }
    protected ERP.Web.Components.CreateWorksheet? AddWorksheetModal { get; set; }
    protected ERP.Web.Components.CopyWorksheet? CopyWorksheetModal { get; set; }
    private TelerikTreeList<WorksheetTreeModel>? WorksheetTreeList { get; set; }
    private TelerikTreeList<WorksheetTreeModel>? SubdivisionPlanTreeList { get; set; }
    private TelerikFileSelect ImportFileRef;
    public List<WorksheetDto>? ListAvailableWorksheets { get; set; }
    public List<WorksheetPlanModel>? WorksheetPlanData { get; set; }
    public List<WorksheetOptModel>? WorksheetOptsData { get; set; }
    public PlanOptionPriceCostModel? SelectedOption { get; set; }
    public WorksheetDto SelectedWorksheet { get; set; } = new WorksheetDto();
    public WorksheetOptModel SelectedWorksheetOpt { get; set; } = new WorksheetOptModel();
    public ApplyPricingModel? ApplyNewPricing { get; set; } = new ApplyPricingModel();
    public ObservableCollection<WorksheetTreeModel> SelectedWorksheetData { get; set; }
    // public List<CombinedTreeModel>? SelectedWorksheetData { get; set; }
    // public List<WorksheetTreeModel>? SelectedWorksheetDataToSave { get; set; } //This makes a copy in order to track deleted items to save later
    public List<SubdivisionDto> SubdivisionPlanData { get; set; }
    public bool IsLoadingWorksheetsList { get; set; } = false;
    public bool IsSelectedWorksheetLoading { get; set; } = false;
    private string loadingWorksheetStyle = "display:none";
    private string loadingItemStyle = "display:none";
    public bool IsLoadingOption { get; set; } = false;
    public bool IsLoadingWorksheets { get; set; } = false;
    public bool IsLoadingPlanData { get; set; } = false;
    public List<WorksheetTreeModel> SubdivisionPlanOptionTreeData { get; set; }
    public string? ErrorMessage;
    public bool? ShowError;
    public WorksheetTreeModel ItemToEdit { get; set; }
    public List<MarkupTypeSelect>? MarkupTypes { get; set; }
    public IEnumerable<WorksheetTreeModel>? SelectedItems { get; set; }
    private int lastOnChangeValue { get; set; }
    private bool UnsavedChanges { get; set; } = false;
    private string? LastColumnEdited {get; set; }
    public WorksheetTreeModel? PreviousSelected { get; set; }//for use in select range with shift key
    public WorksheetTreeModel? CurrentSelected { get; set; }//for use in select range with shift key
    private WorksheetTreeModel? ItemBeingEdited { get; set; }
    public IEnumerable<object> ExpandedItems { get; set; } = new List<WorksheetTreeModel>();
    private bool worksheetEditable = false;
    private string? worksheetLockedBy = "";
    private bool showWorksheet {get; set;} = false;//whether the dashboard or a selected worksheet is showing
    public ApplyPricingSelect? ApplyPriceModal { get; set; }
    public bool loading { get; set; } = false;
    List<string> AllowedExtensions { get; set; } = new List<string>() { ".xlsx" };
    public int MaxSize { get; set; } = 10 * 1024 * 1024; //10 MB
    public bool IsImported { get; set; } = false;
    private bool AllowEdit { get; set; } = true;

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    private async Task BeforeInternalNavigation(LocationChangingContext context)
    {
        if (UnsavedChanges)
        {
            var proceed = await Dialogs.ConfirmAsync("You have unsaved changes, are you sure you want to leave this page?");

            if (!proceed)
            {
                context.PreventNavigation();
            }
            else
            {
                //clear worksheet lock if user had it locked
                if(SelectedWorksheet != null && SelectedWorksheet.WorksheetId != 0)
                {
                    await SalesPriceService.ClearWorksheetLockAsync(SelectedWorksheet.WorksheetId);
                }

            }
        }
        else
        {
            //clear lock
            if (SelectedWorksheet != null && SelectedWorksheet.WorksheetId != 0)
            {
                await SalesPriceService.ClearWorksheetLockAsync(SelectedWorksheet.WorksheetId);
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        IsLoadingWorksheets = true;
        IsLoadingPlanData = true;
        SelectedItems = Enumerable.Empty<WorksheetTreeModel>();
        MarkupTypes = new List<MarkupTypeSelect>()
        {
            new MarkupTypeSelect{ Text = "Markup %", Value = 0},
            new MarkupTypeSelect{ Text = "Market Value", Value = 1},
            new MarkupTypeSelect{ Text = "Margin %", Value = 2},
        };
        var worksheetsDashboardTask = SalesPriceService.GetWorksheetsAsync();
        var subdivisionDataTask = SubdivisionService.GetSubdivisionsAsync();
        await Task.WhenAll(new Task[]{worksheetsDashboardTask, subdivisionDataTask});
        ListAvailableWorksheets = worksheetsDashboardTask.Result;
        var data = subdivisionDataTask.Result;
        SubdivisionPlanData = data.Value;
        SubdivisionPlanOptionTreeData = SubdivisionPlanData.Select(x => new WorksheetTreeModel()
            {
                Id = Guid.NewGuid(),
                SubdivisionId = x.SubdivisionId,
                SubdivisionName = x.SubdivisionName,
                DragClue = x.SubdivisionName,
                HasChildren = true
            }).OrderBy(x => x.SubdivisionName).ToList();
        var addDummyPlanData = SubdivisionPlanOptionTreeData.Select(x => new WorksheetTreeModel()
            {
                Id = Guid.NewGuid(),
                ParentId = x.Id,               
                SubdivisionName = x.SubdivisionName,
                DragClue = x.SubdivisionName,
                HasChildren = false,
                IsDummyForSearch = true
            }).OrderBy(x => x.PlanNumber).ToList();
        SubdivisionPlanOptionTreeData.AddRange(addDummyPlanData);//Hack to make the expandable rows stay expandable when filtering, so it appears there are children

        WorksheetPlanData = new List<WorksheetPlanModel>();
        SelectedWorksheetData = new ObservableCollection<WorksheetTreeModel>();
        //ShowError = getGroups.IsSuccess;
        //ErrorMessage = getGroups.Message;
        //OptionGroupOptions = getGroups.Value;

        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;

        IsLoadingWorksheets = false;
        IsLoadingPlanData = false;
    }
    private async Task BreakLock()
    {
        worksheetLockedBy = await SalesPriceService.BreakWorksheetLockAsync((int)SelectedWorksheet.WorksheetId);
        worksheetEditable = true;
    }
    async Task OnSubdivTreeStateInitHandler(TreeListStateEventArgs<WorksheetTreeModel> args)
    {
        var collapsedItemsState = new TreeListState<WorksheetTreeModel>()
        {
            //collapse all items in the TreeList upon initialization of the state
                ExpandedItems = new List<WorksheetTreeModel>(),                
        };        
        args.TreeListState = collapsedItemsState;
    }
    async Task OnWorksheetTreeStateInitHandler(TreeListStateEventArgs<WorksheetTreeModel> args)
    {
        var collapsedItemsState = new TreeListState<WorksheetTreeModel>()
            {
                //collapse all items in the TreeList upon initialization of the state
                ExpandedItems = new List<WorksheetTreeModel>(),
                //filter isactive only
                FilterDescriptors = new List<IFilterDescriptor>()
                    {
                        new CompositeFilterDescriptor(){
                            FilterDescriptors = new FilterDescriptorCollection()
                            {
                                new FilterDescriptor()
                                {
                                    Member = nameof(WorksheetTreeModel.IsActive),
                                    MemberType = typeof(bool),
                                    Operator = FilterOperator.IsEqualTo,
                                    Value = true
                                },
                            }
                        },

                    }
            };
        args.TreeListState = collapsedItemsState;
    }
    void OnRowClickHandler(TreeListRowClickEventArgs args)
    {
        var keypress = args.EventArgs as MouseEventArgs;
        CurrentSelected = args.Item as WorksheetTreeModel;
        if(keypress != null)
        {
            if(keypress.ShiftKey == true)
            {
                //add range to selected
                //TODO: what if this range includes rows not currently showing because they are collapsed?? they could be removed on collapse, or mark them as ishwoing = false?
                var indexPrevSelected = SelectedWorksheetData.SelectRecursive(x => x.Children).IndexOf(PreviousSelected);
                var indexCurrSelected = SelectedWorksheetData.SelectRecursive(x => x.Children).IndexOf(CurrentSelected);
                var beginRange = indexCurrSelected <= indexPrevSelected ? indexCurrSelected : indexPrevSelected;
                var endRange = indexCurrSelected <= indexPrevSelected ? indexPrevSelected : indexCurrSelected;
                var list1 = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => SelectedWorksheetData.SelectRecursive(x => x.Children).IndexOf(x) > beginRange && SelectedWorksheetData.SelectRecursive(x => x.Children).IndexOf(x) < endRange).ToList();
                foreach(var addItem in list1)
                {
                    AddToSelectedCollection(addItem);
                }
                AddToSelectedCollection(CurrentSelected);
            }
            else if (keypress.CtrlKey == true)
            {
                //now manually add multiple to the click handler - actually this doesn't matter since it's added anyway to the whole collection
                var test = keypress.ShiftKey;
            }
            else
            {
                //TODO: it gets messed up if use shift with previous selection. Should probably clear the whole selection first
                AddToSelectedCollection(CurrentSelected);
            }
        }
        else
        {
            AddToSelectedCollection(CurrentSelected);
        }
        PreviousSelected = CurrentSelected;
    }
    public void AddToSelectedCollection(WorksheetTreeModel item)
    {
        //Add the information to the SelectedItems collection
        var index = SelectedWorksheetData.SelectRecursive(x => x.Children).IndexOf(item);
        var currentSelectedItems = new List<WorksheetTreeModel>(SelectedItems);
        var findItemIndex = currentSelectedItems.FindIndex(x => x.Id == item.Id);
        if (findItemIndex == -1)
        {
            if (item.Children != null)
            {
                currentSelectedItems.AddRange(item.Children.SelectRecursive(x => x.Children));
            }
            currentSelectedItems.Add(item);
            SelectedItems = currentSelectedItems;
        }
        else
        {
            currentSelectedItems.RemoveAt(findItemIndex);
            if (item.Children != null)
            {
                var itemsToBeRemoved = item.Children.SelectRecursive(x => x.Children).Select(x => x.Id).ToList();
                currentSelectedItems.RemoveAll(x => itemsToBeRemoved.Contains(x.Id));
            }
            SelectedItems = currentSelectedItems;
        }
    }
    public class MarkupTypeSelect
    {
        public string? Text { get; set; }
        public int Value { get; set; }
    }
    private async void HandleAddPlanOptionToWorksheetSubmit(ResponseModel<List<WorksheetOptModel>> addPlanOptionsResponse)
    {
        //TODO: this has to change for locked worksheet
        worksheetLockedBy = await SalesPriceService.GetWorksheetLockAsync((int)SelectedWorksheet.WorksheetId);//This will give the current user the lock unless another user already has the lock

        //TODO: if worksheet is locked by another user, prompt for continue in read-only or break user's lock
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        worksheetEditable = worksheetLockedBy == userName;
        if (!worksheetEditable)
        {
            await Dialogs.AlertAsync($"Sorry, worksheet is locked by {worksheetLockedBy}. Cannot add plans/options"); //TODO: Allow prompt to break other user's lock?
                                                                                                                      //TODO: add plans in a non-save way
        }
        else
        {
            if (addPlanOptionsResponse.IsSuccess)
            {
                //refresh the worksheet to get new data
                //TODO: avoid the refresh, just add to the collection at the right place
                loadingWorksheetStyle = "";
                IsLoadingOption = true;
                SelectedWorksheetData = new ObservableCollection<WorksheetTreeModel>((await SalesPriceService.GetAllWorksheetDataAsync((int)SelectedWorksheet.WorksheetId)).Value);


                // SelectedWorksheetData = new ObservableCollection<WorksheetTreeModel>();
                // WorksheetPlanData = null;
                // WorksheetPlanData = await SalesPriceService.GetWorksheetPlansAsync((int)SelectedWorksheet.WorksheetId);
                // SelectedWorksheetData = new ObservableCollection<WorksheetTreeModel>(WorksheetPlanData.Select(x => new WorksheetTreeModel()
                //     {
                //         Id = Guid.NewGuid(),
                //         SubdivisionName = x.SubdivisionName,
                //         PriceDate = x.Pricedate,
                //         PlanId = x.WorksheetPlanId,
                //         PlanName = x.PlanName,
                //         PlanNumber = x.PlanNumber,
                //         HasChildren = true,
                //         SellPrice = (decimal?)x.Sellprice,
                //         Cost = (decimal?)x.Costprice,
                //         IsActive = x.IsActive
                //     }));
                // SelectedWorksheetDataToSave = SelectedWorksheetData.ToList();
                loadingWorksheetStyle = "display:none";
                IsLoadingOption = false;
                SelectedItems = Enumerable.Empty<WorksheetTreeModel>();//set all items unselected
            }
            ShowSuccessOrErrorNotification(addPlanOptionsResponse.Message, addPlanOptionsResponse.IsSuccess);
            AddPlanOptionToWorksheetModal.Hide();
            StateHasChanged();
        }
    }
    private async void HandleValidCopyWorksheetSubmit(ResponseModel<WorksheetDto> responseWorksheet)
    {
        if (responseWorksheet.IsSuccess)
        {
            showWorksheet = true;
            //refresh the worksheets list, set the selected one to the new worksheet       
            ListAvailableWorksheets = await SalesPriceService.GetWorksheetsAsync();
            SelectedWorksheet = ListAvailableWorksheets.Where(x => x.WorksheetId == responseWorksheet.Value.WorksheetId).FirstOrDefault();
            worksheetLockedBy = await SalesPriceService.GetWorksheetLockAsync((int)SelectedWorksheet.WorksheetId);
            var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userName = user.User.Identity.Name.Split('@')[0];
            worksheetEditable = worksheetLockedBy == userName;
            if (!worksheetEditable)
            {
                bool isConfirmed = await Dialogs.ConfirmAsync("Worksheet is locked. Break Lock (click OK) or continue in Read-only(cancel)?");

                if (isConfirmed)
                {
                    Console.WriteLine("The user is sure, continue.");
                    worksheetLockedBy = await SalesPriceService.BreakWorksheetLockAsync((int)SelectedWorksheet.WorksheetId);
                }
                else
                {
                    Console.WriteLine("The user changed their mind");
                }
            }
            loadingWorksheetStyle = "";
            IsLoadingOption = true;
            // SelectedWorksheetData = new ObservableCollection<WorksheetTreeModel>();
            // WorksheetPlanData = null;
            // WorksheetPlanData = await SalesPriceService.GetWorksheetPlansAsync((int)SelectedWorksheet.WorksheetId);

            SelectedWorksheetData = new ObservableCollection<WorksheetTreeModel>((await SalesPriceService.GetAllWorksheetDataAsync((int)SelectedWorksheet.WorksheetId)).Value);



            // SelectedWorksheetData = new ObservableCollection<WorksheetTreeModel>(WorksheetPlanData.Select(x => new WorksheetTreeModel()
            //     {
            //         Id = Guid.NewGuid(),
            //         SubdivisionName = x.SubdivisionName,
            //         PriceDate = x.Pricedate,
            //         PlanId = x.WorksheetPlanId,
            //         PlanName = x.PlanName,
            //         PlanNumber = x.PlanNumber,
            //         HasChildren = true,
            //         SellPrice = (decimal?)x.Sellprice,
            //         Cost = (decimal?)x.Costprice,
            //         IsActive = x.IsActive
            //     }));
            //SelectedWorksheetDataToSave = SelectedWorksheetData.ToList();
            loadingWorksheetStyle = "display:none";
            IsLoadingOption = false;
            SelectedItems = Enumerable.Empty<WorksheetTreeModel>();//set all items unselected
        }
        ShowSuccessOrErrorNotification(responseWorksheet.Message, responseWorksheet.IsSuccess);
        CopyWorksheetModal.Hide();
        StateHasChanged();
    }
    private async void HandleValidAddWorksheetSubmit(ResponseModel<WorksheetDto> responseWorksheet)
    {
        if (responseWorksheet.IsSuccess)
        {
            ListAvailableWorksheets = await SalesPriceService.GetWorksheetsAsync();
            SelectedWorksheet = ListAvailableWorksheets.Where(x => x.WorksheetId == responseWorksheet.Value.WorksheetId).FirstOrDefault();
            worksheetLockedBy = await SalesPriceService.GetWorksheetLockAsync((int)SelectedWorksheet.WorksheetId);
            var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userName = user.User.Identity.Name.Split('@')[0];
            worksheetEditable = worksheetLockedBy == userName;
            if (!worksheetEditable)
            {
                bool isConfirmed = await Dialogs.ConfirmAsync("Worksheet is locked. Break Lock (click OK) or continue in Read-only(cancel)?");

                if (isConfirmed)
                {
                    Console.WriteLine("The user is sure, continue.");
                    worksheetLockedBy = await SalesPriceService.BreakWorksheetLockAsync((int)SelectedWorksheet.WorksheetId);
                }
                else
                {
                    Console.WriteLine("The user changed their mind");
                }
            }
            loadingWorksheetStyle = "";
            IsLoadingOption = true;
            showWorksheet = true;
            SelectedWorksheetData = new ObservableCollection<WorksheetTreeModel>((await SalesPriceService.GetAllWorksheetDataAsync((int)SelectedWorksheet.WorksheetId)).Value);
            // SelectedWorksheetData = new ObservableCollection<WorksheetTreeModel>();
            // WorksheetPlanData = null;
            // WorksheetPlanData = await SalesPriceService.GetWorksheetPlansAsync((int)SelectedWorksheet.WorksheetId);
            // SelectedWorksheetData = new ObservableCollection<WorksheetTreeModel>(WorksheetPlanData.Select(x => new WorksheetTreeModel()
            //     {
            //         Id = Guid.NewGuid(),
            //         SubdivisionName = x.SubdivisionName,
            //         PriceDate = x.Pricedate,
            //         PlanId = x.WorksheetPlanId,
            //         PlanName = x.PlanName,
            //         PlanNumber = x.PlanNumber,
            //         HasChildren = true,
            //         SellPrice = (decimal?)x.Sellprice,
            //         Cost = (decimal?)x.Costprice,
            //         IsActive = x.IsActive
            //     }));
            // SelectedWorksheetDataToSave = SelectedWorksheetData.ToList();
            loadingWorksheetStyle = "display:none";
            IsLoadingOption = false;
            SelectedItems = Enumerable.Empty<WorksheetTreeModel>();//set all items unselected
        }
        ShowSuccessOrErrorNotification(responseWorksheet.Message, responseWorksheet.IsSuccess);

        AddWorksheetModal.Hide();
        StateHasChanged();
    }
    private async void HandleValidRepriceWorksheetSubmit(ResponseModel<RepriceWorksheetModel> responseWorksheet)
    {
        if (responseWorksheet.IsSuccess)
        {
            //reloads the worksheet
            await ReloadWorksheet((int)SelectedWorksheet.WorksheetId);
        }
        ShowSuccessOrErrorNotification(responseWorksheet.Message, responseWorksheet.IsSuccess);
        RepriceWorksheetModal.Hide();
        StateHasChanged();
    }
    private async void HandleValidRepriceOptionSubmit(ResponseModel<RepriceWorksheetOptsModel> responseModel)
    {

        if(responseModel.IsSuccess)
        {
            //reloads the worksheet
            await ReloadWorksheet((int)SelectedWorksheet.WorksheetId);
            RepriceWorksheetOptModal.Hide();
            StateHasChanged();
        }
        ShowSuccessOrErrorNotification(responseModel.Message, responseModel.IsSuccess);
    }
    private void AddWorksheet()
    {
        AddWorksheetModal.Show();
        StateHasChanged();
    }
    private void FillDown()
    {
        UnsavedChanges = true;
        //find the top most item to use to fill down
        var getSelectedRows = SelectedItems;//TODO: problem is items not being removed from selected rows collection
        var selecteArray = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => SelectedItems.Select(y => y.Id).Contains(x.Id)).ToArray();
        var topItem = selecteArray[0];//Test whether this is correct, is it always this one. Or why not keep list and use first();, 

        //determine if what level of tree is the selected top item to determine what exactly can fill down and to where
        if(topItem.WorksheetActivityId != null && LastColumnEdited == "Cost")//activity level only cost is editable
        {
            //TODO: refactor
            //take that value and apply it to the others
            foreach (var item in getSelectedRows.Where(x => x.WorksheetActivityId != null))//only update other activities if the top row in the fill down is activity
            {
                //if it's activity, the only thing that could have changed is cost
                item.Cost = topItem.Cost;

                //recalculate sums
                var findItem = SelectedWorksheetData.SelectRecursive(x => x.Children).FirstOrDefault(x => x.Id == item.Id);
                //var findSaveItem = SelectedWorksheetDataToSave.Where(x => x.Id == item.Id).First();
                findItem.Cost = topItem.Cost;
                findItem.LumpSum = true;
                // findSaveItem.Cost = topItem.Cost;
                // findSaveItem.LumpSum = true;
                var findParent = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.Id == item.ParentId).First();
                // var findSaveParent = SelectedWorksheetDataToSave.Where(x => x.Id == item.ParentId).First();
                //update the cost of the parent, also update the pricing of the parent depending on markuptype
                var activitiesThisOption = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.ParentId == item.ParentId);
                var sumActivityCost = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.ParentId == item.ParentId).Sum(x => x.Cost ?? 0);
                findParent.Cost = sumActivityCost;
                // findSaveParent.Cost = sumActivityCost;
                //update the percent totals
                foreach (var activity in activitiesThisOption)
                {
                    activity.PercentOfTotal = activity.Cost != null && sumActivityCost != 0 ? 100 * (double)activity.Cost / (double)sumActivityCost : 0;
                }

                //now update the pricing of the parent option
                if (findParent.MarkupType == 0)
                {
                    //markup percent
                    findParent.SellPrice = RoundUpToTen(findParent.Cost + (findParent.Cost * (decimal?)findParent.MarkupPercent / 100));
                    findParent.Margin = (double?)findParent.Cost * findParent.MarkupPercent / 100;
                    findParent.MarginPercent = findParent.SellPrice != null && findParent.SellPrice != 0 ? 100 * findParent.Margin / (double?)findParent.SellPrice : 0;
                    findParent.MarketValue = findParent.SellPrice;
                    findParent.Markup = (decimal?)findParent.Margin;
                    // findSaveParent.SellPrice = findParent.SellPrice;
                    // findSaveParent.Margin = findParent.Margin;
                    // findSaveParent.MarginPercent = findParent.MarginPercent;
                    // findSaveParent.MarketValue = findParent.MarketValue;
                    // findSaveParent.Markup = findParent.Markup;

                }
                if (findParent.MarkupType == 1)
                {
                    //market value
                    findParent.SellPrice = findParent.MarketValue;
                    findParent.Margin = (double?)findParent.SellPrice - (double?)findParent.Cost;
                    findParent.MarkupPercent = findParent.Cost != null && findParent.Cost != 0 ? 100 * findParent.Margin / (double?)findParent.Cost : 0;
                    findParent.MarginPercent = findParent.SellPrice != null && findParent.SellPrice != 0 ? 100 * findParent.Margin / (double?)findParent.SellPrice : 0;
                    findParent.Markup = (decimal?)findParent.Margin;
                    // findSaveParent.SellPrice = findParent.SellPrice;
                    // findSaveParent.Margin = findParent.Margin;
                    // findSaveParent.MarginPercent = findParent.MarginPercent;
                    // findSaveParent.MarketValue = findParent.MarketValue;
                    // findSaveParent.Markup = findParent.Markup;
                }
                if (findParent.MarkupType == 2)
                {
                    //margin percent
                    findParent.SellPrice = RoundUpToTen(findParent.MarginPercent != null && findParent.MarginPercent != 0 ? findParent.Cost / (1 - ((decimal?)findParent.MarginPercent) / 100) : 0);
                    findParent.Margin = (double?)findParent.SellPrice * findParent.MarginPercent;
                    findParent.MarkupPercent = findParent.Cost != null && findParent.Cost != 0 ? 100 * findParent.Margin / (double?)findParent.Cost : 0;
                    findParent.MarketValue = findParent.SellPrice;
                    findParent.Markup = (decimal?)findParent.Margin;
                    // findSaveParent.SellPrice = findParent.SellPrice;
                    // findSaveParent.Margin = findParent.Margin;
                    // findSaveParent.MarginPercent = findParent.MarginPercent;
                    // findSaveParent.MarketValue = findParent.MarketValue;
                    // findSaveParent.Markup = findParent.Markup;
                }

                var updateItem = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.Id == item.Id).SingleOrDefault();
                updateItem.SellPrice = item.SellPrice;
                updateItem.Cost = item.Cost;
                updateItem.MarkupType = item.MarkupType;
                updateItem.Margin = item.Margin;
                updateItem.MarginPercent = item.MarginPercent;
                updateItem.MarkupPercent = item.MarkupPercent;
                updateItem.MarketValue = item.MarketValue;
                updateItem.Markup = item.Markup;
                // var updateItemToSave = SelectedWorksheetDataToSave.Where(x => x.Id == item.Id).SingleOrDefault();
                // updateItemToSave.SellPrice = item.SellPrice;
                // updateItemToSave.Cost = item.Cost;
                // updateItemToSave.MarkupType = item.MarkupType;
                // updateItemToSave.Margin = item.Margin;
                // updateItemToSave.MarginPercent = item.MarginPercent;
                // updateItemToSave.MarkupPercent = item.MarkupPercent;
                // updateItemToSave.MarketValue = item.MarketValue;
                // updateItemToSave.Markup = item.Markup;
            }
        }

        else if(topItem.WorksheetOptionId != null)
        {
            //TODO: plan acts are different
            //in wms, if they just changed the markup type, fill that down. if they didn't just change the markup type, fill down only applies to same markup type
            //so need to track which field being updated
            if(LastColumnEdited == "MarkupType")
            {
                foreach (var item in getSelectedRows.Where(x => x.WorksheetOptionId != null && x.WorksheetActivityId == null))
                {
                    item.MarkupType = topItem.MarkupType;
                }
            }
            else if(LastColumnEdited == "MarketValue")
            {
                foreach (var item in getSelectedRows.Where(x => x.WorksheetOptionId != null && x.WorksheetActivityId == null && x.MarkupType == 1))
                {
                    //only update the selected options that have that same markup type as top one
                    //market value
                    item.MarketValue = topItem.MarketValue;
                    item.SellPrice = item.MarketValue;
                    item.Margin = (double?)item.SellPrice - (double?)item.Cost;
                    item.MarkupPercent = item.Cost != null && item.Cost != 0 ? 100 * item.Margin / (double?)item.Cost : 0;
                    item.MarginPercent = item.SellPrice != null && item.SellPrice != 0 ? 100 * item.Margin / (double?)item.SellPrice : 0;
                    item.Markup = (decimal?)item.Margin;
                    var updateItem = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.Id == item.Id).SingleOrDefault();
                    updateItem.SellPrice = item.SellPrice;
                    updateItem.Cost = item.Cost;
                    updateItem.MarkupType = item.MarkupType;
                    updateItem.Margin = item.Margin;
                    updateItem.MarginPercent = item.MarginPercent;
                    updateItem.MarkupPercent = item.MarkupPercent;
                    updateItem.MarketValue = item.MarketValue;
                    updateItem.Markup = item.Markup;
                    // var updateItemToSave = SelectedWorksheetDataToSave.Where(x => x.Id == item.Id).SingleOrDefault();
                    // updateItemToSave.SellPrice = item.SellPrice;
                    // updateItemToSave.Cost = item.Cost;
                    // updateItemToSave.MarkupType = item.MarkupType;
                    // updateItemToSave.Margin = item.Margin;
                    // updateItemToSave.MarginPercent = item.MarginPercent;
                    // updateItemToSave.MarkupPercent = item.MarkupPercent;
                    // updateItemToSave.MarketValue = item.MarketValue;
                    // updateItemToSave.Markup = item.Markup;
                }

            }
            else if(LastColumnEdited == "MarkupPercent")
            {
                foreach (var item in getSelectedRows.Where(x => x.WorksheetOptionId != null && x.WorksheetActivityId == null && x.MarkupType == 0))
                {
                    //markup percent
                    // item.SellPrice = RoundUpToTen(item.SellPrice);//This should already be rounded to 10
                    // var findItem = SelectedWorksheetData.SingleOrDefault(x => x.Id == item.Id);
                    item.MarkupPercent = topItem.MarkupPercent;
                    item.SellPrice = RoundUpToTen(item.Cost + (item.Cost * (decimal?)item.MarkupPercent / 100));
                    item.Margin = (double?)item.SellPrice - (double?)item.Cost;
                    item.MarginPercent = item.SellPrice != null && item.SellPrice != 0 ? 100 * (double?)(item.SellPrice - item.Cost) / (double?)item.SellPrice : 0;
                    item.MarketValue = item.SellPrice;
                    item.Markup = (decimal?)item.Margin;
                    var updateItem = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.Id == item.Id).SingleOrDefault();
                    updateItem.SellPrice = item.SellPrice;
                    updateItem.Cost = item.Cost;
                    updateItem.MarkupType = item.MarkupType;
                    updateItem.Margin = item.Margin;
                    updateItem.MarginPercent = item.MarginPercent;
                    updateItem.MarkupPercent = item.MarkupPercent;
                    updateItem.MarketValue = item.MarketValue;
                    updateItem.Markup = item.Markup;
                    // var updateItemToSave = SelectedWorksheetDataToSave.Where(x => x.Id == item.Id).SingleOrDefault();
                    // updateItemToSave.SellPrice = item.SellPrice;
                    // updateItemToSave.Cost = item.Cost;
                    // updateItemToSave.MarkupType = item.MarkupType;
                    // updateItemToSave.Margin = item.Margin;
                    // updateItemToSave.MarginPercent = item.MarginPercent;
                    // updateItemToSave.MarkupPercent = item.MarkupPercent;
                    // updateItemToSave.MarketValue = item.MarketValue;
                    // updateItemToSave.Markup = item.Markup;
                }
            }
            else if(LastColumnEdited == "MarginPercent")
            {
                //margin percent
                foreach (var item in getSelectedRows.Where(x => x.WorksheetOptionId != null && x.WorksheetActivityId == null && x.MarkupType == 2))
                {
                    item.MarginPercent = topItem.MarginPercent;
                    item.SellPrice = RoundUpToTen(item.Cost / (1 - ((decimal?)item.MarginPercent) / 100));
                    item.Margin = (double?)item.SellPrice - (double?)item.Cost;
                    item.MarkupPercent = item.SellPrice != null && item.SellPrice != 0 ? 100 * (double?)(item.SellPrice - item.Cost) / (double?)item.Cost : 0;
                    item.MarketValue = item.SellPrice;
                    item.Markup = (decimal?)item.Margin;
                    var updateItem = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.Id == item.Id).SingleOrDefault();
                    updateItem.SellPrice = item.SellPrice;
                    updateItem.Cost = item.Cost;
                    updateItem.MarkupType = item.MarkupType;
                    updateItem.Margin = item.Margin;
                    updateItem.MarginPercent = item.MarginPercent;
                    updateItem.MarkupPercent = item.MarkupPercent;
                    updateItem.MarketValue = item.MarketValue;
                    updateItem.Markup = item.Markup;
                    // var updateItemToSave = SelectedWorksheetDataToSave.Where(x => x.Id == item.Id).SingleOrDefault();
                    // updateItemToSave.SellPrice = item.SellPrice;
                    // updateItemToSave.Cost = item.Cost;
                    // updateItemToSave.MarkupType = item.MarkupType;
                    // updateItemToSave.Margin = item.Margin;
                    // updateItemToSave.MarginPercent = item.MarginPercent;
                    // updateItemToSave.MarkupPercent = item.MarkupPercent;
                    // updateItemToSave.MarketValue = item.MarketValue;
                    // updateItemToSave.Markup = item.Markup;
                }
            }

        }
    }
    async Task OnExpandSubdivTree(TreeListExpandEventArgs args)
    {

        var item = args.Item as WorksheetTreeModel;
        if (item.HasChildren && item.ParentId == null)
        {
            if (!SubdivisionPlanOptionTreeData.Any(x => x.ParentId == item.Id && x.IsDummyForSearch != true))
            {
                var findDummyForSearch = SubdivisionPlanOptionTreeData.Where(x => x.ParentId == item.Id && x.IsDummyForSearch == true).ToList();
                foreach (var dummy in findDummyForSearch)
                {
                    SubdivisionPlanOptionTreeData.Remove(dummy);
                };
                //get plans in subdivision
                var data = await PlanService.GetPhasePlansAsync((int)item.SubdivisionId);
                if (data.Value.Count == 0) // not null as returning empty list in case of failure
                {
                    item.HasChildren = false;
                }
                else
                {
                    var addPlanData = data.Value.Select(x => new WorksheetTreeModel() // not null as returning empty list in case of failure
                        {
                            Id = Guid.NewGuid(),
                            ParentId = item.Id,
                            PlanId = x.PhasePlanId,
                            PlanName = x.PlanName,
                            PlanNumber = x.PlanNumber,
                            SubdivisionName = item.SubdivisionName,
                            DragClue = x.PlanName,
                            HasChildren = true
                        }).OrderBy(x => x.PlanNumber).ToList();
                    SubdivisionPlanOptionTreeData.AddRange(addPlanData);
                    var addDummyOptionPlanData = addPlanData.Select(x => new WorksheetTreeModel()
                        {
                            Id = Guid.NewGuid(),
                            ParentId = x.Id,
                            PlanId = x.PhasePlanId,
                            PlanName = x.PlanName,
                            PlanNumber = x.PlanNumber,
                            SubdivisionName = item.SubdivisionName,
                            DragClue = x.PlanName,
                            HasChildren = false,
                            IsDummyForSearch = true
                        }).OrderBy(x => x.PlanNumber).ToList();
                    SubdivisionPlanOptionTreeData.AddRange(addDummyOptionPlanData);
                }
            }
        }
        else if (item.HasChildren && item.SubdivisionId == null && item.PlanId != null)
        {
            //get options in plan if not already added
            if (!SubdivisionPlanOptionTreeData.Any(x => x.ParentId == item.Id && x.IsDummyForSearch != true))
            {
                var findDummyForSearch = SubdivisionPlanOptionTreeData.Where(x => x.ParentId == item.Id && x.IsDummyForSearch == true).ToList();
                foreach(var dummy in findDummyForSearch)
                {
                    SubdivisionPlanOptionTreeData.Remove(dummy);
                };
                var data = (await SalesPriceService.GetAvailablePlanOptionsByPlanAsync((int)item.PlanId)).Value;
                if(data.Count == 0)
                {
                    item.HasChildren = false;
                }
                else
                {
                    var options = data.Select(x => new WorksheetTreeModel()
                        {
                            Id = Guid.NewGuid(),
                            ParentId = item.Id,
                            WorksheetOptionId = x.PlanOptionId,
                            OptionName = x.ModifiedOptionDesc,
                            OptionCode = x.OptionCode,
                            SubdivisionName = item.SubdivisionName,
                            PlanName = item.PlanName,
                            PlanNumber = item.PlanNumber,
                            DragClue = x.OptionCode,
                            HasChildren = false,
                        }).OrderBy(x => x.OptionCode);
                    SubdivisionPlanOptionTreeData.AddRange(options);
                }                
            }            
        }
    }
    // async Task OnExpand(TreeListExpandEventArgs args)
    // {
    //     var item = args.Item as WorksheetTreeModel;
    //     if (item.HasChildren && item.ParentId == null)
    //     {
    //         //get options in plan
    //         if (!SelectedWorksheetData.Any(x => x.ParentId == item.Id && x.IsDummyForSearch != true))
    //         {
    //             var findDummyForSearch = SelectedWorksheetData.Where(x => x.ParentId == item.Id && x.IsDummyForSearch == true).ToList();
    //             foreach (var dummy in findDummyForSearch)
    //             {
    //                 SelectedWorksheetData.Remove(dummy);
    //             };
    //             var optionsResponse = await SalesPriceService.GetWorksheetOptionsAsync((int)item.PlanId);
    //             if (optionsResponse.IsSuccess = false)
    //             {
    //                 ShowSuccessOrErrorNotification(optionsResponse.Message, true);
    //             }
    //             if (optionsResponse.Value.Count == 0)
    //             {
    //                 item.HasChildren = false;
    //             }
    //             else
    //             {
    //                 var itemsToAdd = optionsResponse.Value.Select(x => new WorksheetTreeModel()
    //                     {
    //                         Id = Guid.NewGuid(),
    //                         ParentId = item.Id,
    //                         PlanId = x.WorksheetPlanId,//should be null unless it's the base house
    //                         WorksheetPlanId = x.WorksheetPlanId,
    //                         IsBaseHouse = x.IsBaseHouse,
    //                         WorksheetOptionId = x.WorksheetOptId,
    //                         AvailablePlanOptionId = x.PlanOptionId,
    //                         PlanName = item.PlanName,
    //                         PlanNumber = item.PlanNumber,
    //                         SubdivisionName = item.SubdivisionName,
    //                         OptionCode = x.OptionCode,
    //                         OptionName = x.OptionName,
    //                         PriceDate = x.Pricedate,
    //                         HasChildren = true,
    //                         ErrorCode = x.Errors,
    //                         ErrorCount = x.ErrorCount,
    //                         ErrorReason = x.ErrorReason,
    //                         WarningCode = x.Warnings,
    //                         SellPrice = (decimal?)x.Sellprice,
    //                         Cost = (decimal?)x.Costprice,
    //                         MarkupPercent = x.Markuppercent,
    //                         Markup = (decimal?)x.Markup,
    //                         Margin = (x.Sellprice - x.Costprice),
    //                         MarginPercent = x.Sellprice != null && x.Sellprice != 0 ? 100 * (x.Sellprice - x.Costprice) / x.Sellprice : 0,
    //                         MarkupType = x.Markuptype,
    //                         MarketValue = (decimal?)x.Marketvalue,
    //                         IsActive = x.IsActive,
    //                     }).ToArray();

    //                 //var arrayData = SelectedWorksheetData.ToList();
    //                 //arrayData.InsertRange(arrayData.IndexOf(item) + 1, itemsToAdd);
    //                 // SelectedWorksheetData = new ObservableCollection<CombinedTreeModel>(arrayData);
    //                 int i = 1;
    //                 foreach (var addItem in itemsToAdd)
    //                 {
    //                     //insert the items so that selected worksheet data remains in order of row display
    //                     SelectedWorksheetData.Insert(SelectedWorksheetData.IndexOf(item) + i, addItem);//TODO: Too slow???
    //                     i++;
    //                 }
    //                 // SelectedWorksheetData.AddRange(itemsToAdd);
    //                 SelectedWorksheetDataToSave = SelectedWorksheetData.ToList();
    //                 var addDummyData = itemsToAdd.Select(x => new WorksheetTreeModel()
    //                     {
    //                         Id = Guid.NewGuid(),
    //                         ParentId = x.Id,
    //                         PlanId = x.PhasePlanId,
    //                         PlanName = item.PlanName,
    //                         PlanNumber = item.PlanNumber,
    //                         SubdivisionName = item.SubdivisionName,
    //                         OptionCode = x.OptionCode,
    //                         OptionName = x.OptionName,
    //                         DragClue = x.PlanName,
    //                         HasChildren = false,
    //                         IsDummyForSearch = true
    //                     }).ToList();
    //                 SelectedWorksheetData.AddRange(addDummyData);
    //             }
    //         }
    //         else
    //         {
    //             //data already in the selected worksheet data, but not showing
    //         }
    //     }
    //     else if (item.HasChildren)
    //     {
    //         //get activities in option - these should have cost, not price
    //         if (!SelectedWorksheetData.Any(x => x.ParentId == item.Id && x.IsDummyForSearch != true))
    //         {
    //             var findDummyForSearch = SelectedWorksheetData.Where(x => x.ParentId == item.Id && x.IsDummyForSearch == true).ToList();
    //             foreach (var dummy in findDummyForSearch)
    //             {
    //                 SelectedWorksheetData.Remove(dummy);
    //             };
    //             if (item.IsBaseHouse == true)
    //             {
    //                 var parentPlanId = SelectedWorksheetData.SingleOrDefault(x => x.Id == item.ParentId).PlanId;
    //                 var itemsResponse = await SalesPriceService.GetWorksheetPlansActivitiesAsync((int)parentPlanId);
    //                 if (itemsResponse.IsSuccess == false)
    //                 {
    //                     ShowSuccessOrErrorNotification(itemsResponse.Message, true);
    //                 }
    //                 double total = itemsResponse.Value.Sum(x => x.Costprice ?? 0);
    //                 if (itemsResponse.Value.Count == 0)
    //                 {
    //                     item.HasChildren = false;
    //                 }
    //                 else
    //                 {
    //                     var itemsToAdd = itemsResponse.Value.Select(x => new WorksheetTreeModel()
    //                         {
    //                             Id = Guid.NewGuid(),
    //                             WorksheetActivityId = x.WorksheetOptActId,
    //                             WorksheetOptionId = 1,//TODO: fix - this is determining base house row
    //                             ParentId = item.Id,
    //                             SubdivisionName = item.SubdivisionName,
    //                             SubNumber = x.SubNumber,
    //                             PlanName = item.PlanName,
    //                             PlanNumber = item.PlanNumber,
    //                             OptionCode = item.OptionCode,
    //                             OptionName = item.OptionName,
    //                             ActivityName = x.Activity,
    //                             PriceDate = item.PriceDate,
    //                             Vendor = x.SupplierName,
    //                             HasChildren = false,
    //                             Cost = (decimal?)x.Costprice,
    //                             LumpSum = x.Lumpsum == "T",
    //                             ErrorCount = x.ErrorCount,
    //                             ErrorCode = x.ErrorCode,
    //                             ErrorReason = x.ErrorReason,
    //                             WarningCode = x.Warnings,
    //                             PercentOfTotal = x.Costprice != null && total != 0 ? 100 * x.Costprice / total : 0,
    //                             IsActive = x.IsActive
    //                         }).OrderBy(x => x.ActivityName).ToArray();

    //                     int i = 1;
    //                     foreach (var addItem in itemsToAdd)
    //                     {
    //                         //insert the items so that selected worksheet data remains in order of row display
    //                         SelectedWorksheetData.Insert(SelectedWorksheetData.IndexOf(item) + i, addItem);
    //                         i++;
    //                     }
    //                     SelectedWorksheetDataToSave = SelectedWorksheetData.ToList();
    //                 }
    //             }
    //             else
    //             {
    //                 var items = (await SalesPriceService.GetWorksheetOptionsActivitiesAsync((int)item.WorksheetOptionId)).Value;
    //                 double total = items.Sum(x => x.Costprice ?? 0);
    //                 if (items.Count == 0)
    //                 {
    //                     item.HasChildren = false;
    //                 }
    //                 else
    //                 {
    //                     var itemsToAdd = items.Select(x => new WorksheetTreeModel()
    //                         {
    //                             Id = Guid.NewGuid(),
    //                             WorksheetActivityId = x.WorksheetOptActId,
    //                             ParentId = item.Id,
    //                             ActivityName = x.Activity,
    //                             Vendor = x.SupplierName,
    //                             SubNumber = x.SubNumber,
    //                             SubdivisionName = item.SubdivisionName,
    //                             PlanName = item.PlanName,
    //                             PlanNumber = item.PlanNumber,
    //                             OptionCode = item.OptionCode,
    //                             OptionName = item.OptionName,
    //                             // PriceDate = item.PriceDate,
    //                             HasChildren = false,
    //                             Cost = (decimal?)x.Costprice,
    //                             LumpSum = x.Lumpsum == "T",
    //                             ErrorCode = x.ErrorCode,
    //                             ErrorCount = x.ErrorCount,
    //                             ErrorReason = x.ErrorReason,
    //                             WarningCode = x.Warnings,
    //                             PercentOfTotal = x.Costprice != null && total != 0 ? 100 * x.Costprice / total : 0,
    //                             IsActive = x.IsActive
    //                         }).OrderBy(x => x.ActivityName).ToArray();
    //                     int i = 1;
    //                     foreach (var addItem in itemsToAdd)
    //                     {
    //                         //insert the items so that selected worksheet data remains in order of row display
    //                         SelectedWorksheetData.Insert(SelectedWorksheetData.IndexOf(item) + i, addItem);
    //                         i++;
    //                     }
    //                     SelectedWorksheetDataToSave = SelectedWorksheetData.ToList();
    //                 }
    //             }
    //         }
    //         else
    //         {
    //             //items were already fetched from database
    //         }
    //     }
    // }
    async Task OnCollapse(TreeListCollapseEventArgs args)
    {

    }
    private void EditItem(TreeListCommandEventArgs args)
    {
        ItemBeingEdited = args.Item as WorksheetTreeModel;
        //TODO: can it cancel the update if it's not really being edited but just they have to click somewhere
        // LastColumnEdited = args.Field;
    }
    private async Task UpdateItem(TreeListCommandEventArgs args)
    {
        LastColumnEdited = args.Field;
        UnsavedChanges = true;
        //This just update the model and does all the calcuations, separate save method to send it all to the database
        var updatePriceItem = (WorksheetTreeModel)args.Item;
        if (ItemBeingEdited != updatePriceItem)
        {
            //something actually changed 
            if (updatePriceItem.WorksheetActivityId != null)//activity
            {
                //Updating both the item shown and the item to save (else when items deleted it can't keep track)

                //Activity level can edit cost, not price
                var findItem = SelectedWorksheetData.SelectRecursive(x=> x.Children).Where(x => x.Id == updatePriceItem.Id).First();
                // var findSaveItem = SelectedWorksheetDataToSave.Where(x => x.Id == updatePriceItem.Id).First();
                if (findItem.Cost != updatePriceItem.Cost)//Only make update if it actually changed
                {
                    findItem.Cost = updatePriceItem.Cost;
                    findItem.LumpSum = true;
                    findItem.WarningCount = 1;//Lump sum warning
                                              // findSaveItem.Cost = updatePriceItem.Cost;
                                              // findSaveItem.LumpSum = true;
                                              // findSaveItem.WarningCount = 1;
                    var findParent = SelectedWorksheetData.SelectMany(x => x.Children).FirstOrDefault(x => x.Id == updatePriceItem.ParentId);
                    // var findSaveParent = SelectedWorksheetDataToSave.Where(x => x.Id == updatePriceItem.ParentId).First();
                    //update the cost of the parent, also update the pricing of the parent depending on markuptype
                    var activitiesThisOption = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.ParentId == updatePriceItem.ParentId);
                    var sumActivityCost = activitiesThisOption.Sum(x => x.Cost ?? 0);
                    findParent.Cost = sumActivityCost;
                    //findSaveParent.Cost = sumActivityCost;
                    //update the percent totals
                    foreach (var activity in activitiesThisOption)
                    {
                        activity.PercentOfTotal = activity.Cost != null && sumActivityCost != 0 ? 100 * (double)activity.Cost / (double)sumActivityCost : 0;
                    }
                    //now update the pricing of the parent option
                    if (findParent.MarkupType == 0)
                    {
                        //markup percent
                        findParent.SellPrice = RoundUpToTen(findParent.Cost + (findParent.Cost * (decimal?)findParent.MarkupPercent / 100));//div by 10, ceiling, then multiply by ten to get rounding up
                        findParent.Margin = (double?)findParent.SellPrice - (double?)findParent.Cost;
                        findParent.MarginPercent = findParent.SellPrice != null && findParent.SellPrice != 0 ? 100 * findParent.Margin / (double?)findParent.SellPrice : 0;
                        findParent.MarketValue = findParent.SellPrice;
                        findParent.Markup = (decimal?)findParent.Margin;
                        // findSaveParent.SellPrice = findParent.SellPrice;
                        // findSaveParent.Margin = findParent.Margin;
                        // findSaveParent.MarginPercent = findParent.MarginPercent;
                        // findSaveParent.MarketValue = findParent.MarketValue;
                        // findSaveParent.Markup = findParent.Markup;

                    }
                    if (findParent.MarkupType == 1)
                    {
                        //market value
                        findParent.SellPrice = findParent.MarketValue;
                        findParent.Margin = (double?)findParent.SellPrice - (double?)findParent.Cost;
                        findParent.MarkupPercent = findParent.Cost != null && findParent.Cost != 0 ? 100 * findParent.Margin / (double?)findParent.Cost : 0;
                        findParent.MarginPercent = findParent.SellPrice != null && findParent.SellPrice != 0 ? 100 * findParent.Margin / (double?)findParent.SellPrice : 0;
                        findParent.Markup = (decimal?)findParent.Margin;
                        // findSaveParent.SellPrice = findParent.SellPrice;
                        // findSaveParent.Margin = findParent.Margin;
                        // findSaveParent.MarginPercent = findParent.MarginPercent;
                        // findSaveParent.MarketValue = findParent.MarketValue;
                        // findSaveParent.Markup = findParent.Markup;
                    }
                    if (findParent.MarkupType == 2)
                    {
                        //margin percent
                        findParent.SellPrice = RoundUpToTen(findParent.MarginPercent != null && findParent.MarginPercent != 0 ? (findParent.Cost) / (1-((decimal?)findParent.MarginPercent)/100) : 0);
                        findParent.Margin = (double?)findParent.SellPrice - (double?)findParent.Cost;
                        findParent.MarkupPercent = findParent.Cost != null && findParent.Cost != 0 ? 100 * findParent.Margin / (double?)findParent.Cost : 0;
                        findParent.MarketValue = findParent.SellPrice;
                        findParent.Markup = (decimal?)findParent.Margin;
                        // findSaveParent.SellPrice = findParent.SellPrice;
                        // findSaveParent.Margin = findParent.Margin;
                        // findSaveParent.MarginPercent = findParent.MarginPercent;
                        // findSaveParent.MarketValue = findParent.MarketValue;
                        // findSaveParent.Markup = findParent.Markup;
                    }
                }
            }
            else if (updatePriceItem.WorksheetOptionId != null)
            {
                //Option level can edit price
                var updateWorksheetOpt = new WorksheetOptModel()
                    {
                        WorksheetOptId = (int)updatePriceItem.WorksheetOptionId,
                        Costprice = (double?)updatePriceItem.SellPrice
                    };
                if (updatePriceItem.MarkupType == 0)
                {
                    //markup percent
                    updatePriceItem.SellPrice = RoundUpToTen(updatePriceItem.Cost + (updatePriceItem.Cost * (decimal?)updatePriceItem.MarkupPercent / 100));
                    updatePriceItem.Margin = (double?)updatePriceItem.SellPrice - (double?)updatePriceItem.Cost;
                    updatePriceItem.MarginPercent = updatePriceItem.SellPrice != null && updatePriceItem.SellPrice != 0 ? 100 * updatePriceItem.Margin / (double?)updatePriceItem.SellPrice : 0;
                    updatePriceItem.MarketValue = updatePriceItem.SellPrice;
                    updatePriceItem.Markup = (decimal?)updatePriceItem.Margin;

                }
                if (updatePriceItem.MarkupType == 1)
                {
                    //market value
                    updatePriceItem.SellPrice = updatePriceItem.MarketValue;
                    updatePriceItem.Margin = (double?)updatePriceItem.SellPrice - (double?)updatePriceItem.Cost;
                    updatePriceItem.MarkupPercent = updatePriceItem.Cost != null && updatePriceItem.Cost != 0 ? 100 * updatePriceItem.Margin / (double?)updatePriceItem.Cost : 0;
                    updatePriceItem.MarginPercent = updatePriceItem.SellPrice != null && updatePriceItem.SellPrice != 0 ? 100 * updatePriceItem.Margin / (double?)updatePriceItem.SellPrice : 0;
                    updatePriceItem.Markup = (decimal?)updatePriceItem.Margin;
                }
                if (updatePriceItem.MarkupType == 2)
                {
                    //margin percent
                    updatePriceItem.SellPrice = RoundUpToTen((updatePriceItem.Cost) /(1 - (decimal?)updatePriceItem.MarginPercent/100));                   
                    updatePriceItem.Margin = (double?)updatePriceItem.SellPrice - (double?)updatePriceItem.Cost;
                    updatePriceItem.MarkupPercent = updatePriceItem.Cost != null && updatePriceItem.Cost != 0 ? 100 * updatePriceItem.Margin / (double?)updatePriceItem.Cost : 0;
                    updatePriceItem.MarketValue = updatePriceItem.SellPrice;
                    updatePriceItem.Markup = (decimal?)updatePriceItem.Margin;
                }

            }
        }

        var updateItem = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.Id == updatePriceItem.Id).SingleOrDefault();
        updateItem.SellPrice = updatePriceItem.SellPrice;
        updateItem.Cost = updatePriceItem.Cost;
        updateItem.MarkupType = updatePriceItem.MarkupType;
        updateItem.Margin = updatePriceItem.Margin;
        updateItem.MarginPercent = updatePriceItem.MarginPercent;
        updateItem.MarkupPercent = updatePriceItem.MarkupPercent;
        updateItem.MarketValue = updatePriceItem.MarketValue;
        updateItem.Markup = updatePriceItem.Markup;
        // var updateItemToSave = SelectedWorksheetDataToSave.Where(x => x.Id == updatePriceItem.Id).SingleOrDefault();
        // updateItemToSave.SellPrice = updatePriceItem.SellPrice;
        // updateItemToSave.Cost = updatePriceItem.Cost;
        // updateItemToSave.MarkupType = updatePriceItem.MarkupType;
        // updateItemToSave.Margin = updatePriceItem.Margin;
        // updateItemToSave.MarginPercent = updatePriceItem.MarginPercent;
        // updateItemToSave.MarkupPercent = updatePriceItem.MarkupPercent;
        // updateItemToSave.MarketValue = updatePriceItem.MarketValue;
        // updateItemToSave.Markup = updatePriceItem.Markup;
    }
    private decimal RoundUpToTen(decimal? inputNum)
    {
        return inputNum == null ? 0 :  (Math.Ceiling((decimal)inputNum / 10)) * 10; 
    }
    protected async Task OnWorksheetSelectedHandler(GridRowClickEventArgs args)
    {
        //check current value against input to prevent getting the same data twice -- onchange fires both on change input and on blur
        var worksheetSelected = args.Item as WorksheetDto;
        int selectedWorksheetId = worksheetSelected.WorksheetId;
        SelectedItems = Enumerable.Empty<WorksheetTreeModel>();

        loadingWorksheetStyle = "";
        showWorksheet = true;
        IsSelectedWorksheetLoading = true;
        StateHasChanged();
        SelectedWorksheet = worksheetSelected;
        SelectedWorksheet.WorksheetName = worksheetSelected.WorksheetName;
        worksheetLockedBy = await SalesPriceService.GetWorksheetLockAsync((int)SelectedWorksheet.WorksheetId);

        //if worksheet is locked by another user, prompt for continue in read-only or break user's lock
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        worksheetEditable = worksheetLockedBy == userName;
        if (!worksheetEditable)
        {
            bool isConfirmed = await Dialogs.ConfirmAsync("Worksheet is locked. Break Lock (click OK) or continue in Read-only(cancel)?");

            if (isConfirmed)
            {
                Console.WriteLine("The user is sure, continue.");
                worksheetLockedBy = await SalesPriceService.BreakWorksheetLockAsync((int)SelectedWorksheet.WorksheetId);
            }
            else
            {
                Console.WriteLine("The user changed their mind");
            }
        }
        SelectedWorksheetData = new ObservableCollection<WorksheetTreeModel>((await SalesPriceService.GetAllWorksheetDataAsync((int)SelectedWorksheet.WorksheetId)).Value);

        loadingWorksheetStyle = "display:none";
        IsSelectedWorksheetLoading = false;
        StateHasChanged();

    }
    protected async Task CloseWorksheet()
    {
        //break lock, prompt save changes
        //close worksheet go back to dashboard
        if (UnsavedChanges)
        {
            var proceed = await Dialogs.ConfirmAsync("You have unsaved changes, are you sure you want to close the worksheet?");
            if (!proceed)
            {
                return;
            }
        }
        await SalesPriceService.ClearWorksheetLockAsync(SelectedWorksheet.WorksheetId);//clear the lock on worksheet being closed
        showWorksheet = false;
        ListAvailableWorksheets = await SalesPriceService.GetWorksheetsAsync();
    }
    // protected async Task OnWorksheetSelectedHandlerOld(object theUserInput)
    // {
    //     //check current value against input to prevent getting the same data twice -- onchange fires both on change input and on blur
    //     int currValue = (int)theUserInput;
    //     SelectedItems = Enumerable.Empty<WorksheetTreeModel>();
    //     if (!currValue.Equals(lastOnChangeValue))
    //     {
    //         if (UnsavedChanges)
    //         {
    //             var proceed = await Dialogs.ConfirmAsync("You have unsaved changes, are you sure you want to switch worksheets?");
    //             if (!proceed)
    //             {
    //                 SelectedWorksheet.WorksheetId = lastOnChangeValue;
    //                 return;
    //             }
    //         }
    //         //if they left previous worksheet, clear their lock
    //         if(lastOnChangeValue != 0)
    //         {
    //             await SalesPriceService.ClearWorksheetLockAsync(lastOnChangeValue);//clear the lock on previous worksheet
    //         }

    //         //save the changed value first, so the check does not pass afterwards
    //         lastOnChangeValue = currValue;

    //         loadingWorksheetStyle = "";
    //         IsLoadingOption = true;
    //         WorksheetPlanData = null;
    //         SelectedWorksheet.WorksheetName = ListAvailableWorksheets.SingleOrDefault(x => x.WorksheetId == SelectedWorksheet.WorksheetId)?.WorksheetName;
    //         worksheetLockedBy = await SalesPriceService.GetWorksheetLockAsync((int)SelectedWorksheet.WorksheetId);

    //         //TODO: if worksheet is locked by another user, prompt for continue in read-only or break user's lock            
    //         var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
    //         var userName = user.User.Identity.Name.Split('@')[0];
    //         worksheetEditable = worksheetLockedBy == userName;
    //         if (!worksheetEditable)
    //         {
    //             bool isConfirmed = await Dialogs.ConfirmAsync("Worksheet is locked. Break Lock (click OK) or continue in Read-only(cancel)?");

    //             if (isConfirmed)
    //             {
    //                 Console.WriteLine("The user is sure, continue.");
    //                 worksheetLockedBy = await SalesPriceService.BreakWorksheetLockAsync((int)SelectedWorksheet.WorksheetId);
    //             }
    //             else
    //             {
    //                 Console.WriteLine("The user changed their mind");
    //             }
    //         }

    //         WorksheetPlanData = await SalesPriceService.GetWorksheetPlansAsync((int)SelectedWorksheet.WorksheetId);
    //         SelectedWorksheetData = new ObservableCollection
    // <WorksheetTreeModel>(WorksheetPlanData.Select(x => new WorksheetTreeModel()
    //             {
    //                 Id = Guid.NewGuid(),
    //                 SubdivisionName = x.SubdivisionName,
    //                 PlanId = x.WorksheetPlanId,
    //                 WorksheetPlanId = x.WorksheetPlanId,
    //                 PhasePlanId = x.PhasePlanId,
    //                 PlanName = x.PlanName,
    //                 PlanNumber = x.PlanNumber,
    //                 HasChildren = true,
    //                 SellPrice = (decimal?)x.Sellprice,
    //                 Cost = (decimal?)x.Costprice,
    //                 IsActive = x.IsActive,
    //                 DisplayRowIndex = 0
    //             }));
    //         SelectedWorksheetDataToSave = SelectedWorksheetData.ToList();
    //         var addDummyData = SelectedWorksheetData.Select(x => new WorksheetTreeModel()
    //             {
    //                 Id = Guid.NewGuid(),
    //                 ParentId = x.Id,
    //                 SubdivisionName = x.SubdivisionName,
    //                 PlanName = x.PlanName,
    //                 PlanNumber = x.PlanNumber,
    //                 DragClue = x.SubdivisionName,
    //                 HasChildren = false,
    //                 IsDummyForSearch = true
    //             }).OrderBy(x => x.PlanNumber).ToList();
    //         SelectedWorksheetData.AddRange(addDummyData);//Hack to make the expandable rows stay expandable when filtering, so it appears there are children
    //         loadingWorksheetStyle = "display:none";
    //         IsLoadingOption = false;
    //         StateHasChanged();
    //     }
    // }
    protected async Task ReloadWorksheet(int selectedWorksheetId)
    {
        var treeListExpandedItems = WorksheetTreeList?.GetState().ExpandedItems;
        SelectedItems = Enumerable.Empty<WorksheetTreeModel>();
        loadingWorksheetStyle = "";

        SelectedWorksheet.WorksheetName = ListAvailableWorksheets.SingleOrDefault(x => x.WorksheetId == SelectedWorksheet.WorksheetId)?.WorksheetName;
        worksheetLockedBy = await SalesPriceService.GetWorksheetLockAsync((int)SelectedWorksheet.WorksheetId);

        //TODO: if worksheet is locked by another user, prompt for continue in read-only or break user's lock
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        worksheetEditable = worksheetLockedBy == userName;

        SelectedWorksheetData = new ObservableCollection<WorksheetTreeModel>((await SalesPriceService.GetAllWorksheetDataAsync((int)SelectedWorksheet.WorksheetId)).Value);

        var currentTreeListState = WorksheetTreeList?.GetState();
        foreach (var expandedItem in treeListExpandedItems)
        {
            currentTreeListState.ExpandedItems.AddRange(SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.WorksheetPlanId == expandedItem.WorksheetPlanId && x.WorksheetOptionId == expandedItem.WorksheetOptionId).ToList());
        }
        await WorksheetTreeList?.SetStateAsync(currentTreeListState);

        loadingWorksheetStyle = "display:none";
        StateHasChanged();
    }
    void EditOptionHandler(GridCommandEventArgs args)
    {
        AvailablePlanOptionDto item = (AvailablePlanOptionDto)args.Item;
    }
    async Task DeleteWorksheetHandler(TreeListCommandEventArgs args)
    {
        worksheetLockedBy = await SalesPriceService.GetWorksheetLockAsync(SelectedWorksheet.WorksheetId);
        //if worksheet is locked by another user, prompt for continue in read-only or break user's lock
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        worksheetEditable = worksheetLockedBy == userName;
        if (worksheetEditable)
        {
            bool isConfirmed = await Dialogs.ConfirmAsync("Are you sure you want to delete this worksheet?");

            if (isConfirmed)
            {
                Console.WriteLine("The user is sure, continue.");
                var deleteWorksheet = new WorksheetDto() { WorksheetId = SelectedWorksheet.WorksheetId };
                var deleteWorksheetResponse = await SalesPriceService.DeleteWorksheetAsync(deleteWorksheet);
                if (deleteWorksheetResponse.IsSuccess)
                {
                    SelectedWorksheet = new WorksheetDto();
                    SelectedWorksheetData = null;
                    WorksheetPlanData = null;
                    showWorksheet = false;
                    ListAvailableWorksheets = await SalesPriceService.GetWorksheetsAsync();
                }
                ShowSuccessOrErrorNotification(deleteWorksheetResponse.Message, deleteWorksheetResponse.IsSuccess);
                StateHasChanged();
            }
            else
            {
                Console.WriteLine("The user changed their mind");
            }
        }
        else
        {
            await Dialogs.AlertAsync($"Cannot delete. Worksheet is locked by {worksheetLockedBy}");          
        }
    }
    async Task DeleteItemHandler(TreeListCommandEventArgs args)
    {

        bool isConfirmed = await Dialogs.ConfirmAsync("Are you sure you want to delete this item?");
        if (isConfirmed)
        {
            UnsavedChanges = true;
            var itemToDelete = (WorksheetTreeModel)args.Item;
            if (itemToDelete.WorksheetActivityId != null)//opt act (or plan act)
            {
                var findItem = SelectedWorksheetData.SelectRecursive(x => x.Children).SingleOrDefault(x => x.Id == itemToDelete.Id);
                // var findItem = SelectedWorksheetDataToSave.SingleOrDefault(x => x.Id == itemToDelete.Id);
                if(findItem != null)
                {
                    findItem.IsActive = false;
                }

                var findParent = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.Id == itemToDelete.ParentId).First();
                //var findSaveParent = SelectedWorksheetDataToSave.Where(x => x.Id == itemToDelete.ParentId).First();

                //TODO: instead of remove from data, use filtering to hide it

                // SelectedWorksheetData.Remove(itemToDelete);//remove from showing                                                          
                //update the cost of the parent, also update the pricing of the parent depending on markuptype
                var activitiesThisOption = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.ParentId == itemToDelete.ParentId && x.IsActive == true);

                if(activitiesThisOption.Count() == 0)
                {
                    //remove the option too now 
                    findParent.IsActive = false;
                    //findSaveParent.IsActive = false;
                    //find children, inactivate them                    
                    //SelectedWorksheetData.Remove(findParent);//TODO:if this was the only option, remove the plan?
                }
                var sumActivityCost = activitiesThisOption.Sum(x => x.Cost ?? 0);
                findParent.Cost = sumActivityCost;
                //findSaveParent.Cost = sumActivityCost;
                //update the percent totals
                foreach (var activity in activitiesThisOption)
                {
                    activity.PercentOfTotal = activity.Cost != null && sumActivityCost != 0 ? 100 * (double)activity.Cost / (double)sumActivityCost : 0;
                }

                //now update the pricing of the parent option
                if (findParent.MarkupType == 0)
                {
                    //markup percent
                    findParent.SellPrice = findParent.Cost + (findParent.Cost * (decimal?)findParent.MarkupPercent / 100);
                    findParent.Margin = (double?)findParent.Cost * findParent.MarkupPercent / 100;
                    findParent.MarginPercent = findParent.SellPrice != null && findParent.SellPrice != 0 ? 100 * findParent.Margin / (double?)findParent.SellPrice : 0;
                    findParent.MarketValue = findParent.SellPrice;
                    findParent.Markup = findParent.SellPrice - findParent.Cost;
                    // findSaveParent.SellPrice = findParent.SellPrice;
                    // findSaveParent.Margin = findParent.Margin;
                    // findSaveParent.MarginPercent = findParent.MarginPercent;
                    // findSaveParent.MarketValue = findParent.MarketValue;
                    // findSaveParent.Markup = findParent.Markup;
                }
                if (findParent.MarkupType == 1)
                {
                    //market value
                    findParent.SellPrice = findParent.MarketValue;
                    findParent.Margin = (double?)findParent.SellPrice - (double?)findParent.Cost;
                    findParent.MarkupPercent = findParent.Cost != null && findParent.Cost != 0 ? 100 * findParent.Margin / (double?)findParent.Cost : 0;
                    findParent.MarginPercent = findParent.SellPrice != null && findParent.SellPrice != 0 ? 100 * findParent.Margin / (double?)findParent.SellPrice : 0;
                    findParent.Markup = findParent.SellPrice - findParent.Cost;
                    // findSaveParent.SellPrice = findParent.SellPrice;
                    // findSaveParent.Margin = findParent.Margin;
                    // findSaveParent.MarginPercent = findParent.MarginPercent;
                    // findSaveParent.MarketValue = findParent.MarketValue;
                    // findSaveParent.Markup = findParent.Markup;
                }
                if (findParent.MarkupType == 2)
                {
                    //margin percent
                    findParent.SellPrice = findParent.MarginPercent != null && findParent.MarginPercent != 0 ? findParent.Cost / (1 - ((decimal?)findParent.MarginPercent) / 100) : 0;
                    findParent.Margin = (double?)findParent.SellPrice * findParent.MarginPercent;
                    findParent.MarkupPercent = findParent.Cost != null && findParent.Cost != 0 ? 100 * findParent.Margin / (double?)findParent.Cost : 0;
                    findParent.MarketValue = findParent.SellPrice;
                    findParent.Markup = findParent.SellPrice - findParent.Cost;
                    // findSaveParent.SellPrice = findParent.SellPrice;
                    // findSaveParent.Margin = findParent.Margin;
                    // findSaveParent.MarginPercent = findParent.MarginPercent;
                    // findSaveParent.MarketValue = findParent.MarketValue;
                    // findSaveParent.Markup = findParent.Markup;
                }          
                //  itemToDelete.IsActive = false;
                //TODO: recalculate total cost and pricing, and if it was the only act, delete the opt
                //to track separately for send to database
            }
            if (itemToDelete.WorksheetOptionId != null && itemToDelete.WorksheetActivityId == null)
            {
                var findItem = SelectedWorksheetData.SelectRecursive(x => x.Children).SingleOrDefault(x => x.Id == itemToDelete.Id);
                //var findItem = SelectedWorksheetDataToSave.SingleOrDefault(x => x.Id == itemToDelete.Id);
                findItem.IsActive = false;
                //find children, inactivate them
                var findChildren = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.ParentId == itemToDelete.Id && x.IsDummyForSearch != true);
                foreach(var child in findChildren)
                {
                    child.IsActive = false;
                    //var findChild = SelectedWorksheetDataToSave.SingleOrDefault(x => x.Id == child.Id);
                    // var findChild = SelectedWorksheetData.SingleOrDefault(x => x.Id == child.Id);
                    // findChild.IsActive = false;                    
                    //SelectedWorksheetData.Remove(child);
                    //  child.IsActive = false;
                }


                // SelectedWorksheetData.Remove(itemToDelete);


                // countRemovedRows+= findChildren.Where(x => x.IsShowing != false).Count() + 1;
                //TODO:if this was the only option, remove the plan?
                // itemToDelete.IsActive = false;
                //TODO: if the node wasn't already expanded, it won't find the children to inactivate them. 
            }
            if (itemToDelete.WorksheetPlanId != null && itemToDelete.WorksheetOptionId == null)
            {
                //itemToDelete.IsActive = false;
                //var findItem = SelectedWorksheetDataToSave.SingleOrDefault(x => x.Id == itemToDelete.Id);

                var findItem = SelectedWorksheetData.SingleOrDefault(x => x.Id == itemToDelete.Id);
                if(findItem != null)
                {
                    findItem.IsActive = false;
                }                
                var findChildren = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.ParentId == itemToDelete.Id && x.IsDummyForSearch != true);
                foreach (var child in findChildren)
                {
                    child.IsActive = false;
                    var findGrandChildren = SelectedWorksheetData.SelectRecursive(x => x.Children).Where(x => x.ParentId == child.Id && x.IsDummyForSearch != true);
                    foreach (var grandChild in findGrandChildren)
                    {
                        grandChild.IsActive = false;
                        //  SelectedWorksheetData.Remove(grandChild);
                        // var findGrandChild = SelectedWorksheetData.SingleOrDefault(x => x.Id == grandChild.Id);
                        // // var findGrandChild = SelectedWorksheetDataToSave.SingleOrDefault(x => x.Id == grandChild.Id);//TODO: these won't be in the collection if not expanded
                        // if(findGrandChild != null)
                        // {
                        //     findGrandChild.IsActive = false;
                        // }
                    }//TODO: loops are likely to be slow
                     // SelectedWorksheetData.Remove(child);
                    // var findChild = SelectedWorksheetData.SingleOrDefault(x => x.Id == child.Id);
                    // // var findChild = SelectedWorksheetDataToSave.SingleOrDefault(x => x.Id == child.Id);
                    // if(findChild != null)
                    // {
                    //     findChild.IsActive = false;
                    // }                    
                }
                //SelectedWorksheetData.Remove(itemToDelete);

                //TODO: apply a filter to the worksheet to hide inactive
               // await FilterInactive();
            }
            //In wms, this delete removes from worksheet but does not save worksheet
            //This needs to remove and recalculate, and mark for deletion if saving. so keep the isactive column, so when saved it saves
            await FilterInactive();
        }
        else
        {
            Console.WriteLine("The user changed their mind");
        }
    }

    async Task FilterInactive()
    {
        var filteredState = new TreeListState<WorksheetTreeModel>()
            {
                FilterDescriptors = new List<IFilterDescriptor>()
                    {
                        new CompositeFilterDescriptor(){
                            FilterDescriptors = new FilterDescriptorCollection()
                            {
                                new FilterDescriptor()
                                {
                                    Member = nameof(WorksheetTreeModel.IsActive),
                                    MemberType = typeof(bool),
                                    Operator = FilterOperator.IsEqualTo,
                                    Value = true
                                },
                            }
                        },

                    },
                ExpandedItems = WorksheetTreeList.State.ExpandedItems
            };
        //var expandedState = WorksheetTreeList.State.ExpandedItems;

        await WorksheetTreeList.SetStateAsync(filteredState);
    }
    async Task ClearSelected(TreeListCommandEventArgs args)
    {
        SelectedItems = new List<WorksheetTreeModel>();
    }
    async Task AddPlanOptionToHandler(TreeListCommandEventArgs args)
    {
        await AddPlanOptionToWorksheetModal.Show();
    }
    async Task SaveAsHandler(TreeListCommandEventArgs args)
    {
        //copy the worksheet to another worksheet with new name. Save it first
        var updateWorksheetResponse = await SalesPriceService.SaveUpdateWorksheetAsync(SelectedWorksheetData.ToList());
        if (updateWorksheetResponse.IsSuccess)
        {
            UnsavedChanges = false;
            CopyWorksheetModal.Show();
        }
        ShowSuccessOrErrorNotification(updateWorksheetResponse.Message, updateWorksheetResponse.IsSuccess);
    }
    async Task SaveWorksheetHandler(TreeListCommandEventArgs args)
    {
        worksheetLockedBy = await SalesPriceService.GetWorksheetLockAsync(SelectedWorksheet.WorksheetId);
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        worksheetEditable = worksheetLockedBy == userName;
        if (worksheetEditable)
        {
           // var updateWorksheetResponse = await SalesPriceService.UpdateWorksheetAsync(SelectedWorksheetDataToSave.ToList());
            var updateWorksheetResponse = await SalesPriceService.SaveUpdateWorksheetAsync(SelectedWorksheetData.ToList());
            if (updateWorksheetResponse.IsSuccess)
            {
                UnsavedChanges = false;
            }
            ShowSuccessOrErrorNotification(updateWorksheetResponse.Message, updateWorksheetResponse.IsSuccess);
        }
        else
        {
            await Dialogs.AlertAsync($"Cannot save changes. Worksheet is locked by {worksheetLockedBy}");
        }
    }
    async Task CancelChangesWorksheetHandler(TreeListCommandEventArgs args)
    {
        //reload the worksheet, which will go back to version before any unsaved changes
        await ReloadWorksheet((int)SelectedWorksheet.WorksheetId);
    }
    private async Task RepriceWorksheet(TreeListCommandEventArgs args)
    {   
        worksheetLockedBy = await SalesPriceService.GetWorksheetLockAsync(SelectedWorksheet.WorksheetId);
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        worksheetEditable = worksheetLockedBy == userName;
        if (worksheetEditable)
        {
            RepriceWorksheetModal.Show();
            StateHasChanged();            
        }
        else
        {
            await Dialogs.AlertAsync($"Cannot reprice. Worksheet is locked by {worksheetLockedBy}");
        }
    }
    private async Task RepriceOption(TreeListCommandEventArgs args)
    {
        worksheetLockedBy = await SalesPriceService.GetWorksheetLockAsync(SelectedWorksheet.WorksheetId);
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        worksheetEditable = worksheetLockedBy == userName;
        if (worksheetEditable)
        {
            WorksheetTreeModel item = (WorksheetTreeModel)args.Item;
            if (item.WorksheetOptionId != null)
            {
                SelectedWorksheetOpt = new WorksheetOptModel()
                    {
                        //plan is needed because base house looks like option in the worksheet but it's actually in separate table
                        WorksheetPlanId = item.WorksheetPlanId ?? 0,
                        WorksheetOptId = (int)item.WorksheetOptionId,
                        OptionCode = item.OptionCode,
                        OptionName = item.OptionName
                    };
                RepriceWorksheetOptModal.Show();
            }
        }
        else
        {
            await Dialogs.AlertAsync($"Cannot reprice. Worksheet is locked by {worksheetLockedBy}");
        }
    }
    private async Task ApplyPricing(TreeListCommandEventArgs args)
    {
        worksheetLockedBy = await SalesPriceService.GetWorksheetLockAsync(SelectedWorksheet.WorksheetId);
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        worksheetEditable = worksheetLockedBy == userName;
        if (worksheetEditable)
        {
            //save the worksheet first
            var confirm = await Dialogs.ConfirmAsync("Apply Pricing?");
            if (confirm)
            {
                var updateWorksheetResponse = new ResponseModel<List<WorksheetTreeModel>>();
                var selectedOptions = new List<WorksheetTreeModel>();

                foreach (var item in SelectedItems.ToList())
                {
                    if (item.WorksheetPlanId != null && item.WorksheetOptionId == null)
                    {
                        selectedOptions.AddRange(item.Children.Where(x => x.WorksheetOptionId != 1).ToList());
                    }
                    else if (item.WorksheetOptionId != null && item.WorksheetOptionId != 1)
                    {
                        selectedOptions.Add(item);
                    }
                }

                selectedOptions = selectedOptions.Distinct().ToList();

                updateWorksheetResponse = await SalesPriceService.UpdateWorksheetOptsAsync(selectedOptions);
                if (updateWorksheetResponse.IsSuccess)
                {
                    ApplyNewPricing = new ApplyPricingModel()
                        {
                            WorksheetData = selectedOptions,
                            ApplyAsCurrent = true
                        };
                    await ApplyPriceModal.Show();
                    StateHasChanged();
                }
                ShowSuccessOrErrorNotification(updateWorksheetResponse.Message, updateWorksheetResponse.IsSuccess);
            }
        }
        else
        {
            await Dialogs.AlertAsync($"Cannot apply pricing. Worksheet is locked by {worksheetLockedBy}");
        }
    }
    private async Task HandleApplyPricing(ResponseModel<ApplyPricingModel> response)
    {
        SelectedItems = Enumerable.Empty<WorksheetTreeModel>();
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
    }
    private async Task ApplyPricingOption(TreeListCommandEventArgs args)
    {
        worksheetLockedBy = await SalesPriceService.GetWorksheetLockAsync(SelectedWorksheet.WorksheetId);
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        worksheetEditable = worksheetLockedBy == userName;
        if (worksheetEditable)
        {
            //save the option first
            WorksheetTreeModel item = (WorksheetTreeModel)args.Item;
            var updateWorksheetOptResponse = await SalesPriceService.UpdateWorksheetOptsAsync(new List<WorksheetTreeModel> { item });//save the item before apply
            if (updateWorksheetOptResponse.IsSuccess)
            {
                //save the pricing
                if (item.WorksheetOptionId != null && (item.OptionName == "BASE HOUSE" || item.WorksheetOptionId != 1))
                {
                    //TODO: Why not apply base house price??
                    ApplyNewPricing.WorksheetData = new List<WorksheetTreeModel>() { item };
                    ApplyNewPricing.ApplyAsCurrent = true;
                    await ApplyPriceModal.Show();
                    StateHasChanged();
                }
            }
            else
            {
                ShowSuccessOrErrorNotification("There was an error saving Worksheet while applying price option. Please retry or contact BI if error persists", true);
            }

            //TODO: in wms it asks about applying the pricign to next price or pubhished price,
            //TODO: show a success message, and maybe also a confirm box before hand
            StateHasChanged();
        }
        else
        {
            await Dialogs.AlertAsync($"Cannot apply pricing. Worksheet is locked by {worksheetLockedBy}");
        }
    }
    private async Task OnRowDropHandler(TreeListRowDropEventArgs<WorksheetTreeModel> args)
    {
        if (args.DestinationTreeList == WorksheetTreeList)
        {
            worksheetLockedBy = await SalesPriceService.GetWorksheetLockAsync(SelectedWorksheet.WorksheetId);
            var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userName = user.User.Identity.Name.Split('@')[0];
            worksheetEditable = worksheetLockedBy == userName;
            if (worksheetEditable)
            {
                loadingWorksheetStyle = "";
                IsLoadingOption = true;
                WorksheetPlanData = null;

                var plansModel = new List<WorksheetPlanModel>();
                var optionsModel = new List<WorksheetOptModel>();
                foreach (var item in args.Items)
                {
                    var getTreeItem = item as WorksheetTreeModel;
                    if (getTreeItem.SubdivisionId != null)//subdivision is selected
                    {
                        //if subdivision is selected, add all the plans in the subdivision (including all the options)
                        var allPlansThisSubdivision = await PlanService.GetPhasePlansAsync((int)getTreeItem.SubdivisionId);
                        var plansToAdd = allPlansThisSubdivision.Value.Select(x => new WorksheetPlanModel() // not null as returning empty list in case of failure
                            {
                                WorksheetId = SelectedWorksheet.WorksheetId,
                                PhasePlanId = x.PhasePlanId
                            });
                        plansModel.AddRange(plansToAdd);
                    }
                    if (getTreeItem.PlanId != null)//plan is selected
                    {
                        plansModel.Add(new WorksheetPlanModel()
                            {
                                WorksheetId = SelectedWorksheet.WorksheetId,
                                PhasePlanId = (int)getTreeItem.PlanId
                            });
                    }
                    if (getTreeItem.WorksheetOptionId != null)//option level selected, this will add the plan and the selected options
                    {
                        optionsModel.Add(new WorksheetOptModel()
                            {
                                WorksheetId = SelectedWorksheet.WorksheetId,
                                PlanOptionId = (int)getTreeItem.WorksheetOptionId,
                                PhasePlanId = SubdivisionPlanOptionTreeData.Where(x => x.Id == getTreeItem.ParentId).FirstOrDefault().PlanId
                            });
                    }
                }
                if (optionsModel.Count > 0)
                {
                    var addOptionsResponse = await SalesPriceService.AddOptionsToWorksheetAsync(optionsModel);
                    ShowSuccessOrErrorNotification(addOptionsResponse.Message, addOptionsResponse.IsSuccess);
                }
                if (plansModel.Count > 0)
                {
                    var addPlansResponse = await SalesPriceService.AddPlansWithOptionsToWorksheetAsync(plansModel);
                    ShowSuccessOrErrorNotification(addPlansResponse.Message, addPlansResponse.IsSuccess);
                }
                //Refresh the worksheet
                await ReloadWorksheet((int)SelectedWorksheet.WorksheetId);
                //TODO: avoid the refresh                
            }

            else
            {
                await Dialogs.AlertAsync($"Cannot add plans/options. Worksheet is locked by {worksheetLockedBy}");
            }
        }
        else
        {
            var test = args.DestinationTreeList;//trying to drop to subdivision pick tree. Nothing happens
        }
    }
    private async Task ExportWorksheet()
    {
        loading = true;
        StateHasChanged();
        var worksheetData = await SalesPriceService.GetAllWorksheetDataAsync((int)SelectedWorksheet.WorksheetId);
        byte[] fileBytes = SalesWorksheetExcelHelper.ExportToExcel(worksheetData.Value);
        var fileName = SelectedWorksheet.WorksheetName + ".xlsx";
        await JSRuntimeService.InvokeAsync<object>("saveAsFile", fileName, Convert.ToBase64String(fileBytes));
        loading= false;
        StateHasChanged();
    }
    private async Task OnFileImportHandler(FileSelectEventArgs args)
    {
        if (args.Files.Count() > 1)
        {
            args.IsCancelled = true;
            ShowSuccessOrErrorNotification("Too many files. You can only upload one at a time.", false);
            return;

        }
        foreach (var file in args.Files)
        {
            var isConfirmed = true;
            if (UnsavedChanges)
            {
                isConfirmed = await Dialogs.ConfirmAsync("Worksheet has unsaved changes. Click OK to continue import or Cancel ?");
            }
            else
            {
                if (isConfirmed)
                {
                    Workbook workbook;
                    loading = true;
                    IWorkbookFormatProvider formatProvider = new XlsxFormatProvider();
                    var fileData = new byte[file.Stream.Length];
                    await file.Stream.ReadAsync(fileData);
                    using (Stream ms = new MemoryStream(fileData))
                    {
                        workbook = formatProvider.Import(ms);
                        ms.Close();
                    }

                    var ExcelSelectedWorksheetData = SalesWorksheetExcelHelper.ImportWorksheetFromExcel(workbook);                    
                    var updateWorksheetResponse = await SalesPriceService.UpdateWorksheetFromImportAsync(ExcelSelectedWorksheetData.ToList());
                    if (updateWorksheetResponse.IsSuccess)
                    {
                        //reloads the worksheet
                        await ReloadWorksheet((int)SelectedWorksheet.WorksheetId);
                        ShowSuccessOrErrorNotification("Successfully imported Sales Worksheet from Excel", true);
                    }
                    else
                    {
                        ShowSuccessOrErrorNotification("There was some issue while importing the data from Excel", false);
                        args.IsCancelled = true;
                    }
                    loading = false;
                    StateHasChanged();
                }
            }
        }
    }
    private void ShowSuccessOrErrorNotification(string message, bool IsSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = IsSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
