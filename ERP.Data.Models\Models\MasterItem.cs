﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class MasterItem
{
    public int MasterItemId { get; set; }

    public int MasterItemPhaseId { get; set; }

    public string? ItemNumber { get; set; }

    public string? PeCategoryCode { get; set; }

    public int BomClassId { get; set; }

    public string? ItemDesc { get; set; }

    public string? ItemNotes { get; set; }

    public int? Waste { get; set; }

    public string? UseWaste { get; set; }

    public string? TakeoffUnit { get; set; }

    public string? OrderUnit { get; set; }

    public double? CnvFctr { get; set; }

    public string? Multdiv { get; set; }

    public string? RndDir { get; set; }

    public double? RndUnit { get; set; }

    public double? PeUnitPrice { get; set; }

    public DateTime? PeUnitPriceDtCg { get; set; }

    public string? Taxable { get; set; }

    public string? JcPhase { get; set; }

    public string? JcCategory { get; set; }

    public string? Wbs1 { get; set; }

    public string? Wbs2 { get; set; }

    public string? Wbs3 { get; set; }

    public string? Wbs4 { get; set; }

    public string? Wbs5 { get; set; }

    public string? Wbs6 { get; set; }

    public string? Wbs7 { get; set; }

    public string? Wbs8 { get; set; }

    public string? Wbs9 { get; set; }

    public string? Wbs10 { get; set; }

    public string? Wbs11 { get; set; }

    public string? Wbs12 { get; set; }

    public string? Wbs13 { get; set; }

    public string? Wbs14 { get; set; }

    public string? Wbs15 { get; set; }

    public string? Wbs16 { get; set; }

    public string? Wbs17 { get; set; }

    public string? Wbs18 { get; set; }

    public string? Wbs19 { get; set; }

    public string? Wbs20 { get; set; }

    public string? Wbs21 { get; set; }

    public string? Wbs22 { get; set; }

    public string? Wbs23 { get; set; }

    public string? Wbs24 { get; set; }

    public string? Wbs25 { get; set; }

    public string? Wbs26 { get; set; }

    public string? Wbs27 { get; set; }

    public string? Wbs28 { get; set; }

    public string? Wbs29 { get; set; }

    public string? Wbs30 { get; set; }

    public string? Wbs31 { get; set; }

    public string? Wbs32 { get; set; }

    public string? Wbs33 { get; set; }

    public string? Wbs34 { get; set; }

    public string? Wbs35 { get; set; }

    public string? Wbs36 { get; set; }

    public string? Wbs37 { get; set; }

    public string? Wbs38 { get; set; }

    public string? Wbs39 { get; set; }

    public string? Wbs40 { get; set; }

    public string? OldItemNumber { get; set; }

    public string? DeletedFromPe { get; set; }

    public double? CalcPercent { get; set; }

    public string? ExcludeFromPo { get; set; }

    public string? Formula { get; set; }

    public int? EstDbOwner { get; set; }

    public int? CalcBasis { get; set; }

    public string? PlanSpecific { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<AsmDetail> AsmDetails { get; set; } = new List<AsmDetail>();

    public virtual BomClass BomClass { get; set; } = null!;

    public virtual ICollection<Cost> Costs { get; set; } = new List<Cost>();

    public virtual ICollection<Estdetail> Estdetails { get; set; } = new List<Estdetail>();

    public virtual MasterItemPhasis MasterItemPhase { get; set; } = null!;

    public virtual ICollection<Pactivity> Pactivities { get; set; } = new List<Pactivity>();
}
