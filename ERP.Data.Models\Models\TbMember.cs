﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class TbMember
{
    public int Id { get; set; }

    public int IndividualId { get; set; }

    public int MemberTypeId { get; set; }

    public int ClientId { get; set; }

    public int? LastUser { get; set; }

    public DateTime? LastChanged { get; set; }

    public int? BrokerInfoId { get; set; }

    public bool IncludeInFollowUp { get; set; }

    public string? Scenario { get; set; }

    public int? RelationshipId { get; set; }

    public DateTime Createddatetime { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? Dwuserid { get; set; }

    public bool? IsActive { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual TbClient Client { get; set; } = null!;

    public virtual TbIndividual Individual { get; set; } = null!;
}
