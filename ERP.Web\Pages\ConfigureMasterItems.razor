﻿@page "/configuremasteritems"
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, Accounting")]
<style>
    /* Gutter */
    .row > * {
        padding-right: calc(var(--bs-gutter-x) * .10);
        padding-left: calc(var(--bs-gutter-x) * .10);
    }

</style>
<PageTitle>Manage Master Items</PageTitle>
<div class="container-fluid flex">
    <div class="row d-flex">
        <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Manage Master Items</h7>
                </div>
            </div>
        </div>

               <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item"><a href="/configuremasterplans">Plans</a></li>
            <li class="breadcrumb-item active">Manage Items</li>
        </ol>


        <div class="col-lg-3">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Trade</h7>
                </div>
            </div>
            @if (MasterTradeData == null)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingTrade />
            }

            else
            {
                <TelerikGrid Data=@MasterTradeData
                             Size="@ThemeConstants.Grid.Size.Small"
                             ScrollMode="@GridScrollMode.Virtual"
                             FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                             PageSize="40"
                             Height="1000px"
                                                     RowHeight="40"
                                                   Sortable="true"
                                                   Resizable="true"
                                                   Groupable="false"
                                                    OnRowClick="@OnTradeRowClickHandler"
                         SelectionMode="GridSelectionMode.Single"
                         EditMode="@GridEditMode.Popup"
                         OnUpdate="@UpdateTradeHandler"
                         OnEdit="@EditTradeHandler"
                         OnDelete="@DeleteTradeHandler"
                         OnCreate="@CreateTradeHandler"
                         OnCancel="@CancelTradeHandler"
                         ConfirmDelete="true"
                         @ref="@TradeGridRef">
                    <GridToolBarTemplate>
                        @if (AllowEdit)
                        {
                            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Trade</GridCommandButton>
                        }                       
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>                    
                    <GridColumns>
                        <GridColumn Field="TradeName" Title="Trade" Editable="true" Groupable="false" />                       
                        <GridColumn Field="TradeDesc" Title="Description" Editable="true" Groupable="false" Width="0"/>
                       @*  <GridColumn Field="ReleaseCode" Title="Release Code" Editable="true" Groupable="false" Width="0" /> *@
                       @* <GridColumn Field="ActivityCode" Title="Activity Code" Editable="true" Groupable="false" Width="0" />*@
                        <GridColumn Field="TradeId" Visible="false" Editable="false" Groupable="false" />
                        <GridCommandColumn>
                            @if (AllowEdit)
                            {
                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                            }                            
                        </GridCommandColumn>
                    </GridColumns>
                    <GridSettings>
                        <GridPopupEditSettings Width="400px"
                                           Height="350px"
                                           Title="Trade">
                        </GridPopupEditSettings>
                    </GridSettings>
                </TelerikGrid>

            }
        </div>
        <div class="col-lg-4">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Activities for Trade: @SelectedTrade?.TradeDesc</h7>
                </div>
            </div>
            @if (MasterActivityData == null)
            {
                <p><em>Select a trade to see activities</em></p>
                <div style=@loadingActivityStyle>Loading...</div>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingActivity />
            }
            else
            {
                <TelerikGrid Data=@MasterActivityData                        
                         FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                             Size="@ThemeConstants.Grid.Size.Small"
                             Pageable="true"
                         PageSize="20"
                         Sortable="true"
                         Resizable="true"
                         Groupable="false"
                         OnRowClick="@OnActivityRowClickHandler"
                         SelectionMode="GridSelectionMode.Single"
                         EditMode="@GridEditMode.Popup"
                         OnUpdate="@UpdateActivityHandler"
                         OnEdit="@EditActivityHandler"
                         OnDelete="@DeleteActivityHandler"
                         OnCreate="@CreateActivityHandler"
                         OnCancel="@CancelActivityHandler"
                         ConfirmDelete="true"
                         @ref="@ActivityGridRef">
                    <GridColumns>
                        <GridColumn Field="Activity" Title="Activity" Editable="true" Groupable="false" />
                        <GridColumn Field="Description" Title="Description" Editable="true" Groupable="false" Width="0"/>
                        <GridColumn Field="Releasecode" Title="Release Code" Editable="true" Groupable="false" Width="0" />
                        <GridColumn Field="TradeId" Title="Trade" Editable="true" Groupable="false" Width="0">
                            <EditorTemplate>
                                @{
                                    SelectedActivity = context as PactivityModel;
                                    <TelerikDropDownList @bind-Value="@SelectedActivityTradeId"
                                                 Data="@MasterTradeData"
                                                 TextField="TradeName"
                                                 ValueField="TradeId"
                                                 Width="100%">
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                        </GridColumn>
                        <GridColumn Field="IsTaxable" Title="Taxable"  Editable="true" Groupable="false" Width="0"/>
                        <GridColumn Field="IsIncludeSelections" Title="Include Selections" Editable="true" Groupable="false" Width="0"/>
                        <GridColumn Field="Notes" Title="Notes" Editable="true" EditorType="@GridEditorType.TextArea" Groupable="false" Width="0" />
                        <GridColumn Field="PactivityId" Visible="false" Editable="false" Groupable="false" />
                        <GridCommandColumn>
                            @if (AllowEdit)
                            {
                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                            }                            
                        </GridCommandColumn>
                    </GridColumns>
                    <DetailTemplate>
                        @{
                            var item = context as PactivityModel;
                            <p>Release Code: @item.Releasecode</p>
                            <p>Include Selections on PO: @item.IncludeSelectionsOnPo</p>
                            <p>Taxable: @item.Taxable</p>                            
                            <p>Notes: @item.Notes</p>              
                        }
                    </DetailTemplate>
                    <GridToolBarTemplate>
                        @if (AllowEdit)
                        {
                            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Activity</GridCommandButton>
                        }
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <GridSettings>
                        <GridPopupEditSettings Width="400px"
                                           Height="400px"
                                           Title="Activity">
                        </GridPopupEditSettings>
                    </GridSettings>
                </TelerikGrid>

            }
        </div>
        <div class="col-lg-5">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Items for Activity: @SelectedActivity?.Activity</h7>
                </div>
            </div>
            @if (MasterItemData == null)
            {
                <p><em>Select an activity to see items</em></p>
                <div style=@loadingItemStyle>Loading...</div>
                 <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingItem />
            }
            else
            {
                <TelerikGrid Data=@MasterItemData 
                         Pageable="true"
                         FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                         Size="@ThemeConstants.Grid.Size.Small"
                         PageSize="40"
                         Sortable="true"
                         Resizable="true"
                         Reorderable="true"
                         Groupable="false"
                         SelectionMode="GridSelectionMode.Single"
                         EditMode="@GridEditMode.Popup"
                         OnUpdate="@UpdateItemHandler"
                         OnEdit="@EditItemHandler"
                         OnDelete="@DeleteItemHandler"
                         OnCreate="@CreateItemHandler"
                         OnCancel="@CancelItemHandler"
                         ConfirmDelete="true"
                         @ref="@ItemGridRef">
                <GridColumns>
                    <GridColumn Field="MasterItemId" Visible="false" Editable="false" Groupable="false" />
                    <GridColumn Field="ItemDesc" Title="Item" Editable="true" Groupable="false" />         
                    <GridColumn Field="PhaseCode" Title="Phase Code" Editable="false" Groupable="false" />
                    <GridColumn Field="PhaseDesc" Title="Phase Description" Editable="false" Groupable="false" Width="0" />
                    <GridColumn Field="ItemNumber" Title="Code" Editable="true" Groupable="false" Width="0" />
                        <GridColumn Field="BomClassName" Title="Default Purchasing Activity" Editable="true" Width="0">
                            <EditorTemplate>
                                @{
                                    SelectedItem = context as ModelManagerItemModel;
                                    <TelerikDropDownList @bind-Value="@SelectedItemBomClassId"
                                                 Data="@AllPactivities"
                                                 TextField="Activity"
                                                 ValueField="BomClassId"
                                                 Filterable="true"
                                                 FilterOperator="@StringFilterOperator.Contains"
                                                 Width="100%">
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                        </GridColumn>
                    @*<GridColumn Field="IsPlanSpecific" Title="Plan Specific" Editable="true" EditorType="@GridEditorType.CheckBox" Groupable="false" Width="0"/>*@
                    <GridColumn Field="TakeoffUnit" Title="TakeOff Unit" Editable="true" Groupable="false" Width="0">
                            <EditorTemplate>
                                @{
                                    SelectedItem = context as ModelManagerItemModel;
                                    <TelerikDropDownList @bind-Value="@SelectedItem.TakeoffUnit"
                                                 Data="@TakeoffUnitOptions"                                                
                                                 Width="100%">
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                    </GridColumn>
                    <GridColumn Field="OrderUnit"  Title="Order Unit"Editable="true" Groupable="false" Width="0">
                            <EditorTemplate>
                                @{
                                    SelectedItem = context as ModelManagerItemModel;
                                    <TelerikDropDownList @bind-Value="@SelectedItem.OrderUnit"
                                                 Data="@TakeoffUnitOptions"
                                                 Width="100%">
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                    </GridColumn>                        
                        <GridColumn Field="RndDir" Title="Round Dir" Editable="true" Groupable="false" Width="0">
                            <EditorTemplate>
                                @{
                                    SelectedItem = context as ModelManagerItemModel;
                                    <TelerikDropDownList @bind-Value="@SelectedItem.RndDir"
                                                 Data="@RoundingOptions"
                                                 Width="100%">
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                        </GridColumn>                     
                        <GridColumn Field="RndUnit" Title="Round Unit" Editable="true" Groupable="false" Width="0" />
                        @*  <GridColumn Field="Multdiv" Title="Multiply or divide" Editable="true" Groupable="false" Width="0">
                            <EditorTemplate>
                                @{
                                    SelectedItem = context as ModelManagerItemModel;
                                    <TelerikDropDownList @bind-Value="@SelectedItem.Multdiv"
                                                 Data="@MultDivOptions"
                                                 Width="100%">
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                         </GridColumn>   *@
                       @*  <GridColumn Field="CnvFctr" Title="Conv. Factor" Editable="true" Groupable="false" Width="0" /> *@
                        @* <GridColumn Field="Waste" Title="Waste %" Editable="true" Groupable="false" Width="0" /> *@
                        <GridColumn Field="IsPlanSpecific" Title="Lump Sum" Editable="true" Groupable="false" EditorType="@GridEditorType.CheckBox" Width="0" />
                       @*  <GridColumn Field="IsTaxable" Title="Taxable" Editable="true" Groupable="false" EditorType="@GridEditorType.CheckBox"  Width="0" />
                        <GridColumn Field="IsExcludeFromPO" Title="ExcludeFrom PO" Editable="true" EditorType="@GridEditorType.CheckBox" Groupable="false" Width="0" /> *@
                        <GridColumn Field="ItemNotes" Title="Notes"  Editable="true" Groupable="false" EditorType="@GridEditorType.TextArea" Width="0" />
                        <GridCommandColumn>
                            @if (AllowEdit)
                            {
                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                            }   
                        </GridCommandColumn>
                </GridColumns>
                    <DetailTemplate>
                        @{
                            var item = context as ModelManagerItemModel;
                            <p>Code: @item.ItemNumber</p>
                            <p>Phase Code: @item.PhaseCode</p>
                            <p>Phase Description: @item.PhaseDesc</p>
                            <p>Description: @item.ItemDesc</p>
                            <p>Takeoff Unit: @item.TakeoffUnit</p>
                            <p>Order Unit: @item.OrderUnit</p>
                            <p>Rounding Dir: @item.RndDir</p>
                            <p>Rounding Unit: @item.RndUnit</p>                           
                            <p>Multiply or Divide: @item.Multdiv</p>
                            <p>Conversion Factor: @item.CnvFctr</p>
                            <p>Waste %: @item.Waste</p>
                            <p>Exclude from PO: @item.ExcludeFromPo</p>
                            <p>Lump Sum: @item.IsPlanSpecific</p>//Lump Sum in WMS refers to PlanSpecific field. I don't know Why
                            //<p>PlanSpecific: @item.PlanSpecific</p>
                            <p>Taxable: @item.Taxable</p>
                            <p>Notes: @item.ItemNotes</p>
                        }
                    </DetailTemplate>
                <GridToolBarTemplate>
                    @if(AllowEdit)
                    {
                            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-success">Add Item</GridCommandButton>
                    }
                        
                        <TelerikToggleButton @bind-Selected="@ShowOptionSpecific" OnClick="@ToggleOptionSpecific" Class="k-button-add"> @ShowHideOptionSpecific Option Specific</TelerikToggleButton>
                    <GridSearchBox DebounceDelay="200"></GridSearchBox>
                </GridToolBarTemplate>
                    <GridSettings>
                        <GridPopupEditSettings Width="600px"
                                           Height="600px"
                                           Title="Item">
                        </GridPopupEditSettings>                       
                    </GridSettings>
            </TelerikGrid>                
            }
            
        </div>
    </div>
</div>

@code {
    private TelerikGrid<TradeDto> TradeGridRef { get; set; }
    private TelerikGrid<PactivityModel> ActivityGridRef { get; set; }
    private TelerikGrid<ModelManagerItemModel> ItemGridRef { get; set; }
    public List<TradeDto>? MasterTradeData { get; set; }
    public List<PactivityModel>? MasterActivityData { get; set; }
    public List<ModelManagerItemModel>? MasterItemData { get; set; }
    public List<PactivityModel>? AllPactivities { get; set; }
    public TradeDto SelectedTrade { get; set; }
    public PactivityModel SelectedActivity { get; set; }
    public ModelManagerItemModel SelectedItem { get; set; }
    public bool ShowOptionSpecific { get; set; } = false;
    public string ShowHideOptionSpecific { get; set; } = "Show";
    public List<string> TrueFalseOptions = new List<string> { "T", "F" };
    public List<string> TakeoffUnitOptions = new List<string> { "LS", "EA", "LF", "SF", "TON", "UKN" };
    public List<string> RoundingOptions = new List<string> { "None", "Up To Multiple" };
    public List<string> MultDivOptions = new List<string> { "Multiply", "Divide" };

    private string loadingActivityStyle = "display:none";
    private string loadingItemStyle = "display:none";
    public bool IsLoadingTrade { get; set; } = false;
    public bool IsLoadingActivity { get; set; } = false;
    public bool IsLoadingItem { get; set; } = false;

    public int? SelectedItemBomClassId { get; set; }
    public int? SelectedActivityTradeId { get; set; }

    private bool AllowEdit { get; set; } = true;

    protected override async Task OnInitializedAsync()
    {
        IsLoadingTrade = true;
        MasterTradeData = (await ItemService.GetTradesAsync()).Value;
        AllPactivities = (await ItemService.GetPurchasingActivitiesAsync()).Value;
        IsLoadingTrade = false;
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }


    protected async Task OnTradeRowClickHandler(GridRowClickEventArgs args)
    {
        MasterActivityData = null;
        loadingActivityStyle = "";
        IsLoadingActivity = true;
        var trade = args.Item as TradeDto;
        SelectedTrade = trade;
        SelectedActivityTradeId = trade.TradeId;
        MasterActivityData = (await ItemService.GetActivityByTradeAsync(trade.TradeId)).Value;
        MasterItemData = null;//reset the master item data else it shows items from a previously selected trade
        loadingActivityStyle = "display:none";
        IsLoadingActivity = false;
    }

    protected async Task OnActivityRowClickHandler(GridRowClickEventArgs args)
    {
        MasterItemData = null;
        loadingItemStyle = "";
        IsLoadingItem = true;
        SelectedActivity = args.Item as PactivityModel;
        MasterItemData = await GetItemData(SelectedActivity.PactivityId, ShowOptionSpecific);
        SelectedItemBomClassId = SelectedActivity.BomClassId;
        loadingItemStyle = "display:none";
        IsLoadingItem = false;
    }
    async Task<List<ModelManagerItemModel>> GetItemData(int activityId, bool showOptionSpecific = false)
    {
        return (await ItemService.GetItemsInActivityAsync(activityId, showOptionSpecific)).Value;
    }


    void EditTradeHandler(GridCommandEventArgs args)
    {
        TradeDto item = (TradeDto)args.Item;
    }

    async Task UpdateTradeHandler(GridCommandEventArgs args)
    {
        TradeDto item = (TradeDto)args.Item;
        await ItemService.UpdateTradeAsync(item);
        MasterTradeData = (await ItemService.GetTradesAsync()).Value;
    }

    async Task DeleteTradeHandler(GridCommandEventArgs args)
    {
        TradeDto item = (TradeDto)args.Item;
        var response = await ItemService.DeleteTradeAsync(item);
        if (response.IsSuccess){
            // update the local view-model data with the service data
            MasterTradeData = (await ItemService.GetTradesAsync()).Value;
        }
        else
        {
            var message = response.Message;//TODO: display error
        }
    }

    async Task CreateTradeHandler(GridCommandEventArgs args)
    {
        TradeDto item = (TradeDto)args.Item;
        await ItemService.AddTradeAsync(item);
        MasterTradeData = (await ItemService.GetTradesAsync()).Value;
    }

    async Task CancelTradeHandler(GridCommandEventArgs args)
    {
        TradeDto item = (TradeDto)args.Item;

        // if necessary, perform actual data source operation here through your service

        Console.WriteLine("Cancel event is fired.");
    }

    void EditActivityHandler(GridCommandEventArgs args)
    {
        PactivityModel item = (PactivityModel)args.Item;
        Console.WriteLine("Edit event is fired.");
    }

    async Task UpdateActivityHandler(GridCommandEventArgs args)
    {
        PactivityModel item = (PactivityModel)args.Item;
        item.TradeId = SelectedActivityTradeId; //not bound to model else it doesn't work in both update and edit to have default value
        await ItemService.UpdateActivityAsync(item);
        MasterActivityData = (await ItemService.GetActivityByTradeAsync(SelectedTrade.TradeId)).Value;
        Console.WriteLine("Update event is fired.");
    }

    async Task DeleteActivityHandler(GridCommandEventArgs args)
    {
        PactivityModel item = (PactivityModel)args.Item;
        await ItemService.DeleteActivityAsync(item.PactivityId);
        MasterActivityData = (await ItemService.GetActivityByTradeAsync(SelectedTrade.TradeId)).Value;
    }

    async Task CreateActivityHandler(GridCommandEventArgs args)
    {
        PactivityModel item = (PactivityModel)args.Item;
        item.TradeId = SelectedActivityTradeId; //not bound to model else it doesn't work in both update and edit to have default value
        var response = await ItemService.AddActivityAsync(item);
        if (response.IsSuccess)
        {
            var addedActivityId = response.Value.PactivityId;
            MasterActivityData = (await ItemService.GetActivityByTradeAsync(SelectedTrade.TradeId)).Value;
            SelectedActivity = MasterActivityData.SingleOrDefault(x => x.PactivityId == addedActivityId);
            MasterItemData = null;
            loadingItemStyle = "";
            IsLoadingItem = true;
            MasterItemData = await GetItemData(addedActivityId);
            AllPactivities = (await ItemService.GetPurchasingActivitiesAsync()).Value;//need to update this list here else can't add items to new activity
            SelectedItemBomClassId = SelectedActivity.BomClassId;
            loadingItemStyle = "display:none";
            IsLoadingItem = false;
        }

        StateHasChanged();
        //TODO: two way binding to the selected activity to make it appear selected on the grid
    }

    async Task CancelActivityHandler(GridCommandEventArgs args)
    {
        PactivityModel item = (PactivityModel)args.Item;
    }

    void EditItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
    }

    async Task UpdateItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
        item.BomClassId = SelectedItemBomClassId;//This is done separately rather than bound to the model so the dropdown can work in either add or edit
        await ItemService.UpdateItemAsync(item);
        MasterItemData = await GetItemData(SelectedActivity.PactivityId, ShowOptionSpecific);
    }

    async Task DeleteItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
        await ItemService.DeleteMasterItemAsync(item.MasterItemId);
        MasterItemData = await GetItemData(SelectedActivity.PactivityId, ShowOptionSpecific);
    }

    async Task CreateItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
        item.BomClassId = SelectedItemBomClassId;//This is done separately rather than bound to the model so the dropdown can work in either add or edit
        item.PhaseDesc = SelectedActivity.Activity;
        item.PhaseCode = SelectedActivity.Releasecode;//TODO: is this the right one? or should they be allowed to pick it from dropdown
        item.MasterPlanId = 1;
        await ItemService.AddMasterItemAsync(item);
        MasterItemData = await GetItemData(SelectedActivity.PactivityId, ShowOptionSpecific);
    }

    async Task CancelItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;

        // if necessary, perform actual data source operation here through your service

        Console.WriteLine("Cancel event is fired.");
    }
    async Task ToggleOptionSpecific()
    {
        var test = ShowOptionSpecific;
        ShowHideOptionSpecific = ShowOptionSpecific ? "Show" : "Hide";
        // ShowOptionSpecific = !ShowOptionSpecific;
        MasterItemData = await GetItemData(SelectedActivity.PactivityId, !ShowOptionSpecific);
    }
}
