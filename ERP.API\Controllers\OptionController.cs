﻿
using AutoMapper;
using DocumentFormat.OpenXml.InkML;
using DocumentFormat.OpenXml.Spreadsheet;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Graph.Drives.Item.Items.Item.Workbook.Functions.Ppmt;
using Microsoft.Graph.Models;
using NLog;
using System.Data;
using System.Linq;
using System.Security.Cryptography;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class OptionController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;

        public OptionController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
        }

        [HttpGet]
        public async Task<IActionResult> GetOptionGroupsAsync()
        {
            try
            {
                var groups = await _context.OptionGroups.Where(x => x.IsActive == true && x.DivId == 1).ToListAsync();
                return Ok(new ResponseModel<List<OptionGroupDto>>() { Value = _mapper.Map<List<OptionGroupDto>>(groups), IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<OptionGroupDto>>() { IsSuccess = false, Message = "failed to get option groups", Value = null });
            }

        }

        [HttpGet]
        public async Task<IActionResult> GetHomeAreasAsync()
        {
            try
            {
                var areas = await _context.HomeAreas.Where(x => x.IsActive == true).ToListAsync();
                var areasDto = _mapper.Map<List<HomeAreaDto>>(areas);
                return Ok(new ResponseModel<List<HomeAreaDto>>() { Value = areasDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<HomeAreaDto>>() { IsSuccess = false, Message = "failed to get home areas", Value = null });
            }
        }


        /// <summary>
        /// Master options by option group
        /// </summary>
        /// <param name="optionGroupId"></param>
        /// <returns></returns>
        [HttpGet("{optionGroupId}")]
        public async Task<IActionResult> GetMasterOptionsByGroupAsync(int optionGroupId)
        {
            try
            {
                var options = new List<MasterOptionHeaderModel>();
                var masterPlanIds = new List<AsmHeaderDto>();

                var connString = _configuration.GetConnectionString("ERPConnection");

                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    //MasterPlanId = 1 is for default plan, so master options, not ones in a specific plan 
                    var query = "SELECT ah.MASTER_OPTION_ID, ah.ASM_HEADER_ID, ah.ASSEMBLY_NOTES, ah.ASSEMBLY_SIZE, mo.OPTION_CODE, mo.OPTION_DESC, mo.OPTION_GROUP_ID, ah.IS_BASE_HOUSE, ah.IS_ELEVATION FROM dbo.ASM_HEADER ah join dbo.MASTER_OPTION mo on ah.MASTER_OPTION_ID = mo.OPTION_ID WHERE ah.IsActive = 1 and mo.IsActive = 1 and ah.MASTER_PLAN_ID = 1 and mo.OPTION_GROUP_ID = @optionGroupId";

                    var command = new SqlCommand(query, connection);
                    command.Parameters.AddWithValue("@optionGroupId", optionGroupId);

                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        var optionCode = reader.GetValue(4) != DBNull.Value ? (string)reader.GetValue(4) : null;
                        var optionDesc = reader.GetValue(5) != DBNull.Value ? (string)reader.GetValue(5) : null;
                        var optionId = reader.GetValue(0) != DBNull.Value ? (int)reader.GetValue(0) : 0;
                        //var getMasterPlanIds = _context.AsmHeaders.Where(x => x.MasterPlanId != 1 && x.MasterOptionId == optionId).Select(x => (int)x.MasterPlanId).ToList();

                        options.Add(new MasterOptionHeaderModel()
                        {
                            OptionId = optionId,
                            AsmHeaderId = reader.GetValue(1) != DBNull.Value ? (int)reader.GetValue(1) : 0,
                            Notes = reader.GetValue(2) != DBNull.Value ? (string)reader.GetValue(2) : null,
                            OptionSize = reader.GetValue(3) != DBNull.Value ? (int)reader.GetValue(3) : 0,
                            OptionCode = optionCode,
                            OptionDesc = optionDesc,
                            OptionGroupId = reader.GetValue(6) != DBNull.Value ? (int)reader.GetValue(6) : 0,
                            IsBaseHouse = reader.GetValue(7) != DBNull.Value ? (string)reader.GetValue(7) : null,
                            IsElevation = reader.GetValue(8) != DBNull.Value ? (string)reader.GetValue(8) : null,
                            BoolIsElevation = (reader.GetValue(8) != DBNull.Value ? (string)reader.GetValue(8) : null) == "T" ? true : false,
                            BoolIsBaseHouse = (reader.GetValue(7) != DBNull.Value ? (string)reader.GetValue(7) : null) == "T" ? true : false,
                            DisplayName = $"{optionCode} - {optionDesc}",
                            MasterPlanIds = new List<int>(),
                        });
                    }
                }

                if (options.Any())
                {

                    using (var connection = new SqlConnection(connString))
                    {
                        connection.Open();

                        var getMasterPlanIdQuery = "SELECT DISTINCT ah.master_plan_id, ah.MASTER_OPTION_ID FROM [dbo].[available_plan_option] ah join dbo.MASTER_OPTION mo on ah.MASTER_OPTION_ID = mo.OPTION_ID WHERE ah.IsActive = 1 and mo.IsActive = 1 and  ah.master_plan_id <> 1 and mo.OPTION_GROUP_ID = @optionGroupId";

                        var getMasterPlanCommand = new SqlCommand(getMasterPlanIdQuery, connection);
                        getMasterPlanCommand.Parameters.AddWithValue("@optionGroupId", optionGroupId);

                        var getMasterPlanReader = getMasterPlanCommand.ExecuteReader();

                        while (getMasterPlanReader.Read())
                        {
                            var getMasterPlanId = (getMasterPlanReader.GetValue(0) != DBNull.Value) ? (int)getMasterPlanReader.GetValue(0) : 0;
                            var masterOptionId = (getMasterPlanReader.GetValue(1) != DBNull.Value) ? (int)getMasterPlanReader.GetValue(1) : 0;
                            masterPlanIds.Add(new AsmHeaderDto
                            {
                                MasterOptionId = masterOptionId,
                                MasterPlanId = getMasterPlanId,
                            });
                        }
                    }


                    //foreach (var option in options)
                    //{
                    //    using (var connection = new SqlConnection(connString))
                    //    {
                    //        connection.Open();

                    //        var getMasterPlanIdQuery = "SELECT DISTINCT ah.master_plan_id FROM [dbo].[available_plan_option] ah join dbo.MASTER_OPTION mo on ah.MASTER_OPTION_ID = mo.OPTION_ID WHERE ah.IsActive = 1 and mo.IsActive = 1 and ah.master_option_id = @masterOptionId and ah.master_plan_id <> 1 and mo.OPTION_GROUP_ID = @optionGroupId";

                    //        var getMasterPlanCommand = new SqlCommand(getMasterPlanIdQuery, connection);
                    //        getMasterPlanCommand.Parameters.AddWithValue("@masterOptionId", option.OptionId);
                    //        getMasterPlanCommand.Parameters.AddWithValue("@optionGroupId", optionGroupId);

                    //        var getMasterPlanReader = getMasterPlanCommand.ExecuteReader();

                    //        while (getMasterPlanReader.Read())
                    //        {
                    //            var getMasterPlanId = (getMasterPlanReader.GetValue(0) != DBNull.Value) ? (int)getMasterPlanReader.GetValue(0) : 0;

                    //            masterPlanIds.Add(new AsmHeaderDto
                    //            {
                    //                MasterOptionId = option.OptionId,
                    //                MasterPlanId = getMasterPlanId,
                    //            });
                    //        }
                    //    }  
                    //}
                }

                if (masterPlanIds.Any())
                {
                    var matches = options.Where(x => masterPlanIds.Any(a => a.MasterOptionId == x.OptionId));

                    foreach (var option in matches)
                    {
                        foreach (var masterPlanId in masterPlanIds)
                        {
                            if (option.OptionId == masterPlanId.MasterOptionId)
                            {
                                option.MasterPlanIds.Add((int)masterPlanId.MasterPlanId);
                            }
                        }
                    }
                }

                options.OrderBy(x => x.OptionCode).ToList();

                //options = await _context.AsmHeaders.Where(x => x.MasterOption.OptionGroupId == optionGroupId && x.MasterPlanId == 1 && x.MasterOption.IsActive == true && x.IsActive == true).Select(x => new MasterOptionHeaderModel()
                //{
                //    OptionId = (int)x.MasterOptionId,
                //    AsmHeaderId = x.AsmHeaderId,
                //    Notes = x.AssemblyNotes,
                //    OptionSize = x.AssemblySize,
                //    OptionCode = x.MasterOption.OptionCode,
                //    OptionDesc = x.MasterOption.OptionDesc,
                //    OptionGroupId = x.MasterOption.OptionGroupId,
                //    IsBaseHouse = x.IsBaseHouse,
                //    IsElevation = x.IsElevation,
                //    BoolIsElevation = x.IsElevation == "T" ? true : false,
                //    BoolIsBaseHouse = x.IsBaseHouse == "T" ? true : false,
                //    DisplayName = $"{x.MasterOption.OptionCode} - {x.MasterOption.OptionDesc}",
                //}).OrderBy(x => x.OptionCode).ToListAsync();
                return Ok(new ResponseModel<List<MasterOptionHeaderModel>>() { Value = options, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MasterOptionHeaderModel>>() { IsSuccess = false, Message = "failed to get master options by group", Value = null });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetAllMasterOptionsAsync()
        {
            try
            {
                var options = new List<MasterOptionHeaderModel>();
                options = await _context.AsmHeaders.Where(x => x.MasterPlanId == 1 && x.MasterOption.IsActive == true && x.IsActive == true).Select(x => new MasterOptionHeaderModel()
                {
                    OptionId = (int)x.MasterOptionId,
                    AsmHeaderId = x.AsmHeaderId,
                    Notes = x.AssemblyNotes,
                    OptionSize = x.AssemblySize,
                    OptionCode = x.MasterOption.OptionCode,
                    OptionDesc = x.MasterOption.OptionDesc,
                    OptionGroupId = x.MasterOption.OptionGroupId,
                    IsBaseHouse = x.IsBaseHouse,
                    IsElevation = x.IsElevation,
                    BoolIsElevation = x.IsElevation == "T" ? true : false,
                    BoolIsBaseHouse = x.IsBaseHouse == "T" ? true : false
                }).ToListAsync();
                return Ok(new ResponseModel<List<MasterOptionHeaderModel>>() { Value = options, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MasterOptionHeaderModel>>() { IsSuccess = false, Message = "failed to get all master options", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteAvailablePlanOptionAsync([FromBody] int optionId)
        {
            try
            {
                //Don't delete, mark inactive
                var updateBy = User.Identity.Name.Split('@')[0];
                var findOption = await _context.AvailablePlanOptions.SingleOrDefaultAsync(x => x.PlanOptionId == optionId);
                findOption.IsActive = false;
                findOption.UpdatedBy = updateBy;
                findOption.UpdatedDateTime = DateTime.Now;
                _context.AvailablePlanOptions.Update(findOption);
                await _context.SaveChangesAsync();

                var worksheetOpts = _context.WorksheetOpts.Where(x => x.PlanOptionId == optionId);
                await worksheetOpts.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));

                return Ok(new ResponseModel<bool>() { Value = true, IsSuccess = true, Message = "Deleted option" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<bool>() { IsSuccess = false, Message = "failed to delete available plan option", Value = false });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteAvailablePlanOptionsAsync([FromBody] List<int> optionIds)
        {
            try
            {
                var options = _context.AvailablePlanOptions.Where(x => optionIds.Contains(x.PlanOptionId));
                var updateBy = User.Identity.Name.Split('@')[0];
                await options.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));

                var worksheetOpts = _context.WorksheetOpts.Where(x => optionIds.Contains(x.PlanOptionId));
                await worksheetOpts.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));

                return Ok(new ResponseModel<List<int>>() { Value = optionIds, IsSuccess = true, Message = "Deleted selected options" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<int>>() { IsSuccess = false, Message = "Failed to delete available plan options", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateAvailablePlanOptionAsync([FromBody] AvailablePlanOptionDto option)
        {
            try
            {
                var findOption = await _context.AvailablePlanOptions.SingleOrDefaultAsync(x => x.PlanOptionId == option.PlanOptionId);
                findOption.IsActive = option.IsActive;
                findOption.UpdatedBy = User.Identity.Name.Split('@')[0];
                findOption.UpdatedDateTime = DateTime.Now;
                findOption.ModifiedOptionDesc = option.ModifiedOptionDesc;
                findOption.OptionGroupId = option.OptionGroupId;
                findOption.OptionTypeId = option.OptionTypeId;
                _context.AvailablePlanOptions.Update(findOption);
                await _context.SaveChangesAsync();
                var responseOption = _mapper.Map<AvailablePlanOptionDto>(findOption);
                return Ok(new ResponseModel<AvailablePlanOptionDto>() { IsSuccess = true, Value = responseOption, Message = "Updated option" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<bool>() { IsSuccess = false, Message = "Failed to update available plan option", Value = false });
            }
        }

        [HttpGet("{planId}")]
        public async Task<IActionResult> GetAvailablePlanOptionsByPlanAsync(int planId)
        {
            try
            {
                var options = await _context.AvailablePlanOptions.Include(x => x.PhasePlan.MasterPlan).
                    Include(x => x.OptionTypeNavigation).
                    Include(x => x.OptionGroup).Where(x => x.PhasePlanId == planId && x.IsActive == true).OrderBy(x => x.OptionCode).ToListAsync();
                var mapOptions = _mapper.Map<List<AvailablePlanOptionDto>>(options.ToList());
                foreach (var option in mapOptions)
                {
                    option.DisplayOptionCodeWithPlanNum = $"{option.PhasePlan.MasterPlan.PlanNum}{option.OptionCode}";
                }
                return Ok(new ResponseModel<List<AvailablePlanOptionDto>>() { Value = mapOptions, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<AvailablePlanOptionDto>>() { IsSuccess = false, Message = "failed to get available plan options by plan", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddMasterOptionAsync([FromBody] MasterOptionHeaderModel Option)
        {
            try
            {
                //TODO: they have to type in the option code, but be sure it's unique and has the right letter for the group
                var findCode = _context.MasterOptions.Where(x => x.OptionCode == Option.OptionCode);
                var updatedBy = User.Identity.Name.Split('@')[0];

                if (findCode.Any())
                {
                    var findOptionId = findCode.Select(a => a.OptionId).FirstOrDefault();

                    await findCode.ExecuteUpdateAsync(a => a.SetProperty(b => b.IsActive, true).SetProperty(b => b.OptionDesc, Option.OptionDesc).SetProperty(b => b.UpdatedBy, updatedBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));

                    // Reactivate asm_header
                    await _context.AsmHeaders.Where(x => x.MasterOptionId == findOptionId && x.IsActive == false).ExecuteUpdateAsync(a => a.SetProperty(b => b.IsActive, true).SetProperty(b => b.UpdatedBy, updatedBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));

                    // Add to plans, reactivate
                    if (Option.MasterPlanIds != null && Option.MasterPlanIds.Any())
                    {
                        foreach (var masterPlanId in Option.MasterPlanIds)
                        {
                            var getPlanNum = _context.MasterPlans.Where(x => x.MasterPlanId == masterPlanId).FirstOrDefault();

                            if (getPlanNum != null)
                            {
                                if (findOptionId != 0)
                                {
                                    // Check to see if it's previously added
                                    var findInactives = _context.AvailablePlanOptions.Where(x => x.MasterOptionId == findOptionId && x.MasterPlanId == masterPlanId && x.IsActive == false);

                                    if (findInactives != null && findInactives.Any())
                                    {
                                        await findInactives.ExecuteUpdateAsync(a => a.SetProperty(b => b.IsActive, true).SetProperty(b => b.UpdatedBy, updatedBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));
                                    }
                                }
                            }
                        }
                    }

                    return Ok(new ResponseModel<MasterOptionHeaderModel> { IsSuccess = true, Message = "Option code exists. Updated with new info and reactivated, including all Available Plan Options.", Value = Option });
                }

                var addOption = new MasterOption()
                {
                    OptionCode = Option.OptionCode,
                    OptionDesc = Option.OptionDesc,
                    OptionGroupId = (int)Option.OptionGroupId,//TODO: fix
                    Elevation = Option.BoolIsElevation,
                    CreatedBy = updatedBy,
                    IsActive = true,
                };

                await _context.MasterOptions.AddAsync(addOption);
                await _context.SaveChangesAsync();

                Option.OptionId = addOption.OptionId;

                //Also add it to the asmHeader with default plan = 1
                var addAsmHeader = new AsmHeader()
                {
                    MasterPlanId = 1,
                    MasterOptionId = addOption.OptionId,
                    AssemblyDesc = addOption.OptionDesc,
                    AssemblyCode = addOption.OptionCode,//master options have assembly code start with plan code 9999. On 09/16, we decide that if Master Plan = 1, it will not have Plan Number prepended. But if it's not Master Plan = 1, then it must have Plan Number prepended. On 10/21, Edgar found out that Option Code is not prepended at all no matter what in Available Plan Option, so we're going to remove Plan Number in Available Plan Option.
                    IsElevation = Option.BoolIsElevation ? "T" : "F",
                    IsBaseHouse = Option.BoolIsBaseHouse ? "T" : "F",
                    AssemblyNotes = Option.Notes,
                    HomeAreaId = 1,//Default???
                    CreatedBy = updatedBy,
                    IsActive = true,
                };

                await _context.AsmHeaders.AddAsync(addAsmHeader);

                // Add to plans, exclude the ones that already added
                if (Option.MasterPlanIds != null && Option.MasterPlanIds.Any())
                {
                    foreach (var masterPlanId in Option.MasterPlanIds)
                    {
                        var getPlanNum = _context.MasterPlans.Where(x => x.MasterPlanId == masterPlanId).FirstOrDefault();

                        if (getPlanNum != null)
                        {
                            await _context.AsmHeaders.AddAsync(new AsmHeader
                            {
                                MasterPlanId = masterPlanId,
                                MasterOptionId = addOption.OptionId,
                                HomeAreaId = 1,
                                AssemblyDesc = addOption.OptionDesc,
                                AssemblyCode = $"{getPlanNum.PlanNum}{addOption.OptionCode}", // Need to prepend with Plan Code
                                IsElevation = Option.BoolIsElevation ? "T" : "F",
                                IsBaseHouse = Option.BoolIsBaseHouse ? "T" : "F",
                                AssemblyNotes = Option.Notes,
                                CreatedBy = updatedBy,
                                IsActive = true
                            });

                            var findPhases = _context.PhasePlans.Where(x => x.MasterPlanId == masterPlanId).ToList();

                            if (findPhases != null && findPhases.Any())
                            {
                                foreach (var phasePlanId in findPhases)
                                {
                                    await _context.AvailablePlanOptions.AddAsync(new AvailablePlanOption
                                    {
                                        PhasePlanId = phasePlanId.PhasePlanId,
                                        MasterPlanId = phasePlanId.MasterPlanId,
                                        HomeAreaId = 1,
                                        SubdivisionId = phasePlanId.SubdivisionId,
                                        MasterOptionId = Option.OptionId,
                                        CutoffStageId = 3, //Default?
                                        OptionGroupId = (int)Option.OptionGroupId,
                                        OptionTypeId = 6, //Default?
                                        //OptionCode = $"{getPlanNum.PlanNum}{addOption.OptionCode}", // Remove the Plan Number prepending on 10/21. See notes above.
                                        OptionCode = addOption.OptionCode,
                                        ModifiedOptionDesc = addOption.OptionDesc,
                                        MarginType = 0,
                                        MarginPercent = 71,
                                        CreatedBy = updatedBy,
                                        CreatedDateTime = DateTime.Now,
                                        IsActive = true
                                    });
                                }
                            }
                        }
                    }
                }

                await _context.SaveChangesAsync();

                return Ok(new ResponseModel<MasterOptionHeaderModel> { IsSuccess = true, Message = "Option added", Value = Option });
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return Ok(new ResponseModel<MasterOptionHeaderModel> { IsSuccess = false, Message = "Failed to add Option. Contact BI if problem persists", Value = Option });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CopyMasterOptionAsync([FromBody] MasterOptionHeaderModel Option)
        {
            try
            {
                //TODO: new master item on plan specific ones 
                var findHeader = _context.AsmHeaders.Include("MasterOption").Where(x => x.AsmHeaderId == Option.AsmHeaderId).SingleOrDefault();
                var addOrUpdateOption = new MasterOption();
                //TODO: thye have to type in the option code, but be sure it's unique and has the right letter for the group
                var findCode = _context.MasterOptions.Where(x => x.OptionCode == Option.OptionCode);
                if (findCode.Any())
                {
                    //TODO: 
                    //reactivate and update if it exists, send a message
                    addOrUpdateOption = findCode.First();
                    addOrUpdateOption.IsActive = true;
                    addOrUpdateOption.OptionCode = Option.OptionCode;
                    addOrUpdateOption.OptionDesc = Option.OptionDesc;
                    addOrUpdateOption.Elevation = findHeader.MasterOption.Elevation;
                    addOrUpdateOption.CascadeOptionSettings = findHeader.MasterOption.CascadeOptionSettings;
                    addOrUpdateOption.CustChoiceReq = findHeader.MasterOption.CustChoiceReq;
                    addOrUpdateOption.Restrictions = findHeader.MasterOption.Restrictions;
                    addOrUpdateOption.FloorOption = findHeader.MasterOption.FloorOption;
                    addOrUpdateOption.SqFt = findHeader.MasterOption.SqFt;
                    addOrUpdateOption.OptionSelectionType = findHeader.MasterOption.OptionSelectionType;
                    addOrUpdateOption.OptionLongDesc = findHeader.MasterOption.OptionLongDesc;
                    addOrUpdateOption.Room = findHeader.MasterOption.Room;
                    addOrUpdateOption.UpdatedBy = User.Identity.Name.Split('@')[0];
                    _context.MasterOptions.Update(addOrUpdateOption);
                    await _context.SaveChangesAsync();
                    Option.OptionId = addOrUpdateOption.OptionId;
                }
                else
                {
                    addOrUpdateOption = new MasterOption()
                    {
                        OptionCode = Option.OptionCode,
                        OptionDesc = Option.OptionDesc,
                        OptionGroupId = (int)Option.OptionGroupId,//TODO: fix
                        Elevation = findHeader.MasterOption.Elevation,
                        CascadeOptionSettings = findHeader.MasterOption.CascadeOptionSettings,
                        CustChoiceReq = findHeader.MasterOption.CustChoiceReq,
                        Restrictions = findHeader.MasterOption.Restrictions,
                        FloorOption = findHeader.MasterOption.FloorOption,
                        SqFt = findHeader.MasterOption.SqFt,
                        OptionSelectionType = findHeader.MasterOption.OptionSelectionType,
                        OptionLongDesc = findHeader.MasterOption.OptionLongDesc,
                        Room = findHeader.MasterOption.Room,
                        //TODO: fill all necessary fields
                        CreatedBy = User.Identity.Name.Split('@')[0],
                        IsActive = true,
                    };
                    await _context.MasterOptions.AddAsync(addOrUpdateOption);
                    await _context.SaveChangesAsync();
                    Option.OptionId = addOrUpdateOption.OptionId;
                }
                //Also add it to the asmHeader with default plan = 1
                var addAsmHeader = new AsmHeader()
                {
                    MasterPlanId = 1,
                    MasterOptionId = addOrUpdateOption.OptionId,
                    AssemblyDesc = addOrUpdateOption.OptionDesc,
                    //AssemblyCode = $"9999{addOption.OptionCode}",//master options have assembly code start with plan code 9999
                    AssemblyCode = $"{addOrUpdateOption.OptionCode}",//Not prepending 9999 anymore
                    IsElevation = findHeader.IsElevation,
                    IsBaseHouse = findHeader.IsBaseHouse,
                    AssemblyNotes = Option.Notes,
                    HomeAreaId = findHeader.HomeAreaId,
                    AssemblySize = findHeader.AssemblySize,
                    AssemblyUnit = findHeader.AssemblyUnit,
                    CreatedBy = User.Identity.Name.Split('@')[0],
                    IsActive = true,
                };
                await _context.AsmHeaders.AddAsync(addAsmHeader);
                await _context.SaveChangesAsync();

                //copy the asm details from the previous header to the new one 
                var getItemsToCopy = _context.AsmDetails.Where(x => x.AsmHeaderId == Option.AsmHeaderId).ToList();
                var insertItems = getItemsToCopy.Select(x => new AsmDetail()
                {
                    AsmHeaderId = addAsmHeader.AsmHeaderId,
                    BomClassId = x.BomClassId,
                    Factor = x.Factor,
                    Formula = x.Formula,
                    SelectAtTakeoffDesc = x.SelectAtTakeoffDesc,
                    SelectAtTakeoffUnit = x.SelectAtTakeoffUnit,
                    MasterItemId = x.MasterItemId,//TODO: check about master item phase and plan/option specific ones. these might need soemthing chagned
                    Calculation = x.Calculation,
                    CalculationCode = x.CalculationCode,
                    CreatedBy = User.Identity.Name.Split('@')[0],
                    DetailType = x.DetailType,
                    OptionItemNotes = x.OptionItemNotes,
                    Ordinality = x.Ordinality,
                });
                _context.AsmDetails.AddRange(insertItems);
                _context.SaveChanges();

            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MasterOptionHeaderModel>() { IsSuccess = false, Message = "failed to copy master option", Value = null });
            }

            return Ok(new ResponseModel<MasterOptionHeaderModel>() { Value = Option, IsSuccess = true, Message = "Success copying Master Option" });
        }


        [HttpPut]
        public async Task<IActionResult> UpdateMasterOptionAsync([FromBody] MasterOptionHeaderModel Option)
        {
            try
            {
                var updatedBy = User.Identity.Name.Split('@')[0];

                //update the master option
                var findOption = _context.MasterOptions.SingleOrDefault(x => x.OptionId == Option.OptionId);
                findOption.OptionGroupId = (int)Option.OptionGroupId;
                findOption.OptionDesc = Option.OptionDesc;
                findOption.OptionCode = Option.OptionCode;
                findOption.UpdatedBy = updatedBy;
                findOption.UpdatedDateTime = DateTime.Now;
                _context.MasterOptions.Update(findOption);

                //update asm header
                var findHeader = _context.AsmHeaders.SingleOrDefault(x => x.MasterPlanId == 1 && x.MasterOptionId == Option.OptionId);
                findHeader.AssemblyDesc = Option.OptionDesc;
                findHeader.AssemblyCode = Option.OptionCode;
                findHeader.IsElevation = Option.BoolIsElevation ? "T" : "F";
                findHeader.IsBaseHouse = Option.BoolIsBaseHouse ? "T" : "F";
                findHeader.AssemblyNotes = Option.Notes;
                findHeader.UpdatedBy = updatedBy;
                findHeader.UpdatedDateTime = DateTime.Now;
                _context.AsmHeaders.Update(findHeader);

                await _context.SaveChangesAsync();

                // update plan(s), exclude existing
                // find if user removes plan
                var findPreviousAvailablePlanOptions = _context.AvailablePlanOptions.Where(x => x.MasterOptionId == Option.OptionId && x.IsActive == true);

                if (findPreviousAvailablePlanOptions.Any())
                {
                    // deactivate
                    await findPreviousAvailablePlanOptions.ExecuteUpdateAsync(a => a.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updatedBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));
                }

                if (Option.MasterPlanIds != null && Option.MasterPlanIds.Any())
                {
                    foreach (var planId in Option.MasterPlanIds)
                    {
                        var findExisting = _context.AsmHeaders.Where(x => x.MasterPlanId == planId && x.MasterOptionId == Option.OptionId && x.IsActive == true).ToList();
                        var findPlanNum = _context.MasterPlans.Where(x => x.MasterPlanId == planId).Select(x => x.PlanNum).SingleOrDefault();

                        // Asm Header
                        // TODO: Asm Detail
                        // New
                        if (!findExisting.Any())
                        {
                            var buildAsmHeader = new AsmHeader
                            {
                                MasterPlanId = planId,
                                MasterOptionId = Option.OptionId,
                                HomeAreaId = 1,
                                AssemblyCode = planId == 1 ? Option.OptionCode : $"{findPlanNum}{Option.OptionCode}",//plan code should append here unless it's in plan 1,
                                AssemblyDesc = Option.OptionDesc,
                                IsElevation = Option.BoolIsElevation ? "T" : "F",
                                IsBaseHouse = Option.BoolIsBaseHouse ? "T" : "F",
                                CreatedBy = updatedBy,
                                CreatedDateTime = DateTime.Now,
                                IsActive = true
                            };

                            await _context.AsmHeaders.AddAsync(buildAsmHeader);

                            var findMasterItems = _context.AsmDetails.Where(x => x.AsmHeaderId == findHeader.AsmHeaderId).Select(x => x.MasterItemId);

                            foreach (var masterItem in findMasterItems)
                            {
                                await _context.AsmDetails.AddAsync(new AsmDetail
                                {
                                    AsmHeaderId = buildAsmHeader.AsmHeaderId,
                                    MasterItemId = masterItem,
                                    CreatedBy = updatedBy,
                                    CreatedDateTime = DateTime.Now
                                });
                            }
                        }
                        //Reactivate
                        else
                        {
                            foreach (var asmHeader in findExisting)
                            {
                                int? getAsmHeaderId = null;

                                // Asm header
                                var findAsmHeader = _context.AsmHeaders.Where(x => x.AsmHeaderId == asmHeader.AsmHeaderId).SingleOrDefault();

                                if (findAsmHeader != null)
                                {
                                    findAsmHeader.AssemblyCode = planId == 1 ? Option.OptionCode : $"{findPlanNum}{Option.OptionCode}";//plan code should append here unless it's in plan 1
                                    findAsmHeader.IsActive = true;
                                    findAsmHeader.UpdatedBy = updatedBy;
                                    findAsmHeader.UpdatedDateTime = DateTime.Now;
                                    _context.AsmHeaders.Update(findAsmHeader);
                                    getAsmHeaderId = findAsmHeader.AsmHeaderId;
                                }
                                else
                                {
                                    // Add
                                    var buildAsmHeader = new AsmHeader
                                    {
                                        MasterPlanId = planId,
                                        MasterOptionId = Option.OptionId,
                                        HomeAreaId = 1,
                                        AssemblyCode = planId == 1 ? Option.OptionCode : $"{findPlanNum}{Option.OptionCode}",//plan code should append here unless it's in plan 1
                                        AssemblyDesc = Option.OptionDesc,
                                        IsElevation = Option.BoolIsElevation ? "T" : "F",
                                        IsBaseHouse = Option.BoolIsBaseHouse ? "T" : "F",
                                        CreatedBy = updatedBy,
                                        CreatedDateTime = DateTime.Now,
                                        IsActive = true
                                    };

                                    _context.AsmHeaders.Add(buildAsmHeader);

                                    getAsmHeaderId = buildAsmHeader.AsmHeaderId;
                                }

                                // Asm details
                                var findAsmDetails = _context.AsmDetails.Where(x => x.AsmHeaderId == asmHeader.AsmHeaderId).ToList();

                                if (findAsmDetails != null)
                                {
                                    foreach (var detail in findAsmDetails)
                                    {
                                        detail.IsActive = true;
                                        detail.UpdatedBy = updatedBy;
                                        detail.UpdatedDateTime = DateTime.Now;
                                    }
                                    _context.AsmDetails.UpdateRange(findAsmDetails);
                                }
                                else
                                {
                                    // Add
                                    if (getAsmHeaderId != null && getAsmHeaderId != 0)
                                    {
                                        var findAsmHeaders = _context.AsmHeaders.Where(x => x.AsmHeaderId == findHeader.AsmHeaderId).Select(x => x.AsmHeaderId).ToList();

                                        if (findAsmHeaders != null && findAsmHeaders.Any())
                                        {
                                            foreach (var headerId in findAsmHeaders)
                                            {
                                                var findAsmMasterItems = _context.AsmDetails.Where(x => x.AsmHeaderId == headerId).Select(x => x.MasterItem).ToList();

                                                if (findAsmMasterItems != null && findAsmMasterItems.Any())
                                                {
                                                    foreach (var masterItem in findAsmMasterItems)
                                                    {
                                                        _context.AsmDetails.Add(new AsmDetail
                                                        {
                                                            AsmHeaderId = (int)getAsmHeaderId,
                                                            MasterItemId = masterItem.MasterItemId,
                                                            CreatedBy = updatedBy,
                                                            CreatedDateTime = DateTime.Now
                                                        });
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // Available Plan Options in Subdivisions
                        // TODO: Add Attributes if the Assembly Code contains attribute (?)
                        var findPhasePlanIds = _context.PhasePlans.Where(x => x.MasterPlanId == planId).ToList();

                        if (findPhasePlanIds != null && findPhasePlanIds.Any())
                        {
                            foreach (var phasePlanId in findPhasePlanIds)
                            {
                                // Check existing
                                var findPreviousAvailablePlanOptionsInPhase = _context.AvailablePlanOptions.Where(x => x.MasterPlanId == phasePlanId.MasterPlanId && x.MasterOptionId == Option.OptionId && x.IsActive == false && x.PhasePlanId == phasePlanId.PhasePlanId);

                                // Reactivate
                                if (findPreviousAvailablePlanOptionsInPhase.Any())
                                {
                                    await findPreviousAvailablePlanOptionsInPhase.ExecuteUpdateAsync(a => a.SetProperty(b => b.IsActive, true).SetProperty(b => b.UpdatedBy, updatedBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));
                                }
                                // New
                                else
                                {
                                    await _context.AvailablePlanOptions.AddAsync(new AvailablePlanOption
                                    {
                                        PhasePlanId = phasePlanId.PhasePlanId,
                                        MasterPlanId = phasePlanId.MasterPlanId,
                                        HomeAreaId = 1,
                                        SubdivisionId = phasePlanId.SubdivisionId,
                                        MasterOptionId = Option.OptionId,
                                        CutoffStageId = 3, //Default?
                                        OptionGroupId = (int)Option.OptionGroupId,
                                        OptionTypeId = 6, //Default?
                                        //OptionCode = $"{findPlanNum}{Option.OptionCode}", // Remove prepending on 10/21. See notes.
                                        OptionCode = Option.OptionCode,
                                        OptionLongDesc = (!string.IsNullOrWhiteSpace(Option.OptionLongDesc)) ? Option.OptionLongDesc : Option.OptionDesc,
                                        MarginType = 0,
                                        MarginPercent = 71,
                                        CreatedBy = updatedBy,
                                        CreatedDateTime = DateTime.Now,
                                        IsActive = true
                                    });

                                    // Add attributes (?)
                                }
                            }
                        }
                    }
                }

                await _context.SaveChangesAsync();
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MasterOptionHeaderModel>() { IsSuccess = false, Message = "Failed to update Master Option", Value = null });
            }

            return Ok(new ResponseModel<MasterOptionHeaderModel>() { Value = Option, IsSuccess = true, Message = "Success updating Master Option" });
        }

        [HttpPut]
        public async Task<IActionResult> DeleteMasterOptionAsync([FromBody] MasterOptionDto Option)
        {
            try
            {
                var updatedBy = User.Identity.Name.Split('@')[0];

                var findOption = _context.MasterOptions.SingleOrDefault(x => x.OptionId == Option.OptionId);

                findOption.IsActive = false;
                findOption.UpdatedBy = updatedBy;
                findOption.UpdatedDateTime = DateTime.Now;

                _context.MasterOptions.Update(findOption);

                // Deactivate available plan options
                var findActives = _context.AvailablePlanOptions.Where(x => x.MasterOptionId == Option.OptionId && x.IsActive == true);

                if (findActives != null)
                {
                    await findActives.ExecuteUpdateAsync(a => a.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updatedBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));
                }

                // Deactive asm header
                await _context.AsmHeaders.Where(x => x.MasterOptionId == Option.OptionId).ExecuteUpdateAsync(x => x.SetProperty(a => a.IsActive, false).SetProperty(a => a.UpdatedBy, updatedBy).SetProperty(a => a.UpdatedDateTime, DateTime.Now));

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MasterOptionDto>() { IsSuccess = false, Message = "failed to delete master option", Value = null });
            }

            return Ok(new ResponseModel<MasterOptionDto>() { Value = Option, IsSuccess = true, Message = "Master Option successfully deleted" });
        }

        [HttpPost]
        public async Task<IActionResult> AddOptionGroupAsync([FromBody] OptionGroupDto OptionGroup)
        {
            try
            {
                var addOptionGroup = new OptionGroup()
                {
                    OptionGroupLetter = OptionGroup.OptionGroupLetter,
                    OptionGroupName = $"{OptionGroup.OptionGroupLetter}. {OptionGroup.OptionGroupName}",
                    DeftMarkupAmount = OptionGroup.DeftMarkupAmount,
                    DeftMarkupType = OptionGroup.DeftMarkupType,
                    IsActive = true,
                    DivId = 1,//Default since we won't use division
                    CreatedBy = User.Identity.Name.Split('@')[0]
                };
                await _context.OptionGroups.AddAsync(addOptionGroup);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<OptionGroupDto>() { Value = OptionGroup, IsSuccess = true, Message = "Success in adding Option Group" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<OptionGroupDto>() { IsSuccess = false, Message = "failed to add option group", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateOptionGroupAsync([FromBody] OptionGroupDto OptionGroup)
        {
            try
            {
                var findOptionGroup = _context.OptionGroups.SingleOrDefault(x => x.OptionGroupId == OptionGroup.OptionGroupId);
                findOptionGroup.UpdatedBy = User.Identity.Name.Split('@')[0];
                findOptionGroup.UpdatedDateTime = DateTime.Now;
                findOptionGroup.OptionGroupLetter = OptionGroup.OptionGroupLetter;
                findOptionGroup.OptionGroupName = OptionGroup.OptionGroupName;
                findOptionGroup.DeftMarkupAmount = OptionGroup.DeftMarkupAmount;
                findOptionGroup.Note = OptionGroup.Note;
                findOptionGroup.DeftMarkupType = OptionGroup.DeftMarkupType;
                _context.OptionGroups.Update(findOptionGroup);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<OptionGroupDto>() { Value = OptionGroup, IsSuccess = true, Message = "Option Group updated" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<OptionGroupDto>() { IsSuccess = false, Message = "failed to update option group", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeleteOptionGroupAsync([FromBody] OptionGroupDto OptionGroup)
        {
            try
            {
                var findOptionGroup = _context.OptionGroups.SingleOrDefault(x => x.OptionGroupId == OptionGroup.OptionGroupId);
                findOptionGroup.IsActive = false;
                findOptionGroup.UpdatedBy = User.Identity.Name.Split('@')[0];
                findOptionGroup.UpdatedDateTime = DateTime.Now;
                _context.OptionGroups.Update(findOptionGroup);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<OptionGroupDto>() { Value = OptionGroup, IsSuccess = true, Message = "Option Group successfully deleted" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<OptionGroupDto>() { IsSuccess = false, Message = "failed to delete option group", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddToAvailablePlanOptionAsync([FromBody] AddOptionToPlanModel OptionsToAdd)
        {
            var responseModel = new AddOptionToPlanModel()
            {
                SubdivisionPlanModel = OptionsToAdd.SubdivisionPlanModel,
                OptionsToAdd = OptionsToAdd.OptionsToAdd
            };

            try
            {
                var userName = User.Identity.Name.Split('@')[0];

                // Get the plan ids for the options in OptionsToAdd which have been soft deleted before 
                var availablePlanOptionIdsToUpdate = _context.AvailablePlanOptions
                    .Where(a => a.IsActive == false)
                    .Join(
                        _context.AsmHeaders
                            .Where(h => OptionsToAdd.OptionsToAdd.Select(x => x.AsmHeaderId).Contains(h.AsmHeaderId)),
                        a => new
                        {
                            SubdivisionId = a.SubdivisionId,
                            MasterOptionId = a.MasterOptionId,
                            PhasePlanId = a.PhasePlanId,                            
                        },
                        h => new
                        {
                            SubdivisionId = OptionsToAdd.SubdivisionPlanModel.SubdivisionId,
                            MasterOptionId = h.MasterOptionId,
                            PhasePlanId = OptionsToAdd.SubdivisionPlanModel.PhasePlanId
                        },
                        (a, h) => a
                    )
                    .GroupBy(a => new { a.SubdivisionId, a.MasterOptionId, a.PhasePlanId })
                    .Select(g => g.OrderByDescending(x => x.CreatedDateTime).First().PlanOptionId) // Select only the most recently soft deleted id, as there may be many soft deleted options for a specific plan id from before this change
                    .ToList();

                // Find all the asmHeaderIds for those soft deleted options
                var asmHeaderIds = _context.AvailablePlanOptions
                    .Where(a => availablePlanOptionIdsToUpdate.Contains(a.PlanOptionId))
                    .Join(
                        _context.AsmHeaders,
                        a => new
                        {
                            a.SubdivisionId,
                            a.MasterOptionId,
                            a.PhasePlanId,
                            a.MasterPlanId
                        },
                        h => new
                        {
                            SubdivisionId = OptionsToAdd.SubdivisionPlanModel.SubdivisionId,
                            MasterOptionId = h.MasterOptionId,
                            PhasePlanId = OptionsToAdd.SubdivisionPlanModel.PhasePlanId,
                            MasterPlanId = h.MasterPlanId
                        },
                        (a, h) => h.AsmHeaderId
                    ).ToList();

                // if an option has been soft deleted before, remove it from the list of options we are going to add
                OptionsToAdd.OptionsToAdd = OptionsToAdd.OptionsToAdd
                    .Where(x => !asmHeaderIds.Contains(x.AsmHeaderId))
                    .ToList();

                // reactivate soft deleted options
                await _context.AvailablePlanOptions
                   .Where(a => availablePlanOptionIdsToUpdate.Contains(a.PlanOptionId))
                   .ExecuteUpdateAsync(s => s
                       .SetProperty(p => p.IsActive, true)
                       .SetProperty(p => p.UpdatedBy, userName)
                       .SetProperty(p => p.UpdatedDateTime, DateTime.Now)
                   );

                // add all the remaining options which have not been soft deleted before
                var optionsToAdd = (from option in OptionsToAdd.OptionsToAdd
                                    let item = _context.AsmHeaders.Where(x => option.AsmHeaderId == x.AsmHeaderId).Select(x => new AvailablePlanOption()
                                    {
                                        SubdivisionId = (int)OptionsToAdd.SubdivisionPlanModel.SubdivisionId,
                                        OptionCode = x.MasterOption.OptionCode,
                                        MasterPlanId = x.MasterPlanId,
                                        OptionGroupId = x.MasterOption.OptionGroupId,
                                        ModifiedOptionDesc = x.MasterOption.OptionDesc,//They can change it later, but if it's just copy for the whole plan, need to have something
                                        Phase = OptionsToAdd.SubdivisionPlanModel.Phase,
                                        MasterOptionId = x.MasterOptionId,
                                        HomeAreaId = x.HomeAreaId,
                                        OptionTypeId = 6, //7 is the ID for "Standard" as the default Per Julie 11/19, default should be 6, Finance
                                        UnitPrice = x.MasterOption.UnitPrice,
                                        UnitCost = x.MasterOption.UnitCost,
                                        UnitQty = x.MasterOption.UnitQty,
                                        IsActive = true,
                                        OptionSelectionType = x.MasterOption.OptionSelectionType ?? "S",
                                        PhasePlanId = OptionsToAdd.SubdivisionPlanModel.PhasePlanId,
                                        CreatedDateTime = DateTime.Now,
                                        CreatedBy = userName,
                                        AgentModQty = x.MasterOption.AgentModQty ?? false,
                                        AgentModType = x.MasterOption.AgentModType ?? false,
                                        AgentModPrice = x.MasterOption.AgentModPrice ?? false,
                                        CustChoiceReq = x.MasterOption.CustChoiceReq ?? false,
                                        Elevation = x.MasterOption.Elevation ?? false,
                                        ActiveDepositSched = x.MasterOption.ActiveDepositSched ?? false,
                                        DollarAmt = x.MasterOption.DollarAmt ?? false,
                                        IsElevation = x.IsElevation
                                    }).FirstOrDefault()
                                    select item).ToList();

                await _context.AvailablePlanOptions.BulkInsertAsync(optionsToAdd);

            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<AddOptionToPlanModel>() { IsSuccess = false, Message = "failed add to available plan option", Value = null });
            }

            return Ok(new ResponseModel<AddOptionToPlanModel>() { Value = responseModel, IsSuccess = true, Message = "Success in adding Available Plan Options" });
        }

        [HttpPost]
        public async Task<IActionResult> AddOptionsToMasterPlanAsync([FromBody] AddOptionToPlanModel OptionToAdd)
        {
            var responseModel = new AddOptionToPlanModel()
            {
                Plan = OptionToAdd.Plan,
                OptionsToAdd = new List<AsmHeaderModel>()
            };

            //for lump sum (PlanSpecific) items, add new master item, else just add new asmdetail
            try
            {
                var listCopyOptions = new List<CopyAsmHeaderModel>();

                foreach (var option in OptionToAdd.OptionsToAdd)
                {
                    var getOption = _context.AsmHeaders.Include("MasterOption").SingleOrDefault(x => x.AsmHeaderId == option.AsmHeaderId);//This is the option we are copying from
                    var addOption = new AsmHeader()
                    {
                        MasterPlanId = OptionToAdd.Plan.MasterPlanId,
                        MasterOptionId = getOption.MasterOptionId,
                        HomeAreaId = option.HomeAreaId,
                        AssemblyDesc = getOption.AssemblyDesc,
                        AssemblyNotes = getOption.AssemblyNotes,
                        AsmGroupId = getOption.AsmGroupId,
                        AssemblySize = getOption.AssemblySize,
                        IsBaseHouse = getOption.IsBaseHouse,
                        IsElevation = getOption.IsElevation,
                        AssemblyCode = $"{OptionToAdd.Plan.PlanNum}{getOption.MasterOption.OptionCode}",//Per Julie 12/5, if adding to plan, this should include the plan code
                        CreatedBy = User.Identity.Name.Split('@')[0]
                    };

                    //Check if an existing Option with the same Option Code already exists
                    var findExisting = _context.AsmHeaders.Where(x => x.AssemblyCode == addOption.AssemblyCode && x.MasterPlanId == OptionToAdd.Plan.MasterPlanId).FirstOrDefault();
                    if (findExisting != null)
                    {
                        //reactivate and update if it was inactive
                        if (findExisting.IsActive == false)
                        {
                            findExisting.IsActive = true;
                            findExisting.AssemblyDesc = getOption.AssemblyDesc;
                            findExisting.AssemblyNotes = getOption.AssemblyNotes;
                            findExisting.AsmGroupId = getOption.AsmGroupId;
                            findExisting.AssemblySize = getOption.AssemblySize;
                            findExisting.AssemblyUnit = getOption.AssemblyUnit;
                            findExisting.IsBaseHouse = getOption.IsBaseHouse;
                            findExisting.IsElevation = getOption.IsElevation;
                            findExisting.UpdatedBy = User.Identity.Name.Split('@')[0];
                            findExisting.UpdatedDateTime = DateTime.Now;
                            _context.AsmHeaders.Update(findExisting);
                            await _context.SaveChangesAsync();
                        }

                        //deactivate any existing asmdetail, readd below 
                        var findDetails = _context.AsmDetails.Where(x => x.AsmHeaderId == findExisting.AsmHeaderId).ToList();
                        foreach (var detail in findDetails)
                        {
                            detail.IsActive = false;
                            detail.UpdatedBy = User.Identity.Name.Split('@')[0];
                            detail.UpdatedDateTime = DateTime.Now;
                        }
                        await _context.BulkUpdateAsync(findDetails, options => options.ColumnInputExpression = x => new { x.IsActive, x.UpdatedDateTime, x.UpdatedBy });
                        var addCopyOption = new CopyAsmHeaderModel()
                        {
                            OldAsmHeaderId = option.AsmHeaderId,
                            NewAsmHeaderId = findExisting.AsmHeaderId
                        };
                        listCopyOptions.Add(addCopyOption);
                    }
                    else
                    {
                        await _context.AsmHeaders.AddAsync(addOption);
                        await _context.SaveChangesAsync();

                        var addCopyOption = new CopyAsmHeaderModel()
                        {
                            OldAsmHeaderId = option.AsmHeaderId,
                            NewAsmHeaderId = addOption.AsmHeaderId
                        };
                        listCopyOptions.Add(addCopyOption);
                    }
                }

                if (listCopyOptions.Count > 0)
                {
                    //add the items
                    var getItems = _context.AsmDetails.Include(x => x.AsmHeader.MasterOption).Include(x => x.MasterItem).Where(x => listCopyOptions.Select(x => x.OldAsmHeaderId).Contains(x.AsmHeaderId) && x.IsActive == true).ToList();
                    var getLumpSumItems = getItems.Where(x => x.MasterItem.PlanSpecific == "T").ToList();
                    var getNonLumpItems = getItems.Where(x => x.MasterItem.PlanSpecific == "F").ToList();

                    //lump sum (plan specific) -- need to make copies of the master items, new master item phases, and new asm details 
                    List<AsmDetail> lumpSumItemsWhereMasterItemExists = new List<AsmDetail>();
                    foreach (var item in getLumpSumItems)
                    {
                        //check if the master item exists, so don't add it again
                        var findExists = _context.MasterItems.Where(x => x.MasterItemPhase.PhaseCode == $"{OptionToAdd.Plan.PlanNum}{item.AsmHeader.MasterOption.OptionCode}" && x.ItemNumber == item.MasterItem.ItemNumber && x.ItemDesc == item.MasterItem.ItemDesc && x.IsActive == true);
                        if (findExists.Any())
                        {
                            // getLumpSumItems.Remove(item);
                            item.MasterItemId = findExists.First().MasterItemId;//set the master item id to the one in the correct phase, if it existed
                            lumpSumItemsWhereMasterItemExists.Add(item);
                        }
                    }

                    getLumpSumItems = getLumpSumItems.Except(lumpSumItemsWhereMasterItemExists).ToList();

                    var distinctHeaderLumpItems = getLumpSumItems.Select(x => x.AsmHeader).Distinct().ToList();

                    var newMasterItemPhases = (from x in listCopyOptions
                                               join y in distinctHeaderLumpItems on x.OldAsmHeaderId equals y.AsmHeaderId
                                               select new MasterItemPhasis()
                                               {
                                                   PhaseCode = $"{OptionToAdd.Plan.PlanNum}{y.MasterOption.OptionCode}",//These  different now because of the new plan
                                                   PhaseDesc = y.AssemblyDesc,
                                                   PhaseNotes = y.AssemblyNotes,
                                                   AsmHeaderId = x.NewAsmHeaderId,
                                                   CreatedBy = User.Identity.Name.Split('@')[0],
                                                   //TODO: multiple items could share phase if in same option
                                               }).ToList();
                    //_context.MasterItemPhases.AddRange(newMasterItemPhases);
                    //await _context.SaveChangesAsync();

                    await _context.MasterItemPhases.BulkInsertAsync(newMasterItemPhases);

                    //TODO:   too slow
                    //master item needs to be inserted here
                    var joinedLumpData = from x in listCopyOptions
                                         join y in getLumpSumItems on x.OldAsmHeaderId equals y.AsmHeaderId
                                         join z in newMasterItemPhases on x.NewAsmHeaderId equals z.AsmHeaderId
                                         select new AsmDetail()
                                         {
                                             AsmHeaderId = x.NewAsmHeaderId,
                                             BomClassId = y.BomClassId,
                                             // MasterItemId = y.MasterItemId,
                                             Ordinality = y.Ordinality,
                                             Calculation = y.Calculation,
                                             CalculationCode = y.CalculationCode,
                                             Formula = y.Formula,
                                             UseItemFormula = y.UseItemFormula,
                                             Factor = y.Factor,
                                             CreatedBy = User.Identity.Name.Split('@')[0],
                                             IsActive = true,
                                             MasterItem = new MasterItem()
                                             {
                                                 ItemNumber = y.MasterItem.ItemNumber,//TODO: New item number??? from phase code plan code stuf
                                                 //ItemDesc = y.AsmHeader.AssemblyDesc,
                                                 ItemDesc = y.MasterItem.ItemDesc,
                                                 BomClassId = y.MasterItem.BomClassId,//everything else should copy from original master item
                                                 PlanSpecific = "T",
                                                 PeCategoryCode = y.MasterItem.PeCategoryCode,
                                                 PeUnitPrice = y.MasterItem.PeUnitPrice,
                                                 PeUnitPriceDtCg = y.MasterItem.PeUnitPriceDtCg,
                                                 CalcPercent = y.MasterItem.CalcPercent,
                                                 DeletedFromPe = y.MasterItem.DeletedFromPe,
                                                 ExcludeFromPo = y.MasterItem.ExcludeFromPo,
                                                 OrderUnit = y.MasterItem.OrderUnit,
                                                 TakeoffUnit = y.MasterItem.TakeoffUnit,
                                                 Taxable = y.MasterItem.Taxable,
                                                 CalcBasis = y.MasterItem.CalcBasis,
                                                 CnvFctr = y.MasterItem.CnvFctr,
                                                 CreatedBy = User.Identity.Name.Split('@')[0],
                                                 Formula = y.MasterItem.Formula,
                                                 ItemNotes = y.MasterItem.ItemNotes,
                                                 JcCategory = y.MasterItem.JcCategory,
                                                 JcPhase = y.MasterItem.JcPhase,
                                                 Multdiv = y.MasterItem.Multdiv,
                                                 MasterItemPhaseId = z.MasterItemPhaseId
                                                 //MasterItemPhase = new MasterItemPhasis()
                                                 //{
                                                 //    PhaseCode = $"{OptionToAdd.Plan.PlanNum}{y.AsmHeader.MasterOption.OptionCode}",//These  different now because of the new plan//TODO: not sure if this is all included
                                                 //    PhaseDesc = y.AsmHeader.AssemblyDesc,
                                                 //    PhaseNotes = y.AsmHeader.AssemblyNotes,
                                                 //    AsmHeaderId = x.NewAsmHeaderId,
                                                 //    CreatedBy = User.Identity.Name.Split('@')[0],
                                                 //    //TODO: multiple items could share phase if in same option
                                                 //},
                                             },

                                         };

                    await _context.AsmDetails.BulkInsertAsync(joinedLumpData, options => options.IncludeGraph = true);//slow?

                    //lump where master item already exists - just add asmdetail 
                    string createBy = User.Identity.Name.Split("@")[0];
                    var joinedDataLumpItemExists = from x in listCopyOptions
                                                   join y in lumpSumItemsWhereMasterItemExists on x.OldAsmHeaderId equals y.AsmHeaderId
                                                   select new AsmDetail()
                                                   {
                                                       AsmHeaderId = x.NewAsmHeaderId,
                                                       BomClassId = y.BomClassId,
                                                       MasterItemId = y.MasterItemId,//this needs to be the find item one, not the old one..
                                                       Ordinality = y.Ordinality,
                                                       Calculation = y.Calculation,
                                                       CalculationCode = y.CalculationCode,
                                                       Formula = y.Formula,
                                                       UseItemFormula = y.UseItemFormula,
                                                       Factor = y.Factor,
                                                       CreatedBy = createBy,
                                                       IsActive = true,
                                                       CreatedDateTime = DateTime.Now
                                                   };
                    await _context.AsmDetails.BulkInsertAsync(joinedDataLumpItemExists);//slow?

                    //non-lump
                    var joinedNonLumpData = from x in listCopyOptions
                                            join y in getNonLumpItems on x.OldAsmHeaderId equals y.AsmHeaderId
                                            select new AsmDetail()
                                            {
                                                AsmHeaderId = x.NewAsmHeaderId,
                                                BomClassId = y.BomClassId,
                                                MasterItemId = y.MasterItemId,
                                                Ordinality = y.Ordinality,
                                                Calculation = y.Calculation,
                                                CalculationCode = y.CalculationCode,
                                                Formula = y.Formula,
                                                Factor = y.Factor,
                                                UseItemFormula = y.UseItemFormula,
                                                CreatedBy = createBy,
                                                IsActive = true,
                                                CreatedDateTime = DateTime.Now
                                            };
                    await _context.AsmDetails.BulkInsertAsync(joinedNonLumpData);//slow?
                }
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<AddOptionToPlanModel>() { IsSuccess = false, Message = "failed to add options to master plan", Value = null });
            }


            return Ok(new ResponseModel<AddOptionToPlanModel>() { Value = responseModel, IsSuccess = true, Message = "Added option" });
        }

        [HttpPost]
        public async Task<IActionResult> AddOptionsToMasterPlanAndAdditionalAsync([FromBody] AddOptionToPlanModel OptionToAdd)
        {
            var responseModel = new AddOptionToPlanModel()
            {
                Plan = OptionToAdd.Plan,
                OptionsToAdd = new List<AsmHeaderModel>(),
                AdditionalPlans = OptionToAdd.AdditionalPlans
            };

            //for lump sum (PlanSpecific) items, add new master item, else just add new asmdetail
            try
            {
                var listCopyOptions = new List<CopyAsmHeaderModel>();

                foreach (var option in OptionToAdd.OptionsToAdd)
                {
                    var getOption = _context.AsmHeaders.Include("MasterOption").SingleOrDefault(x => x.AsmHeaderId == option.AsmHeaderId);//This is the option we are copying from

                    var getPlanNum = _context.MasterPlans.Where(x => x.MasterPlanId == responseModel.Plan.MasterPlanId).Select(x => x.PlanNum).FirstOrDefault();

                    var addOption = new AsmHeader()
                    {
                        MasterPlanId = OptionToAdd.Plan.MasterPlanId,
                        MasterOptionId = getOption.MasterOptionId,
                        HomeAreaId = option.HomeAreaId,
                        AssemblyDesc = getOption.AssemblyDesc,
                        AssemblyNotes = getOption.AssemblyNotes,
                        AsmGroupId = getOption.AsmGroupId,
                        AssemblySize = getOption.AssemblySize,
                        IsBaseHouse = getOption.IsBaseHouse,
                        IsElevation = getOption.IsElevation,
                        AssemblyCode = $"{getPlanNum}{getOption.MasterOption.OptionCode}",
                        CreatedBy = User.Identity.Name.Split('@')[0]
                    };

                    //Check if an existing Option with the same Option Code already exists
                    var findExisting = _context.AsmHeaders.Where(x => x.AssemblyCode == addOption.AssemblyCode && x.MasterPlanId == responseModel.Plan.MasterPlanId).FirstOrDefault();

                    if (findExisting != null)
                    {
                        //reactivate and update if it was inactive
                        if (findExisting.IsActive == false)
                        {
                            findExisting.IsActive = true;
                            findExisting.AssemblyDesc = getOption.AssemblyDesc;
                            findExisting.AssemblyNotes = getOption.AssemblyNotes;
                            findExisting.AsmGroupId = getOption.AsmGroupId;
                            findExisting.AssemblySize = getOption.AssemblySize;
                            findExisting.AssemblyUnit = getOption.AssemblyUnit;
                            findExisting.IsBaseHouse = getOption.IsBaseHouse;
                            findExisting.IsElevation = getOption.IsElevation;
                            findExisting.UpdatedBy = User.Identity.Name.Split('@')[0];
                            findExisting.UpdatedDateTime = DateTime.Now;
                            _context.AsmHeaders.Update(findExisting);
                            await _context.SaveChangesAsync();
                        }
                        // return Ok(new ResponseModel<AddOptionToPlanModel>() { Value = responseModel, IsSuccess = false, Message = "An option you're trying to add already exists" });
                    }
                    else
                    {
                        await _context.AsmHeaders.AddAsync(addOption);

                        //Add the option to available plan options to all subdivisions that have the plan
                        var findAllAvailablePlanOptions = _context.PhasePlans.Include("MasterPlan").Where(x => x.MasterPlanId == addOption.MasterPlanId && x.IsActive == true).ToList();

                        if (findAllAvailablePlanOptions.Any())
                        {
                            foreach (var availablePlanOption in findAllAvailablePlanOptions)
                            {
                                await _context.AvailablePlanOptions.AddAsync(new AvailablePlanOption
                                {
                                    PhasePlanId = availablePlanOption.PhasePlanId,
                                    MasterPlanId = availablePlanOption.MasterPlanId,
                                    HomeAreaId = addOption.HomeAreaId,
                                    SubdivisionId = availablePlanOption.SubdivisionId,
                                    MasterOptionId = addOption.MasterOptionId,
                                    CutoffStageId = 3, //Default?
                                    OptionGroupId = _context.MasterOptions.Where(x => x.OptionId == addOption.MasterOptionId).Select(x => x.OptionGroupId).FirstOrDefault(),
                                    OptionTypeId = 6, //Make Standard as Default?
                                    OptionCode = getOption.MasterOption.OptionCode,
                                    ModifiedOptionDesc = addOption.AssemblyDesc,
                                    OptionLongDesc = addOption.AssemblyDesc,
                                    MarginType = 0, //Default?
                                    MarginPercent = 71, //Default?
                                });
                            }
                        }

                        // Add the option and apply to other selected Plans
                        if (OptionToAdd.AdditionalPlans != null && OptionToAdd.AdditionalPlans.Any())
                        {
                            foreach (var plan in OptionToAdd.AdditionalPlans)
                            {
                                var planNum = _context.MasterPlans.Where(x => x.MasterPlanId == plan).Select(x => x.PlanNum).FirstOrDefault();

                                if (planNum != null)
                                {
                                    await _context.AsmHeaders.AddAsync(new AsmHeader
                                    {
                                        MasterPlanId = plan,
                                        MasterOptionId = getOption.MasterOptionId,
                                        HomeAreaId = option.HomeAreaId,
                                        AssemblyDesc = getOption.AssemblyDesc,
                                        AssemblyNotes = getOption.AssemblyNotes,
                                        AsmGroupId = getOption.AsmGroupId,
                                        AssemblySize = getOption.AssemblySize,
                                        IsBaseHouse = getOption.IsBaseHouse,
                                        IsElevation = getOption.IsElevation,
                                        AssemblyCode = $"{planNum}{getOption.MasterOption.OptionCode}",
                                        CreatedBy = User.Identity.Name.Split('@')[0]
                                    });

                                    var findAdditionalAvailablePlanOptions = _context.PhasePlans.Where(x => x.MasterPlanId == plan && x.IsActive == true).ToList();

                                    //Add the option to available plan options to all subdivisions that have the plan
                                    if (findAdditionalAvailablePlanOptions.Any())
                                    {
                                        foreach (var additionalPlan in findAdditionalAvailablePlanOptions)
                                        {
                                            await _context.AvailablePlanOptions.AddAsync(new AvailablePlanOption
                                            {
                                                PhasePlanId = additionalPlan.PhasePlanId,
                                                MasterPlanId = additionalPlan.MasterPlanId,
                                                HomeAreaId = option.HomeAreaId,
                                                SubdivisionId = additionalPlan.SubdivisionId,
                                                MasterOptionId = getOption.MasterOptionId,
                                                CutoffStageId = 3, //Default?
                                                OptionGroupId = _context.MasterOptions.Where(x => x.OptionId == addOption.MasterOptionId).Select(x => x.OptionGroupId).FirstOrDefault(),
                                                OptionTypeId = 6, //Make Standard as Default?
                                                OptionCode = getOption.MasterOption.OptionCode,
                                                OptionLongDesc = getOption.AssemblyDesc,
                                                ModifiedOptionDesc = getOption.AssemblyDesc,
                                                MarginType = 0, //Default?
                                                MarginPercent = 71, //Default?
                                            });
                                        }
                                    }
                                }
                            }
                        }

                        await _context.SaveChangesAsync();

                        var addCopyOption = new CopyAsmHeaderModel()
                        {
                            OldAsmHeaderId = option.AsmHeaderId,
                            NewAsmHeaderId = addOption.AsmHeaderId
                        };

                        listCopyOptions.Add(addCopyOption);
                    }
                }

                if (listCopyOptions.Count > 0)
                {
                    //add the items
                    var getItems = _context.AsmDetails.Include(x => x.AsmHeader.MasterOption).Include(x => x.MasterItem).Where(x => listCopyOptions.Select(x => x.OldAsmHeaderId).Contains(x.AsmHeaderId) && x.IsActive == true).ToList();
                    var getLumpSumItems = getItems.Where(x => x.MasterItem.PlanSpecific == "T").ToList();
                    var getNonLumpItems = getItems.Where(x => x.MasterItem.PlanSpecific == "F").ToList();

                    //lump sum (plan specific) -- need to make copies of the master items, new master item phases, and new asm details 
                    List<AsmDetail> lumpSumItemsWhereMasterItemExists = new List<AsmDetail>();

                    foreach (var item in getLumpSumItems)
                    {
                        //check if the master item exists, so don't add it again
                        var findExists = _context.MasterItems.Where(x => x.MasterItemPhase.PhaseCode == $"{OptionToAdd.Plan.PlanNum}{item.AsmHeader.MasterOption.OptionCode}" && x.ItemNumber == item.MasterItem.ItemNumber && x.ItemDesc == item.MasterItem.ItemDesc).Any();

                        if (findExists)
                        {
                            getLumpSumItems.Remove(item);
                            lumpSumItemsWhereMasterItemExists.Add(item);
                        }
                    }

                    var distinctHeaderLumpItems = getLumpSumItems.Select(x => x.AsmHeader).Distinct().ToList();

                    var newMasterItemPhases = (from x in listCopyOptions
                                               join y in distinctHeaderLumpItems on x.OldAsmHeaderId equals y.AsmHeaderId
                                               select new MasterItemPhasis()
                                               {
                                                   PhaseCode = $"{OptionToAdd.Plan.PlanNum}{y.MasterOption.OptionCode}",//These  different now because of the new plan//TODO: not sure if this is all included
                                                   PhaseDesc = y.AssemblyDesc,
                                                   PhaseNotes = y.AssemblyNotes,
                                                   AsmHeaderId = x.NewAsmHeaderId,
                                                   CreatedBy = User.Identity.Name.Split('@')[0],
                                                   //TODO: multiple items could share phase if in same option
                                               }).ToList();
                    _context.MasterItemPhases.AddRange(newMasterItemPhases);
                    await _context.SaveChangesAsync();

                    //TODO:   too slow
                    var joinedLumpData = from x in listCopyOptions
                                         join y in getLumpSumItems on x.OldAsmHeaderId equals y.AsmHeaderId
                                         join z in newMasterItemPhases on x.NewAsmHeaderId equals z.AsmHeaderId
                                         select new AsmDetail()
                                         {
                                             AsmHeaderId = x.NewAsmHeaderId,
                                             BomClassId = y.BomClassId,
                                             // MasterItemId = y.MasterItemId,
                                             Ordinality = y.Ordinality,
                                             Calculation = y.Calculation,
                                             CalculationCode = y.CalculationCode,
                                             Formula = y.Formula,
                                             Factor = y.Factor,
                                             UseItemFormula = y.UseItemFormula,
                                             CreatedBy = User.Identity.Name.Split('@')[0],
                                             IsActive = true,
                                             MasterItem = new MasterItem()
                                             {
                                                 ItemNumber = y.MasterItem.ItemNumber,//TODO: New item number??? from phase code plan code stuf
                                                 ItemDesc = y.AsmHeader.AssemblyDesc,
                                                 BomClassId = y.MasterItem.BomClassId,//everything else should copy from original master item
                                                 PlanSpecific = "T",
                                                 PeCategoryCode = y.MasterItem.PeCategoryCode,
                                                 PeUnitPrice = y.MasterItem.PeUnitPrice,
                                                 PeUnitPriceDtCg = y.MasterItem.PeUnitPriceDtCg,
                                                 CalcPercent = y.MasterItem.CalcPercent,
                                                 DeletedFromPe = y.MasterItem.DeletedFromPe,
                                                 ExcludeFromPo = y.MasterItem.ExcludeFromPo,
                                                 OrderUnit = y.MasterItem.OrderUnit,
                                                 TakeoffUnit = y.MasterItem.TakeoffUnit,
                                                 Taxable = y.MasterItem.Taxable,
                                                 CalcBasis = y.MasterItem.CalcBasis,
                                                 CnvFctr = y.MasterItem.CnvFctr,
                                                 CreatedBy = User.Identity.Name.Split('@')[0],
                                                 Formula = y.MasterItem.Formula,
                                                 ItemNotes = y.MasterItem.ItemNotes,
                                                 JcCategory = y.MasterItem.JcCategory,
                                                 JcPhase = y.MasterItem.JcPhase,
                                                 Multdiv = y.MasterItem.Multdiv,
                                                 MasterItemPhaseId = z.MasterItemPhaseId
                                                 //MasterItemPhase = new MasterItemPhasis()
                                                 //{
                                                 //    PhaseCode = $"{OptionToAdd.Plan.PlanNum}{y.AsmHeader.MasterOption.OptionCode}",//These  different now because of the new plan//TODO: not sure if this is all included
                                                 //    PhaseDesc = y.AsmHeader.AssemblyDesc,
                                                 //    PhaseNotes = y.AsmHeader.AssemblyNotes,
                                                 //    AsmHeaderId = x.NewAsmHeaderId,
                                                 //    CreatedBy = User.Identity.Name.Split('@')[0],
                                                 //    //TODO: multiple items could share phase if in same option
                                                 //},
                                             },
                                         };

                    _context.AsmDetails.AddRange(joinedLumpData);//slow?
                    await _context.SaveChangesAsync();

                    //lump where master item already exists - just add asmdetail 
                    string createBy = User.Identity.Name.Split("@")[0];

                    var joinedDataLumpItemExists = from x in listCopyOptions
                                                   join y in lumpSumItemsWhereMasterItemExists on x.OldAsmHeaderId equals y.AsmHeaderId
                                                   select new AsmDetail()
                                                   {
                                                       AsmHeaderId = x.NewAsmHeaderId,
                                                       BomClassId = y.BomClassId,
                                                       MasterItemId = y.MasterItemId,
                                                       Ordinality = y.Ordinality,
                                                       Calculation = y.Calculation,
                                                       CalculationCode = y.CalculationCode,
                                                       Formula = y.Formula,
                                                       Factor = y.Factor,
                                                       UseItemFormula = y.UseItemFormula,
                                                       CreatedBy = createBy,
                                                       IsActive = true,
                                                       CreatedDateTime = DateTime.Now
                                                   };

                    //non-lump
                    var joinedData = from x in listCopyOptions
                                     join y in getNonLumpItems on x.OldAsmHeaderId equals y.AsmHeaderId
                                     select new AsmDetail()
                                     {
                                         AsmHeaderId = x.NewAsmHeaderId,
                                         BomClassId = y.BomClassId,
                                         MasterItemId = y.MasterItemId,
                                         Ordinality = y.Ordinality,
                                         Calculation = y.Calculation,
                                         CalculationCode = y.CalculationCode,
                                         Formula = y.Formula,
                                         Factor = y.Factor,
                                         UseItemFormula = y.UseItemFormula,
                                         CreatedBy = createBy,
                                         IsActive = true,
                                         CreatedDateTime = DateTime.Now
                                     };

                    DataTable OptionsToAddDataTable = new DataTable("AsmDetails");
                    DataColumn AsmHeaderId = new DataColumn("AsmHeaderId", typeof(int));
                    OptionsToAddDataTable.Columns.Add(AsmHeaderId);
                    DataColumn MasterItemId = new DataColumn("MasterItemId", typeof(int));
                    OptionsToAddDataTable.Columns.Add(MasterItemId);
                    DataColumn BomClassId = new DataColumn("BomClassId", typeof(int));
                    OptionsToAddDataTable.Columns.Add(BomClassId);
                    DataColumn Ordinality = new DataColumn("Ordinality", typeof(int));
                    OptionsToAddDataTable.Columns.Add(Ordinality);
                    DataColumn Calculation = new DataColumn("Calculation", typeof(string));
                    OptionsToAddDataTable.Columns.Add(Calculation);
                    DataColumn CalculationCode = new DataColumn("CalculationCode", typeof(int));
                    OptionsToAddDataTable.Columns.Add(CalculationCode);
                    DataColumn Formula = new DataColumn("Formula", typeof(string));
                    OptionsToAddDataTable.Columns.Add(Formula);
                    DataColumn UseItemFormula = new DataColumn("UseItemFormula", typeof(string));
                    OptionsToAddDataTable.Columns.Add(UseItemFormula);
                    DataColumn Factor = new DataColumn("Factor", typeof(double));
                    OptionsToAddDataTable.Columns.Add(Factor);
                    DataColumn CreatedBy = new DataColumn("CreatedBy");
                    OptionsToAddDataTable.Columns.Add(CreatedBy);

                    foreach (var itemToAdd in joinedData)
                    {
                        OptionsToAddDataTable.Rows.Add(itemToAdd.AsmHeaderId, itemToAdd.MasterItemId, itemToAdd.BomClassId, itemToAdd.Ordinality, itemToAdd.Calculation, itemToAdd.CalculationCode, itemToAdd.Formula, itemToAdd.UseItemFormula, itemToAdd.Factor ?? 0, createBy);
                    }

                    foreach (var itemToAdd in joinedDataLumpItemExists)
                    {
                        OptionsToAddDataTable.Rows.Add(itemToAdd.AsmHeaderId, itemToAdd.MasterItemId, itemToAdd.BomClassId, itemToAdd.Ordinality, itemToAdd.Calculation, itemToAdd.CalculationCode, itemToAdd.Formula, itemToAdd.UseItemFormula, itemToAdd.Factor ?? 0, createBy);
                    }

                    var conn = _configuration.GetConnectionString("ERPConnection");
                    using (var connection = new SqlConnection(conn))
                    {
                        connection.Open();
                        using (var bulkCopy = new SqlBulkCopy(connection))
                        {
                            bulkCopy.DestinationTableName = "ASM_DETAIL";
                            bulkCopy.ColumnMappings.Add("AsmHeaderId", "ASM_HEADER_ID");
                            bulkCopy.ColumnMappings.Add("MasterItemId", "MASTER_ITEM_ID");
                            bulkCopy.ColumnMappings.Add("BomClassId", "BOM_CLASS_ID");
                            bulkCopy.ColumnMappings.Add("Ordinality", "ORDINALITY");
                            bulkCopy.ColumnMappings.Add("Calculation", "CALCULATION");
                            bulkCopy.ColumnMappings.Add("CalculationCode", "CALCULATION_CODE");
                            bulkCopy.ColumnMappings.Add("Formula", "FORMULA");
                            bulkCopy.ColumnMappings.Add("UseItemFormula", "USE_ITEM_FORMULA");
                            bulkCopy.ColumnMappings.Add("Factor", "FACTOR");
                            bulkCopy.ColumnMappings.Add("CreatedBy", "CreatedBy");

                            try
                            {
                                bulkCopy.WriteToServer(OptionsToAddDataTable);
                            }
                            catch (Exception ex)
                            {
                                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                                return Ok(new ResponseModel<AddOptionToPlanModel>() { Value = responseModel, IsSuccess = false, Message = "Failed to add option" });
                            }
                        }
                    }
                }
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<AddOptionToPlanModel>() { IsSuccess = false, Message = "failed to add options to master plan", Value = null });
            }


            return Ok(new ResponseModel<AddOptionToPlanModel>() { Value = responseModel, IsSuccess = true, Message = "Added option" });
        }

        #region Tree
        [HttpGet]
        public async Task<IActionResult> GetTreeItemsAsync()
        {
            try
            {
                var items = new List<TreeListOptionGroup>();

                var optionGroups = await _context.OptionGroups.Where(x => x.OptionGroupId == 1).Select(x => x).ToListAsync();
                //var masterOptions = await _context.MasterOptions.Where(x => x.OptionGroupId == 1 && x.OptionId == 15749).Select(x => x).ToListAsync();
                //var asmHeaders = await _context.AsmHeaders.Where(x => x.MasterPlanId == 1 && x.MasterOptionId == 15749).Select(x => x).ToListAsync();

                foreach (var a in optionGroups)
                {
                    var treeItem = new TreeListOptionGroup
                    {
                        Id = a.OptionGroupId,
                        Code = null,
                        Description = a.OptionGroupName,
                        HasChildren = true,
                        ParentId = null
                    };

                    items.Add(treeItem);
                }

                return Ok(new ResponseModel<List<TreeListOptionGroup>>() { Value = items, IsSuccess = true });
            }
            catch (Exception ex)
            {
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif
                return StatusCode(500, new ResponseModel<List<TreeListOptionGroup>>() { IsSuccess = false, Message = "failed to get tree items", Value = null });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetTreeChildItemsAsync()
        {
            try
            {
                var items = new List<TreeListOptionGroup>();

                var masterOptions = await _context.MasterOptions.Where(x => x.OptionGroupId == 1 && x.OptionId == 15749).Select(x => x).ToListAsync();

                //var asmHeaders = await _context.AsmHeaders.Where(x => x.MasterPlanId == 1 && x.MasterOptionId == 15749).Select(x => x).ToListAsync();

                // Hardcoded values for proof-of-concept of multi-level
                var masterItemIds = new[] { 404563, 404564 };
                var masterItems = await _context.MasterItems.Where(x => masterItemIds.Contains(x.MasterItemId)).Select(x => x).ToListAsync();

                foreach (var a in masterOptions)
                {
                    var treeItem = new TreeListOptionGroup
                    {
                        Id = a.OptionId,
                        Code = a.OptionCode,
                        Description = a.OptionDesc,
                        HasChildren = true,
                        ParentId = 1
                    };

                    if (masterItems.Count > 0)
                    {
                        foreach (var b in masterItems)
                        {
                            treeItem.DirectReports.Add(new TreeListOptionGroup
                            {
                                Id = b.MasterItemId,
                                Code = null,
                                Description = b.ItemDesc,
                                HasChildren = false,
                                ParentId = 160390
                            });
                        }
                    }

                    items.Add(treeItem);
                }

                return Ok(new ResponseModel<List<TreeListOptionGroup>>() { Value = items, IsSuccess = true });
            }
            catch (Exception ex)
            {
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif
                return StatusCode(500, new ResponseModel<List<TreeListOptionGroup>>() { IsSuccess = false, Message = "failed to get tree child items", Value = null });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetTreeOptionGroupsAsync()
        {
            try
            {
                var optionGroups = new List<TreeOptionGroup>();

                var getOptionGroups = await _context.OptionGroups.Where(x => x.IsActive == true && x.OptionGroupId == 1).ToListAsync();

                foreach (var o in getOptionGroups)
                {
                    var optionGroup = new TreeOptionGroup
                    {
                        OptionGroupId = o.OptionGroupId,
                        OptionGroupName = o.OptionGroupName,
                    };

                    var getMasterOptions = await _context.MasterOptions.Where(x => x.IsActive == true && x.OptionGroupId == o.OptionGroupId && x.OptionId == 1).ToListAsync();

                    foreach (var m in getMasterOptions)
                    {
                        var buildTreeMasterOption = new TreeMasterOption
                        {
                            OptionId = m.OptionId,
                            OptionCode = m.OptionCode,
                            OptionDesc = m.OptionDesc
                        };

                        optionGroup.MasterOptions.Add(buildTreeMasterOption);

                        var getAsmHeaders = await _context.AsmHeaders.Where(x => x.IsActive == true && x.MasterOptionId == m.OptionId && x.MasterOptionId == 1).ToListAsync();

                        foreach (var a in getAsmHeaders)
                        {
                            buildTreeMasterOption.AsmHeaders.Add(new TreeAsmHeader
                            {
                                AsmHeaderId = a.AsmHeaderId,
                                AssemblyCode = a.AssemblyCode,
                                AssemblyDesc = a.AssemblyDesc
                            });
                        }
                    }

                    optionGroups.Add(optionGroup);
                }

                return Ok(new ResponseModel<List<TreeOptionGroup>>() { Value = optionGroups, IsSuccess = true });
            }
            catch (Exception ex)
            {
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif
                return StatusCode(500, new ResponseModel<List<TreeOptionGroup>>() { IsSuccess = false, Message = "failed to get tree option groups", Value = null });
            }
        }

        /// <summary>
        /// Gets option types.
        /// </summary>
        /// <returns>List of option types.</returns>
        #endregion
        [HttpGet]
        public async Task<IActionResult> GetOptionTypesAsync()
        {
            var optionTypesDto = new List<OptionTypeDto>();
            try
            {
                var optionTypes = await _context.OptionTypes.Where(x => x.IsActive == true).ToListAsync();
                optionTypesDto = _mapper.Map<List<OptionTypeDto>>(optionTypes);
                return Ok(optionTypesDto);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, optionTypesDto);
            }
        }

        /// <summary>
        /// Option with Groups attached to it
        /// </summary>
        /// <param name="optionId"></param>
        /// <returns></returns>
        [HttpGet("{optionId}")]
        public async Task<IActionResult> GetOptionWithGroupsAsync(int optionId)
        {
            try
            {
                var options = new List<AttrGroupAssignmentDto>();
                var groups = new List<MasterAttributeGroupDto>();

                var connString = _configuration.GetConnectionString("ERPConnection");

                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    var query = "SELECT a.[ATTR_GROUP_ASSIGNMENT_ID] FROM [dbo].[MasterOptionAttributeItem] a WHERE a.MASTER_OPTION_ID = @optionId AND a.IsActive = 1";

                    var command = new SqlCommand(query, connection);
                    command.Parameters.AddWithValue("@optionId", optionId);

                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        if (reader.GetValue(0) != DBNull.Value)
                        {
                            options.Add(new AttrGroupAssignmentDto
                            {
                                AttrGroupAssignmentId = Convert.ToInt32(reader.GetValue(0).ToString())
                            });
                        }
                    }
                }

                if (options.Any())
                {
                    foreach (var option in options)
                    {
                        var getGroupId = await _context.AttrGroupAssignments.Where(x => x.AttrGroupAssignmentId == option.AttrGroupAssignmentId && x.IsActive == true).Select(x => x.AttributeGroupId).FirstOrDefaultAsync();

                        if (getGroupId != 0)
                        {
                            var getGroup = await _context.MasterAttributeGroups.Where(x => x.AttributeGroupId == getGroupId).FirstOrDefaultAsync();

                            if (getGroup != null)
                            {
                                var checkExists = groups.Exists(x => x.AttributeGroupId == getGroup.AttributeGroupId);

                                if (!checkExists)
                                {
                                    groups.Add(new MasterAttributeGroupDto
                                    {
                                        AttributeGroupId = getGroup.AttributeGroupId,
                                        Description = getGroup.Description,
                                        IsActive = getGroup.IsActive,
                                        AttributeGroupAssignmentId = option.AttrGroupAssignmentId,
                                        MasterOptionId = optionId,
                                        IsMaster = true
                                    });
                                }
                            }
                        }
                    }
                }

                return Ok(new ResponseModel<List<MasterAttributeGroupDto>>() { Value = groups, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MasterAttributeGroupDto>>() { IsSuccess = false, Message = "failed to get master options by group", Value = null });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetMasterAttributeGroupsAsync()
        {
            try
            {
                var groups = await _context.MasterAttributeGroups.Where(x => x.IsActive == true).ToListAsync();
                return Ok(new ResponseModel<List<MasterAttributeGroupDto>>() { Value = _mapper.Map<List<MasterAttributeGroupDto>>(groups), IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MasterAttributeGroupDto>>() { IsSuccess = false, Message = "failed to get master attribute groups", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddMasterOptionAttributeItemAsync([FromBody] List<MasterOptionAttributeItemDto> items)
        {
            try
            {
                if (items.Any())
                {
                    foreach (var group in items)
                    {
                        var getAttributeAssignments = await _context.AttrGroupAssignments.Where(x => x.AttributeGroupId == group.AttributeGroupId && x.IsActive == true).ToListAsync();

                        if (getAttributeAssignments.Any())
                        {
                            foreach (var getAttributeAssignment in getAttributeAssignments)
                            {
                                var checkExist = await _context.MasterOptionAttributeItems.Where(x => x.AttrGroupAssignmentId == getAttributeAssignment.AttrGroupAssignmentId && x.MasterOptionId == group.MasterOptionId).FirstOrDefaultAsync();

                                if (checkExist == null)
                                {
                                    _context.MasterOptionAttributeItems.Add(new MasterOptionAttributeItem
                                    {
                                        MasterOptionId = group.MasterOptionId,
                                        AttrGroupAssignmentId = getAttributeAssignment.AttrGroupAssignmentId,
                                        IsActive = true,
                                        CreatedBy = User.Identity.Name.Split('@')[0],
                                        CreatedDateTime = DateTime.Now
                                    });

                                    await _context.SaveChangesAsync();
                                }
                                else if (checkExist != null && checkExist.IsActive == false)
                                {
                                    // Reactivate
                                    checkExist.IsActive = true;
                                    checkExist.UpdatedDateTime = DateTime.Now;
                                    checkExist.UpdatedBy = User.Identity.Name.Split('@')[0];

                                    _context.MasterOptionAttributeItems.Update(checkExist);

                                    await _context.SaveChangesAsync();
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MasterOptionAttributeItemDto>>() { IsSuccess = false, Message = "failed to add groups", Value = null });
            }

            return Ok(new ResponseModel<List<MasterOptionAttributeItemDto>>() { Value = items, IsSuccess = true, Message = "Groups added" });
        }

        [HttpPut]
        public async Task<IActionResult> ReactivateMasterOptionAttributeItemAsync([FromBody] MasterAttributeGroupDto model)
        {
            try
            {
                var findMasterOptionAttributeItem = await _context.MasterOptionAttributeItems.Where(x => x.MasterOptionId == model.MasterOptionId && x.AttrGroupAssignmentId == model.AttributeGroupAssignmentId && x.IsActive == false).FirstOrDefaultAsync();

                if (findMasterOptionAttributeItem != null)
                {
                    findMasterOptionAttributeItem.IsActive = true;
                    findMasterOptionAttributeItem.UpdatedDateTime = DateTime.Now;
                    findMasterOptionAttributeItem.UpdatedBy = User.Identity.Name.Split('@')[0];

                    _context.MasterOptionAttributeItems.Update(findMasterOptionAttributeItem);

                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MasterAttributeGroupDto>() { IsSuccess = false, Message = "failed to reactivate group", Value = null });
            }

            return Ok(new ResponseModel<MasterAttributeGroupDto>() { Value = model, IsSuccess = true, Message = "Group is reactivated" });
        }

        [HttpPut]
        public async Task<IActionResult> DeleteMasterOptionAttributeItemAsync([FromBody] MasterAttributeGroupDto model)
        {
            try
            {
                // Find all group assignments based on the group id
                var findAttrGroupAssignments = await _context.AttrGroupAssignments.Where(x => x.AttributeGroupId == model.AttributeGroupId && x.IsActive == true).ToListAsync();

                if (findAttrGroupAssignments.Any())
                {
                    foreach (var group in findAttrGroupAssignments)
                    {
                        var findMasterOptionAttributeItem = await _context.MasterOptionAttributeItems.Where(x => x.MasterOptionId == model.MasterOptionId && x.AttrGroupAssignmentId == group.AttrGroupAssignmentId && x.IsActive == true).FirstOrDefaultAsync();

                        if (findMasterOptionAttributeItem != null)
                        {
                            findMasterOptionAttributeItem.IsActive = false;
                            findMasterOptionAttributeItem.UpdatedDateTime = DateTime.Now;
                            findMasterOptionAttributeItem.UpdatedBy = User.Identity.Name.Split('@')[0];

                            _context.MasterOptionAttributeItems.Update(findMasterOptionAttributeItem);

                            await _context.SaveChangesAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MasterAttributeGroupDto>() { IsSuccess = false, Message = "failed to deactivate group", Value = null });
            }

            return Ok(new ResponseModel<MasterAttributeGroupDto>() { Value = model, IsSuccess = true, Message = "Group is deactivated" });
        }
    }
}
