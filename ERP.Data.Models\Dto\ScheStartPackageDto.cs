﻿using ERP.Data.Models.Abstract;

namespace ERP.Data.Models;

public class ScheStartPackageDto : IMapFrom<ScheStartPackage>
{
    public int PackageId { get; set; }

    public string PackageName { get; set; } = null!;

    public int SubdivisionId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }
}
