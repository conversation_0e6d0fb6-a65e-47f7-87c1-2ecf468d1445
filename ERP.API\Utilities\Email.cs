﻿using DocumentFormat.OpenXml.Spreadsheet;
using ERP.Data.Models.Dto;
using Newtonsoft.Json;
using NLog;
using NLog.Fluent;
using SendGrid;
using SendGrid.Helpers.Mail;
using System.Net.Mail;

namespace ERP.API.Utilities
{
    public class Email
    {
        private static Logger _logger = LogManager.GetLogger("serviceLogger");
        private IConfiguration _configuration;

        public Email(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public void SendEmail(string subject, string body, List<string> sendTo, bool internalMessage = true, string replyTo = null, List<FileModel>? attachments = null)
        {
            try
            {
                internalMessage = true;//NoReply is not working using only vanmetrics for now
                string conn = internalMessage ? _configuration.GetConnectionString("EmailConnection") : _configuration.GetConnectionString("NoReplyEmaiConnection");

                var username = conn.Split(';')[0];
                var password = conn.Split(';')[1];
                              
                using var message = new MailMessage();
                foreach (var email in sendTo)
                {
                    message.To.Add(email);
                }
                message.From = new MailAddress(username);
                message.Subject = subject;
                message.Body = body;
                message.IsBodyHtml = true;
                if(replyTo != null)
                {
                    message.ReplyToList.Add(new MailAddress(replyTo, "reply-to"));
                }
                if (attachments != null)
                {
                    foreach (var file in attachments)
                    {
                        using var ms = new MemoryStream(file.FileData);
                        System.Net.Mail.Attachment attachment = new System.Net.Mail.Attachment(new MemoryStream(file.FileData), file.FileName);
                        message.Attachments.Add(attachment);
                    }
                }

                using var client = new SmtpClient();
                client.Host = "smtp.office365.com";
                client.UseDefaultCredentials = false;
                client.Credentials = new System.Net.NetworkCredential(username, password, "vanmetreco.com");
                //client.Credentials = new System.Net.NetworkCredential(username, password, "vanmetreco.com");
                client.Port = 587;
                client.EnableSsl = true;
                client.Send(message);
            }
            catch (Exception ex)
            {
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif
                try
                {
                    //Try gmail instead
                    var conn2 = _configuration.GetConnectionString("GmailConnection");

                    var username2 = conn2.Split(';')[0];
                    var password2 = conn2.Split(';')[1];

                    using var message = new MailMessage();

                    foreach (var email in sendTo)
                    {
                        message.To.Add(email);
                    }
                    message.From = new MailAddress(username2);
                    message.Subject = subject;
                    message.Body = body;
                    message.IsBodyHtml = true;

                    if (attachments != null)
                    {
                        foreach (var file in attachments)
                        {
                            using var ms = new MemoryStream(file.FileData);
                            System.Net.Mail.Attachment attachment = new System.Net.Mail.Attachment(new MemoryStream(file.FileData), file.FileName);
                            message.Attachments.Add(attachment);
                        }
                    }

                    using var client = new SmtpClient();

                    client.Host = "smtp.gmail.com";
                    client.Credentials = new System.Net.NetworkCredential(username2, password2);
                    client.Port = 587;
                    client.EnableSsl = true;
                    client.Send(message);
                }
                catch (Exception exception)
                {
#if DEBUG
                    _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif
                }
                throw; //re-throw so error can be handled higher up (e.g. show error message to user)
            }
        }
        public async Task SendGridSendEmail(List<SendGridEmailModel> sendMessages, IEnumerable<FileModel>? files = null)
        {
            try
            {
                var apiKey = _configuration.GetSection("SendGrid:ApiKey").Value;
                var client = new SendGridClient(apiKey);
                var uniqueArgs = new Dictionary<string, string>()
                {
                    { "category", "supplier message" },
                    { "subject", "supplier message" }
                };

                var personalizationsList = new List<Personalization>();

                using var message = new MailMessage();
                foreach (var email in sendMessages)
                {
                    //var activities = "newstring";
                    //var updateActivities = 
                    var jsonActivitiesItem = JsonConvert.SerializeObject(email.Activities);
                    personalizationsList.Add(new Personalization
                    {
                        Tos = new List<EmailAddress>
                        {
                            new EmailAddress(email.Email),//TODO: fill in name as well
                        },
                        CustomArgs = uniqueArgs,
                        TemplateData = email,
                    });
                }

                var msg = new SendGridMessage()
                {
                    TemplateId = "d-312fe6b9b6ea46408ff70ec9ae1a4a14",//This is the template for "Supplier Message"
                                                                      // From = new EmailAddress("<EMAIL>", "VanMetrics"),
                    From = new EmailAddress("<EMAIL>", "Van Metre Homes"),
                    Subject = "Supplier message",//Actual subject sent from personalization list
                    // PlainTextContent = body,//todo: plain text content
                    // HtmlContent = body,//content not needed when using template
                    Personalizations = personalizationsList
                };
                if(files != null)
                {
                    foreach (var file in files)
                    {

                        // Attachment attachment = new Attachment(file.InputStream, file.FileName); //original file names
                        SendGrid.Helpers.Mail.Attachment attachment = new SendGrid.Helpers.Mail.Attachment();
                        using var ms = new MemoryStream(file.FileData);
                        // file.File.InputStream.CopyTo(ms);
                        string base64 = Convert.ToBase64String(ms.ToArray());
                        attachment.Content = base64;
                        // attachment.Type = file.File.ContentType;//TODO: I think will allways be application/pdf
                        attachment.Filename = file.FileName;
                        msg.AddAttachment(attachment);
                    }
                }
                try
                {
                    var response = await client.SendEmailAsync(msg).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
#if DEBUG
                    _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif

                    throw;
                }
            }
            catch (Exception ex)
            {
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif

                throw; //re-throw so error can be handled higher up (e.g. show error message to user)
            }
        }



        public async Task SendMultipleEmail(List<SendGridEmailModel> sendMessages)
        {
            try
            {
                var apiKey = _configuration.GetSection("SendGrid:ApiKey").Value;
                var client = new SendGridClient(apiKey);
                var uniqueArgs = new Dictionary<string, string>()
                {
                    { "category", "supplier schedule" },
                    { "subject", "supplier schedule" }
                };

                var personalizationsList = new List<Personalization>();

                using var message = new MailMessage();
                foreach (var email in sendMessages.Where(x => !string.IsNullOrWhiteSpace(x.Email)))
                {
                    //var activities = "newstring";
                    //var updateActivities = 
                    var jsonActivitiesItem = JsonConvert.SerializeObject(email.Activities);
                    personalizationsList.Add(new Personalization
                    {
                        Tos = new List<EmailAddress>
                        {
                            new EmailAddress(email.Email),//TODO: fill in name as well
                        },
                        CustomArgs = uniqueArgs,
                        TemplateData = email,
                        //TemplateData = new Dictionary<string, string>
                        //{
                        //    { "Activity", "Test activity" },
                        //    { "MessageBody", email.Body },
                        //    { "MessageSubject", email.Subject },
                        //    { "Activities", jsonActivitiesItem }
                        //}
                    });
                }

                var msg = new SendGridMessage()
                {
                    TemplateId = "d-312fe6b9b6ea46408ff70ec9ae1a4a14",//This is the template for "Supplier Schedule"
                   // From = new EmailAddress("<EMAIL>", "VanMetrics"),
                    From = new EmailAddress("<EMAIL>", "Van Metre Homes"),
                    Subject = "Scheduled activity update",//Actual subject sent from personalization list
                    // PlainTextContent = body,//todo: plain text content
                    // HtmlContent = body,//content not needed when using template
                    Personalizations = personalizationsList
                };

                try
                {
                    var response = await client.SendEmailAsync(msg).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
#if DEBUG
                    _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif

                    throw;
                }
            }
            catch (Exception ex)
            {
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif
                
                throw; //re-throw so error can be handled higher up (e.g. show error message to user)
            }
        }
        public async Task SendPOMultipleEmail(List<SendGridEmailModel> sendMessages, IEnumerable<FileModel>? files, IWebHostEnvironment env)
        {
            try
            {
                var apiKey = _configuration.GetSection("SendGrid:ApiKey").Value;
                var client = new SendGridClient(apiKey);
                var uniqueArgs = new Dictionary<string, string>()
                {
                    { "category", "purchase order" },
                    { "subject", "purchase order" }
                };

                var personalizationsList = new List<Personalization>();

                using var message = new MailMessage();
                foreach (var email in sendMessages.Where(x => !string.IsNullOrWhiteSpace(x.Email)))
                {
                    //var activities = "newstring";
                    //var updateActivities = 
                    var jsonActivitiesItem = JsonConvert.SerializeObject(email.Activities);
                    personalizationsList.Add(new Personalization
                    {
                        Tos = new List<EmailAddress>
                        {
                            new EmailAddress(email.Email),//TODO: fill in name as well
                        },
                        CustomArgs = uniqueArgs,
                        TemplateData = email,
                    });
                }

                var templateId = string.Empty;
                if (env.IsDevelopment())
                {
                    templateId = "d-c027253c7f8540a39e0e5de82a485b18";
                }
                else
                {
                    templateId = "d-bd749b48ee524e29b82693bc86efea19";
                }

                var msg = new SendGridMessage()
                {
                    TemplateId = templateId,//This is the template for "PO"
                    //From = new EmailAddress("<EMAIL>", "VanMetrics"),
                    From = new EmailAddress("<EMAIL>", "Van Metre Homes"),
                    Subject = "Purchase Order issued",//Actual subject sent from personalization list
                    // PlainTextContent = body,//todo: plain text content
                    // HtmlContent = body,//content not needed when using template
                    Personalizations = personalizationsList
                };
                if(files != null)
                {
                    foreach (var file in files)
                    {

                        // Attachment attachment = new Attachment(file.InputStream, file.FileName); //original file names
                        SendGrid.Helpers.Mail.Attachment attachment = new SendGrid.Helpers.Mail.Attachment();
                        using var ms = new MemoryStream(file.FileData);
                        // file.File.InputStream.CopyTo(ms);
                        string base64 = Convert.ToBase64String(ms.ToArray());
                        attachment.Content = base64;
                        // attachment.Type = file.File.ContentType;//TODO: I think will allways be application/pdf
                        attachment.Filename = file.FileName;
                        msg.AddAttachment(attachment);
                    }
                }
                try
                {
                    var response = await client.SendEmailAsync(msg).ConfigureAwait(false);
                    if (!response.IsSuccessStatusCode)
                    {
#if DEBUG
                        _logger.Debug(response.ToString());
#else
                _logger.Error(response.ToString());
#endif
                    }
                }
                catch (Exception ex)
                {
#if DEBUG
                    _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif

                    throw;
                }
            }
            catch (Exception ex)
            {
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif

                throw; //re-throw so error can be handled higher up (e.g. show error message to user)
            }
        }
    }
}
