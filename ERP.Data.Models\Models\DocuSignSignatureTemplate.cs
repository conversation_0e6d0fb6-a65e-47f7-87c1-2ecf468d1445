﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class DocuSignSignatureTemplate
{
    public int DocuSignSignatureTemplateId { get; set; }

    public int? CommunityId { get; set; }

    public int? TemplateTypeId { get; set; }

    public bool IsActive { get; set; }

    public string Name { get; set; } = null!;

    public string DocuSignId { get; set; } = null!;

    public virtual Subdivision? Community { get; set; }

    public virtual ICollection<Envelope> Envelopes { get; set; } = new List<Envelope>();

    public virtual TemplateType? TemplateType { get; set; }
}
