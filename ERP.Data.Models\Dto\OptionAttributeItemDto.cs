﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class OptionAttributeItemDto : IMapFrom<OptionAttributeItem>
{
    public int OptAttrItemId { get; set; }

    public int? AttrGroupItemId { get; set; }

    public int? PlanOptionId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }  

    public bool? IsActive { get; set; }

    public decimal? Price { get; set; }

    public string? PriceApprovedBy { get; set; }

    public DateTime? PriceApprovedDate { get; set; }

    public  AttributeGroupItemDto? AttrGroupItem { get; set; }

   // public virtual AvailablePlanOption? PlanOption { get; set; }
}
