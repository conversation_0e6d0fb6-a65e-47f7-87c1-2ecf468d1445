﻿@using ERP.Data.Models;
@inject PlanService PlanService
<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }
    /*.mytreeclass .k-drag-cell {
            display:none
        }*/
    .k-drag-clue {
       /* background-color: rgb(244,97,71);*/
        color: white;
    }
    .k-icon.k-icon-with-modifier{
        display:none
    }
</style>
<TelerikGrid Data="@PlanData"
             Width="100%"
             SelectionMode="@GridSelectionMode.Multiple"
             RowDraggable="true"
             OnRowDrop="@((GridRowDropEventArgs<WorksheetTreeModel> args) => OnGridRowDropHandler(args))">
    <DetailTemplate Context="planContext">
       <NestedOptionGrid PhasePlanId="planContext.PlanId" HandleDrop=@HandleDrop></NestedOptionGrid>
    </DetailTemplate>
    <GridColumns>
        <GridColumn Field="PlanId" Visible="false"></GridColumn>
        <GridColumn Field="PlanName" Title="Plan"></GridColumn>
    </GridColumns>
</TelerikGrid>


@code {

    [Parameter]
    public EventCallback<GridRowDropEventArgs<WorksheetTreeModel>> HandleDrop { get; set; }

    [Parameter]
    public int? SubdivisionId { get; set; }

    public List<WorksheetTreeModel>? PlanData;

    private async Task OnGridRowDropHandler(GridRowDropEventArgs<WorksheetTreeModel> args)
    {
        await HandleDrop.InvokeAsync(args);
    }

    protected override async Task OnParametersSetAsync()
    {
        //get plans in subdivision
        var getPlanData = await PlanService.GetPhasePlansAsync((int)SubdivisionId);
        PlanData = getPlanData.Value.Select(x => new WorksheetTreeModel() // not null as returning empty list in case of failure
            {
                Id = Guid.NewGuid(),
                SubdivisionId = SubdivisionId,
                PlanId = x.PhasePlanId,
                PlanName = x.MasterPlan.PlanName,
                HasChildren = true
            }).ToList(); 
    }
    

}
