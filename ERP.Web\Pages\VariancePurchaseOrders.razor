﻿@page "/variancepurchaseorders"
@using ERP.Data.Models
@using ERP.Web.DocumentProcessing
@using Telerik.DataSource.Extensions
@inject PoService PoService
@inject BudgetService BudgetService
@inject SubdivisionService SubdivisionService
@inject JobService JobService
@inject VPOItemPickService VPOItemPickService
@implements IDisposable
@inject AuthenticationStateProvider _authenticationStateProvider
@inject IJSRuntime JS
@inject HttpClient Http
<style type="text/css">
    /* Gutter */
    .row > * {
    padding-right: calc(var(--bs-gutter-x) * .10);
    padding-left: calc(var(--bs-gutter-x) * .10);
    }

    .button-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    justify-content: left;
    }

    .custom-file-select .k-file-validation-message {
    color: red;
    font-weight: bold;
    font-size: 0.95rem;
    padding: 4px 0;
    }


    .d-none {
        display: none !important;
    }
</style>

<PageTitle>Variance Purchase Orders</PageTitle>

<div class="container-fluid flex">
    <div class="col-lg-12">
        <div class="card" style="background-color: #2e5771">
            <div class="card-body" style="padding:0.5rem">
                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Variance Purchase Orders</h7>
            </div>
        </div>
    </div>

    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item active">Variance Purchase Orders</li>
    </ol>

    <TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
    <TelerikTooltip TargetSelector=".tooltip-target" />

    <div class="row d-flex">
        <div class="col-lg-12">
            @if (IsLoadingOptions)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingOptions />
            }
            else
            {
                <TelerikGrid @ref="@PoGridRef"
                EditMode="@GridEditMode.Popup"
                Pageable="true"
                PageSize="20"
                OnRead="@ReadItems"
                TItem="@PoheaderDto"
                Groupable="true"
                Sortable="true"
                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu">
                    <GridToolBarTemplate>
                        <span title="Approved VPOs are uneditable" class="tooltip-target">
                            &nbsp;
                            <TelerikFontIcon Size="lg" Icon="@FontIcon.WarningCircle"></TelerikFontIcon>
                        </span>
                        <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add VPO</GridCommandButton>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                        <label for="SubdivisionFilter">Subdivision:</label>
                        <TelerikDropDownList Id="SubdivisionFilter"
                        Data="@AllSubdivisions"
                        TextField="SubdivisionName"
                        ValueField="SubdivisionName"
                        Filterable="true"
                        FilterOperator="StringFilterOperator.Contains"
                        DefaultText="All"
                        @bind-Value="@SelectedSubdivision" OnChange="OnSubdivisionChanged" Width="200px" />
                        <label for="JobByFilter">Job:</label>
                        <TelerikDropDownList Id="JobFilter"
                        Data="@Jobs"
                        TextField="JobNumber"
                        ValueField="JobNumber"
                        Filterable="true"
                        FilterOperator="StringFilterOperator.Contains"
                        DefaultText="All"
                        @bind-Value="@SelectedJob" Width="200px" OnChange="ApplyFilters" />
                        <label for="StatusFilter">Status:</label>
                        <TelerikDropDownList Id="StatusFilter"
                        Data="@Status"
                        TextField="."
                        ValueField="."
                        Filterable="true"
                        FilterOperator="StringFilterOperator.Contains"
                        DefaultText="All"
                        @bind-Value="@SelectedStatus" Width="200px" OnChange="ApplyFilters" />
                    </GridToolBarTemplate>
                    <GridSettings>
                        <GridPopupEditSettings Width="550px" MaxHeight="95vh" MaxWidth="95vw"></GridPopupEditSettings>
                        <GridPopupEditFormSettings Context="POContext">
                            <FormTemplate>
                                @{
                                    EditPoHeaderDto = POContext.Item as PoheaderDto;
                                    <TelerikForm Model="@EditPoHeaderDto"
                                    ColumnSpacing="10px"
                                    Columns="1"
                                    ButtonsLayout="@FormButtonsLayout.Stretch"
                                    OnValidSubmit="@OnValidSubmit">
                                        <FormValidation>
                                            <DataAnnotationsValidator></DataAnnotationsValidator>
                                        </FormValidation>
                                        <FormItems>

                                            <FormItem>
                                                <Template>
                                                    <TelerikFileSelect
                                                    AllowedExtensions="@AllowedExtensions"
                                                    MaxFileSize="@MaxFileSize"
                                                    OnSelect="@OnSelectHandler"
                                                    OnRemove="@OnRemoveHandler"
                                                    Class="custom-file-select" />
                                                </Template>
                                            </FormItem>

                                            @{
                                            if (EditPoHeaderDto.PoheaderId == 0)
                                            {
                                                <FormItem Field="Podescription" LabelText="Description" Enabled="true"></FormItem>
                                                <FormItem Field="Pototal" LabelText="Total" Enabled="true"></FormItem>
                                            }
                                            <FormItem>
                                                <Template>
                                                    <label for="position">Community</label>
                                                    <TelerikDropDownList Value="@CurrentJob.SubdivisionId"
                                                    Data="@SubdivisionData"
                                                    DefaultText="Select Subdivision"
                                                    TextField="SubdivisionName" ValueField="SubdivisionId"
                                                    ValueChanged="@( (int c) => SubdivisionSelected(c) )"
                                                    ValueExpression="@( () => CurrentJob.SubdivisionId )"
                                                    Filterable="true"
                                                    FilterOperator="@FilterOperator">
                                                    </TelerikDropDownList>
                                                </Template>
                                            </FormItem>
                                            @*      // <FormItem>
                                                //     <Template>
                                                //         <label for="jobPostingGroup">Job Posting Group</label>
                                                //         <TelerikDropDownList Value="@CurrentJob.JobPostingGroup"
                                                //         Data="@JobPostingGroupsData"
                                                //         DefaultText="Select Job Posting Group"
                                                //         TextField="JobPostingGroupDesc" ValueField="JobPostingGroupCode"
                                                //         ValueChanged="@( (string c) => JobPostingGroupSelected(c) )"
                                                //         ValueExpression="@( () => CurrentJob.JobPostingGroup )"
                                                //         Filterable="true"
                                                //         FilterOperator="@FilterOperator">
                                                //         </TelerikDropDownList>
                                                //     </Template>
                                                // </FormItem>
                                                *@


                                                <FormItem Field="Pojobnumber">
                                                    <Template>
                                                        <label for="job">Job</label>
                                                        <TelerikDropDownList Data="@JobsData"
                                                                             DefaultText="Select Job"
                                                                             TextField="DisplayDescription" ValueField="JobNumber"
                                                                             @bind-Value="@EditPoHeaderDto.Pojobnumber"
                                                                             OnChange="JobSelected"
                                                                             Enabled="@(CurrentJob.SubdivisionId > 0)"
                                                                             Id="job"
                                                                             Filterable="true"
                                                                             FilterOperator="@FilterOperator">
                                                        </TelerikDropDownList>
                                                    </Template>
                                                </FormItem>
                                                    <FormItem Field="HouseJob" class="@(ShowHouseJobs ? "" : "d-none")">
                                                    <Template>
                                                        <label for="housejob">Related House Job</label>
                                                        <TelerikDropDownList Data="@HouseJobsData"
                                                                                DefaultText="Select House Job"
                                                                                TextField="DisplayDescription" ValueField="JobNumber"
                                                                                @bind-Value="@EditPoHeaderDto.HouseJob"
                                                                                Enabled="@(CurrentJob.SubdivisionId > 0)"
                                                                                Id="Housejob"
                                                                                Filterable="true"
                                                                                FilterOperator="@FilterOperator">
                                                        </TelerikDropDownList>
                                                    </Template>
                                                </FormItem>
                                            }



                                            @{
                                                if (EditPoHeaderDto.PoheaderId == 0)
                                                {

                                                    @if (UserIsVMDB)
                                                    {
                                                        //only VMDB users. Should it be only VMDB jobs?
                                                        <FormItem>
                                                            <Template>
                                                                <label for="variance">Variance</label>
                                                                <TelerikDropDownList Data="@VarianceData"
                                                                DefaultText="Select Variance"
                                                                TextField="Category" ValueField="JccategoryId"
                                                                @bind-Value="@EditPoHeaderDto.JccCategoryId"
                                                                Filterable="true"
                                                                FilterOperator="@FilterOperator"></TelerikDropDownList>
                                                            </Template>
                                                        </FormItem>
                                                    }
                                                    <FormItem Field="PurchasingActivityId">
                                                        <Template>
                                                            <label for="variance">Purchasing Activity</label>
                                                            <TelerikDropDownList 
                                                            Data="@PurchasingActivityData"
                                                            DefaultText="Select Purchasing Activity"
                                                            TextField="DropdownDescription" ValueField="PactivityId"
                                                            OnChange="PactivitySelected"

                                                            @bind-Value="@EditPoHeaderDto.PurchasingActivityId"
                                                            Filterable="true"
                                                            FilterOperator="@FilterOperator">
                                                            </TelerikDropDownList>
                                                        </Template>
                                                    </FormItem>
                                                }
                                            }

                                            <FormItem>
                                                <Template>
                                                    <label for="variance">Supplier</label>
                                                    <TelerikDropDownList Value="@EditPoHeaderDto.SubNumber"
                                                    Data="@SupplierData"
                                                    DefaultText="Select Supplier"
                                                    TextField="SubName" ValueField="SubNumber"
                                                    ValueChanged="@( (int c) => SupplierSelected(c) )"
                                                    ValueExpression="@( () => CurrentSupplier.SubNumber )"
                                                    Filterable="true"
                                                    FilterOperator="@FilterOperator">
                                                    </TelerikDropDownList>
                                                </Template>
                                            </FormItem>

                                            @{
                                                if (EditPoHeaderDto.PoheaderId == 0)
                                                {
                                                    <!-- Backcharge Supplier -->
                                                    <FormItem>
                                                        <Template>
                                                            <label for="variance">Backcharge Supplier</label>
                                                            <TelerikDropDownList @bind-Value="@EditPoHeaderDto.BackchargeSubNumber"
                                                            Data="@SupplierData"
                                                            DefaultText="Select Backcharge Supplier"
                                                            TextField="SubName" ValueField="SubNumber"
                                                            Filterable="true"
                                                            FilterOperator="@FilterOperator">
                                                            </TelerikDropDownList>
                                                        </Template>
                                                    </FormItem>
                                                    <!-- End of Backcharge Supplier -->
                                                }
                                            }

                                        </FormItems>
                                        <FormButtons>
                                            <TelerikButton Icon="@FontIcon.Save" Class="k-button-success">Save</TelerikButton>
                                            <TelerikButton Icon="@FontIcon.Cancel" ButtonType="@ButtonType.Button" OnClick="@OnCancel">Cancel</TelerikButton>
                                        </FormButtons>
                                    </TelerikForm>
                                }
                            </FormTemplate>
                        </GridPopupEditFormSettings>
                    </GridSettings>
                    <GridColumns>
                        <GridColumn Field="Podescription" Title="Purchasing Activity" Editable="true" Groupable="true" />
                        <GridColumn Field="VpoNumberSearch" Title="VPO Number" Editable="false" Groupable="false" />
                        <GridColumn Field="Ponumber" Title="PO Number" Editable="false" Groupable="false" />
                        <GridColumn Field="SubdivisionName" Title="Subdivision" Editable="false" Groupable="false" />
                        <GridColumn Field="Pojobnumber" Title="Job Number" Editable="false" Groupable="true" />
                        <GridColumn Field="HouseJob" Title="Related House Job" Editable="false" Groupable="true" />
                        <GridColumn Field="Pototal" DisplayFormat="{0:C2}" Title="Amount" Editable="true" Groupable="false" />
                        <GridColumn Field="Supplier" Title="Supplier" Editable="true" Groupable="true" />
                        <GridColumn Field="CreatedBy" Title="Created By" Editable="false" Groupable="false" />
                        <GridColumn Field="CreatedDateTime" Title="Created" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Groupable="false" />
                        <GridColumn Field="PostatusNavigation.Postatus1" Title="Status" Editable="false" Groupable="false" />
                        <GridCommandColumn>
                            <div class="button-container">
                                @{
                                    var row = context as PoheaderDto;
                                    if(row != null)
                                    {
                                        if (row.PostatusNavigation?.Postatus1 == "Pending")
                                        {
                                            <GridCommandButton Title="Save" Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                            // if (!row.IsPOApprovalStarted ?? false)
                                            // {
                                            //     <GridCommandButton Title="Edit" Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                            // }
                                            <GridCommandButton Title="Delete" Command="Delete" OnClick="@DeleteVPO" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                        }
                                        <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                                        if (row.PostatusNavigation?.Postatus1 == "Sent")
                                        {
                                            <GridCommandButton Command="CancelPO" OnClick="@CancelVPO" Class="tooltip-target k-button-danger" Title="Cancel VPO" Icon="@FontIcon.X"></GridCommandButton>
                                        }                                   

                                        <GridCommandButton Title="Copy" Command="Copy" OnClick="@Copy" Icon="@FontIcon.Copy"></GridCommandButton>

                                        <GridCommandButton Title="Download" Command="Download" OnClick="@Download" Icon="@FontIcon.Download"></GridCommandButton>

                                        if (row.PoAttachments.Any())
                                        {
                                            <GridCommandButton Title="View and Download Attachments" Command="View" OnClick="@ViewAttachments" Icon="@FontIcon.Paperclip"></GridCommandButton>
                                        }
                                    }                                                               
                                }
                            </div>
                        </GridCommandColumn>
                    </GridColumns>
                    <DetailTemplate>
                        @{
                            PoheaderDto poHeader = context as PoheaderDto;
                            <ERP.Web.Components.NestedVPOs PoJobNumber="@poHeader.Pojobnumber" POHeaderId="@poHeader.PoheaderId" AvoidChanges="@poHeader.IsPOApprovalStarted"></ERP.Web.Components.NestedVPOs>
                        }
                    </DetailTemplate>

                </TelerikGrid>
            }
        </div>
    </div>
</div>

@code {
    public List<PoheaderDto>? PoData { get; set; }
    private TelerikGrid<PoheaderDto>? PoGridRef { get; set; }
    private PoheaderDto EditPoHeaderDto { get; set; }
    public bool IsLoadingOptions { get; set; } = true;
    public bool IsLoadingItem { get; set; } = false;
    private List<SubdivisionDto> SubdivisionData { get; set; }
    private List<JobDto> JobsData { get; set; }
    private List<JobDto> HouseJobsData { get; set; }
    private List<JobPostingGroupDto> JobPostingGroupsData { get; set; }
    private List<JccategoryDto> VarianceData { get; set; }
    private List<PactivityDto?>? PurchasingActivityData { get; set; }
    private List<TradeDto> TradeData { get; set; }
    private List<SupplierDto> SupplierData { get; set; }
    private StringFilterOperator FilterOperator { get; set; } = StringFilterOperator.Contains;
    private bool IsModalVisible { get; set; }
    private bool UserIsVMDB { get; set; } = false;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    public CascadeSubdivisionJobDto CurrentJob { get; set; } = new CascadeSubdivisionJobDto();
    public CascadePactivityTradeDto CurrentActivity { get; set; } = new CascadePactivityTradeDto();
    public CascadeSupplierContactDto CurrentSupplier { get; set; } = new CascadeSupplierContactDto();
    public CascadeSupplierContactDto CurrentBackchargeSupplier { get; set; } = new CascadeSupplierContactDto();
    public ViewAttachments? ViewAttachmentsModal { get; set; }
    public List<SubdivisionDto>? AllSubdivisions { get; set; }
    public List<JobDto> AllJobs { get; set; }
    public List<JobDto> Jobs { get; set; }
    private string? SelectedSubdivision { get; set; }
    private string? SelectedJob { get; set; }
    private FilterDescriptorCollection filterCollection = new FilterDescriptorCollection();
    private string SelectedStatus { get; set; }
    private BrowserInfo? BrowserInfo { get; set; }
    private bool ShowHouseJobs { get; set; }
    public List<string> Status = new List<string>()
    {
        "Pending",
        "Pending Payment Approval",
        "Sent",
        "Approved",
        "Cancelled"
    };

    private bool? _isTotalChanged { get; set; }

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    List<string> AllowedExtensions { get; set; } = new List<string>()
    {
        ".jpeg", ".jpg",
        ".png", ".gif",
        ".heic",".pdf",
        ".doc", ".docx",
        ".xls", ".xlsx",
        ".csv",
        ".txt",
        ".rtf"
    };

    int MaxFileSize { get; set; } = 2 * 1024 * 1024; // 2 MB since new screenshots have higher quality

    async Task OnSelectHandler(FileSelectEventArgs args)
    {
        foreach (var file in args.Files)
        {
            string fileExtension = Path.GetExtension(file.Name).ToLower(); // Get the file extension and convert it to lowercase
            if (AllowedExtensions.Contains(fileExtension))
            {
                var buffer = new byte[file.Stream.Length];
                await file.Stream.ReadAsync(buffer);
                EditPoHeaderDto.Files.Add(new FileModel()
                    {
                        FileData = buffer,
                        FileName = file.Name
                    });
            }
            else
            {
                return;
            }
        }
    }

    async Task OnRemoveHandler(FileSelectEventArgs args)
    {
        foreach (var file in args.Files)
        {
            var fileToRemove = EditPoHeaderDto.Files
                .FirstOrDefault(f => f.FileName == file.Name);

            if (fileToRemove != null)
            {
                EditPoHeaderDto.Files.Remove(fileToRemove);
            }
        }

        await Task.CompletedTask;
    }

    async Task<string> getUserName()
    {
        var user = (await _authenticationStateProvider.GetAuthenticationStateAsync()).User;
        var userName = user.Identity.Name.Split('@')[0];
        return userName;
    }

    protected override async Task OnInitializedAsync()
    {
        //await LoadData();
        var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
        BrowserInfo = await JS.InvokeAsync<BrowserInfo>("browserUtils.getBrowserInfo", null);
        UserIsVMDB = user.User.IsInRole("DesignBuild");
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await LoadData();

            Console.WriteLine("first render");
        }
        else
        {
            Console.WriteLine("second render");
        }
    }
    private async Task OnCancel()
    {
        await ExitEditAsync();
    }

    private async Task OnValidSubmit()
    {
        var cleanedFiles = EditPoHeaderDto.Files
            .Where(f => f != null && f.FileData != null && !string.IsNullOrWhiteSpace(f.FileName))
            .ToList();

        EditPoHeaderDto.Files = cleanedFiles;

        if (EditPoHeaderDto.PoheaderId != 0)
        {
            // Edit
            var editResponse = await PoService.EditVPOAsync(EditPoHeaderDto);
            ShowNotification(editResponse.Message, editResponse.IsSuccess);
        }
        else
        {
            // Create
            var addResponse = await PoService.AddVPOAsync(EditPoHeaderDto);
            ShowNotification(addResponse.Message, addResponse.IsSuccess);
        }

        await ExitEditAsync();

        var poData = await PoService.GetVPOs(getUserName().Result);
        PoData = poData.Value;
    }

    protected async Task ReadItems(GridReadEventArgs args)
    {
        var poData = await PoService.GetVPOs(getUserName().Result);
        var datasourceResult = poData.Value.ToDataSourceResult(args.Request);

        args.Data = datasourceResult.Data;
        args.Total = datasourceResult.Total;
    }

    private async Task LoadData()
    {
        IsLoadingItem = true;
        IsLoadingOptions = true;
        // var poDataTask = PoService.GetVPOs(getUserName().Result);
        var subdivisionTask = SubdivisionService.GetSubdivisionsAsync();
        var varianceTask = PoService.GetVariances();
        var pactivityTask = PoService.GetPurchasingActivities();
        var suplierTask = PoService.GetSupplierData();
        var jobsTask = SubdivisionService.GetAllJobsAsync();
        // var jobPostingGroupsTask = JobService.GetJobPostingGroups();
        await Task.WhenAll(new Task[] {  subdivisionTask, varianceTask, pactivityTask, suplierTask, jobsTask});
        SubdivisionData = subdivisionTask.Result.Value.OrderBy(x => x.SubdivisionName).ToList();
        VarianceData = varianceTask.Result.Value.Where(x => x.DesignBuild == true).ToList();
        PurchasingActivityData = pactivityTask.Result.Value;
        SupplierData = suplierTask.Result.Value;
        JobsData = jobsTask.Result.Value;
        AllJobs = jobsTask.Result.Value; // used for filtering
        Jobs = jobsTask.Result.Value; // used for filtering
        var subdivisionsTask = await SubdivisionService.GetSubdivisionsAsync();
        AllSubdivisions = subdivisionsTask.Value;

        //var postingGroupsToUse = new List<string>() { "CCONST", "CSHELL", "OHCONSTRUCTION", "OHDESIGN", "OHMARKETING", "OHOTHER", "OHSALES", "OVERHEAD", "SFCONST", "TECONST", "TICONST", "THCONST", "VMDB", "VMDBHB", "WARRANTY"  };
        //JobPostingGroupsData = jobPostingGroupsTask.Result.Value.Where(x => postingGroupsToUse.Contains(x.JobPostingGroupCode)).ToList();
        // CurrentJob.JobPostingGroup = "OHCONSTRUCTION";
        //PoData = poDataTask.Result.Value;
        IsLoadingItem = false;
        IsLoadingOptions = false;
        StateHasChanged();
    }

    private async Task OnGridStateInit(GridStateEventArgs<PoapprovalDto> args)
    {

        var filters = new CompositeFilterDescriptor()
            {
                FilterDescriptors = new FilterDescriptorCollection() {
                     new FilterDescriptor()
                     {
                        Member = nameof(PoheaderDto.SubdivisionId),
                        MemberType = typeof(string),
                        Operator = Telerik.DataSource.FilterOperator.IsEqualTo,
                        Value = SelectedSubdivision
                     },
                     new FilterDescriptor()
                     {
                        Member = nameof(PoheaderDto.Pojobnumber),
                        MemberType = typeof(string),
                        Operator = Telerik.DataSource.FilterOperator.IsEqualTo,
                        Value = SelectedJob
                     },
                     new FilterDescriptor()
                     {
                        Member = nameof(PoheaderDto.Status),
                        MemberType = typeof(string),
                        Operator = Telerik.DataSource.FilterOperator.IsEqualTo,
                        Value = SelectedStatus
                     }
                }
            };
    }

    private async Task ExitEditAsync()
    {
        var state = PoGridRef?.GetState();
        state.OriginalEditItem = null;
        state.EditItem = null;
        state.InsertedItem = null;

        await PoGridRef?.SetStateAsync(state);
    }

    private async Task SubdivisionSelected(int subdivisionId)
    {
        if (subdivisionId == 0)
        {
            CurrentJob = new CascadeSubdivisionJobDto();
            return;
        }

        var jobsData = await SubdivisionService.GetJobBySubdivisionAsync(subdivisionId, false);
        JobsData = jobsData.Value;
        var postingGroupsToUse = new List<string>() { "CCONST", "CSHELL", "OHCONSTRUCTION", "OHDESIGN", "OHMARKETING", "OHOTHER", "OHSALES", "OVERHEAD", "SFCONST", "TECONST", "TICONST", "THCONST", "VMDB", "VMDBHB", "WARRANTY" };
        JobsData = jobsData.Value.Where(x => postingGroupsToUse.Contains(x.JobPostingGroup)).ToList();
        var ohJobs = JobsData.Where(x => x.JobNumber.Contains("OH")).ToList();
        if (ohJobs != null)
        {
            foreach (var ohJob in ohJobs)
            {
                var index = JobsData.IndexOf(ohJob);
                JobsData.RemoveAt(index);
                // JobsData.Insert(0, ohJob);
            }
        }
        var warrantyJobs = JobsData.Where(x => x.JobNumber.Contains("WC")).ToList();
        if (warrantyJobs != null)
        {
            foreach (var warrantyJob in warrantyJobs)
            {
                var index = JobsData.IndexOf(warrantyJob);
                JobsData.RemoveAt(index);
                JobsData.Insert(0, warrantyJob);
            }
        }
        var CNJobs = JobsData.Where(x => x.JobNumber.Contains("CN")).ToList();
        if (CNJobs != null)
        {
            foreach (var CNJob in CNJobs)
            {
                var index = JobsData.IndexOf(CNJob);
                JobsData.RemoveAt(index);
                JobsData.Insert(0, CNJob);
            }
        }


        var subdivisionData = await SubdivisionService.GetSubdivisionsAsync();
        var selectedSubdivision = subdivisionData.Value.Where(x => x.SubdivisionId == subdivisionId).FirstOrDefault();

        CurrentJob.SubdivisionId = selectedSubdivision.SubdivisionId;
    }

    private async Task JobPostingGroupSelected(string jobPostingGroupCode)
    {
        if (CurrentJob.SubdivisionId == 0)
        {
            CurrentJob = new CascadeSubdivisionJobDto();
            return;
        }

        var jobsData = await SubdivisionService.GetJobBySubdivisionAsync(CurrentJob.SubdivisionId, false);
        JobsData = jobsData.Value;
        JobsData = JobsData.Where(x=>x.JobPostingGroup == jobPostingGroupCode).ToList();

        CurrentJob.JobPostingGroup = jobPostingGroupCode;
    }

    private async Task JobSelected()
    {        
        if (EditPoHeaderDto.Pojobnumber != null && EditPoHeaderDto.Pojobnumber != CurrentJob.JobNumber)
        {
            CurrentJob.JobNumber = EditPoHeaderDto.Pojobnumber;
            EditPoHeaderDto.HouseJob = null;//Clear any previous input
            var postingGroupsToUse = new List<string>() { "WARRANTY" };//just warranty for now. Shoud it be Overhead too
            //var postingGroupsToUse = new List<string>() { "OVERHEAD", "WARRANTY", "OHCONSTRUCTION", "OHMARKETING", "OHSALES", "OHOTHER", "OHDESIGN" };
            var selectedJob = JobsData.Where(x => x.JobNumber == CurrentJob.JobNumber).FirstOrDefault();
            bool isInGroup = selectedJob != null && postingGroupsToUse.Contains(selectedJob.JobPostingGroup);
            if (isInGroup)
            {
                var constructionPostingGroup = new List<string>() { "CCONST",  "SFCONST", "TECONST", "TICONST", "THCONST", "ADUCONST" };
                HouseJobsData = JobsData.Where(x => constructionPostingGroup.Contains(x.JobPostingGroup)).ToList();
                ShowHouseJobs = true;
            }
            else
            {
                ShowHouseJobs = false;
            }

            var pactivityData = await PoService.GetPurchasingActivitiesByJob(CurrentJob.JobNumber);
            PurchasingActivityData = pactivityData.Value;
            StateHasChanged();
        }       
    }
    private async Task PactivitySelected()
    {
        if (EditPoHeaderDto.PurchasingActivityId == 0 && EditPoHeaderDto.PurchasingActivityId != null)
        {
            CurrentActivity = new CascadePactivityTradeDto();
            return;
        }

        // var tradeData = await PoService.GetTradeByActivityAsync(pactivityId);
        // TradeData = tradeData.Value;dxx

        // var activityData = await PoService.GetPurchasingActivities();
        // var selectedActivity = activityData.Value.Where(x => x.PactivityId == pactivityId).FirstOrDefault();

        // EditPoHeaderDto.PurchasingActivityId = selectedActivity.PactivityId;
        // CurrentActivity.PactivityId = selectedActivity.PactivityId;

        //EditPoHeaderDto.PurchasingActivityId = pactivityId;
        CurrentActivity.PactivityId = EditPoHeaderDto.PurchasingActivityId;
    }

    private async Task SupplierSelected(int supplierId)
    {
        if (supplierId == 0)
        {
            CurrentSupplier = new CascadeSupplierContactDto();
            return;
        }

        var supplierData = await PoService.GetSupplierData();
        var selectedSupplier = supplierData.Value.Where(x => x.SubNumber == supplierId).FirstOrDefault();

        EditPoHeaderDto.SubNumber = selectedSupplier.SubNumber;
    }

    protected override void OnInitialized()
    {
        this.VPOItemPickService.OnDataChanged += GetChildUpdate;

    }
    async Task DeleteVPO(GridCommandEventArgs args)
    {
        var itemToDelete = (PoheaderDto)args.Item;
        if (itemToDelete != null && itemToDelete.Podateapproved == null)
        {
            var response = await PoService.DeletePo(itemToDelete);
            await ExitEditAsync();

            ShowNotification(response.Message, response.IsSuccess);
        }
    }
    async Task CancelVPO(GridCommandEventArgs args)
    {
        //TODO: prompt are you sure?
        var itemToCancel = (PoheaderDto)args.Item;
        if (itemToCancel != null && itemToCancel.Podateapproved == null)
        {
            var emailVendor = await Dialogs.ConfirmAsync("Notify supplier of cancellation? Cancel will cancel VPO but not notify supplier.");
            var response = await PoService.CancelPo(itemToCancel);
            if (response.IsSuccess)
            {
                if (emailVendor)
                {
                    if (itemToCancel != null)
                    {
                        var poHeaderdata = await PoService.GetPoHeaderAsync(itemToCancel.PoheaderId);
                        var poHeader = poHeaderdata.Value;
                        var data = await PoService.GetVPODetailByPOHeaderIdAsync(itemToCancel.PoheaderId);
                        var poDetails = data.Value;
                        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(poHeader, poDetails);

                        var files = new List<FileModel>();
                        files.Add(new FileModel()
                            {
                                FileData = fileData,
                                FileName = $"{itemToCancel.Ponumber}.pdf"
                            });
                        var emailModel = new PoEmailModel()
                            {
                                Files = files,
                                Poheader = itemToCancel,
                                Subject = $"CANCELLED Van Metre Purchase Order: {itemToCancel.Ponumber}",
                                Body = "The attached purchase order is cancelled. Visit the Van Metre Trade Portal (https://trades.vanmetrehomes.com/) for more information"
                            };
                        await PoService.EmailPos(emailModel);
                    }
                }
            }
            await ExitEditAsync();

            ShowNotification(response.Message, response.IsSuccess);
        }
    }

    public async void Download(GridCommandEventArgs args)
    {
        var poHeader = args.Item as PoheaderDto;
        if (poHeader != null)
        {
            var getPoHeaderdata = await PoService.GetPoHeaderAsync(poHeader.PoheaderId);
            var getPoHeader = getPoHeaderdata.Value;
            var estDetailsResponse = await BudgetService.GetPoDetailsByPONumberAsync(poHeader.PoheaderId);
            if (estDetailsResponse.IsSuccess)
            {
                GenerateDocumentAndDownload(getPoHeader, estDetailsResponse.Value);
            }
            else
            {
                ShowNotification(estDetailsResponse.Message, false);
            }
        }
    }

    public async void Copy(GridCommandEventArgs args)
    {
        var poHeader = args.Item as PoheaderDto;
        if (poHeader != null)
        {
            var headerData = await PoService.GetPoHeaderAsync(poHeader.PoheaderId);
            var header = headerData.Value;
            var detail = await PoService.GetVPODetailByPOHeaderIdAsync(poHeader.PoheaderId);
            PodetailDto poDetails = detail.Value.FirstOrDefault();
            var state = PoGridRef?.GetState();

            EditPoHeaderDto = new PoheaderDto()
            {
                Podescription = poDetails?.Poitemdesc,
                Pototal = poHeader.Pototal,
                Pojobnumber = poHeader.Pojobnumber,
                PojobnumberNavigation = header?.PojobnumberNavigation,
                SubdivisionName = poHeader.SubdivisionName,
                PurchasingActivityId = poDetails?.PurchasingActivityId,
                SubNumber = poHeader.SubNumber,
                BackchargeSubNumber = poHeader.BackchargeSubNumber,
            };

            CurrentJob.SubdivisionId = poHeader.SubdivisionId;
            var jobsData = await SubdivisionService.GetJobBySubdivisionAsync(poHeader.SubdivisionId, false);
            JobsData = jobsData.Value;

            var pactivityData = await PoService.GetPurchasingActivitiesByJob(poHeader.Pojobnumber);
            PurchasingActivityData = pactivityData.Value;

            state.InsertedItem = EditPoHeaderDto;
            await PoGridRef?.SetStateAsync(state);
        }
    }


    public async void ViewAttachments(GridCommandEventArgs args)
    {
        var poHeader = args.Item as PoheaderDto;
        if (poHeader != null)
        {
            var blobFilesDetail = await PoService.GetBlobFilesDetailAsync(poHeader);
            var files = blobFilesDetail.Value;
            if (files != null)
            {
                foreach (var file in files)
                {
                    await OpenFileInNewTab(file);
                    if (file.Content != null)
                    {
                        DemoFileExporter.Save(JS, file.Content, file.ContentType, file.DownloadName);
                    }
                }
            }
        }
    }

    private async Task OpenFileInNewTab(BlobFileDetails file)
    {
        string base64File = string.Empty;
        string fileName = string.Empty;
        string contentType = string.Empty;
        if (ImageConverter.IsHeicFile(file.DownloadName) && BrowserInfo?.Browser != "Safari")
        {
            var jpegContent = ImageConverter.ConvertHeicBytesToJpeg(file.Content);
            base64File = Convert.ToBase64String(jpegContent);
            fileName = Path.ChangeExtension(file.DownloadName, ".jpg");
            contentType = "image/jpeg";
        }
        else
        {
            base64File = Convert.ToBase64String(file.Content);
            fileName = file.DownloadName;
            contentType = file.ContentType;
        }

        // Invoke JavaScript function to open a new tab and display the file
        await JS.InvokeVoidAsync("viewMyFile", base64File, contentType, fileName);
    }

    async Task GenerateDocumentAndDownload(PoheaderDto poHeader, List<PodetailDto> poDetails)
    {
        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(poHeader, poDetails);
        // await PoService.UploadPoAsync(itemToIssue.Poheader, fileData);
        DemoFileExporter.Save(JS, fileData, "application/pdf", $"PO-{poHeader.Ponumber}.pdf");
    }
    async void ShowNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
    async Task GetChildUpdate()
    {
        await this.InvokeAsync(StateHasChanged);

        _isTotalChanged = this.VPOItemPickService.IsChanged;

        if (_isTotalChanged == true)
        {
            PoGridRef?.Rebind();
        }
    }

    async Task OnSubdivisionChanged()
    {
        // Reset the selected job
        SelectedJob = "All";

        // Re-filter the job list based on selected subdivision
        if (!string.IsNullOrWhiteSpace(SelectedSubdivision) && SelectedSubdivision != "All")
        {
            int subdivisionId = AllSubdivisions
                .FirstOrDefault(x => x.SubdivisionName == SelectedSubdivision)?.SubdivisionId ?? 0;

            Jobs = AllJobs
                .Where(x => x.SubdivisionId == subdivisionId)
                .ToList();
        }
        else
        {
            Jobs = AllJobs.ToList();
        }

        // Re-apply filters with updated job list
        await ApplyFilters();
    }

    async Task ApplyFilters()
    {
        var state = PoGridRef.GetState();

        // Create a new composite filter to hold both filters
        var compositeFilter = new CompositeFilterDescriptor
            {
                LogicalOperator = FilterCompositionLogicalOperator.And,
                FilterDescriptors = new FilterDescriptorCollection()
            };

        // Add subdivision filter if applicable
        if (!string.IsNullOrWhiteSpace(SelectedSubdivision) && SelectedSubdivision != "All")
        {
            compositeFilter.FilterDescriptors.Add(new FilterDescriptor
                {
                    Member = nameof(PoheaderDto.SubdivisionName),
                    MemberType = typeof(string),
                    Operator = Telerik.DataSource.FilterOperator.IsEqualTo,
                    Value = SelectedSubdivision
                });
        }

        // Add job number filter if applicable
        if (!string.IsNullOrWhiteSpace(SelectedJob) && SelectedJob != "All")
        {
            compositeFilter.FilterDescriptors.Add(new FilterDescriptor
                {
                    Member = nameof(PoheaderDto.Pojobnumber),
                    MemberType = typeof(string),
                    Operator = Telerik.DataSource.FilterOperator.IsEqualTo,
                    Value = SelectedJob
                });
        }

        if (!string.IsNullOrWhiteSpace(SelectedStatus) && SelectedStatus != "All")
        {
            compositeFilter.FilterDescriptors.Add(new FilterDescriptor
                {
                    Member = nameof(PoheaderDto.Status),
                    MemberType = typeof(string),
                    Operator = Telerik.DataSource.FilterOperator.IsEqualTo,
                    Value = SelectedStatus
                });
        }

        var newState = new GridState<PoheaderDto>
            {
                Skip = 0,
                FilterDescriptors = compositeFilter.FilterDescriptors.Any()
                    ? new List<IFilterDescriptor> { compositeFilter }
                    : new List<IFilterDescriptor>(),
            };

        await PoGridRef.SetStateAsync(newState);
    }


    public void Dispose()
    {
        this.VPOItemPickService.OnDataChanged -= GetChildUpdate;
    }
}
