﻿@page "/lotdetails/{jobnumber}"
@page "/lotdetails"
@using ERP.Data.Models.Dto;
@implements IDisposable
@inject NavigationManager NavManager
@inject SubdivisionService SubdivisionService
@inject JobDocumentService DocumentService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject SelectedOptionsService SelectedOptionsService
@inject JobService JobService
@inject IJSRuntime JS

<style type="text/css">
    .k-form .k-form-field > .k-label, .k-form .k-form-field > kendo-label, .k-form .k-form-label > label[for=checkBoxNoWrap] {
    display: inline;
    flex-flow: nowrap;
    }

    .checkBoxNoWrapClass > div {
    width: 5%;
    float: left;
    }


    .k-button-danger {
    border-color: #e6a7a7;
    color: #000000;
    background-color: #e6a7a7;
    }

    .k-button-success {
    border-color: #f4cd64;
    color: #000000;
    background-color: #f4cd64;
    }

    .selected-items-container {
    max-height: 300px;
    overflow-y: auto;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
    font-family: "Roboto", sans-serif;
    font-size: .75rem;
    }

    .custom-dropdown {
    width: 250px;
    }
</style>
<SubdivisionJobPickerMenuBar ShowSupplier="false" ShowJob="true" ShowSubdivision="true"></SubdivisionJobPickerMenuBar>
<TelerikLoaderContainer Visible="@IsLoading" Text="Please wait..." />
<TelerikTooltip TargetSelector=".tooltip-target" />

<ErrorBoundary>
    <ChildContent>
        <PageTitle>Lots | @lotDetails.JobNumber</PageTitle>
        <TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

        <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Details For: @lotDetails.JobNumber</h7>
                </div>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item"><a href="/lots">Lots</a></li>
            <li class="breadcrumb-item active">Details</li>
        </ol>

        <br />
        @if(JobNumber == null)
        {
            <p>Select a lot to see details</p>
        }
        else
        {
            <EditForm Model="@lotDetails" OnValidSubmit="@HandleValidSubmit">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <TelerikTabStrip ActiveTabIndex="@ActiveTabIndex" ActiveTabIndexChanged="@TabChangedHandler">
                    <TabStripTab Title="Lot Details">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-4">
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Job Number:</label>
                                        <div class="col-sm-8">
                                            &nbsp; <TelerikTextBox Enabled="false" @bind-Value="lotDetails.JobNumber" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Job Description:</label>
                                        <div class="col-sm-8">
                                            &nbsp; <TelerikTextBox Enabled="false" @bind-Value="lotDetails.JobDesc" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Subdivision:</label>
                                        <div class="col-sm-8">&nbsp;
                                            @if (lotDetails.Subdivision != null)
                                            {
                                                <TelerikTextBox  Enabled="false" @bind-Value="lotDetails.Subdivision.SubdivisionName" Width="200px"></TelerikTextBox>
                                            }

                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Lot Number:</label>
                                        <div class="col-sm-8">
                                            &nbsp;  <TelerikTextBox  Enabled="@AllowEdit" @bind-Value="lotDetails.LotNumber" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Job Address:</label>
                                        <div class="col-sm-8">
                                            &nbsp;  <TelerikTextBox Enabled="@AllowEdit" @bind-Value="lotDetails.JobAddress1" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Job Address2:</label>
                                        <div class="col-sm-8">
                                            &nbsp;  <TelerikTextBox Enabled="@AllowEdit" @bind-Value="lotDetails.JobAddress2" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Job City:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikTextBox Enabled="@AllowEdit" @bind-Value="lotDetails.JobCity" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Job State:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikTextBox Enabled="@AllowEdit" @bind-Value="lotDetails.JobState" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Job Zip:</label>
                                        <div class="col-sm-8">
                                            &nbsp;  <TelerikTextBox Enabled="@AllowEdit" @bind-Value="lotDetails.JobZipCode" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Job County:</label>
                                        <div class="col-sm-8">
                                            &nbsp;  <TelerikTextBox  Enabled="@AllowEdit" @bind-Value="lotDetails.JobCounty" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Blocked:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikCheckBox Enabled="false" @bind-Value="lotDetails.BoolBlocked"></TelerikCheckBox>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Projected Lot Takedown:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikDatePicker Enabled="@AllowEdit" @bind-Value="lotDetails.ProjectedLotTakedown" Width="200px"></TelerikDatePicker>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Projected Sales Release Date:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikDatePicker Enabled="@AllowEdit" @bind-Value="lotDetails.ProjectedSalesReleaseDate" Width="200px"></TelerikDatePicker>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">General Option Budget:</label>
                                        <div class="col-sm-8">
                                            &nbsp;  <TelerikNumericTextBox Enabled="@AllowEdit" @bind-Value="lotDetails.GeneralOptionBudget" Width="200px"></TelerikNumericTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Homesite Option Budget:</label>
                                        <div class="col-sm-8">
                                            &nbsp;  <TelerikNumericTextBox Enabled="@AllowEdit" @bind-Value="lotDetails.HomesiteOptionSpendBudget" Width="200px"></TelerikNumericTextBox>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <div class="col-1"></div>
                                @if(AllowEdit)
                                {
                                    <div class="col">
                                        <button type="submit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                                            <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                                                <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                                    <!--!-->
                                                    <!--!-->
                                                    <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                                                </svg>
                                            </span> Update
                                        </button>
                                    </div>
                                }

                            </div>
                        </div>
                    </TabStripTab>
                    <TabStripTab Title="More Lot Details">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-3">
                                    @* <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Lot Availability:</label>
                                        <div class="col-sm-8">
                                         &nbsp;   <TelerikNumericTextBox  Enabled="@AllowEdit" @bind-Value="lotDetails.LotAvailability" Width="200px"></TelerikNumericTextBox>
                                        </div>
                                    </div> *@
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Lot Cost:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikNumericTextBox  Enabled="@AllowEdit"  @bind-Value="lotDetails.LotCost" Width="200px"></TelerikNumericTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Job Posting Group:</label>
                                        <div class="col-sm-8">
                                            &nbsp;  <TelerikTextBox Enabled="false" @bind-Value="lotDetails.JobPostingGroup" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Lot Premium:</label>
                                        <div class="col-sm-8">
                                            &nbsp;  <TelerikNumericTextBox  Enabled="false" @bind-Value="lotDetails.LotPremium" Width="200px"></TelerikNumericTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Lot Status:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikDropDownList Data= "@LotStatusList"
                                            Enabled="false" TextField="StatusName" ValueField="Id" @bind-Value="lotDetails.LotStatus" Width="200px">
                                            </TelerikDropDownList>
                                        </div>
                                    </div>                                    
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Lot Section Code:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikTextBox  Enabled="@AllowEdit" @bind-Value="lotDetails.LotSectionCode" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Lot Width:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.LotWidth" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    @*                                     <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Lot Swing:</label>
                                        <div class="col-sm-8">
                                            &nbsp;  <TelerikTextBox Enabled="@AllowEdit" @bind-Value="lotDetails.LotSwing" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div> *@
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Lot Size:</label>
                                        <div class="col-sm-8">
                                            &nbsp;<TelerikNumericTextBox Enabled="@AllowEdit" @bind-Value="lotDetails.LotSize" Width="200px"></TelerikNumericTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Plan Name:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikTextBox  Enabled="false" @bind-Value="lotDetails.PlanName" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Plan Code:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikTextBox  Enabled="false" @bind-Value="lotDetails.PlanCode" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">In SS (check for SS, blank for Handshake):</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikCheckBox Enabled="@AllowEdit" @bind-Value="lotDetails.BoolIsSs"></TelerikCheckBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Home Orientation is Per Plan:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikCheckBox  Enabled="@AllowEdit" @bind-Value="lotDetails.BoolHomeOrientationPerPlan"></TelerikCheckBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Parking Number:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikTextBox  Enabled="@AllowEdit" @bind-Value="lotDetails.ParkingNum" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Garage Number:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikTextBox Enabled="@AllowEdit" @bind-Value="lotDetails.GarageNum" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Storage Number:</label>
                                        <div class="col-sm-8">
                                            &nbsp;   <TelerikTextBox Enabled="@AllowEdit" @bind-Value="lotDetails.StorageNum" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Garage Orientation:</label>
                                        <div class="col-sm-8">
                                            &nbsp;  <TelerikDropDownList Data="@GarageOrientationList" TextField="Name" ValueField="GarageOrientationId" Enabled="@AllowEdit" @bind-Value="lotDetails.GarageOrientationId" Width="200px"></TelerikDropDownList>
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Construction Type:</label>
                                        <div class="col-sm-8">
                                            &nbsp;  <TelerikDropDownList Data="@ConstructionTypesList" TextField="Description" ValueField="JobConstructionTypeId" Enabled="@AllowEdit" @bind-Value="lotDetails.JobConstructionTypeId" Width="200px"></TelerikDropDownList>
                                        </div>
                                    </div>
                                    @* <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Lot Unit:</label>
                                        <div class="col-sm-8">
                                         &nbsp;   <TelerikTextBox  Enabled="@AllowEdit" @bind-Value="lotDetails.LotUnit" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div> *@
                                    @*  <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Model Name:</label>
                                        <div class="col-sm-8">
                                         &nbsp;   <TelerikTextBox  Enabled="@AllowEdit" @bind-Value="lotDetails.ModelName" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div> *@

                                    @*                                     <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Closed:</label>
                                        <div class="col-sm-8">
                                            &nbsp;    <TelerikTextBox Enabled="false" @bind-Value="lotDetails.Closed" Width="200px"></TelerikTextBox>
                                        </div>
                                    </div> *@
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Notes:</label>
                                        <div class="col-sm-8">
                                            &nbsp;    <TelerikTextArea Enabled="@AllowEdit" @bind-Value="lotDetails.Notes" Width="200px"></TelerikTextArea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <div class="col-1"></div>
                                @if(AllowEdit)
                                {
                                    <div class="col">
                                        <button type="submit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                                            <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                                                <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                                    <!--!-->
                                                    <!--!-->
                                                    <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                                                </svg>
                                            </span> Update
                                        </button>
                                    </div>
                                }                               
                            </div>
                        </div>
                    </TabStripTab>
                    <TabStripTab Title="Stick/Building/Phase">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-3">
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Stick:</label>
                                        <div class="col-sm-8">
                                            &nbsp;
                                            @{
                                                <TelerikDropDownList Data="@AllSticksInSubdivision"
                                                @ref="StickDropdownListRef"
                                                Enabled="@AllowEdit"
                                                Filterable="true"
                                                FilterOperator="@StringFilterOperator.Contains"
                                                @bind-Value="lotDetails.StickBuilingNum"
                                                Width="200px">
                                                </TelerikDropDownList>
                                                @if (lotDetails.StickBuilingNum == "Add New")
                                                {
                                                    <br />
                                                    <TelerikTextBox @bind-Value="NewStickName" Placeholder="New Stick Name" Width="200px"></TelerikTextBox>
                                                }
                                            }
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Building Num (for Condo):</label>
                                        <div class="col-sm-8">&nbsp;
                                            @{
                                                <TelerikDropDownList Data="@AllBuildingNumsInSubdivision"
                                                @ref="BuildingDropdownListRef"
                                                Filterable="true"
                                                Enabled="@AllowEdit"
                                                FilterOperator="@StringFilterOperator.Contains"
                                                @bind-Value="lotDetails.BuildingNum"
                                                Width="200px">
                                                </TelerikDropDownList>
                                                @if (lotDetails.BuildingNum == "Add New")
                                                {
                                                    <br />
                                                    <TelerikTextBox @bind-Value="NewBuildingNumber" Placeholder="New Building Num" Width="200px"></TelerikTextBox>
                                                }
                                            }
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Phase:</label>
                                        <div class="col-sm-8">
                                            &nbsp;
                                            @{
                                                <TelerikDropDownList @ref="PhaseDropdownListRef"
                                                Data="@AllPhasesInSubdivision"
                                                Filterable="true"
                                                Enabled="@AllowEdit"
                                                FilterOperator="@StringFilterOperator.Contains"
                                                @bind-Value="lotDetails.Phase"
                                                Width="200px">
                                                </TelerikDropDownList>
                                                @if (lotDetails.Phase == "Add New")
                                                {
                                                    <br />
                                                    <TelerikTextBox @bind-Value="NewPhase" Placeholder="New Phase" Width="200px"></TelerikTextBox>
                                                }
                                            }
                                        </div>
                                    </div>


                                </div>
                                <div class ="col-6">
                                    <div class="mb-3 row">
                                        <div class="col-sm-12">
                                            <label class="form-label">Select jobs to apply the same Stick/Building Num/Phase</label><br />
                                            <TelerikGrid Height="400px"
                                            RowHeight="40"
                                            @bind-SelectedItems="@SelectedJobs"
                                            FilterMode="@GridFilterMode.FilterMenu"
                                            Data="AvailableJobs"
                                            SelectionMode="GridSelectionMode.Multiple">
                                                <GridColumns>
                                                    <GridCheckboxColumn SelectAllMode="GridSelectAllMode.All"></GridCheckboxColumn>
                                                    <GridColumn Field="JobNumber" Filterable="true"></GridColumn>
                                                    <GridColumn Field="LotNumber" Title="Lot"></GridColumn>
                                                    @*  <GridColumn Field="Subdivision.SubdivisionName" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Subdivision"></GridColumn> *@                                                   
                                                    <GridColumn Field="Phase" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Phase"></GridColumn>
                                                    <GridColumn Field="BuildingNum" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Building"></GridColumn>
                                                    <GridColumn Field="StickBuilingNum" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Stick"></GridColumn>
                                                </GridColumns>
                                            </TelerikGrid>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3 row">
                                <div class="col-1"></div>
                                @if (AllowEdit)
                                {
                                    <div class="col">
                                        <button type="submit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                                            <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                                                <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                                    <!--!-->
                                                    <!--!-->
                                                    <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                                                </svg>
                                            </span> Update
                                        </button>
                                    </div>
                                }                               
                            </div>
                        </div>

                    </TabStripTab>
                    <TabStripTab Title="Customer">
                        <div class="card-body">
                            <div class="row">
                                @if (lotDetails.Customer != null)
                                {
                                    <div class="col-5">
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Customer Name:</label>
                                            <div class="col-sm-8">
                                                &nbsp;  <TelerikTextBox Enabled="false" @bind-Value="lotDetails.Customer.CustomerName" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Address:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.Customer.Address1" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Address 2:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.Customer.Address2" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">City:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.Customer.City" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">State:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.Customer.State" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Zip:</label>
                                            <div class="col-sm-8">
                                                &nbsp;  <TelerikTextBox Enabled="false" @bind-Value="lotDetails.Customer.Postcode" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-5">
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Cobuyer:</label>
                                            <div class="col-sm-8">
                                                &nbsp;  <TelerikTextBox Enabled="false" @bind-Value="lotDetails.UserContact1" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Phone:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.Customer.HomePhone" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Mobile:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.Customer.MobilePhone" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Email:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.Customer.Email" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </TabStripTab>
                    <TabStripTab Title="Contract">
                        <div class="card-body">
                            <div class="row">
                                @if (lotDetails.Customer != null && lotDetails.SalesConfig != null)
                                {
                                    <div class="col-5">
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Contract Amount:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikNumericTextBox Format="C0" Decimals="0" Enabled="false" @bind-Value="lotDetails.SalesConfig.Baseprice" Width="200px"></TelerikNumericTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Lot Premium:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikNumericTextBox Enabled="false" @bind-Value="lotDetails.SalesConfig.LotPremium" Width="200px"></TelerikNumericTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Lot Premium Incentive:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikNumericTextBox Enabled="false" @bind-Value="lotDetails.SalesConfig.LotPremiumIncentive" Width="200px"></TelerikNumericTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Job Size:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.SalesConfig.Status" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Job Unit:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.Customer.State" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                        @if (lotDetails.SalesConfig.PhasePlan != null && lotDetails.SalesConfig.PhasePlan.MasterPlan != null)
                                        {
                                            <div class="mb-3 row">
                                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Plan Code:</label>
                                                <div class="col-sm-8">
                                                    &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.SalesConfig.PhasePlan.MasterPlan.PlanNum" Width="200px"></TelerikTextBox>
                                                </div>
                                            </div>
                                            <div class="mb-3 row">
                                                <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Plan Name:</label>
                                                <div class="col-sm-8">
                                                    &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.SalesConfig.PhasePlan.MasterPlan.PlanName" Width="200px"></TelerikTextBox>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                    <div class="col-5">
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Sales Config Id:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikNumericTextBox Enabled="false" @bind-Value="lotDetails.SalesConfig.SalesconfigId" Width="200px"></TelerikNumericTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Sales Config Status:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.SalesConfig.Status" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                        @*  <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Sales Config Prev Status:</label>
                                            <div class="col-sm-8">
                                             &nbsp;   <TelerikTextBox Enabled="false" @bind-Value="lotDetails.SalesConfig.Prevstatus" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div> *@
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Base Price:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikNumericTextBox Format="C0" Decimals="0" Enabled="false" @bind-Value="lotDetails.SalesConfig.Baseprice" Width="200px"></TelerikNumericTextBox>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Estimate Settlement Date:</label>
                                            <div class="col-sm-8">
                                                &nbsp;  <TelerikDateInput Enabled="false" @bind-Value="lotDetails.SalesConfig.Estimatedsettlementdate" Width="200px"></TelerikDateInput>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Closing Date:</label>
                                            <div class="col-sm-8">
                                                &nbsp;   <TelerikDateInput Enabled="false" @bind-Value="lotDetails.SalesConfig.Closingdate" Width="200px"></TelerikDateInput>
                                            </div>
                                        </div>
                                        <div class="mb-3 row">
                                            <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Sales Contact:</label>
                                            <div class="col-sm-8">
                                                <TelerikTextBox Enabled="false" @bind-Value="lotDetails.SalesConfig.SalesContact" Width="200px"></TelerikTextBox>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </TabStripTab>
                    <TabStripTab Title="Site Contacts">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <TelerikGrid Data=@JobContacts
                                    FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                    Height="600px" RowHeight="60" PageSize="20"
                                    Sortable="true"
                                    Resizable="true"
                                    Context="jobContactsGridContext"
                                    SelectionMode="GridSelectionMode.Single"
                                    EditMode="@GridEditMode.Inline"
                                    OnUpdate="@UpdateContactHandler"
                                    OnEdit="@EditContactHandler"
                                    OnDelete="@DeleteContactHandler"
                                    OnAdd="@AddContactHandler"
                                    OnCreate="@CreateContactHandler"
                                    OnCancel="@CancelContactHandler"
                                    ConfirmDelete="true"
                                    @ref="@ContactGridRef">
                                        <GridColumns>
                                            <GridColumn Field="JobContactId" Visible="false" Editable="false" Groupable="false" />
                                            <GridColumn Field="JobNumber" Visible="false" Editable="false" Groupable="false" />
                                            <GridColumn Context="gridContext4" Field="UserId" Visible="true" Editable="true" Groupable="false">
                                                <EditorTemplate>
                                                    @{
                                                        ItemToEdit = gridContext4 as JobContactDto;
                                                        if (ItemToEdit.User?.UserId == null)//user only editable on add new, not on edit
                                                        {
                                                            <TelerikDropDownList Data="@AllUsers"
                                                            @bind-Value="@ItemToEdit.UserId"
                                                            TextField="FullName"
                                                            OnChange="@SelectedUserChanged"
                                                            ValueField="UserId"
                                                            Filterable="true"
                                                            FilterOperator="StringFilterOperator.Contains"
                                                            DefaultText="Select User"
                                                            Class="custom-dropdown">
                                                                <DropDownListSettings>
                                                                    <DropDownListPopupSettings Height="auto"></DropDownListPopupSettings>
                                                                </DropDownListSettings>
                                                            </TelerikDropDownList>
                                                        }
                                                        else
                                                        {
                                                            <span>@ItemToEdit.User.UserId</span>
                                                        }
                                                    }
                                                </EditorTemplate>
                                            </GridColumn>
                                            <GridColumn Field="User.FirstName" Title="First Name" Visible="true" Editable="false" Groupable="false" />
                                            <GridColumn Field="User.LastName" Title="Last Name" Visible="true" Editable="false" Groupable="false" />
                                            <GridColumn Field="User.EmailAddress" Title="Email" Visible="true" Editable="false" Groupable="false" />
                                            <GridColumn Context="gridContext3" Field="JobContactId" Title="Role" Visible="true" Editable="true" Groupable="false" Width="200px">
                                                <Template>
                                                    @{
                                                        var item = gridContext3 as JobContactDto;
                                                        if (item != null && item.Role != null)
                                                        {
                                                            <span>@item.Role.RoleName</span>
                                                        }
                                                    }
                                                </Template>
                                                <EditorTemplate>
                                                    @{
                                                        var ItemToEdit = gridContext3 as JobContactDto;
                                                        <TelerikDropDownList Data="@AllRoles"
                                                        @bind-Value="@ItemToEdit.RoleId"
                                                        TextField="RoleName"
                                                        ValueField="RoleId"
                                                        DefaultText="Select Type"
                                                        Filterable="true"
                                                        FilterOperator="StringFilterOperator.Contains"
                                                        Width="100%">
                                                            <DropDownListSettings>
                                                                <DropDownListPopupSettings Height="auto"></DropDownListPopupSettings>
                                                            </DropDownListSettings>
                                                        </TelerikDropDownList>

                                                    }

                                                </EditorTemplate>
                                            </GridColumn>
                                            <GridCommandColumn Context="gridContext2">
                                                @{
                                                    if(AllowEdit && AllowJobContactEdit)
                                                    {
                                                        <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true" Class="k-button-success">
                                                        </GridCommandButton>
                                                        <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                                        <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                                        <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">
                                                        </GridCommandButton>
                                                    }
                                                }

                                            </GridCommandColumn>
                                        </GridColumns>
                                        <GridToolBarTemplate>
                                            @if(AllowEdit && AllowJobContactEdit)
                                            {
                                                <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add</GridCommandButton>
                                            } 
                                            <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                        </GridToolBarTemplate>
                                    </TelerikGrid>
                                </div>
                                <div class="col-lg-6 ml-1">
                                    <div class="mb-6 row card-body">
                                        <div style="margin-bottom:8px; text-align:left">Apply these contacts to other lots?</div>
                                        <TelerikMultiSelect Data="@Subdivisions"
                                        @bind-Value="@SelectedSubdivisions"
                                        OnChange="@OnSubdivisionSelect"
                                        Placeholder="Select the subdivision to view the other lots"
                                        AutoClose="false"
                                        TextField="SubdivisionName"
                                        ValueField="SubdivisionId"
                                        Filterable="true"
                                        FilterOperator="@StringFilterOperator.Contains">
                                        </TelerikMultiSelect>
                                        <div style="margin-bottom:16px" />
                                        @if (AvailableJobs.Count != 0)
                                        {
                                            <TelerikGrid Height="500px"
                                            RowHeight="40"
                                            @bind-SelectedItems="@SelectedJobs"
                                            FilterMode="@GridFilterMode.FilterMenu"
                                            Data="AvailableJobs"
                                            SelectionMode="GridSelectionMode.Multiple">
                                                <GridColumns>
                                                    <GridCheckboxColumn SelectAllMode="GridSelectAllMode.All"></GridCheckboxColumn>
                                                    <GridColumn Field="JobNumber" Filterable="true"></GridColumn>
                                                    <GridColumn Field="LotNumber" Title="Lot"></GridColumn>
                                                    <GridColumn Field="JobPostingGroup" Title="Job Posting Group" FilterMenuType="@FilterMenuType.CheckBoxList"></GridColumn>
                                                    @*  <GridColumn Field="Subdivision.SubdivisionName" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Subdivision"></GridColumn> *@
                                                    <GridColumn Field="Phase" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Phase"></GridColumn>
                                                    <GridColumn Field="BuildingNum" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Building"></GridColumn>
                                                    <GridColumn Field="StickBuilingNum" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Stick"></GridColumn>
                                                </GridColumns>
                                            </TelerikGrid>
                                        }
                                    </div>
                                    <br />
                                    <div>@UpdateMessage</div>
                                    @if(AllowEdit && AllowJobContactEdit)
                                    {
                                        <button type="button" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary" @onclick="UpdateSelectedJobs" style="margin-left:1.5em">
                                            <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                                                <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                                    <!--!-->
                                                    <!--!-->
                                                    <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                                                </svg>
                                            </span> Update
                                        </button>
                                    }

                                </div>
                            </div>
                        </div>
                    </TabStripTab>
                    <TabStripTab Title="Documents">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-12">
                                    <TelerikGrid Data=@JobAttachments
                                    FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                    FilterMenuType="@FilterMenuType.CheckBoxList"
                                    Height="600px" RowHeight="60" PageSize="20"
                                    Sortable="true"
                                    Resizable="true"
                                    SelectionMode="GridSelectionMode.Single"
                                    EditMode="@GridEditMode.Incell"
                                    OnUpdate="@UpdateDocumentHandler"
                                    OnDelete="@DeleteDocumentHandler"
                                    OnCreate="@CreateDocumentHandler"
                                    OnCancel="@CancelDocumentHandler"
                                    OnStateInit="@( (GridStateEventArgs<JobAttachmentDto> args) => OnJobDocStateInitHandler(args) )"
                                    EnableLoaderContainer="false"
                                    @ref="@DocumentGridRef">
                                        <GridColumns>
                                            <GridColumn Field="AttachementId" Visible="false" Editable="false" Groupable="false" />
                                            <GridColumn Field="JobNumber" Title= "Job Number" Visible="true" Editable="false" Groupable="false" />
                                            <GridColumn Field="Name" Visible="true" Editable="@AllowEdit" Groupable="false" />
                                            <GridColumn Field="JobNumber" Title="Type" Visible="true" Editable="@AllowEdit" Groupable="true">
                                                <Template Context="gridContext">
                                                    @{
                                                        var ItemToEdit = gridContext as JobAttachmentDto;
                                                        <span>@ItemToEdit?.Doctype?.Doctype1</span>
                                                    }
                                                </Template>
                                                <EditorTemplate Context="gridContext">
                                                    @{
                                                        var ItemToEdit = gridContext as JobAttachmentDto;
                                                        <TelerikDropDownList Data="@AllDocumentTypes"
                                                        @bind-Value="@ItemToEdit.DoctypeId"
                                                        TextField="Doctype1"
                                                        ValueField="DoctypeId"
                                                        DefaultText="Select Type"
                                                        Width="100%">
                                                            <DropDownListSettings>
                                                                <DropDownListPopupSettings Height="auto"></DropDownListPopupSettings>
                                                            </DropDownListSettings>
                                                        </TelerikDropDownList>
                                                    }
                                                </EditorTemplate>
                                            </GridColumn>
                                            <GridColumn Field="IsActive" Visible="true" Editable="false" Groupable="false" FilterMenuType="@FilterMenuType.Menu">
                                                <Template Context="gridContext">
                                                    @{
                                                        var item = (JobAttachmentDto)gridContext;
                                                        var toggleValue = (bool)item.IsActive;
                                                        var ActiveOrInactive = (bool)item.IsActive ? "Active" : "Inactive";
                                                        <TelerikToggleButton Enabled="AllowEdit" @bind-Selected="toggleValue"
                                                        OnClick="@(() => OnActiveInactiveToggle(!toggleValue, item))"> @ActiveOrInactive </TelerikToggleButton>
                                                    }
                                                </Template>
                                                <FilterMenuTemplate Context="gridContext">
                                                    @foreach (var option in ActiveInactiveFilterList)
                                                    {
                                                        <div>
                                                            <TelerikCheckBox Value="@(IsCheckboxInCurrentFilter(gridContext.FilterDescriptor, option))"
                                                            TValue="bool"
                                                            ValueChanged="@((value) => UpdateActiveInactiveFilter(value, option, gridContext))"
                                                            Id="@($"{option}")">
                                                            </TelerikCheckBox>
                                                            <label for="@($"{option}")">
                                                                @option
                                                            </label>
                                                        </div>
                                                    }
                                                </FilterMenuTemplate>
                                                <FilterMenuButtonsTemplate Context="filterContext">
                                                    <TelerikButton OnClick="@(async _ => await filterContext.FilterAsync())">Filter </TelerikButton>
                                                    <TelerikButton OnClick="@(() => ClearActiveInactiveFilterAsync(filterContext))">Clear</TelerikButton>
                                                </FilterMenuButtonsTemplate>
                                            </GridColumn> 
                                            <GridCommandColumn Context="gridContext">
                                                @{
                                                    var item = gridContext as JobAttachmentDto;
                                                    if(!item.Path.StartsWith("\\"))
                                                    {
                                                        <GridCommandButton Command="Download" OnClick="@DownloadItem" Class="k-button-success">Download</GridCommandButton>
                                                        <GridCommandButton Command="Download" OnClick="@ViewItem" Class="k-button-success">View</GridCommandButton> 
                                                    }
                                                }
                                            </GridCommandColumn>
                                            <GridCommandColumn Context="gridContext">
                                                @{
                                                    if(AllowEdit){                                                       
                                                        <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Title="Permanent Delete" Class="k-button-danger tooltip-target"></GridCommandButton>
                                                    }
                                                }
                                            </GridCommandColumn>
                                        </GridColumns>
                                        <GridToolBarTemplate>
                                            @{
                                                if(AllowEdit)
                                                {
                                                    <GridCommandButton Command="MyAddDocumentCommand" Icon="@FontIcon.Plus" OnClick="@AddDocumentFromToolbar" Class="k-button-add">Add Document</GridCommandButton>
                                                }
                                            }
                                            <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                        </GridToolBarTemplate>
                                        <GridSettings>
                                            <GridPopupEditSettings Width="600px"
                                            Height="600px"
                                            Title="Contact">
                                            </GridPopupEditSettings>
                                        </GridSettings>
                                    </TelerikGrid>
                                </div>
                            </div>
                        </div>
                    </TabStripTab>
                    <TabStripTab Title="Selected Options">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-12">
                                    @if(SelectedOptionsData == null){
                                        <p>Options data not loaded</p>
                                    }
                                    else if(SelectedOptionsData.Count == 0)
                                    {
                                        <p>No options selected</p>
                                    }
                                    else
                                    {
                                        <TelerikGrid Data=@SelectedOptionsData
                                        Groupable="true"
                                        FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                        Height="600px" RowHeight="60" PageSize="20"
                                        Sortable="true"
                                        Resizable="true">
                                            <GridColumns>
                                                <GridColumn Field="OptionCode" Title="Option Code" Editable="false" Width="100px" FilterMenuType="@FilterMenuType.CheckBoxList"></GridColumn>
                                                <GridColumn Field="OptionDesc" Title="Option Desc" Editable="false" Width="200px"></GridColumn>
                                                <GridColumn Field="Qty" Title="Quantity" Editable="false" Width="50px"></GridColumn>
                                                <GridColumn Field="SelectionNotes" Title="Selections" Editable="false" Width="200px">
                                                    <Template Context="optionGridContext">
                                                        @{
                                                            var item = optionGridContext as TbBuiltOptionDto;
                                                            if (item.BuildAttributeItems != null && item.BuildAttributeItems.Any())
                                                            {
                                                                foreach (var attr in item.BuildAttributeItems.Where(x => !string.IsNullOrWhiteSpace(x.AttrGroupAssignment.AttributeItem.Description)))
                                                                {
                                                                    <span>@($"{attr.AttrGroupAssignment.AttributeGroup.Description} : {attr.AttrGroupAssignment.AttributeItem.Description}")</span>

                                                                    <br />
                                                                }
                                                            }
                                                            else
                                                            {
                                                                <span>No selections</span>
                                                            }
                                                        }
                                                    </Template>
                                                </GridColumn>
                                            </GridColumns>
                                            <GridToolBarTemplate>
                                                <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                                <GridCommandButton Command="ExcelExport" Icon="@FontIcon.FileExcel">Export to Excel</GridCommandButton>
                                            </GridToolBarTemplate>
                                            <GridExport>
                                                <GridExcelExport FileName="@selectedOptionsFileName" AllPages="true" />
                                            </GridExport>
                                            <GridSettings>
                                                <GridPopupEditSettings Width="600px"
                                                Height="600px"
                                                Title="Contact">
                                                </GridPopupEditSettings>
                                            </GridSettings>
                                        </TelerikGrid>
                                    }

                                </div>
                            </div>
                        </div>
                    </TabStripTab>
                </TelerikTabStrip>
                @*            <br />
            <br />
            <button type="submit" class="btn btn-primary">Update</button>*@
            </EditForm>
        }

    </ChildContent>
    <ErrorContent>
        <div class="alert alert-danger">
            Oops! There's an issue with this Job
        </div>
    </ErrorContent>
</ErrorBoundary>

<ERP.Web.Components.UploadJobDocument @ref="AddJobDocumentModal" JobNumber=@JobNumber HandleAddSubmit="HandleValidAddDocumentSubmit"></ERP.Web.Components.UploadJobDocument>

@code {

    public class BreadcrumbItem
    {
        public FontIcon? Icon { get; set; }
        public string Text { get; set; }
        public string Url { get; set; }
    }

    [Parameter]
    public string JobNumber { get; set; }
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    private string? selectedOptionsFileName { get; set; } = string.Empty;
    public string NewStickName { get; set; }
    public string NewBuildingNumber { get; set; }
    public string NewPhase { get; set; }
    public List<LotStatusDto>  LotStatusList {get; set; }= new List<LotStatusDto>();
    public List<GarageOrientationDto> GarageOrientationList { get; set; } = new List<GarageOrientationDto>();
    public List<JobConstructionTypeDto> ConstructionTypesList { get; set; } = new List<JobConstructionTypeDto>();
    public JobDto lotDetails { get; set; } = new JobDto();
    public List<JobSelected>? JobsThisSubdivision { get; set; } = new List<JobSelected>();
    public List<JobDto> SelectLots { get; set; } = new List<JobDto>();
    public List<string> AllLots { get; set; }
    public List<string> AllPhasesInSubdivision { get; set; } = new List<string> { "Add New" };
    public List<string> AllBuildingNumsInSubdivision { get; set; } = new List<string> { "Add New" };
    public List<string> AllSticksInSubdivision { get; set; } = new List<string> { "Add New" };
    public string? SelectedLot { get; set; }
    public bool SelectAllThisSubdivision { get; set; } = false;
    public int ActiveTabIndex { get; set; }
    private TelerikMultiSelect<string, string> MultiSelectRef;
    private TelerikDropDownList<string, string?> StickDropdownListRef; 
    private TelerikDropDownList<string, string?> BuildingDropdownListRef; 
    private TelerikDropDownList<string, string?> PhaseDropdownListRef;
    private IEnumerable<JobDto> SelectedJobs { get; set; } = new List<JobDto>();
    private List<JobDto> AvailableJobs { get; set; } = new List<JobDto>();
    public IEnumerable<BreadcrumbItem> BreadCrumbItems = new List<BreadcrumbItem>();
    public List<JobContactDto>? JobContacts { get; set; }
    private TelerikGrid<JobContactDto>? ContactGridRef { get; set; }
    private string UpdateMessage { get; set; } = "";
    public List<SubdivisionDto> Subdivisions { get; set; }
    public List<int> SelectedSubdivisions { get; set; }
    protected ERP.Web.Components.UploadJobDocument? AddJobDocumentModal { get; set; }
    private TelerikGrid<JobAttachmentDto>? DocumentGridRef { get; set; }
    public List<JobAttachmentDto>? JobAttachments { get; set; }
    public List<DoctypeDto>? AllDocumentTypes;
    public List<RoleDto>? AllRoles { get; set; }
    public List<UserDto>? AllUsers;
    public JobContactDto? ItemToEdit { get; set; }
    private string? SelectedJob { get; set; }
    public List<TbBuiltOptionDto>? SelectedOptionsData { get; set; } = new List<TbBuiltOptionDto>();
    private bool AllowEdit { get; set; } = true;
    private bool AllowJobContactEdit { get; set; } = true;
    private bool IsLoading { get; set; } = false;
    public List<string> ActiveInactiveFilterList { get; set; } = new List<string> { "Active", "Inactive" };
    public List<string> ActiveInactiveFilterCurrentSelection { get; set; } = new List<string>();

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    public class JobSelected
    {
        public bool Selected { get; set; }
        public string JobNumber { get; set; }
    }

    async Task TabChangedHandler(int newIndex)
    {
        SelectedSubdivisions = new List<int> { lotDetails.SubdivisionId ?? 0 };
        var JobsInSubdivision = Subdivisions.Where(x => SelectedSubdivisions.Contains(x.SubdivisionId)).ToList().Join(SelectLots, a => a.SubdivisionId, b => b.SubdivisionId, (a, b) => b).ToList();
        AvailableJobs = JobsInSubdivision;
        SelectedJobs = new List<JobDto>();
        ActiveTabIndex = newIndex;
    }


    protected override async Task OnParametersSetAsync()
    {
        if(JobNumber != null)
        {           
            SelectedJob = JobNumber;
            var stringDate = DateTime.Now.ToString("MM-dd-yyyy");
            selectedOptionsFileName = $"SelectedOptions-{JobNumber}-{stringDate}";
            SubdivisionJobPickService.JobNumber = JobNumber;
            if (lotDetails != null)
            {
                var jobDetailsTask = SubdivisionService.GetLotAsync(JobNumber);
                var jobContactsTask = SubdivisionService.GetJobContactsAsync(JobNumber);
                var jobDocumentsTask = DocumentService.GetJobDocumentsAsync(JobNumber);
                var getSelectedOptionsTask = SelectedOptionsService.GetAllSelectedOptionsByJobAsync(JobNumber);
                await Task.WhenAll(new Task[] { getSelectedOptionsTask, jobDetailsTask, jobContactsTask, jobDocumentsTask });
                lotDetails = jobDetailsTask.Result.Value;
                JobContacts = jobContactsTask.Result.Value;
                JobAttachments = jobDocumentsTask.Result.Value;
                var getSelectedOptionsData = getSelectedOptionsTask.Result.Value;
                SelectedOptionsData = getSelectedOptionsData.Where(x => x.BuilderApproved == 1 && x.MostRecent == true && x.Removed == false).ToList();//TODO: don't have duplicates, filter denise approved
                foreach(var option in SelectedOptionsData)
                {
                    if (option.BuildAttributeItems != null && option.BuildAttributeItems.Any())
                    {
                        List<string> attributeList = new List<string>();
                        var debug = option.BuildAttributeItems.Where(x => x.AttrGroupAssignment == null).ToList();

                        foreach (var attr in option.BuildAttributeItems)
                        {
                            if (attr.AttrGroupAssignment != null)
                            {
                                if (attr.AttrGroupAssignment.AttributeItem != null && !string.IsNullOrWhiteSpace(attr.AttrGroupAssignment.AttributeItem.Description))
                                {
                                    attributeList.Add($"{attr.AttrGroupAssignment.AttributeGroup.Description} : {attr.AttrGroupAssignment.AttributeItem.Description}");
                                }
                            }
                        }

                        option.SelectionNotes = string.Join(",", attributeList);
                    }
                }
                //SelectedOptionsData = getSelectedOptionsData.ToList();//TODO: don't have duplicates, filter denise approved
                if (lotDetails.SubdivisionId != null)
                {
                    //preselect the subdivision containing the current lot for apply contacts other lots
                    SelectedSubdivisions = new List<int> { lotDetails.SubdivisionId ?? 0 };
                    var jobsInSubdivision = (await SubdivisionService.GetJobsAsync((int)lotDetails.SubdivisionId)).Value;
                    AvailableJobs = jobsInSubdivision;
                    AllPhasesInSubdivision.AddRange(jobsInSubdivision.Select(x => x.Phase).Distinct().ToList());
                    AllBuildingNumsInSubdivision.AddRange(jobsInSubdivision.Select(x => x.BuildingNum).Distinct().ToList());
                    AllSticksInSubdivision.AddRange(jobsInSubdivision.Select(x => x.StickBuilingNum).Distinct().ToList());
                }
            }
        }
        else if(SubdivisionJobPickService.JobNumber != null)
        {
            JobNumber = SubdivisionJobPickService.JobNumber;
            SelectedJob = JobNumber;
            var jobDetailsTask = SubdivisionService.GetLotAsync(JobNumber);
            var jobContactsTask = SubdivisionService.GetJobContactsAsync(JobNumber);
            var jobDocumentsTask = DocumentService.GetJobDocumentsAsync(JobNumber);
            var getSelectedOptionsTask = SelectedOptionsService.GetAllSelectedOptionsByJobAsync(JobNumber);
            await Task.WhenAll(new Task[] { getSelectedOptionsTask, jobDetailsTask, jobContactsTask, jobDocumentsTask });
            lotDetails = jobDetailsTask.Result.Value;
            JobContacts = jobContactsTask.Result.Value;
            JobAttachments = jobDocumentsTask.Result.Value;
            var getSelectedOptionsData = getSelectedOptionsTask.Result.Value;
            SelectedOptionsData = getSelectedOptionsData.Where(x => x.BuilderApproved == 1 && x.MostRecent == true && x.Removed == false).ToList();//filter approved, non removed
            foreach (var option in SelectedOptionsData)
            {
                if (option.BuildAttributeItems != null && option.BuildAttributeItems.Any())
                {
                    List<string> attributeList = new List<string>();
                    foreach (var attr in option.BuildAttributeItems)
                    {
                        attributeList.Add($"{attr.AttrGroupAssignment.AttributeGroup.Description} : {attr.AttrGroupAssignment.AttributeItem.Description}");
                    }
                    option.SelectionNotes = string.Join(",", attributeList);
                }
            }
            if (lotDetails != null)
            {
                if (lotDetails.SubdivisionId != null)
                {
                    //preselect the subdivision containing the current lot for apply contacts other lots
                    SelectedSubdivisions = new List<int> { lotDetails.SubdivisionId ?? 0 };
                    var jobsInSubdivision = (await SubdivisionService.GetJobsAsync((int)lotDetails.SubdivisionId)).Value;
                    AvailableJobs = jobsInSubdivision;
                    AllPhasesInSubdivision.AddRange(jobsInSubdivision.Select(x => x.Phase).Distinct().ToList());
                    AllBuildingNumsInSubdivision.AddRange(jobsInSubdivision.Select(x => x.BuildingNum).Distinct().ToList());
                    AllSticksInSubdivision.AddRange(jobsInSubdivision.Select(x => x.StickBuilingNum).Distinct().ToList());
                }               
            }
        }
        // await Task.Delay(1);//hack to make sure things have rendered
        StateHasChanged();
    }

    protected override async Task OnInitializedAsync()
    {
        SubdivisionJobPickService.OnChanged += JobChangedHandler;
        BreadCrumbItems = new List<BreadcrumbItem>
        {
            new BreadcrumbItem { Url = "/", Icon = FontIcon.Home },
            new BreadcrumbItem { Text = "Lots", Url = $"Lots" },
            new BreadcrumbItem { Text = "Details", Url = $"LotDetails" },
        };
        var allLotsTask = SubdivisionService.GetAllJobsAsync();
        var subdivisionsTask = SubdivisionService.GetSubdivisionsAsync();
        var rolesTask = SubdivisionService.GetAllRolesAsync();
        var getStatusTask = JobService.GetJobStatusList();
        var getOrientationsTask = JobService.GetGarageOrientationsList();
        var getConstructionTypesTask = JobService.GetJobConstructionTypes();
        // var usersTask = SubdivisionService.GetUserContactsAsync();
        var documentsTypesTask = DocumentService.GetDocumentTypesAsync();
        await Task.WhenAll(new Task[] { allLotsTask, subdivisionsTask, rolesTask, documentsTypesTask, getStatusTask, getOrientationsTask, getConstructionTypesTask });
        Subdivisions = subdivisionsTask.Result.Value.Select(x => new SubdivisionDto() { SubdivisionName = x.SubdivisionName, SubdivisionId = x.SubdivisionId }).ToList();
        SelectLots = allLotsTask.Result.Value.ToList();
        AllLots = SelectLots.Select(x => x.JobNumber).ToList();
        AllDocumentTypes = documentsTypesTask.Result.Value;
        AllRoles = rolesTask.Result.Value;
        LotStatusList = getStatusTask.Result.Value;
        GarageOrientationList = getOrientationsTask.Result.Value;
        ConstructionTypesList = getConstructionTypesTask.Result.Value;
        //  AllUsers = usersTask.Result.Value;
        AllUsers = (await SubdivisionService.GetUserContactsFromAzureAsync()).Value;

        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userRoleOperations = user.User.IsInRole("Operations");
        var userroleConstruction = user.User.IsInRole("ConstructionSuper");        
        AllowEdit = userRoleAdmin || userRoleOperations;
        var userRoleJobContactEdit = user.User.IsInRole("JobContactEditor");
        AllowJobContactEdit = userRoleJobContactEdit;
    }
    async Task JobChangedHandler()
    {
        var selected = SubdivisionJobPickService.JobNumber;
        if (JobNumber != selected)//check if actual change, so not calling this multiple times
        {
            JobNumber = selected;
            if(JobNumber != null)
            {
                NavManager.NavigateTo($"lotdetails/{JobNumber}");
            }

            // lotDetails = (await SubdivisionService.GetLotAsync(JobNumber)).Value;

            // if (lotDetails != null)
            // {
            //     JobContacts = (await SubdivisionService.GetJobContactsAsync(JobNumber)).Value;
            //     if (lotDetails.SubdivisionId != null)
            //     {
            //         //preselect the subdivision containing the current lot for apply contacts other lots
            //         AllPhasesInSubdivision.RemoveAll(x => !"Add New".Equals(x));
            //         AllBuildingNumsInSubdivision.RemoveAll(x => !"Add New".Equals(x));
            //         AllSticksInSubdivision.RemoveAll(x => !"Add New".Equals(x));
            //         SelectedJobs.Clear();
            //         SelectedSubdivisions = new List<int> { lotDetails.SubdivisionId ?? 0 };
            //         var jobsInSubdivision = (await SubdivisionService.GetJobsAsync((int)lotDetails.SubdivisionId)).Value;
            //         AvailableJobs = jobsInSubdivision.Select(x => x.JobNumber).ToList();
            //         AllPhasesInSubdivision.AddRange(jobsInSubdivision.Select(x => x.Phase).Distinct().ToList());
            //         AllBuildingNumsInSubdivision.AddRange(jobsInSubdivision.Select(x => x.BuildingNum).Distinct().ToList());
            //         AllSticksInSubdivision.AddRange(jobsInSubdivision.Select(x => x.StickBuilingNum).Distinct().ToList());
            //     }
            //     JobAttachments = (await DocumentService.GetJobDocumentsAsync(JobNumber)).Value;

            // }
            // StateHasChanged();
        }

    }
    private void AddDocumentFromToolbar(GridCommandEventArgs args)
    {
        AddJobDocumentModal.Show();
        StateHasChanged();
    }

    private async void HandleValidAddDocumentSubmit(ResponseModel responseItem)
    {
        JobAttachments = (await DocumentService.GetJobDocumentsAsync(JobNumber)).Value;
        AddJobDocumentModal.Hide();

        if (responseItem != null)
        {
            ShowSuccessOrErrorNotification(responseItem.Message, responseItem.IsSuccess);
        }        
        else
        {
            var message = (!string.IsNullOrWhiteSpace(responseItem.Message)) ? responseItem.Message : "No file uploaded or invalid file type.";
            ShowSuccessOrErrorNotification(message, false);
        }

        StateHasChanged();
    }

    private void SelectAllHandler()
    {
        if (SelectAllThisSubdivision == false)
        {
            foreach (var job in JobsThisSubdivision)
            {
                job.Selected = true;
            }
        }
        else
        {
            foreach (var job in JobsThisSubdivision)
            {
                job.Selected = false;
            }
        }
    }

    private async void HandleValidSubmit()
    {
        //updates the lot
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        lotDetails.UpdatedBy = userName;
        lotDetails.HomeOrientationPerPlan = lotDetails.BoolHomeOrientationPerPlan;
        if (lotDetails.Phase == "Add New")
        {
            lotDetails.Phase = NewPhase;
            AllPhasesInSubdivision.Add(NewPhase);
            PhaseDropdownListRef?.Rebind();
        }
        if (lotDetails.BuildingNum == "Add New")
        {
            lotDetails.BuildingNum = NewBuildingNumber;
            AllBuildingNumsInSubdivision.Add(NewBuildingNumber);
            BuildingDropdownListRef?.Rebind();
        }
        if (lotDetails.StickBuilingNum == "Add New")
        {
            lotDetails.StickBuilingNum = NewStickName;
            AllSticksInSubdivision.Add(NewStickName);
            StickDropdownListRef?.Rebind();
        }

        var selectedJobs = JobsThisSubdivision.Where(x => x.Selected == true).ToList();
        var updateResponse = await SubdivisionService.UpdateLotAsync(lotDetails);
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
        if (SelectedJobs.Count() > 0)
        {
            var selectedJobNums = SelectedJobs.Select(x => x.JobNumber).ToList();
            var updateMultipleResponse = await SubdivisionService.UpdateLotsAsync(lotDetails, selectedJobNums);
            ShowSuccessOrErrorNotification(updateMultipleResponse.Message, updateMultipleResponse.IsSuccess);
        }
        var jobsInSubdivision = (await SubdivisionService.GetJobsAsync((int)lotDetails.SubdivisionId)).Value;
        AvailableJobs = jobsInSubdivision;
        SelectedJobs = new List<JobDto>();
        StateHasChanged();
    }
    void OnSubdivisionSelect(object selectedSubdivisions)
    {
        SelectedSubdivisions = selectedSubdivisions as List<int>;
        if (SelectedSubdivisions != null)//SelectedSubdivisions is null if user click in and out of the dropdown without picking the first time after page loads (later if something has been picked and then unselected, it is empty list)
        {
            var JobsInSubdivision = Subdivisions.Where(x => SelectedSubdivisions.Contains(x.SubdivisionId)).ToList().Join(SelectLots, a => a.SubdivisionId, b => b.SubdivisionId, (a, b) => b).ToList();
            JobsInSubdivision.RemoveAll(x => x.JobNumber == lotDetails.JobNumber);
            AvailableJobs = JobsInSubdivision;
        }
    }

    // void ToggleSelectAll(bool selectAll)
    // {
    //     SelectedJobs.Clear();

    //     if (selectAll)
    //     {
    //         SelectedJobs.AddRange(AvailableJobs);
    //     }

    //     MultiSelectRef.Rebind();
    // }

    // bool IsAllSelected()
    // {
    //     return SelectedJobs.Count == AvailableJobs.Count;

    //     // in this example we do a simple count check for performance
    //     // all items in the dropdown should be in the data anyway
    //     // caveat: virtualization does not work that way, but for it selecting all
    //     // would be a completely different feature anyway that will require asking the server for data
    //     // so it is beyond the scope of this article as it depends heavily on the use case and needs
    // }

    // // for the item checkboxes
    // bool GetChecked(string text)
    // {
    //     return SelectedJobs.Contains(text);
    // }

    public async void UpdateSelectedJobs()
    {
        UpdateMessage = "Updating. Please wait...";
        var selectedJobNumbers = SelectedJobs.Select(x => x.JobNumber).ToList();
        var updateJob = new UpdateJob()
            {
                JobNumber = JobNumber,
                JobsToUpdate = selectedJobNumbers
            };
        var updateResponse = await SubdivisionService.UpdateMultipleJobsContactsAsync(updateJob);
        UpdateMessage = "";
        SelectedJobs = new List<JobDto>();
        MultiSelectRef.Rebind();
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
        StateHasChanged();
    }

    void EditContactHandler(GridCommandEventArgs args)
    {
        JobContactDto item = (JobContactDto)args.Item;
    }

    async Task UpdateContactHandler(GridCommandEventArgs args)
    {
        JobContactDto item = (JobContactDto)args.Item;
        var updateJobContactResponse = await SubdivisionService.UpdateJobContactAsync(item);
        JobContacts = (await SubdivisionService.GetJobContactsAsync(JobNumber)).Value;
        ShowSuccessOrErrorNotification(updateJobContactResponse.Message, updateJobContactResponse.IsSuccess);
    }

    async Task DeleteContactHandler(GridCommandEventArgs args)
    {
        JobContactDto item = (JobContactDto)args.Item;
        var deleteContactResponse = await SubdivisionService.DeleteJobContactAsync(item);
        JobContacts = (await SubdivisionService.GetJobContactsAsync(JobNumber)).Value;
        ShowSuccessOrErrorNotification(deleteContactResponse.Message, deleteContactResponse.IsSuccess);
    }

    async Task CreateContactHandler(GridCommandEventArgs args)
    {
        JobContactDto item = (JobContactDto)args.Item;
        item.User = AllUsers.FirstOrDefault(x => x.UserId == item.UserId);
        item.Role = AllRoles.FirstOrDefault(x => x.RoleId == item.RoleId);
        item.JobNumber = JobNumber;
        var responseItem = await SubdivisionService.AddJobContactAsync(item);
        ShowSuccessOrErrorNotification(responseItem.Message, responseItem.IsSuccess);
        JobContacts = (await SubdivisionService.GetJobContactsAsync(JobNumber)).Value;
    }

    void SelectedUserChanged()
    {
        var userSelected = AllUsers.FirstOrDefault(x => x.UserId == ItemToEdit.UserId);
        if (userSelected != null)
        {
            ItemToEdit.User = new UserDto() { FirstName = userSelected.FirstName, LastName = userSelected.LastName, EmailAddress = userSelected.EmailAddress };
        }
    }

    async Task AddContactHandler(GridCommandEventArgs args)
    {
        JobContactDto item = (JobContactDto)args.Item;
        //TODO: user id editable in add but not edit

    }

    async Task CancelContactHandler(GridCommandEventArgs args)
    {
        JobContactDto item = (JobContactDto)args.Item;
    }

    void EditDocumentHandler(GridCommandEventArgs args)
    {
        JobAttachmentDto item = (JobAttachmentDto)args.Item;
    }

    async Task UpdateDocumentHandler(GridCommandEventArgs args)
    {
        JobAttachmentDto item = (JobAttachmentDto)args.Item;
        await DocumentService.UpdateJobDocumentAsync(item);
        JobAttachments = (await DocumentService.GetJobDocumentsAsync(JobNumber)).Value;
    }

    async Task DeleteDocumentHandler(GridCommandEventArgs args)
    {
        JobAttachmentDto item = (JobAttachmentDto)args.Item;

        bool confirmPermanentDelete = await Dialogs.ConfirmAsync("The file will be deleted permanently. This cannot be undone.");

        if (confirmPermanentDelete)
        {
            IsLoading = true;
            StateHasChanged();
            await DocumentService.DeleteJobDocumentAsync(item);
            JobAttachments = (await DocumentService.GetJobDocumentsAsync(JobNumber)).Value;
            Console.WriteLine("The user is sure, continue.");
            IsLoading = false;
        }
        else
        {
            Console.WriteLine("The user changed their mind");
        }
    }

    async Task CreateDocumentHandler(GridCommandEventArgs args)
    {
        JobAttachmentDto item = (JobAttachmentDto)args.Item;
        item.JobNumber = JobNumber;
        await DocumentService.AddJobDocumentAsync(item);
        JobAttachments = (await DocumentService.GetJobDocumentsAsync(JobNumber)).Value;
    }

    async Task CancelDocumentHandler(GridCommandEventArgs args)
    {
        JobAttachmentDto item = (JobAttachmentDto)args.Item;
    }

    async Task OnActiveInactiveToggle(bool value, JobAttachmentDto jobAttachment)
    {
        jobAttachment.IsActive = value;
        var updateJobDocumentResponse = await DocumentService.UpdateJobDocumentAsync(jobAttachment);
        DocumentGridRef.Rebind();
    }

    private async Task ClearActiveInactiveFilterAsync(FilterMenuTemplateContext filterContext)
    {
        await filterContext.ClearFilterAsync();
        ActiveInactiveFilterCurrentSelection.Clear();
        DocumentGridRef.Rebind();
    }

    private bool IsCheckboxInCurrentFilter(CompositeFilterDescriptor filterDescriptor, string option)
    {
        return ActiveInactiveFilterCurrentSelection.Contains(option);
    }

    private void UpdateActiveInactiveFilter(bool isChecked, string optionValue, FilterMenuTemplateContext context)
    {
        var filterDescriptor = context.FilterDescriptor;
        filterDescriptor.LogicalOperator = FilterCompositionLogicalOperator.Or;
        var value = optionValue == "Active" ? true : false;
        if (!isChecked)
        {
            // find and remove the filter descriptor for this checkbox
            var removeFilters = filterDescriptor.FilterDescriptors.Where(x =>
            {
                var fd = x as FilterDescriptor;
                if ((fd.Operator == FilterOperator.IsEqualTo && fd.Value == null) || (fd.Operator == FilterOperator.IsEqualTo && (bool)fd.Value == value))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }).ToList();
            foreach (var filter in removeFilters)
            {
                filterDescriptor.FilterDescriptors.Remove(filter);
            }
            ActiveInactiveFilterCurrentSelection.Remove(optionValue);
        }
        else
        {
            // add a filter descriptor for this checkbox
            filterDescriptor.FilterDescriptors.Add(new FilterDescriptor()
                {
                    Member = nameof(JobAttachmentDto.IsActive),
                    MemberType = typeof(bool),
                    Operator = FilterOperator.IsEqualTo,
                    Value = value
                });
            if (!ActiveInactiveFilterCurrentSelection.Contains(optionValue))
            {
                ActiveInactiveFilterCurrentSelection.Add(optionValue);
            }
        }
    }

    protected async Task DownloadItem(GridCommandEventArgs args)
    {
        var itemToDownload = args.Item as JobAttachmentDto;
        IsDownloadStarted = 1;

        StateHasChanged();

        var responseBytes = await DocumentService.DownloadDocumentAsync(itemToDownload.AttachmentId);
        var fileName = itemToDownload.Name;
        var getMimeType = DocumentService.GetMimeType(fileName);
        await JS.InvokeAsync<object>("saveAsFile", fileName, Convert.ToBase64String(responseBytes.Value));
        IsDownloadStarted = 2;
    }

    protected async Task ViewItem(GridCommandEventArgs args)
    {
        var itemToDownload = args.Item as JobAttachmentDto;
        IsDownloadStarted = 1;
        StateHasChanged();

        var responseBytes = await DocumentService.DownloadDocumentAsync(itemToDownload.AttachmentId);
        var fileName = itemToDownload.Name;
        var getMimeType = DocumentService.GetMimeType(fileName);
        await JS.InvokeAsync<object>("viewMyFile", Convert.ToBase64String(responseBytes.Value), getMimeType, fileName);
        IsDownloadStarted = 2;
    }

    public int IsDownloadStarted { get; set; } = 0;

    public JobContactDto? EditJobContactModel { get; set; }

    private async Task OnValidSubmit()
    {
        //TODO: is this method used?
        var updateResponse = await SubdivisionService.UpdateJobContactAsync(EditJobContactModel);
        JobContacts = (await SubdivisionService.GetJobContactsAsync(JobNumber)).Value;
        await ExitEditAsync();
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
    }

    private async Task OnCancel()
    {
        await ExitEditAsync();
    }

    private async Task ExitEditAsync()
    {
        var state = ContactGridRef?.GetState();
        state.OriginalEditItem = null;
        state.EditItem = null;
        state.InsertedItem = null;

        await ContactGridRef?.SetStateAsync(state);
    }

    async Task OnJobDocStateInitHandler(GridStateEventArgs<JobAttachmentDto> args)
    {
        if (!ActiveInactiveFilterCurrentSelection.Contains("Active"))
        {
            ActiveInactiveFilterCurrentSelection.Add("Active");
        }

        var collapsedItemsState = new GridState<JobAttachmentDto>()
            {
                FilterDescriptors = new List<IFilterDescriptor>()
                {
                    new CompositeFilterDescriptor()
                    {
                        FilterDescriptors = new FilterDescriptorCollection()
                        {
                            new FilterDescriptor()
                            {
                                Member = nameof(JobAttachmentDto.IsActive),
                                MemberType = typeof(bool),
                                Operator = FilterOperator.IsEqualTo,
                                Value = true
                            }
                        }
                    }
                },
            };

        args.GridState = collapsedItemsState;
    }

    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }

    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= JobChangedHandler;
    }
}
