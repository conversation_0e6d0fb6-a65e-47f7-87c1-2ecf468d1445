﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ScheduleChainSchedAid
{
    public int ScheduleChainSchedAidId { get; set; }

    public int ScheduleChainId { get; set; }

    public int ScheduleAid { get; set; }

    public int ScheduleChainSeq { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ScheduleChain ScheduleChain { get; set; } = null!;
}
