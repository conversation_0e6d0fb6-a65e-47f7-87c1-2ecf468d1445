﻿@using ERP.Data.Models.Dto;
@inject ColorSchemeService ColorSchemeService
@inject OptionService OptionService
@inject PlanService PlanService
@inject SubdivisionService SubdivisionService

<TelerikWindow Modal="true"
@bind-Visible="@IsModalVisible"
Width="800px"
Height="800px"
CloseOnOverlayClick="true">
    <WindowTitle>
        Add Color Scheme
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@ColorSchemeToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />

            <div class="k-validation-summary k-messagebox k-messagebox-error p-0" role="alert">
                <ValidationSummary />
            </div>
            <p>Select Subdivision/Plan/Elevation:</p>
            <p>
                <label>Subdivision</label>
                <TelerikDropDownList @bind-Value="@SelectedSubdivisionId"
                Data="@AllSubdivisions"
                TextField="SubdivisionName"
                ValueField="SubdivisionId"
                DefaultText="Select Subdivision"
                OnChange="@CascadePlans"
                Filterable="true"
                Width="100%">
                    <DropDownListSettings>
                        <DropDownListPopupSettings Height="200px"></DropDownListPopupSettings>
                    </DropDownListSettings>
                </TelerikDropDownList>
            </p>
            <p><label>Select Plan</label>
                <TelerikDropDownList @bind-Value="@SelectedPlanId"
                Data="@PlansInSubdivision"
                TextField="PlanName"
                ValueField="PhasePlanId"
                DefaultText="Select Plan"
                OnChange="@CascadeOptions"
                Filterable = "true"
                Width="100%">
                    <DropDownListSettings>
                        <DropDownListPopupSettings Height="200px"></DropDownListPopupSettings>
                    </DropDownListSettings>
                </TelerikDropDownList>
            </p>           
            <p>
                <label>Select Elevations(s)</label>              
                <TelerikMultiSelect Class="selected-items-container"
                Context="multiSelectContext"
                @ref="MultiSelectRef"
                TextField="OptionCode"
                ValueField="PlanOptionId"
                TagMode="@MultiSelectTagMode.Multiple"
                MaxAllowedTags="5"
                Data="@AllElevationsInPlan"
                ClearButton="true"
                @bind-Value="@SelectedOptionIds"
                AutoClose="false"
                Filterable="true"
                OnChange="@OptionsChanged"
                FilterOperator="StringFilterOperator.Contains"
                Placeholder="Select Options">
                    <HeaderTemplate>
                        <label style="padding: 4px 8px;">
                            <TelerikCheckBox TValue="bool"
                            Value="@IsAllSelected()"
                            ValueChanged="@( (bool v) => ToggleSelectAll(v) )">
                            </TelerikCheckBox>
                            &nbsp;Select All
                        </label>
                    </HeaderTemplate>
                    <ItemTemplate>
                        <input type="checkbox"
                        class="k-checkbox k-checkbox-md"
                        checked="@GetChecked(multiSelectContext.PlanOptionId)">
                        @multiSelectContext.OptionCode - @multiSelectContext.ModifiedOptionDesc
                    </ItemTemplate>                   
                </TelerikMultiSelect>
            </p>
            <label>Select Color Scheme Name</label>
            <TelerikDropDownList @bind-Value="@SelectedSchemeName"
            Data="@SchemesThisSubdivision"
            DefaultText="Select Scheme Name"
            OnChange="@ChangeSelectedSchemeName"
            Filterable="true"
            Width="100%">
                <DropDownListSettings>
                    <DropDownListPopupSettings Height="200px"></DropDownListPopupSettings>
                </DropDownListSettings>
            </TelerikDropDownList>
            <br />
            @if (SelectedSchemeName == "Add New" || SelectedSchemeName == "" || SelectedSchemeName == null)
            {
                <br />
                <label>New Color Scheme Name</label>
                <TelerikTextBox @bind-Value="ColorSchemeToAdd.ColorSchemeNum"></TelerikTextBox>
            }           
            <br />
            <br />
            <label>New Materials/Colors or Copy from another scheme?</label>
            <TelerikRadioGroup Data="@NewOrCopyOptions"
            OnChange="@NewOrCopyChanged"
            @bind-Value="@NewOrCopySelected">
            </TelerikRadioGroup>
            <label>Materials/Colors</label>
            @if(NewOrCopySelected == "Copy")
            {
                <label>Selelct Material Color Scheme to Copy</label>
                <TelerikDropDownList @bind-Value="@SelectedCopySchemeId"
                Data="@AllSchemes"
                OnChange="@SelectedSchemeChanged"
                TextField="OriginalColorSchemeName"
                ValueField="Id"
                DefaultText="Select Scheme"
                FilterOperator="@StringFilterOperator.Contains"
                Filterable="true"
                Width="100%">
                    <DropDownListSettings>
                        <DropDownListPopupSettings Height="200px"></DropDownListPopupSettings>
                    </DropDownListSettings>
                </TelerikDropDownList>
                <br />

            }
            <TelerikGrid Context="materialColorsContext" Data=@NewSchemesData
            @ref="GridRef"
            OnCreate="@CreateSchemeHandler"
            OnDelete="@DeleteSchemeHandler"
            OnCancel="@CancelSchemeHandler"
            OnEdit="@EditSchemeHandler"
            OnUpdate="@UpdateSchemeHandler"
            OnAdd="@AddSchemeHandler"
            ConfirmDelete="true"
            EditMode="@GridEditMode.Inline">
                <GridColumns>
                    <GridColumn Field="MaterialColorPredefined.Material.Material1" Title="Material">
                        <EditorTemplate Context="colorSchemContext">
                            @{
                                EditedMaterialColorSchemeRow = colorSchemContext as MaterialColorSchemeDto;
                                EditedMaterialColorSchemeRow.MaterialColorPredefined = EditedMaterialColorSchemeRow.MaterialColorPredefined ?? new MaterialColorPredefinedDto();
                                <TelerikDropDownList Data="@Materials"
                                TextField="Material1"
                                ValueField="MateriaId"
                                Filterable="true"
                                OnChange="@MaterialChangeHandler"
                                FilterOperator="@StringFilterOperator.Contains"
                                @bind-Value="EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId"
                                Width="100%">
                                </TelerikDropDownList>
                                @if (EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId == -1)
                                {
                                    <TelerikTextBox @bind-Value="NewMaterialName"></TelerikTextBox>
                                }

                            }
                        </EditorTemplate>
                    </GridColumn>
                    <GridColumn Field="MaterialColorPredefined.ColorScheme.ColorScheme1" Title="Color" Editable="true">
                        <EditorTemplate Context="colorSchemContext">
                            @{
                                EditedMaterialColorSchemeRow = colorSchemContext as MaterialColorSchemeDto;
                                EditedMaterialColorSchemeRow.MaterialColorPredefined = EditedMaterialColorSchemeRow.MaterialColorPredefined ?? new MaterialColorPredefinedDto();
                                <TelerikDropDownList Data="@Colors"
                                TextField="ColorScheme1"
                                ValueField="ColorShemeId"
                                Filterable="true"
                                FilterOperator="@StringFilterOperator.Contains"
                                @bind-Value="EditedMaterialColorSchemeRow.MaterialColorPredefined.ColorSchemeId"
                                Width="100%">
                                </TelerikDropDownList>
                                @if (EditedMaterialColorSchemeRow.MaterialColorPredefined.ColorSchemeId == -1)
                                {
                                    <TelerikTextBox @bind-Value="NewColorName"></TelerikTextBox>
                                }
                            }
                        </EditorTemplate>
                    </GridColumn>
                    <GridCommandColumn Context="newContexxtName">
                        <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                        <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                        <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                        <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                    </GridCommandColumn>
                </GridColumns>
                <GridToolBarTemplate>
                    @{
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                        <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add</GridCommandButton>
                        if (validation)
                        {
                            <label style="color:red">Material Color Exists. Please reactivate the existing combination</label>
                        }
                    }
                </GridToolBarTemplate>
            </TelerikGrid>
            <br />
            <TelerikButton Class="btn btn-primary" ButtonType="ButtonType.Submit" Enabled="@IsAddEnabled" Icon="FontIcon.Save">Update</TelerikButton>
            @* <button type="submit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                    </svg>
                </span> Update
            </button> *@
            <button type="button" @onclick="CancelAddItem" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-base">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel" aria-hidden="true">
                    <!--!-->
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M256 32c-50.3 0-96.8 16.6-134.1 44.6-17.2 12.8-32.4 28.1-45.3 45.3C48.6 159.2 32 205.7 32 256c0 123.7 100.3 224 224 224 50.3 0 96.8-16.6 134.1-44.6 17.2-12.8 32.4-28.1 45.3-45.3 28-37.4 44.6-83.8 44.6-134.1 0-123.7-100.3-224-224-224zm0 384c-88.2 0-160-71.8-160-160 0-32.6 9.8-62.9 26.6-88.2l221.6 221.6C318.9 406.2 288.6 416 256 416zm133.4-71.8L167.8 122.6C193.1 105.8 223.4 96 256 96c88.2 0 160 71.8 160 160 0 32.6-9.8 62.9-26.6 88.2z"></path><!--!-->
                    </svg><!--!-->
                    <!--!-->
                </span> Cancel
            </button>
            <div style=@submittingStyle>Adding. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    [Parameter]    
    public bool IsModalVisible { get; set; }
    public MaterialColorSchemeDto ColorSchemeToAdd { get; set; } = new MaterialColorSchemeDto();
    public List<MaterialColorSchemeDto>? NewSchemesData { get; set; } = new List<MaterialColorSchemeDto>();
    private TelerikGrid<MaterialColorSchemeDto>? GridRef { get; set; }
    public List<ColorSchemeDto>? Colors { get; set; }
    public List<ColorSchemeDto>? AllColors { get; set; }
    public List<MaterialDto>? Materials { get; set; }
    public List<MaterialColorPredefinedDto>? AllPredefinedMaterialColors { get; set; }
    private string? NewColorName { get; set; }
    private string? NewMaterialName { get; set; }
    private string? NewGridColorName { get; set; }
    private string? NewGridMaterialName { get; set; }
    [Parameter]
    public EventCallback<ResponseModel<List<MaterialColorSchemeDto>>> HandleAddSubmit { get; set; }
    public List<HomeAreaDto>? AllHomeAreas { get; set; }
    public List<PhasePlanDto>? AllPhasePlans { get; set; }
    public int? SelectedPlanId;
    public int? PlanIdSelected;
    public int? SelectedSubdivisionId;
    public int? SubdivisionIdSelected;
    public Guid? CopySchemeIdSelected;
    public Guid? SelectedCopySchemeId;
    private MaterialColorSchemeDto? EditedMaterialColorSchemeRow { get; set; }
    public List<AvailablePlanOptionDto>? AllElevationsInPlan { get; set; } = new List<AvailablePlanOptionDto>();
    public int SelectedOptionId;
    public List<int>? SelectedOptionIds = new List<int>();
    public bool IsAddEnabled => SelectedOptionIds.Count() > 0;
    public string? ErrorMessage;
    public bool? ShowError;
    private string submittingStyle = "display:none";
    private TelerikMultiSelect<AvailablePlanOptionDto, int>? MultiSelectRef;
    public List<ColorSchemeElevationTreeModel>? AllSchemes { get; set; }
    public List<SubdivisionDto>? AllSubdivisions { get; set; }
    public List<AvailablePlanOptionDto>? ElevationsInPlan { get; set; } = new List<AvailablePlanOptionDto>();
    public List<PhasePlanDto>? PlansInSubdivision { get; set; }
    TelerikRadioGroup<string, int?> RadioGroupRef { get; set; }
    string? NewOrCopySelected { get; set; } = "New";
    List<string> NewOrCopyOptions { get; set; } = new List<string>{ "New", "Copy" };
    public List<string>? SchemesThisSubdivision { get; set; } = new List<string>();
    public string? SelectedSchemeName { get; set; }
    private int? CurrentMaterialId { get; set; }
    public bool validation { get; set; } = false;

    public async Task Show()
    {
        IsModalVisible = true;
        validation = false;
        SelectedOptionIds = new List<int>();
        NewSchemesData = new List<MaterialColorSchemeDto>();
        submittingStyle = "display:none";
        //If someone added a scheme and now wants to copy, this data has to refresh
        var schemesTask = ColorSchemeService.GetAllColorSchemesAsync();
        var colorsTask = ColorSchemeService.GetColorsAsync();
        var materialsTask = ColorSchemeService.GetMaterialsAsync();
        var predefinedMaterialColorsTask = ColorSchemeService.GetPredefinedMaterialColorCombinationsAsync();
        await Task.WhenAll(colorsTask, materialsTask, schemesTask, predefinedMaterialColorsTask);
        var getColors = colorsTask.Result;
        Colors = getColors.Value;
        AllColors = getColors.Value;
        Colors.Insert(0, new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = "Add New" });
        var getMaterials = materialsTask.Result;
        Materials = getMaterials.Value;
        Materials.Insert(0, new MaterialDto() { MateriaId = -1, Material1 = "Add New" });
        AllSchemes = schemesTask.Result.Value;
        AllPredefinedMaterialColors = predefinedMaterialColorsTask.Result.Value;
        SchemesThisSubdivision = new List<string>() { "Add New" };
        StateHasChanged();
    }
    protected override async Task OnInitializedAsync()
    {
        AllSubdivisions = (await SubdivisionService.GetSubdivisionsAsync()).Value;
        // var schemesTask = ColorSchemeService.GetAllColorSchemesAsync();
        // var colorsTask = ColorSchemeService.GetColorsAsync();
        // var materialsTask = ColorSchemeService.GetMaterialsAsync();
        // await Task.WhenAll(colorsTask, materialsTask, schemesTask);
        // var getColors = colorsTask.Result;
        // Colors = getColors.Value;
        // Colors.Insert(0, new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = "Add New" });
        // var getMaterials = materialsTask.Result;
        // Materials = getMaterials.Value;
        // Materials.Insert(0, new MaterialDto() { MateriaId = -1, Material1 = "Add New" });
        // AllSchemes = schemesTask.Result.Value;
        // SchemesThisSubdivision = new List<string>() { "Add New" };
    }
    private async Task OptionsChanged()
    {
        if (SelectedOptionIds != null && SelectedOptionIds.Count != 0)
        {
            SchemesThisSubdivision = AllSchemes.SelectMany(x => x.MaterialColors).Where(x => x.PlanOptionId != null && SelectedOptionIds.Contains((int)x.PlanOptionId)).Select(x => x.ColorSchemeNum).Distinct().ToList();
            SchemesThisSubdivision.Insert(0, "Add New");

        }

    }
    private async Task ChangeSelectedSchemeName()
    {
        if (SelectedSchemeName != null && SelectedSchemeName != "Add New" )
        {
            ColorSchemeToAdd.ColorSchemeNum = SelectedSchemeName;
        }
    }
    private async Task NewOrCopyChanged()
    {
        if (NewOrCopySelected == "New")
        {
            NewSchemesData = new List<MaterialColorSchemeDto>();
        }
    }
    private async Task CascadeOptions()
    {
        if(PlanIdSelected != SelectedPlanId)
        {
            PlanIdSelected = SelectedPlanId;
            if(SelectedPlanId != null)
            {
                AllElevationsInPlan = (await PlanService.GetElevationsInPlanAsync((int)SelectedPlanId)).Value;
                var SchemesThisSubdivision = AllSchemes.SelectMany(x => x.MaterialColors).Where(x => x.PlanOptionId != null && SelectedOptionIds.Contains((int)x.PlanOptionId)).Select(x => x.ColorSchemeNum).Distinct();
            }         
        }

    }
    private async Task CascadePlans()
    {
        if(SelectedSubdivisionId != null)
        {
            if(SelectedSubdivisionId != SubdivisionIdSelected)
            {
                SubdivisionIdSelected = SelectedSubdivisionId;
                PlansInSubdivision = (await PlanService.GetPhasePlansInSubdivisionAsync((int)SelectedSubdivisionId)).Value;

            }           
        }        
    }
    private async Task SelectedSchemeChanged()
    {
        if (SelectedCopySchemeId != null)
        {
            if (CopySchemeIdSelected != SelectedCopySchemeId)
            {
                CopySchemeIdSelected = SelectedCopySchemeId;
                NewSchemesData = AllSchemes.Where(x => x.Id == SelectedCopySchemeId).FirstOrDefault().MaterialColors;
                foreach(var item in NewSchemesData)
                {
                    item.Guid = Guid.NewGuid();
                }
            }
        }
    }
    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        var addSchemes = new List<MaterialColorSchemeDto>();
        foreach(var optionId in SelectedOptionIds)
        {
            var schemesToAdd = NewSchemesData.Select(x => new MaterialColorSchemeDto()
                {
                // Material = x.Material,
                // ColorScheme = x.ColorScheme,
                   MaterialColorPredefined = x.MaterialColorPredefined,
                // MaterialColorPredefinedId = x.MaterialColorPredefinedId,
                    ColorSchemeNum = ColorSchemeToAdd.ColorSchemeNum,
                    PlanOptionId = optionId
                });
            addSchemes.AddRange(schemesToAdd);
        }
        var response = await ColorSchemeService.AddSchemesAsync(addSchemes);
        submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(response);
    }

    void CancelAddItem()
    {
        //ColorSchemeToAdd = new MaterialColorSchemeDto();//clear for next use
        SelectedPlanId = 0;
        SelectedSubdivisionId = null;
        IsModalVisible = false;
    }

    public void Hide()
    {
        // ColorSchemeToAdd = new MaterialColorSchemeDto();//clear for next use
        SelectedPlanId = 0;
        SelectedSubdivisionId = null;
        IsModalVisible = false;        
    }

    void ToggleSelectAll(bool selectAll)
    {
        SelectedOptionIds.Clear();

        if (selectAll)
        {
            SelectedOptionIds.AddRange(AllElevationsInPlan.Select(x=> x.PlanOptionId));
        }

        MultiSelectRef.Rebind();
    }

    bool IsAllSelected()
    {
        return SelectedOptionIds.Count == AllElevationsInPlan.Count;
    }

    // for the item checkboxes
    bool GetChecked(int selected)
    {
        return SelectedOptionIds.Contains(selected);
    }

    async Task CreateSchemeHandler(GridCommandEventArgs args)
    {
        MaterialColorSchemeDto item = (MaterialColorSchemeDto)args.Item;

        item.MaterialColorPredefined.Material = item.MaterialColorPredefined.MaterialId == -1 ? new MaterialDto() { MateriaId = -1, Material1 = NewMaterialName } : Materials.FirstOrDefault(x => x.MateriaId == item.MaterialColorPredefined.MaterialId);
        item.MaterialColorPredefined.ColorScheme = item.MaterialColorPredefined.ColorSchemeId == -1 ? new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = NewColorName } : Colors.FirstOrDefault(x => x.ColorShemeId == item.MaterialColorPredefined.ColorSchemeId);

        var predefinedColorsForSelectedMaterial = AllPredefinedMaterialColors.Where(x => x.MaterialId == item.MaterialColorPredefined.MaterialId).Select(x=>x.ColorScheme).ToList();
        
        if (item.MaterialColorPredefined.ColorSchemeId == -1 && predefinedColorsForSelectedMaterial.Any(x => x.ColorScheme1 == NewColorName))
        {
            validation = true;
        }
        else
        {
            validation = false;
            item.Guid = Guid.NewGuid();
            NewSchemesData.Add(item);
            NewColorName = "";
            NewMaterialName = "";
        }
        StateHasChanged();
    }
    async Task UpdateSchemeHandler(GridCommandEventArgs args)
    {
        MaterialColorSchemeDto item = (MaterialColorSchemeDto)args.Item;
        var updateItem = NewSchemesData.Where(x => x.Guid == item.Guid).FirstOrDefault();
        if (item.MaterialColorPredefined.MaterialId == -1)
        {
            updateItem.MaterialColorPredefined.Material = new MaterialDto() { MateriaId = -1, Material1 = NewMaterialName };
        }
        else
        {
            updateItem.MaterialColorPredefined.Material = Materials.FirstOrDefault(x => x.MateriaId == item.MaterialColorPredefined.MaterialId);
        }
        if (item.MaterialColorPredefined.ColorSchemeId == -1)
        {
            updateItem.MaterialColorPredefined.ColorScheme = new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = NewColorName };
        }
        else
        {
            updateItem.MaterialColorPredefined.ColorScheme = Colors.FirstOrDefault(x => x.ColorShemeId == item.MaterialColorPredefined.ColorSchemeId);
        }
        NewColorName = "";
        NewMaterialName = "";
        if(updateItem != null)
        {
            updateItem = item;
        }
        GridRef.Rebind();
        StateHasChanged();

    }
    async Task DeleteSchemeHandler(GridCommandEventArgs args)
    {
        MaterialColorSchemeDto item = (MaterialColorSchemeDto)args.Item;
        NewSchemesData.Remove(item);
    }
    async Task MaterialChangeHandler()
    {
        var editItem = EditedMaterialColorSchemeRow;
        if (EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId != null && EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId != CurrentMaterialId)
        {
            CurrentMaterialId = EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId;
            var getColors = await ColorSchemeService.GetColorsAsync((int)EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId);
            Colors = getColors.Value;
            Colors.Add(new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = "Add New" });
            StateHasChanged();
        }

    }
    async Task AddSchemeHandler(GridCommandEventArgs args)
    {
        MaterialColorSchemeDto item = (MaterialColorSchemeDto)args.Item;
        item.MaterialColorPredefined = new MaterialColorPredefinedDto();
        EditedMaterialColorSchemeRow = item;        
        if (EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId != null && EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId != CurrentMaterialId)
        {
            CurrentMaterialId = EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId;
            var getColors = await ColorSchemeService.GetColorsAsync((int)EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId);
            Colors = getColors.Value;
            Colors.Add(new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = "Add New" });
            StateHasChanged();
        }
    }
    async Task EditSchemeHandler(GridCommandEventArgs args)
    {
        MaterialColorSchemeDto item = (MaterialColorSchemeDto)args.Item;
        var editItem = EditedMaterialColorSchemeRow;
        EditedMaterialColorSchemeRow = item;
        if (EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId != null && EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId != CurrentMaterialId)
        {
            CurrentMaterialId = EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId;
            var getColors = await ColorSchemeService.GetColorsAsync((int)EditedMaterialColorSchemeRow.MaterialColorPredefined.MaterialId);
            Colors = getColors.Value;
            Colors.Add(new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = "Add New" });
            StateHasChanged();
        }
    }
    void CancelSchemeHandler(GridCommandEventArgs args)
    {
        MaterialColorSchemeDto item = (MaterialColorSchemeDto)args.Item;
    }
}
