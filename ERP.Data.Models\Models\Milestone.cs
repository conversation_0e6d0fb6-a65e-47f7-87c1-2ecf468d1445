﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Milestone
{
    public int MilestoneId { get; set; }

    public string? MilestoneName { get; set; }

    public int? Seq { get; set; }

    public int? DivId { get; set; }

    public int? ReportingId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<ScheduleMilestone> ScheduleMilestones { get; set; } = new List<ScheduleMilestone>();

    public virtual ICollection<TemplateMilestone> TemplateMilestones { get; set; } = new List<TemplateMilestone>();
}
