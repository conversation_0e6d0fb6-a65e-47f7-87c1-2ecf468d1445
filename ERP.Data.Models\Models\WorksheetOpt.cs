﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class WorksheetOpt
{
    public int WorksheetOptId { get; set; }

    public int WorksheetPlanId { get; set; }

    public int PlanOptionId { get; set; }

    public double? Costprice { get; set; }

    public double? Markup { get; set; }

    public double? Sellprice { get; set; }

    public DateTime? Pricedate { get; set; }

    public int? Markuptype { get; set; }

    public double? Markuppercent { get; set; }

    public double? Marketvalue { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual AvailablePlanOption PlanOption { get; set; } = null!;

    public virtual ICollection<WorksheetOptAct> WorksheetOptActs { get; set; } = new List<WorksheetOptAct>();

    public virtual WorksheetPlan WorksheetPlan { get; set; } = null!;
}
