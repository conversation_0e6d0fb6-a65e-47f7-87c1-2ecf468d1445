﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Poheader
{
    public int PoheaderId { get; set; }

    public string? Ponumber { get; set; }

    public string? Releasecode { get; set; }

    public string? Pojobnumber { get; set; }

    public int? Potype { get; set; }

    public string? Podescription { get; set; }

    public int SubNumber { get; set; }

    public string? Povendor { get; set; }

    public DateTime? Podateissued { get; set; }

    public DateTime? Podateprinted { get; set; }

    public DateTime? Podateapproved { get; set; }

    public double? Pototal { get; set; }

    public string? Printable { get; set; }

    public double? Taxamount { get; set; }

    public int? Postatus { get; set; }

    public string? Approvedby { get; set; }

    public string? PendInvNumber { get; set; }

    public DateTime? PendInvDate { get; set; }

    public string? RetentionType { get; set; }

    public double? Retention { get; set; }

    public double? MaximumRetention { get; set; }

    public string? LastAcceptInvNo { get; set; }

    public DateTime? LastAcceptInvDte { get; set; }

    public double? LastAcceptInvAmt { get; set; }

    public double? LastAcceptRetent { get; set; }

    public DateTime? PendInvAcntDate { get; set; }

    public DateTime? PendInvPayDate { get; set; }

    public DateTime? PendInvDiscDate { get; set; }

    public double? PendInvAmount { get; set; }

    public double? PendInvTax { get; set; }

    public double? PendInvDisc { get; set; }

    public double? PendInvRetention { get; set; }

    public string? PendInvDescrip { get; set; }

    public DateTime? TaskCompleteDate { get; set; }

    public int? TradeId { get; set; }

    public int? SactivityId { get; set; }

    public string? Issuedby { get; set; }

    public string? PoSelections { get; set; }

    public string? JccOnHold { get; set; }

    public string? WcompOnHold { get; set; }

    public DateTime? Podatecancelled { get; set; }

    public string? Cancelledby { get; set; }

    public int? PurchasingContractsId { get; set; }

    public string? DocId { get; set; }

    public string? TradeArchived { get; set; }

    public DateTime? TradeDateViewed { get; set; }

    public string? TradeUserViewed { get; set; }

    public DateTime? TradeDateArchived { get; set; }

    public string? TradeUserArchived { get; set; }

    public string? PendInvNotes { get; set; }

    public string? TaskCompleteBy { get; set; }

    public string? ApprovalHold { get; set; }

    public string? ApprovalHoldReason { get; set; }

    public string? PendClose { get; set; }

    public int? PurchasingContCoId { get; set; }

    public string? UserModified { get; set; }

    public DateTime? DateModified { get; set; }

    public string? Ponotes { get; set; }

    public string? JobClosedHold { get; set; }

    public DateTime? PosentTimestamp { get; set; }

    public int? VpoNumber { get; set; }

    public string? VpoVendorinvoice { get; set; }

    public int? LienWaiverId { get; set; }

    public string? LienWaiverHold { get; set; }

    public DateTime? TradeDateAccepted { get; set; }

    public string? TradeAcceptedOverrideuser { get; set; }

    public int? TradeUserAccepted { get; set; }

    public string? TradeAcceptedHold { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public int? MasterPo { get; set; }

    public int? IntegrationPo { get; set; }

    public string? IntegrationPonumber { get; set; }

    public string? TbdChange { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public string? BackchargePo { get; set; }

    public string? Notes { get; set; }

    public string? VpoAssignedTo { get; set; }

    public string? VpoDesc { get; set; }

    public int? VpoGroupId { get; set; }

    public Guid? BcId { get; set; }

    public string? HouseJob { get; set; }

    public virtual ICollection<CheckPayment> CheckPayments { get; set; } = new List<CheckPayment>();

    public virtual Job? HouseJobNavigation { get; set; }

    public virtual ICollection<PoAttachment> PoAttachments { get; set; } = new List<PoAttachment>();

    public virtual ICollection<Poapproval> Poapprovals { get; set; } = new List<Poapproval>();

    public virtual ICollection<Podetailoption> Podetailoptions { get; set; } = new List<Podetailoption>();

    public virtual ICollection<Podetail> Podetails { get; set; } = new List<Podetail>();

    public virtual ICollection<Pojccdetail> Pojccdetails { get; set; } = new List<Pojccdetail>();

    public virtual Job? PojobnumberNavigation { get; set; }

    public virtual Postatus? PostatusNavigation { get; set; }

    public virtual Sactivity? Sactivity { get; set; }

    public virtual Supplier SubNumberNavigation { get; set; } = null!;

    public virtual Trade? Trade { get; set; }
}
