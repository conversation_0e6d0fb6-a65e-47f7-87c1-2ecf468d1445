﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Web.Data
{
    public class VPOItemPickService
    {
        private bool? _isChanged;

        public bool? IsChanged
        {
            get
            {
                return _isChanged;
            }
            set
            {
                _isChanged = value;
                NotifyChanged();
            }
        }

        public Func<Task>? OnDataChanged { get; set; }

        private async Task NotifyChanged() => await OnDataChanged?.Invoke();
    }
}
