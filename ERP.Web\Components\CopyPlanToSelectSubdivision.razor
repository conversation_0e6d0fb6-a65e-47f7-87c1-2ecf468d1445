﻿
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject PlanService PlanService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <h7 class="page-title" style="font-weight:bold">Copy Plan</h7>
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@PlanToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />
            <label class="form-label">Subdivision to add plan to</label>
            <TelerikDropDownList Data="@AllSubdivisions"
            @bind-Value="@PlanToAdd.SubdivisionId"
                                 DefaultText="Select A Subdivision"
                                 ScrollMode="@DropDownScrollMode.Virtual"
                                 TextField="SubdivisionName"
                                 ValueField="SubdivisionId"
                                 PageSize="10"
                                 Filterable="true"
                                 ItemHeight="35"
                                 OnChange="@OnSubdivisionChoiceChangeHandler"
                                 FilterOperator="@StringFilterOperator.Contains"
                                 Class="dropdownControl">
                <DropDownListSettings>
                    <DropDownListPopupSettings Height="350px"></DropDownListPopupSettings>
                </DropDownListSettings>
            </TelerikDropDownList>
            <br />
            <div>
                @if(PhasesThisSubdivision != null && PhasesThisSubdivision.Count != 0)
                {
                    <label class="form-label">Phase to add plan to </label>
                    <TelerikDropDownList Data="@PhasesThisSubdivision"
                    @bind-Value="@PlanToAdd.PhaseName"
                                         DefaultText="Select A Phase"
                                         ScrollMode="@DropDownScrollMode.Virtual"
                                         PageSize="10"
                                         Filterable="true"
                                         ItemHeight="35"
                                         OnChange="@OnPhaseChoiceChangeHandler"
                                         FilterOperator="@StringFilterOperator.Contains"
                                         Class="dropdownControl">
                        <DropDownListSettings>
                            <DropDownListPopupSettings Height="350px"></DropDownListPopupSettings>
                        </DropDownListSettings>
                    </TelerikDropDownList>
                    <br />
                    <div style=@ShowNewPhaseInput>
                        <label class="form-label">Phase name to add</label>
                        <TelerikTextBox @bind-Value="@PlanToAdd.PhaseName"></TelerikTextBox>
                    </div>
                }
            </div>
            <label class="form-label">Plan to add</label>
            <TelerikDropDownList Data="@phasePlans"
                                 @bind-Value="@PlanToAdd.PlanId"
                                 TextField="DisplaySubdivisionPlanName"
                                 ValueField="PhasePlanId"
                                 DefaultText="Select A Plan"
                                 ScrollMode="@DropDownScrollMode.Virtual"
                                 PageSize="10"
                                 Filterable="true"
                                 ItemHeight="35"
                                 FilterOperator="@StringFilterOperator.Contains"
                                 Class="dropdownControl">
                <DropDownListSettings>
                    <DropDownListPopupSettings Height="350px"></DropDownListPopupSettings>
                </DropDownListSettings>
            </TelerikDropDownList>
            <div class="customSeparator">
                <button type="submit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                    <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                        <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                            <!--!-->
                            <!--!-->
                            <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                        </svg>
                    </span> Update
                </button>
                <button type="button" @onclick="CancelAddPlan" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-base">
                    <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel" aria-hidden="true">
                        <!--!-->
                        <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                            <!--!-->
                            <!--!-->
                            <path d="M256 32c-50.3 0-96.8 16.6-134.1 44.6-17.2 12.8-32.4 28.1-45.3 45.3C48.6 159.2 32 205.7 32 256c0 123.7 100.3 224 224 224 50.3 0 96.8-16.6 134.1-44.6 17.2-12.8 32.4-28.1 45.3-45.3 28-37.4 44.6-83.8 44.6-134.1 0-123.7-100.3-224-224-224zm0 384c-88.2 0-160-71.8-160-160 0-32.6 9.8-62.9 26.6-88.2l221.6 221.6C318.9 406.2 288.6 416 256 416zm133.4-71.8L167.8 122.6C193.1 105.8 223.4 96 256 96c88.2 0 160 71.8 160 160 0 32.6-9.8 62.9-26.6 88.2z"></path><!--!-->
                        </svg><!--!-->
                        <!--!-->
                    </span> Cancel
                </button>
                <div style=@ShowLoading>Submitting. Please wait...</div>
                <div style=@ShowError>Something went wrong.</div>
            </div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public AddPlanToSubdivisionPhaseModel PlanToAdd { get; set; } = new AddPlanToSubdivisionPhaseModel();
    public string ShowNewPhaseInput { get; set; } = "display:none";
    public string ShowLoading{ get; set; } = "display:none";
    public string ShowError { get; set; } = "display:none";
    public List<SubdivisionPlanModel>? phasePlans { get; set; }
    public int? SelectedPhasePlanNum { get; set; }
    public List<string?>? PhasesThisSubdivision;
    public List<SubdivisionDto>? AllSubdivisions;
    private int SelectedSubdivId { get; set; }

    [Parameter]
    public EventCallback<ResponseModel> HandleAddSubmit { get; set; }

    public async Task Show()
    {
        IsModalVisible = true;
        var phasePlansResponse = await PlanService.GetPhasePlansAsync();
        phasePlans = phasePlansResponse.Value;

        var data = await SubdivisionService.GetSubdivisionsAsync();
        AllSubdivisions = data.Value;
        StateHasChanged();
    }  
    private void OnPhaseChoiceChangeHandler(object newVal)
    {        
        if(newVal != null && newVal.ToString() == "New Phase")
        {
            ShowNewPhaseInput = "";
        }
        else
        {
            ShowNewPhaseInput = "display:none";
        }
    }
    private async void OnSubdivisionChoiceChangeHandler(object newVal)
    {
        if(newVal != null && SelectedSubdivId != (int)newVal)
        {
            SelectedSubdivId = (int)newVal;
            var getPhases = await PlanService.GetPhasePlansAsync((int)PlanToAdd.SubdivisionId);
            PhasesThisSubdivision = getPhases.Value.Select(x => x.Phase).Distinct().ToList(); // not null as returning empty list in case of failure
            PhasesThisSubdivision = PhasesThisSubdivision.Prepend("New Phase").Prepend("None").ToList();
        }
       
    }
    private async void HandleValidAddSubmit()
    {
        ShowLoading = "";
        PlanToAdd.PhaseName = PlanToAdd.PhaseName == "None" ? null : PlanToAdd.PhaseName; //if they picked "None" just use null?
        var response = await PlanService.AddPhasePlanToSubdivisionAsync(PlanToAdd);
        //TODO: show success/error
        if (response.IsSuccess)
        {
            ShowLoading = "display:none";
        }
        else
        {
            ShowLoading = "display:none";
            ShowError = ""; //this won't actually show, since invoking the handle add hides the modal, so that function needs to display the error
        }
        await HandleAddSubmit.InvokeAsync(response);        
    }
    async void CancelAddPlan()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;        
    }
}
