﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class PodetailoptionDto : IMapFrom<Podetailoption>
{
    public int PodetailoptionsId { get; set; }

    public int PoheaderId { get; set; }

    public string? OptionNumber { get; set; }

    public string? OptionDesc { get; set; }

    public string? OptionLongDesc { get; set; }

    public string? OptionExtendedDesc { get; set; }

    public string? OptionNotes { get; set; }

    public string? OptionSelections { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

}
