# ASP.NET
# Build and test ASP.NET projects.
# Add steps that publish symbols, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/apps/aspnet/build-aspnet-4

trigger:
- master

pool:
  vmImage: 'windows-latest'

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'
  projectPath: 'ERP.Web/*.csproj'
  nugetConfigPath: 'nuget.config'

steps:


- task: DotNetCoreCLI@2
  inputs:
    command: 'restore'
    projects: '$(projectPath)'
    feedsToUse: 'config'
    nugetConfigPath: '$(nugetConfigPath)'
    externalFeedCredentials: 'TelerikWithNugetKey'

# - task: DotNetCoreCLI@2
#   inputs:
#     command: 'build'
#     projects: '$(projectPath)'


