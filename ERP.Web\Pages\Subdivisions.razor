﻿@page "/subdivisions"
@using ERP.Data.Models.Dto;
@inject SubdivisionService SubdivisionService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector, ConstructionManager")]
<style>
    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>

<PageTitle>Subdivisions</PageTitle>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<ErrorBoundary>
    <ChildContent>
        <div class="col-lg-12">
            <div class="card" style="background-color: #2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Subdivisions</h7>
                </div>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item active">Subdivisions</li>
        </ol>

        @* <div class="card col-3">
            <div class="card-header">
                <h7 class="page-title" style="font-weight:bold">Go to Lot (Enter Job number)</h7>
            </div>
            <div class="card-body">
                <TelerikAutoComplete Data="AllLots"
                                     ScrollMode="@DropDownScrollMode.Virtual"
                                     ItemHeight="30"
                                     OnChange="@OnChangeLotHandler"
                @bind-Value="@SelectedLot"
                                     Filterable="true"
                                     PageSize="10"
                                     FilterOperator="@StringFilterOperator.Contains">
                    <AutoCompleteSettings>
                        <AutoCompletePopupSettings Height="300px" />
                    </AutoCompleteSettings>
                </TelerikAutoComplete>
            </div>
        </div> *@
        <br />
        @if (subdivisions == null)
        {
            <p><em>Loading...</em></p>
            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner"/>
        }
        else
        {
            <TelerikGrid Data=@subdivisions
                     ConfirmDelete="true"
                     ScrollMode="@GridScrollMode.Virtual"
                     FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                     Height="1000px" RowHeight="60" PageSize="20"
                     Sortable="true"
                     Resizable="true"
                     EditMode="@GridEditMode.Inline"
                     OnUpdate="@UpdateSubdivisionHandler"
                     OnEdit="@EditSubdivisionHandler"
                     OnCancel="@CancelSubdivisionHandler"
                     @ref="@GridRef">
                <GridSettings>
                    <GridPopupEditSettings Width="800px"></GridPopupEditSettings>
                </GridSettings>
                <GridColumns>
                    <GridCommandColumn Context="dataItem" Width="120px">
                        @{
                            var subdiv = dataItem as SubdivisionDto;
                            <a href=@($"/lots/{subdiv.SubdivisionId}") class="btn btn-outline-primary">View Lots</a>
                        }
                    </GridCommandColumn>
                    <GridCommandColumn Context="dataItem" Width="120px">
                        @{
                            var subdiv = dataItem as SubdivisionDto;
                            <a href=@($"/subdivisiondetails/{subdiv.SubdivisionId}") class="btn btn-outline-primary">View Details</a>
                        }
                    </GridCommandColumn>
                    <GridColumn Field="SubdivisionNum" Title="Subdivision Number" Editable="false" />
                    <GridColumn Field="SubdivisionName" Title="Name" Editable="false" />
                    <GridColumn Field="MarketingName" Title="Marketing Name" />
                    <GridCommandColumn>
                        @if(AllowEdit)
                        {
                            <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true" Class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">Update</GridCommandButton>
                            <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                            @*                         <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton> *@
                            <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                        }
                    </GridCommandColumn>
                </GridColumns>
                <GridToolBarTemplate>
                  @*   <GridCommandButton Command="AddFromToolbar" OnClick="@AddSubdivisionFromToolbar" Icon="@FontIcon.Plus" Class="k-button-add">Add Subdivision</GridCommandButton> *@
                    <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>
                </GridToolBarTemplate>
                <NoDataTemplate>
                    <p>@Message</p>
                </NoDataTemplate>
            </TelerikGrid>
        }
    </ChildContent>
</ErrorBoundary>

<ERP.Web.Components.AddSubdivision @ref="AddSubdivisionModal" HandleAddSubmit="HandleValidAddSubdivisionSubmit"></ERP.Web.Components.AddSubdivision>

@code {
    private List<SubdivisionDto>? subdivisions;
    public List<JobDto> SelectLots { get; set; } = new List<JobDto>();
    public string? SelectedLot { get; set; }
    public List<string>? AllLots { get; set; }
    private TelerikGrid<SubdivisionDto> GridRef { get; set; }
    public string? Message { get; set; } = "No data to display";
    protected ERP.Web.Components.AddSubdivision? AddSubdivisionModal { get; set; }
    private bool AllowEdit { get; set; } = true;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var getSubdivisions = await SubdivisionService.GetSubdivisionsAsync();
        subdivisions = getSubdivisions.Value;
        Message = getSubdivisions.Message;
        // SelectLots = (await SubdivisionService.GetAllLotsAsync()).Select(x => new JobDto() { JobNumber = x.JobNumber, LotNumber = x.LotNumber }).ToList();
        // AllLots = SelectLots.Select(x => x.JobNumber).ToList();
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }
    public class JobDto
    {
        public string? JobNumber { get; set; }
        public string? LotNumber { get; set; }
    }
    public class JobSelected
    {
        public bool Selected { get; set; }
        public string JobNumber { get; set; }
    }

    //switch to different lot
    public async void OnChangeLotHandler(object theUserChoice)
    {
        string selectedLot = (string)theUserChoice;
        if (!string.IsNullOrWhiteSpace(selectedLot) && AllLots.Contains(selectedLot))
        {
            NavManager.NavigateTo($"lotdetails/{selectedLot}");
            StateHasChanged();
        }
    }

    void EditSubdivisionHandler(GridCommandEventArgs args)
    {
        SubdivisionDto item = (SubdivisionDto)args.Item;
    }

    async Task UpdateSubdivisionHandler(GridCommandEventArgs args)
    {
        SubdivisionDto item = (SubdivisionDto)args.Item;
        var updateSubdivisionResponse = await SubdivisionService.UpdateSubdivisionAsync(item, false);
        if (updateSubdivisionResponse.IsSuccess)
        {
            var getSubdivisions = await SubdivisionService.GetSubdivisionsAsync();
            subdivisions = getSubdivisions.Value;
            Message = getSubdivisions.Message;
            ShowSuccessOrErrorNotification(updateSubdivisionResponse.Message, false);
        }
        else
        {
            ShowSuccessOrErrorNotification(updateSubdivisionResponse.Message, false);
        }
    }

    // async Task DeleteSubdivisionHandler(GridCommandEventArgs args)
    // {
    //     SubdivisionDto item = (SubdivisionDto)args.Item;
    //     var deleteSubdivisionResponse = await SubdivisionService.DeleteSubdivisionAsync(item);
    //     if (deleteSubdivisionResponse.IsSuccess)
    //     {
    //         var getSubdivisions = await SubdivisionService.GetSubdivisionsAsync();
    //         subdivisions = getSubdivisions.Value;
    //         Message = getSubdivisions.Message;
    //         ShowSuccessOrErrorNotification(deleteSubdivisionResponse.Message, false);
    //     }
    //     else
    //     {
    //         ShowSuccessOrErrorNotification(deleteSubdivisionResponse.Message, true);
    //     }
    // }

    // async Task CreateSubdivisionHandler(GridCommandEventArgs args)
    // {
    //     SubdivisionDto item = (SubdivisionDto)args.Item;
    //     var addSubdivisionResponse = await SubdivisionService.AddSubdivisionAsync(item);
    //     if (true)
    //     {
    //         var getSubdivisions = await SubdivisionService.GetSubdivisionsAsync();
    //         subdivisions = getSubdivisions.Value;
    //         Message = getSubdivisions.Message;
    //         ShowSuccessOrErrorNotification(addSubdivisionResponse.Message, false);
    //     }
    //     else
    //     {
    //         ShowSuccessOrErrorNotification(addSubdivisionResponse.Message, true);
    //     }
    // }

    async Task CancelSubdivisionHandler(GridCommandEventArgs args)
    {
        SubdivisionDto item = (SubdivisionDto)args.Item;

        // if necessary, perform actual data source operation here through your service

        Console.WriteLine("Cancel event is fired.");
    }

    private void AddSubdivisionFromToolbar(GridCommandEventArgs args)
    {
        AddSubdivisionModal.Show();
    }

    private async void HandleValidAddSubdivisionSubmit(ResponseModel<SubdivisionDto> responseSubdivision)
    {
        var getSubdivisions = await SubdivisionService.GetSubdivisionsAsync();
        subdivisions = getSubdivisions.Value;
        Message = getSubdivisions.Message;
        AddSubdivisionModal.Hide();

        if (responseSubdivision.IsSuccess)
        {
            ShowSuccessOrErrorNotification("Successfully added new subdivision", false);
        }
        else
        {
            ShowSuccessOrErrorNotification(responseSubdivision.Message, true);
        }

        StateHasChanged();
    }
    private void ShowSuccessOrErrorNotification(string message, bool error)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = error ? ThemeConstants.Notification.ThemeColor.Error : ThemeConstants.Notification.ThemeColor.Success
            });
    }
}
