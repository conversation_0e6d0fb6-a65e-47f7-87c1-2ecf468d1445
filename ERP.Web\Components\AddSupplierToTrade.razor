﻿
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject TradeService TradeService
@inject PoService POService

@using ERP.Data.Models.Dto
<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Supplier to Trade/Subdivision 
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@SupplierToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <p>Select Supplier to Add to Trade: @Trade.TradeName</p>
            <p>For Subdivision: @Subdivision.SubdivisionName</p>
            <p><label>Trade</label>
            <TelerikDropDownList @bind-Value="@SupplierToAdd.SubNumber"
                                 Data="@AllSuppliers"
                                 TextField="SubName"
                                 ValueField="SubNumber"
                                 DefaultText="Select Supplier"
                                 Filterable="true"
                                 Width="100%">
            </TelerikDropDownList>
            </p>
            <p>
                <label>Default?</label>
                <TelerikCheckBox @bind-Value="@SupplierToAdd.IsDefault"/>
            </p>
            
            <br/>
            <button type="submit" class="btn btn-primary">Add Supplier to Trade</button>                   
            <button type="button" @onclick="CancelAddItem" class="btn btn-secondary">Cancel</button> 
            <div style=@submittingStyle>Adding. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public TradeSupplierModel SupplierToAdd { get; set; } = new TradeSupplierModel();
    [Parameter]
    public TradeSupplierModel Trade { get; set; }
    [Parameter]
    public SubdivisionDto Subdivision { get; set; }
    [Parameter]
    public EventCallback<ResponseModel<TradeSupplierModel>> HandleAddSubmit { get; set; }

    public List<SupplierDto>? AllSuppliers { get; set; }
    public int SelectedSupplierId;
    private string submittingStyle = "display:none";

    public async Task Show()
    {
        IsModalVisible = true;              
        AllSuppliers = (await POService.GetSuppliersAsync()).Value;
        StateHasChanged();
    }

    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";     
        SupplierToAdd.TradeId = Trade.TradeId;
        SupplierToAdd.SubdivisionId = Subdivision.SubdivisionId;
        var responseItem = await TradeService.AddSupplierToTradeAndSubdivision(SupplierToAdd);
        submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(responseItem);
    }
    async void CancelAddItem()
    {
        IsModalVisible = false;
    }
    public void Hide()
    {
        IsModalVisible = false;        
    }

}
