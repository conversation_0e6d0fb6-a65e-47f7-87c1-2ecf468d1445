﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;

namespace ERP.Data.Models;

public class MasterPlanDto : IMapFrom<MasterPlan>
{
    public int MasterPlanId { get; set; }

    public int? SsmasterPlanId { get; set; }

    public int? WmsmasterPlanId { get; set; }

    [Required(ErrorMessage = "Plan Number is required")]
    public string PlanNum { get; set; } = null!;

    [Required(ErrorMessage = "Plan Name is required")]
    public string? PlanName { get; set; }
    public string? DisplayName { get; set; } //for use in dropdowns to display plan num and name

    public string? PlanModelNum { get; set; }

    public string? WmsplanNum { get; set; }

    public string? BaseHousePlan { get; set; }

    public string? ElevationCode { get; set; }

    public string? ElevationNotes { get; set; }

    [Range(1, 9, ErrorMessage = "Plan Type is required")]
    public int PlanTypeId { get; set; }

    public string? Description { get; set; }

    public decimal? DefaultPrice { get; set; }

    public double? DefaultMarginPct { get; set; }

    public decimal? Cost { get; set; }

    public decimal? ConstructionDays { get; set; }

    public int? MinLotSize { get; set; }

    public int? SquareFeet { get; set; }

    public short? Bedrooms { get; set; }

    public decimal? Bathrooms { get; set; }

    public decimal? Stories { get; set; }

    public int? PlanSize { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }


    public string? ItemGroupCode { get; set; }

    public int? MasterItemGroupId { get; set; }

    public string? GroupPhaseCode { get; set; }

    public string? GroupPhaseDesc { get; set; }

    public string? GroupPhaseNotes { get; set; }

    public string? DeletedFromPe { get; set; }

    public int? AsmGroup { get; set; }

    public string? ErrorMessage { get; set; }

    //public virtual ICollection<AsmHeader> AsmHeaders { get; set; } = new List<AsmHeader>();

    //public virtual ICollection<PhasePlan> PhasePlans { get; set; } = new List<PhasePlan>();

    //public virtual PlanType PlanType { get; set; } = null!;

    public void Mapping(Profile profile)
    {
        profile.CreateMap<MasterPlanDto, MasterPlan>().ReverseMap();
    }
}
