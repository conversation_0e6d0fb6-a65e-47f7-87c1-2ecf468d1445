﻿using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class BuildAttributeItemDto : IMapFrom<BuildAttributeItem>
{
    public int BuildOptionId { get; set; }

    public int OpAttrGroupItemId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    // public TbBuiltOptionDto? BuildOption { get; set; } 
    public AttrGroupAssignmentDto? AttrGroupAssignment { get; set; }
    public OptionAttributeGroupItemDto? OpAttrGroupItem { get; set; } 
}
