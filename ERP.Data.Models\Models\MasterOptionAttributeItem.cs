﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class MasterOptionAttributeItem
{
    public int MasterOptionAttAssignmentId { get; set; }

    public int MasterOptionId { get; set; }

    public int? AttrGroupAssignmentId { get; set; }

    public string? LastUser { get; set; }

    public DateTime? LastChanged { get; set; }

    public bool? IsActive { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual AttrGroupAssignment? AttrGroupAssignment { get; set; }

    public virtual MasterOption MasterOption { get; set; } = null!;
}
