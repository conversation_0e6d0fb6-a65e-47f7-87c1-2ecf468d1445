﻿using ERP.Data.Models.Validation;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ERP.Data.Models.Dto;

public class ColorSchemeModel
{


    public string? ColorShcemNum { get; set;  }
    public string? SubdivisionName { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

}
