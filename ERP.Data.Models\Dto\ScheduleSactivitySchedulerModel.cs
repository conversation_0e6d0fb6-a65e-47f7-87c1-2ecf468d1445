﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class ScheduleSactivitySchedulerModel 
{
    public int ScheduleAid { get; set; }
    public string? ActivityName { get; set; } 
    public string? Predecessor { get; set; }
    public List<int>? PredIds { get; set; }
    public int? ScheduleMid { get; set; }

    public int? ScheduleId { get; set; }

    public int? SactivityId { get; set; }

    public int? SubNumber { get; set; }

    public int? Seq { get; set; }

    public int? Duration { get; set; }

    public int? LagTime { get; set; }

   public string? DateType { get; set; }//for disguishing whether the schedulre appointments are baseline, proj, scheduled, or actual dates

    public DateTime? StartDate { get; set; }

    public DateTime? EndDate { get; set; }

    
    public string? Reminder { get; set; }

    public DateTime? ReminderDate { get; set; }

    public string? ReminderText { get; set; }

    public string? Note { get; set; }

    public DateTime? NotifyDate { get; set; }

    public DateTime? UpdateDate { get; set; }

    public string? VarianceCode { get; set; }

    public string? Complete { get; set; }
    public bool BoolComplete { get; set; }

    public int? SaChecklistId { get; set; }

    public int? PlusminusDays { get; set; }

    public string? AdjustReminderWithProjStart { get; set; }

    public int? Calduration { get; set; }


    public string? TradeCrew { get; set; }

    public int? ActualDuration { get; set; }

    public string? GrossLag { get; set; }

    public string? Excludefromschedule { get; set; }

    public string? SupplierNote { get; set; }

    public string? IsLocked { get; set; }
    public bool BoolIgnoreNoWorkDays { get; set; }//IsLocked is for Ignore no work days

    public string? GenPitBudget { get; set; }

    public int? SchDatesPinned { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public SactivityDto? Sactivity { get; set; }

    public ScheduleDto? Schedule { get; set; }

    public ScheduleMilestoneDto? ScheduleM { get; set; }

    public SupplierDto? SubNumberNavigation { get; set; }

    public VarianceDto? VarianceCodeNavigation { get; set; }
   // public ICollection<ScheduleSactivityLinkDto>? ScheduleSactivityLinks { get; set; } = new List<ScheduleSactivityLinkDto>();

    //public ICollection<ScheduleSactivityPredDto>? ScheduleSactivityPreds { get; set; } = new List<ScheduleSactivityPredDto>();
    public bool SupplierEditable { get; set; }  //sactivity supplier will be editable if no issued pos
    public bool IsAllDay { get; set; } = true;
    public DateTime? CompletedDate { get; set; }

    public string? CompletedBy { get; set; }
    public int? FirstPredId { get; set; }
    public double? PercentComplete { get; set; }
    public string? PercentCompleteTitle { get; set; }
    public bool HasChildren { get; set; } = false;
    public DateTime? Start { get; set; }
    public DateTime? End { get; set; }
    public string? Title { get; set; }
}
