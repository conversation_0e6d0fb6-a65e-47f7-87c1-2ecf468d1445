﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Salesconfigoption
{
    public int SalesconfigoptionsId { get; set; }

    public int? SalesconfigId { get; set; }

    public int? PlanOptionId { get; set; }

    public double? OptionPrice { get; set; }

    public double? OptionQuantity { get; set; }

    public string? OptionNotes { get; set; }

    public string? AssociatedEstimate { get; set; }

    public string? SsOptioncode { get; set; }

    public string? OptionSelections { get; set; }

    public string? ScDescription { get; set; }

    public int? PeeHeaderId { get; set; }

    public int? MasterOptionId { get; set; }

    public string? IsRollbackOption { get; set; }

    public string? IsTransferredOption { get; set; }

    public int? EstheaderId { get; set; }

    public string? IsDeletedOption { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public int? ContractId { get; set; }

    public bool? IsCustomerApprovalRequired { get; set; }

    public string? Notes { get; set; }

    public virtual ICollection<Estoption> Estoptions { get; set; } = new List<Estoption>();

    public virtual AvailablePlanOption? PlanOption { get; set; }

    public virtual Salesconfig? Salesconfig { get; set; }

    public virtual ICollection<SalesconfigoptionsAttribute> SalesconfigoptionsAttributes { get; set; } = new List<SalesconfigoptionsAttribute>();
}
