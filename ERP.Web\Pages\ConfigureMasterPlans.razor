﻿@page "/configuremasterplans"
@using ERP.Data.Models.Dto;
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject PlanService PlanService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager NavManager
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, Accounting")]
<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }

    /* Gutter */
    .row > * {
        padding-right: calc(var(--bs-gutter-x) * .10);
        padding-left: calc(var(--bs-gutter-x) * .10);
    }

    .k-notification-success {
        border-color: #c3e6cb;
        color: #155724;
        background-color: #d4edda;
        font-size: .688rem;
        font-family: "Roboto",sans-serif;
        width: 250px;
        padding: .75rem 1.25rem;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }

    .k-form .k-form-label, .k-form .k-form-field-wrap {
        display: inline;
    }
</style>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<PageTitle>Manage Master Plans</PageTitle>

<div class="container-fluid flex">
    <div class="row d-flex">
        <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Manage Master Plans</h7>
                </div>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item active">Manage Plans</li>
        </ol>

        <div class="col-3">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Master Plan</h7>
                </div>
            </div>

            @if (MasterPlanData == null)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingPlan />
            }
            else
            {
                <TelerikGrid Data=@MasterPlanData
                             ScrollMode="@GridScrollMode.Virtual"
                             FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                             Height="1000px" RowHeight="60" PageSize="20"
                             Sortable="true"
                             Resizable="true"
                             Reorderable="true"
                             Groupable="false"
                             Size="@ThemeConstants.Grid.Size.Small"
                             OnRowClick="@OnPlanRowClickHandler"
                             SelectionMode="GridSelectionMode.Single"
                             EditMode="@GridEditMode.Popup"
                             OnUpdate="@UpdatePlanHandler"
                             OnEdit="@EditPlanHandler"
                             OnDelete="@DeletePlanHandler"
                             OnCreate="@CreatePlanHandler"
                             OnCancel="@CancelPlanHandler"
                             ConfirmDelete="true"
                @ref="@PlanGridRef">
                    <GridToolBarTemplate>
                        @if(AllowEdit)
                        {
                            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Plan</GridCommandButton>
                            <GridCommandButton Command="CopyPlanCommand" Icon="@FontIcon.Plus" OnClick="@CopyPlanFromToolbar" Class="k-button-success">Copy Plan</GridCommandButton>
                        }                        
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <GridColumns>
                        <GridColumn Field="PlanNum" Title="Plan Number" Editable="true" Groupable="false" />
                        <GridColumn Field="PlanName" Title="Plan Name" Editable="true" Groupable="false" />
                        <GridColumn Field="PlanTypeId" Title="Plan Type" Editable="true" Groupable="false" Width="0">
                            <EditorTemplate>
                                @{
                                    SelectedPlan = context as MasterPlanDto;
                                    <TelerikDropDownList @bind-Value="@SelectedPlan.PlanTypeId"
                                                         Data="@PlanTypeOptions"
                                                         TextField="PlanTypeCode"
                                                         ValueField="PlanTypeId"
                                                         Width="100%">
                                        <DropDownListSettings>
                                            <DropDownListPopupSettings Height="auto"></DropDownListPopupSettings>
                                        </DropDownListSettings>
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                        </GridColumn>

                        <GridColumn Field="SquareFeet" Title="Plan Square Feet" Editable="true" Groupable="false" Width="0" />
                        <GridColumn Field="MasterPlanId" Visible="false" Editable="false" Groupable="false" />
                        <GridCommandColumn>
                            @if(AllowEdit)
                            {
                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                            }
                        </GridCommandColumn>
                    </GridColumns>
                    <GridSettings>
                        <GridPopupEditSettings Width="400px"
                                               Height="500px"
                                               Title="Plan">
                        </GridPopupEditSettings>
                    </GridSettings>
                </TelerikGrid>

            }
        </div>
        <div class="col-4">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Selected Plan: @SelectedPlan?.PlanName</h7>
                </div>
            </div>

            @if (MasterOptionData == null)
            {
                <p><em>Select a plan to see options</em></p>
                <div style=@loadingOptionStyle>Loading...</div>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingOption />
            }

            else
            {
                //NOTE 11/19: additional plans editable set to false for now. Edit with multiple is slow and need to revisit 
                <TelerikGrid Data=@MasterOptionData
                             FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                             Height="1000px" RowHeight="60" PageSize="20"
                             Sortable="true"
                             Resizable="true"
                             Reorderable="true"
                             Groupable="true"
                             OnRowClick="@OnOptionRowClickHandler"
                             SelectionMode="GridSelectionMode.Single"
                             EditMode="@GridEditMode.Popup"
                             OnUpdate="@UpdateOptionHandler"
                             OnEdit="@EditOptionHandler"
                             OnDelete="@DeleteOptionHandler"
                             OnCreate="@CreateOptionHandler"
                             OnCancel="@CancelOptionHandler"
                             ConfirmDelete="true"
                @ref="@OptionGridRef">
                    <GridColumns>
                        <GridColumn Field="AssemblyCode" Title="Code" Groupable="false">
                            <EditorTemplate>
                                @{
                                    SelectedOption = context as AsmHeaderModel;

                                    if (SelectedOption.AsmHeaderId != 0)
                                    {
                                        <span class="k-textbox k-input telerik-blazor k-input-solid k-rounded-md k-input-md">
                                            <!--!-->
                                            <input role="textbox" class="k-input-inner" dir="ltr" aria-readonly="false" aria-invalid="false" tabindex="0" value="@SelectedOption.AssemblyCode" disabled><!--!-->
                                        </span>
                                    }
                                }
                            </EditorTemplate>
                        </GridColumn>
                        <GridColumn Field="AssemblyDesc" Title="Option" Editable="true" Groupable="false" />
                        <GridColumn Field="AssemblySize" Title="Option Size" Editable="true" Groupable="false" Width="0" />
                        <GridColumn Field="AssemblyUnit" Title="Unit" Editable="false" Groupable="false" Width="0" />
                        <GridColumn Field="HomeAreaId" Title="Home Area" Editable="false" Groupable="false" Width="0">
                            <EditorTemplate>
                                @{
                                    SelectedOption = context as AsmHeaderModel;
                                    <TelerikDropDownList @bind-Value="@SelectedOption.HomeAreaId"
                                                         Data="@HomeAreaOptions"
                                                         TextField="HomeArea1"
                                                         ValueField="HomeAreaId"
                                                         Width="100%">
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                        </GridColumn>
                        <GridColumn Field="OptionGroupName" Title="Group" Editable="false" Groupable="true">
                        </GridColumn>
                        <GridColumn Field="BoolIsElevation" Title="Elevation" Editable="true" EditorType="@GridEditorType.CheckBox" Groupable="false" Width="0" />
                        <GridColumn Field="BoolIsBaseHouse" Title="Base House" Editable="false" EditorType="@GridEditorType.CheckBox" Groupable="false" Width="0" />
                        <GridColumn Field="AssemblyNotes" Title="Notes" Editable="true" EditorType="@GridEditorType.TextArea" Groupable="false" Width="0" />
                        <GridColumn Field="AsmHeaderId" Visible="false" Editable="false" Groupable="false" />
                        <GridColumn Title="Add/Update Plan(s)" Editable = "false" Width="0px"> 
                            <EditorTemplate>
                                @{
                                    CurrentlySelectedMasterPlans = context as AsmHeaderModel;

                                    <TelerikMultiSelect Class="selected-items-container"
                                                        ScrollMode="DropDownScrollMode.Virtual"
                                                        ItemHeight="30"
                                                        PageSize="20"
                                                        TextField="DisplayName"
                                                        ValueField="MasterPlanId"
                                                        TagMode="@MultiSelectTagMode.Multiple"
                                                        MaxAllowedTags="5"
                                                        Data="@MasterPlanData"
                                                        ShowClearButton="true"
                                                        @bind-Value="@CurrentlySelectedMasterPlans.MasterPlanIds"
                                                        AutoClose="false"
                                                        Filterable="true"
                                                        FilterOperator="StringFilterOperator.Contains"
                                                        Placeholder="Select Plans">
                                        <MultiSelectSettings>
                                            <MultiSelectPopupSettings Height="auto" />
                                        </MultiSelectSettings>
                                    </TelerikMultiSelect>
                                }
                            </EditorTemplate>
                        </GridColumn>
                        <GridCommandColumn>
                        @if(AllowEdit)
                        {
                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>

                        }
                        </GridCommandColumn>
                    </GridColumns>
                    <DetailTemplate>
                        @{
                            var item = context as AsmHeaderModel;
                            <p>Description: @item.AssemblyDesc</p>
                            <p>Category: @item.OptionGroupName</p>
                            <p>Option Size: @item.AssemblySize</p>
                            <p>Unit: @item.AssemblyUnit</p>
                            <p>Elevation: @item.IsElevation</p>
                            <p>Base House: @item.IsBaseHouse</p>
                            <p>Home Area: @item.HomeAreaName</p>
                            <p>Notes: @item.AssemblyNotes</p>
                        }
                    </DetailTemplate>
                    <GridToolBarTemplate>
                    @if(AllowEdit)
                    {
                            <GridCommandButton Command="AddOptionCommand" Icon="@FontIcon.Plus" OnClick="@AddOptionFromToolbar" Class="k-button-add">Add Option</GridCommandButton>
                    }
                        
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <GridSettings>
                        <GridPopupEditSettings Width="400px"
                                               Height="600px"
                                               Title="Option">
                        </GridPopupEditSettings>
                    </GridSettings>
                </TelerikGrid>
            }
        </div>
        <div class="col-5">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Selected Option: @SelectedOption?.AssemblyDesc</h7>
                </div>
            </div>

            @if (MasterItemData == null)
            {
                <p><em>Select an activity to see items</em></p>
                <div style=@loadingItemStyle>Loading...</div>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingItem />
            }
            else
            {
                <TelerikGrid Data=@MasterItemData
                             FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                             Height="1000px" RowHeight="60" PageSize="20"
                             Sortable="true"
                             Resizable="true"
                             Reorderable="true"
                             Groupable="true"
                             SelectionMode="GridSelectionMode.Single"
                             EditMode="@GridEditMode.Incell"
                             OnUpdate="@UpdateItemHandler"
                             OnEdit="@EditItemHandler"
                             OnDelete="@DeleteItemHandler"
                             OnCreate="@CreateItemHandler"
                             OnCancel="@CancelItemHandler"
                             ConfirmDelete="true"
                             @ref="@ItemGridRef">
                    <GridColumns>
                        <GridColumn Field="PlanSpecific" Title="Plan Specific" Editable="false" Width="30px" Groupable="false" />
                        <GridColumn Field="ItemDesc" Title="Item" Editable="false" Groupable="false" />
                        <GridColumn Field="BomClassName" Title="Activity" Editable="true" Groupable="true">
                            <EditorTemplate>
                                @{
                                    SelectedItem = context as ModelManagerItemModel;
                                    <TelerikDropDownList @bind-Value="@SelectedItem.BomClassId"
                                                         Data="@AllPactivities"
                                                         TextField="Activity"
                                                         ValueField="BomClassId"
                                                         Filterable="true"
                                                         FilterOperator="@StringFilterOperator.Contains"
                                                         Width="100%">
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                        </GridColumn>
                        <GridColumn Field="ItemNumber" Title="Item Number" Editable="false" Groupable="false" Width="0" />
                        <GridColumn Field="TakeoffUnit" Title="Unit" Editable="true" Groupable="false">
                            <EditorTemplate>
                                @{
                                    SelectedItem = context as ModelManagerItemModel;
                                    <TelerikDropDownList @bind-Value="@SelectedItem.TakeoffUnit"
                                                         Data="@TakeoffUnitOptions"
                                                         Width="100%">
                                    </TelerikDropDownList>
                                }
                            </EditorTemplate>
                        </GridColumn>
                        <GridColumn Field="Factor" Title="Quantity" Editable="true" Groupable="false" />
                        <GridColumn Field="OptionItemNotes" Title="Notes" Editable="true" EditorType="@GridEditorType.TextArea" Groupable="false" />
                        <GridColumn Field="MasterItemId" Visible="false" Editable="false" Groupable="false" />
                        <GridCommandColumn>
                        @if(AllowEdit)
                        {
                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                        }

                        </GridCommandColumn>
                    </GridColumns>
                    <DetailTemplate>
                        @{
                            var item = context as ModelManagerItemModel;

                            <div class="row p-1">
                                <div class="col-6">
                                    <p>Phase Code: @item.PhaseCode</p>
                                    <p>Code: @item.ItemNumber</p>
                                    <p>Purchasing Activity: @item.BomClassName</p>
                                    <p>Takeoff Quantity: @item.Factor</p>
                                    <p>Takeoff Unit: @item.TakeoffUnit</p>
                                    <p>Option Item Notes: @item.OptionItemNotes</p>
                                    <p>Plan Specific: @item.PlanSpecific </p>
                                </div>
                                <div class="col-6">
                                    <p><em>All Plans Options with this Item:</em></p>
                                    @foreach(var opt in item.OptionsContainingItem)
                                    {
                                        <p>@opt</p>
                                    }
                                </div>
                            </div>                           
                        }
                    </DetailTemplate>
                    <GridToolBarTemplate>
                    @if(AllowEdit){
                            <GridCommandButton Command="MyToolbarCommand" Icon="@FontIcon.Plus" OnClick="@MyCommandFromToolbar" Class="k-button-add">Add Item</GridCommandButton>
                    }
                        
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <GridSettings>
                        <GridPopupEditSettings Width="400px"
                                               Height="600px"
                                               Title="Item">
                        </GridPopupEditSettings>
                    </GridSettings>
                </TelerikGrid>
            }

        </div>
    </div>
</div>

<ERP.Web.Components.AddItemToPlanOption @ref="AddItemModal" Option=@SelectedOption HandleAddSubmit="HandleValidAddItemSubmit"></ERP.Web.Components.AddItemToPlanOption>
<ERP.Web.Components.AddOptionToPlan @ref="AddOptionModal" Plan=@SelectedPlan HandleAddSubmit="HandleValidAddOptionSubmit"></ERP.Web.Components.AddOptionToPlan>
<ERP.Web.Components.CopyPlanToNewPlan @ref="CopyPlanModal" HandleAddSubmit="HandleValidCopyPlanSubmit"></ERP.Web.Components.CopyPlanToNewPlan>

@code {
    private TelerikGrid<MasterPlanDto>? PlanGridRef { get; set; }
    private TelerikGrid<AsmHeaderModel>? OptionGridRef { get; set; }
    private TelerikGrid<ModelManagerItemModel>? ItemGridRef { get; set; }
    public List<MasterPlanDto>? MasterPlanData { get; set; }
    public List<AsmHeaderModel>? MasterOptionData { get; set; }
    public List<ModelManagerItemModel>? MasterItemData { get; set; }
    public List<PactivityModel>? AllPactivities { get; set; }
    public AsmHeaderModel SelectedOption { get; set; }
    public MasterPlanDto SelectedPlan { get; set; }
    public ModelManagerItemModel SelectedItem { get; set; }
    public List<string> TakeoffUnitOptions = new List<string> { "LS", "EA", "LF", "SF", "TON", "UKN" };
    protected ERP.Web.Components.AddOptionToPlan? AddOptionModal { get; set; }
    protected ERP.Web.Components.AddItemToPlanOption? AddItemModal { get; set; }
    protected ERP.Web.Components.CopyPlanToNewPlan? CopyPlanModal { get; set; }

    public List<PlanTypeDto> PlanTypeOptions = new List<PlanTypeDto>();
    public List<HomeAreaDto> HomeAreaOptions = new List<HomeAreaDto>();
    public List<OptionGroupDto> OptionGroupOptions = new List<OptionGroupDto>();

    private string loadingOptionStyle = "display:none";
    private string loadingItemStyle = "display:none";
    public bool IsLoadingOption { get; set; } = false;
    public bool IsLoadingPlan { get; set; } = false;
    public bool IsLoadingItem { get; set; } = false;
    private bool AllowEdit { get; set; } = true;
    public string? ErrorMessage;
    public bool? ShowError;

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    public AsmHeaderModel CurrentlySelectedMasterPlans { get; set; }

    protected override async Task OnInitializedAsync()
    {
        IsLoadingPlan = true;
        var pactivitiesTask = ItemService.GetPurchasingActivitiesAsync();
        var masterPlanTask = PlanService.GetMasterPlansAsync();
        var homeAreasTask = OptionService.GetHomeAreasAsync();
        var groupsTask = OptionService.GetOptionGroupsAsync();
        var planTypesTask = PlanService.GetPlanTypesAsync();
        await Task.WhenAll(new Task[] { pactivitiesTask, masterPlanTask, homeAreasTask, groupsTask, planTypesTask });
        AllPactivities = pactivitiesTask.Result.Value;
        var masterPlanData = masterPlanTask.Result;
        MasterPlanData = masterPlanData.Value;
        var planTypesData = planTypesTask.Result;
        PlanTypeOptions = planTypesData.Value;
        HomeAreaOptions = homeAreasTask.Result.Value;
        var getGroups = groupsTask.Result;
        ShowError = getGroups.IsSuccess;
        ErrorMessage = getGroups.Message;
        OptionGroupOptions = getGroups.Value;
        IsLoadingPlan = false;
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }

    protected async Task OnPlanRowClickHandler(GridRowClickEventArgs args)
    {
        loadingOptionStyle = "";
        IsLoadingOption = true;
        MasterOptionData = null;
        MasterItemData = null;
        SelectedPlan = args.Item as MasterPlanDto;
        MasterOptionData = (await ItemService.GetAssembliesInPlanWithOtherMasterPlanAsync(SelectedPlan.MasterPlanId)).Value;
        MasterItemData = null;//reset the master item data else it shows items from a previously selected trade
        loadingOptionStyle = "display:none";
        IsLoadingOption = false;
    }

    protected async Task OnOptionRowClickHandler(GridRowClickEventArgs args)
    {
        IsLoadingItem = true;
        SelectedOption = args.Item as AsmHeaderModel;
        MasterItemData = (await ItemService.GetItemsInAssemblyAsync((int)SelectedOption.AsmHeaderId)).Value;//OptionId here is asmheader id, not master optionid
        IsLoadingItem = false;
    }

    void EditPlanHandler(GridCommandEventArgs args)
    {
        MasterPlanDto item = (MasterPlanDto)args.Item;
    }

    async Task UpdatePlanHandler(GridCommandEventArgs args)
    {
        MasterPlanDto item = (MasterPlanDto)args.Item;
        var updatePlanResponse = await PlanService.UpdateMasterPlanAsync(item);
        var masterPlanResponse = await PlanService.GetMasterPlansAsync();
        MasterPlanData = masterPlanResponse.Value;
        ShowSuccessOrErrorMessage(updatePlanResponse.Message, updatePlanResponse.IsSuccess);
    }

    async Task DeletePlanHandler(GridCommandEventArgs args)
    {
        MasterPlanDto item = (MasterPlanDto)args.Item;
        var deletePlanResponse = await PlanService.DeleteMasterPlanAsync(item);
        var masterPlanResponse = await PlanService.GetMasterPlansAsync();
        MasterPlanData = masterPlanResponse.Value;
        ShowSuccessOrErrorMessage(deletePlanResponse.Message, deletePlanResponse.IsSuccess);        
    }

    async Task CreatePlanHandler(GridCommandEventArgs args)
    {
        MasterPlanDto item = (MasterPlanDto)args.Item;
        var response = await PlanService.AddMasterPlanAsync(item);
        //refresh the data
        var masterPlanResponse = await PlanService.GetMasterPlansAsync();
        MasterPlanData = masterPlanResponse.Value;
        //show message
        ShowSuccessOrErrorMessage(response.Message, response.IsSuccess);
    }

    async Task CancelPlanHandler(GridCommandEventArgs args)
    {
        MasterPlanDto item = (MasterPlanDto)args.Item;
    }

    void EditOptionHandler(GridCommandEventArgs args)
    {
        AsmHeaderModel item = (AsmHeaderModel)args.Item;
    }

    async Task UpdateOptionHandler(GridCommandEventArgs args)
    {
        AsmHeaderModel item = (AsmHeaderModel)args.Item;
        var response = await ItemService.UpdateAssemblyAsync(item);
        MasterOptionData = (await ItemService.GetAssembliesInPlanWithOtherMasterPlanAsync(SelectedPlan.MasterPlanId)).Value;

        ShowSuccessOrErrorMessage(response.Message, response.IsSuccess);
    }

    async Task DeleteOptionHandler(GridCommandEventArgs args)
    {
        AsmHeaderModel item = (AsmHeaderModel)args.Item;
        var response = await ItemService.DeleteAsmHeaderAsync(item.AsmHeaderId);
        MasterOptionData = (await ItemService.GetAssembliesInPlanWithOtherMasterPlanAsync(SelectedPlan.MasterPlanId)).Value;

        if (response.IsSuccess == false)
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = response.Message,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = response.Message,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }
    }

    async Task CreateOptionHandler(GridCommandEventArgs args)
    {
        AsmHeaderModel item = (AsmHeaderModel)args.Item;
    }

    async Task CancelOptionHandler(GridCommandEventArgs args)
    {
        AsmHeaderModel item = (AsmHeaderModel)args.Item;
    }

    void EditItemHandler(GridCommandEventArgs args)
    {

    }

    async Task UpdateItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
        var asmDetailToUpdate = new AsmDetailDto()
            {
                AsmHeaderId = item.AsmHeaderId,
                MasterItemId = item.MasterItemId,
                AsmDetailId = (int)item.AsmDetailId,
                SelectAtTakeoffUnit = item.TakeoffUnit,//TODO: fix so it's master item takeoff unit
                Factor = item.Factor,
                BomClassId = item.BomClassId,
                OptionItemNotes = item.OptionItemNotes,
            };
        var response = await ItemService.UpdateAsmDetailsAsync(asmDetailToUpdate);
        MasterItemData = (await ItemService.GetItemsInAssemblyAsync((int)SelectedOption.AsmHeaderId)).Value;//OptionId here is asmheader id, not master optionid

        ShowSuccessOrErrorMessage(response.Message, response.IsSuccess);
    }

    async Task DeleteItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
        var asmDetailToUpdate = new AsmDetailDto()
            {
                AsmDetailId = (int)item.AsmDetailId,
                MasterItemId = item.MasterItemId,
            };
        var response = await ItemService.DeleteAsmDetailAsync(asmDetailToUpdate);
        MasterItemData = (await ItemService.GetItemsInAssemblyAsync((int)SelectedOption.AsmHeaderId)).Value;//OptionId here is asmheader id, not master optionid

        ShowSuccessOrErrorMessage(response.Message, response.IsSuccess);
    }

    async Task CreateItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
    }

    async Task CancelItemHandler(GridCommandEventArgs args)
    {
        ModelManagerItemModel item = (ModelManagerItemModel)args.Item;
    }

    private void MyCommandFromToolbar(GridCommandEventArgs args)
    {
        //note - the args.Item object is null because the command item is not associated with an item
        AddItemModal.Show();
        StateHasChanged();
    }

    private void AddOptionFromToolbar(GridCommandEventArgs args)
    {
        //note - the args.Item object is null because the command item is not associated with an item
        AddOptionModal.Show();
        StateHasChanged();
    }

    private void CopyPlanFromToolbar(GridCommandEventArgs args)
    {
        //note - the args.Item object is null because the command item is not associated with an item
        CopyPlanModal.Show();
        StateHasChanged();
    }

    private async void HandleValidCopyPlanSubmit(ResponseModel<MasterPlanDto> responseItem)
    {
        var masterPlanResponse = await PlanService.GetMasterPlansAsync();
        MasterPlanData = masterPlanResponse.Value;
        CopyPlanModal.Hide();
        ShowSuccessOrErrorMessage(responseItem.Message, responseItem.IsSuccess);
        StateHasChanged();
    }

    private async void HandleValidAddOptionSubmit(ResponseModel<AddOptionToPlanModel> response)
    {
        ShowSuccessOrErrorMessage(response.Message, response.IsSuccess);
        if (response.IsSuccess)
        {
            MasterOptionData = (await ItemService.GetAssembliesInPlanWithOtherMasterPlanAsync(SelectedPlan.MasterPlanId)).Value;
        }
        AddOptionModal.Hide();       
        StateHasChanged();
    }

    private async void HandleValidAddItemSubmit(List<ModelManagerItemModel> responseItem)
    {
        MasterItemData = MasterItemData = (await ItemService.GetItemsInAssemblyAsync((int)SelectedOption.AsmHeaderId)).Value;//OptionId here is asmheader id, not master optionid//TODO: check this should probably be option id not asmheader id
        AddItemModal.Hide();
        StateHasChanged();
    }

    async void ShowSuccessOrErrorMessage(string message, bool isSuccess)
    {
        // Alert
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });       
    }
}
