﻿@inject OptionService OptionService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="500px"
               Height="300px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Group(s) to Option
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@GroupToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <p>
                <label>Select Multiple Groups to Add To Option:</label>
                <TelerikMultiSelect Data="@AllMasterAttributeGroup"
                                    TextField="Description"
                                    ValueField="AttributeGroupId"
                                    Placeholder="Select Master Attribute Group"
                                    Filterable="true"
                                    FilterOperator="StringFilterOperator.Contains"
                                    @bind-Value="@SelectedMasterAttributeGroups"
                                    Width="100%">
                </TelerikMultiSelect>
            </p>
            <button type="submit" class="btn btn-primary">Add</button>
            <button type="button" @onclick="CancelAddItem" class="btn btn-secondary">Cancel</button>
            <div style=@submittingStyle>Adding groups. Please wait...</div>
        </EditForm>
    </WindowContent>
</TelerikWindow>

@code {
    public bool IsModalVisible { get; set; }
    public MasterAttributeGroupDto GroupToAdd { get; set; } = new MasterAttributeGroupDto();
    public List<MasterAttributeGroupDto>? AllMasterAttributeGroup { get; set; }
    public List<int>? SelectedMasterAttributeGroups { get; set; }
    private List<MasterOptionAttributeItemDto> GroupToAssign { get; set; }

    private string submittingStyle = "display:none";

    [Parameter]
    public MasterOptionHeaderModel Option { get; set; }
    [Parameter]
    public EventCallback<ResponseModel> HandleAddSubmit { get; set; }

    public async Task Show()
    {
        SelectedMasterAttributeGroups = null;
        IsModalVisible = true;
        await LoadMasterAttributeGroupsAsync();
        StateHasChanged();
    }

    private async Task LoadMasterAttributeGroupsAsync()
    {
        var getMasterAttributeGroups = await OptionService.GetMasterAttributeGroupsAsync();
        AllMasterAttributeGroup = getMasterAttributeGroups.Value;
    }

    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";

        SelectedMasterAttributeGroups.Add(Option.OptionId);

        if (SelectedMasterAttributeGroups.Any())
        {
            GroupToAssign = new List<MasterOptionAttributeItemDto>();

            foreach (var item in SelectedMasterAttributeGroups)
            {
                GroupToAssign.Add(new MasterOptionAttributeItemDto
                    {
                        AttributeGroupId = item,
                        MasterOptionId = Option.OptionId
                    });
            }

            var responseItem = await OptionService.AddMasterOptionAttributeItemAsync(GroupToAssign);
            var responseModel = new ResponseModel() { IsSuccess = responseItem.IsSuccess, Message = responseItem.Message };
            await HandleAddSubmit.InvokeAsync(responseModel);
        }

        submittingStyle = "display:none";
    }

    async void CancelAddItem()
    {
        IsModalVisible = false;
    }

    public async Task Hide()
    {
        IsModalVisible = false;
    }
}
