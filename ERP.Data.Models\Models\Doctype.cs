﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Doctype
{
    public int DoctypeId { get; set; }

    public string? Doctype1 { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<JobAttachment> JobAttachments { get; set; } = new List<JobAttachment>();

    public virtual ICollection<PoAttachment> PoAttachments { get; set; } = new List<PoAttachment>();
}
