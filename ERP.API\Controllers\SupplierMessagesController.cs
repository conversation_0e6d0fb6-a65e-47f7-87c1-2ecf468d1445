﻿
using AutoMapper;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using ERP.API.Utilities;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Data;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class SupplierMessagesController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IWebHostEnvironment _env;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        private readonly Email _email;

        public SupplierMessagesController(IConfiguration configuration, ErpDevContext context, IMapper mapper, IWebHostEnvironment env)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _email = new Email(configuration);
            _env = env;
        }

        /// <summary>
        /// get supplier insurance
        /// </summary>
        /// <param name="subNum">The supplier number</param>
        /// <returns></returns>
        [HttpGet("{subNum}")]
        public async Task<IActionResult> GetSupplierMessagesAsync(int subNum)
        {
            try
            {
                var supplierMessages = await _context.SupplierCommunications.AsNoTracking().Where(x => x.SubNumber == subNum && x.IsActive== true).ToListAsync();
                var supplierMessagesDto = _mapper.Map<List<SupplierCommunicationDto>>(supplierMessages);
                return Ok(new ResponseModel<List<SupplierCommunicationDto>> {Value = supplierMessagesDto, Message = "Fetched supplier messages", IsSuccess = true});
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SupplierCommunicationDto> { IsSuccess = false, Message = "Failed to fetch supplier messages ", Error = $"{ex.Message} {ex.InnerException?.Message}", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SendEmailAsync([FromBody] EmailModel emailModel)
        {
            var getEmail = emailModel.Emails;
            string emailSubject = emailModel.Subject;
            string emailBody = emailModel.Body;
            if (_env.IsDevelopment())
            {
                getEmail = new List<string>() { "<EMAIL>" };//send to me in test
                emailBody = $"TEST {emailModel.Body}";
                emailSubject = $" TEST {emailModel.Subject}";
            }
                     
            string emailTo = string.Join(",", emailModel.Emails);
            try
            {
                var saveMessage = new SupplierCommunication()
                {
                    SubNumber = emailModel.SupplierNumber ?? 0,
                    JobNumber = null,
                    ScheduleAid = null,
                    PoheaderId = null,
                    IsActive = true,
                    CreatedBy = User.Identity.Name.Split('@')[0],
                    MessageSubject = emailSubject,
                    Message = emailBody,
                    SendFrom = emailModel.FromEmail,
                    SendTo = emailTo,
                };
                var sendGridMessageModel = emailModel.Emails.Select(x => new SendGridEmailModel()
                {
                    Email = x,
                    MessageSubject = emailSubject,
                    MessageBody = emailBody,
                    
                }).ToList();
                if (getEmail != null) 
                {
                   // await _email.SendGridSendEmail(sendGridMessageModel, files: emailModel.Files);
                    _email.SendEmail(emailSubject, emailBody, getEmail, internalMessage: false, replyTo: emailModel.FromEmail, attachments: emailModel.Files);
                }
                //TODO: save the attachment
                                
                _context.SupplierCommunications.Add(saveMessage);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EmailModel> { IsSuccess = false, Message = "Failed to send Email", Value = null });
            }

            return Ok(new ResponseModel<EmailModel>() { Value = emailModel, IsSuccess = true, Message = "Email sent" });
        }

        [HttpGet]
        public async Task<IActionResult> GetSuppliersAsync()
        {
            //TODO: Subcontact_email, subcontact2email, is not used. Get the contacts from the Supplier_Contacts table
            try
            {
                List<SupplierDto> suppliers = new List<SupplierDto>();
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    var query = "SELECT SUB_NUMBER, SHORT_NAME, SUB_NAME FROM dbo.SUPPLIER WHERE IsActive = 1";

                    var command = new SqlCommand(query, connection);
                    var reader = await command.ExecuteReaderAsync();

                    while (reader.Read())
                    {
                        suppliers.Add(new SupplierDto()
                        {
                            SubNumber = reader.GetValue(0) != DBNull.Value ? Convert.ToInt32(reader.GetValue(0)) : 0,
                            ShortName = reader.GetValue(1) != DBNull.Value ? (string)(reader.GetValue(1)) : "",
                            SubName = reader.GetValue(2) != DBNull.Value ? (string)(reader.GetValue(2)) : "",
                        });
                    }
                }
                return Ok(new ResponseModel<List<SupplierDto>>() { Value = suppliers, IsSuccess = true, Message = "Got suppliers data" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SupplierDto>> { IsSuccess = false, Message = "Failed to get data", Value = null });

            }
        }

        /// <summary>
        /// get supplier insurance
        /// </summary>
        /// <param name="subNum">The supplier number</param>
        /// <returns></returns>
        [HttpGet("{subNum}")]
        public async Task<IActionResult> GetSupplierContactsAsync(int subNum)
        {
            try
            {
                var supplierContacts = await _context.SupplierContacts.AsNoTracking().Include(x => x.Contact).Where(x => x.SubNumber == subNum && x.IsActive == true).ToListAsync();
                var supplierContactsDto = _mapper.Map<List<SupplierContactDto>>(supplierContacts);
                return Ok(new ResponseModel<List<SupplierContactDto>> { Value = supplierContactsDto, Message = "Fetched supplier contacts", IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SupplierInsuranceDto> { IsSuccess = false, Message = "Failed to fetch supplier contacts ", Error = $"{ex.Message} {ex.InnerException.Message}", Value = null });
            }
        }
    }
}
