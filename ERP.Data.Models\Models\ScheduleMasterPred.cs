﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ScheduleMasterPred
{
    public int ScheduleMasterId { get; set; }

    public int ScheduleAid { get; set; }

    public int PredScheduleAid { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ScheduleMaster ScheduleMaster { get; set; } = null!;
}
