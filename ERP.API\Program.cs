
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication;
using Microsoft.OpenApi.Models;
using Microsoft.Identity.Web;
using NLog;
using ERP.API.Data;
using ERP.Data.Models;
using AutoMapper;
using System.Reflection;
using ERP.API.Utilities;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.Identity.Abstractions;
using ERP.API.ExtensionMethods;


LogManager.LoadConfiguration(string.Concat(Directory.GetCurrentDirectory(), "/nlog.config"));
var builder = WebApplication.CreateBuilder(args);

ConfigurationHelper.Initialize(builder.Configuration);

builder.Services.AddAutoMapper(Assembly.Load("ERP.Data.Models"));

var initialScopes = builder.Configuration["DownstreamApi:Scopes"]?.Split(' ') ?? builder.Configuration["MicrosoftGraph:Scopes"]?.Split(' ');
//builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
//    .AddMicrosoftIdentityWebApi(builder.Configuration.GetSection("AzureAd"))
//    .EnableTokenAcquisitionToCallDownstreamApi()//need scopes, but canpt put in string []
//    .AddDownstreamApi("BuisinessCentral", builder.Configuration.GetSection("BusinessCentral"))
//    .AddMicrosoftGraph(builder.Configuration.GetSection("GraphApi"))
//    .AddInMemoryTokenCaches();


builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddMicrosoftIdentityWebApi(builder.Configuration.GetSection("AzureAd"))
    .EnableTokenAcquisitionToCallDownstreamApi()
    .AddDownstreamApi("BusinessCentral", builder.Configuration.GetSection("BusinessCentral")).UseHttpClientTimeout("DownstreamApi", TimeSpan.FromMinutes(10))
    .AddMicrosoftGraph(builder.Configuration.GetSection("GraphApi"))
    .AddInMemoryTokenCaches();


builder.Services.AddAuthorization();

builder.Services.AddTransient<ExportService>();

builder.Services.AddControllers();
//builder.Services.AddControllers(
  //  options => options.SuppressImplicitRequiredAttributeForNonNullableReferenceTypes = true);//without this the model state is invalid for nonnullable types eg string


// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
//builder.Services.AddSwaggerGen();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Swagger for ERP API", Version = "v1" });
    c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
    {
        Description = "OAuth2.0 Auth Code with PKCE",
        Name = "oauth2",
        Type = SecuritySchemeType.OAuth2,
        Flows = new OpenApiOAuthFlows
        {
            AuthorizationCode = new OpenApiOAuthFlow
            {
                AuthorizationUrl = new Uri(builder.Configuration["AuthorizationUrl"]),
                TokenUrl = new Uri(builder.Configuration["TokenUrl"]),
                Scopes = new Dictionary<string, string>
                {
                    { builder.Configuration["ApiScope"], "access_as_user" }
                }
            }
        }
    });
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference { Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme, Id = "oauth2" }
            },
            new[] { builder.Configuration["ApiScope"] }
        }
    });
});

// Register DBContext
string connString = builder.Configuration.GetConnectionString("ERPConnection");
//builder.Services.AddDbContext<ErpDevContext>(options => options.UseSqlServer(connString));
builder.Services.AddDbContext<ErpDevContext>(options => options.UseSqlServer(connString, opts => opts.CommandTimeout(300)));//try change timeout. not sure this works
builder.Services.AddCors(p => p.AddPolicy("corsapp", builder =>
{
    builder.WithOrigins("*").AllowAnyMethod().AllowAnyHeader();//TODO: change to only allow erp
}));



var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
   // app.UseSwaggerUI();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "swaggerAADdemo v1");
        c.OAuthClientId(builder.Configuration["OpenIdClientId"]);
        //c.OAuthClientSecret(builder.Configuration["ClientSecret"]);
       // c.OAuthUseBasicAuthenticationWithAccessCodeGrant();
        //c.OAuth2RedirectUrl(builder.Configuration["RedirectURL"]);
        c.OAuthUsePkce();
        c.OAuthScopeSeparator(" ");
    });
}

app.UseCors("corsapp");

app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();
app.MapDefaultControllerRoute();
app.MapControllers();

app.Run();

