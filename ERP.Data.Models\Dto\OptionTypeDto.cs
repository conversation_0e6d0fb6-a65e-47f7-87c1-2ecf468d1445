﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class OptionTypeDto: IMapFrom<OptionType>
{
    public int OptionTypeId { get; set; }

    public string? OptionType1 { get; set; } 

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    //  public byte[] RecordTimeStampe { get; set; } = null!;

    // public virtual ICollection<AvailablePlanOption> AvailablePlanOptions { get; set; } = new List<AvailablePlanOption>();

    public void Mapping(Profile profile)
    {
        profile.CreateMap<OptionTypeDto, OptionType>().ReverseMap();
    }
}
