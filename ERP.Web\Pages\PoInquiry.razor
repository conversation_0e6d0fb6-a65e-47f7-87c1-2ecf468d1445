﻿@page "/poinquiry"
@inject PoService PoService
@inject SubdivisionService SubdivisionService
@inject TradeService TradeService
@inject BudgetService BudgetService
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject IJSRuntime JsRuntime
@implements IDisposable
@inject ProtectedSessionStorage ProtectedSessionStore
@inject AuthenticationStateProvider AuthenticationStateProvider
@using Telerik.Blazor.Components.Grid
@using Telerik.Documents.SpreadsheetStreaming
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ReadOnly, Accounting")]
<style>
    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>

<PageTitle>Purchase Order Inquiry</PageTitle>
<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="true" ShowJob="true" ShowSubdivision="true"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
<TelerikTooltip TargetSelector=".tooltip-target" />
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<div class="container-fluid flex">
    <div class="col-lg-12">
        <div class="card" style="background-color: #2e5771">
            <div class="card-body" style="padding:0.5rem">
                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Purchase Order Inquiry</h7>
            </div>
        </div>
    </div>

    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item active">Purchase Order Inquiry</li>
    </ol>

    <div class="row d-flex">
        @if (displayLoadingSpinner)
        {
            <p><em>Loading...</em></p>
            <TelerikLoader></TelerikLoader>
        }
        <div class="container-fluid">
            <div class="row">
@*                 <TelerikContextMenu @ref="@ContextMenuRef" Data="@MenuItems" OnClick="@((ContextMenuItem item) => OnItemClick(item))"></TelerikContextMenu> *@
                <TelerikGrid Data="@CurrentSelectedViewPOsData"
                             ScrollMode="@GridScrollMode.Virtual"
                             PageSize="40"
                             Height="1000px"
                             RowHeight="40"
                             Resizable="true"
                             FilterMode="GridFilterMode.FilterMenu"
                            @ref="@POInquiryGrid"
                                Sortable="true">
                    <GridToolBarTemplate>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                        <label for="PONumberFilter">Purchase Order #:</label>
                        <TelerikTextBox Id="PONumberFilter" @bind-Value="@PurchaseOrderFilter" OnChange="OnPurchaseOrderFilterChange" Width="100px" />

                        <TelerikToggleButton @bind-Selected="@IsSupplierSelected"
                                             OnClick="@OnJobSupplierToggleButtonClick">
                            View By Job/Supplier: <strong>@SupplierOrJobSelected</strong>
                        </TelerikToggleButton>
                        <TelerikToggleButton @bind-Selected="@IsCancelledSelected"
                                             OnClick="@OnActiveCancelledToggleButtonClick">
                        Active/Cancelled: <strong>@ActiveOrCanceled</strong>
                        </TelerikToggleButton>
                       @*  <TelerikDropDownList Data="@( new List<string>() { "Active", "Canceled" } )" @bind-Value="@ActiveOrCanceled" OnChange="@ToggleCanceledIssuedPOs" Width="110px" />
                        <TelerikDropDownList Data="@( new List<string>() { "Supplier", "Job" } )" @bind-Value="@SupplierOrJobSelected" OnChange="@OnViewOptionChange" Width="110px" /> *@
                        <label for="StartDate">StartDate</label>
                        <TelerikDatePicker @bind-Value="@StartDateValue"
                                          Id="StartDate"
                                          Enabled=!IsAllDatesSelected
                                          Format="d"
                                          OnChange="@OnDateChange"
                                          AutoSwitchKeys="@AutoSwitchKeys">
                        </TelerikDatePicker>                        
                        <label for="EndDate">EndDate</label>
                        <TelerikDatePicker @bind-Value="@EndDateValue"
                                          Format="d"
                                          Enabled=!IsAllDatesSelected
                                          Id="EndDate"
                                          OnChange="@OnDateChange"
                                          AutoSwitchKeys="@AutoSwitchKeys">
                        </TelerikDatePicker>
                        <TelerikCheckBox Id="AllDates" @bind-Value="@IsAllDatesSelected" OnChange="@ToggleAllDatesView" />
                        @if(SupplierOrJobSelected == "Job")
                        {
                            <label for="AllDates">Show all Dates for Selected @SupplierOrJobSelected</label>
                        }
                        else
                        {
                            <label for="AllDates">Show all Dates (past 3 years) for Selected @SupplierOrJobSelected</label>
                        }
                        
@*                         <span>Time: @TimeToLoad</span>
                        <span>Total Records: @TotalRecords</span> *@
                        <GridCommandButton Command="ExcelExport" Icon="@FontIcon.FileExcel" Class="k-button-add">Export to Excel</GridCommandButton>
                    </GridToolBarTemplate>
                    <GridExport>
                        <GridExcelExport FileName="PurchaseOrders" OnBeforeExport="@OnExcelBeforeExport" AllPages="true" />
                    </GridExport>
                    <GridColumns>
                        <GridCommandColumn Width="80px">
                            <GridCommandButton Title="Download" Command="Download" OnClick="@Download" Icon="@FontIcon.Download" ></GridCommandButton>
                            @if(AllowEdit)
                            {
                                <GridCommandButton Title="Email" Command="Email" OnClick="@Email" Icon="@FontIcon.Envelop" Class="k-button-success"></GridCommandButton>
                            }
                            
                        </GridCommandColumn>
                        <GridColumn Field="Releasecode" Title="Release Code" Width="40px"/>
                        <GridColumn Field="Pojobnumber" Title="Job Number" Visible="@IsSupplierSelected" Width="80px" />
                        <GridColumn Field="Ponumber" Title="PO Number" Width="100px" />
                        <GridColumn Field="VpoNumber" Title="VPO Number" Width="80px" />
                        <GridColumn Field="SubNumberNavigation.SubName" Title="Supplier Name" FilterMenuType="@FilterMenuType.CheckBoxList" Width="190px" />
                        <GridColumn Field="Podescription" Title="Description" FilterMenuType="@FilterMenuType.CheckBoxList" Width="140px" />
                        <GridColumn Field="PostatusNavigation.Postatus1" Title="Status" FilterMenuType="@FilterMenuType.CheckBoxList" Width="80px" />
                        <GridColumn Field="Issuedby" Title="Issued By" Width="100px" />
                        <GridColumn Field="Podateissued" Title="Date Issued" DisplayFormat="{0:MM/dd/yy}" Width="80px" />
                        <GridColumn Field="Podateprinted" Title="Date Sent" DisplayFormat="{0:MM/dd/yy}" Width="80px" />
                        <GridColumn Field="TaskCompleteBy" Title="Approved By" Width="100px" />
                        <GridColumn Field="Podateapproved" Title="Date Fully Approved" DisplayFormat="{0:MM/dd/yy}" Width="80px" />
                        <GridColumn Field="Pototal" Title="Net" DisplayFormat="{0:C2}" Width="100px" />
                      @*   <GridColumn Field="Taxamount" Title="Tax Amount" DisplayFormat="{0:C2}" /> *@
                        <GridColumn Field="Cancelledby" Title="Canceled By" Visible="@IsCancelledSelected" Width="80px" />
                        <GridColumn Field="Podatecancelled" Title="Canceled Date" DisplayFormat="{0:MM/dd/yy}" Visible="@IsCancelledSelected" Width="100px" />
                        <GridColumn Field="PaymentCheckNumbers" Title="Check Numbers" Width="100px" />
                        <GridColumn Field="PaymentCheckDates" Title="Check Dates" Width="80px" />
                        <GridColumn Field="IsSchedulePayPoint" Title="Sch Link" Editable="false" Groupable="false" Width="40px">
                            <Template>
                                @{
                                    var item = (PoheaderDto)context;
                                    if (item.IsSchedulePayPoint == true)
                                    {
                                        <span class="tooltip-target" title="Schedule Linked"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Success" Icon="@FontIcon.CheckCircle" /></span>
                                    }
                                    else
                                    {
                                        <span class="tooltip-target" title="No Schedule Activity Link"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Error" Icon="@FontIcon.XCircle" /></span>
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </TelerikGrid>
            </div>
        </div>
        
    </div>
</div>

@code {
    private bool displayLoadingSpinner { get; set; } = false;
    private string? JobSelected { get; set; }
    private int? SelectedSupplier { get; set; }
    private string? PurchaseOrderFilter { get; set; }
    private TelerikGrid<PoheaderDto>? POInquiryGrid { get; set; }
    private List<PoheaderDto>? POsForJob { get; set; } = new List<PoheaderDto>();
    private List<PoheaderDto>? POsForSupplier { get; set; } = new List<PoheaderDto>();
    private List<PoheaderDto>? CurrentSelectedViewPOsData { get; set; }
    private string ActiveOrCanceled { get; set; } = "Active";
    private bool ExcludeClosedJobs { get; set; } = false;
    private List<string> ViewOptions { get; set; } = new List<string> { "Supplier", "Job" };
    private string SupplierOrJobSelected { get; set; } = "Job";
    private string SupplierOrJobSelectedChange { get; set; } = "Job";
    private bool IsAllDatesSelected { get; set; } = true;
    public DateTime StartDateValue { get; set; } = DateTime.Now.AddYears(-1);
    public DateTime EndDateValue { get; set; } = DateTime.Now;
    public DateTime StartDateValueChange { get; set; } = DateTime.Now.AddYears(-1);
    public DateTime EndDateValueChange { get; set; } = DateTime.Now;
    private List<object> AutoSwitchKeys { get; set; } = new List<object>() { ".", "/", " ", "-" };
    // private TelerikContextMenu<ContextMenuItem> ContextMenuRef { get; set; }
    // public PoheaderDto ClickedPORow { get; set; }
    // private List<ContextMenuItem> MenuItems { get; set; } = new List<ContextMenuItem>()
    // {
    //     new ContextMenuItem()
    //     {
    //         Text = "Download PO",
    //         Icon = FontIcon.Download
    //     },
    //     new ContextMenuItem()
    //     {
    //         Text = "Email PO",
    //         Icon = FontIcon.Envelop
    //     }
    // };

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    bool IsSupplierSelected { get; set; } = false;
    bool IsCancelledSelected { get; set; } = false;
    private bool AllowEdit { get; set; } = true;

    protected override async Task OnInitializedAsync()
    {
        SubdivisionJobPickService.OnChanged += JobSelectedHandler;
        SubdivisionJobPickService.OnChanged += SupplierSelectedHandler;
        if (SubdivisionJobPickService.JobNumber != null)
        {
            JobSelected = SubdivisionJobPickService.JobNumber;
        }
        if (SubdivisionJobPickService.SupplierNumber != null)
        {
            SelectedSupplier = SubdivisionJobPickService.SupplierNumber;
        }
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
        await LoadDataBasedOnSelection();
    }

    async Task OnJobSupplierToggleButtonClick()
    {
        SupplierOrJobSelected = IsSupplierSelected ? "Job" : "Supplier";
        //TODO: this should also check if the current supplier data is the same as the one in the box, so it doesn't load it again
        await LoadDataBasedOnSelection();
    }
    async Task OnActiveCancelledToggleButtonClick()
    {
        ActiveOrCanceled = IsCancelledSelected ? "Active" : "Canceled";
        if (SupplierOrJobSelected == "Job" && JobSelected != null)
        {
            CurrentSelectedViewPOsData = ActiveOrCanceled == "Canceled" ? POsForJob.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                        POsForJob.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
        }
        if (SupplierOrJobSelected == "Supplier" && SelectedSupplier != null)
        {
            CurrentSelectedViewPOsData = ActiveOrCanceled == "Canceled" ? POsForSupplier.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                        POsForSupplier.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
        }

        POInquiryGrid.Rebind();
        // await LoadDataBasedOnSelection();
    }
    protected async Task JobSelectedHandler()
    {
        if (SupplierOrJobSelected == "Job")
        {
            if(JobSelected != SubdivisionJobPickService.JobNumber)
            {
                displayLoadingSpinner = true;
                StateHasChanged();
                JobSelected = SubdivisionJobPickService.JobNumber;
                PurchaseOrderFilter = "";
                POsForJob = IsAllDatesSelected ? (await PoService.GetPoHeadersForJobAsync(JobSelected)).Value :
                                                (await PoService.GetPoHeadersForJobAsync(JobSelected, StartDateValue, EndDateValue)).Value;
                CurrentSelectedViewPOsData = ActiveOrCanceled == "Canceled" ? POsForJob.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                            POsForJob.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
                displayLoadingSpinner = false;
                StateHasChanged();
            }
        }
    }

    protected async Task SupplierSelectedHandler()
    {
        if (SupplierOrJobSelected == "Supplier" )
        {
            if(SelectedSupplier != SubdivisionJobPickService.SupplierNumber)//This is to prevent it calling the database if the supplier hasn't actually changed
            {
                SelectedSupplier = SubdivisionJobPickService.SupplierNumber;
                displayLoadingSpinner = true;
                StateHasChanged();
                PurchaseOrderFilter = "";
                POsForSupplier = IsAllDatesSelected ? (await PoService.GetPoHeadersForSupplierAsync((int)SelectedSupplier)).Value :
                                                    (await PoService.GetPoHeadersForSupplierAsync((int)SelectedSupplier, StartDateValue, EndDateValue)).Value;
                CurrentSelectedViewPOsData = ActiveOrCanceled == "Canceled" ? POsForSupplier.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                            POsForSupplier.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
                displayLoadingSpinner = false;
                StateHasChanged();
            }
        }
    }

    private void ToggleCanceledIssuedPOs()
    {
        if (SupplierOrJobSelected == "Job" && JobSelected != null)
        {
            CurrentSelectedViewPOsData = ActiveOrCanceled == "Canceled" ? POsForJob.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                        POsForJob.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
        }
        if (SupplierOrJobSelected == "Supplier" && SelectedSupplier != null)
        {
            CurrentSelectedViewPOsData = ActiveOrCanceled == "Canceled" ? POsForSupplier.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                        POsForSupplier.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
        }

        POInquiryGrid.Rebind();
    }

    protected async Task OnPurchaseOrderFilterChange(object theUserInput)
    {
        var purchaseOrderNumber = (string)theUserInput;
        if (SupplierOrJobSelected == "Job" && JobSelected != null)
        {
            CurrentSelectedViewPOsData = ActiveOrCanceled == "Canceled" ? POsForJob.Where(x => x.Postatus == 5 && purchaseOrderNumber.Equals(x.Ponumber, StringComparison.InvariantCultureIgnoreCase)).OrderBy(x => x.Ponumber).ToList() :
                                                    POsForJob.Where(x => x.Postatus != 5 && purchaseOrderNumber.Equals(x.Ponumber, StringComparison.InvariantCultureIgnoreCase)).OrderBy(x => x.Ponumber).ToList();
        }
        if (SupplierOrJobSelected == "Supplier" && SelectedSupplier != null)
        {
            POsForSupplier = (await PoService.GetPoHeadersForPONumberAsync(purchaseOrderNumber)).Value;
            if (SelectedSupplier != null && POsForSupplier != null && SelectedSupplier.Equals(POsForSupplier.First().SubNumberNavigation.SubNumber))
            {
                CurrentSelectedViewPOsData = ActiveOrCanceled == "Canceled" ? POsForSupplier.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                        POsForSupplier.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
            }
            else
            {
                CurrentSelectedViewPOsData = new List<PoheaderDto>();
            }  
        }
        POInquiryGrid.Rebind();
    }

    private async Task OnViewOptionChange()
    {
        //SupplierOrJobSelected is bound to the dropdown. SupplierOrJobSelectedChange is not. 
        //This check is used to prevent reloading the data if the selection hasn't actually changed 
        //which can happen if the user just clicks in and out of the box, or because the onchange fires onblur as well as on change
        if (SupplierOrJobSelected != SupplierOrJobSelectedChange)
        {
            SupplierOrJobSelectedChange = SupplierOrJobSelected;
            await LoadDataBasedOnSelection();
        }

    }

    private async Task ToggleAllDatesView()
    {
        await LoadDataBasedOnSelection();
    }

    private async Task OnDateChange(object dateSelected)
    {
        if (StartDateValue != StartDateValueChange || EndDateValue != EndDateValueChange)
        {
            StartDateValueChange = StartDateValue;
            EndDateValueChange = EndDateValue;
            await LoadDataBasedOnSelection();
        }
    }

    private async Task LoadDataBasedOnSelection()
    {
        PurchaseOrderFilter = "";
        displayLoadingSpinner = true;
        StateHasChanged();
        JobSelected = SubdivisionJobPickService.JobNumber;
        SelectedSupplier = SubdivisionJobPickService.SupplierNumber;
        if (SupplierOrJobSelected == "Job" && JobSelected != null)
        {
            POsForJob = IsAllDatesSelected ? (await PoService.GetPoHeadersForJobAsync(JobSelected)).Value :
                                            (await PoService.GetPoHeadersForJobAsync(JobSelected, StartDateValue, EndDateValue)).Value;
            CurrentSelectedViewPOsData = ActiveOrCanceled == "Canceled" ? POsForJob.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                    POsForJob.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
        }
        if (SupplierOrJobSelected == "Supplier" && SelectedSupplier != null)
        {
            POsForSupplier = IsAllDatesSelected ? (await PoService.GetPoHeadersForSupplierAsync((int)SelectedSupplier)).Value :
                                    (await PoService.GetPoHeadersForSupplierAsync((int)SelectedSupplier, StartDateValue, EndDateValue)).Value;
            CurrentSelectedViewPOsData = ActiveOrCanceled == "Canceled" ? POsForSupplier.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                    POsForSupplier.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
        }
        if ((SupplierOrJobSelected == "Job" && JobSelected == null) || (SupplierOrJobSelected == "Supplier" && SelectedSupplier == null))
        {
            CurrentSelectedViewPOsData = new List<PoheaderDto>();
        }
        displayLoadingSpinner = false;
        StateHasChanged();
    }

    // public async void OnItemClick(ContextMenuItem item)
    // {
    //     if (ClickedPORow != null)
    //     {
    //         var estDetailsResponse = await BudgetService.GetPoDetailsByPONumberAsync(ClickedPORow.PoheaderId);

    //         if (item.Text == "Download PO" && estDetailsResponse.IsSuccess)
    //         {
    //             GenerateDocumentAndDownload(ClickedPORow, estDetailsResponse.Value);
    //         }
    //         if (item.Text == "Email PO" && estDetailsResponse.IsSuccess)
    //         {
    //             GenerateDocumentAndEmail(ClickedPORow, estDetailsResponse.Value);
    //         }
    //         if (estDetailsResponse.IsSuccess == false)
    //         {
    //             ShowNotification(estDetailsResponse.Message, false);
    //         }
    //     }
    // }
    public async void Download(GridCommandEventArgs args)
    {
        var poHeader = args.Item as PoheaderDto;
        if (poHeader != null)
        {
            var estDetailsResponse = await BudgetService.GetPoDetailsByPONumberAsync(poHeader.PoheaderId);
            if (estDetailsResponse.IsSuccess)
            {
                GenerateDocumentAndDownload(poHeader, estDetailsResponse.Value);
            }                  
            else
            {
                ShowNotification(estDetailsResponse.Message, false);
            }
        }
    }
    public async void Email(GridCommandEventArgs args)
    {
        var poHeader = args.Item as PoheaderDto;
        if (poHeader != null)
        {
            var estDetailsResponse = await BudgetService.GetPoDetailsByPONumberAsync(poHeader.PoheaderId);
            if (estDetailsResponse.IsSuccess)
            {
                GenerateDocumentAndEmail(poHeader, estDetailsResponse.Value);
            }
            else
            {
                ShowNotification(estDetailsResponse.Message, false);
            }
        }
    }
    async void ShowNotification(string message, bool isSuccess)
    {
        // Alert
        if (!isSuccess)
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = message,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = message,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }
    }

    async Task GenerateDocumentAndDownload(PoheaderDto poHeader, List<PodetailDto> poDetails)
    {
        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(poHeader, poDetails);
        // await PoService.UploadPoAsync(itemToIssue.Poheader, fileData);
        DemoFileExporter.Save(JsRuntime, fileData, "application/pdf", $"PO-{poHeader.Ponumber}.pdf");
    }
    async Task GenerateDocumentAndEmail(PoheaderDto poHeader, List<PodetailDto> poDetails)
    {
        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(poHeader, poDetails);

        var files = new List<FileModel>();
        files.Add(new FileModel()
            {
                FileData = fileData,
                FileName = $"{poHeader.Ponumber}.pdf"
            });

        var emailModel = new PoEmailModel()
            {
                Files = files,
                Poheader = poHeader,
                Subject = $"Van Metre Purchase Order: {poHeader.Ponumber}",
                Body = "Please see the attached purchase order. Visit the Van Metre Trade Portal (https://trades.vanmetrehomes.com/) for more information."
            };
        var response = await PoService.EmailPos(emailModel);
        if (response.IsSuccess)
        {
            //update the status to sent if it was issued, keep status as cancelled or approved if it is one of those
            poHeader.PostatusNavigation.Postatus1 = poHeader.Postatus == 1 ? "Sent" : poHeader.PostatusNavigation.Postatus1;
            poHeader.PostatusNavigation.PostatusId = poHeader.Postatus == 1 || poHeader.Postatus == null ? 2 : poHeader.PostatusNavigation.PostatusId;
            poHeader.Postatus = poHeader.Postatus == 1 ? 2 : poHeader.Postatus;//change from issued to sent, but if approved or cancelled don't change status
        }
    }
    // private void OnContextMenu(GridRowClickEventArgs args)
    // {
    //     ClickedPORow = args.Item as PoheaderDto;

    //     if (args.EventArgs is MouseEventArgs mouseEventArgs)
    //     {
    //         _ = ContextMenuRef.ShowAsync(mouseEventArgs.ClientX, mouseEventArgs.ClientY);
    //     }
    // }
    // public class ContextMenuItem
    // {
    //     public string Text { get; set; }
    //     public object Icon { get; set; }
    // }

    private async Task OnExcelBeforeExport(GridBeforeExcelExportEventArgs args)
    {

        // Customize the Width of the first exported column
        // Customize the Width of the exported column
        args.Columns[0].Width = "80px";
        args.Columns[1].Width = "100px";
        args.Columns[2].Width = "80px";
        args.Columns[3].Width = "250px";
        args.Columns[4].Width = "250px";
        args.Columns[5].Width = "150px";
        args.Columns[6].Width = "60px";
        
        args.Columns[7].Width = "80px";
        args.Columns[7].NumberFormat = BuiltInNumberFormats.GetShortDate();

        args.Columns[8].Width = "80px";
        args.Columns[8].NumberFormat = BuiltInNumberFormats.GetShortDate();
        
        args.Columns[9].Width = "100px";
        
        args.Columns[10].Width = "80px";
        args.Columns[10].NumberFormat = BuiltInNumberFormats.GetShortDate();
        
        args.Columns[11].Width = "70px";
    }
    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= JobSelectedHandler;
        SubdivisionJobPickService.OnChanged -= SupplierSelectedHandler;
    }
}
