﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class SupplierandActivitiesListSelectModel
{
    public int SubNumber { get; set; }

    public List<int>? SelectedActivities { get; set; }
    public DateTime? CostAsOfDate { get; set; }
    public int? SubdivisionId { get; set; }
    public bool? DivisionDefaults { get; set; }
    public bool IncludeItemsWithoutCosts { get; set; }
    public bool IncludeDivisionDefaultCosts { get; set; }
    public bool OnlyItemsWithoutCosts { get; set; }
    public bool ApplyCostsAsDivisionDefault { get; set; }
}
