﻿@page "/supplierSchedule"
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector, ConstructionManager")]
@inject SubdivisionService SubdivisionService
@inject ScheduleService ScheduleService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject PoService PoService
@inject BudgetService BudgetService
@implements IDisposable
@using Telerik.Blazor.Components.Grid
@using Telerik.Documents.SpreadsheetStreaming
@using ERP.Data.Models.Dto
@using ERP.Data.Models
@using Telerik.DataSource.Extensions
@using System.Collections.ObjectModel

<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }
    /*.mytreeclass .k-drag-cell {
            display:none;
        }*/
/*    .mytreeclass .k-table-th.k-header.k-drag-cell{
        visibility: collapse;
    }
    .mytreeclass .k-drag-cell.k-touch-action-none{
        visibility: collapse;

    }*/
    .mytreeclass .k-drag-col{
        visibility: collapse;
    }
    .mytreeclass .k-header {
        min-width: 200px;
    }

    .mytreeclass  {
       overflow-x: auto;
    }
    .k-drag-clue {
        color: white;
    }

    .k-icon.k-icon-with-modifier {
        display: none;
    }

    .SubdivisionTable .k-hierarchy-cell {
        display: none;
    }
    
    .k-splitbar {
        width: 15px;
        color: black;
        padding-left:5px;
        padding-right:5px;
    }

    .pane {
        padding: 8px;
        overflow-x: auto;
    }

    .k-splitter .k-pane {
        overflow-x: auto !important;
    }

    .k-splitter-horizontal{
        overflow-x: auto !important;
    }
    .page-wrapper .page-content{     
       /* padding-right: 100px !important;
        margin-right: 100px !important;*/ 
/*        width: 95% !important;*/
    }
    .page-wrapper{
       /* padding-right: 100px !important;
        margin-right: 100px !important;
            width: 80% !important;*/
    }
    .k-table-td{
        padding: 4px !important;
        font-size: 11px;
    }

    .k-table-td { /*these settings are to prevent the 0 width columns from affecting the column height*/
        padding: 4px !important;
        font-size: 11px;
        height: 30px !important;
        white-space: nowrap !important; 
        overflow: visible;
    }

    .negativeValuesRowFormatting {
        background-color: #ffd6d6 !important;
    }

    .positiveValuesRowFormatting {
        background-color: #d7f5e3 !important;
    }

    .k-button-solid-base.k-selected {
        border-color: #f4cd64;
        color: black;
        background-color: #f4cd64;
    }

    .k-panelbar > .k-item > .k-link.k-selected, .k-panelbar > .k-panelbar-header > .k-link.k-selected {
        color: black;
        background-color: #f4cd64;
    }

    .k-panelbar > .k-item > .k-link, .k-panelbar > .k-panelbar-header > .k-link {
        color: black;
    }

        .k-panelbar > .k-panelbar-header > .k-link.k-selected:hover {
            background-color: #f4cd64;
        }




</style>
<TelerikMediaQuery Media="@SmallScreenMediaQuery" OnChange="@((doesMatch) => IsSmallScreen = doesMatch)"></TelerikMediaQuery>
<TelerikMediaQuery Media="@LargeScreenMediaQuery" OnChange="@((doesMatch) => IsLargeScreen = doesMatch)"></TelerikMediaQuery>
<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="true" ShowJob="true" ShowSubdivision="true" @ref="subdivisionJobPickerMenuBar"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
   <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Open Schedule Activities For Supplier</h7>
                </div>
            </div>
<TelerikTooltip TargetSelector=".tooltip-target" />

@if (IsLargeScreen)
{
    <div class="row">
        <TelerikGrid Data="@SupplierScheduleActivities"
                     FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                     Height="1000px" RowHeight="60" PageSize="20"
                     Width="100%"
                     ScrollMode="@GridScrollMode.Virtual"
                     Sortable="true"
                     Resizable="true"
                     Reorderable="true"
                     Groupable="false"
                     OnRowRender="@OnRowRenderHandler"
                     SelectionMode="GridSelectionMode.Single"
                     ConfirmDelete="true">
            <GridColumns>
                <GridColumn Field="ScheduleM.Schedule.JobNumber" Title="Job" Editable="false" Groupable="true" />
                <GridColumn Field="Sactivity.ActivityName" Title="Activity" Editable="false" Groupable="true">
                    <Template>
                        @{
                            var item = context as ScheduleSactivityDto;
                            <TelerikButton Title="View/Edit" OnClick="() => EditActivity(item)" Class="k-button-success mr-1">Edit</TelerikButton>
                            @($" - {item.Sactivity.ActivityName}")

                        }
                    </Template>
                </GridColumn> 
               @*  <GridColumn Field="ScheduleM.Schedule.JobNumberNavigation.LotNumber" Title="Lot" Editable="false" Groupable="true" />
                <GridColumn Field="ScheduleM.Schedule.JobNumberNavigation.JobAddress1" Title="Address" Editable="false" Groupable="true" /> *@
               @*  <GridCommandColumn Context="dataItem" Width="120px">
                    @{
                        var item = dataItem as ScheduleDto;
                        <a href=@($"/lotdetails/{item.JobNumber}") class="btn btn-outline-primary">Job Details</a>
                    }
                </GridCommandColumn> *@
                <GridColumn Field="SchStartDate" Title="Scheduled Start" Editable="false" Groupable="true" DisplayFormat="{0:MM/dd/yyyy}" Width="125px" />
                <GridColumn Field="SchEndDate" Title="Scheduled End" Editable="false" Groupable="true" DisplayFormat="{0:MM/dd/yyyy}" Width="125px" />
                <GridColumn Field="ActualStartDate" Title="Actual Start" Editable="false" Groupable="true" DisplayFormat="{0:MM/dd/yyyy}" Width="125px" />
                <GridColumn Field="ActualEndDate" Title="Actul End" Editable="false" Groupable="true" DisplayFormat="{0:MM/dd/yyyy}" Width="125px" />
                <GridCommandColumn>
                    <GridCommandButton Class="tooltip-target k-button-success" Title="Job Details" OnClick="SelectJobDetails"  Command="Details">Job Details</GridCommandButton>
                    <GridCommandButton Class="tooltip-target k-button-success" Title="Schedule" OnClick = "SelectSchedule" Command="Schedule">Schedule</GridCommandButton>
                </GridCommandColumn>
            </GridColumns>
            <GridToolBarTemplate>
                <GridSearchBox DebounceDelay="200"></GridSearchBox>
                <GridCommandButton Command="ExcelExport" Icon="@FontIcon.FileExcel" Class="k-button-add">Export to Excel</GridCommandButton>
            </GridToolBarTemplate>
            <GridExport>
                <GridExcelExport FileName="SupplierSchedule" OnBeforeExport="@OnExcelBeforeExport" AllPages="true" />
            </GridExport>
        </TelerikGrid>
        
        <br />
        <br />
    </div>
}
else
{
    if (SupplierScheduleActivities == null)
    {
        <p>Loading</p>
    }
    else
    {
        <TelerikPanelBar Data="SupplierScheduleActivities">
            <PanelBarBindings>
                <PanelBarBinding>
                    <HeaderTemplate>
                        @{
                            var item = context as ScheduleSactivityDto;
                            <div class="row justify-content-center" style="width: 100%; text-align:center">
                                <div class="col-12 justify-content-center" style="width:100%; font-size: 14px">@item.ScheduleM.Schedule.JobNumber</div>
                                <div class="col-12 justify-content-center" style="width:100%; font-size: 14px">@item.Sactivity.ActivityName</div>
                                <div class="col-12 justify-content-center" style="width:100%; font-size: 12px">Sch. Start: @item.SchStartDate?.ToString("MM/dd/yyyy") - Sch. End: @item.SchEndDate?.ToString("MM/dd/yyyy") </div>
                            </div>

                        }
                    </HeaderTemplate>
                    <ContentTemplate>
                        @{
                            var details = context as ScheduleSactivityDto;
                            <div class="k-card-body">
                                <div class="row">
                                    <div class="col-4" style="text-align: left; padding-right: 10px">
                                        <label class="form-label" style="font-size: 15px">
                                            <TelerikButton Enabled="AllowEdit" Title="View/Edit" OnClick="() => EditActivity(details)" Class="k-button-success">Edit</TelerikButton>
                                        </label>
                                    </div>
                                </div>
                                @*<div class="row">
                                    <br />
                                    <hr class="mt-2" />
                                    <TelerikButton OnClick="@Save" Title="Save" Icon="@FontIcon.Save" Class=" tooltip-target k-button-success"></TelerikButton>
                                </div>*@
                            </div>
                        }
                    </ContentTemplate>
                </PanelBarBinding>
            </PanelBarBindings>
        </TelerikPanelBar>
    }
}
<EditScheduleActivity @ref="EditScheduleActivity" SelectedActivity="@SelectedActivity" HandleAddSubmit="@HandleValidEditActivitySubmit"></EditScheduleActivity>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
@code {

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    private bool AllowEdit { get; set; } = true;
    public SubdivisionJobPickerMenuBar? subdivisionJobPickerMenuBar { get; set; }
    public int? SupplierSelected { get; set; }
    private bool IsSmallScreen { get; set; }
    private bool IsLargeScreen { get; set; }
    private string SmallScreenMediaQuery { get; set; } = "(max-width: 767px)";
    private string LargeScreenMediaQuery { get; set; } = "(min-width: 1199px)";

    protected EditScheduleActivity? EditScheduleActivity { get; set; }
    public ScheduleSactivityDto? SelectedActivity { get; set; }
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    public List<ScheduleSactivityDto>? SupplierScheduleActivities { get; set; }

    protected override async Task OnInitializedAsync()
    {
        SubdivisionJobPickService.OnChanged += SupplierChangedHandler;
        if(SubdivisionJobPickService.SupplierNumber != null)
        {
            var schedulesTask = ScheduleService.GetScheduleActivitysForSupplierAsync((int)SubdivisionJobPickService.SupplierNumber);
            await Task.WhenAll(new Task[] { schedulesTask });
            SupplierScheduleActivities = schedulesTask.Result.Value;
        }
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }
    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= SupplierChangedHandler;
    }
    async Task SelectSchedule(GridCommandEventArgs args)
    {
        var selectedSchedule = args.Item as ScheduleSactivityDto;
        if(selectedSchedule != null)
        {
            SubdivisionJobPickService.JobNumber = selectedSchedule.ScheduleM.Schedule.JobNumber;
            subdivisionJobPickerMenuBar.StoreSubdivJobSupplier();
            NavManager.NavigateTo($"schedule");
            StateHasChanged();
        }
        var selected = SubdivisionJobPickService.JobNumber;
    }
    async Task SelectJobDetails(GridCommandEventArgs args)
    {
        var selectedSchedule = args.Item as ScheduleSactivityDto;
        if (selectedSchedule != null)
        {
            SubdivisionJobPickService.JobNumber = selectedSchedule.ScheduleM.Schedule.JobNumber;
            subdivisionJobPickerMenuBar.StoreSubdivJobSupplier();
            NavManager.NavigateTo($"lotdetails/{selectedSchedule.ScheduleM.Schedule.JobNumber}");
            StateHasChanged();
        }
        var selected = SubdivisionJobPickService.JobNumber;
    }
    async Task SupplierChangedHandler()
    {
        var selected = SubdivisionJobPickService.SupplierNumber;
        if (SupplierSelected != selected)//check if actual change, so not calling this multiple times
        {
            SupplierSelected = selected;
            var schedulesTask = ScheduleService.GetScheduleActivitysForSupplierAsync((int)SubdivisionJobPickService.SupplierNumber);
            await Task.WhenAll(new Task[] { schedulesTask });
            SupplierScheduleActivities = schedulesTask.Result.Value;
            StateHasChanged();
        }
    }

    void OnRowRenderHandler(GridRowRenderEventArgs args)
    {
        ScheduleSactivityDto item = args.Item as ScheduleSactivityDto;

        //args.Class = item.DateToStart > item.BaseStartDate || item.DateToEnd > item.BaseEndDate ? "positiveValuesRowFormatting" : "negativeValuesRowFormatting";
    }
    private void EditActivity(ScheduleSactivityDto activity)
    {
        if (activity != null)
        {
            SelectedActivity = activity;
        }
        EditScheduleActivity?.Show();
        StateHasChanged();
    }
    private async void HandleValidEditActivitySubmit(ResponseModel<ScheduleSactivityDto> responseActivity)
    {
        EditScheduleActivity.Hide();
        //TODO: refresh
        if (responseActivity.IsSuccess)
        {
            if (SubdivisionJobPickService.SupplierNumber != null)
            {
                var schedulesTask = ScheduleService.GetScheduleActivitysForSupplierAsync((int)SubdivisionJobPickService.SupplierNumber);
                await Task.WhenAll(new Task[] { schedulesTask });
                SupplierScheduleActivities = schedulesTask.Result.Value;
            }
        }
        if (SubdivisionJobPickService.SupplierNumber != null)
        {
            var schedulesTask = ScheduleService.GetScheduleActivitysForSupplierAsync((int)SubdivisionJobPickService.SupplierNumber);
            await Task.WhenAll(new Task[] { schedulesTask });
            SupplierScheduleActivities = schedulesTask.Result.Value;
        }
        ShowSuccessOrErrorNotification(responseActivity.Message, responseActivity.IsSuccess);
        StateHasChanged();
    }

    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                Closable = true,
                CloseAfter = 0,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }

    private async Task OnExcelBeforeExport(GridBeforeExcelExportEventArgs args)
    {

        // Customize the Width of the first exported column
        // // Customize the Width of the exported column
        args.Columns[0].Width = "100px";
        args.Columns[1].Width = "250px";
        args.Columns[2].Width = "100px";
        args.Columns[3].Width = "100px";
        args.Columns[4].Width = "100px";
        args.Columns[5].Width = "100px";



        // Change the format of the date column
        args.Columns[2].NumberFormat = BuiltInNumberFormats.GetShortDate();
        args.Columns[3].NumberFormat = BuiltInNumberFormats.GetShortDate();
        args.Columns[4].NumberFormat = BuiltInNumberFormats.GetShortDate();
        args.Columns[5].NumberFormat = BuiltInNumberFormats.GetShortDate();


    }
}
