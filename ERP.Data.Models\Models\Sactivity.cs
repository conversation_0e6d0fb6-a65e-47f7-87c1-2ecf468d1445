﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Sactivity
{
    public int SactivityId { get; set; }

    public string? ActivityName { get; set; }

    public int? TradeId { get; set; }

    public int? Seq { get; set; }

    public int? DefaultDuration { get; set; }

    public int? DefaultLagtime { get; set; }

    public string? DefaultNote { get; set; }

    public string? UpdateTemplates { get; set; }

    public string? UpdateSchedules { get; set; }

    public string? DownloadToPalm { get; set; }

    public int? ChecklistId { get; set; }

    public int? ReportingId { get; set; }

    public string? Workdays { get; set; }

    public int? DivId { get; set; }

    public string? GrossLag { get; set; }

    public string? GenPitBudget { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<Estactivity> Estactivities { get; set; } = new List<Estactivity>();

    public virtual ICollection<Pactivity> Pactivities { get; set; } = new List<Pactivity>();

    public virtual ICollection<Poheader> Poheaders { get; set; } = new List<Poheader>();

    public virtual ICollection<ScheduleChain> ScheduleChains { get; set; } = new List<ScheduleChain>();

    public virtual ICollection<ScheduleSactivity> ScheduleSactivities { get; set; } = new List<ScheduleSactivity>();

    public virtual ICollection<ScheduleSactivityPred> ScheduleSactivityPreds { get; set; } = new List<ScheduleSactivityPred>();

    public virtual ICollection<TemplateSactivity> TemplateSactivities { get; set; } = new List<TemplateSactivity>();

    public virtual ICollection<TemplateSactivityPred> TemplateSactivityPreds { get; set; } = new List<TemplateSactivityPred>();

    public virtual Trade? Trade { get; set; }
}
