﻿@page "/customestimate"
@inject BudgetService BudgetService
@inject TradeService TradeService
@inject SubdivisionService SubdivisionService
@inject AuthenticationStateProvider AuthenticationStateProvider
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling")]
@using ERP.Data.Models
@using System.Collections.ObjectModel
@using Telerik.DataSource.Extensions

<PageTitle>Purchasing | Custom Estimates</PageTitle>

<div class="container-fluid flex">
     <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Custom Estimates</h7>
                </div>
            </div>
        </div>

    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item active">Custom Estimates</li>
    </ol>

<TelerikTooltip TargetSelector=".tooltip-target" />
@if (AllCustomEstimates == null)
{
    <p><em>Loading...</em></p>
        <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />
}

else
{

    <TelerikGrid Data=@AllCustomEstimates
                 ConfirmDelete="true"
                 ScrollMode="@GridScrollMode.Virtual"
                 EditMode="@GridEditMode.Incell"
                 Width="100%"
                 Height="1000px" RowHeight="60" PageSize="20"
                 Sortable="true"
                 Resizable="true"              
                 OnUpdate="@UpdateItem"
                 OnDelete="@DeleteItem">
        <GridColumns>
            <GridCommandColumn Context="dataItem" Width="120px">
                @{
                    var custOpt = dataItem as EstcustoptionDto;
                    <a href=@($"/customestimatedetail/{custOpt.EstcustoptionId}") class="btn btn-outline-primary">View Details</a>
                }
            </GridCommandColumn>
            <GridColumn Field="IsEstimateCompleted" Width="50px" Title="Finalized" Editable="true">
                <Template>
                    @{
                        SelectedEstCustOption = context as EstcustoptionDto;
                        <TelerikCheckBox @bind-Value="SelectedEstCustOption.IsEstimateCompleted"></TelerikCheckBox>
                    }
                </Template>
                <EditorTemplate>
                    @{
                        SelectedEstCustOption = context as EstcustoptionDto;
                        <TelerikCheckBox @bind-Value="SelectedEstCustOption.IsEstimateCompleted"></TelerikCheckBox>
                    }
                </EditorTemplate>
            </GridColumn>
            <GridColumn Field="Optioncode" Title="Option Code" Width="100px"  Editable="true" />
            <GridColumn Field="Optiondesc" Title="Option Description" Width="150px" Editable="true" />
            <GridColumn Field="JobNumber" Title="Job Number" Width="100px" Editable="false" />
            <GridColumn Field="Subdivision.SubdivisionName" Title="Subdivision" Width="200px" Editable="false">               
            </GridColumn>
            <GridColumn Field="IsBuilderapproved" Title="Builder Approved" Width="40px"  Editable="false">
                <Template>
                    @{
                        SelectedEstCustOption = context as EstcustoptionDto;
                        <TelerikCheckBox @bind-Value="SelectedEstCustOption.IsBuilderapproved"></TelerikCheckBox>
                    }
                </Template>
            </GridColumn>
            <GridColumn Field="IsCustomerapproved" Title="Customer Approved" Width="40px" Editable="false">
                <Template>
                    @{
                        SelectedEstCustOption = context as EstcustoptionDto;
                        <TelerikCheckBox @bind-Value="SelectedEstCustOption.IsCustomerapproved"></TelerikCheckBox>
                    }
                </Template>
            </GridColumn>
            <GridColumn Field="Estheader.Estimator" Title="Estimator" Width="100px"  Editable="false" />
            <GridColumn Field="Price" Title="Sales Price"  Width="100px" Editable="false" />
            <GridColumn Field="EstimateCost" Title="Cost" Width="100px" Editable="false" />
            <GridCommandColumn  Width="120px">
                <GridCommandButton Title="Delete" Class="tooltip-target k-button-danger" Command="Delete" Icon="@FontIcon.Trash"></GridCommandButton>
            </GridCommandColumn>
        </GridColumns>

        <GridToolBarTemplate>
            <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>
            <GridCommandButton Command="AddCustEstimateFromToolbar" OnClick="@AddCustomEstimateFromToolbar" Icon="@FontIcon.Plus" Class="k-button-add">Add Custom Estimate</GridCommandButton>
        </GridToolBarTemplate>
    </TelerikGrid>
}

<ERP.Web.Components.AddCustomEstimate @ref="AddCustomEsimateModal" HandleAddSubmit="HandleValidAddCustomEstimateSubmit"></ERP.Web.Components.AddCustomEstimate>

@code {
    public ObservableCollection<EstcustoptionDto>? AllCustomEstimates { get; set; }
    public EstcustoptionDto? SelectedEstCustOption { get; set; }
    protected ERP.Web.Components.AddCustomEstimate? AddCustomEsimateModal { get; set; }
   // public List<SubdivisionDto>? AllSubdivisions {get; set; }
    protected override async Task OnInitializedAsync()
    {
        var getEstimates = await BudgetService.GetCustomEstimatesAsync();
       // var subdivisionsTask = SubdivisionService.GetSubdivisionsAsync();
        //await Task.WhenAll(getEstimatesTask, subdivisionsTask);
            AllCustomEstimates = new ObservableCollection<EstcustoptionDto>(getEstimates.Value);
        //var data = subdivisionsTask.Result;
        //AllSubdivisions = data.Value;
    }

    private void AddCustomEstimateFromToolbar(GridCommandEventArgs args)
    {
        AddCustomEsimateModal.Show();
    }

    private async void HandleValidAddCustomEstimateSubmit(AddCustomEstimateModel responseEstimate)
    {
        var getEstimates = await BudgetService.GetCustomEstimatesAsync();
            AllCustomEstimates = new ObservableCollection<EstcustoptionDto>(getEstimates.Value);
        AddCustomEsimateModal.Hide();
        StateHasChanged();
    }
    protected async Task DeleteItem(GridCommandEventArgs args)
    {
        var itemToDelete = args.Item as EstcustoptionDto;
        await BudgetService.DeleteCustomOptionEstimateAsync(itemToDelete);
        AllCustomEstimates.Remove(itemToDelete);

    }
    protected async Task UpdateItem(GridCommandEventArgs args)
    {
        var itemToUpdate = args.Item as EstcustoptionDto;
        await BudgetService.UpdateCustomOptionEstimateAsync(itemToUpdate);
        var findItem = AllCustomEstimates.SingleOrDefault(x => x.EstcustoptionId == itemToUpdate.EstcustoptionId);
        findItem.Optioncode = itemToUpdate.Optioncode;
        findItem.Optiondesc = itemToUpdate.Optiondesc;
        findItem.IsEstimateCompleted = itemToUpdate.IsEstimateCompleted;
    }
    protected async Task EditItem(GridCommandEventArgs args)
    {
        var itemToUpdate = args.Item as EstcustoptionDto;
       
    }
}
</div>