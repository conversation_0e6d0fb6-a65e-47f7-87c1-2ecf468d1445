﻿using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Pages;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using NLog;
using System.ComponentModel;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class SupplierMessagesService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public SupplierMessagesService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<ResponseModel<List<SupplierCommunicationDto>>> GetSupplilerMessagesAsync(int subNum)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/suppliermessages/getsuppliermessages/{subNum}");
                var responseString = await response.Content.ReadAsStringAsync();
                var supplierMessages = JsonConvert.DeserializeObject<ResponseModel<List<SupplierCommunicationDto>>>(responseString);
                return supplierMessages;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }
            return new ResponseModel<List<SupplierCommunicationDto>> { IsSuccess = false, Message = "Error while fetching supplier messages" };
        }

        public async Task<ResponseModel<EmailModel>> SendEmail(EmailModel model)
        {
            try
            {

                var response = await _downstreamAPI.PostForUserAsync<EmailModel, ResponseModel<EmailModel>>(
                           "DownstreamApi", model,
                            options =>
                            {
                                options.RelativePath = "api/suppliermessages/sendemail/";
                            });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<EmailModel>() { IsSuccess = false, Value = new EmailModel(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<SupplierDto>>> GetSuppliersAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/suppliermessages/getsuppliers");
                var responseString = await response.Content.ReadAsStringAsync();
                var suppliers = JsonConvert.DeserializeObject<ResponseModel<List<SupplierDto>>>(responseString);
                return suppliers;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<SupplierDto>>() { IsSuccess = false, Value = new List<SupplierDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<SupplierContactDto>>> GetSupplilerContactsAsync(int subNum)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/suppliermessages/getsuppliercontacts/{subNum}");
                var responseString = await response.Content.ReadAsStringAsync();
                var supplierContacts = JsonConvert.DeserializeObject<ResponseModel<List<SupplierContactDto>>>(responseString);
                return supplierContacts;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }
            return new ResponseModel<List<SupplierContactDto>> { IsSuccess = false, Message = "Error while fetching supplier contacts" };
        }
    }
}
