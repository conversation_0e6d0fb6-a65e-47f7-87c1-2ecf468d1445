﻿using System.Text;
using ClosedXML.Excel;
using ERP.Data.Models.Dto;

namespace ERP.API.Data
{
    public class ExportService
    {
        public string GetCSV(IEnumerable<CostModel> list)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.AppendLine("Id,Cost,EffectiveDate");
            foreach (var cost in list)
            {
                stringBuilder.AppendLine($"{cost.CostsId},{cost.UnitCost},{cost.CostEffectiveDate}");//TODO: fill all
            }

            return stringBuilder.ToString();
        }

        private byte[] ConvertToByte(XLWorkbook workbook)
        {
            var stream = new MemoryStream();
            workbook.SaveAs(stream);

            var content = stream.ToArray();
            return content;
        }

        public byte[] CreateCostsExport(List<CostModel> costs, string author)
        {
            var workbook = new XLWorkbook();
            workbook.Properties.Title = "Export costs from ERP";
            workbook.Properties.Author = author;
            workbook.Properties.Subject = "Export costs";
            workbook.Properties.Keywords = "costs";

            CreateCostWorksheet(workbook, costs);

            return ConvertToByte(workbook);
        }

        public byte[] CreateSchedulesExport(List<ScheduleTemplateTreeModel> schedules, string author)
        {
            var workbook = new XLWorkbook();
            workbook.Properties.Title = "Export schedules from ERP";
            workbook.Properties.Author = author;
            workbook.Properties.Subject = "Export schedules";
            workbook.Properties.Keywords = "schedules";

            CreateSchedulesWorksheet(workbook, schedules);

            return ConvertToByte(workbook);
        }

        public void CreateCostWorksheet(XLWorkbook package, List<CostModel> costs)
        {
            var worksheet = package.Worksheets.Add("Costs");
            worksheet.Columns().Width = 25;
            worksheet.Column(1).Hide();
            worksheet.Column(2).Hide();
            worksheet.Column(3).Hide();
            worksheet.Column(4).Hide();
            worksheet.Column(5).Hide();
            worksheet.Column(15).Hide();

            worksheet.Cell(1, 1).Value = "Item Id";
            worksheet.Cell(1, 2).Value = "Supplier Id";
            worksheet.Cell(1, 3).Value = "Subdivision Id";
            worksheet.Cell(1, 4).Value = "Phase Id";
            worksheet.Cell(1, 5).Value = "CostsId";
            worksheet.Cell(1, 6).Value = "Phase Code";
            worksheet.Cell(1, 7).Value = "Option Description";
            worksheet.Cell(1, 8).Value = "Activity";
            worksheet.Cell(1, 9).Value = "Item Desc";
            worksheet.Cell(1, 10).Value = "Item Num";
            worksheet.Cell(1, 11).Value = "Unit Cost";
            worksheet.Cell(1, 12).Value = "Supplier";
            worksheet.Cell(1, 13).Value = "Subdivision Name";
            worksheet.Cell(1, 14).Value = "Unit";
            worksheet.Cell(1, 15).Value = "Cost Effective Date";

            //to make columns other than unit cost Grey to look like disabled 
            worksheet.Column(6).Style.Font.FontColor = XLColor.AshGrey;
            worksheet.Column(7).Style.Font.FontColor = XLColor.AshGrey;
            worksheet.Column(8).Style.Font.FontColor = XLColor.AshGrey;
            worksheet.Column(9).Style.Font.FontColor = XLColor.AshGrey;
            worksheet.Column(10).Style.Font.FontColor = XLColor.AshGrey;
            worksheet.Column(12).Style.Font.FontColor = XLColor.AshGrey;
            worksheet.Column(13).Style.Font.FontColor = XLColor.AshGrey;
            worksheet.Column(14).Style.Font.FontColor = XLColor.AshGrey;
            worksheet.Row(1).Style.Font.FontColor = XLColor.Black;

            for (int index = 1; index <= costs.Count; index++)
            {
                worksheet.Cell(index + 1, 1).Value = costs[index - 1].MasterItemId;
                worksheet.Cell(index + 1, 2).Value = costs[index - 1].SubNumber;
                worksheet.Cell(index + 1, 3).Value = costs[index - 1].SubdivisionId;
                worksheet.Cell(index + 1, 4).Value = costs[index - 1].MasterItemPhaseId;
                worksheet.Cell(index + 1, 5).Value = costs[index - 1].CostsId;
                worksheet.Cell(index + 1, 6).Value = costs[index - 1].PhaseCode;
                worksheet.Cell(index + 1, 7).Value = costs[index - 1].OptionDesc;
                worksheet.Cell(index + 1, 8).Value = costs[index - 1].PurchasingActivity;
                worksheet.Cell(index + 1, 9).Value = costs[index - 1].ItemDesc;
                worksheet.Cell(index + 1, 10).Value = costs[index - 1].ItemNumber;
                worksheet.Cell(index + 1, 11).Value = costs[index - 1].UnitCost;
                worksheet.Cell(index + 1, 12).Value = costs[index - 1].SupplierName;
                worksheet.Cell(index + 1, 13).Value = costs[index - 1].SubdivisionName;
                worksheet.Cell(index + 1, 14).Value = costs[index - 1].Unit;
                worksheet.Cell(index + 1, 15).Value = ((DateTime)costs[index - 1].CostEffectiveDate).ToString("d");//TODO: fix null
            }
        }

        public byte[] CreateSchedulesTemplatesExport(List<ScheduleTemplateTreeModel> scheduleTemplates, string author)
        {
            var workbook = new XLWorkbook();
            workbook.Properties.Title = "Export schedule template from ERP";
            workbook.Properties.Author = author;
            workbook.Properties.Subject = "Export schedule template";
            workbook.Properties.Keywords = "schedule template";

            CreateScheduleTempalteWorksheet(workbook, scheduleTemplates);

            return ConvertToByte(workbook);
        }

        public void CreateScheduleTempalteWorksheet(XLWorkbook package, List<ScheduleTemplateTreeModel> scheduleTemplates)
        {
            var worksheet = package.Worksheets.Add("ScheduleTemplates");
            worksheet.Columns().Width = 7;

            worksheet.Cell(1, 1).Value = "Activity Name";
            worksheet.Cell(1, 2).Value = "Milestone Name";
            worksheet.Cell(1, 3).Value = "Seq";
            worksheet.Cell(1, 4).Value = "Predecessor";
            worksheet.Cell(1, 5).Value = "Duration";
            worksheet.Cell(1, 6).Value = "Lag Time";
            worksheet.Cell(1, 7).Value = "Purchasing Activity";

            var exportList = new List<ScheduleTemplateTreeModel>();

            foreach (var scheduleTemplate in scheduleTemplates)
            {
                if (scheduleTemplate.HasChildren)
                {
                    var templates = scheduleTemplate.Children;
                    templates?.ForEach(x => x.MilestoneName = scheduleTemplate.MilestoneName);
                    if (templates != null && templates.Count > 0)
                    {
                        exportList.AddRange(templates);
                    }
                }
            }

            for (int index = 1; index <= exportList.Count; index++)
            {
                worksheet.Cell(index + 1, 1).Value = exportList[index - 1].ActivityName;
                worksheet.Cell(index + 1, 2).Value = exportList[index - 1].MilestoneName;
                worksheet.Cell(index + 1, 3).Value = exportList[index - 1].TemplateSactivity?.Seq;
                worksheet.Cell(index + 1, 4).Value = exportList[index - 1].Predecessors;
                worksheet.Cell(index + 1, 5).Value = exportList[index - 1].TemplateSactivity?.Duration;
                worksheet.Cell(index + 1, 6).Value = exportList[index - 1].TemplateSactivity?.LagTime;
                worksheet.Cell(index + 1, 7).Value = exportList[index - 1].PurchasingActivities;
            }
        }

        public void CreateSchedulesWorksheet(XLWorkbook package, List<ScheduleTemplateTreeModel> schedules)
        {
            var worksheet = package.Worksheets.Add("Schedules");
            worksheet.Columns().Width = 25;

            
            worksheet.Cell(1, 1).Value = "Activity Name";
            worksheet.Cell(1, 2).Value = "Milestone Name";
            worksheet.Cell(1, 3).Value = "Note";
            worksheet.Cell(1, 4).Value = "Started";
            worksheet.Cell(1, 5).Value = "Completed";
            worksheet.Cell(1, 6).Value = "Ignore No Work Days";
            worksheet.Cell(1, 7).Value = "Variance Code";
            worksheet.Cell(1, 8).Value = "Predecessors";
            worksheet.Cell(1, 9).Value = "Duration";
            worksheet.Cell(1, 10).Value = "Lag Time";
            worksheet.Cell(1, 11).Value = "PlusminusDays";

            worksheet.Cell(1, 12).Value = "Supplier";
            worksheet.Cell(1, 13).Value = "Schedule Start Date";
            worksheet.Cell(1, 14).Value = "Schedule End Date";
            worksheet.Cell(1, 15).Value = "Actual Start Date";
            worksheet.Cell(1, 16).Value = "Actual End Date";

            worksheet.Row(1).Style.Font.FontColor = XLColor.Black;

            var exportList = new List<ScheduleTemplateTreeModel>();

            foreach (var schedule in schedules)
            {
                if (schedule.HasChildren)
                {
                    var activities = schedule.Children;
                    if (activities != null && activities.Count > 0)
                    {
                        exportList.AddRange(activities);
                    }
                }
            }

            for (int index = 1; index <= exportList.Count; index++)
            {
                worksheet.Cell(index + 1, 1).Value = exportList[index - 1].ScheduleSactivity?.ActivityName;
                worksheet.Cell(index + 1, 2).Value = exportList[index - 1].ScheduleSactivity?.ScheduleM?.Milestone?.MilestoneName;
                worksheet.Cell(index + 1, 3).Value = exportList[index - 1].ScheduleSactivity?.Note;
                worksheet.Cell(index + 1, 4).Value = exportList[index - 1].ScheduleSactivity?.BoolStarted.ToString();
                worksheet.Cell(index + 1, 5).Value = exportList[index - 1].ScheduleSactivity?.BoolComplete.ToString();
                worksheet.Cell(index + 1, 6).Value = exportList[index - 1].ScheduleSactivity?.BoolIgnoreNoWorkDays.ToString();
                worksheet.Cell(index + 1, 7).Value = exportList[index - 1].ScheduleSactivity?.VarianceCode;
                worksheet.Cell(index + 1, 8).Value = exportList[index - 1].Predecessors;
                worksheet.Cell(index + 1, 9).Value = exportList[index - 1].ScheduleSactivity?.Duration;
                worksheet.Cell(index + 1, 10).Value = exportList[index - 1].ScheduleSactivity?.LagTime;
                worksheet.Cell(index + 1, 11).Value = exportList[index - 1].ScheduleSactivity?.PlusminusDays;
                worksheet.Cell(index + 1, 12).Value = exportList[index - 1].ScheduleSactivity?.SubNumberNavigation?.SubName;
                worksheet.Cell(index + 1, 13).Value = exportList[index - 1].ScheduleSactivity?.BaseStartDate;
                worksheet.Cell(index + 1, 14).Value = exportList[index - 1].ScheduleSactivity?.BaseEndDate;
                worksheet.Cell(index + 1, 15).Value = exportList[index - 1].ScheduleSactivity?.ActualStartDate;
                worksheet.Cell(index + 1, 16).Value = exportList[index - 1].ScheduleSactivity?.ActualEndDate;
            }
        }
    }
}
