﻿using AutoMapper;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NLog;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class DocusignConnectController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        public DocusignConnectController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
        }

        [HttpPost]
        public async Task<IActionResult> PostEventAsync([FromBody] Rootobject docusignEvent)
        {
            try
            {
                //Docusign Connect calls this when the envelope is sent, viewed, completed
                var test = docusignEvent;
                //TODO: add auth
                //TODO: save the information that the envelope is complete, maybe upload the completed file
                return Ok(new ResponseModel() { IsSuccess = true, Message = "Success!" });
            }
            catch (Exception ex)
            {
                return Ok(new ResponseModel() { IsSuccess = false, Message = ex.Message });
            }

        }




        public class Rootobject
        {
            [JsonProperty(PropertyName = "event")]
            public string? @event { get; set; }
            public string? apiVersion { get; set; }
            public string? uri { get; set; }
            public int? retryCount { get; set; }
            public int? configurationId { get; set; }
            public DateTime generatedDateTime { get; set; }
            public Data? data { get; set; }
        }

        public class Data
        {
            public string? accountId { get; set; }
            public string? userId { get; set; }
            public string? envelopeId { get; set; }
            public Envelopesummary? envelopeSummary { get; set; }
        }

        public class Envelopesummary
        {
            public string? status { get; set; }
            public string? documentsUri { get; set; }
            public string? recipientsUri { get; set; }
            public string? attachmentsUri { get; set; }
            public string? envelopeUri { get; set; }
            public string? emailSubject { get; set; }
            public string? envelopeId { get; set; }
            public string? signingLocation { get; set; }
            public string? customFieldsUri { get; set; }
            public string? notificationUri { get; set; }
            public string? enableWetSign { get; set; }
            public string? allowMarkup { get; set; }
            public string? allowReassign { get; set; }
            public DateTime? createdDateTime { get; set; }
            public DateTime? lastModifiedDateTime { get; set; }
            public DateTime? deliveredDateTime { get; set; }
            public DateTime? initialSentDateTime { get; set; }
            public DateTime? sentDateTime { get; set; }
            public DateTime? completedDateTime { get; set; }
            public DateTime? statusChangedDateTime { get; set; }
            public string? documentsCombinedUri { get; set; }
            public string? certificateUri { get; set; }
            public string? templatesUri { get; set; }
            public string? expireEnabled { get; set; }
            public DateTime? expireDateTime { get; set; }
            public string? expireAfter { get; set; }
            public Sender? sender { get; set; }
            public Customfields? customFields { get; set; }
            public string? purgeState { get; set; }
            public string? envelopeIdStamping { get; set; }
            public string? is21CFRPart11 { get; set; }
            public string? signerCanSignOnMobile { get; set; }
            public string? autoNavigation { get; set; }
            public string? isSignatureProviderEnvelope { get; set; }
            public string? hasFormDataChanged { get; set; }
            public string? allowComments { get; set; }
            public string? hasComments { get; set; }
            public string? allowViewHistory { get; set; }
            public Envelopemetadata? envelopeMetadata { get; set; }
            public object? anySigner { get; set; }
            public string? envelopeLocation { get; set; }
            public string? isDynamicEnvelope { get; set; }
            public string? burnDefaultTabData { get; set; }
        }

        public class Sender
        {
            public string? userName { get; set; }
            public string? userId { get; set; }
            public string? accountId { get; set; }
            public string? email { get; set; }
            public string? ipAddress { get; set; }
        }

        public class Customfields
        {
            public Textcustomfield[]? textCustomFields { get; set; }
            public object[]? listCustomFields { get; set; }
        }

        public class Textcustomfield
        {
            public string? fieldId { get; set; }
            public string? name { get; set; }
            public string? show { get; set; }
            public string? required { get; set; }
            public string? value { get; set; }
        }

        public class Envelopemetadata
        {
            public string? allowAdvancedCorrect { get; set; }
            public string? @event { get; set; }
            public string? allowCorrect { get; set; }
        }


    }
}
