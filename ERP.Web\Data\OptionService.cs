﻿using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using NLog;
using Microsoft.Identity.Abstractions;

namespace ERP.Web.Data
{
    public class OptionService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        public OptionService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationState)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationState;
        }       
        public async Task<ResponseModel<List<HomeAreaDto>>> GetHomeAreasAsync()
        {
            var areas = new ResponseModel<List<HomeAreaDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/option/gethomeareas");
                var responseString = await response.Content.ReadAsStringAsync();
                areas = JsonConvert.DeserializeObject<ResponseModel<List<HomeAreaDto>>>(responseString);
            }

            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
               // _logger.ForDebugEvent().Property("username", user?.User?.Identity?.Name?.Split('@')[0]).Exception(ex).Log();
                //_logger.Error(logdata);
                //var type = ex.GetType().ToString();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return areas;
        }
        public async Task<ResponseModel<List<OptionGroupDto>>> GetOptionGroupsAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/option/getoptiongroups");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var groups = JsonConvert.DeserializeObject<ResponseModel<List<OptionGroupDto>>>(responseString);
                    return groups;
                }                
            }
           
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return  new ResponseModel<List<OptionGroupDto>>() { IsSuccess = false, Value = null, Message = "Sorry, there was an error." }; 
        }
        
        public async Task<ResponseModel<List<OptionGroupDto>>> GetDistinctOptionGroupsAsync()
        {
            var groups = new ResponseModel<List<OptionGroupDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                               x => x.RelativePath = $"api/option/getoptiongroups");
                var responseString = await response.Content.ReadAsStringAsync();
                groups = JsonConvert.DeserializeObject<ResponseModel<List<OptionGroupDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif         
            }

            return groups;
        }

            //MasterPlanId = 1 is for default plan, not ones in a specific plan 
        public async Task<ResponseModel<List<MasterOptionHeaderModel>>> GetMasterOptionsByGroupAsync(int optionGroupId)
        {
            var options = new ResponseModel<List<MasterOptionHeaderModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                               options => options.RelativePath = $"api/option/getmasteroptionsbygroup/{optionGroupId}");
                var responseString = await response.Content.ReadAsStringAsync();
                options = JsonConvert.DeserializeObject<ResponseModel<List<MasterOptionHeaderModel>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif          
            }
            return options;
        }

        public async Task<ResponseModel<List<MasterOptionHeaderModel>>> GetAllMasterOptionsAsync()
        {
            var options = new ResponseModel<List<MasterOptionHeaderModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                               options => options.RelativePath = $"api/option/getallmasteroptions");
                var responseString = await response.Content.ReadAsStringAsync();
                options = JsonConvert.DeserializeObject<ResponseModel<List<MasterOptionHeaderModel>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif           
            }
            return options;
        }
        public async Task<ResponseModel> CopyMasterOptionAsync(MasterOptionHeaderModel model)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<MasterOptionHeaderModel, MasterOptionHeaderModel>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/option/copymasteroption/";
                             });
                return new ResponseModel() { IsSuccess = true, Message = "Done" };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel() { IsSuccess = false, Message = "Something failed" };
        }
        /// <summary>
        /// Get items in plan option
        /// </summary>
        /// <param name="optionId"></param>
        /// <returns></returns>
        public async Task<ResponseModel<List<AvailablePlanOptionDto>>> GetAvailablePlanOptionsByPlanAsync(int planId)
        {
            var items = new ResponseModel<List<AvailablePlanOptionDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/option/GetAvailablePlanOptionsByPlan/{planId}");
                var responseString = await response.Content.ReadAsStringAsync();
                items = JsonConvert.DeserializeObject<ResponseModel<List<AvailablePlanOptionDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return items;
        }
        public async Task<ResponseModel<AvailablePlanOptionDto>> UpdateAvailablePlanOptionAsync(AvailablePlanOptionDto updateOption)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<AvailablePlanOptionDto, ResponseModel<AvailablePlanOptionDto>>(
                            "DownstreamApi", updateOption,
                             options =>
                             {
                                 options.RelativePath = "api/option/UpdateAvailablePlanOption/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<AvailablePlanOptionDto>() { Value = null, IsSuccess = false, Message = "failed to update" };
        }
        public async Task<ResponseModel<bool>> DeleteAvailablePlanOptionAsync(int optionId)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<int, object>(
                            "DownstreamApi", optionId,
                             options => {
                                 options.RelativePath = "api/option/DeleteAvailablePlanOption/";
                             });
                return new ResponseModel<bool>() { Value = true, IsSuccess = true };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<bool>() { Value = false, IsSuccess = false };
        }

        public async Task<ResponseModel<List<int>>> DeleteAvailablePlanOptionsAsync(List<int> availablePlanOptionIds)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<int>, ResponseModel<List<int>>>(
                            "DownstreamApi", availablePlanOptionIds,
                             options => {
                                 options.RelativePath = "api/option/DeleteAvailablePlanOptions/";
                             });
                return new ResponseModel<List<int>>() { Value = availablePlanOptionIds, IsSuccess = true };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<int>>() { Value = availablePlanOptionIds, IsSuccess = false };
        }

        public async Task<ResponseModel<MasterOptionHeaderModel>> AddMasterOptionAsync(MasterOptionHeaderModel Option)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<MasterOptionHeaderModel, ResponseModel<MasterOptionHeaderModel>>(
                            "DownstreamApi", Option,
                             options => {
                                 options.RelativePath = "api/option/addmasteroption/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<MasterOptionHeaderModel>() { IsSuccess = false, Value = Option, Message = "Could not add option. Contact BI if proplem persists." };
        }

        public async Task<ResponseModel<MasterOptionHeaderModel>> UpdateMasterOptionAsync(MasterOptionHeaderModel Option)
        {
            var returnOption = new ResponseModel<MasterOptionHeaderModel>();
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterOptionHeaderModel, ResponseModel<MasterOptionHeaderModel>>(
                            "DownstreamApi", Option,
                             options => {
                                 options.RelativePath = "api/option/updatemasteroption/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return returnOption;
        }

        public async Task<ResponseModel<MasterOptionDto>> DeleteMasterOptionAsync(MasterOptionDto Option)
        {

            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterOptionDto, ResponseModel<MasterOptionDto>>(
                            "DownstreamApi", Option,
                             options => {
                                 options.RelativePath = "api/option/deletemasteroption/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<MasterOptionDto>() { Value = new MasterOptionDto(), IsSuccess = false };
        }


        public async Task<ResponseModel<OptionGroupDto>> AddOptionGroupAsync(OptionGroupDto OptionGroup)
        {
            var addOptionGroup = new ResponseModel<OptionGroupDto>();
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<OptionGroupDto, ResponseModel<OptionGroupDto>>(
                            "DownstreamApi", OptionGroup,
                             options => {
                                 options.RelativePath = "api/option/addoptiongroup/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return addOptionGroup;
        }

        public async Task<ResponseModel<OptionGroupDto>> UpdateOptionGroupAsync(OptionGroupDto OptionGroup)
        {
            var addOptionGroup = new ResponseModel<OptionGroupDto>();
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<OptionGroupDto, ResponseModel<OptionGroupDto>>(
                            "DownstreamApi", OptionGroup,
                             options => {
                                 options.RelativePath = "api/option/updateoptiongroup/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return addOptionGroup;
        }
        public async Task<ResponseModel<OptionGroupDto>> DeleteOptionGroupAsync(OptionGroupDto OptionGroup)
        {
            var addOptionGroup = new ResponseModel<OptionGroupDto>();
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<OptionGroupDto, ResponseModel<OptionGroupDto>>(
                            "DownstreamApi", OptionGroup,
                             options => {
                                 options.RelativePath = "api/option/deleteoptiongroup/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return addOptionGroup;
        }

        public async Task<ResponseModel<AddOptionToPlanModel>> AddOptionsToPlanAsync(List<AsmHeaderModel> OptionsToAdd, MasterPlanDto Plan)
        {
            var addOptionsModel = new AddOptionToPlanModel()
            {
                Plan = Plan,
                OptionsToAdd = OptionsToAdd
            };

            try
            {
                var response = await _downstreamAPI.PostForUserAsync<AddOptionToPlanModel, ResponseModel<AddOptionToPlanModel>>(
                            "DownstreamApi", addOptionsModel,
                             options => {
                                 options.RelativePath = "api/option/addoptionstomasterplan/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<AddOptionToPlanModel>() { Value = addOptionsModel, IsSuccess = false, Message = "Failed to add option. If issue persists please contact BI" };
        }

        public async Task<ResponseModel<AddOptionToPlanModel>> AddOptionsToPlanAndAdditionalAsync(List<AsmHeaderModel> OptionsToAdd, MasterPlanDto Plan, List<int> AdditionalPlans)
        {
            var addOptionsModel = new AddOptionToPlanModel()
            {
                Plan = Plan,
                OptionsToAdd = OptionsToAdd,
                AdditionalPlans = AdditionalPlans
            };

            try
            {
                var response = await _downstreamAPI.PostForUserAsync<AddOptionToPlanModel, ResponseModel<AddOptionToPlanModel>>(
                            "DownstreamApi", addOptionsModel,
                             options => {
                                 options.RelativePath = "api/option/addoptionstomasterplanandadditional/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<AddOptionToPlanModel>() { Value = addOptionsModel, IsSuccess = false, Message = "Failed to add option. If issue persists please contact BI" };
        }

        public async Task<ResponseModel<AddOptionToPlanModel>> AddToAvailablePlanOptionAsync(List<AsmHeaderModel> OptionsToAdd, SubdivisionPlanModel subdivisionPlanModel)
        {
            var addOptionsModel = new AddOptionToPlanModel()
            {
                SubdivisionPlanModel = subdivisionPlanModel,
                OptionsToAdd = OptionsToAdd
            };
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<AddOptionToPlanModel, ResponseModel<AddOptionToPlanModel>>(
                            "DownstreamApi", addOptionsModel,
                             options => {
                                 options.RelativePath = "api/option/addtoavailableplanoption/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<AddOptionToPlanModel>() { Value = addOptionsModel, IsSuccess = false };
        }

        #region Tree
        public async Task<ResponseModel<List<TreeListHierarchicalOptionGroup>>> GetTreeDataAsync()
        {
            var groups = new List<TreeListHierarchicalOptionGroup>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/option/gettreeitems");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    groups = JsonConvert.DeserializeObject<List<TreeListHierarchicalOptionGroup>>(responseString);
                    return new ResponseModel<List<TreeListHierarchicalOptionGroup>>() { IsSuccess = true, Value = groups };
                }
            }

            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes or something
            }
            catch (Exception ex)
            {
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.Error(ex);
#endif
            }
            return new ResponseModel<List<TreeListHierarchicalOptionGroup>>() { IsSuccess = false, Value = groups, Message = "Sorry, there was an error." };
        }

        public async Task<ResponseModel<List<TreeListHierarchicalOptionGroup>>> GetTreeChildAsync()
        {
            var groups = new List<TreeListHierarchicalOptionGroup>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/option/gettreechilditems");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    groups = JsonConvert.DeserializeObject<List<TreeListHierarchicalOptionGroup>>(responseString);
                    return new ResponseModel<List<TreeListHierarchicalOptionGroup>>() { IsSuccess = true, Value = groups };
                }
            }

            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes or something
            }
            catch (Exception ex)
            {
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.Error(ex);
#endif
            }
            return new ResponseModel<List<TreeListHierarchicalOptionGroup>>() { IsSuccess = false, Value = groups, Message = "Sorry, there was an error." };
        }

        public async Task<ResponseModel<List<TreeOptionGroup>>> GetTreeOptionAsync()
        {
            List<TreeOptionGroup> options = new List<TreeOptionGroup>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/option/gettreeoptiongroups");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    options = JsonConvert.DeserializeObject<List<TreeOptionGroup>>(responseString);

                    return new ResponseModel<List<TreeOptionGroup>>() { IsSuccess = true, Value = options };
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.Error(ex);
#endif
            }

            return new ResponseModel<List<TreeOptionGroup>>() { IsSuccess = false, Value = options, Message = "Sorry, there was an error." };
        }
        #endregion

        /// <summary>
        /// Gets a list of option types.
        /// </summary>
        /// <returns></returns>
        public async Task<List<OptionTypeDto>> GetOptionTypesAsync()
        {
            var items = new List<OptionTypeDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/option/getoptiontypes");
                var responseString = await response.Content.ReadAsStringAsync();
                items = JsonConvert.DeserializeObject<List<OptionTypeDto>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return items;
        }

        public async Task<ResponseModel<List<MasterAttributeGroupDto>>> GetOptionWithGroupsAsync(int optionId)
        {
            var options = new ResponseModel<List<MasterAttributeGroupDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                               options => options.RelativePath = $"api/option/getoptionwithgroups/{optionId}");
                var responseString = await response.Content.ReadAsStringAsync();
                options = JsonConvert.DeserializeObject<ResponseModel<List<MasterAttributeGroupDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif          
            }
            return options;
        }

        public async Task<ResponseModel<List<MasterAttributeGroupDto>>> GetMasterAttributeGroupsAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/option/getmasterattributegroups");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var groups = JsonConvert.DeserializeObject<ResponseModel<List<MasterAttributeGroupDto>>>(responseString);
                    return groups;
                }
            }

            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<MasterAttributeGroupDto>>() { IsSuccess = false, Value = null, Message = "Sorry, there was an error." };
        }

        public async Task<ResponseModel<List<MasterOptionAttributeItemDto>>> AddMasterOptionAttributeItemAsync(List<MasterOptionAttributeItemDto> items)
        {
            var responseItem = new ResponseModel<List<MasterOptionAttributeItemDto>>();

            try
            {
                responseItem = await _downstreamAPI.PostForUserAsync<List<MasterOptionAttributeItemDto>, ResponseModel<List<MasterOptionAttributeItemDto>>>(
                    "DownstreamApi", items,
                    options => {
                        options.RelativePath = "api/option/addmasteroptionattributeitem/";
                    });
                return new ResponseModel<List<MasterOptionAttributeItemDto>>() { Value = responseItem.Value ?? items, IsSuccess = true, Message = responseItem.Message };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<MasterOptionAttributeItemDto>>() { Value = items, IsSuccess = false };
        }

        public async Task<ResponseModel<MasterAttributeGroupDto>> ReactivateMasterOptionAttributeItemAsync(MasterAttributeGroupDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterAttributeGroupDto, MasterAttributeGroupDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/option/reactivatemasteroptionattributeitem/";
                             });

                return new ResponseModel<MasterAttributeGroupDto>() { IsSuccess = true, Value = response, Message = "Success" };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new ResponseModel<MasterAttributeGroupDto>() { IsSuccess = false, Value = null, Message = "Sorry, there was an error." };
        }

        public async Task<ResponseModel<MasterAttributeGroupDto>> DeleteMasterOptionAttributeItemAsync(MasterAttributeGroupDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterAttributeGroupDto, MasterAttributeGroupDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/option/deletemasteroptionattributeitem/";
                             });

                return new ResponseModel<MasterAttributeGroupDto>() { IsSuccess = true, Value = response, Message = "Success" };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }

            return new ResponseModel<MasterAttributeGroupDto>() { IsSuccess = false, Value = null, Message = "Sorry, there was an error." };
        }
    }
}
