﻿@inject BudgetService BudgetService

<div class="card-body">
    <div class="row d-flex justify-content-center">
        <div class="col">
            <p class="text-dark mb-0 fw-semibold">Estimate Budget</p>
            <h3 class="m-0">@TotalEstimateBudget</h3>
            <p class="mb-0 text-truncate text-muted">
                <span class="text-success">
                    Last Month
                </span>
            </p>
        </div>
        <div class="col-auto align-self-center">
            <div class="report-main-icon bg-light-alt">
                <img width="64" height="64" src="https://img.icons8.com/dusk/64/estimate.png" alt="estimate" class="align-self-center text-muted icon-sm" />
            </div>
        </div>
    </div>
</div>

@code {
    public string? TotalEstimateBudget { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        var totalEstimateBudget = await BudgetService.GetEstimateBudget();

        if (totalEstimateBudget != null)
        {
            if (totalEstimateBudget.Value != null)
            {
                TotalEstimateBudget = totalEstimateBudget.Value.TotalEstimateBudget;
            }
        }
    }
}
