﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class ModelManagerItemModel
{
    public int MasterItemId { get; set; }
    public int? PactivityId { get; set; }
    public int? AsmDetailId { get; set; }
    public int MasterItemPhaseId { get; set; }
    public int AsmHeaderId { get; set; }
    public int? MasterPlanId { get; set; }
    public string? ItemNumber { get; set; }
    public double? Factor { get; set; }
    public string? PeCategoryCode { get; set; }

    public int? BomClassId { get; set; }
    public string? PhaseCode { get; set; }
    public string? PhaseDesc { get; set; }
    public string? ItemDesc { get; set; }

    public string? ItemNotes { get; set; }
    public string? OptionItemNotes { get; set; }
    public int? Waste { get; set; }

    public string? UseWaste { get; set; }

    public string? TakeoffUnit { get; set; }

    public string? OrderUnit { get; set; }

    public double? CnvFctr { get; set; }

    public string? Multdiv { get; set; }

    public string? RndDir { get; set; }

    public double? RndUnit { get; set; }

    public double? PeUnitPrice { get; set; }

    public DateTime? PeUnitPriceDtCg { get; set; }

    public string? Taxable { get; set; }
    public bool IsTaxable { get; set; } = false; //in the database Taxable, LumpSum, and ExcludefromPO are string, but really this is a yes/no thing
    public bool IsLumpSum { get; set; } = false;
    public bool IsExcludeFromPO { get; set; } = false;

    public string? JcPhase { get; set; }

    public string? JcCategory { get; set; }

    public int? OldTlpePhaseId { get; set; }

    public string? OldItemNumber { get; set; }

    public string? DeletedFromPe { get; set; }

    public double? CalcPercent { get; set; }

    public string? ExcludeFromPo { get; set; }

    public string? Formula { get; set; }

    public int? EstDbOwner { get; set; }

    public int? CalcBasis { get; set; }

    public string? PlanSpecific { get; set; }
    public bool IsPlanSpecific { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }
    public string? BomClassName { get; set; }

    public List<string>? OptionsContainingItem { get; set; }
}
