﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class TemplateSactivity
{
    public int TemplateAid { get; set; }

    public int TemplateMid { get; set; }

    public int? TemplateIdNotused { get; set; }

    public int SactivityId { get; set; }

    public int? Seq { get; set; }

    public int? Duration { get; set; }

    public int? LagTime { get; set; }

    public string? UserAlpha1 { get; set; }

    public string? UserAlpha2 { get; set; }

    public string? UserAlpha3 { get; set; }

    public string? UserAlpha4 { get; set; }

    public string? UserAlpha5 { get; set; }

    public string? UserAlpha6 { get; set; }

    public DateTime? UserDate1 { get; set; }

    public DateTime? UserDate2 { get; set; }

    public DateTime? UserDate3 { get; set; }

    public DateTime? UserDate4 { get; set; }

    public DateTime? UserDate5 { get; set; }

    public DateTime? UserDate6 { get; set; }

    public string? Note { get; set; }

    public int? ChecklistId { get; set; }

    public double? UserCurrency1 { get; set; }

    public double? UserCurrency2 { get; set; }

    public double? UserCurrency3 { get; set; }

    public double? UserCurrency4 { get; set; }

    public double? UserCurrency5 { get; set; }

    public double? UserCurrency6 { get; set; }

    public string? GrossLag { get; set; }

    public string? GenPitBudget { get; set; }

    public DateTime Createddatetime { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public string? UpdatedBy { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual Sactivity Sactivity { get; set; } = null!;

    public virtual TemplateMilestone TemplateM { get; set; } = null!;

    public virtual ICollection<TemplateSactivityPred> TemplateSactivityPreds { get; set; } = new List<TemplateSactivityPred>();
}
