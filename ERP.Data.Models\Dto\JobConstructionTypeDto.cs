﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class JobConstructionTypeDto : IMapFrom<JobConstructionType>
{
    public int JobConstructionTypeId { get; set; }

    public string? Description { get; set; } 

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<JobConstructionTypeDto, JobConstructionType>().ReverseMap();
    }

    // public byte[] RecordTimeStamp { get; set; } = null!;

    // public virtual ICollection<Job> Jobs { get; set; } = new List<Job>();
}
