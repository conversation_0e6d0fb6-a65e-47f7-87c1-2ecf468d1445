﻿@using ERP.Web.Components
@using Microsoft.AspNetCore.Components.Web
@namespace ERP.Web.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="~/" />
    <link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />
    <link href="_content/Telerik.UI.for.Blazor/css/kendo-theme-bootstrap/all.css" rel="stylesheet" />
    <link href="_content/Telerik.UI.for.Blazor/css/kendo-font-icons/font-icons.css" rel="stylesheet" />
    <link href="css/site.css" rel="stylesheet" />
    <link href="css/assets/css/app.css" rel="stylesheet" type="text/css" />
    <link href="css/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <link href="css/assets/css/metisMenu.min.css" rel="stylesheet" type="text/css" />
    <link href="css/assets/plugins/daterangepicker/daterangepicker.css" rel="stylesheet" type="text/css" />
    <link rel="icon" type="image/x-icon" href="~/css/assets/images/vm-hub-25.png">



    <style type="text/css">
        .btn-report-bug {
        background-color: black; 
        color: white;  
        border: none;  
        padding: 17px 20px; 
        display: flex; 
        align-items: center; 
        justify-content: center;  
        font-size: 12px; 
        cursor: pointer; 
        transition: background-color 0.3s ease; 
        }
        /* Add hover effect */
        .btn-report-bug:hover {
        background-color: #333; 
        }
        /* Style for the icon inside the button */
        .btn-report-bug i {
        display: flex; 
        align-items: center; 
        justify-content: center;
        text-align: center;
        font-size: 18px;
        display: inline-block;
        margin-right: 8px;  
        }

        .topbar .navigation-menu > li:hover a {
        color: #f4cd64;
        }

        .navbar-custom-menu .has-submenu.active a {
        color: #fff4d8;
        }

        .navbar-custom-menu .has-submenu submenu.active a {
        color: #fff4d8;
        }

        .navbar-custom-menu .has-submenu.active a span {
        background-color: rgba(244, 205, 100, 0.1);
        }



        [data-layout="horizontal"] .topbar .brand {
        background-color: #000000;
        }

        [data-layout="horizontal"] .topbar .brand .logo .logo-sm {
        height: 30px;
        margin-left:10px;
        }

        [data-layout="horizontal"] .navbar-custom-menu {
        min-height: 52px;
        display: flex;
        align-items: center;
        background-color: #000000;
        padding-left: 25px;
        }

        .navbar-custom .nav-link {
        padding: 0 0.75rem;
        color: #ffffff;
        line-height: 52px;
        max-height: 52px;
        background-color: #000000;      
        }

        .topbar .navigation-menu > li > a {
        display: flex;
        align-items: center;
        color: #ffffff;
        background-color: #000000
        }

        body {
        font-family: "Roboto",sans-serif;
        font-size: .75rem;
        background-color: #efefef;
        }

        [data-layout="horizontal"] .page-wrapper {
        width: 100%;
        }

        .k-grid, .k-table, .btn, .k-input-md, .k-input-inner,
        .k-tabstrip, .k-input-value-text,
        .k-list-md, .k-list-item-text, .k-list-optionlabel, .k-input-value-text,
        .card-body, .k-button, .k-button-md, label, .k-window-title, .card-title,
        .k-messagebox {
        font-size: .688rem;
        font-family: "Roboto",sans-serif;
        }

        .k-tabstrip-content, .k-tabstrip > .k-content {
        border: 1px solid #dee2e6;
        }

        .k-grid .k-table-th.k-selected, .k-grid td.k-selected, .k-grid .k-table-td.k-selected, .k-grid .k-table-row.k-selected > td, .k-grid .k-table-row.k-selected > .k-table-td {
        color: #212529;
        background-color: #fff9ec;
        }

        .k-tabstrip-items-wrapper .k-item {
        color: #000000;
        background-color: #D3D3D3;
        }

        .k-tabstrip-top > .k-tabstrip-items-wrapper .k-item {
        border-top-left-radius: 0.25rem;
        border-top-right-radius: 0.25rem;
        border-bottom-width: 0;
        margin-right: 2px;
        }

        h4 {
        font-size: .9125rem;
        font-weight: 500;
        font-family: "Roboto",sans-serif;
        }

        .k-breadcrumb, .k-tabstrip-items .k-link {
        font-size: .688rem;
        font-family: "Roboto",sans-serif;
        }

        .searchBox {
        width: 20em !important;
        }

        p {
        font-size: .688rem;
        font-family: "Roboto",sans-serif;
        }

        .left-sidenav-menu li ul li > a {
        font-family: "Roboto",sans-serif;
        font-size: .688rem;
        }

        .k-window-title {
        font-family: "Roboto",sans-serif;
        font-size: .688rem;
        font-weight: 500;
        }

        .customSeparator {
        margin: 12px 0 12px 0;
        }

        .hr {
        border-bottom: 1px solid #e3ebf6;
        }

        .px-4 {
        padding-left: 0 !important;
        padding-right: 0 !important;
        }

        .navbar {
        padding-top: 0;
        }

        .k-button-solid-primary {
        background-color: #f4cd64;
        color: #000000;
        border-color: #f4cd64;
        }

        .k-button-solid-primary:hover {
        background-color: rgba(244, 205, 100, 0.25);
        color: #000000;
        border-color: #f4cd64;
        }

        .k-button-success {
        border-color: #f4cd64;
        color: #000000;
        background-color: #f4cd64;
        }

        .k-button-danger {
        border-color: #e6a7a7;
        color: #000000;
        background-color: #e6a7a7;
        }

        .k-button-add {
        border-color: #a7e6c0;
        color: #000000;
        background-color: #a7e6c0;
        }


        .btn-outline-light {
        color: #000000;
        background-image: none;
        background-color: rgba(244, 205, 100, 0.25);
        border-color: #000000;
        }


        .btn-soft-primary {
        background-color: #fff4d8;
        color: #000000;
        }

        .btn-outline-primary {
        color: #f4cd64;
        border-color: #f4cd64;
        background-color: rgba(244, 205, 100, 0.25);
        }

        .btn-outline-primary:hover {
        color: #ffffff;
        background-color: #f4cd64;
        border-color: #f4cd64;
        }

        .btn-primary {
        border-color: #f4cd64;
        color: #000000;
        background-color: #f4cd64;
        }

        .btn-primary:hover {
        border-color: #f4cd64;
        color: #000000;
        background-color: rgba(244, 205, 100, 0.25);
        }

        .k-chip-md {
        font-family: "Roboto",sans-serif;
        font-size: .688rem;
        }

        .k-window {
        font-family: "Roboto",sans-serif;
        font-size: .688rem;
        }

        .card-header {
        background-color: #dcd8ce;
        color: #4b432c;
        border-bottom: 2px solid #968d73;
        font-weight: bold;
        font-size: .75rem;
        }

        .card .card-header {
        background-color: #dcd8ce;
        color: #4b432c;
        border-bottom: 2px solid #968d73;
        font-weight: bold;
        font-size: .75rem;
        }

        .validation-errors {
        border-color: #f3bfc1;
        color: #6f0d12;
        background-color: #f7d1d3;
        }


        .sticky-top {
        position: sticky;
        top: 60px;
        z-index: 1000; 
        background-color: #efefef; 
        }


    </style>

    @*Telerik*@
    @*     <script src="https://blazor.cdn.telerik.com/blazor/6.2.0/telerik-blazor.min.js"></script> *@
    <script src="_content/Telerik.UI.for.Blazor/js/telerik-blazor.js"></script>@* defer allows load asyncrhonously, but may be causing errors on slower networks*@
    @*   https://docs.telerik.com/blazor-ui/troubleshooting/js-errors#defer-attribute *@
    @*  <script src="_content/Telerik.UI.for.Blazor/js/telerik-blazor.js" defer></script> *@
    <component type="typeof(HeadOutlet)" render-mode="ServerPrerendered" />
</head>
<body data-layout="horizontal">
    <!-- Top Bar Start -->
    <div class="topbar">
        <!-- LOGO -->
        <div class="brand">
            <a href="/" class="logo">
                <span>
                    <img src="css/assets/images/vmlogowht.png" alt="logo-small" class="logo-sm ml-5">
                </span>
            </a>
        </div>
        <!--end logo-->
        <!-- Navbar -->
        <nav class="navbar-custom">
            <ul class="list-unstyled topbar-nav float-end mb-0 ml-2">
                <!--NEW TASK BUTTON-->
                @*  <li class="creat-btn">
                    <div class="nav-link">
                        <a class=" btn btn-sm btn-soft-primary" href="#" role="button"><i class="fas fa-plus me-2"></i>New Task</a>
                    </div>
                </li> *@

                <!--HIDDEN FOR NOW, DOES NOTHING-->

                @*  <li class="dropdown hide-phone">
                    <a class="nav-link dropdown-toggle arrow-none waves-light waves-effect" data-bs-toggle="dropdown" href="#" role="button"
                       aria-haspopup="false" aria-expanded="false">
                        <i data-feather="search" class="topbar-icon"></i>
                    </a>

                    <div class="dropdown-menu dropdown-menu-end dropdown-lg p-0">
                        <!-- Top Search Bar -->
                        <div class="app-search-topbar">
                            <form action="#" method="get">
                                <input type="search" name="search" class="from-control top-search mb-0" placeholder="Type text...">
                                <button type="submit"><i class="ti-search"></i></button>
                            </form>
                        </div>
                    </div>
                </li> *@

                <!--NOTIFICATION ICON SETUP OR REMOVE? -->

                @*   <li class="dropdown notification-list">
                    <a class="nav-link dropdown-toggle arrow-none waves-light waves-effect" data-bs-toggle="dropdown" href="#" role="button"
                       aria-haspopup="false" aria-expanded="false">
                        <i data-feather="bell" class="align-self-center topbar-icon"></i>
                        <span class="badge bg-danger rounded-pill noti-icon-badge">2</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end dropdown-lg pt-0">

                        <h6 class="dropdown-item-text font-15 m-0 py-3 border-bottom d-flex justify-content-between align-items-center">
                            Notifications <span class="badge bg-primary rounded-pill">2</span>
                        </h6>
                        <div class="notification-menu" data-simplebar>
                            <!-- item-->
                            <a href="#" class="dropdown-item py-3">
                                <small class="float-end text-muted ps-2">2 min ago</small>
                                <div class="media">
                                    <div class="avatar-md bg-soft-primary">
                                        <i data-feather="shopping-cart" class="align-self-center icon-xs"></i>
                                    </div>
                                    <div class="media-body align-self-center ms-2 text-truncate">
                                        <h6 class="my-0 fw-normal text-dark">Your order is placed</h6>
                                        <small class="text-muted mb-0">Dummy text of the printing and industry.</small>
                                    </div><!--end media-body-->
                                </div><!--end media-->
                            </a><!--end-item-->
                            <!-- item-->
                            <a href="#" class="dropdown-item py-3">
                                <small class="float-end text-muted ps-2">10 min ago</small>
                                <div class="media">
                                    <div class="avatar-md bg-soft-primary">
                                        <img src="assets/images/users/user-4.jpg" alt="" class="thumb-sm rounded-circle">
                                    </div>
                                    <div class="media-body align-self-center ms-2 text-truncate">
                                        <h6 class="my-0 fw-normal text-dark">Meeting with designers</h6>
                                        <small class="text-muted mb-0">It is a long established fact that a reader.</small>
                                    </div><!--end media-body-->
                                </div><!--end media-->
                            </a><!--end-item-->
                            <!-- item-->
                            <a href="#" class="dropdown-item py-3">
                                <small class="float-end text-muted ps-2">40 min ago</small>
                                <div class="media">
                                    <div class="avatar-md bg-soft-primary">
                                        <i data-feather="users" class="align-self-center icon-xs"></i>
                                    </div>
                                    <div class="media-body align-self-center ms-2 text-truncate">
                                        <h6 class="my-0 fw-normal text-dark">UX 3 Task complete.</h6>
                                        <small class="text-muted mb-0">Dummy text of the printing.</small>
                                    </div><!--end media-body-->
                                </div><!--end media-->
                            </a><!--end-item-->
                            <!-- item-->
                            <a href="#" class="dropdown-item py-3">
                                <small class="float-end text-muted ps-2">1 hr ago</small>
                                <div class="media">
                                    <div class="avatar-md bg-soft-primary">
                                        <img src="assets/images/users/user-5.jpg" alt="" class="thumb-sm rounded-circle">
                                    </div>
                                    <div class="media-body align-self-center ms-2 text-truncate">
                                        <h6 class="my-0 fw-normal text-dark">Your order is placed</h6>
                                        <small class="text-muted mb-0">It is a long established fact that a reader.</small>
                                    </div><!--end media-body-->
                                </div><!--end media-->
                            </a><!--end-item-->
                            <!-- item-->
                            <a href="#" class="dropdown-item py-3">
                                <small class="float-end text-muted ps-2">2 hrs ago</small>
                                <div class="media">
                                    <div class="avatar-md bg-soft-primary">
                                        <i data-feather="check-circle" class="align-self-center icon-xs"></i>
                                    </div>
                                    <div class="media-body align-self-center ms-2 text-truncate">
                                        <h6 class="my-0 fw-normal text-dark">Payment Successfull</h6>
                                        <small class="text-muted mb-0">Dummy text of the printing.</small>
                                    </div><!--end media-body-->
                                </div><!--end media-->
                            </a><!--end-item-->
                        </div>
                        <!-- All-->
                        <a href="javascript:void(0);" class="dropdown-item text-center text-primary">
                            View all <i class="fi-arrow-right"></i>
                        </a>
                    </div>
                </li> *@


                <li>
                    <button id="reportBug" onclick="navigateToBugReport()" class="btn-report-bug" title="Create Support Email">
                        <i class="fa fa-question-circle" aria-hidden="true"></i>&nbsp;
                    </button>
                </li>
                <li class="dropdown">
                    <component type="typeof(MyProfile)" render-mode="Server" />
                </li>
                <li class="menu-item">
                    <!-- Mobile menu toggle-->
                    <a class="navbar-toggle nav-link" id="mobileToggle">
                        <div class="lines">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </a><!-- End mobile menu toggle-->
                </li> <!--end menu item-->
            </ul><!--end topbar-nav-->

            <div class="navbar-custom-menu">
                <div id="navigation">
                    <!-- Navigation Menu-->
                    <ul class="navigation-menu">
                        @* <li class="has-submenu">
                            <a href="#">
                                <span><i data-feather="home" class="align-self-center hori-menu-icon"></i>Dashboard</span>
                            </a>
                        </li><!--end has-submenu--> *@
                        @if (@Context.User.IsInRole("Admin") || @Context.User.IsInRole("DesignCenter") || @Context.User.IsInRole("Accounting") || @Context.User.IsInRole("ReadOnly"))
                        {
                            <li class="has-submenu">
                                <a href="#">
                                    <span><i data-feather="grid" class="align-self-center hori-menu-icon"></i>Configurations</span>
                                </a>
                                <ul class="submenu">
                                    <li class="has-submenu">
                                        <a href="#"><i class="ti ti-minus"></i>Plans </a>
                                        <ul class="submenu">
                                            <li><a href="/configuremasterplans"><i class="ti ti-minus"></i>Plans</a></li>
                                            <li><a href="/configuremasteroptions"><i class="ti ti-minus"></i>Options</a></li>
                                            <li><a href="/configuremasteritems"><i class="ti ti-minus"></i>Items</a></li>
                                            @if ((@Context.User.IsInRole("Admin") || @Context.User.IsInRole("DesignCenter")) && !@Context.User.IsInRole("ReadOnly"))
                                            {
                                                <li><a href="/colorschemes"><i class="ti ti-minus"></i>Color Schemes</a></li>
                                            }

                                        </ul>
                                    </li> <!--end has-submenu-->
                                    <li class="has-submenu">
                                        <a href="#"><i class="ti ti-minus"></i>Subdivision</a>
                                        <ul class="submenu">
                                            <li><a href="/subdivisions"><i class="ti ti-minus"></i>Subdivisions</a></li>
                                            <li><a href="/lots"><i class="ti ti-minus"></i>Lots</a></li>
                                            @if (!(Context.User.IsInRole("ReadOnly")))
                                            {
                                                <li><a href="/hoa"><i class="ti ti-minus"></i>HOA</a></li>
                                            }

                                        </ul>
                                    </li> <!--end has-submenu-->
                                    <li class="has-submenu">
                                        <a href="#"><i class="ti ti-minus"></i>Trades and Suppliers </a>
                                        <ul class="submenu">
                                            <li><a href="/suppliers"><i class="ti ti-minus"></i>Suppliers</a></li>
                                            <li><a href="/setuptradesuppliers"><i class="ti ti-minus"></i>Assign Trade Suppliers</a></li>
                                            @if (!Context.User.IsInRole("ReadOnly"))
                                            {
                                                <li><a href="/suppliermessages"><i class="ti ti-minus"></i>Supplier Messages</a></li>
                                            }
                                        </ul>
                                    </li> <!--end has-submenu-->
                                    @if (@Context.User.IsInRole("Admin") || @Context.User.IsInRole("DesignCenter"))
                                    {
                                        <li class="has-submenu">
                                        <a href="#"><i class="ti ti-minus"></i>Attributes</a>
                                        <ul class="submenu">
                                            <li><a href="/attributes"><i class="ti ti-minus"></i>Groups and Items</a></li>
                                            <li><a href="/attributesbyplan"><i class="ti ti-minus"></i>By Plan</a></li>
                                            <li><a href="/attributesbyoption"><i class="ti ti-minus"></i>By Option</a></li>
                                        </ul>
                                        </li>
                                    }
                                </ul><!--end submenu-->
                            </li>

                            <!--end has-submenu-->

                            <li class="has-submenu">
                                <a href="#">
                                    <span><i data-feather="box" class="align-self-center hori-menu-icon"></i>Sales</span>
                                </a>
                                <ul class="submenu">
                                    @if (!Context.User.IsInRole("ReadOnly"))
                                    {
                                        <li class="has-submenu">
                                            <a href="#"><i class="ti ti-minus"></i>Approve Sales Config</a>
                                            <ul class="submenu">
                                                <li class="mr-1"><a href="/approvesalesconfig"><i class="ti ti-minus"></i>Sales Config/Change Orders</a></li>
                                                <li><a href="/approveselections"><i class="ti ti-minus"></i>Selected Options</a></li>
                                            </ul>
                                        </li><!--end has-submenu-->
                                    }

                                    <li class="has-submenu">
                                        <a href="#"><i class="ti ti-minus"></i>Sales Pricing</a>
                                        <ul class="submenu">
                                            <li>
                                                <ul>
                                                    <li class="mr-1"><a href="/manageprice"><i class="ti ti-minus"></i>Community Models/Options</a></li>
                                                    <li><a href="/salesworksheet"><i class="ti ti-minus"></i>Sales Worksheets &nbsp;</a></li>                                                   
                                                </ul>
                                            </li>
                                        </ul>
                                    </li><!--end has-submenu-->
                                </ul><!--end submenu-->
                            </li>
                         <!--end has-submenu-->
                        }
                        @if (@Context.User.IsInRole("Admin") || @Context.User.IsInRole("Accounting") || @Context.User.IsInRole("ReadOnly"))
                            {
                                <li class="has-submenu">
                                <a href="#">
                                    <span><i data-feather="file-plus" class="align-self-center hori-menu-icon"></i>Purchasing</span>
                                </a>
                                <ul class="submenu">
                                    <li>
                                        <a href="/managecost"><i class="ti ti-minus"></i>Cost</a>
                                    </li>
                                    <li>
                                        <a href="/budget"><i class="ti ti-minus"></i>Budget</a>
                                    </li>
                                    <li class="has-submenu">
                                        <a href="#"><i class="ti ti-minus"></i>Purchase Orders</a>
                                        <ul class="submenu">
                                            <li>
                                                <a href="/issuepo"><i class="ti ti-minus"></i>Issue PO</a>
                                            </li>
                                            <li>
                                                <a href="/poinquiry"><i class="ti ti-minus"></i>Purchase Order Inquiry</a>
                                            </li>
                                            <li>
                                                <a href="/checkpayments"><i class="ti ti-minus"></i>Payments</a>
                                            </li>
                                            <li>
                                                <a href="/approvepos"><i class="ti ti-minus"></i>Approve Payment of POs</a>
                                            </li>

                                        </ul>
                                    </li><!--end has-submenu-->
                                    <li>
                                        <a href="/customestimate"><i class="ti ti-minus"></i>Custom Estimates</a>
                                    </li>
                                    <li>
                                        <a href="/purchasingactivities"><i class="ti ti-minus"></i>Purchasing Activities</a>
                                    </li>
                                    <li>
                                        <a href="/jobtasks"><i class="ti ti-minus"></i>Job Tasks</a>
                                    </li>
                                    <li>
                                        <a href="/addenda"><i class="ti ti-minus"></i>Addenda</a>
                                    </li>
                                    <li>                                                
                                        <a href="/accounting"><i class="ti ti-minus"></i>Send to Accounting</a>                                        
                                    </li>
                                </ul>
                            </li>

                            <!--end has-submenu-->
                            } 
                            
                        @if (@Context.User.IsInRole("Admin") || @Context.User.IsInRole("DesignCenter") || @Context.User.IsInRole("ConstructionDirector") || @Context.User.IsInRole("ConstructionManager"))
                        {
                            <li class="has-submenu">
                                <a href="#">
                                    <span><i data-feather="file-plus" class="align-self-center hori-menu-icon"></i>Scheduling</span>
                                </a>
                                <ul class="submenu">
                                    @*  <li>
                                <a href="/masterschedule"><i class="ti ti-minus"></i>Master Schedule</a>
                                </li>    *@
                                    @if (@Context.User.IsInRole("Admin") || @Context.User.IsInRole("ConstructionDirector") || @Context.User.IsInRole("ConstructionManager"))
                                    {
                                        <li>
                                        <a href="/constructiondashboard"><i class="ti ti-minus"></i>Dashboard</a>
                                    </li>
                                    <li>
                                        <a href="/schedule"><i class="ti ti-minus"></i>Schedules</a>
                                    </li>
                                    <li>
                                        <a href="/openschedules"><i class="ti ti-minus"></i>My Schedule/Open Schedules</a>
                                    </li>
                                    <li>
                                        <a href="/constructiondailytasks"><i class="ti ti-minus"></i>My Tasks</a>
                                    </li>
                                    <li>
                                        <a href="/supplierschedule"><i class="ti ti-minus"></i>Supplier Schedules</a>
                                    </li>
                                    <li>
                                        <a href="/packageitem"><i class="ti ti-minus"></i>Package Items</a>                                           
                                    </li>
                                    }
                                    @if (@Context.User.IsInRole("Admin") || @Context.User.IsInRole("ConstructionDirector")  || @Context.User.IsInRole("DesignCenter") || @Context.User.IsInRole("ConstructionManager"))
                                    {
                                         <li><a href="/lotdetails"><i class="ti ti-minus"></i>Job Details</a></li>
                                    <li class="has-submenu">
                                        <a href="#"><i class="ti ti-minus"></i>VPO</a>
                                        <ul class="submenu">
                                            <li>
                                                <a href="/variancepurchaseorders"><i class="ti ti-minus"></i>My VPOs</a>
                                            </li>
                                            <li>
                                                <a href="/vpoapprovals"><i class="ti ti-minus"></i>VPO Approvals</a>
                                            </li>
                                            <li>
                                                <a href="/vpothreshold"><i class="ti ti-minus"></i>Adjust Threshold</a>
                                            </li>
                                        </ul>
                                    </li>
                                    }
                                    
                                   
                                    @if (@Context.User.IsInRole("Admin") || @Context.User.IsInRole("ConstructionDirector"))
                                    {
                                    <li class="has-submenu">
                                        <a href="#"><i class="ti ti-minus"></i>Setup</a>
                                        <ul class="submenu">
                                            <li>
                                                <a href="/scheduletemplates"><i class="ti ti-minus"></i>Templates</a>
                                            </li>
                                            <li>
                                                <a href="/milestones"><i class="ti ti-minus"></i>Milestones</a>
                                            </li>
                                            <li>
                                                <a href="/scheduleactivities"><i class="ti ti-minus"></i>Activities</a>
                                            </li>
                                            <li>
                                                <a href="/variancecodes"><i class="ti ti-minus"></i>Variance Codes</a>
                                            </li>
                                            <li>
                                                <a href="/schedulecalendar"><i class="ti ti-minus"></i>Holiday Calendar</a
                                            </li>
                                            
                                        </ul>
                                    </li><!--end has-submenu-->


                                    }

                                </ul>
                            </li>

                            <!--end has-submenu-->
                        }
                        @if (@Context.User.IsInRole("Admin") || @Context.User.IsInRole("Operations"))
                        {
                            <li class="has-submenu">
                                <a href="#">
                                    <span><i data-feather="file-plus" class="align-self-center hori-menu-icon"></i>Operations</span>
                                </a>
                                <ul class="submenu">
                                    <li>
                                        <a href="/operationsjob"><i class="ti ti-minus"></i>Job Operations Dashboard</a>
                                    </li>
                                     <li>
                                        <a href="/releaseschedules"><i class="ti ti-minus"></i>Release Schedules</a>
                                    </li> 
                                </ul>
                            </li>

                            <!--end has-submenu-->
                        }
                    </ul><!-- End navigation menu -->
                </div> <!-- end navigation -->
            </div>
            <!-- Navbar -->
        </nav>
        <!-- end navbar-->
    </div>
    <!-- Top Bar End -->

    <div class="page-wrapper">
        <!-- Page Content-->
        <div class="page-content">
            <div class="container-fluid">
                <component type="typeof(EnvironmentBanner)" render-mode="Server" />
                <div class="row">
                    <div class="col-sm-12">
                        @RenderBody()

                        <div id="blazor-error-ui">
                            <environment include="Staging,Production">
                                An error has occurred. This application may no longer respond until reloaded.
                            </environment>
                            <environment include="Development">
                                An unhandled exception has occurred. See browser dev tools for details.
                            </environment>
                            <a href="" class="reload">Reload</a>
                            <a class="dismiss">🗙</a>
                        </div>
                    </div>
                </div>
            </div><!-- container -->
            <footer class="footer text-center text-sm-start" style="position:relative; margin-top:2em">
                &copy; <script>
                           document.write(new Date().getFullYear())
                </script> VanMetrics
            </footer><!--end footer-->
        </div>
        <!-- end page content -->
    </div>

    <script src="_framework/blazor.server.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.3.min.js" integrity="sha256-pvPw+upLPUjgMXY0G+8O0xUf+/Im1MZjXxxgOcBQBXU=" crossorigin="anonymous"></script>
    <script src="css/assets/js/metismenu.min.js"></script>
    <script src="css/assets/js/waves.js"></script>
    <script src="css/assets/js/feather.min.js"></script>
    <script src="css/assets/js/simplebar.min.js"></script>
    <script src="css/assets/js/moment.js"></script>
    <script src="css/assets/plugins/daterangepicker/daterangepicker.js"></script>
    <script src="css/assets/plugins/apex-charts/apexcharts.min.js"></script>
    <script src="css/assets/plugins/jvectormap/jquery-jvectormap-2.0.2.min.js"></script>
    <script src="css/assets/plugins/jvectormap/jquery-jvectormap-us-aea-en.js"></script>

    <script src="css/assets/js/app.js"></script>

    <script src="js/download.js"></script>
    <script src="js/browser-check.js"></script>
    <script>
        // JavaScript function to handle the click event
    function navigateToBugReport() {
        // Redirect to the report bug page
        window.location.href = 'mailto:<EMAIL>?subject=Help Request for Hub ERP';
    }
    </script>

</body>
</html>
