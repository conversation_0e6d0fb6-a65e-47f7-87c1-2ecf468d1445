﻿using System;
using System.Collections.Generic;
using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;

namespace ERP.Data.Models.Dto;

public class JobDto : IMapFrom<Job>
{
    public string? JobNumber { get; set; }
    public string? DisplayDescription { get; set; }//for dropdown display 
    public string? SubdivisionNum { get; set; } //BC won't have subdivisonId
    public decimal? AcquisitionLotCost { get; set; }

    public int? ApprovedDepositRequirement { get; set; }

    public string? ElevationCode { get; set; }

    public string? ElevationDesc { get; set; }

    public int? GarageOrientationId { get; set; }

    public string? JobAddress1 { get; set; }

    public string? JobAddress2 { get; set; }

    public string? JobCity { get; set; }

    public string? JobCounty { get; set; }

    public string? JobDesc { get; set; }

    public string? JobPostingGroup { get; set; }

    public string? JobState { get; set; }

    public string? JobZipCode { get; set; }

    public int? LotAvailability { get; set; }

    public decimal? LotCost { get; set; }

    public string? LotNumber { get; set; }

    public decimal? LotPremium { get; set; }

    public string? LotSectionCode { get; set; }

    public int? LotStatus { get; set; }

    public string? LotSwing { get; set; }

    public string? LotUnit { get; set; }

    public string? LotWidth { get; set; }

    public string? ModelName { get; set; }

    public DateTime? OverLot { get; set; }

    public string? Phase { get; set; }

    public string? PlanCode { get; set; }

    public string? PlanName { get; set; }

    public string? PossessionStatus { get; set; }

    public DateTime? ProjectedTakeDownDate { get; set; }

    public string? Restrictions { get; set; }

    public DateTime? StatsStickStartDate { get; set; }

    public string? SubdivisionClass { get; set; }

    public DateTime? TakedownDate { get; set; }

    public string? TakedownType { get; set; }

    public int? SubdivisionId { get; set; }

    public string? Supervisor { get; set; }

    public string? ProjectMgr { get; set; }

    public string? SalesContact { get; set; }

    public string? FieldSuper { get; set; }

    public string? UserContact1 { get; set; }

    public string? UserContact2 { get; set; }

    public DateTime? CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdateDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public bool? IsModel { get; set; }

    public bool? IsListed { get; set; }

    public string? BuildingNum { get; set; }

    public string? Notes { get; set; }

    public int? CustomerId { get; set; }

    public int? LotStatusId { get; set; }

    public string? StickBuilingNum { get; set; }
    public decimal? LotSize { get; set; }
    public DateTime? BcSettlementDate { get; set; }
    public bool? Blocked { get; set; }
    public bool BoolBlocked { get; set; }
    public bool? LinkWithErp { get; set; }
    public DateTime? EstimatedCompletionDate { get; set; }
    public string? EstimatedCompletionSource { get; set; }
    public int? JobConstructionTypeId { get; set; }//factory or field built

    //public virtual ICollection<JobContact> JobContacts { get; set; } = new List<JobContact>();

    public string? Closed { get; set; }//Used by BC integration to support open or closed in BC
    public JobConstructionTypeDto? JobConstructionType { get; set; }
    public SubdivisionDto? Subdivision { get; set; }
    public CustomerDto? Customer { get; set; }
    public SalesconfigDto? SalesConfig { get; set; }
    public LotStatusDto? LotStatusNavigation { get; set; }
    public GarageOrientationDto? GarageOrientation { get; set; }
    public ScheduleDto? JobSchedule { get; set; }
    public bool? HomeOrientationPerPlan { get; set; }
    public bool BoolHomeOrientationPerPlan { get; set; }

    public string? ParkingNum { get; set; }

    public string? StorageNum { get; set; }
    public string? GarageNum { get; set; }
    public string? EntityNum { get; set; }
    public bool? IsSs { get; set; }
    public bool BoolIsSs { get; set; }
    public DateTime? ProjectedLotTakedown { get; set; }

    public DateTime? ProjectedSalesReleaseDate { get; set; }

    public decimal? GeneralOptionBudget { get; set; }

    public decimal? HomesiteOptionSpendBudget { get; set; }
    public string? JobAndLotNumString { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<JobDto, Job>().ReverseMap();
    }

    public JobDto ShallowCopy()
    {
        return (JobDto)MemberwiseClone();
    }
}
