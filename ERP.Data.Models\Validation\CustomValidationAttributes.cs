﻿using ERP.Data.Models.Dto;
using System.ComponentModel.DataAnnotations;

namespace ERP.Data.Models.Validation
{
    public class CustomEmailValidation : ValidationAttribute
    {
        //EmailAddressAttribute makes the email required - ie it returns false if it's empty. So we need this custom attribute, which returns true for empty, otherwise it uses EmailAddressAttribute
        //Note emailaddress attribute accepts things that are clearly wrong, like "wrong@wrong", really probably should use regex
        public override bool IsValid(object? value)
        {
            if (value == null) return true;
            string? input = value as string;
            if (input == string.Empty) return true;
            else
            {
                var emailAttribute = new EmailAddressAttribute();
                return emailAttribute.IsValid(input);
            }
        }
    }

    public class CustomPhoneValidation : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value == null) return true;
            string? input = value as string;
            if (input == string.Empty) return true;
            else
            {
                var phoneAttribute = new PhoneAttribute();
                return phoneAttribute.IsValid(input);
            }
        }
    }

    public class DateInFutureValidation : ValidationAttribute
    {
        protected override ValidationResult IsValid(object? value, ValidationContext validationContext)
        {            
            if(value != null)
            {
                value = (DateTime)value;
                var contextImportModel = validationContext.ObjectInstance as ImportCostModel;
                var contextCostModel = validationContext.ObjectInstance as CostModel;
                if (contextImportModel != null && (contextImportModel.CostEditType == CostEditType.Next || contextImportModel.CostEditType == CostEditType.Future))
                {
                    //if cost import model, if cost edit type is next, date must be in future
                    //TODO: actually, is this the correct thing to do? it actually needs to compare with the previous costs effective date, not necessarily current date
                    //maybe ask julie do they ever need to pick a next cost date that is actually past 
                    if (DateTime.Now.Date.CompareTo(value) <= 0)
                    {
                        return ValidationResult.Success;
                    }
                    else
                    {
                        return new ValidationResult("Cost Effective Date must be in the future");
                    }
                }
                else if (contextCostModel != null && (contextCostModel.EditType == CostEditType.Next || contextCostModel.EditType == CostEditType.Future))
                {
                    //if cost model, if cost edit type is next, date must be in future
                    //TODO: actually, is this the correct thing to do? it actually needs to compare with the previous costs effective date, not necessarily current date
                    //maybe ask julie do they ever need to pick a next cost date that is actually past 
                    if (DateTime.Now.Date.CompareTo(value) <= 0)
                    {
                        return ValidationResult.Success;
                    }
                    else
                    {
                        return new ValidationResult("Cost Effective Date must be in the future");
                    }
                }
            }

            return ValidationResult.Success;
        }
    }
}
