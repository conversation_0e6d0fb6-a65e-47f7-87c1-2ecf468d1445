﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ERP.Data.Models.Dto;

public class AssignTradeSupplierModel
{
    public int TradeId { get; set; }
    public int? OldTradeId { get; set; }
    public int? SubdivisionId { get; set; }
    public string? SubdivisionName { get; set; }
    public string? DefaultSupplier { get; set; }
    public bool IsDefault { get; set; }
    public string? AreaName { get; set; }
    public string? TradeName { get; set; }
    public string? TradeDesc { get; set; }
    public int SubNumber { get; set; }


}
