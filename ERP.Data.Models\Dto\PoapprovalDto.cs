﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace ERP.Data.Models;

public class PoapprovalDto : IMapFrom<Poapproval>
{
    public int PoapprovalId { get; set; }

    public int PoheaderId { get; set; }

    public int? Approvalseq { get; set; }

    public string? Invnumber { get; set; }

    public string? Invdescription { get; set; }

    public double? Invnetamount { get; set; }

    public double? Invtaxamount { get; set; }

    public double? Invretention { get; set; }

    public DateTime? Invdate { get; set; }

    public string? Approvedby { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    //public byte[]? RecordTimeStamp { get; set; }

    public PoheaderDto? Poheader { get; set; }
    
    public DateTime? ApprovedDate { get; set; }

    public string? Notes { get; set; }

    public int? VpoApprovalId { get; set; }

    public string? SuccessMessage { get; set; }

    public string? ErrorMessage { get; set; }

    public int? RoleId { get; set; }
    public string? EmpUserId { get; set; }

    public string? PoJobNumber { get; set; }

    public bool ShowApproveButton { get; set; } = false;

    public int? JobContactId { get; set; }

    public string? NextLevelApprover { get; set; }
    
    public Guid? BcId { get; set; }
    
    public string? PoStatus { get; set; }
    public bool? ExportBc { get; set; }
    public string? PurchasingActivityName { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<PoapprovalDto, Poapproval>().ReverseMap();
    }
    public string? Vendor { get; set; }

    [JsonIgnore]
    public string? SubdivisionName => Poheader?.SubdivisionName;

    [JsonIgnore]
    public string? Pojobnumber => Poheader?.Pojobnumber;

    public List<PoAttachmentDto> PoAttachments { get; set; } = new List<PoAttachmentDto>();
}
