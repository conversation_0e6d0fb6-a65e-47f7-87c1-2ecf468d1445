﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ERP.Data.Models.Validation;
namespace ERP.Data.Models.Dto;

public class JobContactModel
{


    [StringLength(30, ErrorMessage = "First Name must be 30 characters or less")]
    public string? FirstName { get; set; }

    [StringLength(30, ErrorMessage = "Last Name must be 30 characters or less")]
    public string? LastName { get; set; }

    public string? Address1 { get; set; }

    public string? Address2 { get; set; }

    public string? City { get; set; }

    public string? State { get; set; }

    public string? Postcode { get; set; }
    [CustomPhoneValidation]
    public string? WorkPhone { get; set; }

    [CustomPhoneValidation]
    public string? HomePhone { get; set; }
    [CustomPhoneValidation]
    public string? MobilePhone { get; set; }

    public string? Fax { get; set; }

    [CustomEmailValidation]
    public string? Email { get; set; }

    public string? Country { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }


    public int JobContactId { get; set; }

    public string? JobNumber { get; set; }

    [Required]
    public string? UserId { get; set; }

    public string? DirectorOfConstruction { get; set; }
    public bool IsDirectorOfConstruction { get; set; }
    public string? ConstructionManager { get; set; }
    public bool IsConstructionManager { get; set; }
    public string? AssistantConstructionManager { get; set; }
    public bool IsAssistantConstructionManager { get; set; }
    public string? SeniorConstructionManager { get; set; }
    public bool IsSeniorConstructionManager { get; set; }
    public string? AreaProductionManager { get; set; }
    public bool IsAreaProductionManager { get; set; }
    public string? TypesString { get; set; }//To hold list of types eg "Construction Manager, Area Production Manager"
    public string? Other { get; set; }
    public bool IsOther { get; set; }
    public int? VpoApprovalLevel { get; set; }

    public string? ProtectFromMaster { get; set; }


}
