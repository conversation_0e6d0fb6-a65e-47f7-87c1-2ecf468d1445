﻿@inject ScheduleService ScheduleService

<TelerikLoaderContainer Visible="@IsLoading" Text="Please wait..." />
<TelerikWindow Modal="true"
@bind-Visible="@IsModalVisible"
Width="800px"
Height="500px"
CloseOnOverlayClick="true">
    <WindowTitle>
        Update Default Supplier For Open Schedule Activities
    </WindowTitle>
    <WindowContent>
        <TelerikGrid Data=@OpenScheduleSactivitiesData
                     SelectionMode="@GridSelectionMode.Multiple"
                     @bind-SelectedItems="@SelectedScheduleSactivities"
                     FilterMode="@GridFilterMode.FilterMenu"
                     FilterMenuType="@FilterMenuType.CheckBoxList">
            <GridColumns>
                <GridCheckboxColumn SelectAll="true" CheckBoxOnlySelection="false" />
                <GridColumn Field="Sactivity.ActivityName" Title="Activity Name" />
                <GridColumn Field="SubNumberNavigation.SubName" Title="Supplier Name" />
                <GridColumn Field="Schedule.JobNumber" Title="Job Number" />
            </GridColumns>
            <GridToolBarTemplate>
                @{
                    <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    if (DefaultSupplier != null)
                    {
                        <b>Default Supplier: @DefaultSupplier.SubName</b>
                    }
                    else
                    {
                        <b>No Default Supplier for Selected Trade and Subdivision</b>
                    }
                }
            </GridToolBarTemplate>
        </TelerikGrid>
        <br />
        <TelerikButton Class="btn btn-primary" ButtonType="ButtonType.Submit" OnClick="HandleValidUpdateSubmit" Enabled="@IsUpdateEnabled" Icon="FontIcon.Save">Update</TelerikButton>
        <TelerikButton Class="btn btn-secondary" ButtonType="ButtonType.Button" OnClick="Cancel" Icon="FontIcon.Cancel">Cancel</TelerikButton>

        <div style=@submittingStyle>Updating. Please wait...</div>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {
    [Parameter]
    public EventCallback<ResponseModel<List<ScheduleSactivityDto>>> HandleUpdateSubmit { get; set; }
    [Parameter]
    public int SelectedTradeId { get; set; }
    [Parameter]
    public int SelectedSubdivisionId { get; set; }
    [Parameter]
    public TradeSupplierModel? DefaultSupplier { get; set; }

    public bool IsModalVisible { get; set; }
    public List<ScheduleSactivityDto>? OpenScheduleSactivitiesData { get; set; }
    public IEnumerable<ScheduleSactivityDto> SelectedScheduleSactivities { get; set; } = Enumerable.Empty<ScheduleSactivityDto>();
    public bool IsUpdateEnabled => SelectedScheduleSactivities.Count() > 0 && DefaultSupplier != null;
    private string submittingStyle = "display:none";
    public bool IsLoading { get; set; } = false;

    public async Task Show()
    {
        IsLoading = true;
        IsModalVisible = true;
        SelectedScheduleSactivities = Enumerable.Empty<ScheduleSactivityDto>();
        OpenScheduleSactivitiesData = (await ScheduleService.GetOpenScheduleActivitiesForTradeSupplierAsync(SelectedTradeId, SelectedSubdivisionId)).Value;

        IsLoading = false;
        StateHasChanged();
    }

    private async void HandleValidUpdateSubmit()
    {
        submittingStyle = "";
        if(DefaultSupplier != null)
        {
            SelectedScheduleSactivities.ToList().ForEach(x => x.SubNumber = DefaultSupplier.SubNumber);
            var udpateResponse = await ScheduleService.UpdateScheduleSactivitysAsync(SelectedScheduleSactivities.ToList());
            await HandleUpdateSubmit.InvokeAsync(udpateResponse);
        }
        else
        {
            await HandleUpdateSubmit.InvokeAsync(new ResponseModel<List<ScheduleSactivityDto>>() { IsSuccess = false, Message = "No Default Supplier"});
        }
        SelectedScheduleSactivities = Enumerable.Empty<ScheduleSactivityDto>();
        submittingStyle = "display:none";
    }

    public void Hide()
    {
        SelectedScheduleSactivities = Enumerable.Empty<ScheduleSactivityDto>();
        OpenScheduleSactivitiesData = null;
        IsModalVisible = false;
    }

    void Cancel()
    {
        SelectedScheduleSactivities = Enumerable.Empty<ScheduleSactivityDto>();
        OpenScheduleSactivitiesData = null;
        IsModalVisible = false;
    }
}