﻿@inject ScheduleService ScheduleService

<TelerikWindow Modal="true"
@bind-Visible="@IsModalVisible"
               Width="300px"
               Height="300px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Variance Code
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@VarianceCodeToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <style>
                .validated{
                    border-color:red;
                }
            </style>
            @if (validation)
            {
                <div class="mb-3">
                    <label class="form-label">Variance Code</label><br />
                    <TelerikTextBox Class="validated" @bind-Value="@VarianceCodeToAdd.VarianceCode" MaxLength="4"></TelerikTextBox>
                    <label style="color:red">Variance Code cannot be empty</label>
                </div>
            }
            else
            {
                <div class="mb-3">
                    <label class="form-label">Variance Code</label><br />
                    <TelerikTextBox @bind-Value="@VarianceCodeToAdd.VarianceCode" MaxLength="4"></TelerikTextBox>
                </div>
            }
            <div class="mb-3">
                <label class="form-label">Code Description</label><br />
                <TelerikTextBox @bind-Value="@VarianceCodeToAdd.VarianceDesc"></TelerikTextBox>
            </div>
            <button type="submit" class="btn btn-primary">Add</button>
            <button type="button" @onclick="CancelAddItem" class="btn btn-secondary">Cancel</button>
            <div style=@submittingStyle>Adding item. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public VarianceDto VarianceCodeToAdd { get; set; } = new VarianceDto();
    public bool validation { get; set; } = false;
    private string submittingStyle = "display:none";

    [Parameter]
    public EventCallback<ResponseModel<VarianceDto>> HandleAddSubmit { get; set; }

    public async Task Show()
    {
        IsModalVisible = true;
        VarianceCodeToAdd.VarianceCode = "";
        VarianceCodeToAdd.VarianceDesc= "";
        StateHasChanged();
    }

    private async void HandleValidAddSubmit()
    {
        if (VarianceCodeToAdd.VarianceCode == "")
        {
            validation = true;
        }
        else
        {
            submittingStyle = "";
            var responseItem = await ScheduleService.AddVarianceCodeAsync(VarianceCodeToAdd);
            submittingStyle = "display:none";
            await HandleAddSubmit.InvokeAsync(responseItem);
        }
    }
    async void CancelAddItem()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {

        IsModalVisible = false;
    }
}

