﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class BCItem
    {
       // public string id { get; set; }
        public string number { get; set; }
        public string displayName { get; set; }
        //public string type { get; set; }
        //public string itemCategoryId { get; set; }
        //public string itemCategoryCode { get; set; }
        //public bool blocked { get; set; }
        //public string gtin { get; set; }
        //public int inventory { get; set; }
        //public float unitPrice { get; set; }
        //public bool priceIncludesTax { get; set; }
        //public float unitCost { get; set; }
        //public string taxGroupId { get; set; }
        //public string taxGroupCode { get; set; }
        //public string baseUnitOfMeasureId { get; set; }
        public string baseUnitOfMeasureCode { get; set; }
        //public DateTime lastModifiedDateTime { get; set; }
    }


    public class BCResponseItem
    {
        public string id { get; set; }
        public string number { get; set; }
        public string displayName { get; set; }
        public string type { get; set; }
        public string itemCategoryId { get; set; }
        public string itemCategoryCode { get; set; }
        public bool blocked { get; set; }
        public string gtin { get; set; }
        public int inventory { get; set; }
        public float unitPrice { get; set; }
        public bool priceIncludesTax { get; set; }
        public float unitCost { get; set; }
        public string taxGroupId { get; set; }
        public string taxGroupCode { get; set; }
        public string baseUnitOfMeasureId { get; set; }
        public string baseUnitOfMeasureCode { get; set; }
        public DateTime lastModifiedDateTime { get; set; }
    }

    public class BCResponseItems
    {
        public string odatacontext { get; set; }
        public BCCustomerResponseItem[] value { get; set; }
    }

    public class BCCustomerResponseItem
    {
        public string odataetag { get; set; }
        public string id { get; set; }
        public string number { get; set; }
        public string displayName { get; set; }
        public string displayName2 { get; set; }
        public string type { get; set; }
        public string itemCategoryId { get; set; }
        public string itemCategoryCode { get; set; }
        public bool blocked { get; set; }
        public string gtin { get; set; }
        public int inventory { get; set; }
        public float unitPrice { get; set; }
        public bool priceIncludesTax { get; set; }
        public int unitCost { get; set; }
        public string taxGroupId { get; set; }
        public string taxGroupCode { get; set; }
        public string baseUnitOfMeasureId { get; set; }
        public string baseUnitOfMeasureCode { get; set; }
        public string generalProductPostingGroupId { get; set; }
        public string generalProductPostingGroupCode { get; set; }
        public string inventoryPostingGroupId { get; set; }
        public string inventoryPostingGroupCode { get; set; }
        public DateTime lastModifiedDateTime { get; set; }
    }

}
