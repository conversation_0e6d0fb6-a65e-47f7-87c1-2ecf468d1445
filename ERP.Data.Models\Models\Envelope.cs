﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Envelope
{
    public int EnvelopeId { get; set; }

    public int ContractId { get; set; }

    public int DocuSignSignatureTemplateId { get; set; }

    public int? SenderId { get; set; }

    public int? VoiderId { get; set; }

    public DateTime? SentDate { get; set; }

    public DateTime? VoidedDate { get; set; }

    public DateTime? SignedDate { get; set; }

    public string Name { get; set; } = null!;

    public string? DocuSignEnvelopeId { get; set; }

    public virtual Contract Contract { get; set; } = null!;

    public virtual DocuSignSignatureTemplate DocuSignSignatureTemplate { get; set; } = null!;

    public virtual Admin? Sender { get; set; }

    public virtual Admin? Voider { get; set; }
}
