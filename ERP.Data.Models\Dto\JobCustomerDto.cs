﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;
using ERP.Data.Models.Dto;
namespace ERP.Data.Models;

public  class JobCustomerDto : IMapFrom<JobCustomer>
{
    public int JobCustomerId { get; set; }

    public string? JobNumber { get; set; } 

    public int CustomerId { get; set; }

    public bool? Cancelled { get; set; }

    public bool? Transfer { get; set; }

    public string? TransfferedTo { get; set; }

    public bool? IsActive { get; set; }

    public DateTime Createddatetime { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? Createdby { get; set; }

    public string? Updatedby { get; set; }

    public  CustomerDto? Customer { get; set; } 

    public JobDto? JobNumberNavigation { get; set; } 
}
