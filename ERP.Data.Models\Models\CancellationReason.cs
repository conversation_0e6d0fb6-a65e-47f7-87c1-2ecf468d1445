﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class CancellationReason
{
    public int CancellationReasonId { get; set; }

    public short ListOrder { get; set; }

    public bool IsActive { get; set; }

    public string Description { get; set; } = null!;

    public virtual ICollection<Cancellation> Cancellations { get; set; } = new List<Cancellation>();
}
