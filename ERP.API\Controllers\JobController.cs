﻿
using AutoMapper;
using ERP.API.Utilities;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class JobController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private readonly Email _email;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        private readonly IWebHostEnvironment _env;

        public JobController(IConfiguration configuration, ErpDevContext context, IMapper mapper, IWebHostEnvironment env)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _email = new Email(configuration);
            _env = env;
        }

        /// <summary>
        /// update from bc
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> JobAsync([FromBody] BCJobModel model)
        {
            try
            {
                string baseRequestURL = _env.IsDevelopment() ? "https://vmerpservices-dev.azurewebsites.net/api/" : "https://vmerpservices.azurewebsites.net/api/";
                //Only filling/updating fields from BC
                //BC has subdivision, but it won't have a subdivisionId, so look up by subdivision num
                var findSubdivision = _context.Subdivisions.FirstOrDefault(x => x.SubdivisionNum == model.SubdivisionNum);
                if (findSubdivision == null) {
                    return BadRequest(new ResponseModel<BCJobModel> { IsSuccess = false, Message = $"Failed to add or update Job. Could not find subdivision {model.SubdivisionNum}", Value = null });
                }
                var findJob = await _context.Jobs.SingleOrDefaultAsync(x => x.JobNumber == model.JobNumber);
                if (findJob != null)
                {
                    findJob.SubdivisionId = findSubdivision.SubdivisionId;
                    //findJob.JobAddress1 = model.JobAddress1 ?? findJob.JobAddress1;//NOTE: 12/13/24, change to address fields update in ERP, not BC or NAV
                    //findJob.JobAddress2 = model.JobAddress2 ?? findJob.JobAddress2;
                    //findJob.JobCity = model.JobCity ?? findJob.JobCity;
                    //findJob.JobState = model.JobState ?? findJob.JobState;
                    //findJob.JobZipCode = model.JobZipCode ?? findJob.JobZipCode;
                    //findJob.JobCounty = updateJob.JobCounty ?? findJob.JobCounty;
                    findJob.JobDesc = model.JobDesc ?? findJob.JobDesc;
                    findJob.JobPostingGroup = model.JobPostingGroup ?? findJob.JobPostingGroup;
                    findJob.EntityNum = model.EntityNum;
                    findJob.Blocked = model?.Blocked ?? findJob.Blocked;
                    findJob.LinkWithErp = model.LinkWithErp ?? findJob.LinkWithErp;
                    findJob.IsActive =  model.LinkWithErp == false ? false : true;//deactivate if link with erp is false                   
                    findJob.UpdateDateTime = DateTime.Now;
                    findJob.UpdatedBy = model.UpdatedBy;
                    _context.Jobs.Update(findJob);
                    _context.SaveChanges();
                 
                    var returnJob = _mapper.Map<BCJobModel>(findJob);
                    returnJob.SubdivisionNum = model.SubdivisionNum;

                    //log to bc api log
                    var jsonItem = JsonConvert.SerializeObject(model);
                    var newLog = new ErpBcApiLog()
                    {
                        Method = "POST",
                        Type = "Job",
                        RequestUrl = $"{baseRequestURL}job/job",
                        RequestBody = jsonItem,
                        ResponseBody = JsonConvert.SerializeObject(returnJob),
                        ResponseCode = "200",
                        CreatedBy = model.CreatedBy,
                        CreatedDateTime = DateTime.Now,
                    };
                    _context.ErpBcApiLogs.Add(newLog);
                    await _context.SaveChangesAsync();
                    return new OkObjectResult(new ResponseModel<BCJobModel> { Value = returnJob, IsSuccess = true, Message = "Updated Job successfully" });
                }
                else
                {
                    var createJob = new Job()
                    {
                        JobNumber = model.JobNumber,
                        SubdivisionId = findSubdivision.SubdivisionId,
                        JobDesc = model.JobDesc,
                        EntityNum = model.EntityNum,
                        JobPostingGroup = model.JobPostingGroup,
                        //JobAddress1 = model.JobAddress1,//NOTE as of 12/13.24, this is to update in ERP
                        //JobAddress2 = model.JobAddress2,
                        //JobCity = model.JobCity,
                        //JobState = model.JobState,
                        ////JobCounty = model.JobCounty,
                        //JobZipCode = model.JobZipCode,
                        Blocked = model.Blocked,
                        LinkWithErp = model.LinkWithErp,
                        IsActive = model.LinkWithErp == false ? false : true,//deactivate if link with erp is false,
                        CreatedBy = model.CreatedBy,
                    };
                    _context.Jobs.Add(createJob);
                    await _context.SaveChangesAsync();

                    var returnJob = _mapper.Map<BCJobModel>(createJob);
                    returnJob.SubdivisionNum = model.SubdivisionNum;

                    //log to bc api log
                    var jsonItem = JsonConvert.SerializeObject(model);
                    var newLog = new ErpBcApiLog()
                    {
                        Method = "POST",
                        Type = "Job",
                        RequestUrl = $"{baseRequestURL}job/job",
                        RequestBody = jsonItem,
                        ResponseBody = JsonConvert.SerializeObject(returnJob),
                        ResponseCode = "200",
                        CreatedBy = model.CreatedBy,
                        CreatedDateTime = DateTime.Now,
                    };
                    _context.ErpBcApiLogs.Add(newLog);
                    await _context.SaveChangesAsync();
                    return new OkObjectResult(new ResponseModel<BCJobModel> { Value = model, IsSuccess = true, Message = "Created Job successfully" });
                }
                
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif

                //log to bc api log
                string baseRequestURL = _env.IsDevelopment() ? "https://vmerpservices-dev.azurewebsites.net/api/" : "https://vmerpservices.azurewebsites.net/api/";
                var jsonItem = JsonConvert.SerializeObject(model);
                var newLog = new ErpBcApiLog()
                {
                    Method = "POST",
                    Type = "Job",
                    RequestUrl = $"{baseRequestURL}job/job",
                    RequestBody = jsonItem,
                    ResponseBody = $"Failed to add or update Job. Error: {ex.Message} {ex.InnerException?.Message}",
                    ResponseCode = "500",
                    CreatedBy = model.CreatedBy,
                    CreatedDateTime = DateTime.Now,
                };
                _context.ErpBcApiLogs.Add(newLog);
                await _context.SaveChangesAsync();
                return StatusCode(500, new ResponseModel<BCJobModel> { IsSuccess = false, Message = $"Failed to add or update Job. Error: {ex.Message} {ex.InnerException?.Message}", Value = null, Error = $"{ex.Message} {ex.InnerException?.Message}" });
            }
        }

        /// <summary>
        /// update from bc -- not used BC calls the above one by one method
        /// </summary>
        /// <param name="updateJob"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> JobsAsync([FromBody] List<JobDto> updateJobs)
        {
            try
            {
                var jobNums = updateJobs.Select(x => x.JobNumber).ToList();
                var existingJobs = _context.Jobs.Where(x => jobNums.Contains(x.JobNumber)).ToList();
                var existingJobNums = existingJobs.Select(x => x.JobNumber).ToList();

                var jobsToUpdate = (from a in updateJobs
                                   join b in existingJobs on a.JobNumber equals b.JobNumber
                                   join c in _context.Subdivisions.Where(x => x.IsActive == true) on a.SubdivisionNum equals c.SubdivisionNum
                                    select new Job()
                                    {
                                        JobNumber = a.JobNumber,
                                        JobAddress1 = a.JobAddress1 ?? b.JobAddress1,
                                        JobAddress2 = a.JobAddress2 ?? b.JobAddress2,
                                        JobCity = a.JobCity ?? b.JobCity,
                                        JobState = a.JobState ?? b.JobState,
                                        JobZipCode = a.JobZipCode ?? b.JobZipCode,
                                        JobCounty = a.JobCounty ?? b.JobCounty,
                                        JobDesc = a.JobDesc ?? b.JobDesc,
                                        JobPostingGroup = a.JobPostingGroup ?? b.JobPostingGroup,//BC integration doesn't seem to send this
                                        LotAvailability = a.LotAvailability ?? b.LotAvailability,
                                        LotStatus = a.LotStatus ?? b.LotStatus,
                                        LotCost = a.LotCost ?? b.LotCost,
                                        LotNumber = a.LotNumber ?? b.LotNumber,
                                        LotPremium = a.LotPremium ?? b.LotPremium,
                                        LotSectionCode = a.LotSectionCode ?? b.LotSectionCode,
                                        LotSwing = a.LotSwing ?? b.LotSwing,
                                        LotUnit = a.LotUnit ?? b.LotUnit,
                                        LotWidth = a.LotWidth ?? b.LotWidth,
                                        ModelName = a.ModelName ?? b.ModelName,
                                        OverLot = a.OverLot ?? b.OverLot,
                                        Phase = a.Phase ?? b.Phase,
                                        PlanCode = a.PlanCode ?? b.PlanCode,
                                        PlanName = a.PlanName ?? b.PlanName,
                                        PossessionStatus = a.PossessionStatus ?? b.PossessionStatus,
                                        ProjectedTakeDownDate = a.ProjectedTakeDownDate ?? b.ProjectedTakeDownDate,
                                        Restrictions = a.Restrictions ?? b.Restrictions,
                                        StatsStickStartDate = a.StatsStickStartDate ?? b.StatsStickStartDate,
                                        SubdivisionClass = a.SubdivisionClass ?? b.SubdivisionClass,
                                        TakedownDate = a?.TakedownDate ?? b.TakedownDate,
                                        TakedownType = a?.TakedownType ?? b.TakedownType,
                                        SubdivisionId = c.SubdivisionId,
                                        Supervisor = a?.Supervisor ?? b.Supervisor,
                                        ProjectMgr = a?.ProjectMgr ?? b.ProjectMgr,
                                        SalesContact = a?.SalesContact ?? b.SalesContact,
                                        FieldSuper = a?.FieldSuper ?? b.FieldSuper,
                                        BuildingNum = a?.BuildingNum ?? b.BuildingNum,
                                        //findJob.CustomerId = updateJob?.CustomerId ?? findJob.CustomerId;//shouldn't come from BC
                                        Blocked = a?.Blocked ?? b.Blocked,
                                        LotSize = a.LotSize ?? b.LotSize,
                                        BcSettlementDate = a.BcSettlementDate ?? b.BcSettlementDate,
                                        LinkWithErp = a.LinkWithErp ?? b.LinkWithErp,
                                        IsActive = a.LinkWithErp == false ? false : true,//deactivate if link with erp is false
                                        JobConstructionTypeId = a.JobConstructionTypeId ?? b.JobConstructionTypeId,
                                        EstimatedCompletionDate = a.EstimatedCompletionDate ?? b.EstimatedCompletionDate,
                                        EstimatedCompletionSource = a.EstimatedCompletionSource ?? b.EstimatedCompletionSource,
                                        EntityNum = a?.EntityNum ?? b.EntityNum,
                                        Closed = a?.LotStatus == null ? b.Closed : a.LotStatus == 6 ? "T" : "F",//TODO: what status Id gets sent for closed
                                        UpdateDateTime = DateTime.Now,
                                        CreatedDateTime = b.CreatedDateTime,
                                        CreatedBy = b.CreatedBy,
                                        CustomerId = b.CustomerId,
                                       // LotStatusId = b.LotStatusId,
                                        UpdatedBy = a.UpdatedBy//NOTE: BC integration doesn't seem to be passing this. 
                                    }).ToList();
                await _context.Jobs.BulkUpdateAsync(jobsToUpdate);

                var returnJobs = _mapper.Map<List<JobDto>>(jobsToUpdate);

                var newJobs = updateJobs.Where(x => !existingJobNums.Contains(x.JobNumber)).ToList();
                var newJobsToInsert = (from a in newJobs
                                      join b in _context.Subdivisions.Where(x => x.IsActive == true) on a.SubdivisionNum equals b.SubdivisionNum
                                      select new Job()
                                      {
                                          JobNumber = a.JobNumber,
                                          AcquisitionLotCost = a.AcquisitionLotCost,
                                          ApprovedDepositRequirement = a.ApprovedDepositRequirement,
                                          ElevationCode = a.ElevationCode,
                                          ElevationDesc = a.ElevationDesc,
                                          // findJob.GarageOrientationId = updateJob.GarageOrientationId ?? findJob.GarageOrientationId;//shouldn't be coming from NAV
                                          JobAddress1 = a.JobAddress1,
                                          JobAddress2 = a.JobAddress2,
                                          JobCity = a.JobCity,
                                          JobState = a.JobState,
                                          JobZipCode = a.JobZipCode,
                                          JobCounty = a.JobCounty,
                                          JobDesc = a.JobDesc,
                                          JobPostingGroup = a.JobPostingGroup,//BC integration doesn't seem to send this
                                          LotAvailability = a.LotAvailability,
                                          LotStatus = a.LotStatus,
                                          LotCost = a.LotCost,
                                          LotNumber = a.LotNumber,
                                          LotPremium = a.LotPremium,
                                          LotSectionCode = a.LotSectionCode,
                                          LotSwing = a.LotSwing,
                                          LotUnit = a.LotUnit,
                                          LotWidth = a.LotWidth,
                                          ModelName = a.ModelName,
                                          OverLot = a.OverLot,
                                          Phase = a.Phase,
                                          PlanCode = a.PlanCode,
                                          PlanName = a.PlanName,
                                          PossessionStatus = a.PossessionStatus,
                                          ProjectedTakeDownDate = a.ProjectedTakeDownDate,
                                          Restrictions = a.Restrictions,
                                          StatsStickStartDate = a.StatsStickStartDate,
                                          SubdivisionClass = a.SubdivisionClass,
                                          TakedownDate = a?.TakedownDate,
                                          TakedownType = a?.TakedownType,
                                          SubdivisionId = b.SubdivisionId,
                                          Supervisor = a?.Supervisor,
                                          ProjectMgr = a?.ProjectMgr,
                                          SalesContact = a?.SalesContact,
                                          FieldSuper = a?.FieldSuper,
                                          BuildingNum = a?.BuildingNum,
                                          //findJob.CustomerId = updateJob?.CustomerId ?? findJob.CustomerId;//shouldn't come from BC
                                          Blocked = a?.Blocked,
                                          LotSize = a.LotSize,
                                          BcSettlementDate = a.BcSettlementDate,
                                          LinkWithErp = a.LinkWithErp,
                                          IsActive = a.LinkWithErp == false ? false : true,//deactivate if link with erp is false
                                          JobConstructionTypeId = a.JobConstructionTypeId,
                                          EstimatedCompletionDate = a.EstimatedCompletionDate,
                                          EstimatedCompletionSource = a.EstimatedCompletionSource,
                                          EntityNum = a?.EntityNum,
                                          Closed = a?.LotStatus == null ? null : a.LotStatus == 6 ? "T" : "F",//TODO: what status Id gets sent for closed
                                          UpdateDateTime = DateTime.Now,
                                          CreatedBy = a.UpdatedBy//NOTE: BC integration doesn't seem to be passing this. 
                                      }).ToList();
                await _context.Jobs.BulkInsertAsync(newJobsToInsert);
                //note that any with subdivision num not found won't be insert

                var returnAddJobs = _mapper.Map<List<JobDto>>(jobsToUpdate);
                returnJobs.AddRange(returnAddJobs);

                return new OkObjectResult(new ResponseModel<List<JobDto>> { Value = returnJobs, IsSuccess = true, Message = "Created/Updated Jobs successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobDto>> { IsSuccess = false, Message = $"Failed to add or update Job. Error: {ex.Message} {ex.InnerException.Message}", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> JobStatusAsync()
        {
            try
            {
                var getStatuses = _context.LotStatuses.AsNoTracking().Where(x => x.IsActive == true).ToList();
                var resturnStatuses = _mapper.Map<List<LotStatusDto>>(getStatuses);
                return Ok(new ResponseModel<List<LotStatusDto>>(){ Value = resturnStatuses, Message = "Got statuses", IsSuccess = true });
            }
            catch(Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<LotStatusDto>> { IsSuccess = false, Message = "Failed to get job status list", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> JobPostingGroupsAsync()
        {
            try
            {
                var getJobPostingGroups = _context.JobPostingGroups.AsNoTracking().Where(x => x.IsActive == true).ToList();
                var jobPostingGroups = _mapper.Map<List<JobPostingGroupDto>>(getJobPostingGroups);
                return Ok(new ResponseModel<List<JobPostingGroupDto>>() { Value = jobPostingGroups, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobPostingGroupDto>> { IsSuccess = false, Message = "Failed to get job posting groups", Value = null });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GarageOrientationsAsync()
        {
            try
            {
                var getOrientations = _context.GarageOrientations.AsNoTracking().Where(x => x.IsActive == true).ToList();
                var returnOrientations = _mapper.Map<List<GarageOrientationDto>>(getOrientations);
                return Ok(new ResponseModel<List<GarageOrientationDto>>() { Value = returnOrientations, Message = "Got orientations", IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<GarageOrientationDto>> { IsSuccess = false, Message = "Failed to get job orientations list", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> JobConstructionTypesAsync()
        {
            try
            {
                var getTypes = _context.JobConstructionTypes.AsNoTracking().Where(x => x.IsActive == true).ToList();
                var returnTypes = _mapper.Map<List<JobConstructionTypeDto>>(getTypes);
                return Ok(new ResponseModel<List<JobConstructionTypeDto>>() { Value = returnTypes, Message = "Got construction types", IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobConstructionTypeDto>> { IsSuccess = false, Message = "Failed to get job construction type list", Value = null });
            }
        }
    }
}
