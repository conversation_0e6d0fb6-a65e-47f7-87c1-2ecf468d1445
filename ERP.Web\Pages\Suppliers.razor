﻿@page "/suppliers"
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector, ConstructionManager")]
@inject TradeService TradeService
@inject ItemService ItemService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
<style>
    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }

    .k-window {
        width: 350px;
    }


</style>

<PageTitle>Suppliers</PageTitle>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<div class="col-lg-12">
    <div class="card" style="background-color:#2e5771">
        <div class="card-body" style="padding:0.5rem">
            <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Suppliers</h7>
        </div>
    </div>
</div>

<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="/">Home</a></li>
    <li class="breadcrumb-item active">Suppliers</li>
</ol>

@if (suppliers == null)
{
    <p><em>Loading...</em></p>
    <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />
}

else
{
    <TelerikGrid Data=@suppliers
             ConfirmDelete="true" 
             ScrollMode="GridScrollMode.Virtual"
             FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
             Height="1000px" RowHeight="60" PageSize="20"
             Sortable="true"
             Resizable="true"
             EditMode="@GridEditMode.Popup"
             OnUpdate="@UpdateSupplierHandler"
             OnEdit="@EditSupplierHandler"
             OnDelete="@DeleteSupplierHandler"
             OnCancel="@CancelSupplierHandler"
             @ref="@GridRef">
        <GridColumns>
            <GridCommandColumn Width="120px">
                @{
                    var supplier = context as TradeSupplierModel;
                    <a href=@($"/supplierdetails/{supplier.SubNumber}") class="btn btn-outline-primary">View Details</a>
                }
            </GridCommandColumn>
            @*<GridCommandColumn Width="120px">
                <GridCommandButton Command="ViewDetails" OnClick="@ShowDetails">Details</GridCommandButton>
            </GridCommandColumn>*@
            <GridColumn Field="SubName" Title="Supplier Name" />
            <GridColumn Field="ShortName" Title="Supplier Short Name" />
            <GridColumn Field="IsActive1" Title="Is Active" />
            <GridColumn Field="Type" Title="Type" />
            <GridCommandColumn>
                @{
                    var supplier = context as TradeSupplierModel;
                    if (supplier.ShortName.StartsWith("ONETIMEBID") && AllowEdit)
                    {
                        <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                        <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                        <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                    }
                }
                @if(AllowEdit)
                {
                    <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                }
                
            </GridCommandColumn>
        </GridColumns>
        <GridToolBarTemplate>
            <GridSearchBox DebounceDelay="200"></GridSearchBox>
            @if(AllowEdit)
            {
                <GridCommandButton Command="AddSupplier" OnClick="@ShowAddSupplier" Icon="@FontIcon.Plus" Class="k-button-add">Add Supplier</GridCommandButton>
            }
            <TelerikToggleButton @bind-Selected="@ToggleInactive" OnClick="@ToggleInactiveSupplier"> @ShowHideInactive InActive Suppliers</TelerikToggleButton>           
        </GridToolBarTemplate>        
        <NoDataTemplate>
            <p>@Message</p>
        </NoDataTemplate>
    </TelerikGrid>
}

<ERP.Web.Components.SupplierDetailsComponent @ref="SupplierDetailsModal" SubNumber=@SelectedSupplierNum></ERP.Web.Components.SupplierDetailsComponent>
<ERP.Web.Components.AddSupplier @ref="AddSupplierModal" HandleAddSubmit="@HandleValidAddSupplierSubmit"></ERP.Web.Components.AddSupplier>

@code {
    private List<TradeSupplierModel>? suppliers;
    private TelerikGrid<TradeSupplierModel> GridRef { get; set; }
    public string? Message { get; set; } = "No data to display";
    public TradeSupplierModel SelectedItem { get; set; }
    public List<TradeDto>? MasterTradeData { get; set; }
    public int? OldTradeId { get; set; }
    public int SelectedSupplierNum { get; set; } = 0;
    protected ERP.Web.Components.SupplierDetailsComponent? SupplierDetailsModal { get; set; }
    protected ERP.Web.Components.AddSupplier? AddSupplierModal { get; set; }
    public bool ToggleInactive { get; set; } = false;
    public string ShowHideInactive { get; set; } = "Show";
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }
    private bool AllowEdit { get; set; } = true;
    protected override async Task OnInitializedAsync()
    {
        suppliers = (await TradeService.GetSuppliersAsync()).Value;
        suppliers = suppliers.Where(x => x.IsActive1 == true).ToList();
        MasterTradeData = (await ItemService.GetTradesAsync()).Value; 
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }

    async Task ToggleInactiveSupplier()
    {
        var test = ToggleInactive;
        ShowHideInactive = ToggleInactive ? "Show" : "Hide";
        // ShowOptionSpecific = !ShowOptionSpecific;
        suppliers = (await TradeService.GetSuppliersAsync()).Value;
        if (ToggleInactive)
        {
            suppliers = suppliers.Where(x => x.IsActive1 == true).ToList();
        }
    }

    void EditSupplierHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel item = (TradeSupplierModel)args.Item;
        OldTradeId = item.TradeId;//track the old trade in case changing
    }

    async Task UpdateSupplierHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel item = (TradeSupplierModel)args.Item;
        item.OldTradeId = OldTradeId;
        var updateResponse = await TradeService.UpdateSupplierAsync(item);
        suppliers = (await TradeService.GetSuppliersAsync()).Value;
        if (ToggleInactive)
        {
            suppliers = suppliers.Where(x => x.IsActive1 == true).ToList();
        }
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
    }

    async Task DeleteSupplierHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel item = (TradeSupplierModel)args.Item;
        var deleteResponse = await TradeService.DeleteSupplierAsync(item);
        suppliers = (await TradeService.GetSuppliersAsync()).Value;
        if (ToggleInactive)
        {
            suppliers = suppliers.Where(x => x.IsActive1 == true).ToList();
        }
        ShowSuccessOrErrorNotification(deleteResponse.Message, deleteResponse.IsSuccess);
    }

    async Task ShowAddSupplier(GridCommandEventArgs args)
    {
        //TradeSupplierModel item = (TradeSupplierModel)args.Item;
        //await TradeService.AddSupplierAsync(item);
        //suppliers = await TradeService.GetSuppliersAsync();
        //if (ToggleInactive)
        //{
        //    suppliers = suppliers.Where(x => x.IsActive1 == true).ToList();
        //}
        AddSupplierModal.Show();
    }

    // private async void HandleValidAddSupplierSubmit(ResponseModel<TradeSupplierModel> responseItem)
    // {
    //     AddSupplierModal.Hide();
    //     suppliers = (await TradeService.GetSuppliersAsync()).Value;
    //     if (ToggleInactive)
    //     {
    //         suppliers = suppliers.Where(x => x.IsActive1 == true).ToList();
    //     }
    //     ShowSuccessOrErrorNotification(responseItem.Message, responseItem.IsSuccess);
    //     StateHasChanged();
    // }

    private async void HandleValidAddSupplierSubmit(ResponseModel<SimplerSupplierDto> responseItem)
    {
        AddSupplierModal.Hide();
        suppliers = (await TradeService.GetSuppliersAsync()).Value;
        if (ToggleInactive)
        {
            suppliers = suppliers.Where(x => x.IsActive1 == true).ToList();
        }
        ShowSuccessOrErrorNotification(responseItem.Message, responseItem.IsSuccess);
        StateHasChanged();
    }

    private void CancelSupplierHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel item = (TradeSupplierModel)args.Item;
    }

    private async Task ShowDetails(GridCommandEventArgs args)
    {
        TradeSupplierModel item = (TradeSupplierModel)args.Item;
        SelectedSupplierNum = item.SubNumber;
        await SupplierDetailsModal.Show();
        StateHasChanged();
    }

    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
