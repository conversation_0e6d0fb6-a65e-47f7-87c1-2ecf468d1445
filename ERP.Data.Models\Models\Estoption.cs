﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Estoption
{
    public int EstoptionId { get; set; }

    public int? EstheaderId { get; set; }

    public string? OptionNumber { get; set; }

    public string? OptionDesc { get; set; }

    public double? OptionSalesPrice { get; set; }

    public double? OptionQty { get; set; }

    public string? IsElevation { get; set; }

    public string? OptionNotes { get; set; }

    public int? SalesconfigoptionsId { get; set; }

    public int? SalesconfigcooptionsId { get; set; }

    public string? IsDeleted { get; set; }

    public string? OptionSelections { get; set; }

    public string? OptionLongDesc { get; set; }

    public string? OptionExtendedDesc { get; set; }

    public string? ElevationCode { get; set; }

    public string? ElevationDesc { get; set; }

    public string? WmsplanNum { get; set; }

    public string? PlanName { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CubitId { get; set; }

    public string? IsSelected { get; set; }

    public string? IncludeInBase { get; set; }

    public int? EstselectiontypeId { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<Estdetail> Estdetails { get; set; } = new List<Estdetail>();

    public virtual Estheader? Estheader { get; set; }

    public virtual ICollection<Estjcedetail> Estjcedetails { get; set; } = new List<Estjcedetail>();

    public virtual Salesconfigcooption? Salesconfigcooptions { get; set; }

    public virtual Salesconfigoption? Salesconfigoptions { get; set; }
}
