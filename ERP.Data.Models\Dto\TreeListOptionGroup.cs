﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    /// <summary>
    /// "True" Object Creation that will get serialized
    /// </summary>
    public class TreeListOptionGroup
    {
        public int? Id { get; set; }
        public int? ParentId { get; set; }
        public string? Code { get; set; }
        public string? Description { get; set; }
        public bool HasChildren { get; set; }

        // Might need "Table" reference for editing purpose. So on click event, it refers to the right table

        public List<TreeListOptionGroup> DirectReports { get; set; } = new List<TreeListOptionGroup>();
    }
}
