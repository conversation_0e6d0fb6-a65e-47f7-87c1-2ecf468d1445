﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public partial class OptionGroupDto : IMapFrom<OptionGroup>
{
    public int OptionGroupId { get; set; }

    public int? SsoptionGroupId { get; set; }

    public int? WmsoptionGroupId { get; set; }

    public string? OptCatCode { get; set; }

    public string? OptionGroupLetter { get; set; }

    public string OptionGroupName { get; set; } = null!;

    public string? Note { get; set; }

    public int? DeftMarkupType { get; set; }

    public int? DeftMarkupAmount { get; set; }

    public bool? PayCommission { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public void Mapping(Profile profile)
    {
        profile.CreateMap<OptionGroupDto, OptionGroup>().ReverseMap();
    }
}
