﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class PhasePlanModel
{
    public int PhasePlanId { get; set; }
    public string? Phase { get; set; }
    public MasterPlan? MasterPlan { get; set; }
    public int? PlanTypeId { get; set; }
    public string? PlanTypeDescription { get; set; }
    public string? PlanNumber { get; set; }
    public string? PlanName { get; set; }//fill from master plan, for flattening needed for some components
}
