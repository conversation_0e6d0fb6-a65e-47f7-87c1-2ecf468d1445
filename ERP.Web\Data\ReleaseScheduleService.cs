﻿
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using NLog;
using System.Diagnostics;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;
using Telerik.Blazor.Components;

namespace ERP.Web.Data
{
    public class ReleaseScheduleService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public ReleaseScheduleService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }
        
        public async Task<ResponseModel<List<JobDto>>> GetReleaseSchedules()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/ScheduleRelease/ReleaseSchedules");
                var responseString = await response.Content.ReadAsStringAsync();
                var schedules = JsonConvert.DeserializeObject<ResponseModel<List<JobDto>>>(responseString);
                return schedules;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<JobDto>>() { IsSuccess = false, Value = new List<JobDto>(), Message = "Something went wrong" };
        }

        public async Task<ResponseModel<List<JobDto>>> ImportReleaseSchedulesExcel(FileSelectFileInfo file)
        {
            var ImportedSchedules = new ResponseModel<List<JobDto>>() { Value = new List<JobDto>() };

            try
            {
                var fileData = new byte[file.Stream.Length];
                await file.Stream.ReadAsync(fileData);

                using (var ms = new MemoryStream(fileData))
                {
                    using (var excelWorkbook = new ClosedXML.Excel.XLWorkbook(ms))
                    {
                        var nonEmptyDataRows = excelWorkbook.Worksheet(1).RowsUsed().Skip(1);
                        foreach (var dataRow in nonEmptyDataRows)
                        {
                            var schedule = new JobDto
                            {
                                JobNumber = dataRow.Cell(1).IsEmpty() ? null : dataRow.Cell(1).Value.ToString(),
                                JobSchedule = new ScheduleDto
                                {
                                    PermitSubmitDate = dataRow.Cell(6).IsEmpty() ? null : DateTime.Parse(dataRow.Cell(6).Value.ToString()),
                                    PermitReceivedDate = dataRow.Cell(7).IsEmpty() ? null : DateTime.Parse(dataRow.Cell(7).Value.ToString()),
                                    PermitNumber = dataRow.Cell(8).IsEmpty() ? null : dataRow.Cell(8)                                                                                                                                                    .Value.ToString()
                                }
                            };
                            ImportedSchedules.Value.Add(schedule);
                        }
                    }
                    ms.Close();
                }
                return ImportedSchedules;
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return ImportedSchedules;
        }

        public async Task<ResponseModel<List<JobDto>>> SaveReleaseSchedulesImportAsync(List<JobDto> importedReleaseSchedules)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<JobDto>, ResponseModel<List<JobDto>>>(
                           "DownstreamApi", importedReleaseSchedules,
                            options => {
                                options.RelativePath = "api/ScheduleRelease/SaveReleaseSchedulesExcel/";
                            });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<JobDto>>() { IsSuccess = false, Message = "Failed to upload release schedules. If problem persists, <NAME_EMAIL>" };
        }
    }
}
