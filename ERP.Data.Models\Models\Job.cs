﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Job
{
    public string JobNumber { get; set; } = null!;

    public decimal? AcquisitionLotCost { get; set; }

    public int? ApprovedDepositRequirement { get; set; }

    public string? ElevationCode { get; set; }

    public string? ElevationDesc { get; set; }

    public int? GarageOrientationId { get; set; }

    public string? JobAddress1 { get; set; }

    public string? JobAddress2 { get; set; }

    public string? JobCity { get; set; }

    public string? JobCounty { get; set; }

    public string? JobDesc { get; set; }

    public string JobPostingGroup { get; set; } = null!;

    public string? JobState { get; set; }

    public string? JobZipCode { get; set; }

    public int? LotAvailability { get; set; }

    public decimal? LotCost { get; set; }

    public string? LotNumber { get; set; }

    public decimal? LotPremium { get; set; }

    public string? LotSectionCode { get; set; }

    public int? LotStatus { get; set; }

    public string? LotSwing { get; set; }

    public string? LotUnit { get; set; }

    public string? LotWidth { get; set; }

    public string? ModelName { get; set; }

    public DateTime? OverLot { get; set; }

    public string? Phase { get; set; }

    public string? PlanCode { get; set; }

    public string? PlanName { get; set; }

    public string? PossessionStatus { get; set; }

    public DateTime? ProjectedTakeDownDate { get; set; }

    public string? Restrictions { get; set; }

    public DateTime? StatsStickStartDate { get; set; }

    public string? SubdivisionClass { get; set; }

    public DateTime? TakedownDate { get; set; }

    public string? TakedownType { get; set; }

    public int? SubdivisionId { get; set; }

    public string? Supervisor { get; set; }

    public string? ProjectMgr { get; set; }

    public string? SalesContact { get; set; }

    public string? FieldSuper { get; set; }

    public string? UserContact1 { get; set; }

    public string? UserContact2 { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdateDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStampe { get; set; } = null!;

    public bool? IsModel { get; set; }

    public bool? IsListed { get; set; }

    public string? BuildingNum { get; set; }

    public string? Notes { get; set; }

    public int? CustomerId { get; set; }

    public string? StickBuilingNum { get; set; }

    public decimal? LotSize { get; set; }

    public DateTime? BcSettlementDate { get; set; }

    public bool? Blocked { get; set; }

    public bool? LinkWithErp { get; set; }

    public int? JobConstructionTypeId { get; set; }

    public DateTime? EstimatedCompletionDate { get; set; }

    public string? EstimatedCompletionSource { get; set; }

    public string? EntityNum { get; set; }

    /// <summary>
    /// this field is used between BC and ERP integration to support job closed or open in BC
    /// </summary>
    public string? Closed { get; set; }

    public bool? HomeOrientationPerPlan { get; set; }

    public string? ParkingNum { get; set; }

    public string? StorageNum { get; set; }

    public bool? IsSs { get; set; }

    public DateTime? SaleReleaseDate { get; set; }

    public decimal? LotSquareFeet { get; set; }

    public int? Bedroom { get; set; }

    public int? Bathroom { get; set; }

    public string? GarageNum { get; set; }

    public DateTime? ProjectedLotTakedown { get; set; }

    public DateTime? ProjectedSalesReleaseDate { get; set; }

    public decimal? GeneralOptionBudget { get; set; }

    public decimal? HomesiteOptionSpendBudget { get; set; }

    public int? HalfBathroom { get; set; }

    public decimal? PlanSquareFeet { get; set; }

    public int? SelectedPlanId { get; set; }

    public int? SelectedElevationId { get; set; }

    public int? SelectedColorSchemeId { get; set; }

    public decimal? AdjustedBasePrice { get; set; }

    public virtual ICollection<Contract> Contracts { get; set; } = new List<Contract>();

    public virtual ICollection<Estactivity> Estactivities { get; set; } = new List<Estactivity>();

    public virtual ICollection<Estheader> Estheaders { get; set; } = new List<Estheader>();

    public virtual GarageOrientation? GarageOrientation { get; set; }

    public virtual ICollection<HoaJob> HoaJobs { get; set; } = new List<HoaJob>();

    public virtual ICollection<JobAttachment> JobAttachments { get; set; } = new List<JobAttachment>();

    public virtual JobConstructionType? JobConstructionType { get; set; }

    public virtual ICollection<JobContact> JobContacts { get; set; } = new List<JobContact>();

    public virtual ICollection<JobCustomer> JobCustomers { get; set; } = new List<JobCustomer>();

    public virtual JobPostingGroup JobPostingGroupNavigation { get; set; } = null!;

    public virtual ICollection<JobTask> JobTasks { get; set; } = new List<JobTask>();

    public virtual LotStatus? LotStatusNavigation { get; set; }

    public virtual ICollection<Poheader> PoheaderHouseJobNavigations { get; set; } = new List<Poheader>();

    public virtual ICollection<Poheader> PoheaderPojobnumberNavigations { get; set; } = new List<Poheader>();

    public virtual ICollection<Salesconfig> Salesconfigs { get; set; } = new List<Salesconfig>();

    public virtual Schedule? Schedule { get; set; }

    public virtual MaterialColorScheme? SelectedColorScheme { get; set; }

    public virtual AvailablePlanOption? SelectedElevation { get; set; }

    public virtual PhasePlan? SelectedPlan { get; set; }

    public virtual Spec? Spec { get; set; }

    public virtual Subdivision? Subdivision { get; set; }

    public virtual ICollection<TbBuiltOption> TbBuiltOptions { get; set; } = new List<TbBuiltOption>();
}
