﻿@inject AttributeService AttributeService
@using ERP.Data.Models
@inject AttributeItemPickService AttributeItemPickService
@implements IDisposable

<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

@if (AttributeGroupData == null)
{
    <p><em>Loading...</em></p>
    <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingOptions />
}
else
{
    <div style="margin-bottom:6px">Drag and Drop from Attribute Item on the left to here</div>
    <TelerikGrid Data="@AttributeGroupData"
                 SelectionMode="GridSelectionMode.Single"
                 EditMode="@GridEditMode.Inline"
                 OnRowExpand="@OnRowExpandHandler"
                 OnDelete="@OnDeleteHandler"
                 RowDraggable="true">
        <GridToolBarTemplate>
            <span class="k-toolbar-spacer"></span>
            <GridSearchBox />
        </GridToolBarTemplate>
        <GridColumns>
            <GridColumn Field="Description" Editable="false"></GridColumn>
            <GridColumn Field="SubdivisionName" Editable="false"></GridColumn>
            <GridColumn Field="PlanNum" Editable="false"></GridColumn>
            <GridColumn Field="AttributeGroupId" Visible="false"></GridColumn>
            <GridCommandColumn>
                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                <GridCommandButton Command="Reactive" Icon="@FontIcon.Link" ShowInEdit="false" OnClick="@ReactiveClickHandler"></GridCommandButton>
            </GridCommandColumn>
        </GridColumns>
        <DetailTemplate>
            @{
                MasterAttributeGroupDto groupData = context as MasterAttributeGroupDto;
                <ERP.Web.Components.NestedItem AttributeGroupId="groupData.AttributeGroupId" MasterOptionId="@MasterOptionId" PlanOptionId="groupData.PlanOptionId"></ERP.Web.Components.NestedItem>
            }
        </DetailTemplate>
    </TelerikGrid>
}

@code {
    /// <summary>
    /// Properties
    /// </summary>
    [Parameter] public int MasterOptionId { get; set; }
    public List<MasterAttributeGroupDto>? AttributeGroupData { get; set; }
    public bool IsLoadingOptions { get; set; } = false;

    /// <summary>
    /// Delegate
    /// </summary>
    private bool? checkStatus;
    private bool? showNotification;

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    /// <summary>
    /// Reset
    /// </summary>
    int RequestCount { get; set; }
    string? errorMessage { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        var result = await AttributeService.GetGroupByMasterOptionIdAsync(MasterOptionId);
        AttributeGroupData = result.Value;
    }

    async Task OnRowExpandHandler(GridRowExpandEventArgs args)
    {
        this.AttributeItemPickService.MasterOptionId = MasterOptionId;
    }

    async Task OnDeleteHandler(GridCommandEventArgs args)
    {
        var group = (MasterAttributeGroupDto)args.Item;
        var result = await AttributeService.DeleteOptionAttributeGroupAsync(group);

        // Notifications
        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = this.AttributeItemPickService.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Successfully delete group from the option",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        // Reload
        var reload = await AttributeService.GetGroupByMasterOptionIdAsync(MasterOptionId);
        AttributeGroupData = reload.Value;
    }

    /// <summary>
    /// Reactivate
    /// </summary>
    /// <param name="args"></param>
    /// <returns></returns>
    private async Task ReactiveClickHandler(GridCommandEventArgs args)
    {
        var group = (MasterAttributeGroupDto)args.Item;
        var result = await AttributeService.ReactivateOptionAttributeGroupAsync(group);

        // Notifications
        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = this.AttributeItemPickService.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Successfully reactivate group from the option",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        // Reload
        var reload = await AttributeService.GetGroupByMasterOptionIdAsync(MasterOptionId);
        AttributeGroupData = reload.Value;
    }

    protected override void OnInitialized()
    {
        this.AttributeItemPickService.OnDataChanged += RefreshGroup;
    }

    async Task RefreshGroup()
    {
        checkStatus = this.AttributeItemPickService.IsChanged!;
        errorMessage = this.AttributeItemPickService.ErrorMessage;
        showNotification = this.AttributeItemPickService.ShowNotification!;

        await this.InvokeAsync(StateHasChanged);

        if (checkStatus == true)
        {
            RequestCount++;

            // This is to suppress delegate calls
            if (RequestCount == 1)
            {
                // Notifications
                if (!string.IsNullOrWhiteSpace(errorMessage))
                {
                    NotificationReference.Show(new NotificationModel()
                        {
                            Text = errorMessage,
                            ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                        });
                }
                else
                {
                    if (showNotification != false)
                    {
                        NotificationReference.Show(new NotificationModel()
                            {
                                Text = "Successfully added item attribute",
                                ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                            });
                    }
                }

                // Reload
                var result = await AttributeService.GetGroupByMasterOptionIdAsync(MasterOptionId);
                AttributeGroupData = result.Value;

                // Refresh all components
                StateHasChanged();
            }

            RequestCount--;
            errorMessage = string.Empty;
            this.AttributeItemPickService.IsChanged = false;
            this.AttributeItemPickService.ErrorMessage = string.Empty;
        }
    }

    public void Dispose()
    {
        this.AttributeItemPickService.OnDataChanged -= RefreshGroup;
    }
}
