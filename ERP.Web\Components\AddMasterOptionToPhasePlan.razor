﻿@using ERP.Data.Models.Dto;
@inject OptionService OptionService;
@inject ItemService ItemService;
@inject SalesPriceService SalesPriceService;

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="250px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Option To Plan
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@OptionsToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <p>Select Option(s) to Add to Plan: @Plan.PlanName</p>

            <p>
                <label>Select Option(s)</label>
                <TelerikMultiSelect @bind-Value="@SelectedOptionIds"
                                    Data="@OptionsData"
                                    ValueField="AsmHeaderId"
                                    TextField="DisplayName"
                                    Filterable="true"
                                    FilterOperator="StringFilterOperator.Contains"
                                    Placeholder="Select Option"
                                    AutoClose="false"
                                    Width="100%">
                </TelerikMultiSelect>
            </p> 
             
            <button type="submit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                    </svg>
                </span> Update
            </button>
            <button type="button" @onclick="CancelAddItem" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-base">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel" aria-hidden="true">
                    <!--!-->
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M256 32c-50.3 0-96.8 16.6-134.1 44.6-17.2 12.8-32.4 28.1-45.3 45.3C48.6 159.2 32 205.7 32 256c0 123.7 100.3 224 224 224 50.3 0 96.8-16.6 134.1-44.6 17.2-12.8 32.4-28.1 45.3-45.3 28-37.4 44.6-83.8 44.6-134.1 0-123.7-100.3-224-224-224zm0 384c-88.2 0-160-71.8-160-160 0-32.6 9.8-62.9 26.6-88.2l221.6 221.6C318.9 406.2 288.6 416 256 416zm133.4-71.8L167.8 122.6C193.1 105.8 223.4 96 256 96c88.2 0 160 71.8 160 160 0 32.6-9.8 62.9-26.6 88.2z"></path><!--!-->
                    </svg><!--!-->
                    <!--!-->
                </span> Cancel
            </button>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    [Parameter]
    public bool IsModalVisible { get; set; }
    [Parameter]
    public EventCallback<AddOptionToPlanModel> HandleAddSubmit { get; set; }
    [Parameter]
    public SubdivisionPlanModel Plan { get; set; }

    public MasterOptionHeaderModel OptionsToAdd { get; set; } = new MasterOptionHeaderModel();
    public List<AsmHeaderModel>? OptionsData { get; set; } = new List<AsmHeaderModel>();
    public List<int>? SelectedOptionIds { get; set; } = new List<int>();

    public async Task Show()
    {
        IsModalVisible = true;
        await LoadOptionsData();
        StateHasChanged();
    }
    void CancelAddItem()
    {
        IsModalVisible = false;
    }
    public void Hide()
    {
        IsModalVisible = false;
    }

    private async Task LoadOptionsData()
    {
        if(Plan!= null)
        {
            var masterOptionsInPlanTask = ItemService.GetAssembliesInPlanAsync(Plan.MasterPlanId ?? 0);
            var availableOptionsTask = SalesPriceService.GetAvailablePlanOptionsByPlanAsync((int)Plan.PhasePlanId);
            await Task.WhenAll(new Task[] { masterOptionsInPlanTask, availableOptionsTask });
            var masterOptions = masterOptionsInPlanTask.Result.Value;
            var availableOptions = availableOptionsTask.Result.Value;            
            var availableOptionCodes = availableOptions.Select(x => $"{Plan.PlanNum}{x.OptionCode}").ToList();
            OptionsData = masterOptions.Where(x => !availableOptionCodes.Contains(x.AssemblyCode) && x.IsBaseHouse != "T").ToList();
        }
        
    }

    private async void HandleValidAddSubmit()
    {
        //submittingStyle = "";
        var options = SelectedOptionIds.Select(x => new AsmHeaderModel()
            {
                AsmHeaderId = x
            }).ToList();

        var responseItem = (await OptionService.AddToAvailablePlanOptionAsync(options, Plan)).Value;

        //submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(responseItem);
        SelectedOptionIds.Clear();
    }
}
