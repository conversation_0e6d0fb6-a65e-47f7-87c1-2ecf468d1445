﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class SupplierInsuranceDto : IMapFrom<SupplierInsurance>
{
    public string? SubNavNumber { get; set; } 

    public int LineNo { get; set; }

    public string? InsuranceTypeCode { get; set; }

    public byte InsuranceRequired { get; set; }

    public string? PolicyNumber { get; set; } 

    public DateTime? PolicyStartDate { get; set; }

    public DateTime? PolicyExpirationDate { get; set; }

    public string? InsuranceCompanyName { get; set; } 

    public string? InsuranceContactName { get; set; } 

    public string? InsuranceContactPhoneNo { get; set; } 

    public decimal Coverageamount { get; set; }

    public DateTime Createddatetime { get; set; }

    public string? Createdby { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? Updatedby { get; set; }

    public bool? IsActive { get; set; }

    //public byte[] RecordTimeStamp { get; set; } = null!;

    public int? SubNumber { get; set; }

    public SupplierInsuranceTypeDto? InsuranceTypeCodeNavigation { get; set; }

    public SupplierDto? SubNumberNavigation { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<SupplierInsuranceDto, SupplierInsurance>().ReverseMap();
    }
}
