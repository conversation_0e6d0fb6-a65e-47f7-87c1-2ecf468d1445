﻿@page "/colorschemes/"
@inject ColorSchemeService ColorSchemeService
@inject SubdivisionService SubdivisionService
@inject ColorSchemePickService ColorSchemePickService
@inject MaterialColorDocumentService MaterialColorDocumentService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JS
@using ERP.Web.Components
@implements IDisposable
@attribute [Authorize(Roles = "Admin, DesignCenter")]
<PageTitle>Color Scheme</PageTitle>

<style>
    .highlightCellBackGroud {
        background-color: lightpink;
    }

    .negativeValuesRowFormatting {
        background-color: lightyellow !important;
    }

    .positiveValuesRowFormatting {
        background-color: lightcyan !important;
    }

    .link-button {
        background-color: transparent;
        border: none;
        padding: 0;
        text-decoration: underline;
        cursor: pointer;
        color: blue;
    }

    .delete-button {
        background-color: transparent;
        border: none;
        padding: 0;
        cursor: pointer;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto",sans-serif;
        font-size: .75rem;
    }
</style>



<TelerikTooltip TargetSelector=".tooltip-target" />

<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<ShowPlansForScheme ColorScheme="@SelectedScheme" @ref="ShowPlansForScheme"></ShowPlansForScheme>

<TelerikLoaderContainer Visible="@IsLoading" Text="Please wait..." />

<div class="container-fluid flex">
    <div class="row d-flex">
        <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Color Schemes</h7>
                </div>
            </div>
        </div>

        <TelerikTabStrip>
            <TabStripTab Title="Plan Color Schemes">
                @if (Grid == null)
                {
                    <p><em>Loading...</em></p>
                    <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />
                }
                else
                {
                    <TelerikGrid Data=@Grid
                    @ref="@PlanSchemeGridRef"
                    Pageable="true" PageSize="100"
                    Sortable="true" SortMode="@SortMode.Multiple"
                    EditMode="@GridEditMode.Inline"
                    Groupable="true"
                    FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                    FilterMenuType="@FilterMenuType.CheckBoxList"
                    ConfirmDelete="true"
                    SelectionMode="@GridSelectionMode.Multiple"
                    @bind-SelectedItems="SelectedSchemes"
                    OnEdit="@EditColorGridHandler"
                    OnDelete="DeleteColorGridHandler"
                    OnUpdate="@UpdateColorGridHandler">
                        <GridToolBarTemplate>
                            <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>                           
                            <GridCommandButton Command="AddNew" Icon="@FontIcon.Plus" Class="k-button-add" OnClick="@ShowAddColorScheme">Add</GridCommandButton>
                            <GridCommandButton Command="DeleteSelected" Icon="@FontIcon.Trash" OnClick="@DeleteSelected" Class="k-button-danger">Delete Selected</GridCommandButton>
                            @*  <GridCommandButton Command="UpdateSelected" Icon="@FontIcon.ArrowDown" OnClick="@UpdateSelected">Fill Down Selected Colors</GridCommandButton> *@
                            <GridCommandButton Command="ExcelExport" Icon="@FontIcon.FileExcel" Class="k-button-success">- Export to Excel</GridCommandButton>
                            <TelerikFileSelect AllowedExtensions="@AllowedExtensions"
                            Multiple="false"
                            MaxFileSize="@MaxSize"
                            OnRemove="@OnRemoveHandler"
                            OnSelect="@OnSelectHandler"
                            @ref="ImportedFileRef">
                                <SelectFilesButtonTemplate>
                                    <TelerikFontIcon Icon="@FontIcon.Import" />
                                    &nbsp; Import
                                </SelectFilesButtonTemplate>
                            </TelerikFileSelect>
                            @if (IsImported)
                            {
                                <TelerikButton ButtonType="@ButtonType.Button" OnClick="@ApplyImportColorSchemes" Class="k-button-success">Save</TelerikButton>
                            }
                        </GridToolBarTemplate>
                        <GridExport >
                            <GridExcelExport OnBeforeExport="@BeforeExport" AllPages="true" FileName="ColorScheme" />
                        </GridExport>
                        <GridColumns>
                            <GridCheckboxColumn />
                            <GridColumn Field="SubdivisionName" Title="Subdivision" Editable="false" />
                            <GridColumn Field="SubdivisionNum" Title="Subdivision Number" Editable="false" Width="0" />
                            <GridColumn Field="PlanName" Title="Plan" Editable="false" />
                            <GridColumn Field="PlanNum" Title="Plan Number" Editable="false" Width="0" />
                            <GridColumn Field="PlanOptionName" Title="Option" Editable="false" />
                            <GridColumn Field="OptionCode" Title="Option Code" Editable="false" />
                            <GridColumn Field="ColorSchemeNum" Title="Color Scheme Num" Editable="false" />
                            <GridColumn Field="MaterialName" Title="Material" Editable="true">
                                <EditorTemplate Context="ColorGridContext">
                                    @{
                                        EditedColorGridRow = ColorGridContext as ColorGridModel;
                                        <TelerikDropDownList Data="@Materials"
                                        TextField="Material1"
                                        ValueField="MateriaId"
                                        Filterable="true"
                                        OnChange="@MaterialChangeHandler"
                                        FilterOperator="@StringFilterOperator.Contains"
                                        @bind-Value="EditedColorGridRow.MaterialId"
                                        Width="100%">
                                        </TelerikDropDownList>
                                        @if (EditedColorGridRow.MaterialId == -1)
                                        {
                                            <TelerikTextBox @bind-Value="NewGridMaterialName"></TelerikTextBox>
                                        }

                                    }
                                </EditorTemplate>
                            </GridColumn>
                            <GridColumn Field="ColorScheme1" Title="Color" Editable="true">
                                <EditorTemplate Context="ColorGridContext">
                                    @{
                                        EditedColorGridRow = ColorGridContext as ColorGridModel;
                                        <TelerikDropDownList Data="@Colors"
                                        TextField="ColorScheme1"
                                        ValueField="ColorShemeId"
                                        Filterable="true"
                                        FilterOperator="@StringFilterOperator.Contains"
                                        @bind-Value="EditedColorGridRow.ColorSchemeId"
                                        Width="100%">
                                        </TelerikDropDownList>
                                        @if (EditedColorGridRow.ColorSchemeId == -1)
                                        {
                                            <TelerikTextBox @bind-Value="NewGridColorName"></TelerikTextBox>
                                        }

                                    }
                                </EditorTemplate>
                            </GridColumn>
                            <GridCommandColumn>
                                <GridCommandButton Title="Save" Class="tooltip-target k-button-success" Command="Save" Icon="@FontIcon.Save" ShowInEdit="true"></GridCommandButton>
                                <GridCommandButton Title="Edit" Class="tooltip-target k-button-success" Command="Edit" Icon="@FontIcon.Pencil"></GridCommandButton>
                                <GridCommandButton Title="Delete" Class="tooltip-target k-button-danger" Command="Delete" Icon="@FontIcon.Trash"></GridCommandButton>
                                <GridCommandButton Title="Cancel" Class="tooltip-target k-button-danger" Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true"></GridCommandButton>
                            </GridCommandColumn>
                        </GridColumns>
                    </TelerikGrid>
                }
            </TabStripTab>
            <TabStripTab Title="Material Color Combinations">
                @if (PredefinedMaterialColorGrid == null)
                {
                    <p><em>Loading...</em></p>
                    <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />
                }
                else
                {
                    <TelerikGrid @ref="@PredefinedMaterialColorGridRef"
                    Data=@PredefinedMaterialColorGrid
                    Sortable="true" SortMode="@SortMode.Multiple"
                    Groupable="true"
                    OnStateInit="@( (GridStateEventArgs<MaterialColorPredefinedDto> args) => OnMaterialColorStateInitHandler(args) )"
                    FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                    FilterMenuType="@FilterMenuType.CheckBoxList"
                    EditMode="@GridEditMode.Inline"
                    OnCreate="@CreatePredefinedMaterialColorHandler"
                    OnUpdate="@UpdatePredefinedMaterialColorHandler">
                        <GridToolBarTemplate>
                            <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>
                            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add</GridCommandButton>
                            <GridCommandButton Icon="@FontIcon.Upload" Class="k-button-add" OnClick="UploadMaterialColorDocument">Upload Image</GridCommandButton>
                            <GridCommandButton Command="ExcelExport" Icon="@FontIcon.FileExcel" Class="k-button-success">- Export to Excel</GridCommandButton>
                            <TelerikFileSelect Class="import"
                            AllowedExtensions="@AllowedExtensions"
                            Multiple="false"
                            MaxFileSize="@MaxSize"
                            OnSelect="@OnPredefinedMaterialColorSelectHandler">
                                <SelectFilesButtonTemplate>
                                    <TelerikFontIcon Icon="@FontIcon.Import" />
                                    &nbsp; Import
                                </SelectFilesButtonTemplate>
                            </TelerikFileSelect>
                        </GridToolBarTemplate>
                        <GridExport>
                            <GridExcelExport OnBeforeExport="@BeforePredefinedMaterialColorGridExport" AllPages="true" FileName="ColorScheme" />
                        </GridExport>
                        <GridColumns>
                            <GridColumn Field="MaterialColorPredefinedId" Title="Id" Width="0px" />
                            <GridColumn Field="Material.Material1" Title="Material">
                                <EditorTemplate>
                                    @{
                                        EditedPredefinedMaterialColorRow = context as MaterialColorPredefinedDto;
                                        <TelerikDropDownList Data="@Materials"
                                        TextField="Material1"
                                        ValueField="MateriaId"
                                        Filterable="true"
                                        OnChange="@PredefinedMaterialChangeHandler"
                                        FilterOperator="@StringFilterOperator.Contains"
                                        @bind-Value="EditedPredefinedMaterialColorRow.MaterialId"
                                        Width="100%">
                                        </TelerikDropDownList>
                                        @if (EditedPredefinedMaterialColorRow.MaterialId == -1)
                                        {
                                            <TelerikTextBox @bind-Value="NewMaterialName"></TelerikTextBox>
                                        }
                                    }
                                </EditorTemplate>
                            </GridColumn>
                            <GridColumn Field="ColorScheme.ColorScheme1" Title="Color">
                                <EditorTemplate>
                                    @{
                                        EditedPredefinedMaterialColorRow = context as MaterialColorPredefinedDto;
                                        <TelerikDropDownList Data="@Colors"
                                        TextField="ColorScheme1"
                                        ValueField="ColorShemeId"
                                        Filterable="true"
                                        FilterOperator="@StringFilterOperator.Contains"
                                        @bind-Value="EditedPredefinedMaterialColorRow.ColorSchemeId"
                                        Width="100%">
                                        </TelerikDropDownList>
                                        @if (EditedPredefinedMaterialColorRow.ColorSchemeId == -1)
                                        {
                                            <TelerikTextBox @bind-Value="NewColorName"></TelerikTextBox>
                                        }
                                    }
                                </EditorTemplate>
                            </GridColumn>
                            <GridColumn Field="ImageLink" Title="Link to Sample Image" Editable=false>
                                <Template>
                                    @{
                                        var item = (MaterialColorPredefinedDto)context;
                                        if (item.ImageLink != null)
                                        {
                                            <button class="link-button" onclick="@( () => DownloadDocument(@item.ImageLink) )">@item.ImageLink</button>
                                            <button type="button" onclick="@( () => DeleteDocument(@item) )" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-base k-button-danger">
                                                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel" aria-hidden="true">
                                                    <!--!-->
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash" viewBox="0 0 16 16">
                                                        <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z" />
                                                        <path d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4zM2.5 3h11V2h-11z" />
                                                    </svg>
                                                    <!--!-->
                                                </span>
                                            </button>
                                        }
                                    }
                                </Template>
                            </GridColumn>
                            <GridColumn Field="IsActive" Visible="true" Editable=false FilterMenuType="@FilterMenuType.Menu">
                                <Template Context="gridContext">
                                    @{
                                        var item = (MaterialColorPredefinedDto)gridContext;
                                        var toggleValue = item.IsActive??true;
                                        var ActiveOrInactive = item.IsActive??true ? "Active" : "Inactive";
                                        <TelerikToggleButton @bind-Selected="toggleValue"
                                        OnClick="@(() => OnActiveInactiveToggle(!toggleValue, item))"> @ActiveOrInactive </TelerikToggleButton>
                                    }
                                </Template>
                                <FilterMenuTemplate Context="gridContext">
                                    @foreach (var option in ActiveInactiveFilterList)
                                    {
                                        <div>
                                            <TelerikCheckBox Value="@(IsCheckboxInCurrentFilter(gridContext.FilterDescriptor, option))"
                                            TValue="bool"
                                            ValueChanged="@((value) => UpdateActiveInactiveFilter(value, option, gridContext))"
                                            Id="@($"{option}")">
                                            </TelerikCheckBox>
                                            <label for="@($"{option}")">
                                                @option
                                            </label>
                                        </div>
                                    }
                                </FilterMenuTemplate>
                                <FilterMenuButtonsTemplate Context="filterContext">
                                    <TelerikButton OnClick="@(async _ => await filterContext.FilterAsync())">Filter </TelerikButton>
                                    <TelerikButton OnClick="@(() => ClearActiveInactiveFilterAsync(filterContext))">Clear</TelerikButton>
                                </FilterMenuButtonsTemplate>
                            </GridColumn>
                            <GridCommandColumn>
                                @{
                                    var item = context as MaterialColorPredefinedDto;
                                    if (Grid.Any(x => x.PredefinedMaterialColorId == item.MaterialColorPredefinedId)) //if any color scheme using this material color combination, restrict edit
                                    {
                                        <span title="Color Scheme exists with this Material Color Combination " class="tooltip-target">
                                            <TelerikFontIcon Icon="@FontIcon.InfoCircle" />
                                        </span>
                                    }
                                    else
                                    {
                                        <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                    }
                                }
                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                            </GridCommandColumn>
                        </GridColumns>
                    </TelerikGrid>
                }
            </TabStripTab>
        </TelerikTabStrip>
    </div>
</div>
<AddColorScheme @ref="AddColorSchemeModal" HandleAddSubmit="AddColorSchemesSubmit"></AddColorScheme>
<UploadMaterialColorDocument @ref="UploadMaterialColorDocumentModal" HandleUploadSubmit="HandleUploadMaterialColorDocumentSubmit"></UploadMaterialColorDocument>

@code {
    private bool IsImported = false;
    private TelerikFileSelect ImportedFileRef;
    private TelerikGrid<ColorGridModel>? PlanSchemeGridRef { get; set; }
    public TelerikGrid<MaterialColorPredefinedDto> PredefinedMaterialColorGridRef { get; set; }
    private List<ColorGridModel>? Grid { get; set; }
    private List<MaterialColorPredefinedDto> PredefinedMaterialColorGrid { get; set; }
    public List<ColorSchemeDto>? Colors { get; set; }
    public List<ColorSchemeDto>? AllColors { get; set; }
    public List<MaterialDto>? Materials { get; set; }
    public IEnumerable<ColorGridModel> SelectedSchemes { get; set; } = Enumerable.Empty<ColorGridModel>();
    private MaterialColorSchemeDto? EditedMaterialColorSchemeRow { get; set; }
    private MaterialColorPredefinedDto? EditedPredefinedMaterialColorRow { get; set; }
    private ColorGridModel? EditedColorGridRow { get; set; }
    private string? NewColorName { get; set; }
    private string? NewMaterialName { get; set; }
    private string? NewGridColorName { get; set; }
    private string? NewGridMaterialName { get; set; }
    private int? CurrentMaterialId { get; set; }
    private bool MaterialIsEditable { get; set; } = true;//material needs to be editable in add new, but not editable in edit row
    protected ERP.Web.Components.ShowPlansForScheme? ShowPlansForScheme { get; set; }
    public string? SelectedScheme { get; set; }
    List<string> AllowedExtensions { get; set; } = new List<string>() { ".xlsx" };
    public int MaxSize { get; set; } = 10 * 1024 * 1024; //10 MB
    public List<ColorSchemeLoadTemplateDto>? ImportedColorSchemes { get; set; } = new List<ColorSchemeLoadTemplateDto>();
    private int GridTotal { get; set; }
    private AddColorScheme? AddColorSchemeModal { get; set; }
    private UploadMaterialColorDocument? UploadMaterialColorDocumentModal { get; set; }
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }
    private bool AllowEdit { get; set; } = true;
    public List<string> ActiveInactiveFilterList { get; set; } = new List<string> { "Active", "Inactive" };
    public List<string> ActiveInactiveFilterCurrentSelection { get; set; } = new List<string>();
    private bool IsLoading { get; set; } = false;

    protected override async Task OnInitializedAsync()
    {
        var materialsTask = ColorSchemeService.GetMaterialsAsync();
        var gridTask = ColorSchemeService.GetColorGridAsync();
        var colorsTask = ColorSchemeService.GetColorsAsync();
        var materialColorCombinationsTask = ColorSchemeService.GetPredefinedMaterialColorCombinationsAsync();
        await Task.WhenAll(materialsTask, gridTask, colorsTask, materialColorCombinationsTask);
        var getMaterials = materialsTask.Result;
        Materials = getMaterials.Value;
        Materials.Insert(0, new MaterialDto() { MateriaId = -1, Material1 = "Add New" });
        var getColors = colorsTask.Result;
        Colors = getColors.Value;
        AllColors = getColors.Value;
        Colors.Insert(0, new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = "Add New" });
        var getGrid = gridTask.Result;
        Grid = getGrid.Value;
        PredefinedMaterialColorGrid = materialColorCombinationsTask.Result.Value;
        this.ColorSchemePickService.OnDataChanged += GetSubdivisionPlanElevation;
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }
    async void ShowAddColorScheme()
    {
        AddColorSchemeModal.Show();
    }
    async Task AddColorSchemesSubmit(ResponseModel<List<MaterialColorSchemeDto>> response)
    {
        if (response.IsSuccess)
        {
            var itemsToadd = new List<ColorGridModel>();
            var newItems = response.Value.Select(x =>
                new ColorGridModel()
                    {
                        Id = x.MaterialColorSchemeId,
                        ColorSchemeNum = x.ColorSchemeNum,
                        SubdivisionName = x.PlanOption.PhasePlan.Subdivision.SubdivisionName,
                        PlanName = x.PlanOption.PhasePlan.MasterPlan.PlanName,
                        PlanOptionName = x.PlanOption.ModifiedOptionDesc,
                        OptionCode = x.PlanOption.OptionCode,
                        MaterialName = x.MaterialColorPredefined.Material.Material1,
                        ColorScheme1 = x.MaterialColorPredefined.ColorScheme.ColorScheme1,
                        MaterialId = x.MaterialColorPredefined.Material.MateriaId,
                        ColorSchemeId = x.MaterialColorPredefined.ColorScheme.ColorShemeId
                    }).ToList();
            Grid.AddRange(newItems);
            PlanSchemeGridRef.Rebind();
            PredefinedMaterialColorGrid = (await ColorSchemeService.GetPredefinedMaterialColorCombinationsAsync()).Value;
        }
        ShowMessage(response.IsSuccess, response.Message);
        AddColorSchemeModal.Hide();
    }
    async Task DeleteSelected()
    {
        var response = await ColorSchemeService.DeleteGridColorsAsync(SelectedSchemes.ToList());
        if (response.IsSuccess)
        {
            Grid.RemoveAll(x => SelectedSchemes.Select(y => y.Id).Contains(x.Id));
            PlanSchemeGridRef.Rebind();
        }
        ShowMessage(response.IsSuccess, response.Message);
        AddColorSchemeModal.Hide();
    }
    async Task UpdateSelected()
    {
        //fill down type function, only for colors
        var getTopItemIndex = SelectedSchemes.Min(x => Grid.IndexOf(x));
        var getTopItem = SelectedSchemes.FirstOrDefault(x => Grid.IndexOf(x) == getTopItemIndex);
        var response = await ColorSchemeService.UpdateGridColorsAsync(SelectedSchemes.OrderBy(x => Grid.IndexOf(x)).ToList());
        if (response.IsSuccess)
        {
            //only update the colors for matching materials
            foreach (var scheme in SelectedSchemes.Where(x => x.MaterialId == getTopItem.MaterialId))
            {
                scheme.ColorSchemeId = getTopItem.ColorSchemeId;
                scheme.ColorScheme1 = getTopItem.ColorScheme1;
            }
        }
        ShowMessage(response.IsSuccess, response.Message);
        AddColorSchemeModal.Hide();
    }
    void OnRowRenderHandler(GridRowRenderEventArgs args)
    {
        MaterialColorSchemeDto item = args.Item as MaterialColorSchemeDto;

        args.Class = item.CanBeReplaced == true ? "positiveValuesRowFormatting" : "negativeValuesRowFormatting";
    }

    void OnCellRenderHandler(GridCellRenderEventArgs args)
    {
        args.Class = "highlightCellBackGroud";
    }

    async Task OnRowExpandHandler(GridRowExpandEventArgs args)
    {
        var data = args.Item as ColorSchemeElevationTreeModel;

        this.ColorSchemePickService.SubdivisionPlanElevation = new ElevationPlanSubdivisionModel
            {
                PlanOption = data.PlanOption
            };
    }

    async Task GetSubdivisionPlanElevation()
    {
        await this.InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        this.ColorSchemePickService.OnDataChanged -= GetSubdivisionPlanElevation;
    }

    async Task OnSelectHandler(FileSelectEventArgs args)
    {
        foreach (var file in args.Files)
        {
            var colorSchemesToAdd = await ColorSchemeService.ImportExcel(file);
            ImportedColorSchemes.AddRange(colorSchemesToAdd.Value);
            IsImported = true;
        }
    }
    async Task BeforeExport(GridBeforeExcelExportEventArgs args)
    {
        // Customize the Width of the exported column
        args.Columns[0].Width = "200px";
        args.Columns[1].Width = "100px";
        args.Columns[2].Width = "150px";
        args.Columns[3].Width = "50px";
        args.Columns[4].Width = "150px";
        args.Columns[5].Width = "150px";
        args.Columns[6].Width = "250px";
        args.Columns[7].Width = "200px";
        args.Columns[8].Width = "200px";        
    }
    async Task BeforePredefinedMaterialColorGridExport(GridBeforeExcelExportEventArgs args)
    {
        // Customize the Width of the exported column
        args.Columns[0].Width = "0px"; //MaterialColorPredefinedId
        args.Columns[1].Width = "200px"; // Material
        args.Columns[2].Width = "200px"; // Color
        args.Columns[3].Width = "0px"; // Image link
        args.Columns[4].Width = "200px"; // IsActive
    }
    async Task OnPredefinedMaterialColorSelectHandler(FileSelectEventArgs args)
    {
        IsLoading = true;

        if (args.Files.Count() > 1)
        {
            args.IsCancelled = true;
            ShowMessage(false, "Too many files. You can only upload one at a time.");
            return;
        }
        foreach (var file in args.Files)
        {
            var ImportedPredefinedMaterialColors = (await ColorSchemeService.ImportPredefinedMaterialColorExcel(file)).Value;
            var importSaveResponse = await ColorSchemeService.SavePredefinedMaterialColorsImportAsync(ImportedPredefinedMaterialColors);
            if (importSaveResponse.IsSuccess)
            {
                PredefinedMaterialColorGrid = (await ColorSchemeService.GetPredefinedMaterialColorCombinationsAsync()).Value;
                PredefinedMaterialColorGridRef.Rebind();
            }
            ShowMessage(importSaveResponse.IsSuccess, importSaveResponse.Message);
            
        }
        IsLoading = false;
    }
    async Task ApplyImportColorSchemes()
    {
        var response = await ColorSchemeService.SaveColorSchemeImportAsync(ImportedColorSchemes);
        ShowMessage(response.IsSuccess, response.Message);
        if(response.IsSuccess){
            ImportedColorSchemes = new List<ColorSchemeLoadTemplateDto>();//clear
            IsImported = false;
            ImportedFileRef.ClearFiles();
            var gridTask = ColorSchemeService.GetColorGridAsync();
            await Task.WhenAll(gridTask);
            Grid = gridTask.Result.Value;
        }      
        StateHasChanged();
    }
    async Task OnRemoveHandler(FileSelectEventArgs args)
    {
        ImportedColorSchemes = new List<ColorSchemeLoadTemplateDto>();//clear
        IsImported = false;
    }
    async Task EditColorGridHandler(GridCommandEventArgs args)
    {
        ColorGridModel item = (ColorGridModel)args.Item;
        EditedColorGridRow = item;
        // var editItem = EditedColorGridRow;

        if (item.MaterialId != null && item.MaterialId != CurrentMaterialId)
        {
            CurrentMaterialId = item.MaterialId;
            var getColors = await ColorSchemeService.GetColorsAsync((int)item.MaterialId);
            Colors = getColors.Value;
            Colors.Add(new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = "Add New" });
        }
        StateHasChanged();
    }
    async Task MaterialChangeHandler()
    {
        var editItem = EditedColorGridRow;
        if (EditedColorGridRow.MaterialId != null && EditedColorGridRow.MaterialId != CurrentMaterialId)
        {
            CurrentMaterialId = EditedColorGridRow.MaterialId;
            var getColors = await ColorSchemeService.GetColorsAsync((int)EditedColorGridRow.MaterialId);
            Colors = getColors.Value;
            Colors.Add(new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = "Add New" });
        }
        StateHasChanged();
    }
    async Task PredefinedMaterialChangeHandler()
    {
        var editItem = EditedPredefinedMaterialColorRow;
        if (EditedPredefinedMaterialColorRow.MaterialId != null && EditedPredefinedMaterialColorRow.MaterialId != CurrentMaterialId)
        {
            CurrentMaterialId = EditedPredefinedMaterialColorRow.MaterialId;
            var existingColorsForMaterial = PredefinedMaterialColorGrid.Where(x => x.Material.MateriaId == CurrentMaterialId).Select(x=>x.ColorScheme).ToList();
            Colors = PredefinedMaterialColorGrid.Select(x => x.ColorScheme).Where(x => !existingColorsForMaterial.Contains(x)).ToList();
            Colors.Insert(0, new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = "Add New" });
            StateHasChanged();
        }
    }
    async Task UpdateColorGridHandler(GridCommandEventArgs args)
    {
        ColorGridModel item = (ColorGridModel)args.Item;

        var editItem = EditedColorGridRow;
        if (EditedColorGridRow.ColorSchemeId == -1)
        {
            EditedColorGridRow.ColorScheme1 = NewGridColorName;
        }
        if (EditedColorGridRow.MaterialId == -1)
        {
            EditedColorGridRow.MaterialName = NewGridMaterialName;
        }
        var response = await ColorSchemeService.UpdateGridColorAsync(EditedColorGridRow);
        if (response.IsSuccess)
        {
            if (EditedColorGridRow.ColorSchemeId == -1)
            {
                var getColors = await ColorSchemeService.GetColorsAsync();
                Colors = getColors.Value;
                Colors.Add(new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = "Add New" });
            }
            if (EditedColorGridRow.MaterialId == -1)
            {
                var getMaterials = await ColorSchemeService.GetMaterialsAsync();
                Materials = getMaterials.Value;
                Materials.Add(new MaterialDto() { MateriaId = -1, Material1 = "Add New" });
            }
            var findItem = Grid.SingleOrDefault(x => x.Id == item.Id);
            if (findItem != null)
            {
                findItem.MaterialId = response.Value.MaterialId;
                findItem.MaterialName = Materials.SingleOrDefault(x => x.MateriaId == response.Value.MaterialId)?.Material1;
                findItem.ColorSchemeId = response.Value.ColorSchemeId;
                findItem.ColorScheme1 = Colors.SingleOrDefault(x => x.ColorShemeId == response.Value.ColorSchemeId)?.ColorScheme1;
            }

            ShowMessage(true, "Successfully updated color grid");
        }
        else
        {
            ShowMessage(false, "There's an error updating the color grid. If problem persists, <NAME_EMAIL>");
        }
        StateHasChanged();
    }
    async Task DeleteColorGridHandler(GridCommandEventArgs args)
    {
        ColorGridModel item = (ColorGridModel)args.Item;
        var response = await ColorSchemeService.DeleteGridColorAsync(item);
        if (response.IsSuccess)
        {
            Grid.Remove(item);
            ShowMessage(true, "Successfully updated color grid");
        }
        else
        {
            ShowMessage(false, "There's an error updating the color grid. If problem persists, <NAME_EMAIL>");
        }
        StateHasChanged();
    }
    private void UploadMaterialColorDocument()
    {
        UploadMaterialColorDocumentModal.Show();
        StateHasChanged();
    }
    private async Task HandleUploadMaterialColorDocumentSubmit(ResponseModel response)
    {
        UploadMaterialColorDocumentModal.Hide();
        PredefinedMaterialColorGrid = (await ColorSchemeService.GetPredefinedMaterialColorCombinationsAsync()).Value;
        PredefinedMaterialColorGridRef.Rebind();
        ShowMessage(response.IsSuccess, response.Message);
        StateHasChanged();
    }
    async void DownloadDocument(string fileName)
    {
        var documentDetails = new DocumentUploadModel()
            {
                FolderName = "materialcolorimages",
                FileName = fileName
            };

        var responseBytes = await MaterialColorDocumentService.DownloadDocumentAsync(documentDetails);

        await JS.InvokeAsync<object>("saveAsFile", fileName, Convert.ToBase64String(responseBytes.Value));
    }
    async void DeleteDocument(MaterialColorPredefinedDto item)
    {
        var deleteResponse = await MaterialColorDocumentService.DeleteDocumentAsync(item);
        PredefinedMaterialColorGrid = (await ColorSchemeService.GetPredefinedMaterialColorCombinationsAsync()).Value;
        PredefinedMaterialColorGridRef.Rebind();
        ShowMessage(deleteResponse.IsSuccess, deleteResponse.Message);
        StateHasChanged();
    }

    async Task OnActiveInactiveToggle(bool value, MaterialColorPredefinedDto materialColorCombination)
    {
        materialColorCombination.IsActive = value;
        var updateMaterialColorCombinationResponse = await ColorSchemeService.UpdatePredefinedMaterialColorCombinationAsync(materialColorCombination);
        PredefinedMaterialColorGridRef.Rebind();
    }

    private async Task ClearActiveInactiveFilterAsync(FilterMenuTemplateContext filterContext)
    {
        await filterContext.ClearFilterAsync();
        ActiveInactiveFilterCurrentSelection.Clear();
        PredefinedMaterialColorGridRef.Rebind();
    }

    private bool IsCheckboxInCurrentFilter(CompositeFilterDescriptor filterDescriptor, string option)
    {
        return ActiveInactiveFilterCurrentSelection.Contains(option);
    }

    private void UpdateActiveInactiveFilter(bool isChecked, string optionValue, FilterMenuTemplateContext context)
    {
        var filterDescriptor = context.FilterDescriptor;
        filterDescriptor.LogicalOperator = FilterCompositionLogicalOperator.Or;
        var value = optionValue == "Active" ? true : false;
        if (!isChecked)
        {
            // find and remove the filter descriptor for this checkbox
            var removeFilters = filterDescriptor.FilterDescriptors.Where(x =>
            {
                var fd = x as FilterDescriptor;
                if ((fd.Operator == FilterOperator.IsEqualTo && fd.Value == null) || (fd.Operator == FilterOperator.IsEqualTo && (bool)fd.Value == value))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }).ToList();
            foreach (var filter in removeFilters)
            {
                filterDescriptor.FilterDescriptors.Remove(filter);
            }
            ActiveInactiveFilterCurrentSelection.Remove(optionValue);
        }
        else
        {
            // add a filter descriptor for this checkbox
            filterDescriptor.FilterDescriptors.Add(new FilterDescriptor()
                {
                    Member = nameof(MaterialColorPredefinedDto.IsActive),
                    MemberType = typeof(bool),
                    Operator = FilterOperator.IsEqualTo,
                    Value = value
                });
            if (!ActiveInactiveFilterCurrentSelection.Contains(optionValue))
            {
                ActiveInactiveFilterCurrentSelection.Add(optionValue);
            }
        }
    }

    async Task OnMaterialColorStateInitHandler(GridStateEventArgs<MaterialColorPredefinedDto> args)
    {
        if (!ActiveInactiveFilterCurrentSelection.Contains("Active"))
        {
            ActiveInactiveFilterCurrentSelection.Add("Active");
        }

        var collapsedItemsState = new GridState<MaterialColorPredefinedDto>()
            {
                FilterDescriptors = new List<IFilterDescriptor>()
                {
                    new CompositeFilterDescriptor()
                    {
                        FilterDescriptors = new FilterDescriptorCollection()
                        {
                            new FilterDescriptor()
                            {
                                Member = nameof(MaterialColorPredefinedDto.IsActive),
                                MemberType = typeof(bool),
                                Operator = FilterOperator.IsEqualTo,
                                Value = true
                            }
                        }
                    }
                },
            };

        args.GridState = collapsedItemsState;
    }

    async Task CreatePredefinedMaterialColorHandler(GridCommandEventArgs args)
    {
        MaterialColorPredefinedDto item = (MaterialColorPredefinedDto)args.Item;

        item.Material = item.MaterialId == -1 ? new MaterialDto() { MateriaId = -1, Material1 = NewMaterialName } : Materials.FirstOrDefault(x => x.MateriaId == item.MaterialId);
        item.ColorScheme = item.ColorSchemeId == -1 ? new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = NewColorName } : Colors.FirstOrDefault(x => x.ColorShemeId == item.ColorSchemeId);
        var addMaterialColorPredefinedResponse = await ColorSchemeService.AddPredefinedMaterialColorCombinationAsync(item);
        PredefinedMaterialColorGrid = (await ColorSchemeService.GetPredefinedMaterialColorCombinationsAsync()).Value;
        PredefinedMaterialColorGridRef.Rebind();
        ShowMessage(addMaterialColorPredefinedResponse.IsSuccess, addMaterialColorPredefinedResponse.Message);
    }

    async Task UpdatePredefinedMaterialColorHandler(GridCommandEventArgs args)
    {
        MaterialColorPredefinedDto item = (MaterialColorPredefinedDto)args.Item;

        item.Material = item.MaterialId == -1 ? new MaterialDto() { MateriaId = -1, Material1 = NewMaterialName } : Materials.FirstOrDefault(x => x.MateriaId == item.MaterialId);
        item.ColorScheme = item.ColorSchemeId == -1 ? new ColorSchemeDto() { ColorShemeId = -1, ColorScheme1 = NewColorName } : Colors.FirstOrDefault(x => x.ColorShemeId == item.ColorSchemeId);
        var addMaterialColorPredefinedResponse = await ColorSchemeService.UpdatePredefinedMaterialColorCombinationAsync(item);
        PredefinedMaterialColorGrid = (await ColorSchemeService.GetPredefinedMaterialColorCombinationsAsync()).Value;
        PredefinedMaterialColorGridRef.Rebind();
        ShowMessage(addMaterialColorPredefinedResponse.IsSuccess, addMaterialColorPredefinedResponse.Message);
    }

    async void ShowMessage(bool success, string message)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}

