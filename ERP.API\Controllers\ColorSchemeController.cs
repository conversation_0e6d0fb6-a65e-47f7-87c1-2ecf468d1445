﻿
using AutoMapper;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Update;
using Microsoft.Graph.ApplicationsWithAppId;
using Microsoft.OpenApi.Any;
using NLog;
using System.Data;
using System.Diagnostics;
using System.Linq;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class ColorSchemeController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;

        public ColorSchemeController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
        }
        [HttpGet]
        public async Task<IActionResult> GetAllColorSchemesAsync()
        {
            try
            {

                var getAllSchemes = await _context.MaterialColorSchemes.Include(x => x.MaterialColorPredefined.Material).Include(x => x.MaterialColorPredefined.ColorScheme).Include(x => x.PlanOption).ThenInclude(x => x.PhasePlan).ThenInclude(x => x.Subdivision).Where(x => x.IsActive == true).ToListAsync();
                var colorSchemesDto = _mapper.Map<List<MaterialColorSchemeDto>>(getAllSchemes);
                var grouped = colorSchemesDto.GroupBy(x => new { ColorSchemeNum = x.ColorSchemeNum, PlanOptionId = x.PlanOptionId }).ToList();
                var groupedReturnData = grouped.Select(x => new ColorSchemeElevationTreeModel()
                {
                    Id = Guid.NewGuid(),
                    ColorSchemeName = x.Key.ColorSchemeNum,
                    OriginalColorSchemeName = $"{x.Key.ColorSchemeNum}-{x.First().PlanOption.ModifiedOptionDesc}-{x.First().PlanOption.PhasePlan.Subdivision.SubdivisionName}",
                   // MaterialColors = x.OrderByDescending(y => y.CanBeReplaced).ThenBy(y => y.Material?.Material1).ToList(),
                   MaterialColors = x.ToList(),
                    MaterialColorsPredefined = x.OrderByDescending(y => y.CanBeReplaced).ThenBy( y => y.MaterialColorPredefined.Material.Material1).Select(y => y.MaterialColorPredefined).ToList(),
                }).ToList();

                return new OkObjectResult(new ResponseModel<List<ColorSchemeElevationTreeModel>> { Value = groupedReturnData, IsSuccess = true });
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ColorSchemeElevationTreeModel>> { IsSuccess = false, Message = "Failed to get color schemes", Value = null });
            }

        }
        
        [HttpGet]
        public async Task<IActionResult> GetMaterialsAsync()
        {
            try
            {
                //All the distinct colors in the colors table, for use in a dropdown
                var getMaterial = await _context.Materials.Where(x => x.IsActive == true).ToListAsync();
                var distinctMaterials = getMaterial.DistinctBy(x => x.Material1);
                var materialsDto = _mapper.Map<List<MaterialDto>>(distinctMaterials);

                return new OkObjectResult(new ResponseModel<List<MaterialDto>> { Value = materialsDto, IsSuccess = true });
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MaterialDto>> { IsSuccess = false, Message = "Failed to get colors", Value = null });
            }

        }
        [HttpGet]
        public async Task<IActionResult> GetColorsAsync()
        {
            try
            {
                //All the distinct colors in the colors table, for use in a dropdown
                var getMasterColor = await _context.ColorSchemes.Where(x => x.IsActive == true).ToListAsync();
                var distinctColors = getMasterColor.DistinctBy(x => x.ColorScheme1);
                var colorSchemesDto = _mapper.Map<List<ColorSchemeDto>>(distinctColors);
                
                return new OkObjectResult(new ResponseModel<List<ColorSchemeDto>> { Value = colorSchemesDto, IsSuccess = true });
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ColorSchemeDto>> { IsSuccess = false, Message = "Failed to get colors", Value = null });
            }

        }
        [HttpGet("{materialId}")]
        public async Task<IActionResult> ColorsAsync(int materialId)
        {
            try
            {
                //All the distinct colors in the colors table, for use in a dropdown, 
                var getMasterColor = await _context.MaterialColorPredefineds.Include(x => x.ColorScheme).Where(x => x.IsActive == true && x.MaterialId == materialId).Select(x => x.ColorScheme).ToListAsync();
                var distinctColors = getMasterColor.DistinctBy(x => x.ColorScheme1);
                var colorSchemesDto = _mapper.Map<List<ColorSchemeDto>>(distinctColors);

                return new OkObjectResult(new ResponseModel<List<ColorSchemeDto>> { Value = colorSchemesDto, IsSuccess = true });
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ColorSchemeDto>> { IsSuccess = false, Message = "Failed to get colors", Value = null });
            }

        }
        [HttpGet]
        public async Task<IActionResult> GetPredefinedMaterialColorCombinationsAsync()
        {
            try
            {
                var combinations = await _context.MaterialColorPredefineds.Include(x => x.Material).Include(x => x.ColorScheme).ToListAsync();
                var combinationsDto = _mapper.Map<List<MaterialColorPredefinedDto>>(combinations);
                return new OkObjectResult(new ResponseModel<List<MaterialColorPredefinedDto>> { Value = combinationsDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MaterialColorPredefinedDto>> { IsSuccess = false, Message = "Failed to get Predefined Material Color combinations", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddPredefinedMaterialColorCombinationAsync([FromBody] MaterialColorPredefinedDto model)
        {
            try
            {
                var userName = User.Identity.Name.Split('@')[0];

                var materialIdForCombination = model.MaterialId;
                var colorIdForCombination = model.ColorSchemeId;

                if (model.MaterialId == -1 && !_context.Materials.Any(x => x.Material1 == model.Material.Material1))
                {
                    var newMaterial = new Material { Material1 = model.Material.Material1, CreatedBy = userName, IsActive = true };
                    await _context.SingleInsertAsync(newMaterial);
                    materialIdForCombination = newMaterial.MateriaId;
                }
                else
                {
                    materialIdForCombination = _context.Materials.First(x => x.Material1 == model.Material.Material1).MateriaId;
                }
                if (model.ColorSchemeId == -1 && !_context.ColorSchemes.Any(x => x.ColorScheme1 == model.ColorScheme.ColorScheme1))
                {
                    var newColor = new ColorScheme { ColorScheme1 = model.ColorScheme.ColorScheme1, CreatedBy = userName, IsActive = true };
                    await _context.SingleInsertAsync(newColor);
                    colorIdForCombination = newColor.ColorShemeId;
                }
                else
                {
                    colorIdForCombination = _context.ColorSchemes.First(x => x.ColorScheme1 == model.ColorScheme.ColorScheme1).ColorShemeId;
                }

                var materialColorPredefinedAlreadyExists = _context.MaterialColorPredefineds.Include(x => x.Material).Include(x => x.ColorScheme).FirstOrDefault(x => x.Material.MateriaId == materialIdForCombination && x.ColorScheme.ColorShemeId == colorIdForCombination);
                if (materialColorPredefinedAlreadyExists == null)
                {
                    var newMaterialColorCombination = new MaterialColorPredefined { MaterialId = materialIdForCombination, ColorSchemeId = colorIdForCombination, IsActive = true, CreatedBy = userName, CreatedDateTime = DateTime.Now };
                    await _context.SingleInsertAsync(newMaterialColorCombination);
                }
                else if (materialColorPredefinedAlreadyExists.IsActive == false)
                {

                    materialColorPredefinedAlreadyExists.IsActive = true;
                    materialColorPredefinedAlreadyExists.UpdatedBy = userName;
                    materialColorPredefinedAlreadyExists.UpdatedDateTime = DateTime.Now;
                    await _context.SingleUpdateAsync(materialColorPredefinedAlreadyExists);
                }
                else
                {
                    return Ok(new ResponseModel<MaterialColorPredefinedDto> { IsSuccess = true, Message = "Material Color Combination already exists", Value = model });
                }

                return Ok(new ResponseModel<MaterialColorPredefinedDto> { IsSuccess = true, Message = "Added Material Color Combination", Value = model });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MaterialColorPredefinedDto> { IsSuccess = false, Message = "Failed to update Predefined material color combination", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> UpdatePredefinedMaterialColorCombinationAsync([FromBody] MaterialColorPredefinedDto model)
        {
            try
            {
                var updateBy = User.Identity.Name.Split('@')[0];

                var materialIdForCombination = model.MaterialId;
                var colorIdForCombination = model.ColorSchemeId;

                if (model.MaterialId == -1 && !_context.Materials.Any(x => x.Material1 == model.Material.Material1))
                {
                    var newMaterial = new Material { Material1 = model.Material.Material1, CreatedBy = updateBy, IsActive = true };
                    await _context.SingleInsertAsync(newMaterial);
                    materialIdForCombination = newMaterial.MateriaId;
                }
                else
                {
                    materialIdForCombination = _context.Materials.First(x => x.Material1 == model.Material.Material1).MateriaId ;
                }
                if (model.ColorSchemeId == -1 && !_context.ColorSchemes.Any(x => x.ColorScheme1 == model.ColorScheme.ColorScheme1))
                {
                    var newColor = new ColorScheme { ColorScheme1 = model.ColorScheme.ColorScheme1, CreatedBy = updateBy, IsActive = true };
                    await _context.SingleInsertAsync(newColor);
                    colorIdForCombination = newColor.ColorShemeId;
                }
                else
                {
                    colorIdForCombination = _context.ColorSchemes.First(x => x.ColorScheme1 == model.ColorScheme.ColorScheme1).ColorShemeId;
                }

                var predefinedMaterialColor = _context.MaterialColorPredefineds.Where(x => x.MaterialColorPredefinedId == model.MaterialColorPredefinedId);
                await predefinedMaterialColor.ExecuteUpdateAsync(s => s.SetProperty(x => x.MaterialId, materialIdForCombination).SetProperty(x => x.ColorSchemeId, colorIdForCombination).SetProperty(x => x.IsActive, model.IsActive).SetProperty(x => x.UpdatedDateTime, DateTime.Now).SetProperty(x => x.UpdatedBy, updateBy));

                return Ok(new ResponseModel<MaterialColorPredefinedDto> { IsSuccess = true, Message = "update", Value = model });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MaterialColorPredefinedDto> { IsSuccess = false, Message = "Failed to update Predefined material color combination", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> LoadPredefinedMaterialColorsAsync([FromBody] List<MaterialColorPredefinedDto> predefinedMaterialColors)
        {
            try
            {
                var colorSchemes = await _context.MaterialColorSchemes.ToListAsync();
                var existingPredefinedMaterialColors = await _context.MaterialColorPredefineds.Include(x => x.Material).Include(x => x.ColorScheme).ToListAsync();
                foreach (var materialColor in predefinedMaterialColors)
                {

                    if (!existingPredefinedMaterialColors.Any(x => x.MaterialColorPredefinedId == materialColor.MaterialColorPredefinedId && x.Material.Material1 == materialColor.Material.Material1 && x.ColorScheme.ColorScheme1 == materialColor.ColorScheme.ColorScheme1))
                    {
                        if (materialColor.MaterialColorPredefinedId == -1 || colorSchemes.Any(x => x.MaterialColorPredefinedId == materialColor.MaterialColorPredefinedId))
                        {
                            await AddPredefinedMaterialColorCombinationAsync(materialColor);
                        }
                        else
                        {
                            await UpdatePredefinedMaterialColorCombinationAsync(materialColor);
                        }
                    }
                }
                return Ok(new ResponseModel<List<MaterialColorPredefinedDto>> { IsSuccess = true, Message = "imported material colors", Value = predefinedMaterialColors });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ColorSchemeLoadTemplateDto>> { IsSuccess = false, Message = "Failed to import material colors", Value = null });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetSubdivisionPlanElevationsAsync()
        {
            try
            {
                var getPlanElevation = await _context.AvailablePlanOptions.Include(x => x.MaterialColorSchemes).Include("PhasePlan.Subdivision").Include("PhasePlan.MasterPlan").Where(x => x.IsActive == true && x.IsElevation == "T").ToListAsync();
                var planElevationDto = _mapper.Map<List<AvailablePlanOptionDto>>(getPlanElevation);
                var grouped = getPlanElevation.GroupBy(x => new { PhasePlan = x.PhasePlan, Subdivision = x.PhasePlan.Subdivision }).ToList().GroupBy(x => new {x.Key.Subdivision}).ToList();
                var groupedReturnData = grouped.Select(x => new ColorSchemeElevationTreeModel()
                {
                    Subdivision = _mapper.Map<SubdivisionDto>(x.Key.Subdivision),
                    Children = x.Select(y => new ColorSchemeElevationTreeModel()
                    {
                        PhasePlan = _mapper.Map<PhasePlanDto>(y.Key.PhasePlan),
                        HasChilden = true,
                        Children = y.Select(z => new ColorSchemeElevationTreeModel()
                        {
                            PlanOption = _mapper.Map<AvailablePlanOptionDto>(z),
                            Children = z.MaterialColorSchemes.Where(y => y.IsActive == true).Select(x => new ColorSchemeElevationTreeModel() { ColorSchemeName = x.ColorSchemeNum}).DistinctBy(y => y.ColorSchemeName).ToList(),
                        }).ToList(),
                    }).ToList(),
                    PhasePlans = _mapper.Map<List<PhasePlanDto>>(x.Select( y=> y.Key.PhasePlan).ToList())
                }).ToList();

                return new OkObjectResult(new ResponseModel<List<ColorSchemeElevationTreeModel>> { Value = groupedReturnData, IsSuccess = true });
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MaterialColorSchemeDto>> { IsSuccess = false, Message = "Failed to get color schemes", Value = null });
            }

        }
        [HttpPost]
        public async Task<IActionResult> AddSchemesAsync([FromBody] List<MaterialColorSchemeDto> model)
        {
            try
            {
                var createBy = User.Identity.Name.Split('@')[0];               
                
                var newMaterials = model.Where(x => x.MaterialColorPredefined.Material.MateriaId == -1).Select(x => new Material()
                {
                    CreatedBy = createBy,
                    Material1 = x.MaterialColorPredefined.Material.Material1
                }).DistinctBy(x => new { x.Material1 }).ToList();
                //material might exist but in another materialcolorpredefined
                var materialsExist = _context.Materials.Where(x => newMaterials.Select(y => y.Material1).Contains(x.Material1)).ToList();
                var materialsToInsert = newMaterials.Where(x => !materialsExist.Any(y => y.Material1 == x.Material1)).ToList();

                await _context.Materials.BulkInsertAsync(materialsToInsert);

                //color might exist but in another materialcolorpredefined
                var newColors = model.Where(x => x.MaterialColorPredefined.ColorScheme.ColorShemeId == -1).ToList().Select(x => new ColorScheme()
                {
                    CreatedBy = createBy,
                    ColorScheme1 = x.MaterialColorPredefined.ColorScheme.ColorScheme1
                }).DistinctBy(x => new { x.ColorScheme1 }).ToList();
                var colorsExist = _context.ColorSchemes.Where(x => newColors.Select(y => y.ColorScheme1).Contains(x.ColorScheme1)).ToList();
                var colorsToInsert = newColors.Where(x => !colorsExist.Any(y => y.ColorScheme1 == x.ColorScheme1)).ToList();

                await _context.ColorSchemes.BulkInsertAsync(colorsToInsert);

                var newMaterialColorsPredefined = model.Where(x => x.MaterialColorPredefined.Material.MateriaId == -1 || x.MaterialColorPredefined.ColorScheme.ColorShemeId == -1).Select(x => new MaterialColorPredefined()
                {
                    MaterialId = _context.Materials.FirstOrDefault(y => y.Material1 == x.MaterialColorPredefined.Material.Material1).MateriaId,
                    ColorSchemeId = _context.ColorSchemes.FirstOrDefault(y => y.ColorScheme1 == x.MaterialColorPredefined.ColorScheme.ColorScheme1).ColorShemeId,
                    CreatedBy = createBy
                }).DistinctBy(x => new { x.MaterialId, x.ColorSchemeId }).ToList();

                var materialColorSchemesNotExist = newMaterialColorsPredefined.Where(x => !_context.MaterialColorPredefineds.ToList().Any(y => y.MaterialId == x.MaterialId && y.ColorSchemeId == x.ColorSchemeId)).ToList();

                await _context.MaterialColorPredefineds.BulkInsertAsync(materialColorSchemesNotExist);
                var findNewInsertedMaterialColorPredefineds = _context.MaterialColorPredefineds.Include(x => x.Material).Include(x => x.ColorScheme).Where(x => newMaterialColorsPredefined.Select(y => y.MaterialColorPredefinedId).Contains(x.MaterialColorPredefinedId)).ToList();


                var newSchemes = model.Select(x => new MaterialColorScheme()
                {
                    ColorSchemeNum = x.ColorSchemeNum,
                    PlanOptionId = x.PlanOptionId,
                    MaterialColorPredefinedId = x.MaterialColorPredefined.Material.MateriaId == - 1 || x.MaterialColorPredefined.ColorScheme.ColorShemeId == -1 ? _context.MaterialColorPredefineds.Include(x => x.Material).Include(x => x.ColorScheme).FirstOrDefault(y => y.Material.Material1 == x.MaterialColorPredefined.Material.Material1 && y.ColorScheme.ColorScheme1 == x.MaterialColorPredefined.ColorScheme.ColorScheme1).MaterialColorPredefinedId : _context.MaterialColorPredefineds.FirstOrDefault(y => y.MaterialId == x.MaterialColorPredefined.Material.MateriaId && y.ColorSchemeId == x.MaterialColorPredefined.ColorScheme.ColorShemeId).MaterialColorPredefinedId,//Too slow?                    
                    CreatedBy = createBy
                }).ToList();

                await _context.MaterialColorSchemes.BulkInsertAsync(newSchemes, options => options.IncludeGraph = true);

                var getSchemes = _context.MaterialColorSchemes.Include(x => x.PlanOption.PhasePlan.MasterPlan).Include(x => x.PlanOption.PhasePlan.Subdivision).Include(x => x.MaterialColorPredefined.Material).Include(x => x.MaterialColorPredefined.ColorScheme).Where(x => newSchemes.Select(y => y.MaterialColorSchemeId).Contains(x.MaterialColorSchemeId)).ToList();
                var getSchemesDto = _mapper.Map<List<MaterialColorSchemeDto>>(getSchemes);
                return Ok(new ResponseModel<List<MaterialColorSchemeDto>> { IsSuccess = true, Message = "Updated Color Schemes", Value = getSchemesDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MaterialColorSchemeDto>> { IsSuccess = false, Message = "Failed to update color scheme", Value = null });
            }
        }
        
        [HttpPost]
        public async Task<IActionResult> UpdateSchemeAsync([FromBody] ColorSchemeElevationTreeModel model)
        {
            try
            {
                //I think this should just update the name everywhere, not anything else
                var updateBy = User.Identity.Name.Split('@')[0];
                var findAll = _context.MaterialColorSchemes.Where(x => x.ColorSchemeNum == model.OriginalColorSchemeName);
                await findAll.ExecuteUpdateAsync(s => s.SetProperty(x => x.ColorSchemeNum, model.ColorSchemeName).SetProperty(x => x.UpdatedDateTime, DateTime.Now).SetProperty(x => x.UpdatedBy, updateBy));

                return Ok(new ResponseModel<ColorSchemeElevationTreeModel> { IsSuccess = true, Message = "update", Value = model });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ColorSchemeElevationTreeModel>> { IsSuccess = false, Message = "Failed to update color scheme", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> DeleteSchemeAsync([FromBody] ColorSchemeElevationTreeModel model)
        {
            try
            {
                //TODO: make the same update in all the ones with the same master scheme?
                var getScheme = _context.MaterialColorSchemes.Where(x => x.ColorSchemeNum == model.ColorSchemeName).ToList();
                foreach(var scheme in getScheme)
                {
                    scheme.IsActive = false;
                    scheme.UpdatedBy = User.Identity.Name.Split('@')[0];
                    scheme.UpdatedDateTime = DateTime.Now;
                }               
                _context.MaterialColorSchemes.UpdateRange(getScheme);
                await _context.SaveChangesAsync();
                //Update all the ones that use the same "master" scheme -- maybe not deactivate those???
                
                return Ok(new ResponseModel<ColorSchemeElevationTreeModel> { IsSuccess = true, Message = "update", Value = model });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MaterialColorSchemeDto>> { IsSuccess = false, Message = "Failed to update color scheme", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> DeleteSchemeFromPlanAsync([FromBody] ColorSchemeElevationTreeModel model)
        {
            try
            {
               //remove the scheme from the plan (deactivate)
                var getScheme = _context.MaterialColorSchemes.Where(x => x.ColorSchemeNum == model.ColorSchemeName && x.PlanOptionId == model.PlanOption.PlanOptionId).ToList();
                foreach (var scheme in getScheme)
                {
                    scheme.IsActive = false;
                    scheme.UpdatedBy = User.Identity.Name.Split('@')[0];
                    scheme.UpdatedDateTime = DateTime.Now;
                }
                _context.MaterialColorSchemes.UpdateRange(getScheme);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<ColorSchemeElevationTreeModel> { IsSuccess = true, Message = "update", Value = model });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MaterialColorSchemeDto>> { IsSuccess = false, Message = "Failed to update color scheme", Value = null });
            }
        }



        [HttpGet("{colorSchemeName}")]
        public async Task<IActionResult> GetPlansForSchemeAsync(string colorSchemeName)
        {
            var getPlans = _context.MaterialColorSchemes.Where(x => x.ColorSchemeNum == colorSchemeName && x.IsActive == true && x.PlanOptionId != 470538).Select(y => $"{y.PlanOption.PhasePlan.Subdivision.SubdivisionName} - {y.PlanOption.PhasePlan.MasterPlan.PlanName}").Distinct().ToList();
            
            //TODO: show subdivisions and plans, find a way to display them
            return Ok(getPlans);
        }

        [HttpPost]
        public async Task<IActionResult> AddMasterSchemeAsync([FromBody] MaterialColorSchemeDto model)
        {
            try
            {
               // var getScheme = _context.MaterialColorSchemes.Where(x => x.ColorSchemeNum == model.MaterialColorScheme.ColorSchemeNum).ToList();
                //var addScheme =  new MaterialColorScheme()
                //{
                //    MaterialId = x.MaterialId,
                //    ColorSchemeId = x.ColorSchemeId,
                //    PlanOptionId = 470538,
                //    CanBeReplaced = x.CanBeReplaced,
                //    ColorSchemeNum = x.ColorSchemeNum,
                //    CreatedBy = User.Identity.Name.Split('@')[0],
                //});
                //_context.MaterialColorSchemes.AddRange(addScheme);
                //await _context.SaveChangesAsync();
                return Ok(new ResponseModel<MaterialColorSchemeDto> { IsSuccess = true, Message = "Added", Value = model });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ElevationPlanSubdivisionModel>> { IsSuccess = false, Message = "Failed to add color scheme", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> LoadColorScheme([FromBody] List<ColorSchemeLoadTemplateDto> loadTemplates)
        {
            try
            {
                //loading from excel
                var userName = User.Identity.Name.Split('@')[0];
                //find and insert any new materials or colors
                var materailsAdded = loadTemplates.Select(x => x.Material.Trim()).Distinct().ToList();
                var allExistingMaterials = _context.Materials.Where(x => x.IsActive == true).Select(x =>x.Material1.Trim()).ToList();
                var newMaterials = materailsAdded.Where(x => !allExistingMaterials.Contains(x)).Select(x => new Material()
                {
                    Material1 = x,
                    CreatedBy = userName
                });
                await _context.Materials.BulkInsertAsync(newMaterials);

                var colorsAdded = loadTemplates.Select(x => x.ColorScheme.Trim()).Distinct().ToList();
                var allExistingColors = _context.ColorSchemes.Where(x => x.IsActive == true).Select(x => x.ColorScheme1.Trim()).ToList();
                var newColors = colorsAdded.Where(x => !allExistingColors.Contains(x)).Select(x => new ColorScheme()
                {
                    ColorScheme1 = x,
                    CreatedBy = userName
                });
                await _context.ColorSchemes.BulkInsertAsync(newColors);

                //now add any new needed materialcolor predefined combinations 
                var loadMaterialColorPredefined = loadTemplates.Select(x => new { material = x.Material.Trim(), color = x.ColorScheme.Trim() }).Distinct().ToList();
                var loadMaterialColorPredefined2 = loadTemplates.Select(x => new { material = x.Material.Trim(), color = x.ColorScheme.Trim() }).ToList();
                var predefinedExists = _context.MaterialColorPredefineds.Include(x => x.Material).Include(x => x.ColorScheme).Select(x => new { material = x.Material.Material1.Trim(), color = x.ColorScheme.ColorScheme1.Trim() }).ToList();
                var notExists = loadMaterialColorPredefined.Where(x => !predefinedExists.Contains(x)).ToList();
                var predefinedsToAdd = notExists.Select(x => new MaterialColorPredefined()
                {
                    MaterialId = _context.Materials.FirstOrDefault(y => y.Material1 == x.material).MateriaId,//TODO: slow
                    ColorSchemeId = _context.ColorSchemes.FirstOrDefault(y => y.ColorScheme1 == x.color).ColorShemeId,
                    CreatedBy = userName,
                }).ToList();
                await _context.MaterialColorPredefineds.BulkInsertAsync(predefinedsToAdd);

                //insert/update the materialcolorschems
                var optionsQuery = (from a in _context.AvailablePlanOptions.Where(x => x.IsActive == true)
                                   join b in _context.PhasePlans.Where(x => x.IsActive == true) on a.PhasePlanId equals b.PhasePlanId
                                   join c in _context.MasterPlans.Where(x => x.IsActive == true) on b.MasterPlanId equals c.MasterPlanId
                                   join d in _context.Subdivisions.Where(x => x.IsActive == true) on b.SubdivisionId equals d.SubdivisionId
                                   select new
                                   {
                                       PlanOptionId =  a.PlanOptionId,
                                       OptionCode = a.OptionCode,
                                       PlanCode =c.PlanNum,
                                       SubdivisionNumber = d.SubdivisionNum,
                                   }).ToList();

                var existingSchemesToUpdate = (from e in loadTemplates
                                       join b in optionsQuery on new { subdiv = e.SubdivisonNumber ?? "", e.OptionCode, plancode = e.PlanNumber ?? "" } equals new { subdiv = b.SubdivisionNumber, b.OptionCode, plancode = b.PlanCode }
                                       join f in _context.Materials.Where(x => x.IsActive == true) on new { Material = e.Material.Trim() } equals new { Material = f.Material1.Trim() }
                                       join h in _context.MaterialColorPredefineds.Where(x => x.IsActive == true) on new { m = f.MateriaId } equals new { m = h.MaterialId }
                                       join i in _context.MaterialColorSchemes.Where(x => x.IsActive == true) on new { e.ColorSchemeNum, h.MaterialColorPredefinedId, b.PlanOptionId } equals new { i.ColorSchemeNum, i.MaterialColorPredefinedId, i.PlanOptionId }
                                       join g in _context.ColorSchemes.Where(x => x.IsActive == true) on new { Color = e.ColorScheme.Trim() } equals new { Color = g.ColorScheme1.Trim() }
                                       join j in _context.MaterialColorPredefineds.Where(x => x.IsActive == true) on new { m = f.MateriaId, c = g.ColorShemeId } equals new { m = j.MaterialId, c = j.ColorSchemeId }
                                       select new
                                       { 
                                           MaterialColorScheme = new MaterialColorScheme()
                                           {
                                               MaterialColorSchemeId = i.MaterialColorSchemeId,
                                               ColorSchemeNum = i.ColorSchemeNum,
                                               PlanOptionId = i.PlanOptionId,
                                               MaterialColorPredefinedId = j.MaterialColorPredefinedId,//this is what is changing
                                               UpdatedBy = userName,
                                               UpdatedDateTime = DateTime.Now,
                                               CreatedBy = i.CreatedBy,
                                               CreatedDateTime = i.CreatedDateTime,
                                               IsActive = true,
                                               CanBeReplaced = i.CanBeReplaced
                                           },
                                           OptionCode = b.OptionCode,
                                           PlanNum = b.PlanCode,
                                           SubdivisionNum = b.SubdivisionNumber,
                                           ColorSchemeNum = i.ColorSchemeNum,
                                           Material = f.Material1                                           
                                       }).ToList();

                var updateSchemes = existingSchemesToUpdate.Select(x => x.MaterialColorScheme).ToList();
                await _context.MaterialColorSchemes.BulkUpdateAsync(updateSchemes);

                var newSchemesToInsert = loadTemplates.Where(x => !existingSchemesToUpdate.Any(y => y.Material == x.Material && y.OptionCode == x.OptionCode && y.SubdivisionNum == x.SubdivisonNumber && y.PlanNum == x.PlanNumber && y.ColorSchemeNum == x.ColorSchemeNum)).ToList();


                var insertSchemes = (
                                        from e in newSchemesToInsert
                                        join b in optionsQuery on new {subdiv = e.SubdivisonNumber ?? "", e.OptionCode, plancode = e.PlanNumber ?? "" } equals new {  subdiv = b.SubdivisionNumber,  b.OptionCode, plancode = b.PlanCode }
                                        join f in _context.Materials.Where(x => x.IsActive == true) on new { Material = e.Material.Trim() } equals new { Material = f.Material1.Trim() }
                                      join g in _context.ColorSchemes.Where(x => x.IsActive == true) on new { Color = e.ColorScheme.Trim() } equals new { Color = g.ColorScheme1.Trim() }
                                      join h in _context.MaterialColorPredefineds.Where(x => x.IsActive == true) on new { m = f.MateriaId, c = g.ColorShemeId} equals new { m = h.MaterialId, c = h.ColorSchemeId}
                                       select new MaterialColorScheme()
                                       {
                                           ColorSchemeNum = e.ColorSchemeNum,
                                           PlanOptionId = b.PlanOptionId,
                                           MaterialColorPredefinedId = h.MaterialColorPredefinedId,
                                           CreatedBy = userName
                                       }).ToList();
                await _context.MaterialColorSchemes.BulkInsertAsync(insertSchemes);

                return Ok(new ResponseModel<List<ColorSchemeLoadTemplateDto>>() { Value = loadTemplates, Message = "Successfully uploaded color scheme data", IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ColorSchemeLoadTemplateDto>> { IsSuccess = false, Message = "Failed to upload color scheme", Value = null });
            }

        }
        [HttpGet]
        public async Task<IActionResult> GetTable()
        {
            try
            {
                var colorGrid = (from a in _context.MaterialColorSchemes.Where(x => x.IsActive == true)
                                join b in _context.AvailablePlanOptions.Where(x => x.IsActive == true) on a.PlanOptionId equals b.PlanOptionId
                                join c in _context.MasterPlans on b.MasterPlanId equals c.MasterPlanId
                                join d in _context.Subdivisions on b.SubdivisionId equals d.SubdivisionId
                                join e in _context.MaterialColorPredefineds on a.MaterialColorPredefinedId equals e.MaterialColorPredefinedId
                                join f in _context.Materials on e.MaterialId equals f.MateriaId
                                join g in _context.ColorSchemes on e.ColorSchemeId equals g.ColorShemeId
                                select new ColorGridModel
                                {
                                    Id = a.MaterialColorSchemeId,
                                    ColorSchemeNum = a.ColorSchemeNum,
                                    SubdivisionName = d.SubdivisionName,
                                    SubdivisionNum = d.SubdivisionNum,
                                    PlanName = c.PlanName,
                                    PlanNum = c.PlanNum,
                                    PlanOptionName = b.ModifiedOptionDesc,
                                    OptionCode = b.OptionCode,
                                    MaterialName = f.Material1,
                                    ColorScheme1 = g.ColorScheme1,
                                    MaterialId = f.MateriaId,
                                    ColorSchemeId = g.ColorShemeId,
                                    PredefinedMaterialColorId = a.MaterialColorPredefinedId
                                }).ToList();
                return Ok(new ResponseModel<List<ColorGridModel>> { IsSuccess = true, Message = "update", Value = colorGrid });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ColorGridModel>> { IsSuccess = false, Message = "Failed to get grid", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> UpdateGridAsync([FromBody] ColorGridModel model)
        {
            try
            {
                if (model.ColorSchemeId == -1)
                {
                    //new color being added
                    //check new color isn't the same as an existing one with same material
                    //TODO: check capitalization and whitespace too to prevent duplicates
                    var findColor = _context.ColorSchemes.Where(x => x.ColorScheme1 == model.ColorScheme1).FirstOrDefault();
                    if (findColor == null)
                    {
                        var addColor = new ColorScheme()
                        {
                            ColorScheme1 = model.ColorScheme1,
                            CreatedBy = User.Identity.Name.Split('@')[0]
                        };
                        _context.ColorSchemes.Add(addColor);
                        await _context.SaveChangesAsync();
                        model.ColorSchemeId = addColor.ColorShemeId;//the new added id
                    }
                    else
                    {
                        model.ColorSchemeId = findColor.ColorShemeId;//the found id
                    }
                }
                if (model.MaterialId == -1)
                {
                    var findMaterial = _context.Materials.FirstOrDefault(x => x.Material1 == model.MaterialName);
                    if(findMaterial == null)
                    {
                        var addMaterial = new Material()
                        {
                            Material1 = model.MaterialName,
                            CreatedBy = User.Identity.Name.Split('@')[0]
                        };
                        _context.Materials.Add(addMaterial);
                        await _context.SaveChangesAsync();
                        model.MaterialId = addMaterial.MateriaId;
                    }
                    else
                    {
                        model.MaterialId = findMaterial.MateriaId;
                    }
                }
                var findMaterialColorPredefined = _context.MaterialColorPredefineds.FirstOrDefault(x => x.MaterialId == model.MaterialId && x.ColorSchemeId == model.ColorSchemeId);
                if (findMaterialColorPredefined == null)
                {
                    var addMaterialColorPredefined = new MaterialColorPredefined()
                    {
                        MaterialId = (int)model.MaterialId,
                        ColorSchemeId = (int)model.ColorSchemeId,
                        CreatedBy = User.Identity.Name.Split('@')[0]
                    };
                    _context.MaterialColorPredefineds.Add(addMaterialColorPredefined);
                    await _context.SaveChangesAsync();
                    findMaterialColorPredefined = addMaterialColorPredefined;
                }
                var updateMaterialColorScheme = await _context.MaterialColorSchemes.FindAsync(model.Id);
                if (updateMaterialColorScheme != null)
                {
                    updateMaterialColorScheme.MaterialColorPredefinedId = findMaterialColorPredefined.MaterialColorPredefinedId;
                    updateMaterialColorScheme.UpdatedBy = User.Identity.Name.Split('@')[0];
                    updateMaterialColorScheme.UpdatedDateTime = DateTime.Now;
                    _context.MaterialColorSchemes.Update(updateMaterialColorScheme);
                    await _context.SaveChangesAsync();
                }
                return Ok(new ResponseModel<ColorGridModel> { IsSuccess = true, Message = "update", Value = model });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MaterialColorSchemeDto>> { IsSuccess = false, Message = "Failed to update color scheme", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> DeleteGridAsync([FromBody] ColorGridModel model)
        {
            try
            {              
                var updateMaterial = await _context.MaterialColorSchemes.FindAsync(model.Id);
                if (updateMaterial != null)
                {
                    updateMaterial.IsActive = false;
                    updateMaterial.UpdatedBy = User.Identity.Name.Split('@')[0];
                    updateMaterial.UpdatedDateTime = DateTime.Now;
                    _context.MaterialColorSchemes.Update(updateMaterial);
                    await _context.SaveChangesAsync();
                }
                return Ok(new ResponseModel<ColorGridModel> { IsSuccess = true, Message = "deleted", Value = model });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MaterialColorSchemeDto>> { IsSuccess = false, Message = "Failed to update color scheme", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> DeleteGridsAsync([FromBody] List<ColorGridModel> model)
        {
            try
            {
                var updateby = User.Identity.Name.Split('@')[0];
                var updateMaterial =  _context.MaterialColorSchemes.Where(x => model.Select(y => y.Id).Contains(x.MaterialColorSchemeId));
                await updateMaterial.ExecuteUpdateAsync(s => s.SetProperty(b => b.UpdatedBy, updateby).SetProperty(b => b.UpdatedDateTime, DateTime.Now).SetProperty(b => b.IsActive, false));
                return Ok(new ResponseModel<List<ColorGridModel>> { IsSuccess = true, Message = "deleted", Value = model });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ColorGridModel>> { IsSuccess = false, Message = "Failed to update color scheme", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> UpdateGridsAsync([FromBody] List<ColorGridModel> model)
        {
            try
            {
                //bulk update to set a bunch of colors to the same color (sort of like fill down), need to update to check it's the same material
                var updateby = User.Identity.Name.Split('@')[0];
                //only update where the material is the same as the first or default
                var itemsToUpdate = model.Where(x => x.MaterialId == model.FirstOrDefault().MaterialId).ToList();
                var updateMaterial = _context.MaterialColorSchemes.Where(x => itemsToUpdate.Select(y => y.Id).Contains(x.MaterialColorSchemeId));
                var findMaterialColorPredefinedId = _context.MaterialColorPredefineds.FirstOrDefault(x => x.MaterialId == model.FirstOrDefault().MaterialId && x.ColorSchemeId == model.FirstOrDefault().ColorSchemeId).MaterialColorPredefinedId;
                await updateMaterial.ExecuteUpdateAsync(s => s.SetProperty(b => b.UpdatedBy, updateby).SetProperty(b => b.UpdatedDateTime, DateTime.Now).SetProperty(b => b.MaterialColorPredefinedId, findMaterialColorPredefinedId));
                return Ok(new ResponseModel<List<ColorGridModel>> { IsSuccess = true, Message = "updated", Value = model });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ColorGridModel>> { IsSuccess = false, Message = "Failed to update color scheme", Value = null });
            }
        }
    }
}
