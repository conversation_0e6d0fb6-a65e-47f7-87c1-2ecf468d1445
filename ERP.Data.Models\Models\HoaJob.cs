﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Ho<PERSON><PERSON>ob
{
    public int HoaAssessmentId { get; set; }

    public string JobNumber { get; set; } = null!;

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual HoaAssessment HoaAssessment { get; set; } = null!;

    public virtual Job JobNumberNavigation { get; set; } = null!;
}
