﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Buyer
{
    public int BuyerId { get; set; }

    public int ContractId { get; set; }

    public int? ContactId { get; set; }

    public byte ListOrder { get; set; }

    public string FirstName { get; set; } = null!;

    public string? MiddleName { get; set; }

    public string LastName { get; set; } = null!;

    public string Address { get; set; } = null!;

    public string City { get; set; } = null!;

    public string State { get; set; } = null!;

    public string Zip { get; set; } = null!;

    public string Country { get; set; } = null!;

    public string Email { get; set; } = null!;

    public string Phone { get; set; } = null!;

    public string? HomePhone { get; set; }

    public string? CellPhone { get; set; }

    public string? WorkPhone { get; set; }

    public virtual Contract Contract { get; set; } = null!;
}
