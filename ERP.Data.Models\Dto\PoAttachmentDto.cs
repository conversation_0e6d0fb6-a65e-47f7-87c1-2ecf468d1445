﻿using ERP.Data.Models.Abstract;

namespace ERP.Data.Models.Dto
{
    public class PoAttachmentDto : IMapFrom<PoAttachment>
    {
        public int PoattachmentId { get; set; }

        public int PoheaderId { get; set; }

        public string? Path { get; set; }

        public string? Name { get; set; }

        public int? DoctypeId { get; set; }

        public DateTime CreatedDateTime { get; set; }

        public DateTime? UpdatedDateTime { get; set; }

        public string? UpdatedBy { get; set; }

        public string? CreatedBy { get; set; }

        public bool? IsActive { get; set; }

        public virtual DoctypeDto? Doctype { get; set; }
    }
}
