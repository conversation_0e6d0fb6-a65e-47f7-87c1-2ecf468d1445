﻿@inject ScheduleService ScheduleService


<div class="card-header">
    <div class="row align-items-center">
        <div class="col">
            <h4 class="card-title">Incomplete Activities by Subdivision</h4>
        </div><!--end col-->
    </div>
</div>
<div class="card-body">
    <div class="">
        <TelerikChart OnSeriesClick="@OnSeriesClickHandler"
                      OnAxisLabelClick="@OnChartAxisLabelClick">
            <ChartTooltip Visible="true"></ChartTooltip>
            <ChartLegend Visible="false"></ChartLegend>

            <ChartSeriesItems>
                <ChartSeries Type="ChartSeriesType.Bar"
                             Name="Total Visits"
                             Data="@IncompleteSubdivisionActivities"
                             Field="Amount"
                             CategoryField="Subdivision.MarketingName">
                </ChartSeries>
            </ChartSeriesItems>

            <ChartValueAxes>
                <ChartValueAxis Max="250"></ChartValueAxis>
            </ChartValueAxes>

            <ChartCategoryAxes>
                <ChartCategoryAxis>
                    <ChartCategoryAxisLabels />
                </ChartCategoryAxis>
            </ChartCategoryAxes>
        </TelerikChart>
    </div>
</div>

@code {
    [Parameter]
    public EventCallback<int> OnIncompleteSubdivisionActivitiesObtained { get; set; }
    [Parameter]
    public EventCallback<IncompleteSubdivisionActivitiesDto> OnSeriesClicked { get; set; }
    public List<IncompleteSubdivisionActivitiesDto>? IncompleteSubdivisionActivities;
    public int TotalIncompleteActivities { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        var totalIncompleteActivitesBySubdivision = await ScheduleService.GetTotalIncompleteActivitiesBySubdivision();

        if (totalIncompleteActivitesBySubdivision != null)
        {
            if (totalIncompleteActivitesBySubdivision.Value != null)
            {
                IncompleteSubdivisionActivities = totalIncompleteActivitesBySubdivision.Value;
                foreach (var subdivision in IncompleteSubdivisionActivities)
                {
                    TotalIncompleteActivities += subdivision.Amount;
                }
                await OnIncompleteSubdivisionActivitiesObtained.InvokeAsync(TotalIncompleteActivities);
            }
        }
    }

    private async Task OnSeriesClickHandler(ChartSeriesClickEventArgs args)
    {
        var item = args.DataItem as IncompleteSubdivisionActivitiesDto;
        if (item is null)
            return;

        if (item.Activities is null)
        {
            var incompleteActivitiesBySubdivision = await ScheduleService.GetIncompleteActivitiesBySubdivision(item.Subdivision.SubdivisionId);
            if (incompleteActivitiesBySubdivision is not null)
            {
                item.Activities = incompleteActivitiesBySubdivision.Value;

                foreach (var job in item.Activities.Select(j => j.ScheduleM.Schedule.JobNumberNavigation))
                {
                    if (job is not null)
                        job.JobAndLotNumString = job.LotNumber != string.Empty ? job.JobAndLotNumString = job.JobNumber + " -- Lot: " + job.LotNumber : job.JobNumber;
                }
            }
        }

        await OnSeriesClicked.InvokeAsync(item);
    }

    private async Task OnChartAxisLabelClick(ChartAxisLabelClickEventArgs args)
    {
        var label = args.Value?.ToString();

        if (label is null)
            return;

        var item = IncompleteSubdivisionActivities?.FirstOrDefault(i => i.Subdivision.MarketingName == label);

        if (item is null)
            return;

        if (item.Activities is null)
        {
            var incompleteActivitiesBySubdivision = await ScheduleService.GetIncompleteActivitiesBySubdivision(item.Subdivision.SubdivisionId);
            if (incompleteActivitiesBySubdivision is not null)
            {
                item.Activities = incompleteActivitiesBySubdivision.Value;

                foreach (var job in item.Activities.Select(j => j.ScheduleM.Schedule.JobNumberNavigation))
                {
                    if (job is not null)
                        job.JobAndLotNumString = !string.IsNullOrEmpty(job.LotNumber)
                            ? job.JobNumber + " -- Lot: " + job.LotNumber
                            : job.JobNumber;
                }
            }
        }

        await OnSeriesClicked.InvokeAsync(item);
    }
}
