﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class SupplierCommunicationDto : IMapFrom<SupplierCommunication>
{
    public int SubCommId { get; set; }

    public int SubNumber { get; set; }

    public string? JobNumber { get; set; }

    public int? ScheduleAid { get; set; }

    public int? PoheaderId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool IsActive { get; set; }

    //public byte[]? RecordTimeStamp { get; set; }

    public string? MessageSubject { get; set; }

    public string? Message { get; set; }

    public string? SendTo { get; set; }

    public string? SendFrom { get; set; }

    public SupplierDto? SubNumberNavigation { get; set; }

    public List<SupplierCommunicationAttachDto>? SupplierCommunicationAttaches { get; set; }
}
