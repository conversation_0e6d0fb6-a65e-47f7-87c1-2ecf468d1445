﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class JobPostingGroup
{
    public string JobPostingGroupCode { get; set; } = null!;

    public string? JobPostingGroupDesc { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual ICollection<Job> Jobs { get; set; } = new List<Job>();

    public virtual ICollection<VpoGroupJpg> VpoGroupJpgs { get; set; } = new List<VpoGroupJpg>();
}
