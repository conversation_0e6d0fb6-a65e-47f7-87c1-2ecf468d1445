﻿@using ERP.Data.Models
@inject PoService PoService
@inject VPOItemPickService VPOItemPickService

<style type="text/css">
    .custom-file-select .k-file-validation-message {
        color: red;
        font-weight: bold;
        font-size: 0.95rem;
        padding: 4px 0;
    }
</style>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

@if (PODetailData == null)
{
    <p><em>Loading...</em></p>
    <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingOptions />
}
else
{
    <TelerikGrid Data="@PODetailData"
                 SelectionMode="GridSelectionMode.Single"
                 EditMode="@GridEditMode.Popup"
                 @ref="@PODetailRef">
 @*      <GridToolBarTemplate>
            @{
                if(!AvoidChanges??false) 
                {
                    GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Item</GridCommandButton>
                }
                <GridSearchBox DebounceDelay="200"></GridSearchBox>
            }
        </GridToolBarTemplate>
        *@ 
        <GridSettings>
            <GridPopupEditSettings Width="550px" MaxHeight="95vh" MaxWidth="95vw"></GridPopupEditSettings>
            <GridPopupEditFormSettings Context="PODetailContext">
                <FormTemplate>
                    @{
                        EditPoDetailDto = PODetailContext.Item as PodetailDto;

                        <TelerikForm Model="@EditPoDetailDto"
                                     ColumnSpacing="10px"
                                     Columns="1"
                                     ButtonsLayout="@FormButtonsLayout.Stretch"
                                     OnValidSubmit="@OnValidSubmit">
                            <FormItems>
                                <FormItem Field="Poitemdesc" LabelText="Description" Enabled="true"></FormItem>

                                @if (EditPoDetailDto.LastAuthDate != null)
                                {
                                    <FormItem Field="Poamount" LabelText="Total" Enabled="false"></FormItem>
                                }
                                else
                                {
                                    <FormItem Field="Poamount" LabelText="Total" Enabled="true"></FormItem>
                                }

                                <FormItem>
                                    <Template>
                                        <TelerikFileSelect AllowedExtensions="@AllowedExtensions"
                                                           MaxFileSize="@MaxFileSize"
                                                           OnSelect="@OnSelectHandler"
                                                           OnRemove="@OnRemoveHandler"
                                                           Class="custom-file-select" />
                                    </Template>
                                </FormItem>

                                <FormItem>
                                    <Template>
                                        <label for="variance">Purchasing Activity</label>
                                        <TelerikDropDownList Data="@PurchasingActivityData"
                                                             DefaultText="Select Purchasing Activity"
                                                             TextField="DropdownDescription" ValueField="PactivityId"
                                                             Enabled="false"
                                                             @bind-Value="@EditPoDetailDto.PurchasingActivityId"></TelerikDropDownList>
                                    </Template>
                                </FormItem>
                            </FormItems>
                            <FormButtons>
                                <TelerikButton Icon="@FontIcon.Save" Class="k-button-success">Save</TelerikButton>
                                <TelerikButton Icon="@FontIcon.Cancel" ButtonType="@ButtonType.Button" OnClick="@OnCancel">Cancel</TelerikButton>
                            </FormButtons>
                        </TelerikForm>
                    }
                </FormTemplate>
            </GridPopupEditFormSettings>
        </GridSettings>
        <GridColumns>
            <GridColumn Field="PhaseCode" Title="Phase Code" Editable="false"></GridColumn>
            <GridColumn Field="ItemNumber" Title="Item Number" Editable="false"></GridColumn>
            <GridColumn Field="Poitemdesc" Title="Description" Editable="true"></GridColumn>
            <GridColumn Field="Poitemnotes" Title="Notes" Editable="true"></GridColumn>
            <GridColumn Field="Poamount" DisplayFormat="{0:C2}" Title="Cost" Editable="true"></GridColumn>
            <GridColumn Field="CreatedBy" Title="Created By" Editable="false" Groupable="false" />
            <GridColumn Field="CreatedDateTime" Title="Created" Editable="false" Groupable="false" />
            <GridCommandColumn>
                @{
                    var row = context as PodetailDto;
                    if (row != null)
                    {
                        <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                        if (!row.IsPOApprovalStarted ?? false)
                        {
                            <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                        }
                        <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                        <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                    }
                }
            </GridCommandColumn>
        </GridColumns>
    </TelerikGrid>
}

@code {
    /// <summary>
    /// Properties
    /// </summary>
    [Parameter] public int POHeaderId { get; set; }
    [Parameter] public bool? AvoidChanges { get; set; }
    [Parameter] public string? PoJobNumber { get; set; }
    public List<PodetailDto>? PODetailData { get; set; }
    public bool IsLoadingOptions { get; set; } = false;
    private TelerikGrid<PodetailDto>? PODetailRef { get; set; }
    private PodetailDto EditPoDetailDto { get; set; }
   // private List<JccategoryDto> VarianceData { get; set; }
    private List<PactivityDto> PurchasingActivityData { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    List<string> AllowedExtensions { get; set; } = new List<string>()
    {
        ".jpeg", ".jpg",
        ".png", ".gif",
        ".heic",".pdf",
        ".doc", ".docx",
        ".xls", ".xlsx",
        ".csv",
        ".txt",
        ".rtf"
    };

    int MaxFileSize { get; set; } = 2 * 1024 * 1024; // 2 MB

    async Task OnSelectHandler(FileSelectEventArgs args)
    {
        foreach (var file in args.Files)
        {
            string fileExtension = Path.GetExtension(file.Name).ToLower(); // Get the file extension and convert it to lowercase
            if (AllowedExtensions.Contains(fileExtension))
            {
                var buffer = new byte[file.Stream.Length];
                await file.Stream.ReadAsync(buffer);
                EditPoDetailDto.Files.Add(new FileModel()
                    {
                        FileData = buffer,
                        FileName = file.Name
                    });
            }
            else
            {
                return;
            }
        }
    }

    async Task OnRemoveHandler(FileSelectEventArgs args)
    {
        foreach (var file in args.Files)
        {
            var fileToRemove = EditPoDetailDto.Files
                .FirstOrDefault(f => f.FileName == file.Name);

            if (fileToRemove != null)
            {
                EditPoDetailDto.Files.Remove(fileToRemove);
            }
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// Receiving parameter from clicked parent Grid
    /// </summary>
    /// <returns></returns>
    protected override async Task OnParametersSetAsync()
    {
        await LoadData();
    }

    private async Task OnValidSubmit()
    {
        if (EditPoDetailDto.PodetailId != 0)
        {
            var editResponse = await PoService.EditVPODetailAsync(EditPoDetailDto);
            ShowSuccessOrErrorNotification(editResponse.Message, editResponse.IsSuccess);
        }
        else
        {
            EditPoDetailDto.PoheaderId = POHeaderId;
            var addResponse = await PoService.AddVPODetailAsync(EditPoDetailDto);
            ShowSuccessOrErrorNotification(addResponse.Message, addResponse.IsSuccess);
        }

        await ExitEditAsync();

        var data = await PoService.GetVPODetailByPOHeaderIdAsync(POHeaderId);
        PODetailData = data.Value;

        this.VPOItemPickService.IsChanged = true;
    }

    private async Task OnCancel()
    {
        await ExitEditAsync();
    }

    private async Task LoadData()
    {
        var data = await PoService.GetVPODetailByPOHeaderIdAsync(POHeaderId);
        PODetailData = data.Value;

        // var varianceData = await PoService.GetVariances();
        // VarianceData = varianceData.Value;
        var pactivityData = await PoService.GetPurchasingActivitiesByJob(PoJobNumber);
        PurchasingActivityData = pactivityData.Value;
        // var pactivityData = await PoService.GetPurchasingActivities();
        // PurchasingActivityData = pactivityData.Value;
    }

    private async Task ExitEditAsync()
    {
        var state = PODetailRef?.GetState();
        state.OriginalEditItem = null;
        state.EditItem = null;
        state.InsertedItem = null;

        await PODetailRef?.SetStateAsync(state);
    }
    private void ShowSuccessOrErrorNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}