﻿@using ERP.Data.Models.Dto;
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject PlanService PlanService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Option To Plan
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@OptionToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <p>Select Option(s) to Add to Plan: <strong>@Plan.PlanName</strong></p>
            <p>
                <label>Option Group(s)</label>
                <TelerikMultiSelect Class="selected-items-container"
                                    Context="optionGroupMultiSelectContext"
                                    @ref="OptionGroupMultiSelectRef"
                                    ScrollMode="@DropDownScrollMode.Virtual"
                                    ItemHeight="30"
                                    PageSize="20"
                                    TextField="OptionGroupName"
                                    ValueField="OptionGroupId"
                                    TagMode="@MultiSelectTagMode.Multiple"
                                    MaxAllowedTags="5"
                                    Data="@AllOptionGroups"
                                    ClearButton="true"
                                    OnChange="CascadeOptions"
                                    @bind-Value="@SelectedOptionGroupIds"
                                    AutoClose="false"
                                    Filterable="true"
                                    FilterOperator="StringFilterOperator.Contains"
                                    Placeholder="Select Option Groups">
                    <HeaderTemplate>
                        <label style="padding: 4px 8px;">
                            <TelerikCheckBox TValue="bool"
                                             Value="@IsAllOptionGroupsSelected()"
                                             ValueChanged="@( (bool v) => ToggleOptionGroupSelectAll(v) )">
                            </TelerikCheckBox>
                            &nbsp;Select All
                        </label>
                    </HeaderTemplate>
                    <ItemTemplate>
                        <input type="checkbox"
                               class="k-checkbox k-checkbox-md"
                               checked="@GetOptionGroupsChecked(optionGroupMultiSelectContext.OptionGroupId)">
                        @optionGroupMultiSelectContext.OptionGroupName
                    </ItemTemplate>
                </TelerikMultiSelect>
            </p>
            <p>
                <label>Select Option(s)</label>
                <TelerikMultiSelect Class="selected-items-container"
                                    Context="multiSelectContext"
                                    @ref="MultiSelectRef"
                                    ScrollMode="DropDownScrollMode.Virtual"
                                    ItemHeight="30"
                                    PageSize="20"
                                    TextField="DisplayName"
                                    ValueField="AsmHeaderId"
                                    TagMode="@MultiSelectTagMode.Multiple"
                                    MaxAllowedTags="5"
                                    Data="@AllOptionsInGroup"
                                    ClearButton="true"
                                    @bind-Value="@SelectedOptionIds"
                                    AutoClose="false"
                                    Filterable="true"
                                    FilterOperator="StringFilterOperator.Contains"
                                    Placeholder="Select Options">
                    <HeaderTemplate>
                        <label style="padding: 4px 8px;">
                            <TelerikCheckBox TValue="bool"
                                             Value="@IsAllSelected()"
                                             ValueChanged="@( (bool v) => ToggleSelectAll(v) )">
                            </TelerikCheckBox>
                            &nbsp;Select All
                        </label>
                    </HeaderTemplate>
                    <ItemTemplate>
                        <input type="checkbox"
                               class="k-checkbox k-checkbox-md"
                               checked="@GetChecked(multiSelectContext.AsmHeaderId)">
                        @multiSelectContext.OptionCode - @multiSelectContext.OptionDesc
                    </ItemTemplate>
                </TelerikMultiSelect>
            </p>
            <p>
                <label>Also Apply to Master Plan(s)</label>
                <TelerikMultiSelect Class="selected-items-container"
                                    Context="masterPlanMultiSelectContext"
                                    @ref="MasterPlanMultiSelectRef"
                                    ScrollMode="DropDownScrollMode.Virtual"
                                    ItemHeight="30"
                                    PageSize="20"
                                    TextField="DisplayName"
                                    ValueField="MasterPlanId"
                                    TagMode="@MultiSelectTagMode.Multiple"
                                    MaxAllowedTags="5"
                                    Data="@AllMasterPlans"
                                    ClearButton="true"
                                    @bind-Value="@SelectedMasterPlanIds"
                                    AutoClose="false"
                                    Filterable="true"
                                    FilterOperator="StringFilterOperator.Contains"
                                    Placeholder="Select Plans">
                    <HeaderTemplate>
                        <label style="padding: 4px 8px;">
                            <TelerikCheckBox TValue="bool"
                                             Value="@IsAllMasterPlanSelected()"
                                             ValueChanged="@( (bool v) => ToggleSelectAllMasterPlan(v) )">
                            </TelerikCheckBox>
                            &nbsp;Select All
                        </label>
                    </HeaderTemplate>
                    <ItemTemplate>
                        <input type="checkbox"
                               class="k-checkbox k-checkbox-md"
                               checked="@GetMasterPlanChecked(masterPlanMultiSelectContext.MasterPlanId)">
                        @masterPlanMultiSelectContext.DisplayName
                    </ItemTemplate>
                </TelerikMultiSelect>
            </p>

            <button type="submit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                    </svg>
                </span> Update
            </button>
            <button type="button" @onclick="CancelAddItem" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-base">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel" aria-hidden="true">
                    <!--!-->
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M256 32c-50.3 0-96.8 16.6-134.1 44.6-17.2 12.8-32.4 28.1-45.3 45.3C48.6 159.2 32 205.7 32 256c0 123.7 100.3 224 224 224 50.3 0 96.8-16.6 134.1-44.6 17.2-12.8 32.4-28.1 45.3-45.3 28-37.4 44.6-83.8 44.6-134.1 0-123.7-100.3-224-224-224zm0 384c-88.2 0-160-71.8-160-160 0-32.6 9.8-62.9 26.6-88.2l221.6 221.6C318.9 406.2 288.6 416 256 416zm133.4-71.8L167.8 122.6C193.1 105.8 223.4 96 256 96c88.2 0 160 71.8 160 160 0 32.6-9.8 62.9-26.6 88.2z"></path><!--!-->
                    </svg><!--!-->
                    <!--!-->
                </span> Cancel
            </button>
            <div style=@submittingStyle>Adding options. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {
    [Parameter]
    public bool IsModalVisible { get; set; }

    public MasterOptionHeaderModel OptionToAdd { get; set; } = new MasterOptionHeaderModel();

    [Parameter]
    public MasterPlanDto Plan { get; set; }

    [Parameter]
    public EventCallback<ResponseModel<AddOptionToPlanModel>> HandleAddSubmit { get; set; }

    public List<HomeAreaDto>? AllHomeAreas { get; set; }
    public List<OptionGroupDto>? AllOptionGroups { get; set; }
    public List<MasterPlanDto>? AllMasterPlans { get; set; }
    public List<MasterOptionHeaderModel>? AllOptionsInGroup { get; set; }

    public List<int>? SelectedOptionGroupIds;
    public List<int>? CheckSelectedOptionGroupIds;
    public int SelectedOptionGroupId;
    public int CheckSelectedOptionGroupId;
    public int? SelectedHomeAreaId;
    public int SelectedOptionId;
    public List<int>? SelectedOptionIds;
    public List<int>? SelectedMasterPlanIds;
    public string? ErrorMessage;
    public bool? ShowError;

    private string submittingStyle = "display:none";

    private TelerikMultiSelect<MasterOptionHeaderModel, int>? MultiSelectRef;
    private TelerikMultiSelect<OptionGroupDto, int>? OptionGroupMultiSelectRef;
    private TelerikMultiSelect<MasterPlanDto, int>? MasterPlanMultiSelectRef;

    public async Task Show()
    {
        IsModalVisible = true;
        OptionToAdd = new MasterOptionHeaderModel();

        await LoadOptionGroupsAsync();
        await LoadMasterPlansAsync();

        AllHomeAreas = (await OptionService.GetHomeAreasAsync()).Value;

        AllOptionsInGroup = new List<MasterOptionHeaderModel>();

        SelectedOptionIds = new List<int>();
        SelectedOptionGroupIds = new List<int>();
        CheckSelectedOptionGroupIds = new List<int>();
        SelectedMasterPlanIds = new List<int>();

        StateHasChanged();
    }

    private async Task LoadOptionGroupsAsync()
    {
        var getGroups = await OptionService.GetOptionGroupsAsync();
        ShowError = getGroups.IsSuccess;
        ErrorMessage = getGroups.Message;
        AllOptionGroups = getGroups.Value;
    }

    private async Task LoadMasterPlansAsync()
    {
        var getMasterPlans = await PlanService.GetMasterPlansExceptAsync(Plan.MasterPlanId);
        ShowError = getMasterPlans.IsSuccess;
        ErrorMessage = getMasterPlans.Message;
        AllMasterPlans = getMasterPlans.Value;
    }

    private async Task CascadeOptions(object newVal)
    {
        if (!SelectedOptionGroupIds.SequenceEqual(CheckSelectedOptionGroupIds))
        {
            CheckSelectedOptionGroupIds = new List<int>();
            CheckSelectedOptionGroupIds.AddRange(SelectedOptionGroupIds);
            AllOptionsInGroup = new List<MasterOptionHeaderModel>();

            foreach (int groupId in SelectedOptionGroupIds)
            {
                //TODO: this will be slow, only change what actually changed
                //todo : parallel foreach
                var getOptionsForGroup = (await OptionService.GetMasterOptionsByGroupAsync(groupId)).Value;
                AllOptionsInGroup.AddRange(getOptionsForGroup);
            }

            MultiSelectRef.Rebind();

            StateHasChanged();
        }
    }

    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";

        var optionsToAdd = SelectedOptionIds.Select(x => new AsmHeaderModel()
            {
                AsmHeaderId = x,
                HomeAreaId = SelectedHomeAreaId //TODO: somehow have to be able to select different areas for different option, Home area not used for now
            }).ToList();

        var responseItem = await OptionService.AddOptionsToPlanAsync(optionsToAdd, Plan); //ORIGINAL
        //var responseItem = await OptionService.AddOptionsToPlanAndAdditionalAsync(optionsToAdd, Plan, SelectedMasterPlanIds);//11/14 Julie says she doesn't want it to add to existing communtiy plan, so switching back to Original

        submittingStyle = "display:none";
        SelectedOptionGroupId = 0;
        SelectedHomeAreaId = null;
        OptionToAdd = new MasterOptionHeaderModel();//clear for next use
        
        await HandleAddSubmit.InvokeAsync(responseItem);
    }

    void CancelAddItem()
    {
        OptionToAdd = new MasterOptionHeaderModel();//clear for next use
        SelectedOptionGroupId = 0;
        SelectedHomeAreaId = null;
        IsModalVisible = false;
    }

    public void Hide()
    {
        OptionToAdd = new MasterOptionHeaderModel();//clear for next use
        SelectedOptionGroupId = 0;
        SelectedHomeAreaId = null;
        IsModalVisible = false;
    }

    void ToggleSelectAll(bool selectAll)
    {
        SelectedOptionIds.Clear();

        if (selectAll)
        {
            SelectedOptionIds.AddRange(AllOptionsInGroup.Select(x => x.AsmHeaderId));
        }

        MultiSelectRef.Rebind();
    }

    bool IsAllSelected()
    {
        return SelectedOptionIds.Count == AllOptionsInGroup.Count;
    }

    // for the item checkboxes
    bool GetChecked(int selected)
    {
        return SelectedOptionIds.Contains(selected);
    }

    void ToggleOptionGroupSelectAll(bool selectAll)
    {
        SelectedOptionGroupIds.Clear();

        if (selectAll)
        {
            SelectedOptionGroupIds.AddRange(AllOptionGroups.Select(x => x.OptionGroupId));
        }

        OptionGroupMultiSelectRef.Rebind();
    }

    bool IsAllOptionGroupsSelected()
    {
        return SelectedOptionGroupIds.Count == AllOptionGroups.Count;
    }

    // for the item checkboxes
    bool GetOptionGroupsChecked(int selected)
    {
        return SelectedOptionGroupIds.Contains(selected);
    }

    bool IsAllMasterPlanSelected()
    {
        return SelectedMasterPlanIds.Count() == AllMasterPlans.Count;
    }

    bool GetMasterPlanChecked(int selected)
    {
        return SelectedMasterPlanIds.Contains(selected);
    }

    void ToggleSelectAllMasterPlan(bool selectAll)
    {
        SelectedMasterPlanIds.Clear();

        if (selectAll)
        {
            SelectedMasterPlanIds.AddRange(AllMasterPlans.Select(x => x.MasterPlanId));
        }

        MasterPlanMultiSelectRef.Rebind();
    }
}
