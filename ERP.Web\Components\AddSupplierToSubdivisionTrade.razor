﻿@using ERP.Data.Models.Dto

@inject SubdivisionService SubdivisionService
@inject TradeService TradeService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Supplier to Trade/Subdivision 
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@TradeToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />
            <p>Select Trade to For Supplier: @Supplier.SubName</p>
            <p><label>Trade</label>
            <TelerikDropDownList @bind-Value="@TradeToAdd.TradeId"
                                 Data="@AllTrades"
                                 TextField="TradeName"
                                 ValueField="TradeId"
                                 DefaultText="Select Trade"
                                 Filterable="true"
                                 Width="100%">
            </TelerikDropDownList>
            @*<TelerikNumericTextBox @bind-Value="@TradeToAdd.TradeId"></TelerikNumericTextBox>*@
            </p>
            <p>
                <label>Subdivision</label>
                <TelerikMultiSelect @bind-Value="@SelectedSubdivisionIds"
                                     Data="@AllSubdivisions"
                                        @ref="MultiSelectRef"
                                    Context="multiSelectContext"
                                        ClearButton="true"
                                     TextField="SubdivisionName"
                                     ValueField="SubdivisionId"
                                    AutoClose="false"
                                    Placeholder="Select Subdivisions"
                                     Filterable="true"
                                    TagMode="@MultiSelectTagMode.Multiple"
                                    MaxAllowedTags="5"
                                    FilterOperator="@StringFilterOperator.Contains"
                                     Width="100%">
                                      <HeaderTemplate>
                        <label style="padding: 4px 8px;">
                            <TelerikCheckBox TValue="bool"
                                             Value="@IsAllSelected()"
                                             ValueChanged="@( (bool v) => ToggleSelectAll(v) )">
                            </TelerikCheckBox>
                            &nbsp;Select All
                        </label>
                    </HeaderTemplate>
                    <ItemTemplate>
                        <input type="checkbox"
                               class="k-checkbox k-checkbox-md"
                               checked="@GetChecked(multiSelectContext.SubdivisionId)">
                        @multiSelectContext.SubdivisionName 
                    </ItemTemplate>
                </TelerikMultiSelect>
            </p>
            <p>
                <label>Default?</label>
                <TelerikCheckBox @bind-Value="@TradeToAdd.IsDefault"/>
            </p>
            
            <br/>
            <div style=@submittingStyle>Adding. Please wait...</div>
            <button type="submit" class="btn btn-primary">Add Supplier to Trade/Subdivision</button>                   
            <button type="button" @onclick="CancelAddItem" class="btn btn-secondary">Cancel</button> 
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public TradeSupplierModel TradeToAdd { get; set; } = new TradeSupplierModel();

    [Parameter]
    public TradeSupplierModel Supplier { get; set; }

    [Parameter]
    public EventCallback<ResponseModel<TradeSupplierModel>> HandleAddSubmit { get; set; }

    public List<TradeSupplierModel>? AllTrades { get; set; }
    public List<SubdivisionDto>? AllSubdivisions { get; set; }
    public List<int>? SelectedSubdivisionIds { get; set; } = new List<int>();
    public int SelectedSupplierId;
    private string submittingStyle = "display:none";
    private TelerikMultiSelect<SubdivisionDto, int>? MultiSelectRef;

    public async Task Show()
    {
        IsModalVisible = true;              
        AllTrades = (await TradeService.GetTradesAsync()).Value;
        var response = await SubdivisionService.GetSubdivisionsAsync();
        AllSubdivisions = response.Value;
        StateHasChanged();
    }


    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";     
        TradeToAdd.SubNumber = Supplier.SubNumber;
        TradeToAdd.SubdivisionIds = SelectedSubdivisionIds;
        var responseItem = await TradeService.AddSupplierToTradeAndSubdivisions(TradeToAdd);
        submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(responseItem);
    }
    async void CancelAddItem()
    {
        IsModalVisible = false;
    }
    public void Hide()
    {
        IsModalVisible = false;        
    }
    void ToggleSelectAll(bool selectAll)
    {
        SelectedSubdivisionIds.Clear();

        if (selectAll)
        {
            SelectedSubdivisionIds.AddRange(AllSubdivisions.Select(x => x.SubdivisionId));
        }

        MultiSelectRef.Rebind();
    }

    bool IsAllSelected()
    {
        return SelectedSubdivisionIds.Count == AllSubdivisions.Count;
    }

    // for the item checkboxes
    bool GetChecked(int selected)
    {
        return SelectedSubdivisionIds.Contains(selected);
    }
}
