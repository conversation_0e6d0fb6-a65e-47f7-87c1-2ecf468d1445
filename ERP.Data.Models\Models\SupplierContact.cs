﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class SupplierContact
{
    public int SupplierContactId { get; set; }

    public int SubNumber { get; set; }

    public int ContactId { get; set; }

    public string? SupplierContactTitle { get; set; }

    public string? SupplierContactPurch { get; set; }

    public string? SupplierContactSched { get; set; }

    public string? SupplierContactIsadmin { get; set; }

    public int? SupplierContactNumber { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public string? BcContactId { get; set; }

    public virtual Contact Contact { get; set; } = null!;

    public virtual Supplier SubNumberNavigation { get; set; } = null!;
}
