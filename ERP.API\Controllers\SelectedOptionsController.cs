﻿using AutoMapper;
using Azure.Identity;
using DocumentFormat.OpenXml.Drawing.Charts;
using DocumentFormat.OpenXml.ExtendedProperties;
using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using DocumentFormat.OpenXml.Spreadsheet;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.RenderTree;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Identity.Abstractions;
using Newtonsoft.Json;
using NLog;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Xml;
using System.Xml.Serialization;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using static Microsoft.Graph.CoreConstants;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class SelectedOptionsController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        private readonly IDownstreamApi _downstreamAPI;
        public SelectedOptionsController(IConfiguration configuration, ErpDevContext context, IMapper mapper, IDownstreamApi downstreamWebApi)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _downstreamAPI = downstreamWebApi;
        }
        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> SelectedOptionsAsync(string jobNumber)
        {
            try
            {
                //Getting the options to show for approval
                //if keep options selected, do the options get added to the spec? If so, this is ok

                
                var allOptions = new List<TbBuiltOption>();
              //  var getContractOptions = _context.Contracts.Include(x => x.Envelopes).ThenInclude(x => x.DocuSignSignatureTemplate).Include(x => x.Cancellation).Include(x => x.BuiltOptions).ThenInclude(x => x.OptionType).Include(x => x.BuiltOptions).ThenInclude(x => x.OptionGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeItem).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.PlanOption).ThenInclude(x => x.PhasePlan.MasterPlan).Where(x => x.SelectedLotId == jobNumber && x.Cancellation == null).SelectMany(x => x.BuiltOptions).ToList();

                //var getContractOptions = _context.Contracts.AsNoTrackingWithIdentityResolution().Include(x => x.Envelopes).ThenInclude(x => x.DocuSignSignatureTemplate).Include(x => x.Cancellation).Include(x => x.BuiltOptions).ThenInclude(x => x.OptionType).Include(x => x.BuiltOptions).ThenInclude(x => x.OptionGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeItem).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.PlanOption).ThenInclude(x => x.PhasePlan.MasterPlan).Where(x => x.SelectedLotId == jobNumber && x.Cancellation == null && x.Envelopes.Any(y => y.VoidedDate == null && y.SignedDate != null && y.DocuSignSignatureTemplate.TemplateTypeId == 1)).SelectMany(x => x.BuiltOptions).ToList();//template type id = 1 is Sales Agreement

                //11/21 added get opAttrGroupItem and AttrGroupAssignment
                var getContractOptions = _context.Contracts.AsNoTrackingWithIdentityResolution().AsSplitQuery().Include(x => x.Envelopes).ThenInclude(x => x.DocuSignSignatureTemplate).Include(x => x.Cancellation).Include(x => x.BuiltOptions).ThenInclude(x => x.OptionType).Include(x => x.BuiltOptions).ThenInclude(x => x.OptionGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeItem).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeItem).Include(x => x.BuiltOptions).ThenInclude(x => x.PlanOption).ThenInclude(x => x.PhasePlan.MasterPlan).Where(x => x.SelectedLotId == jobNumber && x.Cancellation == null && x.Envelopes.Any(y => y.VoidedDate == null && y.SignedDate != null && y.DocuSignSignatureTemplate.TemplateTypeId == 1)).SelectMany(x => x.BuiltOptions).ToList();//template type id = 1 is Sales Agreement

                //3/3/25 Chris says IsActive false on tbBuiltOptions refers to whether AvailablePlanOption was active or not at the time of selection, and those are still ones that should be used. Removed condition for tbbuiltOption is active true

                //var test = getContractOptions.Where(x => x.OptionDesc.StartsWith("OAK STAIR")).ToList();
                
                allOptions.AddRange(getContractOptions);

                //if (!getContractOptions.Any())
                //{
                //    //spec options get copied to contract if there is a contract on the spec, so get the spec options to approve only if there was no contract
                //    var getSpecOptions = _context.Specs.AsNoTrackingWithIdentityResolution().Include(x => x.BuiltOptions).ThenInclude(x => x.OptionType).Include(x => x.BuiltOptions).ThenInclude(x => x.OptionGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeItem).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeItem).Include(x => x.BuiltOptions).ThenInclude(x => x.PlanOption).ThenInclude(x => x.PhasePlan.MasterPlan).Where(x => x.JobId == jobNumber).SelectMany(x => x.BuiltOptions).ToList();
                //    allOptions.AddRange(getSpecOptions);
                //}
                var getSpecOptions = _context.Specs.AsNoTrackingWithIdentityResolution().Include(x => x.BuiltOptions).ThenInclude(x => x.OptionType).Include(x => x.BuiltOptions).ThenInclude(x => x.OptionGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeItem).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeItem).Include(x => x.BuiltOptions).ThenInclude(x => x.PlanOption).ThenInclude(x => x.PhasePlan.MasterPlan).Where(x => x.JobId == jobNumber).SelectMany(x => x.BuiltOptions).ToList();
                allOptions.AddRange(getSpecOptions);


                //  allOptions.RemoveAll(x => x.QtyDelta == 0 && !x.BuildAttributeItems.Any());//buying spec adds copies the options from spec to contract, but with qty delta = 0, trying to exclude those to avoid approve twice, if could also find ones where the attributes exist but did not change, then it should be good
                //for now it doesn't seem terrible if denise approves the ones where it looks like attributes changed but really they didn't

                //TODO: custom options
                //TODO: some options missing plan option id in tbbuilt. Try to get those by plan code and option code. 

                var ids = allOptions.Select(x => x.Id);
              //  var test3 = allOptions.ToList();
                var missingPlanOption = allOptions.Where(x => x.PlanOption == null && x.PlanCode != null && x.OptionCode != null).ToList();

              //  var test4 = allOptions.Where(x => x.OptionDesc.Contains("Appliance")).ToList();

                var getSubdivisionId = _context.Jobs.AsNoTracking().SingleOrDefault(x => x.JobNumber == jobNumber)?.SubdivisionId;
                var planOptionsInSubdivision = _context.AvailablePlanOptions.AsNoTracking().Include(x => x.PhasePlan.MasterPlan).Where(x => x.SubdivisionId == getSubdivisionId).ToList();
                var findPlanOption = (from a in missingPlanOption
                                     join b in planOptionsInSubdivision on new { optCode = a.OptionCode, planCode = a.PlanCode ?? ""} equals new { optCode = b.OptionCode, planCode = b.PhasePlan.MasterPlan.PlanNum}
                                     select new TbBuiltOption()
                                     {
                                         Id = a.Id,
                                         LotId = a.LotId,
                                         PlanOptionId = b.PlanOptionId,
                                         PlanOption = b,
                                         OptionTypeId = a.OptionTypeId,
                                         OptionGroupId = a.OptionGroupId,
                                         Qty = a.Qty,
                                         QtyDelta = a.QtyDelta,
                                         Price = a.Price,
                                         PriceDelta = a.PriceDelta,
                                         OptionCode = a.OptionCode,
                                         OptionDesc = a.OptionDesc,
                                         Elevation = a.Elevation,
                                         Removed = a.Removed,
                                         PrintDate = a.PrintDate,
                                         ChangeDate = a.ChangeDate,
                                         MostRecent = a.MostRecent,
                                         LastChanged = a.LastChanged,
                                         LastUser = a.LastUser,
                                         BuilderApproved = a.BuilderApproved,
                                         BuildAttributeItems = a.BuildAttributeItems,
                                         JobNumber = a.JobNumber,
                                     }).ToList();

                //remove the ones that were missing plan optionid but had plancode and option code and add the ones where planoptionid is fixed, 
                allOptions = allOptions.Except(missingPlanOption).ToList();
                allOptions.AddRange(findPlanOption);
                //TODO: what if plan option is null. it will be null on custom option. above will fail

                //TODO: can't do this for custom
                var removeMissingPlanOption = allOptions.Where(x => x.PlanOption == null && x.PlanCode == null && x.OptionCode == null).ToList();
                allOptions = allOptions.Except(removeMissingPlanOption).ToList();

                allOptions = allOptions.OrderBy(x => x.PlanOption?.OptionCode).ThenBy(x => x.OptionDesc).ThenByDescending(x => x.MostRecent).ThenByDescending(x => x.CreatedDateTime).ToList();
               
                

                //if buy spec, those options get rewritten but with qty delta zero

                //qty delta 0 could be attribute change, denise approve need to write new attributes 

                //TODO: still need to handle cancel don't keep options case - 


                //allOptions.AddRange(getSpecOptions);
                //allOptions.AddRange(getContractOptions);

                var selectedOptionsDto = _mapper.Map<List<TbBuiltOptionDto>>(allOptions);
                foreach (var item in selectedOptionsDto)
                {
                    item.BoolBuilderApproved = item.BuilderApproved == 1;
                    item.NeedsApproval = item.BuilderApproved != 1 && item.MostRecent && ((item.Removed || item.Qty == 0) ? item.PlanOptionId != null ? selectedOptionsDto.Any(x => x.PlanOption?.OptionCode == item.PlanOption?.OptionCode && x.BuilderApproved == 1 && x.CreatedDateTime <= item.CreatedDateTime) : selectedOptionsDto.Any(x => x.OptionDesc == item.OptionDesc && x.BuilderApproved == 1 && x.CreatedDateTime <= item.CreatedDateTime) : true);//it needs approval if it is a remove and if the same option exists and was approved, or if it's not a remove
                    item.OptionCode = $"{item.PlanOption?.PhasePlan?.MasterPlan?.PlanNum}{item.PlanOption?.OptionCode}";//for display
                    item.BoolFromSpec = item.FromSpec == 1;
                    if (item.BuildAttributeItems != null && item.BuildAttributeItems.Any(x => x.OpAttrGroupItem != null))
                    {
                        foreach (var attr in item.BuildAttributeItems.Where(x => x.OpAttrGroupItem != null))
                        {
                            attr.AttrGroupAssignment = attr.OpAttrGroupItem.AttrGroupAssignment;//to handle either oppAttrGroupItem filled or attrgroupassignment filled
                        }                       
                    }
                    if(item.BuildAttributeItems != null && item.BuildAttributeItems.Any())
                    {
                        item.BuildAttributeItems = item.BuildAttributeItems.Where(x => !string.IsNullOrWhiteSpace(x.AttrGroupAssignment?.AttributeItem?.Description) && x.AttrGroupAssignment.AttributeItem.Description != "Selection Needed" && x.AttrGroupAssignment.AttributeItem.Description != "Not Applicable").ToList();//exclude blank selections
                    }
                    //TODO: getSpecOptions need to mark from spec true - not sure if this is being filled
                }
                return new OkObjectResult(new ResponseModel<List<TbBuiltOptionDto>> { Value = selectedOptionsDto, IsSuccess = true, Message = "Selected options fetched successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<TbBuiltOptionDto>> { IsSuccess = false, Message = "Error getting options data" });
            }

        }
        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> SelectedOptionsAllAsync(string jobNumber)
        {
            try
            {
                //above one only gets ones from cms, this gets the old ss ones as well 
                var allOptions = new List<TbBuiltOption>();
                var getSpecOptions = _context.Specs.AsNoTracking().Include(x => x.BuiltOptions).ThenInclude(x => x.OptionType).Include(x => x.BuiltOptions).ThenInclude(x => x.OptionGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeItem).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeItem).Include(x => x.BuiltOptions).ThenInclude(x => x.PlanOption).ThenInclude(x => x.PhasePlan.MasterPlan).Include(x => x.BuiltOptions).ThenInclude(x => x.PlanOption).Where(x => x.JobId == jobNumber).SelectMany(x => x.BuiltOptions).OrderBy(x => x.OptionCode).ThenBy(x => x.OptionDesc).ThenByDescending(x => x.CreatedDateTime).ToList();

                var getContractOptions = _context.Contracts.AsNoTracking().Include(x => x.Envelopes).ThenInclude(x => x.DocuSignSignatureTemplate).Include(x => x.Cancellation).Include(x => x.BuiltOptions).ThenInclude(x => x.OptionType).Include(x => x.BuiltOptions).ThenInclude(x => x.OptionGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeItem).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeGroup).Include(x => x.BuiltOptions).ThenInclude(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeItem).Include(x => x.BuiltOptions).ThenInclude(x => x.PlanOption).ThenInclude(x => x.PhasePlan.MasterPlan).Include(x => x.BuiltOptions).ThenInclude(x => x.PlanOption).Where(x => x.SelectedLotId == jobNumber && x.Cancellation == null && x.Envelopes.Any(y => y.VoidedDate == null && y.SignedDate != null && y.DocuSignSignatureTemplate.TemplateTypeId == 1)).SelectMany(x => x.BuiltOptions).OrderBy(x => x.OptionCode).ThenBy(x => x.OptionDesc).ThenByDescending(x => x.CreatedDateTime).ToList();//ratified date not null is a real contract, else it might just be scenario, cancellation null to not get cancelled customer options //template type id = 1 is Sales Agreement

                var getSSOptions = await _context.TbBuiltOptions.AsNoTracking().Include(x => x.OptionType).Include(x => x.OptionGroup).Include(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeItem).Include(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeGroup).Include(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeGroup).Include(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeItem).Include(x => x.PlanOption).ThenInclude(x => x.PhasePlan.MasterPlan).Include(x => x.PlanOption).Where(x => x.JobNumber == jobNumber).OrderBy(x => x.OptionCode).ThenBy(x => x.OptionDesc).ThenByDescending(x => x.CreatedDateTime).ToListAsync();
               
                allOptions.AddRange(getSpecOptions);
                allOptions.AddRange(getContractOptions);
                allOptions.AddRange(getSSOptions);

                var selectedOptionsDto = _mapper.Map<List<TbBuiltOptionDto>>(allOptions);
                foreach (var item in selectedOptionsDto)
                {
                    item.BoolBuilderApproved = item.BuilderApproved == 1;
                    item.NeedsApproval = item.BuilderApproved != 1 && item.MostRecent && (item.Removed ? selectedOptionsDto.Any(x => x.PlanOption?.OptionCode == item.PlanOption?.OptionCode && x.BuilderApproved == 1 && x.ChangeDate <= item.ChangeDate) : true);
                    item.OptionCode = item.PlanOption?.OptionCode ?? $"{item.PlanCode}{item.OptionCode}";
                    if (item.BuildAttributeItems != null && item.BuildAttributeItems.Any(x => x.OpAttrGroupItem != null))
                    {
                        foreach (var attr in item.BuildAttributeItems.Where(x => x.OpAttrGroupItem != null))
                        {
                            attr.AttrGroupAssignment = attr.OpAttrGroupItem.AttrGroupAssignment;//to handle either oppAttrGroupItem filled or attrgroupassignment filled
                        }
                    }
                    if (item.BuildAttributeItems != null && item.BuildAttributeItems.Any())
                    {
                        item.BuildAttributeItems = item.BuildAttributeItems.Where(x => !string.IsNullOrWhiteSpace(x.AttrGroupAssignment?.AttributeItem?.Description) && x.AttrGroupAssignment.AttributeItem.Description != "Selection Needed" && x.AttrGroupAssignment.AttributeItem.Description != "Not Applicable").ToList();//exclude blank selections
                    }
                }
                var missingCode = selectedOptionsDto.Where(x => x.OptionCode == null).ToList();
                return new OkObjectResult(new ResponseModel<List<TbBuiltOptionDto>> { Value = selectedOptionsDto, IsSuccess = true, Message = "Selected options fetched successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<TbBuiltOptionDto>> { IsSuccess = false, Message = "Error getting options data" });
            }

        }

        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> SelectedOptionsBuyerAsync(string jobNumber)
        {
            try
            {
                //Todo: envelope to get status

                //TODO: if cancellation, how to push clearing the buyer from ERP?
                //TODO: show if it's spec with no buyer yet.
                //get customer associated with the job in CMS
                //var getspec = _context.Specs.Include(x => x.BuiltOptions).Where(x => x.LotId == jobNumber).SelectMany(x => x.BuiltOptions).ToList();
                //var getscontracts = _context.Contracts.Include(x => x.BuiltOptions).Where(x => x.SelectedLotId == jobNumber && x.SentDate != null).SelectMany(x => x.BuiltOptions).ToList();//sent date null may be scenario
                string message = "";
                var getSpec = _context.Specs.Where(x => x.JobId == jobNumber).ToList();
                message = getSpec.Any() ? "Spec" : "TBB";

                var getscontractsbuyer = _context.Buyers.AsNoTracking().Include(x => x.Contract.Cancellation).Include(x => x.Contract.Envelopes).ThenInclude(x => x.DocuSignSignatureTemplate).Where(x => x.Contract.SelectedLotId == jobNumber && x.Contract.Cancellation == null && x.ListOrder == 0 && x.Contract.Envelopes.Any(y => y.VoidedDate == null && y.SignedDate != null && y.DocuSignSignatureTemplate.TemplateTypeId == 1)).ToList();//ratified date null may be scenario, cancellation should be null if not cancelled, list order 0 is primary buyer, template type id 1 is sales agreement
                var getCancelledBuyer = _context.Buyers.AsNoTracking().Include(x => x.Contract.Cancellation).Where(x => x.Contract.SelectedLotId == jobNumber && x.Contract.Cancellation != null && x.ListOrder == 0).ToList();
                var getCancelledBuyersDto = _mapper.Map<List<BuyerDto>>(getCancelledBuyer);//
                if (getCancelledBuyersDto.Any()) 
                {
                    message = message + " - Cancelled";
                }
                foreach(var buyer in getCancelledBuyersDto)
                {
                    buyer.Status = message + " Cancelled";
                    buyer.IsCancelled = true;
                }
               
                var getBuyersDto = _mapper.Map<List<BuyerDto>>(getscontractsbuyer);//
                foreach (var buyer in getBuyersDto)
                {
                    buyer.Status = message + " Sold";
                    buyer.IsCancelled = false;
                }
                if (getBuyersDto.Any())
                {
                    message = message + " - Sold";
                }
                var returnBuyersDto = new List<BuyerDto>();
                returnBuyersDto.AddRange(getBuyersDto);
                returnBuyersDto.AddRange(getCancelledBuyersDto);
                return new OkObjectResult(new ResponseModel<List<BuyerDto>> { Value = returnBuyersDto, IsSuccess = true, Message = message });
                //TODO: make sure not cancelled customer, check spec, 
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<BuyerDto>> { IsSuccess = false, Message = "Error getting buyer" });
            }
        }

        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> GetAllOptionsAsync(string jobNumber)
        {
            try
            {
                var options = new List<AvailablePlanOption>();
                var specPhasePlanId = _context.Specs.AsNoTracking().Where(x => x.JobId == jobNumber).Select(x => x.SelectedFloorplanId).FirstOrDefault();
                var contractPhasePlanId = _context.Contracts.Include(x => x.Envelopes).ThenInclude(x => x.DocuSignSignatureTemplate).Include(x => x.Cancellation).Where(c => c.SelectedLotId == jobNumber && c.Cancellation == null && c.Envelopes.Any(y => y.VoidedDate == null && y.SignedDate != null)).Select(x => x.SelectedFloorplanId).FirstOrDefault();

                if (specPhasePlanId != 0)
                {
                    options = _context.AvailablePlanOptions.AsNoTracking().AsSplitQuery().Include(x => x.OptionGroup).Include(x => x.OptionTypeNavigation).Include(x => x.PhasePlan.MasterPlan).Where(x => x.PhasePlanId == specPhasePlanId && x.IsActive == true).ToList();
                    foreach (var option in options)
                    {
                        option.OptionCode = $"{option.PhasePlan.MasterPlan.PlanNum}{option.OptionCode}";
                    }
                    var missing = options.Where(x => x.OptionCode == null || x.OptionCode == "").ToList();
                }
                else if (contractPhasePlanId != null && contractPhasePlanId != 0)
                {
                    options = _context.AvailablePlanOptions.AsNoTracking().AsSplitQuery().Include(x => x.OptionGroup).Include(x => x.OptionTypeNavigation).Include(x => x.PhasePlan.MasterPlan).Where(x => x.PhasePlanId == contractPhasePlanId && x.IsActive == true).ToList();
                    foreach (var option in options)
                    {
                        option.OptionCode = $"{option.PhasePlan.MasterPlan.PlanNum}{option.OptionCode}";
                    }
                }

                return new OkObjectResult(new ResponseModel<List<AvailablePlanOptionDto>> { Value = _mapper.Map<List<AvailablePlanOptionDto>>(options), IsSuccess = true, Message = string.Empty });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<BuyerDto>> { IsSuccess = false, Message = "Error getting buyer" });
            }
        }

        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> JobCustomerAsync(string jobNumber)
        {
            try
            {
                //this gets the customer already in ERP associated with the job, if there is one
                var customerDto = new CustomerDto();
                var getJobCustomer = await _context.JobCustomers.AsNoTracking().Include(x => x.Customer).FirstOrDefaultAsync(x => x.JobNumber == jobNumber && x.IsActive == true);
                if (getJobCustomer != null)
                {
                    var customer = getJobCustomer.Customer;
                    customerDto = _mapper.Map<CustomerDto>(customer);
                }
                //TODO: is there distinguishing cobuyer?
                return new OkObjectResult(new ResponseModel<CustomerDto> { Value = customerDto, IsSuccess = true, Message = "found customer" });
                //TODO: make sure not cancelled customer, check spec, filter by already sent to sales config, 
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<CustomerDto> { IsSuccess = false, Message = "Error getting buyer" });
            }

        }
        [HttpGet]
        public async Task<IActionResult> RecentJobCustomersAsync()
        {
            try
            {
                //this gets the customer already in ERP associated with the job, if there is one
                var recentContracts = _context.Contracts.AsNoTracking().Include(x => x.Buyers).Include(x => x.Envelopes).ThenInclude(x => x.DocuSignSignatureTemplate).Include(x => x.Cancellation).Where(x => x.Cancellation == null && x.Envelopes.Any(y => y.VoidedDate == null && y.SignedDate >= DateTime.Now.AddMonths(-1) && y.DocuSignSignatureTemplate.TemplateTypeId == 1)).ToList();//todo: check that docusign signature template name
                var selectEmails = recentContracts.SelectMany(x => x.Buyers.Where(y => y.ListOrder == 0)).Select(x => x.Email).ToList();
                var findERPCustomers = _context.Customers.Where(x => selectEmails.Contains(x.CustomerKey)).ToList();
                var customerJobs = recentContracts.Select(x => new JobCustomerDto()
                {
                    Customer = new CustomerDto()
                    {
                        CustomerName = $"{x.Buyers.FirstOrDefault(x => x.ListOrder == 0).FirstName} {x.Buyers.FirstOrDefault(x => x.ListOrder == 0).LastName}",
                        CustomerKey = x.Buyers.FirstOrDefault(x => x.ListOrder == 0)?.Email,
                        Email = x.Buyers.FirstOrDefault(x => x.ListOrder == 0)?.Email,
                        Address1 = x.Buyers.FirstOrDefault(x => x.ListOrder == 0)?.Address,
                        HomePhone = x.Buyers.FirstOrDefault(x => x.ListOrder == 0)?.HomePhone,
                        MobilePhone = x.Buyers.FirstOrDefault(x => x.ListOrder == 0)?.CellPhone,
                        CustomerId = findERPCustomers.Where(y => y.CustomerKey == x.Buyers.FirstOrDefault(x => x.ListOrder == 0)?.Email).FirstOrDefault()?.CustomerId ?? 0,
                        SentToNav = findERPCustomers.Where(y => y.CustomerKey == x.Buyers.FirstOrDefault(x => x.ListOrder == 0)?.Email).FirstOrDefault()?.SentToNav,
                        BoolSentToNav = findERPCustomers.Where(y => y.CustomerKey == x.Buyers.FirstOrDefault(x => x.ListOrder == 0)?.Email).FirstOrDefault()?.SentToNav == true
                    },
                    JobNumber = x.SelectedLotId
                }).ToList();
                   
                return new OkObjectResult(new ResponseModel<List<JobCustomerDto>> { Value = customerJobs, IsSuccess = true, Message = "found customers" });
                //TODO: make sure not cancelled customer, c
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobCustomerDto>> { IsSuccess = false, Message = "Error getting buyers" });
            }

        }
        [HttpPost]
        public async Task<IActionResult> PushCustomerDataAsync([FromBody] string jobNumber)
        {
            try
            {
                //TODO: clear customer if cancelled, find JobCustomer and mark cancelled, find sales config and mark cancelled, clear in BC
                
                //Push customer data to Customer table, in case they want that available in BC before they are ready to release options            
                var updateBy = User.Identity.Name.Split('@')[0];

               // var getsContractsBuyer = _context.Buyers.Include(x => x.Contract.Cancellation).Where(x => x.Contract.SelectedLotId == jobNumber && x.Contract.Cancellation == null).ToList();
                var getsContractsBuyer = _context.Buyers.Include(x => x.Contract.Cancellation).Include(x => x.Contract.Envelopes).ThenInclude(x => x.DocuSignSignatureTemplate).Where(x => x.Contract.SelectedLotId == jobNumber && x.Contract.Cancellation == null && x.ListOrder == 0 && x.Contract.Envelopes.Any(y => y.VoidedDate == null && y.SignedDate != null && y.DocuSignSignatureTemplate.TemplateTypeId == 1)).ToList();//ratified date null may be scenario, cancellation should be null if not cancelled, list order 0 is primary buyer
                var getCancelledBuyer = _context.Buyers.Include(x => x.Contract.Cancellation).Where(x => x.Contract.SelectedLotId == jobNumber && x.Contract.Cancellation != null).ToList();
                if (getCancelledBuyer != null && getCancelledBuyer.Any())
                {
                    //clear from BC, 
                    //update job to link customer
                    string baseRequestUrl = _configuration.GetSection("BusinessCentral:CustomerBaseUrl").Value;
                    var jobCustomerToPost = new BCJobCustomer()
                    {
                        Sell_to_Customer_No = "BLANK"//default customer for jobs
                    };

                    //patch the job to link the customer
                    var jsonItem = JsonConvert.SerializeObject(jobCustomerToPost);
                    var stringBody = new StringContent(jsonItem);
                    var response = await _downstreamAPI.CallApiForAppAsync("BusinessCentral",
                       options =>
                       {
                           options.HttpMethod = "PATCH";
                           options.RelativePath = $"Project_Card_Excel('{jobNumber}')";
                           options.BaseUrl = baseRequestUrl;
                           options.CustomizeHttpRequestMessage = message =>
                           {
                               message.Headers.Add("Accept", "*/*");
                               message.Headers.Add("If-Match", "*");
                               message.Content.Headers.Remove("Content-Type");//TODO: better way to do this so don't have to remove the header and re-add
                               message.Content.Headers.Add("Content-Type", "application/json");
                           };
                       }, stringBody);
                    var apiLog = new ErpBcApiLog()
                    {
                        Method = "PATCH",
                        Type = "Job",
                        RequestBody = jsonItem,
                        RequestUrl = $"{baseRequestUrl}Project_Card_Excel('{jobNumber}')",
                        ResponseBody = await response.Content.ReadAsStringAsync(),
                        ResponseCode = response.StatusCode.ToString(),//TODO: how to get actual response, what if it failed
                        CreatedBy = updateBy
                    };
                    _context.ErpBcApiLogs.Add(apiLog);
                    await _context.SaveChangesAsync();
                    //var response3 = await _downstreamAPI.PatchForAppAsync<BCJobCustomer, BCResponseJobCustomer>("BusinessCentral", jobCustomerToPost,
                    //   options =>
                    //   {
                    //       options.RelativePath = $"Project_Card_Excel('{jobNumber}')";
                    //       options.BaseUrl = baseRequestUrl;
                    //       options.CustomizeHttpRequestMessage = message =>
                    //       {
                    //           message.Headers.Add("If-Match", "*");
                    //       };
                    //   });
                    //var apiLog2 = new ErpBcApiLog()
                    //{
                    //    Method = "PATCH",
                    //    Type = "Job",
                    //    RequestBody =jobCustomerToPost.ToString(),
                    //    RequestUrl = $"{baseRequestUrl}Project_Card_Excel('{jobNumber}')",
                    //    ResponseBody = response3.ToString(),
                    //    ResponseCode = 200,//TODO: how to get actual response, what if it failed
                    //    CreatedBy = updateBy
                    //};
                    //_context.ErpBcApiLogs.Add(apiLog2);
                    //await _context.SaveChangesAsync();

                    //mark cancelled in jobcustomer
                    var cancelledEmails = getCancelledBuyer.Select(x => x.Email).ToList();
                    var findCancelledCustomers = _context.JobCustomers.Include(x =>x.Customer).Where(x => x.JobNumber == jobNumber && x.Customer != null && x.Customer.Email != null && x.IsActive == true && cancelledEmails.Contains(x.Customer.Email));
                    foreach (var jobCustomer in findCancelledCustomers)
                    {
                        jobCustomer.Cancelled = true;
                        jobCustomer.IsActive = false;//deactivate old ones
                        jobCustomer.Updatedby = updateBy;
                        jobCustomer.Updateddatetime = DateTime.Now;
                    }
                    _context.JobCustomers.UpdateRange(findCancelledCustomers);
                    await _context.SaveChangesAsync();
                    //find salesconfig and co and mark "d" and unapprovec
                    var findSalesConfigToCancel = _context.Salesconfigs.Where(x => x.JobNumber == jobNumber && x.Owneremail != null && x.Status == "sale" && cancelledEmails.Contains(x.Owneremail)).ToList();
                    foreach(var config in findSalesConfigToCancel)
                    {
                        config.SsAction = "D";//Delete, job is cancelled                        
                        config.IsActive = config.SentToPurchasing == "F" ? false : true;//if it wasn't sent to purchasing, just deactivate it 
                        config.SentToPurchasing = "F";//unmark sent, since it needs to be resent,
                        config.Canceldate = getCancelledBuyer.First().Contract?.Cancellation?.Date;
                        config.UpdatedBy = updateBy;
                        config.UpdatedDateTim = DateTime.Now;
                    }
                    _context.Salesconfigs.UpdateRange(findSalesConfigToCancel);
                    await _context.SaveChangesAsync();
                    var findConfigIds = findSalesConfigToCancel.Select(x => x.SalesconfigId).ToList();
                    var findCos = _context.Salesconfigcos.Where(x => findConfigIds.Contains(x.SalesconfigId)).ToList();
                    foreach (var config in findCos) 
                    {
                       // config.SsAction = "D";//Delete, job is cancelled                        
                        config.IsActive = config.SentToPurchasing == "F" ? false : true;//if it wasn't sent to purchasing, just deactivate
                        config.SentToPurchasing = "F";//unmark sent, since it needs to be resent if it was already
                        config.CoStatusdate = DateTime.Now;
                        config.UpdatedBy = updateBy;
                        config.UpdatedDateTim = DateTime.Now;
                    }
                    _context.Salesconfigcos.UpdateRange(findCos);
                    await _context.SaveChangesAsync();
                }

                if (getsContractsBuyer != null && getsContractsBuyer.Count != 0)
                {
                    var firstBuyer = getsContractsBuyer.FirstOrDefault(x => x.ListOrder == 0);//First buyer is listOrder = 0, cobuyers in order after that

                    //check if customer exists in BC
                    //TODO: make sure it has same name, email, address before using. This is just checking email
                    var baseUrl = _configuration.GetSection("BusinessCentral:BaseUrl").Value;
                    string baseRequestUrl = _configuration.GetSection("BusinessCentral:CustomerBaseUrl").Value;
                    var getExistingBCCustomerResponse = await _downstreamAPI.GetForAppAsync<BCCustomersResponse>("BusinessCentral",
                       options => {
                           options.RelativePath = $"customers?$filter=email eq '{firstBuyer.Email}'";
                           options.BaseUrl = baseUrl;
                       });

                    string bcCustomerNo = "";                    
                    if (getExistingBCCustomerResponse.value.Count() > 0)
                    {
                        //customer exists in BC, use it
                        bcCustomerNo = getExistingBCCustomerResponse.value.First().number;
                        //TODO: update the BC customer with new addresss etc, if it has changed
                        var itemToPost = new BCCustomer()
                        {
                            Link_With_ERP_HomeBuilder = true,
                            Name = $"{firstBuyer.FirstName} {firstBuyer.LastName}",
                            E_Mail = firstBuyer.Email,
                            Address = firstBuyer.Address,
                            // Address_2 = firstBuyer.a,
                            City = firstBuyer.City,
                            County = firstBuyer.State,//County is state in BC
                            Phone_No = firstBuyer.Phone,
                            MobilePhoneNo = firstBuyer.CellPhone,
                            Post_Code = firstBuyer.Zip,
                            Customer_Posting_Group = "NH_CUST",
                            Gen_Bus_Posting_Group = "DOMESTIC"
                        };
                        var jsonItem2 = JsonConvert.SerializeObject(itemToPost);
                        var stringBody2 = new StringContent(jsonItem2);
                        var response2 = await _downstreamAPI.CallApiForAppAsync("BusinessCentral",
                           options =>
                           {
                               options.HttpMethod = "PATCH";
                               options.RelativePath = $"Customer_Card_Excel('{bcCustomerNo}')";
                               options.BaseUrl = baseRequestUrl;
                               options.CustomizeHttpRequestMessage = message =>
                               {
                                   message.Headers.Add("Accept", "*/*");
                                   message.Headers.Add("If-Match", "*");
                                   message.Content.Headers.Remove("Content-Type");//TODO: better way to do this so don't have to remove the header and re-add
                                   message.Content.Headers.Add("Content-Type", "application/json");
                               };
                           }, stringBody2);
                        var responseContent = await response2.Content.ReadAsStringAsync();
                        var apiLog2 = new ErpBcApiLog()
                        {
                            Method = "PATCH",
                            Type = "Job",
                            RequestBody = jsonItem2,
                            RequestUrl = $"{baseRequestUrl}Customer_Card_Excel",
                            ResponseBody = responseContent,
                            ResponseCode = response2.StatusCode.ToString(),//TODO: how to get actual response, what if it failed
                            CreatedBy = updateBy
                        };
                        _context.ErpBcApiLogs.Add(apiLog2);
                        var jsonResponse = JsonConvert.DeserializeObject<BCResponseCustomer>(responseContent);
                        bcCustomerNo = jsonResponse.No;
                    }
                    else
                    {
                        //new customer in BC
                        //var baseUrl = _configuration.GetSection("BusinessCentral:BaseUrl").Value;
                        var companyId = _configuration.GetSection("BusinessCentral:companyId").Value;
                        var itemToPost = new BCCustomer()
                        {
                            Link_With_ERP_HomeBuilder = true,
                            Name = $"{firstBuyer.FirstName} {firstBuyer.LastName}",
                            E_Mail = firstBuyer.Email,
                            Address = firstBuyer.Address,
                           // Address_2 = firstBuyer.a,
                            City = firstBuyer.City,
                            County = firstBuyer.State,//County is state in BC
                            Phone_No = firstBuyer.Phone,
                            MobilePhoneNo = firstBuyer.CellPhone,
                            Post_Code = firstBuyer.Zip,
                            Customer_Posting_Group = "NH_CUST",
                            Gen_Bus_Posting_Group = "DOMESTIC"
                        };
                      
                        //patch the job to link the customer
                        var jsonItem2 = JsonConvert.SerializeObject(itemToPost);
                        var stringBody2 = new StringContent(jsonItem2);
                        var response2 = await _downstreamAPI.CallApiForAppAsync("BusinessCentral",
                           options =>
                           {
                               options.HttpMethod = "POST";
                               options.RelativePath = "Customer_Card_Excel";
                               options.BaseUrl = baseRequestUrl;
                               options.CustomizeHttpRequestMessage = message =>
                               {
                                   message.Headers.Add("Accept", "*/*");
                                  // message.Headers.Add("If-Match", "*");
                                   message.Content.Headers.Remove("Content-Type");//TODO: better way to do this so don't have to remove the header and re-add
                                   message.Content.Headers.Add("Content-Type", "application/json");
                               };
                           }, stringBody2);
                        var responseContent = await response2.Content.ReadAsStringAsync();
                        var apiLog2 = new ErpBcApiLog()
                        {
                            Method = "POST",
                            Type = "Job",
                            RequestBody = jsonItem2,
                            RequestUrl = $"{baseRequestUrl}Customer_Card_Excel",
                            ResponseBody = responseContent,
                            ResponseCode = response2.StatusCode.ToString(),//TODO: how to get actual response, what if it failed
                            CreatedBy = updateBy
                        };
                        _context.ErpBcApiLogs.Add(apiLog2);
                        var jsonResponse = JsonConvert.DeserializeObject<BCResponseCustomer>(responseContent);
                        bcCustomerNo = jsonResponse.No;
                    }
                    //update job to link customer
                    var jobCustomerToPost = new BCJobCustomer()
                    {
                        Sell_to_Customer_No = bcCustomerNo
                    };

                    //patch the job to link the customer
                    var jsonItem = JsonConvert.SerializeObject(jobCustomerToPost);
                    var stringBody = new StringContent(jsonItem);
                    var response = await _downstreamAPI.CallApiForAppAsync("BusinessCentral",
                       options =>
                       {

                           options.HttpMethod = "PATCH";
                           options.RelativePath = $"Project_Card_Excel('{jobNumber}')";
                           options.BaseUrl = baseRequestUrl;
                           options.CustomizeHttpRequestMessage = message =>
                           {
                               message.Headers.Add("Accept", "*/*");
                               message.Headers.Add("If-Match", "*");
                               message.Content.Headers.Remove("Content-Type");//TODO: better way to do this so don't have to remove the header and re-add
                               message.Content.Headers.Add("Content-Type", "application/json");
                           };
                       }, stringBody);
                    var apiLog = new ErpBcApiLog()
                    {
                        Method = "PATCH",
                        Type = "Job",
                        RequestBody = jsonItem,
                        RequestUrl = $"{baseRequestUrl}Project_Card_Excel('{jobNumber}')",
                        ResponseBody = await response.Content.ReadAsStringAsync(),
                        ResponseCode = response.StatusCode.ToString(),
                        CreatedBy = updateBy
                    };
                    _context.ErpBcApiLogs.Add(apiLog);
                    await _context.SaveChangesAsync();

                    //add or update the customer in ERP database and link to job
                    int erpCustomerId = 0; 
                    var findCustomer = _context.Customers.Where(x => x.IsActive == true && x.Email == firstBuyer.Email);
                    if (findCustomer.Any())
                    {
                        erpCustomerId = findCustomer.First().CustomerId;
                        var updateCustomer = findCustomer.First();
                        updateCustomer.Address1 = firstBuyer.Address;
                        updateCustomer.Postcode = firstBuyer.Zip;
                        updateCustomer.City = firstBuyer.City;
                        updateCustomer.State = firstBuyer.State;
                        updateCustomer.CustomerKey = firstBuyer.Email;
                        updateCustomer.Country = firstBuyer.Country;
                        updateCustomer.CustomerName = $"{firstBuyer.FirstName} {firstBuyer.LastName}";
                        updateCustomer.HomePhone = firstBuyer.Phone;
                        updateCustomer.WorkPhone = firstBuyer.WorkPhone;
                        updateCustomer.MobilePhone = firstBuyer.CellPhone;
                        updateCustomer.AcntCustomerCode = bcCustomerNo;//update the existing customer with bc id
                        updateCustomer.UpdatedBy = updateBy;
                        updateCustomer.UpdatedDateTime = DateTime.Now;
                        _context.Customers.Update(updateCustomer);
                        await _context.SaveChangesAsync();
                    }
                    else
                    {
                        var newCustomer = new Customer()
                        {
                            CreatedBy = updateBy,
                            CustomerName = $"{firstBuyer.FirstName} {firstBuyer.LastName}",
                            Address1 = firstBuyer.Address,
                            City = firstBuyer.City,
                            State = firstBuyer.State,
                            Country = firstBuyer.Country,
                            Postcode = firstBuyer.Zip,
                            WorkPhone = firstBuyer.WorkPhone,
                            HomePhone = firstBuyer.Phone,
                            MobilePhone = firstBuyer.CellPhone,
                            Email = firstBuyer.Email,
                            AcntCustomerCode = bcCustomerNo, 
                            CustomerKey = firstBuyer.Email,
                            IsActive = true
                        };
                        _context.Customers.Add(newCustomer);
                        await _context.SaveChangesAsync();
                        erpCustomerId = newCustomer.CustomerId;
                    }
                    //update customer on the job
                    var findJobCustomers = _context.JobCustomers.Where(x => x.JobNumber == jobNumber && x.IsActive == true);
                    foreach (var jobCustomer in findJobCustomers)
                    {                       
                        jobCustomer.IsActive = false;//deactivate old ones
                        jobCustomer.Updatedby = updateBy;
                        jobCustomer.Updateddatetime = DateTime.Now;
                    }
                    _context.JobCustomers.UpdateRange(findJobCustomers);
                    await _context.SaveChangesAsync();
                    var newJobCustomer = new JobCustomer()
                    {
                        CustomerId = erpCustomerId,
                        JobNumber = jobNumber,
                        Createdby = updateBy
                    };
                    _context.JobCustomers.Add(newJobCustomer);
                    await _context.SaveChangesAsync();
                }
                               
                return new OkObjectResult(new ResponseModel<string> { Value = jobNumber, IsSuccess = true, Message = "Customer data updated successfully" });

            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<string> { IsSuccess = false, Message = "Error while pushing customer data" });
            }
        }
        [HttpPost]
        public async Task<IActionResult> PushCustomerDataNavAsync([FromBody] string jobNumber)
        {
            try
            {
                //TODO: clear customer if cancelled, find JobCustomer and mark cancelled, find sales config and mark cancelled, clear in BC

                //Push customer data to Customer table, in case they want that available in BC before they are ready to release options            
                var updateBy = User.Identity.Name.Split('@')[0];

                var getJob = _context.Jobs.Include(x => x.Subdivision).Where(x => x.JobNumber == jobNumber).FirstOrDefault();
                // var getsContractsBuyer = _context.Buyers.Include(x => x.Contract.Cancellation).Where(x => x.Contract.SelectedLotId == jobNumber && x.Contract.Cancellation == null).ToList();
                var getsContractsBuyer = _context.Buyers.Include(x => x.Contract.Cancellation).Include(x => x.Contract.Envelopes).ThenInclude(x => x.DocuSignSignatureTemplate).Where(x => x.Contract.SelectedLotId == jobNumber && x.Contract.Cancellation == null && x.ListOrder == 0 && x.Contract.Envelopes.Any(y => y.VoidedDate == null && y.SignedDate != null && y.DocuSignSignatureTemplate.TemplateTypeId == 1)).ToList();//ratified date null may be scenario, cancellation should be null if not cancelled, list order 0 is primary buyer
                var getCancelledBuyer = _context.Buyers.Include(x => x.Contract.Cancellation).Where(x => x.Contract.SelectedLotId == jobNumber && x.Contract.Cancellation != null).ToList();
                if (getCancelledBuyer != null && getCancelledBuyer.Any())
                {
                    //clear from BC, 
                    //update job to link customer
                    //var jobCustomerToPost = new NavJob()
                    //{
                    //   BilltoCustomerNo  = "BLANK"//default customer for jobs
                    //};

                    ////patch the job to link the customer
                    //var jsonItem = JsonConvert.SerializeObject(jobCustomerToPost);
                    //var stringBody = new StringContent(jsonItem);
                    

                    //mark cancelled in jobcustomer
                    var cancelledEmails = getCancelledBuyer.Select(x => x.Email).ToList();
                    var findCancelledCustomers = _context.JobCustomers.Include(x => x.Customer).Where(x => x.JobNumber == jobNumber && x.Customer != null && x.Customer.Email != null && x.IsActive == true && cancelledEmails.Contains(x.Customer.Email));
                    foreach (var jobCustomer in findCancelledCustomers)
                    {
                        jobCustomer.Cancelled = true;
                        jobCustomer.IsActive = false;//deactivate old ones
                        jobCustomer.Updatedby = updateBy;
                        jobCustomer.Updateddatetime = DateTime.Now;
                    }
                    _context.JobCustomers.UpdateRange(findCancelledCustomers);
                    await _context.SaveChangesAsync();
                    //find salesconfig and co and mark "d" and unapprovec
                    var findSalesConfigToCancel = _context.Salesconfigs.Where(x => x.JobNumber == jobNumber && x.Owneremail != null && x.Status == "sale" && cancelledEmails.Contains(x.Owneremail)).ToList();
                    foreach (var config in findSalesConfigToCancel)
                    {
                        config.SsAction = "D";//Delete, job is cancelled                        
                        config.IsActive = config.SentToPurchasing == "F" ? false : true;//if it wasn't sent to purchasing, just deactivate it 
                        config.SentToPurchasing = "F";//unmark sent, since it needs to be resent,
                        config.Canceldate = getCancelledBuyer.First().Contract?.Cancellation?.Date;
                        config.UpdatedBy = updateBy;
                        config.UpdatedDateTim = DateTime.Now;
                    }
                    _context.Salesconfigs.UpdateRange(findSalesConfigToCancel);
                    await _context.SaveChangesAsync();
                    var findConfigIds = findSalesConfigToCancel.Select(x => x.SalesconfigId).ToList();
                    var findCos = _context.Salesconfigcos.Where(x => findConfigIds.Contains(x.SalesconfigId)).ToList();
                    foreach (var config in findCos)
                    {
                        // config.SsAction = "D";//Delete, job is cancelled                        
                        config.IsActive = config.SentToPurchasing == "F" ? false : true;//if it wasn't sent to purchasing, just deactivate
                        config.SentToPurchasing = "F";//unmark sent, since it needs to be resent if it was already
                        config.CoStatusdate = DateTime.Now;
                        config.UpdatedBy = updateBy;
                        config.UpdatedDateTim = DateTime.Now;
                    }
                    _context.Salesconfigcos.UpdateRange(findCos);
                    await _context.SaveChangesAsync();
                }

                if (getsContractsBuyer != null && getsContractsBuyer.Count != 0)
                {
                    var firstBuyer = getsContractsBuyer.FirstOrDefault(x => x.ListOrder == 0);//First buyer is listOrder = 0, cobuyers in order after that

                    //check if customer exists in BC
                    //TODO: make sure it has same name, email, address before using. This is just checking email

                    //add or update the customer in ERP database and link to job
                    int erpCustomerId = 0;
                    var findCustomer = _context.Customers.Where(x => x.IsActive == true && x.Email == firstBuyer.Email);
                    if (findCustomer.Any())
                    {
                        erpCustomerId = findCustomer.First().CustomerId;
                        var updateCustomer = findCustomer.First();
                        updateCustomer.Address1 = firstBuyer.Address;
                        updateCustomer.Postcode = firstBuyer.Zip;
                        updateCustomer.City = firstBuyer.City;
                        updateCustomer.State = firstBuyer.State;
                        updateCustomer.CustomerKey = firstBuyer.Email;
                        updateCustomer.Country = firstBuyer.Country;
                        updateCustomer.CustomerName = $"{firstBuyer.FirstName} {firstBuyer.LastName}";
                        updateCustomer.HomePhone = firstBuyer.Phone;
                        updateCustomer.WorkPhone = firstBuyer.WorkPhone;
                        updateCustomer.MobilePhone = firstBuyer.CellPhone;
                       // updateCustomer.AcntCustomerCode = bcCustomerNo;//update the existing customer with bc id
                        updateCustomer.UpdatedBy = updateBy;
                        updateCustomer.UpdatedDateTime = DateTime.Now;
                        _context.Customers.Update(updateCustomer);
                        await _context.SaveChangesAsync();
                    }
                    else
                    {
                        var newCustomer = new Customer()
                        {
                            CreatedBy = updateBy,
                            CustomerName = $"{firstBuyer.FirstName} {firstBuyer.LastName}",
                            Address1 = firstBuyer.Address,
                            City = firstBuyer.City,
                            State = firstBuyer.State,
                            Country = firstBuyer.Country,
                            Postcode = firstBuyer.Zip,
                            WorkPhone = firstBuyer.WorkPhone,
                            HomePhone = firstBuyer.Phone,
                            MobilePhone = firstBuyer.CellPhone,
                            Email = firstBuyer.Email,
                           // AcntCustomerCode = bcCustomerNo,//TODO: how this work with nav?
                            CustomerKey = firstBuyer.Email
                        };
                        _context.Customers.Add(newCustomer);
                        await _context.SaveChangesAsync();
                        erpCustomerId = newCustomer.CustomerId;
                    }
                    //update customer on the job
                    var findJobCustomers = _context.JobCustomers.Where(x => x.JobNumber == jobNumber && x.IsActive == true);
                    foreach (var jobCustomer in findJobCustomers)
                    {
                        jobCustomer.IsActive = false;//deactivate old ones
                        jobCustomer.Updatedby = updateBy;
                        jobCustomer.Updateddatetime = DateTime.Now;
                    }
                    _context.JobCustomers.UpdateRange(findJobCustomers);
                    await _context.SaveChangesAsync();
                    var newJobCustomer = new JobCustomer()
                    {
                        CustomerId = erpCustomerId,
                        JobNumber = jobNumber,
                        Createdby = updateBy
                    };
                    _context.JobCustomers.Add(newJobCustomer);
                    await _context.SaveChangesAsync();



                    //For nav number will be customer id 
                    string bcCustomerNo = "";
                    var customerToPost = new NavCustomer()
                    {
                        No = erpCustomerId.ToString(),
                        Name = $"{firstBuyer.FirstName} {firstBuyer.LastName}",
                        GlobalDimension1Code = getJob.Subdivision.SubdivisionNum,
                        GlobalDimension2Code = getJob.Subdivision.EntityNum,
                        CustomerPostingGroup = "NH_CUST"
                    };

                    //update job to link customer
                    var jobCustomerToPost = new NavJob()
                    {
                        No = jobNumber,
                        Description = getJob.JobDesc,
                        GlobalDimension1Code = getJob.Subdivision.SubdivisionNum,
                        GlobalDimension2Code = getJob.Subdivision.EntityNum,
                        BilltoCustomerNo = erpCustomerId.ToString()
                    };

                    byte[] renderedBytes = null;
                    using (MemoryStream ms = new MemoryStream())
                    {

                        var writer = XmlWriter.Create(ms);
                        writer.WriteStartElement("DataList");

                        writer.WriteStartElement("CustomerList");
                        writer.WriteElementString("TableID", "18");
                        writer.WriteElementString("PackageCode", "CUSTOMERS - NH");
                        writer.WriteStartElement("Customer");//start customer
                        writer.WriteElementString("No", customerToPost.No);
                        writer.WriteElementString("Name", customerToPost.Name);
                        writer.WriteElementString("GlobalDimension1Code", customerToPost.GlobalDimension1Code);
                        writer.WriteElementString("GlobalDimension2Code", customerToPost.GlobalDimension2Code);
                        writer.WriteElementString("CustomerPostingGroup", customerToPost.CustomerPostingGroup);
                        writer.WriteEndElement();//close customer
                        writer.WriteEndElement();//close customer list
                       
                        writer.WriteStartElement("JobList");
                        writer.WriteElementString("TableID", "167");
                        writer.WriteElementString("PackageCode", "CUSTOMERS - NH");
                        writer.WriteStartElement("Job");
                        writer.WriteElementString("No", jobCustomerToPost.No);
                        writer.WriteElementString("Description", jobCustomerToPost.Description);
                        writer.WriteElementString("GlobalDimension1Code", jobCustomerToPost.GlobalDimension1Code);
                        writer.WriteElementString("GlobalDimension2Code", jobCustomerToPost.GlobalDimension2Code);
                        writer.WriteElementString("BilltoCustomerNo", jobCustomerToPost.BilltoCustomerNo);
                        writer.WriteEndElement();//close job
                        writer.WriteEndElement();//close job list
                        
                        writer.WriteEndElement();//close data list

                        writer.Flush();//does this close it

                        renderedBytes = ms.ToArray();
                    }

                    return new OkObjectResult(new ResponseModel<byte[]> { Value = renderedBytes, IsSuccess = true, Message = "Customer data updated successfully" });
                }

                return new OkObjectResult(new ResponseModel<byte[]> { Value = null , IsSuccess = true, Message = "Customer data updated successfully" });

            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<byte[]> { IsSuccess = false, Message = "Error while pushing customer data" });
            }
        }
        [HttpPost]
        public async Task<IActionResult> PushCustomersDataNavAsync([FromBody] List<JobCustomerDto> customers)
        {
            try
            {
                //Push customer data          
                var updateBy = User.Identity.Name.Split('@')[0];

                var navCustomers = new List<NavCustomer>();
                var navJobs = new List<NavJob>();
                foreach (var customer in customers)
                {

                    var getErpCustomer = _context.Customers.FirstOrDefault(x => x.CustomerKey == customer.Customer.CustomerKey);
                    var getErpCustomerId = getErpCustomer?.CustomerId;
                    if (getErpCustomerId == 0 || getErpCustomerId == null)
                    {
                        //add erp customer
                        var newCustomer = new Customer()
                        {
                            CreatedBy = updateBy,
                            CustomerName = customer.Customer.CustomerName,
                            HomePhone = customer.Customer.HomePhone,
                            MobilePhone = customer.Customer.MobilePhone,
                            Address1 = customer.Customer.Address1,
                            Address2 = customer.Customer.Address2,
                            Email = customer.Customer.Email,
                            // AcntCustomerCode = bcCustomerNo,//TODO: how this work with nav?
                            CustomerKey = customer.Customer.Email,
                            SentToNav = true
                        };
                        _context.Customers.Add(newCustomer);
                        await _context.SaveChangesAsync();
                        getErpCustomerId = newCustomer.CustomerId;
                    }
                    else
                    {
                        getErpCustomer.SentToNav = true;
                        getErpCustomer.UpdatedDateTime = DateTime.Now;
                        getErpCustomer.UpdatedBy = updateBy;
                        _context.Customers.Update(getErpCustomer);
                        await _context.SaveChangesAsync();
                    }
                    var getJob = _context.Jobs.Include(x => x.Subdivision).Where(x => x.JobNumber == customer.JobNumber).FirstOrDefault();

                    //update customer on the job
                    var findJobCustomers = _context.JobCustomers.Where(x => x.JobNumber == customer.JobNumber && x.IsActive == true);
                    foreach (var jobCustomer in findJobCustomers)
                    {
                        jobCustomer.IsActive = false;//deactivate old ones
                        jobCustomer.Updatedby = updateBy;
                        jobCustomer.Updateddatetime = DateTime.Now;
                    }
                    _context.JobCustomers.UpdateRange(findJobCustomers);
                    await _context.SaveChangesAsync();
                    var newJobCustomer = new JobCustomer()
                    {
                        CustomerId = (int)getErpCustomerId,
                        JobNumber = customer.JobNumber,
                        Createdby = updateBy,
                    };
                    _context.JobCustomers.Add(newJobCustomer);
                    await _context.SaveChangesAsync();

                    //For nav number will be customer id 
                    string bcCustomerNo = "";
                    var customerToPost = new NavCustomer()
                    {
                        No = getErpCustomerId.ToString(),
                        Name = customer.Customer.CustomerName,
                        GlobalDimension1Code = getJob.Subdivision.SubdivisionNum,
                        GlobalDimension2Code = getJob.Subdivision.EntityNum,
                        CustomerPostingGroup = "NH_CUST"
                    };
                    navCustomers.Add(customerToPost);
                    //update job to link customer
                    var jobCustomerToPost = new NavJob()
                    {
                        No = customer.JobNumber,
                        Description = getJob.JobDesc,
                        GlobalDimension1Code = getJob.Subdivision.SubdivisionNum,
                        GlobalDimension2Code = getJob.Subdivision.EntityNum,
                        BilltoCustomerNo = getErpCustomerId.ToString()
                    };
                    navJobs.Add(jobCustomerToPost);
                }
                byte[] renderedBytes = null;
                using (MemoryStream ms = new MemoryStream())
                {

                    var writer = XmlWriter.Create(ms);
                    writer.WriteStartElement("DataList");

                    writer.WriteStartElement("CustomerList");
                    writer.WriteElementString("TableID", "18");
                    writer.WriteElementString("PackageCode", "CUSTOMERS - NH");
                    foreach (var customer in navCustomers)
                    {
                        writer.WriteStartElement("Customer");//start customer
                        writer.WriteElementString("No", customer.No);
                        writer.WriteElementString("Name", customer.Name);
                        writer.WriteElementString("GlobalDimension1Code", customer.GlobalDimension1Code);
                        writer.WriteElementString("GlobalDimension2Code", customer.GlobalDimension2Code);
                        writer.WriteElementString("CustomerPostingGroup", customer.CustomerPostingGroup);
                        writer.WriteEndElement();//close customer
                    }

                    writer.WriteEndElement();//close customer list

                    writer.WriteStartElement("JobList");
                    writer.WriteElementString("TableID", "167");
                    writer.WriteElementString("PackageCode", "CUSTOMERS - NH");
                    foreach (var job in navJobs)
                    {
                        writer.WriteStartElement("Job");
                        writer.WriteElementString("No", job.No);
                        writer.WriteElementString("Description", job.Description);
                        writer.WriteElementString("GlobalDimension1Code", job.GlobalDimension1Code);
                        writer.WriteElementString("GlobalDimension2Code", job.GlobalDimension2Code);
                        writer.WriteElementString("BilltoCustomerNo", job.BilltoCustomerNo);
                        writer.WriteEndElement();//close job
                    }
                    writer.WriteEndElement();//close job list

                    writer.WriteEndElement();//close data list

                    writer.Flush();//does this close it

                    renderedBytes = ms.ToArray();
                }

                return new OkObjectResult(new ResponseModel<byte[]> { Value = renderedBytes, IsSuccess = true, Message = "Customer data exported successfully" });

            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<byte[]> { IsSuccess = false, Message = "Error while pushing customer data" });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdateCustomerSentToNavAsync([FromBody] List<JobCustomerDto> customers)
        {
            try
            {
                //update exported column, to track which ones sent to nav
                var findCustomers = customers.Select(x => x.Customer).ToList();
                foreach (var customer in findCustomers)
                {
                    customer.UpdatedDateTime = DateTime.Now;
                    customer.UpdatedBy = User.Identity.Name.Split('@')[0];
                    customer.SentToNav = customer.SentToNav;
                }
                var updateItems = _mapper.Map<List<Customer>>(findCustomers);
                await _context.Customers.BulkUpdateAsync(updateItems, options => options.ColumnInputExpression = x => new { x.SentToNav, x.UpdatedBy, x.UpdatedDateTime });
                return Ok(new ResponseModel<List<JobCustomerDto>> { IsSuccess = true, Value = customers });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstjcedetailDto>> { IsSuccess = false, Message = "failed to delete Jce details", Value = null });
            }
        }
        private async Task<string> GetBCTokenAsync()
        {

            var clientId = _configuration.GetSection("BusinessCentral:clientId").Value;
            var clientSecret = _configuration.GetSection("BusinessCentral:clientSecret").Value;
            var tenantId = _configuration.GetSection("BusinessCentral:tenantId").Value;
            var token_url = "https://login.microsoftonline.com/" + tenantId + "/oauth2/v2.0/token";

            var client = new HttpClient();

            var content = new StringContent(
                "grant_type=client_credentials" +
                "&scope=https://api.businesscentral.dynamics.com/.default" +
                "&client_id=" + System.Web.HttpUtility.UrlEncode(clientId) +
                "&client_secret=" + System.Web.HttpUtility.UrlEncode(clientSecret));

            content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");

            var response = await client.PostAsync(token_url, content);
            var tokenresponse = await response.Content.ReadAsStringAsync();
            var access_token_Object = JsonConvert.DeserializeObject<dynamic>(tokenresponse);
            var access_token = access_token_Object.access_token;

            return access_token;
        }
        [HttpPost]
        public async Task<IActionResult> ApproveSelectedAsync([FromBody] List<TbBuiltOptionDto> selectedOptions)
        {
            try
            {
                //TODO: an option can be removed from a spec on a contract
                //TODO: don't push the base house with spec and with customer

                //Creates sales configs/change orders based on approved option selections
                //Per Jess, when contract is cancelled options will always be kept, so for now not worrying about case where option was approved and now needs to be cancelled
                
                //PER JULIE, if just attribute change, generate a "note" PO with no cost
                //TODO:  qty delta could also be changed price, does that need to be pushed?

                //TODO: cancelled customer, spec, custom option
                var cancelledOptions = selectedOptions.Where(x => x.CancelledOption == true).ToList();
                //If it's a cancelation, find the sales config and create a cancellation chagne order, and mark the originl one cancelled,  

                var updateBy = User.Identity.Name.Split('@')[0];

                var findOptions = _context.TbBuiltOptions.Include(x => x.PlanOption).Include(x => x.BuildAttributeItems).Include(x => x.Contracts).ThenInclude(y => y.Buyers).Where(x => selectedOptions.Select(y => y.Id).Contains(x.Id)).ToList();

                var findContractOptions = _context.TbBuiltOptions.Include(x => x.PlanOption.PhasePlan.MasterPlan).Include(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeItem).Include(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeGroup).Include(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeGroup).Include(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeItem).Include(x => x.Contracts).ThenInclude(y => y.Buyers).Include(x => x.Contracts).ThenInclude(x => x.ContractAdmin).ThenInclude(x => x.Admin).Include(x => x.Contracts).ThenInclude(x => x.Envelopes).ThenInclude(x => x.DocuSignSignatureTemplate).Where(x => selectedOptions.Select(y => y.Id).Contains(x.Id) && x.Contracts.Any()).ToList();//no sales config or budget needed if qty delta = 0, but will mark it builder approved below
                //can't exclude qty delta = 0 on above, since it's copy from spec has qty delta = 0, however, below will look at qty and determine actual qty change
                //only real contract not scenario - should be ok because selected that way on front end so the selected ones to approve should only be real cntract or spec


                foreach(var option in findContractOptions.Where(x => x.BuildAttributeItems.Any()))
                {
                    foreach(var attr in option.BuildAttributeItems.Where(x => x.OpAttrGroupItem != null))
                    {
                        attr.AttrGroupAssignmentId = attr.OpAttrGroupItem.AttrGroupAssignmentId;
                        attr.AttrGroupAssignment = attr.OpAttrGroupItem.AttrGroupAssignment;//to accomodate either OpAttrGroupItem or AttrGroupAssignment filled
                    }
                }

                var findSpecOptions = _context.TbBuiltOptions.Include(x => x.PlanOption.PhasePlan.MasterPlan).Include(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeItem).Include(x => x.BuildAttributeItems).ThenInclude(x => x.OpAttrGroupItem.AttrGroupAssignment.AttributeGroup).Include(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeGroup).Include(x => x.BuildAttributeItems).ThenInclude(x => x.AttrGroupAssignment.AttributeItem).Include(x => x.Specs).ThenInclude(x => x.Job).Where(x => selectedOptions.Select(y => y.Id).Contains(x.Id) && x.Specs.Any()).ToList();//no sales config or budget needed if qty delta = 0, but will mark it builder approved below - not true, qty delta can be attribute change, need to get the ones with attributes but qty 0

                foreach (var option in findSpecOptions.Where(x => x.BuildAttributeItems.Any()))
                {
                    foreach (var attr in option.BuildAttributeItems.Where(x => x.OpAttrGroupItem != null))
                    {
                        attr.AttrGroupAssignmentId = attr.OpAttrGroupItem.AttrGroupAssignmentId;
                        attr.AttrGroupAssignment = attr.OpAttrGroupItem.AttrGroupAssignment;//to accomodate either OpAttrGroupItem or AttrGroupAssignment filled
                    }
                }
                if (findContractOptions.Any())
                {
                    var groupedByJob = findContractOptions.GroupBy(x => x.Contracts.First().SelectedLotId);//this is not needed since front end only displays one job at a time

                    foreach (var group in groupedByJob)
                    {                       
                        var jobNum = group.Key;
                        var findContract = group.First().Contracts.First();
                        var findSpec = _context.Specs.Include(x => x.Job).Include(x => x.SelectedFloorplan).FirstOrDefault(x => x.JobId == jobNum);
                       // var findContract = _context.Contracts.Include(x => x.Cancellation).Include(x => x.Buyers).Include(x => x.SelectedLot).Include(x => x.SelectedFloorplan).Include(x => x.ContractAdmin).Include(x => x.Envelopes).ThenInclude(x => x.DocuSignSignatureTemplate).FirstOrDefault(x => x.SelectedLotId == jobNum); //TODO: cancellation?
                        bool isSpec = findSpec != null ? findContract != null ? false : true : false;
                        var status = isSpec ? "spec home" : "sale";
                        var primaryBuyer = findContract.Buyers.Where(x => x.ListOrder == 0).FirstOrDefault();
                        var findConfig = _context.Salesconfigs.Where(x => x.JobNumber == jobNum && x.Status == status && x.Owneremail == primaryBuyer.Email && (x.SsAction == "U" || x.SsAction == "A")).FirstOrDefault();//TODO: -- check cancelled, 
                                       
                        if (findConfig == null)
                        {
                            var newConfig = new Salesconfig()
                            {
                                JobNumber = jobNum,
                                IsDeleted = "F",
                                SsAction = "U",//U seems to be updated A is add, D is delete
                                Saledate = isSpec ? null : findContract.Envelopes.FirstOrDefault(x => x.DocuSignSignatureTemplate.TemplateTypeId == 1 && x.VoidedDate == null && x.SignedDate != null)?.SentDate,
                                Ratificationdate = findContract.Envelopes.FirstOrDefault(x => x.DocuSignSignatureTemplate.TemplateTypeId == 1 && x.VoidedDate == null && x.SignedDate != null)?.SignedDate,
                                PhasePlanId = isSpec ? findSpec.SelectedFloorplanId : findContract.SelectedFloorplanId,
                              //  Baseprice = (double?)(isSpec ? findSpec.SelectedFloorplan.PhasePlanPrice : findContract.SelectedFloorplan.PhasePlanPrice),//is phase plan price filled
                                Ownername = primaryBuyer?.FirstName + " " + primaryBuyer?.LastName,
                                OwnerFirstName = primaryBuyer?.FirstName,
                                OwnerLastName = primaryBuyer?.LastName,
                                Owneremail = primaryBuyer?.Email,
                                Owneraddress1 = primaryBuyer?.Address,
                                Ownerphone1 = primaryBuyer?.HomePhone,
                                Status = status,
                                LotPremium = isSpec ? (double?)findSpec.Job.LotPremium : (double?)findContract.SelectedLot?.LotPremium,
                                LotSwing = isSpec ? findSpec.Job.LotSwing : findContract.SelectedLot?.LotSwing,
                                SalesagentName = isSpec ? null : $"{findContract.ContractAdmin?.Admin.FirstName} {findContract.ContractAdmin?.Admin.LastName}",
                                //TODO: all the customer information
                                CreatedBy = updateBy,
                            };
                            _context.Salesconfigs.Add(newConfig);
                            await _context.SaveChangesAsync();
                            findConfig = newConfig;
                            var newOptions = group.Select(x => new Salesconfigoption()
                            {
                                SalesconfigId = newConfig.SalesconfigId,
                                PlanOptionId = x.PlanOptionId,
                                OptionQuantity = (double?)x.Qty, 
                                OptionPrice = (double?)x.Price,//note price on tbbuilt option is unit price                    
                                SsOptioncode = $"{x.PlanOption?.PhasePlan.MasterPlan.PlanNum}{x.PlanOption?.OptionCode}",
                                ScDescription = x.OptionDesc,
                                OptionNotes = x.CustomerDesc,//Notes seems to go to customer desc
                                OptionSelections = x.BuildAttributeItems.Any() ? string.Join("; ", x.BuildAttributeItems.Where(y => !string.IsNullOrWhiteSpace(y.AttrGroupAssignment?.AttributeItem?.Description)).Select(y => $"{y.AttrGroupAssignment?.AttributeGroup?.Description}:  {y.AttrGroupAssignment?.AttributeItem?.Description}")) : x.CustomerDesc,
                                SalesconfigoptionsAttributes = x.BuildAttributeItems.Select(y => new SalesconfigoptionsAttribute()
                                {
                                    OpAttrGroupItemId = y.OpAttrGroupItemId,
                                    AttrGroupAssignmentId = y.AttrGroupAssignmentId ?? y.OpAttrGroupItem?.AttrGroupAssignmentId,
                                    CreatedBy = updateBy
                                }).ToList(),
                                CreatedBy = updateBy,
                            }).ToList();
                            _context.Salesconfigoptions.BulkInsert(newOptions, options => options.IncludeGraph = true);
                        }                       
                        else
                        {

                            //look in ny previous sales configs or cos for the same option, to be sure to put the update amount
                            var maxCoNumber = _context.Salesconfigcos.Where(x => x.SalesconfigId == findConfig.SalesconfigId).Max(x => x.CoNumber) ?? 0;
                            var newCo = new Salesconfigco()
                            {
                                SalesconfigId = findConfig.SalesconfigId,
                                CoNumber = maxCoNumber + 1,
                                CoStatusdate = DateTime.Now,//Is this the right date
                                CreatedBy = User.Identity.Name.Split('@')[0],
                            };
                            _context.Salesconfigcos.Add(newCo);
                            await _context.SaveChangesAsync();

                            //add the options
                            //To get the actual qty change,look at previous configs nd cos, get most recent qty, then compare
                            var findNewOptions = group.Select(x => x).ToList();
                            var findNewOptionsPlanOption = findNewOptions.Where(x => x.PlanOptionId != null);
                            var findNewOptionsCustom = findNewOptions.Where(x => x.PlanOptionId == null);

                            //find previous configs co options with same plan option id, grouped by plan option id (or description, for custom)
                            var findPrevCoOptionsPlanOpt = _context.Salesconfigcooptions.Include(x => x.Salesconfigco).Where(x => x.Salesconfigco.SalesconfigId == findConfig.SalesconfigId && findNewOptionsPlanOption.Select(y => y.PlanOptionId).Contains(x.PlanOptionId)).Select(x => new SalesconfigcooptionDto
                            {
                                PlanOptionId = x.PlanOptionId,
                                SsOptioncode = x.SsOptioncode,
                                ScDescription = x.ScDescription,
                                SalesconfigcoQuantityChange = x.SalesconfigcoAction == "d" ? -x.SalesconfigcoQuantityChange : x.SalesconfigcoQuantityChange,
                            }).ToList();

                            var findPrevConfigOptionsPlanOpt = _context.Salesconfigoptions.Where(x => x.SalesconfigId == findConfig.SalesconfigId && findNewOptionsPlanOption.Select(y => y.PlanOptionId).Contains(x.PlanOptionId)).Select(x => new SalesconfigcooptionDto
                            {
                                PlanOptionId = x.PlanOptionId,
                                SsOptioncode = x.SsOptioncode,
                                ScDescription = x.ScDescription,
                                SalesconfigcoQuantityChange = x.OptionQuantity,
                            }).ToList();

                            var findPrevCoOptionsCustom = _context.Salesconfigcooptions.Include(x => x.Salesconfigco).Where(x => x.Salesconfigco.SalesconfigId == findConfig.SalesconfigId && findNewOptionsCustom.Select(y => y.OptionDesc).Contains(x.ScDescription)).Select(x => new SalesconfigcooptionDto
                            {
                                ScDescription = x.ScDescription,
                                SalesconfigcoQuantityChange = x.SalesconfigcoAction == "d" ? -x.SalesconfigcoQuantityChange : x.SalesconfigcoQuantityChange,
                            }).ToList();

                            var findPrevConfigOptionsCustom = _context.Salesconfigoptions.Where(x => x.SalesconfigId == findConfig.SalesconfigId && findNewOptionsCustom.Select(y => y.OptionDesc).Contains(x.ScDescription)).Select(x => new SalesconfigcooptionDto
                            {
                                ScDescription = x.ScDescription,
                                SalesconfigcoQuantityChange = x.OptionQuantity
                            }).ToList();

                            var existingCoAndConfigOptionsPlanOpt = new List<SalesconfigcooptionDto>();
                            var existingCoAndConfigOptionsCustom = new List<SalesconfigcooptionDto>();
                            existingCoAndConfigOptionsPlanOpt.AddRange(findPrevConfigOptionsPlanOpt);
                            existingCoAndConfigOptionsPlanOpt.AddRange(findPrevCoOptionsPlanOpt);
                            existingCoAndConfigOptionsCustom.AddRange(findPrevConfigOptionsCustom);
                            existingCoAndConfigOptionsCustom.AddRange(findPrevCoOptionsCustom);
                            var findAllPrevGroupedPlanOpt = existingCoAndConfigOptionsPlanOpt.GroupBy(x => x.PlanOptionId).ToList();
                            var findAllPrevGroupedCustom = existingCoAndConfigOptionsCustom.GroupBy(x => x.ScDescription).ToList();

                            var allNotExisting = new List<TbBuiltOption>();
                            var notExistingPlanOpt = findNewOptionsPlanOption.Where(x => !existingCoAndConfigOptionsPlanOpt.Any(y => y.PlanOptionId == x.PlanOptionId)).ToList();
                            var notExistingCustom = findNewOptionsCustom.Where(x => !existingCoAndConfigOptionsCustom.Any(y => y.ScDescription == x.OptionDesc)).ToList();
                            allNotExisting.AddRange(notExistingPlanOpt);
                            allNotExisting.AddRange(notExistingCustom);
                            //existing qty would be sum of the group qty, execpt that it's negative if action =d
                            //then qty delta would be new quty in the findnewoption - existingqty, except if it's remove, 
                            //if its just selections changes, should it be a co?

                            var addUpdatedOptionsPlanOpt = (from a in findAllPrevGroupedPlanOpt
                                                     join b in findNewOptionsPlanOption on a.Key equals b.PlanOptionId
                                                     select new Salesconfigcooption()
                                                     {
                                                         SalesconfigcoId = newCo.SalesconfigcoId,
                                                         PlanOptionId = b.PlanOptionId,
                                                         SalesconfigcoAction = b.Removed || b.QtyDelta < 0 ? "d" : "a",//a = adding, d = deleting, what about change qty or just update selections
                                                         SalesconfigcoQuantityChange = b.Removed == true ? a.Sum(x => x.SalesconfigcoQuantityChange) : (double?)b.Qty - a.Sum(x => x.SalesconfigcoQuantityChange) < 0 ? -((double?)b.Qty - a.Sum(x => x.SalesconfigcoQuantityChange)) : (double?)b.Qty - a.Sum(x => x.SalesconfigcoQuantityChange),//absolute val
                                                         SalesconfigcoPrice = (double?)b.Price,
                                                         SsOptioncode = b.PlanOption?.OptionCode,
                                                         ScDescription = b.OptionDesc,
                                                         SalesconfigcoNotes = b.CustomerDesc,//Notes seems to go to customer desc
                                                         SalesconfigcoSelections = b.BuildAttributeItems.Any() ? string.Join("; ", b.BuildAttributeItems.Where(y => !string.IsNullOrWhiteSpace(y.AttrGroupAssignment?.AttributeItem?.Description)).Select(y => $"{y.AttrGroupAssignment?.AttributeGroup?.Description}:  {y.AttrGroupAssignment?.AttributeItem?.Description}")) : b.CustomerDesc,
                                                         SalesconfigcooptionsAttributes = b.BuildAttributeItems.Select(y => new SalesconfigcooptionsAttribute()
                                                         {
                                                             OpAttrGroupItemId = y.OpAttrGroupItemId,
                                                             AttrGroupAssignmentId = y.AttrGroupAssignmentId ?? y.OpAttrGroupItem?.AttrGroupAssignmentId,
                                                             CreatedBy = updateBy
                                                         }).ToList(),
                                                         JcCategory = "CO",
                                                         CreatedBy = updateBy
                                                     }).ToList();
                            var insertUpdateOptionsPlanOpt = addUpdatedOptionsPlanOpt.ToList();
                            _context.Salesconfigcooptions.BulkInsert(insertUpdateOptionsPlanOpt, options => options.IncludeGraph = true);

                            var addUpdatedOptionsCustom = (from a in findAllPrevGroupedCustom
                                                            join b in findNewOptionsCustom on a.Key equals b.OptionDesc
                                                            select new Salesconfigcooption()
                                                            {
                                                                SalesconfigcoId = newCo.SalesconfigcoId,
                                                                //PlanOptionId = b.PlanOptionId,
                                                                SalesconfigcoAction = b.Removed || b.QtyDelta < 0 ? "d" : "a",//a = adding, d = deleting, what about change qty or just update selections
                                                                SalesconfigcoQuantityChange = b.Removed == true ? a.Sum(x => x.SalesconfigcoQuantityChange) : (double?)b.Qty - a.Sum(x => x.SalesconfigcoQuantityChange) < 0 ? -((double?)b.Qty - a.Sum(x => x.SalesconfigcoQuantityChange)) : (double?)b.Qty - a.Sum(x => x.SalesconfigcoQuantityChange),//absolute val
                                                                SalesconfigcoPrice = (double?)b.Price,
                                                                //SsOptioncode = b.PlanOption?.OptionCode,//TODO: is this null? or should it be something like "CUST"
                                                                ScDescription = b.OptionDesc,
                                                                SalesconfigcoNotes = b.CustomerDesc,//Notes seems to go to customer desc
                                                                SalesconfigcoSelections = b.BuildAttributeItems.Any() ? string.Join("; ", b.BuildAttributeItems.Where(y => !string.IsNullOrWhiteSpace(y.AttrGroupAssignment?.AttributeItem?.Description)).Select(y => $"{y.AttrGroupAssignment?.AttributeGroup?.Description}:  {y.AttrGroupAssignment?.AttributeItem?.Description}")) : b.CustomerDesc,
                                                                SalesconfigcooptionsAttributes = b.BuildAttributeItems.Select(y => new SalesconfigcooptionsAttribute()
                                                                {
                                                                    OpAttrGroupItemId = y.OpAttrGroupItemId,
                                                                    AttrGroupAssignmentId = y.AttrGroupAssignmentId ?? y.OpAttrGroupItem?.AttrGroupAssignmentId,
                                                                    CreatedBy = updateBy
                                                                }).ToList(),
                                                                JcCategory = "CO",
                                                                CreatedBy = updateBy
                                                            }).ToList();
                            var insertUpdateOptionsCustom = addUpdatedOptionsCustom.ToList();
                            _context.Salesconfigcooptions.BulkInsert(insertUpdateOptionsCustom, options => options.IncludeGraph = true);

                            //Insert any that didn't have previous ones
                            var newOptions = allNotExisting.Select(x => new Salesconfigcooption()
                            {
                                SalesconfigcoId = newCo.SalesconfigcoId,
                                PlanOptionId = x.PlanOptionId,
                                SalesconfigcoAction = x.Removed || x.QtyDelta < 0 ? "d" : "a",//a = adding, d = deleting, what about change qty or just update selections
                                SalesconfigcoQuantityChange = (double?)x.Qty,//it's not already in the config/co, add the whole qty
                                SalesconfigcoPrice = (double?)x.Price,//price is unit price
                                SsOptioncode =  $"{x.PlanOption?.PhasePlan.MasterPlan.PlanNum}{x.PlanOption?.OptionCode}",
                                ScDescription = x.OptionDesc,
                                SalesconfigcoNotes = x.CustomerDesc,//Notes seems to go to customer desc
                                SalesconfigcoSelections = x.BuildAttributeItems.Any() ? string.Join("; ", x.BuildAttributeItems.Where(y => !string.IsNullOrWhiteSpace(y.AttrGroupAssignment?.AttributeItem?.Description)).Select(y => $"{y.AttrGroupAssignment?.AttributeGroup?.Description}:  {y.AttrGroupAssignment?.AttributeItem?.Description}")) : x.CustomerDesc,
                                SalesconfigcooptionsAttributes = x.BuildAttributeItems.Select(y => new SalesconfigcooptionsAttribute()
                                {
                                    OpAttrGroupItemId = y.OpAttrGroupItemId,
                                    AttrGroupAssignmentId = y.AttrGroupAssignmentId ?? y.OpAttrGroupItem?.AttrGroupAssignmentId,
                                    CreatedBy = updateBy
                                }).ToList(),
                                JcCategory = "CO",
                                CreatedBy = updateBy
                            });
                            _context.Salesconfigcooptions.BulkInsert(newOptions, options => options.IncludeGraph = true);                            
                        }                        
                    }
                }
                if (findSpecOptions.Any())
                {
                    //TODO: if there's already a contract, why also create a config for the spec? What if the option is in both? is it getting counted twice
                    var groupedByJob = findSpecOptions.GroupBy(x => x.Specs.First().JobId);
                    foreach (var group in groupedByJob)
                    {

                        var jobNum = group.Key;
                        var findSpec = group.First().Specs.First();
                        var status = "spec home";
                        var findConfig = _context.Salesconfigs.Where(x => x.JobNumber == jobNum && x.Status == status && (x.SsAction == "U" || x.SsAction == "A")).FirstOrDefault();

                        if (findConfig == null)
                        {
                            var newConfig = new Salesconfig()
                            {
                                JobNumber = jobNum,
                                IsDeleted = "F",
                                SsAction = "U",//U seems to be updated A is add, D is delete
                                Ratificationdate = DateTime.Now,//approved date for spec house 
                                PhasePlanId = findSpec.SelectedFloorplanId,
                                //Baseprice = (double?)findSpec.SelectedFloorplan.PhasePlanPrice,//PhasePlanPrice seems not filled
                                Ownername = "Spec",
                                Status = status,
                                LotPremium = (double?)findSpec.Job.LotPremium,
                                LotSwing = findSpec.Job.LotSwing,
                                //SalesagentName = isSpec ? null : $"{findContract.ContractAdmins.First().Admin.FirstName} {findContract.ContractAdmins.First().Admin.LastName}",
                                CreatedBy = updateBy,
                            };
                            _context.Salesconfigs.Add(newConfig);
                            await _context.SaveChangesAsync();
                            findConfig = newConfig;
                            var newOptions = group.Select(x => new Salesconfigoption()
                            {
                                SalesconfigId = newConfig.SalesconfigId,
                                PlanOptionId = x.PlanOptionId,
                                OptionQuantity = (double?)x.Qty,//TODO: this is new option, most recent, no previous approved, so use total qty. But wait, what if it was a spec and they approved the spec option? Is it here again?     
                                OptionPrice = (double?)x.Price,//unit price                   
                                SsOptioncode = $"{x.PlanOption?.PhasePlan.MasterPlan.PlanNum}{x.PlanOption?.OptionCode}",
                                ScDescription = x.OptionDesc,
                                OptionNotes = x.CustomerDesc,//Notes seems to go to customer desc
                                OptionSelections = x.BuildAttributeItems.Any() ? string.Join("; ", x.BuildAttributeItems.Where(y => !string.IsNullOrWhiteSpace(y.AttrGroupAssignment?.AttributeItem?.Description)).Select(y => $"{y.AttrGroupAssignment?.AttributeGroup?.Description}:  {y.AttrGroupAssignment?.AttributeItem?.Description}")) : x.CustomerDesc,
                                SalesconfigoptionsAttributes = x.BuildAttributeItems.Select(y =>
                                new SalesconfigoptionsAttribute()
                                {
                                    OpAttrGroupItemId = y.OpAttrGroupItemId,
                                    AttrGroupAssignmentId = y.AttrGroupAssignmentId ?? y.OpAttrGroupItem?.AttrGroupAssignmentId,
                                    CreatedBy = updateBy
                                }).ToList(),
                                CreatedBy = updateBy,
                            }).ToList();
                            _context.Salesconfigoptions.BulkInsert(newOptions, options => options.IncludeGraph = true);
                        }
                        else
                        {
                            var maxCoNumber = _context.Salesconfigcos.Where(x => x.SalesconfigId == findConfig.SalesconfigId).Max(x => x.CoNumber) ?? 0;
                            var newCo = new Salesconfigco()
                            {
                                SalesconfigId = findConfig.SalesconfigId,
                                CoNumber = maxCoNumber + 1,
                                CoStatusdate = DateTime.Now,//Is this the right date
                                CreatedBy = User.Identity.Name.Split('@')[0],
                            };
                            _context.Salesconfigcos.Add(newCo);
                            await _context.SaveChangesAsync();

                            //add the options
                            //To get the actual qty change,look at previous configs nd cos, get most recent qty, then compare

                            var findNewOptions = group.Select(x => x).ToList();
                            var findNewOptionsPlanOption = findNewOptions.Where(x => x.PlanOptionId != null);
                            var findNewOptionsCustom = findNewOptions.Where(x => x.PlanOptionId == null);

                            //for options that don't have plan option id (custom) match on description
                            //find previous configs co options with same plan option id (or description), grouped by plan option id
                            var findPrevCoOptionsPlanOpt = _context.Salesconfigcooptions.Include(x => x.Salesconfigco).Where(x => x.Salesconfigco.SalesconfigId == findConfig.SalesconfigId && findNewOptionsPlanOption.Select(y => y.PlanOptionId).Contains(x.PlanOptionId)).Select(x => new SalesconfigcooptionDto
                            {
                                PlanOptionId = x.PlanOptionId,
                                SsOptioncode = x.SsOptioncode,
                                ScDescription = x.ScDescription,
                                SalesconfigcoQuantityChange = x.SalesconfigcoAction == "d" ? -x.SalesconfigcoQuantityChange : x.SalesconfigcoQuantityChange,
                            }).ToList();

                            var findPrevConfigOptionsPlanOpt = _context.Salesconfigoptions.Where(x => x.SalesconfigId == findConfig.SalesconfigId && findNewOptionsPlanOption.Select(y => y.PlanOptionId).Contains(x.PlanOptionId)).Select(x => new SalesconfigcooptionDto
                            {
                                PlanOptionId = x.PlanOptionId,
                                SsOptioncode = x.SsOptioncode,
                                ScDescription = x.ScDescription,
                                SalesconfigcoQuantityChange = x.OptionQuantity,
                            }).ToList();

                            var findPrevCoOptionsCustom = _context.Salesconfigcooptions.Include(x => x.Salesconfigco).Where(x => x.Salesconfigco.SalesconfigId == findConfig.SalesconfigId && findNewOptionsCustom.Select(y => y.OptionDesc).Contains(x.ScDescription)).Select(x => new SalesconfigcooptionDto
                            {
                                ScDescription = x.ScDescription,
                                SalesconfigcoQuantityChange = x.SalesconfigcoAction == "d" ? -x.SalesconfigcoQuantityChange : x.SalesconfigcoQuantityChange,
                            }).ToList();

                            var findPrevConfigOptionsCustom = _context.Salesconfigoptions.Where(x => x.SalesconfigId == findConfig.SalesconfigId && findNewOptionsCustom.Select(y => y.OptionDesc).Contains(x.ScDescription)).Select(x => new SalesconfigcooptionDto
                            {
                                ScDescription = x.ScDescription,
                                SalesconfigcoQuantityChange = x.OptionQuantity
                            }).ToList();

                            var existingCoAndConfigOptionsPlanOpt = new List<SalesconfigcooptionDto>();
                            var existingCoAndConfigOptionsCustom = new List<SalesconfigcooptionDto>();
                            existingCoAndConfigOptionsPlanOpt.AddRange(findPrevConfigOptionsPlanOpt);
                            existingCoAndConfigOptionsPlanOpt.AddRange(findPrevCoOptionsPlanOpt);
                            existingCoAndConfigOptionsCustom.AddRange(findPrevConfigOptionsCustom);
                            existingCoAndConfigOptionsCustom.AddRange(findPrevCoOptionsCustom);
                            var findAllPrevGroupedPlanOpt = existingCoAndConfigOptionsPlanOpt.GroupBy(x => x.PlanOptionId).ToList();
                            var findAllPrevGroupedCustom = existingCoAndConfigOptionsCustom.GroupBy(x => x.ScDescription).ToList();

                            var allNotExisting = new List<TbBuiltOption>();
                            var notExistingPlanOpt = findNewOptionsPlanOption.Where(x => !existingCoAndConfigOptionsPlanOpt.Any(y => y.PlanOptionId == x.PlanOptionId)).ToList();
                            var notExistingCustom = findNewOptionsCustom.Where(x => !existingCoAndConfigOptionsCustom.Any(y => y.ScDescription == x.OptionDesc)).ToList();
                            allNotExisting.AddRange(notExistingPlanOpt);
                            allNotExisting.AddRange(notExistingCustom);

                            //existing qty would be sum of the group qty, execpt that it's negative if action =d
                            //then qty delta would be new quty in the findnewoption - existingqty, except if it's remove, 
                            //if its just selections changes, should it be a co?
                            var addUpdatedOptionsPlanOpt = (from a in findAllPrevGroupedPlanOpt
                                                            join b in findNewOptionsPlanOption on a.Key equals b.PlanOptionId
                                                            select new Salesconfigcooption()
                                                            {
                                                                SalesconfigcoId = newCo.SalesconfigcoId,
                                                                PlanOptionId = b.PlanOptionId,
                                                                SalesconfigcoAction = b.Removed || b.QtyDelta < 0 ? "d" : "a",//a = adding, d = deleting, what about change qty or just update selections
                                                                SalesconfigcoQuantityChange = b.Removed == true ? a.Sum(x => x.SalesconfigcoQuantityChange) : (double?)b.Qty - a.Sum(x => x.SalesconfigcoQuantityChange) < 0 ? -((double?)b.Qty - a.Sum(x => x.SalesconfigcoQuantityChange)) : (double?)b.Qty - a.Sum(x => x.SalesconfigcoQuantityChange),//absolute val
                                                                SalesconfigcoPrice = (double?)b.Price,
                                                                SsOptioncode = $"{b.PlanOption?.PhasePlan.MasterPlan.PlanNum}{b.PlanOption?.OptionCode}",
                                                                ScDescription = b.OptionDesc,
                                                                SalesconfigcoNotes = b.CustomerDesc,//Notes seems to go to customer desc
                                                                SalesconfigcoSelections = b.BuildAttributeItems.Any() ? string.Join("; ", b.BuildAttributeItems.Where(y => !string.IsNullOrWhiteSpace(y.AttrGroupAssignment?.AttributeItem?.Description)).Select(y => $"{y.AttrGroupAssignment?.AttributeGroup?.Description}:  {y.AttrGroupAssignment?.AttributeItem?.Description}")) : b.CustomerDesc,
                                                                SalesconfigcooptionsAttributes = b.BuildAttributeItems.Select(y => new SalesconfigcooptionsAttribute()
                                                                {
                                                                    OpAttrGroupItemId = y.OpAttrGroupItemId,
                                                                    AttrGroupAssignmentId = y.AttrGroupAssignmentId ?? y.OpAttrGroupItem?.AttrGroupAssignmentId,
                                                                    CreatedBy = updateBy
                                                                }).ToList(),
                                                                JcCategory = "CO",
                                                                CreatedBy = updateBy
                                                            }).ToList();

                            //For updated options, don't insert the ones where salesconfigcoqutychange = 0, so no budget and stuff when no qty change
                            //var insertUpdateOptionsPlanOpt = addUpdatedOptionsPlanOpt.Where(x => x.SalesconfigcoQuantityChange != 0).ToList();
                            var insertUpdateOptionsPlanOpt = addUpdatedOptionsPlanOpt.ToList();//Include qty 0, it should generate note po
                            _context.Salesconfigcooptions.BulkInsert(insertUpdateOptionsPlanOpt, options => options.IncludeGraph = true);

                            var addUpdatedOptionsCustom = (from a in findAllPrevGroupedCustom
                                                           join b in findNewOptionsCustom on a.Key equals b.OptionDesc
                                                           select new Salesconfigcooption()
                                                           {
                                                               SalesconfigcoId = newCo.SalesconfigcoId,
                                                               //PlanOptionId = b.PlanOptionId,
                                                               SalesconfigcoAction = b.Removed || b.QtyDelta < 0 ? "d" : "a",//a = adding, d = deleting, what about change qty or just update selections
                                                               SalesconfigcoQuantityChange = b.Removed == true ? a.Sum(x => x.SalesconfigcoQuantityChange) : (double?)b.Qty - a.Sum(x => x.SalesconfigcoQuantityChange) < 0 ? -((double?)b.Qty - a.Sum(x => x.SalesconfigcoQuantityChange)) : (double?)b.Qty - a.Sum(x => x.SalesconfigcoQuantityChange),//absolute val
                                                               SalesconfigcoPrice = (double?)b.Price,
                                                               //SsOptioncode = b.PlanOption?.OptionCode,//TODO: is this null? or should it be something like "CUST"
                                                               ScDescription = b.OptionDesc,
                                                               SalesconfigcoNotes = b.CustomerDesc,//Notes seems to go to customer desc
                                                               SalesconfigcoSelections = b.BuildAttributeItems.Any() ? string.Join("; ", b.BuildAttributeItems.Where(y => !string.IsNullOrWhiteSpace(y.AttrGroupAssignment?.AttributeItem?.Description)).Select(y => $"{y.AttrGroupAssignment?.AttributeGroup?.Description}:  {y.AttrGroupAssignment?.AttributeItem?.Description}")) : b.CustomerDesc,
                                                               SalesconfigcooptionsAttributes = b.BuildAttributeItems.Select(y => new SalesconfigcooptionsAttribute()
                                                               {
                                                                   OpAttrGroupItemId = y.OpAttrGroupItemId,
                                                                   AttrGroupAssignmentId = y.AttrGroupAssignmentId ?? y.OpAttrGroupItem?.AttrGroupAssignmentId,
                                                                   CreatedBy = updateBy
                                                               }).ToList(),
                                                               JcCategory = "CO",
                                                               CreatedBy = updateBy
                                                           }).ToList();

                            //For updated options, don't insert the ones where salesconfigcoqutychange = 0, so no budget and stuff when no qty change
                            var insertUpdateOptionsCustom = addUpdatedOptionsCustom.ToList();
                            _context.Salesconfigcooptions.BulkInsert(insertUpdateOptionsCustom, options => options.IncludeGraph = true);

                            //Insert any that didn't have previous ones
                            var newOptions = allNotExisting.Select(x => new Salesconfigcooption()
                            {
                                SalesconfigcoId = newCo.SalesconfigcoId,
                                PlanOptionId = x.PlanOptionId,
                                SalesconfigcoAction = x.Removed || x.QtyDelta < 0 ? "d" : "a",//a = adding, d = deleting, what about change qty or just update selections
                                SalesconfigcoQuantityChange = (double?)x.Qty,//it's not already in the config/co, add the whole qty
                                SalesconfigcoPrice = (double?)x.Price,
                                SsOptioncode = $"{x.PlanOption?.PhasePlan.MasterPlan.PlanNum}{x.PlanOption?.OptionCode}",
                                ScDescription = x.OptionDesc,
                                SalesconfigcoNotes = x.CustomerDesc,//Notes seems to go to customer desc
                                SalesconfigcoSelections = x.BuildAttributeItems.Any() ? string.Join("; ", x.BuildAttributeItems.Where(y => !string.IsNullOrWhiteSpace(y.AttrGroupAssignment?.AttributeItem?.Description)).Select(y => $"{y.AttrGroupAssignment?.AttributeGroup?.Description}:  {y.AttrGroupAssignment?.AttributeItem?.Description}")) : x.CustomerDesc,
                                SalesconfigcooptionsAttributes = x.BuildAttributeItems.Select(y =>  new SalesconfigcooptionsAttribute()
                                {
                                    OpAttrGroupItemId = y.OpAttrGroupItemId,
                                    AttrGroupAssignmentId = y.AttrGroupAssignmentId ?? y.OpAttrGroupItem?.AttrGroupAssignmentId,
                                    CreatedBy = updateBy
                                }).ToList(),
                                JcCategory = "CO",
                                CreatedBy = updateBy
                            });
                            _context.Salesconfigcooptions.BulkInsert(newOptions, options => options.IncludeGraph = true);
                        }                       
                    }
                }

                //mark the tbBuiltOptions as builder approved
                var findTbBuiltOptions = findOptions.Select(x => x.Id);
                var updateOptions = _context.TbBuiltOptions.Where(x => findTbBuiltOptions.Contains(x.Id));
                updateOptions.ExecuteUpdate(s => s.SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now).SetProperty(b => b.BuilderApproved, (short)1).SetProperty(b => b.ApprovedBy, updateBy).SetProperty(b => b.ApprovedDate, DateTime.Now));
                var findOptionsList = findOptions.ToList();
                var findOptionsDto = _mapper.Map<List<TbBuiltOptionDto>>(findOptionsList);
                return Ok(new ResponseModel<List<TbBuiltOptionDto>>() { IsSuccess = true, Value = findOptionsDto, Message = "Approved selcted options" });

            }          
             catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<string> { IsSuccess = false, Message = "Error approving options" });
            }
        }
    }
}
