﻿@inject StartPackageDocumentService StartPackageDocumentService
@inject ScheduleStartPackageService ScheduleStartPackageService
@inject IConfiguration Configuration

<style type="text/css">
    .k-dropzone-hint, .k-form-hint {
        font-family: "Roboto",sans-serif;
        font-size: .688rem;
    }
</style>
<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <h7 class="page-title" style="font-weight:bold">Add Document</h7>
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@ItemToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <div class="k-form-field">
                <label class="k-label k-form-label">Document Type:</label>
                <div class="k-form-field-wrap">
                    <TelerikDropDownList @bind-Value="@SelectedPackageItemId"
                                         Data="@AllPackageItems"
                                         TextField="ItemName"
                                         ValueField="PackageItemId"
                                         DefaultText="Select Package Item"
                                         Filterable="true"
                                         Width="100%">
                        <DropDownListSettings>
                            <DropDownListPopupSettings Height="auto"></DropDownListPopupSettings>
                        </DropDownListSettings>
                    </TelerikDropDownList>
                </div>
            </div><br />

            <div class="k-form-field">
                <label class="k-label k-form-label">Upload:</label>
                <div class="k-form-field-wrap">
                    <TelerikUpload SaveUrl="@SaveUrl"
                                   SaveField="files"
                                   AllowedExtensions="@( new List<string>() { ".pdf", ".docx", ".jpg", ".png" } )"
                                   OnSelect="@OnSelect"
                                   OnUpload="@OnUpload"
                                   OnSuccess="@OnSuccess"
                                   OnError="@OnError"
                                   OnCancel="@OnCancelHandler"
                                   OnRemove="@OnRemoveHandler"
                                   AutoUpload="false">
                    </TelerikUpload>

                    <div class="k-form-hint">Accepted files:&nbsp;&nbsp;<strong>PDF, DOCX, JPG, PNG</strong></div>
                </div>
            </div><br />

            <button type="button" @onclick="CancelUpload" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-base">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel" aria-hidden="true">
                    <!--!-->
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M256 32c-50.3 0-96.8 16.6-134.1 44.6-17.2 12.8-32.4 28.1-45.3 45.3C48.6 159.2 32 205.7 32 256c0 123.7 100.3 224 224 224 50.3 0 96.8-16.6 134.1-44.6 17.2-12.8 32.4-28.1 45.3-45.3 28-37.4 44.6-83.8 44.6-134.1 0-123.7-100.3-224-224-224zm0 384c-88.2 0-160-71.8-160-160 0-32.6 9.8-62.9 26.6-88.2l221.6 221.6C318.9 406.2 288.6 416 256 416zm133.4-71.8L167.8 122.6C193.1 105.8 223.4 96 256 96c88.2 0 160 71.8 160 160 0 32.6-9.8 62.9-26.6 88.2z"></path><!--!-->
                    </svg><!--!-->
                    <!--!-->
                </span> Cancel
            </button>
            <div style=@submittingStyle>Adding item. Please wait...</div>

        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>
@code {
    [Parameter]
    public int SubdivisionId { get; set; }

    [Parameter]
    public int PackageId { get; set; }

    [Parameter]
    public EventCallback<ResponseModel> HandleUploadSubmit { get; set; }

    public bool IsModalVisible { get; set; }
    public DocumentUploadModel ItemToAdd { get; set; } = new DocumentUploadModel();
    public int? SelectedPackageItemId;
    public List<SchePackageItemDto>? AllPackageItems { get; set; }
    public string? BaseUrl { get; set; }
    public string? SaveUrl { get; set; }
    private string submittingStyle = "display:none";
    Dictionary<string, bool> FilesValidationInfo { get; set; } = new Dictionary<string, bool>();

    public async Task Show()
    {
        BaseUrl = Configuration.GetSection("DownstreamApi").GetSection("BaseUrl").Value;
        SaveUrl = BaseUrl + "api/StartPackageDocument/UploadDocument";
        IsModalVisible = true;
        AllPackageItems = (await ScheduleStartPackageService.GetPackageItemsAsync(PackageId)).Value;
        StateHasChanged();
    }

    public async Task Hide()
    {
        IsModalVisible = false;
    }

    async void CancelUpload()
    {
        IsModalVisible = false;
        await HandleUploadSubmit.InvokeAsync(new ResponseModel { IsSuccess = false, Message = "Nothing to upload" });
    }

    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        submittingStyle = "display:none";
    }

    public void OnSelect(UploadSelectEventArgs args)
    {
        foreach (var file in args.Files)
        {
            if (!FilesValidationInfo.Keys.Contains(file.Id))
            {
                // nothing is assumed to be valid until the server returns an OK
                FilesValidationInfo.Add(file.Id, false);
            }
        }
    }

    void OnCancelHandler(UploadCancelEventArgs e)
    {
        RemoveFailedFilesFromList(e.Files);
    }

    void OnRemoveHandler(UploadEventArgs e)
    {
        RemoveFailedFilesFromList(e.Files);
    }

    public async void OnError(UploadErrorEventArgs args)
    {
        RemoveFailedFilesFromList(args.Files);
        await HandleUploadSubmit.InvokeAsync(new ResponseModel { IsSuccess = false, Message = args.Request.ResponseText });
    }

    void RemoveFailedFilesFromList(List<UploadFileInfo> files)
    {
        foreach (var file in files)
        {
            if (FilesValidationInfo.Keys.Contains(file.Id))
            {
                FilesValidationInfo.Remove(file.Id);
            }
        }
    }

    public async void OnSuccess(UploadSuccessEventArgs args)
    {
        await HandleUploadSubmit.InvokeAsync(new ResponseModel { IsSuccess = true, Message = "Upload is successful" });
    }

    public async Task OnUpload(UploadEventArgs args)
    {
        var token = await StartPackageDocumentService.GetTokenAsync();
        args.RequestData.Add("FileName", PackageId + "-" + SelectedPackageItemId + "-" + args.Files[0].Name);
        args.RequestData.Add("FolderName", SubdivisionId);

        var tokenObj = new
        {
            Bearer = $"{token}"
        };
        var tokenList = new List<string>() { $"bearer {token}" };
        args.RequestHeaders.Add("Authorization", $"bearer {token.Value}");
    }
}
