﻿@inject SubdivisionJobPickService SubdivisionJobPickService
@inject ScheduleService ScheduleService
@inject SubdivisionService SubdivisionService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager NavManager
<div>
    @if (SelectedSchedule == null)
    {
        <p>There is no schedule for this job. Create one.</p>
        <div>
            <TelerikButton OnClick="@NewScheduleFromTemplate" Title="New schedule from template" Icon="@FontIcon.Plus" Class=" tooltip-target k-button-add"></TelerikButton>
        </div>
    }
    else
    {
        <EditForm Model="@SelectedSchedule" OnValidSubmit="@HandleValidSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />
            <div>
                <div class="row p-1">
                    <div class="col-lg-4">
                        <label class="form-label">Template : </label>
                    </div>
                    <div class="col-lg-8">
                        @if (SelectedSchedule.Template != null)
                        {
                            <TelerikTextBox Enabled="false" @bind-Value="@SelectedSchedule.Template.TemplateName"></TelerikTextBox>
                        }
                    </div>
                </div>
            </div>
            <div>
                <div class="row p-1">
                    <div class="col-lg-4">
                        <label class="form-label">Approve Release: </label>
                    </div>
                    <div class="col-lg-8">
                        <TelerikCheckBox OnChange="ChangeApproved" @bind-Value="@SelectedSchedule.IniSchApproved"></TelerikCheckBox>
                    </div>
                </div>
            </div>
            <div>
                <div class="row p-1">
                    <div class="col-lg-4">
                        <label class="form-label">Approved By: </label>
                    </div>
                    <div class="col-lg-8">
                        <TelerikTextBox Enabled="false" @bind-Value="@SelectedSchedule.IniSchApprovedby" Width="100%"></TelerikTextBox>
                    </div>
                </div>
            </div>
            <div>
                <div class="row p-1">
                    <div class="col-lg-4">
                        <label class="form-label">Approve Date: </label>
                    </div>
                    <div class="col-lg-8">
                        <TelerikDateInput Enabled="false" @bind-Value="@SelectedSchedule.IniSchApproveDate" Width="100%"></TelerikDateInput>
                    </div>
                </div>
            </div>
            <div>
                <div class="row p-1">
                    <div class="col-lg-4">
                        <label class="form-label">Duration: </label>
                    </div>
                    <div class="col-lg-8">
                        <TelerikNumericTextBox Enabled="false" @bind-Value="@SelectedSchedule.IniSchDuration" Width="100%"></TelerikNumericTextBox>
                    </div>
                </div>
            </div>
            <div>
                <div class="row p-1">
                    <div class="col-lg-4">
                        <label class="form-label">Initial Start: </label>
                    </div>
                    <div class="col-lg-8">
                        @{
                            bool enable = SelectedSchedule.IniSchApproved != true;
                            <TelerikDatePicker Enabled=@enable OnChange="UpdateInitialStart" @bind-Value="@SelectedSchedule.IniSchStartDate" Width="100%"></TelerikDatePicker>
                        }
                    </div>
                </div>
            </div>
            <div>
                <div class="row p-1">
                    <div class="col-lg-4">
                        <label class="form-label">Initial End: </label>
                    </div>
                    <div class="col-lg-8">
                        <TelerikDatePicker Enabled="false" @bind-Value="@SelectedSchedule.IniSchEndDate" Width="100%"></TelerikDatePicker>
                    </div>
                </div>
            </div>
            <div>
                <div class="row p-1">
                    <div class="col-lg-4">
                        <label class="form-label">Permit Submitted: </label>
                    </div>
                    <div class="col-lg-8">
                        <TelerikDatePicker Enabled="true" @bind-Value="@SelectedSchedule.PermitSubmitDate" Width="100%"></TelerikDatePicker>
                    </div>
                </div>
            </div>
            <div>
                <div class="row p-1">
                    <div class="col-lg-4">
                        <label class="form-label">Permit Received: </label>
                    </div>
                    <div class="col-lg-8">
                        <TelerikDatePicker Enabled="true" @bind-Value="@SelectedSchedule.PermitReceivedDate" Width="100%"></TelerikDatePicker>
                    </div>
                </div>
            </div>
            <div>
                <div class="row p-1">
                    <div class="col-lg-4">
                        <label class="form-label">Permit Number: </label>
                    </div>
                    <div class="col-lg-8">
                        <TelerikTextBox Enabled="true" @bind-Value="@SelectedSchedule.PermitNumber" Width="100%"></TelerikTextBox>
                    </div>
                </div>
            </div>
            <div>
                <div class="row p-1">
                    <div class="col-lg-4">
                        <label class="form-label">Publish Schedule: </label>
                    </div>
                    <div class="col-lg-8">
                        <TelerikCheckBox Enabled="true" @bind-Value="@SelectedSchedule.BoolPublished"></TelerikCheckBox>
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-primary">Update</button>
        </EditForm>
        <br/>
        <TelerikButton OnClick="@GoToSchedule" Title="Go To Schedule" Class=" tooltip-target k-button-add">View Schedule</TelerikButton>
        <TelerikButton OnClick="@NewScheduleFromTemplate" Title="New schedule from template" Icon="@FontIcon.Plus" Class=" tooltip-target k-button-add"></TelerikButton>
    }
</div>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<ERP.Web.Components.CreateScheduleFromTemplate @ref="AddScheduleFromTemplateModal" SelectedTemplate="@SelectedTemplate.TemplateId" SelectedJobs="@SelectedJob" SelectedSubdivision="@SelectedSubdivisionId" HandleAddSubmit="@HandleValidAddScheduleFromTemplateSubmit"></ERP.Web.Components.CreateScheduleFromTemplate>
@code {

    [Parameter]
    public string JobNumber { get; set; }
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    public int? SelectedSubdivisionId { get; set; }
    public List<string>? SelectedJob = new List<string>();   
    public ScheduleDto? SelectedSchedule { get; set; }
    public TemplateDto? SelectedTemplate { get; set; } = new TemplateDto();
    public int? PreviousSelectedTemplateId { get; set; }
    protected ERP.Web.Components.CreateTemplate? AddTemplateModal { get; set; }
    protected ERP.Web.Components.CreateScheduleFromTemplate? AddScheduleFromTemplateModal { get; set; }
    public bool PublishSchedule { get; set; } = false;

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }


    protected override async Task OnInitializedAsync()
    {

    }

    protected override async Task OnParametersSetAsync()
    {
        SelectedSchedule = (await ScheduleService.GetPreScheduleAsync(JobNumber)).Value;
        SelectedSubdivisionId = (await SubdivisionService.GetLotAsync(JobNumber)).Value?.SubdivisionId;
        SelectedJob = new List<string>() { JobNumber };
    }
    private void GoToSchedule()
    {
        if (JobNumber != null)
        {
            SubdivisionJobPickService.JobNumber = JobNumber;          
            NavManager.NavigateTo($"schedule");
            StateHasChanged();
        }
    }
    private void NewScheduleFromTemplate()
    {
        AddScheduleFromTemplateModal.Show();
        StateHasChanged();
    }
    private async void HandleValidAddScheduleFromTemplateSubmit(ScheduleDto responseSchedule)
    {
        AddScheduleFromTemplateModal.Hide();
        SelectedSchedule = (await ScheduleService.GetPreScheduleAsync(JobNumber)).Value;
        StateHasChanged();
    }
    private async Task UpdateInitialStart()
    {
        //TODO: if selected date is a holiday and they choose to use it anyway, how to adjust schedule?
        if(SelectedSchedule?.IniSchStartDate != null){
            var checkHoliday = (await ScheduleService.CheckHolidayAsync(SelectedSchedule.IniSchStartDate.Value.Date)).Value;
            if (checkHoliday)
            {
                var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday, cancel to select new date");
                if (!confirm)
                {
                    return;
                }
            }
            var response = await ScheduleService.CalculateScheduleEndFromStartAsync(SelectedSchedule);
            SelectedSchedule.IniSchEndDate = response.Value.IniSchEndDate;
            SelectedSchedule.IniSchDuration = response.Value.IniSchDuration;
            ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
            StateHasChanged();
        }
        else
        {
            ShowSuccessOrErrorNotification("Missing start date", false);
        }
    }
    private async Task HandleValidSubmit()
    {
        var schedule = SelectedSchedule;
        var result = await ScheduleService.UpdatePrescheduleAsync(SelectedSchedule);
        SelectedSchedule = (await ScheduleService.GetPreScheduleAsync(JobNumber)).Value;
        ShowSuccessOrErrorNotification(result.Message, result.IsSuccess);//TODO: fix response success
        StateHasChanged();
    }
    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
    private async void ChangeApproved()
    {
        if (SelectedSchedule.IniSchApproved == true)
        {
            SelectedSchedule.IniSchApproveDate = DateTime.Now;
            SelectedSchedule.BoolPublished = true;
            if (SelectedSchedule.IniSchStartDate == null)
            {
                SelectedSchedule.IniSchStartDate = DateTime.Now;
                //TODO: show spinner
                await UpdateInitialStart();
            }
            var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userName = user.User.Identity.Name.Split('@')[0];
            SelectedSchedule.IniSchApprovedby = userName;
            StateHasChanged();
        }
        else
        {
            SelectedSchedule.IniSchApproveDate = null;
            SelectedSchedule.IniSchApprovedby = null;
            SelectedSchedule.BoolPublished = false;
        }
    }
}
