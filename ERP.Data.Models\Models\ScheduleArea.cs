﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ScheduleArea
{
    public int ScheduleAreaId { get; set; }

    public string? ScheduleAreaDesc { get; set; }

    public int ScheduleMasterId { get; set; }

    public int? ScheduleAreaSeq { get; set; }

    public int? SubdivisionId { get; set; }

    public string? UserCreated { get; set; }

    public DateTime? DateCreated { get; set; }

    public string? UserModified { get; set; }

    public DateTime? DateModified { get; set; }

    public DateTime? DateToStart { get; set; }

    public DateTime? DateToEnd { get; set; }

    public DateTime? BaseStartDate { get; set; }

    public DateTime? BaseEndDate { get; set; }

    public DateTime? ProjStartDate { get; set; }

    public DateTime? ProjEndDate { get; set; }

    public DateTime? ActualStartDate { get; set; }

    public DateTime? ActualEndDate { get; set; }

    public int? PlusminusDays { get; set; }

    public int? BaseDuration { get; set; }

    public int? ProjDuration { get; set; }

    public int? BaseCalduration { get; set; }

    public int? ProjCalduration { get; set; }

    public string? Published { get; set; }

    public int? ActualDuration { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ScheduleMaster ScheduleMaster { get; set; } = null!;

    public virtual ICollection<SchedulePhase> SchedulePhases { get; set; } = new List<SchedulePhase>();

    public virtual Subdivision? Subdivision { get; set; }
}
