﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ItemCostSummary
{
    public int ItemCostDetailId { get; set; }

    public int SessionId { get; set; }

    public string Activity { get; set; } = null!;

    public int? SelectedSubNumber { get; set; }

    public string? SubName { get; set; }

    public double? OrderAmount { get; set; }

    public double? TaxAmount { get; set; }

    public string? TradeName { get; set; }

    public int? WarningCount { get; set; }

    public int? ErrorCount { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }
}
