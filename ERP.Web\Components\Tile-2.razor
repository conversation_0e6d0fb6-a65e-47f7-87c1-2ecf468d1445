﻿@inject SalesPriceService SalesPriceService

<div class="card-body">
    <div class="row d-flex justify-content-center">
        <div class="col">
            <p class="text-dark mb-0 fw-semibold">Total Options Sold</p>
            <h3 class="m-0">@TotalOptionsSold</h3>
            <p class="mb-0 text-truncate text-muted">
                <span class="text-success">
                    @if (TrendingPercentage >= 1)
                    {
                        <i class="mdi mdi-trending-up"></i>
                    }
                    else
                    {
                        <i class="mdi mdi-trending-down"></i>
                    }
                    @String.Format("{0:0.0}", TrendingPercentage)%;
                </span> 
                Last Week
            </p>
        </div>
        <div class="col-auto align-self-center">
            <div class="report-main-icon bg-light-alt">
                <img width="64" height="64" src="https://img.icons8.com/dusk/64/refund-2.png" alt="options-sold" class="align-self-center text-muted icon-sm" />
            </div>
        </div>
    </div>
</div>

@code {
    public string? TotalOptionsSold { get; set; }
    public decimal TrendingPercentage { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        var totalOptionsSold = await SalesPriceService.GetTotalOptionsSold();

        if (totalOptionsSold != null)
        {
            if (totalOptionsSold.Value != null)
            {
                TotalOptionsSold = totalOptionsSold.Value.TotalOptionsSold;
                TrendingPercentage = Convert.ToDecimal(totalOptionsSold.Value.TrendingPercentage);
            }
        }
    }
}
