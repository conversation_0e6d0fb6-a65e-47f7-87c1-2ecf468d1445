﻿@using ERP.Data.Models;
@using ERP.Data.Models.Dto;
@inject ColorSchemeService ColorSchemeService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Plans for Scheme @ColorScheme
    </WindowTitle>
    <WindowContent>
        <div>
            @foreach(var plan in PlansThisScheme)
            {
                <p>@plan</p>
            }
        </div>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }

    [Parameter]
    public string? ColorScheme { get; set; }
    [Parameter]
    public EventCallback<AddCustomEstimateModel> HandleAddSubmit { get; set; }

    public List<string>? PlansThisScheme { get; set; } = new List<string>();
    public void Show()
    {
        IsModalVisible = true;
        StateHasChanged();
    }
    protected override async Task OnParametersSetAsync()
    {
        if (ColorScheme != null)
        {
            PlansThisScheme = await ColorSchemeService.GetPlansForSchemeAsync(ColorScheme);
        }
    }
    public async Task Hide()
    {
        IsModalVisible = false;
    }

}
