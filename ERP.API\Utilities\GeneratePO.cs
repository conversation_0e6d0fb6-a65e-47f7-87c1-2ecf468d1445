﻿using Telerik.Documents.Primitives;
using Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.Export;
using Telerik.Windows.Documents.Fixed.FormatProviders.Pdf;
using Telerik.Windows.Documents.Fixed.Model.ColorSpaces;
using Telerik.Windows.Documents.Fixed.Model.Editing;
using Telerik.Windows.Documents.Fixed.Model.Fonts;
using Telerik.Windows.Documents.Fixed.Model;
using ERP.Data.Models;
using Table = Telerik.Windows.Documents.Fixed.Model.Editing.Tables.Table;
using TableRow = Telerik.Windows.Documents.Fixed.Model.Editing.Tables.TableRow;
using Telerik.Documents.Core.Fonts;
using Telerik.Windows.Documents.Fixed.Model.Editing.Tables;
using FontFamily = Telerik.Documents.Core.Fonts.FontFamily;

//using DocumentFormat.OpenXml.Drawing;

namespace ERP.API.DocumentProcessing
{
    public static class GeneratePO
    {
        public static byte[] GenerateFile(PoheaderDto po, List<PodetailDto> poDetails)
        {
            PdfFormatProvider formatProvider = new PdfFormatProvider();
            formatProvider.ExportSettings.ImageQuality = ImageQuality.High;

            Telerik.Documents.ImageUtils.ImagePropertiesResolver defaultImagePropertiesResolver = new Telerik.Documents.ImageUtils.ImagePropertiesResolver();
            Telerik.Windows.Documents.Extensibility.FixedExtensibilityManager.ImagePropertiesResolver = defaultImagePropertiesResolver;

            byte[] renderedBytes = null;
            using (MemoryStream ms = new MemoryStream())
            {
                RadFixedDocument document = CreateDocument(po, poDetails);
                formatProvider.Export(document, ms);
                renderedBytes = ms.ToArray();
            }

            return renderedBytes;
        }
        private static readonly double defaultLeftIndent = 50;
        private static readonly double defaultLineHeight = 18;
        public static RadFixedDocument CreateDocument(PoheaderDto po, List<PodetailDto> poDetails)
        {
            RadFixedDocument document = new RadFixedDocument();
            double currentTopOffset = 50;
            var newEditor = new RadFixedDocumentEditor(document);
            newEditor.SectionProperties.PageMargins = new Thickness(50, 50, 50, 100);
            var rootpath = Directory.GetCurrentDirectory();

            Block block = new Block();
            block.GraphicProperties.FillColor = RgbColors.Black;
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
            using (FileStream fs = new FileStream(
                System.IO.Path.Combine(rootpath, @"wwwroot/assets/images/vmlogo.png"),
                FileMode.Open,
                FileAccess.Read))
            {
                block.InsertImage(fs);
            }
            newEditor.InsertBlock(block);

            RadFixedPage currentPage = document.Pages[0];
            var width = currentPage.Size.Width;
            var middle = currentPage.Size.Width / 2;
            var widthExceptMargin = width - 100;//left right margins are set to 50 above
            var halfPageWidth = widthExceptMargin / 2;
            FixedContentEditor editor = new FixedContentEditor(currentPage);
           
            editor.Position.Translate(595, 45);           
            InsertPOBox(editor, po);

            InsertVendorBlock(editor, po, halfPageWidth);

            InsertJobAdressBlock(editor, po, middle, halfPageWidth);
            
            InsertJobTypeBlock(editor, po, widthExceptMargin);

            if(po.Postatus == 5)
            {
                InsertCancelledBox(editor, po);
            }
            
            editor.Position.Translate(50, 300);
            InsertActivity(editor, po);

            newEditor.InsertLineBreak();
            newEditor.InsertLineBreak();
            newEditor.InsertLineBreak();
            newEditor.InsertLineBreak();
            newEditor.InsertLineBreak();
            newEditor.InsertLineBreak();
            newEditor.InsertLineBreak();
            newEditor.InsertLineBreak();
            newEditor.InsertLineBreak();
            newEditor.InsertLineBreak();
            newEditor.InsertLineBreak();
            newEditor.InsertLineBreak();
            newEditor.InsertLineBreak();
            newEditor.InsertLineBreak();

            var Items = poDetails;
            InsertItemsTable(newEditor, Items);
            InsertTotal(newEditor, po.Pototal);
            newEditor.InsertLineBreak();

            if (po.Postatus == 4)
            {
                InsertApprovals(editor, po);
            }
            InsertDisclaimer(editor);

            DrawFooterToDocument(document);
            newEditor.Dispose();
            return document;
        }
        private static void InsertCancelledBox(FixedContentEditor editor, PoheaderDto po)
        {
            editor.Position.Translate(350, 650);
            Block block = new Block();
            block.GraphicProperties.FillColor = new RgbColor(255, 0, 0);
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
            block.TextProperties.Font = FontsRepository.HelveticaBoldOblique;
            block.TextProperties.FontSize = 28;
            block.InsertText("CANCELLED");
            block.InsertLineBreak();
            editor.DrawBlock(block);
            editor.Position.Translate(50, 500);
        }
        private static void InsertApprovals(FixedContentEditor editor, PoheaderDto po)
        {
            editor.Position.Translate(50, 960);
            Block block = new Block();
            block.InsertText($"Van Metre Approved By: {po.TaskCompleteBy } Approved Date: {po.Podateapproved: MM/dd/yyyy}");
            block.InsertLineBreak();
            editor.DrawBlock(block);
        }
        private static void InsertPOBox(FixedContentEditor editor, PoheaderDto po)
        {
            Block block = new Block();
            block.GraphicProperties.FillColor = RgbColors.Black;
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Right;
            block.TextProperties.Font = FontsRepository.HelveticaBoldOblique;
            block.InsertText("Purchase Order Number:");
            block.InsertLineBreak();            
            block.InsertText(po.Ponumber);
            block.InsertLineBreak();
            block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Job Number: ");
            block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $"{po.Pojobnumber}");
            block.TextProperties.Font = FontsRepository.Helvetica;
            block.InsertLineBreak();
            block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Order Date: ");
            block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $"{po.Podateissued:MM/dd/yyyy}");
            editor.DrawBlock(block);
        }
        private static void InsertActivity(FixedContentEditor editor, PoheaderDto poHeader)
        {
            Block block = new Block();
            block.GraphicProperties.FillColor = RgbColors.Black;
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
            block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"{poHeader.Podescription}");
            block.InsertLineBreak();
            //Point leftLineStart = new Point(0, 0);
            //Point rightLineEnd = new Point(680, 0);
            //block.InsertLine(leftLineStart, rightLineEnd);
            block.InsertLineBreak();
            //  block.TextProperties.Font = FontsRepository.HelveticaBoldOblique;
            
            block.TextProperties.Font = FontsRepository.Helvetica;
            //  block.InsertText("Option details here ");TODO: group by option
            editor.DrawBlock(block);
        }
      
        private static void InsertItemsTable(RadFixedDocumentEditor editor, List<PodetailDto> Items)
        {
            try
            {
                var itemsGroupedByOption = Items.GroupBy(x => x.OptionNumber);
                foreach(var optionGroup in itemsGroupedByOption)
                {
                    var optionNumber = optionGroup.Key;
                    Table optionTable = new Table();
                    optionTable.DefaultCellProperties.Padding = new Thickness(5, 5, 0, 0);
                    optionTable.LayoutType = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.TableLayoutType.AutoFit;
                    TableRow optionfirstRow = optionTable.Rows.AddTableRow();
                    TableCell optionc1 = optionfirstRow.Cells.AddTableCell();
                    optionc1.Blocks.AddBlock().InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Option Code");
                    optionc1.PreferredWidth = 155;
                    TableCell optionc2 = optionfirstRow.Cells.AddTableCell();
                    optionc2.Blocks.AddBlock().InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Option Description");
                    optionc2.PreferredWidth = 600;
                    TableRow optionaddRow = optionTable.Rows.AddTableRow();
                    TableCell optionCell1 = optionaddRow.Cells.AddTableCell();
                    optionCell1.Blocks.AddBlock().InsertText($"{optionNumber}");
                    optionCell1.PreferredWidth = 155;
                    TableCell optionCell2 = optionaddRow.Cells.AddTableCell();
                    optionCell2.Blocks.AddBlock().InsertText($"{optionGroup.First().OptionDesc}");//it should be the same for all in the group
                    optionCell2.PreferredWidth = 600;
                    editor.InsertTable(optionTable);

                    //notes might be redundant with attribute selections on some jobs, not sure
                    if (optionGroup.Sum(x => x.Poamount) == 0 && (!string.IsNullOrWhiteSpace(optionGroup.First().Optionselections) || !string.IsNullOrWhiteSpace(optionGroup.First().Podetailoptions?.OptionNotes)))
                    {
                        Block block = new Block();
                        block.GraphicProperties.FillColor = RgbColors.Black;
                        block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
                        block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Note Selections Change");
                        editor.InsertBlock(block);
                    }

                    //show attribute selections if any
                    if (!string.IsNullOrWhiteSpace(optionGroup.First().Optionselections))
                    {
                        Block block = new Block();
                        block.GraphicProperties.FillColor = RgbColors.Black;
                        block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
                        block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $"Selections: {optionGroup.First().Optionselections}");
                        editor.InsertBlock(block);
                    }
                    //notes might be redundant with attribute selections on some jobs, not sure
                    if (!string.IsNullOrWhiteSpace(optionGroup.First().Podetailoptions?.OptionNotes))
                    {
                        Block block = new Block();
                        block.GraphicProperties.FillColor = RgbColors.Black;
                        block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
                        block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $"Notes: {optionGroup.First().Podetailoptions?.OptionNotes}");
                        editor.InsertBlock(block);
                    }
                    

                    editor.InsertLineBreak();
                    Table table = new Table();
                    table.DefaultCellProperties.Padding = new Thickness(5, 5, 0, 0);
                    table.LayoutType = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.TableLayoutType.AutoFit;
                    TableRow firstRow = table.Rows.AddTableRow();
                    //firstRow.Cells.AddTableCell().Blocks.AddBlock().InsertText("Item No.");
                    TableCell c1 = firstRow.Cells.AddTableCell();
                    c1.Blocks.AddBlock().InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Item No.");
                    c1.PreferredWidth = 155;
                    TableCell c2 = firstRow.Cells.AddTableCell();
                    c2.Blocks.AddBlock().InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Item Description");
                    c2.PreferredWidth = 355;
                    TableCell c3 = firstRow.Cells.AddTableCell();
                    c3.Blocks.AddBlock().InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Qty");
                    c3.PreferredWidth = 55;
                    TableCell c4 = firstRow.Cells.AddTableCell();
                    c4.Blocks.AddBlock().InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Unit");
                    c4.PreferredWidth = 55;
                    TableCell c5 = firstRow.Cells.AddTableCell();
                    Block headerBlock5 = new Block();
                    headerBlock5.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Right;
                    headerBlock5.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Unit Cost");
                    c5.Blocks.Add(headerBlock5);
                    c5.PreferredWidth = 65;
                    TableCell c6 = firstRow.Cells.AddTableCell();
                    Block headerBlock6 = new Block();
                    headerBlock6.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Right;
                    headerBlock6.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Total");
                    c6.Blocks.Add(headerBlock6);
                    c6.PreferredWidth = 65;
                    foreach (var item in optionGroup)
                    {
                        TableRow addRow = table.Rows.AddTableRow();
                        TableCell cell1 = addRow.Cells.AddTableCell();
                        cell1.Blocks.AddBlock().InsertText($"{item.PhaseCode} {item.ItemNumber}");
                        cell1.PreferredWidth = 150;
                        TableCell cell2 = addRow.Cells.AddTableCell();
                        cell2.Blocks.AddBlock().InsertText($"{item.Poitemdesc}");
                        cell2.PreferredWidth = 360;
                        TableCell cell3 = addRow.Cells.AddTableCell();
                        cell3.Blocks.AddBlock().InsertText(item.Pounitqty == 0 ? "No Change" : $"{item.Pounitqty}");
                        cell3.PreferredWidth = 55;
                        TableCell cell4 = addRow.Cells.AddTableCell();
                        cell4.Blocks.AddBlock().InsertText($"{item.Pounit}");
                        cell4.PreferredWidth = 55;
                        TableCell cell5 = addRow.Cells.AddTableCell();
                        Block block5 = new Block();
                        block5.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Right;
                        block5.InsertText($"{item.Pounitcost:n2}");
                        cell5.Blocks.Add(block5);
                        cell5.PreferredWidth = 65;
                        TableCell cell6 = addRow.Cells.AddTableCell();
                        Block block6 = new Block();
                        block6.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Right;
                        block6.InsertText(item.Poamount == 0 ? "No Change" :$"{item.Poamount:n2}");
                        cell6.Blocks.Add(block6);
                        cell6.PreferredWidth = 65;
                    }
                    editor.InsertTable(table);
                    editor.InsertLineBreak();
                }
            }
            catch (Exception ex)
            {
                var debug = ex.Message;
            }

        }
        private static void InsertModelTypeTable(RadFixedDocumentEditor editor, PoheaderDto po)
        {
            try
            {
                Table table = new Table();
                table.DefaultCellProperties.Padding = new Thickness(5, 5, 0, 0);
                table.LayoutType = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.TableLayoutType.AutoFit;
                TableRow firstRow = table.Rows.AddTableRow();
                TableCell c1 = firstRow.Cells.AddTableCell();
                c1.Blocks.AddBlock().InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Model");
                c1.PreferredWidth = 150;
                TableCell c2 = firstRow.Cells.AddTableCell();
                c2.Blocks.AddBlock().InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Lot Swing");
                c2.PreferredWidth = 360;

                TableRow addRow = table.Rows.AddTableRow();
                TableCell cell1 = addRow.Cells.AddTableCell();
                cell1.Blocks.AddBlock().InsertText($"{po.PojobnumberNavigation.PlanName}");
                cell1.PreferredWidth = 150;
                TableCell cell2 = addRow.Cells.AddTableCell();
                cell2.Blocks.AddBlock().InsertText($"{po.PojobnumberNavigation.LotSwing}");
                cell2.PreferredWidth = 360;
                
                editor.InsertTable(table);
            }
            catch (Exception ex)
            {
                var debug = ex.Message;
            }

        }

        private static void InsertVendorBlock(FixedContentEditor editor, PoheaderDto po, double blockWidth = 350)
        {
            editor.Position.Translate(50, 110);
            Block block = new Block();
            block.GraphicProperties.FillColor = RgbColors.Black;
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
            editor.GraphicProperties.StrokeColor = RgbColors.Black;
            editor.GraphicProperties.FillColor = RgbColors.Transparent;
            Rect rect = new Rect(0, 0, blockWidth, 120);
            editor.DrawRectangle(rect);
            editor.Position.Translate(55, 115);
            block.GraphicProperties.FillColor = RgbColors.Black;
            block.TextProperties.Font = FontsRepository.HelveticaBoldOblique;
            block.InsertText("VENDOR");
            block.InsertLineBreak();
            block.TextProperties.Font = FontsRepository.Helvetica;
            block.InsertText($"{po.SubNumberNavigation.SubName}");
            block.InsertLineBreak();
            if (!string.IsNullOrWhiteSpace(po.SubNumberNavigation.SubAddress))
            {
                block.InsertText(po.SubNumberNavigation.SubAddress);
                block.InsertLineBreak();
            }
            if (!string.IsNullOrWhiteSpace(po.SubNumberNavigation.SubAddress2))
            {            
                block.InsertText(po.SubNumberNavigation.SubAddress2);
                block.InsertLineBreak();
            }
            if (!string.IsNullOrWhiteSpace(po.SubNumberNavigation.SubCity))
            {
                block.InsertText($"{po.SubNumberNavigation.SubCity}, {po.SubNumberNavigation.SubState} {po.SubNumberNavigation.SubPostcode}");
                block.InsertLineBreak();
            }
            if (!string.IsNullOrWhiteSpace(po.SubNumberNavigation.SubPhone))
            {
                block.InsertText($"{po.SubNumberNavigation.SubPhone}");
                block.InsertLineBreak();
            }
            block.Measure();
            editor.DrawBlock(block);
        }
        private static void InsertJobAdressBlock(FixedContentEditor editor, PoheaderDto po, double middle = 400, double blockWidth = 350 )
        {
            
            editor.Position.Translate(middle, 110);
            Block block = new Block();
            block.GraphicProperties.FillColor = RgbColors.Black;
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
            editor.GraphicProperties.StrokeColor = RgbColors.Black;
            editor.GraphicProperties.FillColor = RgbColors.Transparent;
            Rect rect = new Rect(0, 0, blockWidth, 120);
            editor.DrawRectangle(rect);
            editor.Position.Translate(middle + 5, 115);
            block.GraphicProperties.FillColor = RgbColors.Black;
            block.TextProperties.Font = FontsRepository.HelveticaBoldOblique;
            block.InsertText("JOB INFORMATION");
            block.InsertLineBreak();
            block.InsertText($"{po.PojobnumberNavigation.Subdivision.SubdivisionName}");
            block.InsertLineBreak();
            block.TextProperties.Font = FontsRepository.Helvetica;
            block.InsertText($"Lot Number:{po.PojobnumberNavigation.LotNumber}");
            block.InsertLineBreak();
            block.InsertText($"{po.PojobnumberNavigation.JobAddress1}");
            block.InsertLineBreak();
            if(!string.IsNullOrWhiteSpace(po.PojobnumberNavigation.JobAddress2))
            {
                block.InsertText($"{po.PojobnumberNavigation.JobAddress2}");
                block.InsertLineBreak();
            }
            block.InsertText($"{po.PojobnumberNavigation.JobCity}, {po.PojobnumberNavigation.JobState} {po.PojobnumberNavigation.JobZipCode}");
            block.InsertLineBreak();
            //block.InsertText($"Permit #:");
            //block.InsertLineBreak();
            block.Measure();
            //Rect boundingRect = new Rect(new Point(0, 0), new Size(200, 300));
            editor.DrawBlock(block, new Size(300, 200)); 
        }
        private static void InsertJobTypeBlock(FixedContentEditor editor, PoheaderDto po, double blockWidth = 700)
        {
            editor.Position.Translate(50, 240);
            Block block = new Block();
            block.GraphicProperties.FillColor = RgbColors.Black;
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
            editor.GraphicProperties.StrokeColor = RgbColors.Black;
            editor.GraphicProperties.FillColor = RgbColors.Transparent;
            Rect rect = new Rect(0, 0, blockWidth, 45);
            editor.DrawRectangle(rect);
            //editor.Position.Translate(55, 255);
            //block.GraphicProperties.FillColor = RgbColors.Black;
            //block.TextProperties.Font = FontsRepository.HelveticaBoldOblique;            
            //block.InsertText("  Model     Lot Swing");
            //block.InsertLineBreak();
            //block.TextProperties.Font = FontsRepository.Helvetica;
            //block.InsertText($" {po.PojobnumberNavigation.PlanName}     {po.PojobnumberNavigation.LotSwing}   ");
            //block.InsertLineBreak();

            Table table = new Table();
            table.DefaultCellProperties.Padding = new Thickness(5, 5, 0, 0);
            table.LayoutType = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.TableLayoutType.AutoFit;
            TableRow firstRow = table.Rows.AddTableRow();
            TableCell c1 = firstRow.Cells.AddTableCell();
            c1.Blocks.AddBlock().InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Model");
            c1.PreferredWidth = 150;
            TableCell c2 = firstRow.Cells.AddTableCell();
            c2.Blocks.AddBlock().InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Lot Swing");
            c2.PreferredWidth = 360;

            TableRow addRow = table.Rows.AddTableRow();
            TableCell cell1 = addRow.Cells.AddTableCell();
            cell1.Blocks.AddBlock().InsertText($"{po.PojobnumberNavigation.PlanName}");
            cell1.PreferredWidth = 150;
            TableCell cell2 = addRow.Cells.AddTableCell();
            cell2.Blocks.AddBlock().InsertText($"{po.PojobnumberNavigation.LotSwing}");
            cell2.PreferredWidth = 360;

            editor.DrawTable(table);


            editor.DrawBlock(block);
        }
        
        private static void InsertTotal(RadFixedDocumentEditor editor, double? total)
        {
            editor.InsertLineBreak();
            Block block = new Block();
            block.GraphicProperties.FillColor = RgbColors.Black;
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Right;
            Point leftLineStart = new Point(0, 0);
            Point rightLineEnd = new Point(100, 0);
            block.InsertLine(leftLineStart, rightLineEnd);
            block.InsertLineBreak();
            block.TextProperties.Font = FontsRepository.Helvetica;
            block.InsertText($"Total:   {total.Value.ToString("c")}");
            block.InsertLineBreak();
            block.InsertText("Tax: $0.00");
            block.InsertLineBreak();
            block.TextProperties.Font = FontsRepository.HelveticaBold;
            block.InsertText($"TOTAL ORDER: {total.Value.ToString("c")}");

            editor.InsertBlock(block);
        }
        private static void InsertMoreDescription(FixedContentEditor editor)
        {
            Block block = new Block();
            block.GraphicProperties.FillColor = RgbColors.Black;
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;

            block.TextProperties.Font = FontsRepository.HelveticaBoldOblique;
            block.InsertText("Please supply the following:");
            block.InsertLineBreak();
            editor.DrawBlock(block);
        }
        private static void InsertDisclaimer(FixedContentEditor editor)
        {
            Block block = new Block();
            block.GraphicProperties.FillColor = RgbColors.Black;
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
            block.TextProperties.Font = FontsRepository.TimesRoman;
            block.TextProperties.FontSize = 10;
            block.InsertText("This is an order to perform work. Please review costs. If no errors are reported within 24 hour from Order Date, the order will be considered correct.");
            block.Measure();
            editor.Position.Translate(50, 980);
            editor.DrawBlock(block);
        }
        public static void DrawFooterToDocument(RadFixedDocument document)
        {
            int numberOfPages = document.Pages.Count;
            for (int pageIndex = 0; pageIndex < numberOfPages; pageIndex++)
            {
                int pageNumber = pageIndex + 1;
                RadFixedPage currentPage = document.Pages[pageIndex];
                DrawFooterToPage(currentPage, pageNumber, numberOfPages);
            }
        }
        private static void DrawFooterToPage(RadFixedPage page, int pageNumber, int numberOfPages)
        {
            FixedContentEditor pageEditor = new FixedContentEditor(page);

            Block footer = new Block();
            footer.InsertText(String.Format("Page {0} of {1}", pageNumber, numberOfPages));
            footer.Measure();

            double footerOffsetX = (page.Size.Width / 2) - (footer.DesiredSize.Width / 2);
            double footerOffsetY = page.Size.Height - 30 - footer.DesiredSize.Height;
            pageEditor.Position.Translate(footerOffsetX, footerOffsetY);
            pageEditor.DrawBlock(footer);
        }
    }
}
