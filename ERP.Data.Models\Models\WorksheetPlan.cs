﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class WorksheetPlan
{
    public int WorksheetPlanId { get; set; }

    public int WorksheetId { get; set; }

    public int PhasePlanId { get; set; }

    public double? Costprice { get; set; }

    public double? Markup { get; set; }

    public double? Sellprice { get; set; }

    public DateTime? Pricedate { get; set; }

    public int? Markuptype { get; set; }

    public double? Markuppercent { get; set; }

    public double? Marketvalue { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual PhasePlan PhasePlan { get; set; } = null!;

    public virtual Worksheet Worksheet { get; set; } = null!;

    public virtual ICollection<WorksheetOpt> WorksheetOpts { get; set; } = new List<WorksheetOpt>();

    public virtual ICollection<WorksheetPlanAct> WorksheetPlanActs { get; set; } = new List<WorksheetPlanAct>();
}
