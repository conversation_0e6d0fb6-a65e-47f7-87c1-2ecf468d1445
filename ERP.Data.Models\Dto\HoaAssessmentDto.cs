﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
namespace ERP.Data.Models.Dto;

public class HoaAssessmentDto : IMapFrom<HoaAssessment>
{
    public int? HoaAssessmentId { get; set; }

    public int HoaId { get; set; }

    public int SubdivisionId { get; set; }

    public string AssessmentLabel { get; set; } = null!;

    public decimal? Icc { get; set; }

    public decimal? MonthAssessment { get; set; }

    public decimal? OtherFee { get; set; }

    [Required(ErrorMessage = "Budget Year is required")]
    public DateTime? BudgetYear { get; set; }

    public DateTime? CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    // public byte[] RecordTimeStamp { get; set; } = null!;

    public HoaDto? Hoa { get; set; }

    // public virtual ICollection<HoaJob> HoaJobs { get; set; } = new List<HoaJob>();

    public SubdivisionDto? Subdivision { get; set; }

    public void Mapping(Profile profile)
    {
        profile.CreateMap<HoaAssessmentDto, HoaAssessment>().ReverseMap();
    }
}
