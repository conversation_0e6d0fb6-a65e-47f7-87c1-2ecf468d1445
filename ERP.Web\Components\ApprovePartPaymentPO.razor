﻿@inject PoService PoService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Top="100px"
               Width="300px"
               Height="350px">
    <WindowTitle>
        Partial Payment PO
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@PoHeader" OnValidSubmit="ApprovePartPayment">
            
            <div class="mb-3">
                <label class="form-label">PO Description</label><br />
                <TelerikTextBox Enabled="false" @bind-Value="@PoHeader.Podescription" />
            </div>
            <div class="mb-3">
                <label class="form-label">Remaining Total:</label><br />
                <TelerikNumericTextBox Enabled="false" Format="C" Decimals="2" @bind-Value="@RemainingTotal" />
            </div>

            <div class="mb-3">
                <label class="form-label">Partial Payment Value</label><br />
                <TelerikNumericTextBox Id="PartialPayment" Format="C" Decimals="2" @bind-Value="@PoHeader.PartPaymentAmt" Max="RemainingTotal"></TelerikNumericTextBox>
            </div>

            <br />
            <button type="submit" class="btn btn-primary">Update</button>
            <button type="button" @onclick="Cancel" class="btn btn-secondary">Cancel</button>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>


@code {
    public bool IsModalVisible { get; set; }

    [Parameter]
    public PoheaderDto? PoHeader { get; set; }
    [Parameter]
    public EventCallback<ResponseModel<PoheaderDto>> HandlePartPayment { get; set; }
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    public PoheaderDto? SelectedPoHeader { get; set; }
    public double RemainingTotal { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        if (PoHeader != null)
        {
            RemainingTotal = (double) (PoHeader.RemainingTotal??0d);
        }
    }

    public void Show()
    {
        IsModalVisible = true;

        StateHasChanged();
    }

    public async Task Hide()
    {
        IsModalVisible = false;
    }

    private async void Cancel()
    {
        IsModalVisible = false;
    }

    private async Task ApprovePartPayment()
    {
        if(PoHeader.PartPaymentAmt == null)
        {
            await Dialogs.AlertAsync("No amount entered");
            return;
        }
        var response = await PoService.ApprovePo(PoHeader);
        await HandlePartPayment.InvokeAsync(response);
    }
}
