﻿@inject AttributeService AttributeService
@using ERP.Data.Models
@inject AttributeItemPickService AttributeItemPickService
@implements IDisposable

<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

@if (AttributeItem == null)
{
    <p><em>Loading...</em></p>
    <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingOptions />
}
else
{
    <TelerikGrid Data="@AttributeItem"
                 SelectionMode="GridSelectionMode.Single"
                 EditMode="@GridEditMode.Inline"
                 RowDraggable="true"
                 OnDelete="@OnDeleteHandler">
        <GridToolBarTemplate>
            <span class="k-toolbar-spacer"></span>
            <GridSearchBox />
        </GridToolBarTemplate>
        <GridColumns>
            <GridColumn Field="Description" Editable="false"></GridColumn>
            <GridColumn Field="IsActive" Title="Is Active?" Editable="false"></GridColumn>
            <GridCommandColumn>
                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                @* <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton> *@
                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                @* <GridCommandButton Command="Reactive" Icon="@FontIcon.Link" ShowInEdit="false" OnClick="@ReactiveClickHandler"></GridCommandButton> *@
            </GridCommandColumn>
        </GridColumns>
    </TelerikGrid>
}

@code {
    /// <summary>
    ///
    /// </summary>
    [Parameter] public int AttributeGroupId { get; set; }
    public List<MasterAttributeItemDto>? AttributeItem { get; set; }
    public bool IsLoadingOptions { get; set; } = false;
    [Parameter] public int MasterOptionId { get; set; }
    [Parameter] public int? PlanOptionId { get; set; }

    /// <summary>
    /// Delegate
    /// </summary>
    private bool? checkStatus;

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    /// <summary>
    /// Reset
    /// </summary>
    int RequestCount { get; set; }
    string? errorMessage { get; set; } = string.Empty;

    /// <summary>
    ///
    /// </summary>
    /// <returns></returns>
    protected override async Task OnParametersSetAsync()
    {
        var result = await AttributeService.GetAttributeAssignmentByGroupAndOptionIdAsync(AttributeGroupId, MasterOptionId, PlanOptionId);
        AttributeItem = result.Value;
    }

    protected override void OnInitialized()
    {
        this.AttributeItemPickService.OnDataChanged += RefreshData;
    }

    /// <summary>
    /// Reload
    /// </summary>
    async Task RefreshData()
    {
        checkStatus = this.AttributeItemPickService.IsChanged!;
        errorMessage = this.AttributeItemPickService.ErrorMessage;
        await this.InvokeAsync(StateHasChanged);

        if (checkStatus == true)
        {
            RequestCount++;

            // This is to suppress delegate calls
            if (RequestCount == 1)
            {
                // Notifications
                if (!string.IsNullOrWhiteSpace(errorMessage))
                {
                    NotificationReference.Show(new NotificationModel()
                        {
                            Text = errorMessage,
                            ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                        });
                }
                else
                {
                    NotificationReference.Show(new NotificationModel()
                        {
                            Text = "Successfully added item attribute",
                            ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                        });
                }

                // Reload
                var result = await AttributeService.GetAttributeAssignmentByGroupAndOptionIdAsync(AttributeGroupId, MasterOptionId, PlanOptionId);
                AttributeItem = result.Value;

                // Refresh all components
                StateHasChanged();
            }

            RequestCount--;
            errorMessage = string.Empty;
            this.AttributeItemPickService.IsChanged = false;
            this.AttributeItemPickService.ErrorMessage = string.Empty;
        }
    }

    /// <summary>
    /// Delete
    /// </summary>
    /// <returns></returns>
    async Task OnDeleteHandler(GridCommandEventArgs args)
    {
        var attributeItem = (MasterAttributeItemDto)args.Item;

        var result = await AttributeService.DeleteOptionAttributeGroupItemAsync(attributeItem);

        // Reload
        var data = await AttributeService.GetAttributeAssignmentByGroupAndOptionIdAsync(AttributeGroupId, MasterOptionId, PlanOptionId);
        AttributeItem = data.Value;

        // Notifications
        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Successfully deleted item attribute",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        // Refresh all components
        StateHasChanged();
    }

    /// <summary>
    /// Reactivate
    /// </summary>
    /// <param name="args"></param>
    /// <returns></returns>
    private async Task ReactiveClickHandler(GridCommandEventArgs args)
    {
        var attributeItem = (MasterAttributeItemDto)args.Item;

        var result = await AttributeService.ReactivateOptionAttributeGroupItemAsync(attributeItem);

        // Reload
        var data = await AttributeService.GetAttributeAssignmentByGroupAndOptionIdAsync(AttributeGroupId, MasterOptionId, PlanOptionId);
        AttributeItem = data.Value;

        // Notifications
        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Successfully reactivate item attribute",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        // Refresh all components
        StateHasChanged();
    }

    /// <summary>
    ///
    /// </summary>
    public void Dispose()
    {
        this.AttributeItemPickService.OnDataChanged -= RefreshData;
    }
}
