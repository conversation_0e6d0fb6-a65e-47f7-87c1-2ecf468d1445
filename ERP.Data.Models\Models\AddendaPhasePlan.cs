﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class AddendaPhasePlan
{
    public int Id { get; set; }

    public int? AddendaId { get; set; }

    public int? PhasePlanId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual Addendum? Addenda { get; set; }

    public virtual PhasePlan? PhasePlan { get; set; }
}
