﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class JccosttypeDto : IMapFrom<Jccosttype>
{
    public int JccosttypeId { get; set; }

    public string? Jccosttype1 { get; set; }

    public int? AcntDbId { get; set; }

    public string? Defaulttaxgroup { get; set; }

    public int? IgCostClassNumber { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    // public virtual ICollection<Jccategory> Jccategories { get; set; } = new List<Jccategory>();
    public void Mapping(Profile profile)
    {
        profile.CreateMap<JccosttypeDto, Jccosttype>().ReverseMap();
    }
}
