﻿@page "/supplierdetails/{subnumber:int}"
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector, ConstructionManager")]
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject PlanService PlanService
@inject TradeService TradeService
@inject SupplierService SupplierService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
<style>
    .k-button-danger {
        border-color: #e6a7a7;
        color: #000000;
        background-color: #e6a7a7;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>

<PageTitle>Supplier Details - @supplierDetails?.SubName</PageTitle>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<div class="col-lg-12">
    <div class="card" style="background-color:#2e5771">
        <div class="card-body" style="padding:0.5rem">
            <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Supplier Details For: @supplierDetails?.SubName</h7>
        </div>
    </div>
</div>

<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="/">Home</a></li>
    <li class="breadcrumb-item"><a href="/suppliers">Suppliers</a></li>
    <li class="breadcrumb-item active">Supplier Details</li>
</ol>

<ErrorBoundary>
    <ChildContent>
        <EditForm Model="@supplierDetails" OnValidSubmit="@HandleValidSubmit">
            <TelerikTabStrip ActiveTabIndex="@ActiveTabIndex" ActiveTabIndexChanged="@TabChangedHandler">
                <TabStripTab Title="Supplier Details">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-5">
                                <DataAnnotationsValidator />
                                <ValidationSummary />
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Supplier Name:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubName" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Short Name:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.ShortName" Enabled="false" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">AP Number:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubApNumber" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Address:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubAddress" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Address 2:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubAddress2" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">City:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubCity" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">State:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubState" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">IsActive:</label>
                                    <div class="col-sm-8">
                                        <TelerikCheckBox @bind-Value="supplierDetails.IsActive1" Enabled="@EditEnabled"></TelerikCheckBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Blocked:</label>
                                    <div class="col-sm-8">
                                        <TelerikCheckBox @bind-Value="supplierDetails.BoolBlocked" Enabled="false"></TelerikCheckBox>
                                    </div>
                                </div>
                            </div>
                            <div class="col-5">
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Zip:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubPostcode" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Country:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubCountry" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Phone:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubPhone" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Phone 2:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubPhone2" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Email:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.Email" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Fax:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.Fax" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">ABN:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.AbnNumber" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Retainage:</label>
                                    <div class="col-sm-8">
                                        <TelerikNumericTextBox @bind-Value="supplierDetails.Retainage" Enabled="@EditEnabled" Width="200px"></TelerikNumericTextBox>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if(AllowEdit)
                        {
                            <div class="mb-3 row">
                                <div class="col-1"></div>
                                <div class="col">
                                    <TelerikButton Enabled="@EditEnabled" ButtonType="ButtonType.Submit" Icon="@FontIcon.Save" Class="k-button-success">Update</TelerikButton>
                                </div>
                            </div>
                        }
                        
                    </div>
                </TabStripTab>
                @* <TabStripTab Title="Tax">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-5">
                                <DataAnnotationsValidator />
                                <ValidationSummary />
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Payment days:</label>
                                    <div class="col-sm-8">
                                        <TelerikNumericTextBox @bind-Value="supplierDetails.PaymentDays" Enabled="@EditEnabled" Width="200px"></TelerikNumericTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Discount Days:</label>
                                    <div class="col-sm-8">
                                        <TelerikNumericTextBox @bind-Value="supplierDetails.DiscountDays" Enabled="@EditEnabled" Width="200px"></TelerikNumericTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Discount Percent:</label>
                                    <div class="col-sm-8">
                                        <TelerikNumericTextBox @bind-Value="supplierDetails.DiscountPercent" Enabled="@EditEnabled" Width="200px"></TelerikNumericTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Tax Exempt:</label>
                                    <div class="col-sm-8">
                                        <TelerikCheckBox @bind-Value="supplierDetails.IsSubtaxexempt" Enabled="@EditEnabled"></TelerikCheckBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Recieves Form 1099:</label>
                                    <div class="col-sm-8">
                                        <TelerikCheckBox @bind-Value="supplierDetails.IsReceivesForm1099" Enabled="@EditEnabled"></TelerikCheckBox>
                                    </div>
                                </div>

                            </div>
                            <div class="col-5">
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Order Mode:</label>
                                    <div class="col-sm-8">
                                        <TelerikNumericTextBox @bind-Value="supplierDetails.OrderMode" Enabled="@EditEnabled" Width="200px"></TelerikNumericTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Vehicle Ins. Expiry Date:</label>
                                    <div class="col-sm-8">
                                        <TelerikDateInput @bind-Value="supplierDetails.VehicleInsExpiryDate" Enabled="@EditEnabled" Width="200px"></TelerikDateInput>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <div class="col-1"></div>
                            <div class="col">
                                <TelerikButton Enabled="@EditEnabled" ButtonType="ButtonType.Submit" Icon="@FontIcon.Save" Class="k-button-success">Update</TelerikButton>
                            </div>
                        </div>
                    </div>
                </TabStripTab> *@
                <TabStripTab Title="Warranty">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-5">
                                <DataAnnotationsValidator />
                                <ValidationSummary />
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Warranty Enabled:</label>
                                    <div class="col-sm-8">
                                        <TelerikCheckBox @bind-Value="supplierDetails.IsWarrantyEnabled" Enabled="@EditEnabled"></TelerikCheckBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Warranty Contact:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubContactWrty" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Contact Email:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubContactWrtyEmail" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Warranty Contact Phone:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubPhoneWrty" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Warranty Mobile Provider:</label>
                                    <div class="col-sm-8">
                                        <TelerikNumericTextBox @bind-Value="supplierDetails.SubContactWrtyMobileSp" Enabled="@EditEnabled" Width="200px"></TelerikNumericTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Warranty Mobile Phone:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubContactWrtyMobile" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>

                            </div>
                            <div class="col-5">
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Warranty Fax:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubFaxWrty" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                                <div class="mb-3 row">
                                    <label class="col-sm-4 form-label align-self-center mb-lg-0 text-end">Warranty Contact Nextel DC:</label>
                                    <div class="col-sm-8">
                                        <TelerikTextBox @bind-Value="supplierDetails.SubContactWrtyDc" Enabled="@EditEnabled" Width="200px"></TelerikTextBox>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if(AllowEdit)
                        {
                            <div class="mb-3 row">
                                <div class="col-1"></div>
                                <div class="col">
                                    <TelerikButton Enabled="@EditEnabled" ButtonType="ButtonType.Submit" Icon="@FontIcon.Save" Class="k-button-success">Update</TelerikButton>
                                </div>
                            </div>
                        }
                        
                    </div>
                </TabStripTab>
                <TabStripTab Title="Insurance">
                    <div class="card-body">
                        <TelerikGrid 
                            Data="SupplierInsuranceData"
                                     FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                     Sortable="true"
                                     Resizable="true"
                                     Groupable="true">
                            <GridColumns>
                                <GridColumn Field="InsuranceTypeCodeNavigation.Description" Title="Type" />
                                <GridColumn Field="InsuranceCompanyName" Title="Insurance Company" />
                                <GridColumn Field="PolicyNumber" Title="Policy Number" />
                                <GridColumn Field="PolicyStartDate"  DisplayFormat="{0:MM/dd/yyyy}" Title="Policy Start Date" />
                                <GridColumn Field="PolicyExpirationDate" DisplayFormat="{0:MM/dd/yyyy}"  Title="Policy Expiration Date" />
                                <GridColumn Field="Coverageamount" DisplayFormat="{0:C}" Title="Coverage Amount" />
                                <GridColumn Field="IsActive" Title="IsActive" />
                            </GridColumns>
                            <GridToolBarTemplate>
                                <GridSearchBox DebounceDelay="200"></GridSearchBox>
                            </GridToolBarTemplate>
                        </TelerikGrid>        
                    </div>
                </TabStripTab>
                <TabStripTab Title="Types">
                    <div class="card-body">
                        <TelerikGrid 
                            Data="SupplierTypesData"
                            OnDelete="@DeleteSupplierTradeTypeHandler"
                            ConfirmDelete="true"
                            Width="400px">
                            <GridColumns>
                                <GridColumn Field="SupplierTypeName" Title="Type Name" />
                                <GridCommandColumn Width="50px" Context="deleteTradeTypeContext">
                                    <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                </GridCommandColumn>
                            </GridColumns>
                            <GridToolBarTemplate>
                                @if(AllowEdit)
                                {
                                    <GridCommandButton Command="Add" Icon="@FontIcon.Plus" OnClick="@ShowAddSupplierTypeModal" Class="k-button-add">Add Supplier Type</GridCommandButton>
                                }                               
                            </GridToolBarTemplate>
                        </TelerikGrid>        
                    </div>
                </TabStripTab>
                @* <TabStripTab Title="Notes">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <DataAnnotationsValidator />
                                <ValidationSummary />
                                <div class="mb-3 row">
                                    <label class="col-3 form-label align-self-center mb-lg-0 text-end">Notes:</label>
                                    <div class="col-7">
                                        <TelerikTextArea @bind-Value="supplierDetails.SubNotes"></TelerikTextArea>
                                    </div>
                                </div>
                            </div>                            
                        </div>
                        <div class="mb-3 row">
                            <div class="col-1"></div>
                            <div class="col">
                                <TelerikButton ButtonType="ButtonType.Submit" Icon="@FontIcon.Save" Class="k-button-success">Update</TelerikButton>
                            </div>
                        </div>
                    </div>
                </TabStripTab> *@
                <TabStripTab Title="Contacts">
                    <div class="card-body">
                        <div class="row">
                            <TelerikGrid Data=@SupplierContacts
                                         FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                         Height="600px" RowHeight="60" PageSize="20"
                                         Sortable="true"
                                         Resizable="true"
                                         Groupable="true"
                                         SelectionMode="GridSelectionMode.Single"
                                         EditMode="@GridEditMode.Popup"                         
                                         OnUpdate="@UpdateContactHandler"                         
                                         OnEdit="@EditContactHandler"                         
                                         OnDelete="@DeleteContactHandler"                         
                                         OnCreate="@CreateContactHandler"                         
                                         OnCancel="@CancelContactHandler"                         
                                         ConfirmDelete="true"
                                         @ref="@ContactGridRef">
                                <GridColumns>
                                    <GridColumn Field="SupplierContactId" Visible="false" Editable="false" Groupable="false" />
                                    <GridColumn Field="ContactId" Visible="false" Editable="false" Groupable="false" />
                                    <GridColumn Field="SubNumber" Visible="false" Editable="false" Groupable="false" />
                                    <GridColumn Field="FirstName" Title="First Name" Visible="true" Editable="true" Groupable="false" />
                                    <GridColumn Field="LastName" Title="Last Name" Visible="true" Editable="true" Groupable="false" />
                                    <GridColumn Field="Address1" Title="Address1" Visible="true" Editable="true" Groupable="false" Width="0" />
                                    <GridColumn Field="Address2" Title="Address2" Visible="true" Editable="true" Groupable="false" Width="0"/>
                                    <GridColumn Field="City" Title="City" Visible="true" Editable="true" Groupable="false" Width="0"/>
                                    <GridColumn Field="State" Title="State" Visible="true" Editable="true" Groupable="false" Width="0"/>
                                    <GridColumn Field="Postcode" Title="Zip" Visible="true" Editable="true" Groupable="false" Width="0" />
                                    <GridColumn Field="Country" Title="Country" Visible="true" Editable="true" Groupable="false" Width="0" />
                                    <GridColumn Field="WorkPhone" Title="Work Phone" Visible="true" Editable="true" Groupable="false" />
                                    <GridColumn Field="HomePhone" Title="Home Phone" Visible="true" Editable="true" Groupable="false" Width="0" />
                                    <GridColumn Field="MobilePhone" Title="Mobile Phone" Visible="true" Editable="true" Groupable="false" Width="0" />
                                    <GridColumn Field="Fax" Title="Fax" Visible="true" Editable="true" Groupable="false" Width="0" />
                                    <GridColumn Field="Email" Title="Email" Visible="true" Editable="true" Groupable="false" />
                                    <GridColumn Field="IsSupplierContactPurch" Title="Purchasing" EditorType="@GridEditorType.CheckBox"  Visible="true" Editable="true" Groupable="false" />
                                    <GridColumn Field="IsSupplierContactSched" Title="Scheduling" EditorType="@GridEditorType.CheckBox" Visible="true" Editable="true" Groupable="false" />
                                    <GridColumn Field="IsSupplierContactAdmin" Title="Admin" EditorType="@GridEditorType.CheckBox" Visible="true" Editable="true" Groupable="false"  />
                                    <GridColumn Field="SupplierContactNumber" Title="Supplier Contact Number" Visible="true" Editable="true" Groupable="false" Width="0" />
                                    <GridCommandColumn Context="gridContext2">  
                                        @if (AllowEdit){
                                            <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                            <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                            <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                            <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                                        }
                                    </GridCommandColumn>
                                </GridColumns>
                                <GridToolBarTemplate>
                                    @if(AllowEdit)
                                    {
                                        <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Contact</GridCommandButton>
                                    } 
                                    <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                </GridToolBarTemplate>
                                <GridSettings>
                                    <GridPopupEditSettings Width="600px"
                                                           Height="600px"
                                                           Title="Contact">
                                    </GridPopupEditSettings>
                                </GridSettings>
                            </TelerikGrid>
                        </div>
                    </div>
                </TabStripTab>
                <TabStripTab Title="Trades and Subdivisions">
                    <div class="row">
                        <div class="col-lg-6">
                            <TelerikGrid Data=@TradesThisSupplier
                                         ScrollMode="GridScrollMode.Virtual"
                                         FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                         Height="600px" RowHeight="60" PageSize="20"
                                         Sortable="true"
                                         Resizable="true"                                         
                                         SelectionMode="GridSelectionMode.Single"
                                         EditMode="@GridEditMode.Inline"
                                         OnDelete="@DeleteTradeSupplierHandler"
                                         OnUpdate="@UpdateTradeSupplierHandler"
                                         OnEdit="@EditTradeSupplierHandler"
                                         OnCancel="@CancelTradeSupplierHandler"
                                         ConfirmDelete="true"
                                         @ref="@SupplierGridRef">
                                <GridColumns>
                                    <GridColumn Field="SubNumber" Visible="false" Editable="false" Groupable="false" />
                                    <GridColumn Field="TradeId" Visible="false" Editable="false" Groupable="false" />
                                    <GridColumn Field="SubdivisionId" Visible="false" Editable="false" Groupable="false" />
                                    <GridColumn Field="TradeName" Title="Trade Name" Visible="true" Editable="false" Groupable="false" />
                                    <GridColumn Field="SubdivisionName" Title="Subdivision Name" Visible="true" Editable="false" Groupable="false" />
                                    <GridColumn Field="IsDefault" Title="Default" Editable="true" EditorType="GridEditorType.CheckBox" Groupable="false" />
                                    <GridCommandColumn Context="gridContext">
                                        @if (AllowEdit)
                                        {
                                            <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                            <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                            <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                            <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                                        }
                                    </GridCommandColumn>
                                </GridColumns>
                                <GridToolBarTemplate>
                                    @if(AllowEdit)
                                    {
                                        <GridCommandButton Command="MyToolbarCommand" Icon="@FontIcon.Plus" OnClick="@ShowAssignSupplierModal" Class="k-button-add">Add Supplier ToTrade/Subdivision</GridCommandButton>
                                    }
                                    <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                </GridToolBarTemplate>
                                <GridSettings>
                                    <GridPopupEditSettings Width="600px"
                                                           Height="600px"
                                                           Title="Trade">
                                    </GridPopupEditSettings>
                                </GridSettings>
                            </TelerikGrid>
                        </div>
                    </div>

                </TabStripTab>
            </TelerikTabStrip>
            @*<br/>
            <br/>
            <div class="mb-3 row">
                <div class="col-sm-12">
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </div>*@
        </EditForm>
        <br />
    </ChildContent>
    <ErrorContent>
        <div class="alert alert-danger"> 
            Oops! There's an issue with this Supplier
        </div>
    </ErrorContent>
</ErrorBoundary>

<ERP.Web.Components.AddSupplierToSubdivisionTrade @ref="AddSupplierModal"  Supplier=@supplierDetails HandleAddSubmit="HandleValidAddSupplierSubmit"></ERP.Web.Components.AddSupplierToSubdivisionTrade>
<ERP.Web.Components.AddSupplierType @ref="AddSupplierTypeModal" SubNumber="@SubNumber" HandleAddSubmit="HandleValidAddSupplierType"></ERP.Web.Components.AddSupplierType>

@code {

    [Parameter]
    public int SubNumber { get; set; }
    public TradeSupplierModel supplierDetails { get; set; } = new TradeSupplierModel();
    public List<SupplierTypeModel> SupplierTypesData { get; set; } = new List<SupplierTypeModel>();
    public List<SupplierInsuranceDto> SupplierInsuranceData { get; set; } = new List<SupplierInsuranceDto>();
    public List<TradeSupplierModel> TradesThisSupplier { get; set; } = new List<TradeSupplierModel>();
    public List<SupplierContactModel>? SupplierContacts {get; set;}
    public int ActiveTabIndex { get; set; }
    private List<MasterPlanDto>? allPlans { get; set; }
    public int? SelectedPlanNum { get; set; }
    public int? SelectedPhasePlanNum { get; set; }
    public string? SelectedJobNum { get; set; }
    public string pleaseWaitStyle { get; set; } = "display:none";
    protected ERP.Web.Components.AddSupplierToSubdivisionTrade? AddSupplierModal { get; set; }
    protected ERP.Web.Components.AddSupplierType? AddSupplierTypeModal {  get; set; }
    private TelerikGrid<TradeSupplierModel>? SupplierGridRef { get; set; }
    private TelerikGrid<SupplierContactModel>? ContactGridRef { get; set; }
    public bool EditEnabled { get; set; } = true;
    private bool AllowEdit { get; set; } = true;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }
    protected override async Task OnInitializedAsync()
    {
        //TODO: all this data should be available with one call
        pleaseWaitStyle = "display:none";
        var supplierDetailsTask = TradeService.GetSupplierAsync(SubNumber);
        var tradesThisSupplierTask = TradeService.GetTradeAndSubdivisionsForSupplierAsync(SubNumber);
        var supplierContactsTask = TradeService.GetSupplierContactsAsync(SubNumber);
        var supplierTypesTask = TradeService.GetSupplierTypesAsync(SubNumber);
        var supplierInsuranceTask = SupplierService.GetSupplierInsuranceAsync(SubNumber);
        await Task.WhenAll(new Task[]{supplierInsuranceTask, supplierContactsTask, supplierDetailsTask, tradesThisSupplierTask, supplierTypesTask});
        SupplierInsuranceData = supplierInsuranceTask.Result.Value;
        supplierDetails = supplierDetailsTask.Result.Value;
        EditEnabled = supplierDetails.ShortName?.ToLower().StartsWith("onetimebid") == true;
        TradesThisSupplier = tradesThisSupplierTask.Result.Value;
        SupplierContacts = supplierContactsTask.Result.Value;
        SupplierTypesData = supplierTypesTask.Result.Value;
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }
    void TabChangedHandler(int newIndex)
    {        
        ActiveTabIndex = newIndex;
    }
    private async void HandleValidSubmit()
    {
        var updateResponse = await TradeService.UpdateSupplierAsync(supplierDetails);
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
    }

    private async void HandleValidAddSupplierSubmit(ResponseModel<TradeSupplierModel> responseItem)
    {
        TradesThisSupplier = (await TradeService.GetTradeAndSubdivisionsForSupplierAsync(SubNumber)).Value;
        AddSupplierModal.Hide();
        ShowSuccessOrErrorNotification(responseItem.Message, responseItem.IsSuccess);
        StateHasChanged();
    }

    private async void HandleValidAddSupplierType(ResponseModel<SupplierTradeTypeModel> addResponse)
    {
        SupplierTypesData = (await TradeService.GetSupplierTypesAsync(SubNumber)).Value;
        AddSupplierTypeModal.Hide();
        ShowSuccessOrErrorNotification(addResponse.Message, addResponse.IsSuccess);
        StateHasChanged();
    }

    private void ShowAssignSupplierModal()
    {
        AddSupplierModal.Show();
        StateHasChanged();
    }

    private void ShowAddSupplierTypeModal()
    {
        AddSupplierTypeModal.Show();
        StateHasChanged();
    }

    async Task DeleteTradeSupplierHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel supplier = (TradeSupplierModel)args.Item;
        supplier.SubNumber = SubNumber;
        var deleteSupplier = new AssignTradeSupplierModel()
            {
                SubdivisionId = supplier.SubdivisionId,
                SubNumber = supplier.SubNumber,
                TradeId = (int)supplier.TradeId
            };
        var deleteResponse = await TradeService.DeleteSupplierFromTradeAsync(deleteSupplier);
        TradesThisSupplier = (await TradeService.GetTradeAndSubdivisionsForSupplierAsync(SubNumber)).Value;
        ShowSuccessOrErrorNotification(deleteResponse.Message, deleteResponse.IsSuccess);
    }
    async Task UpdateTradeSupplierHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel supplier = (TradeSupplierModel)args.Item;
        supplier.SubNumber = SubNumber;
        supplier.SubName = supplierDetails.SubName;
        var updateResponse = await TradeService.UpdateTradeSupplierAsync(supplier);
        TradesThisSupplier = (await TradeService.GetTradeAndSubdivisionsForSupplierAsync(SubNumber)).Value;
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
    }
    void EditTradeSupplierHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel supplier = (TradeSupplierModel)args.Item;
        supplier.SubNumber = SubNumber;
    }
    void CancelTradeSupplierHandler(GridCommandEventArgs args)
    {
        TradeSupplierModel supplier = (TradeSupplierModel)args.Item;
    }
    void EditContactHandler(GridCommandEventArgs args)
    {
        SupplierContactModel item = (SupplierContactModel)args.Item;
    }

    async Task UpdateContactHandler(GridCommandEventArgs args)
    {
        SupplierContactModel item = (SupplierContactModel)args.Item;
        var updateResponse = await TradeService.UpdateContactAsync(item);
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
        SupplierContacts = (await TradeService.GetSupplierContactsAsync(SubNumber)).Value;
    }

    async Task DeleteContactHandler(GridCommandEventArgs args)
    {
        SupplierContactModel item = (SupplierContactModel)args.Item;
        var deleteResponse = await TradeService.DeleteContactAsync(item);
        ShowSuccessOrErrorNotification(deleteResponse.Message, deleteResponse.IsSuccess);
        SupplierContacts = (await TradeService.GetSupplierContactsAsync(SubNumber)).Value;
    }

    async Task DeleteSupplierTradeTypeHandler(GridCommandEventArgs args)
    {
        SupplierTypeModel supplierType = (SupplierTypeModel)args.Item;
        SupplierTradeTypeModel supplierTradeTypeModel = new SupplierTradeTypeModel()
            {
                SupplierNumber = SubNumber,
                SupplierTypeId = supplierType.SupplierTypeId
            };
        var deleteResponse = await TradeService.DeleteSupplierTradeTypeAsync(supplierTradeTypeModel);
        SupplierTypesData = (await TradeService.GetSupplierTypesAsync(SubNumber)).Value;
        ShowSuccessOrErrorNotification(deleteResponse.Message, deleteResponse.IsSuccess);
    }

    async Task CreateContactHandler(GridCommandEventArgs args)
    {
        SupplierContactModel item = (SupplierContactModel)args.Item;
        item.SubNumber = SubNumber;
        var addContactResponse = await TradeService.AddContactAsync(item);
        ShowSuccessOrErrorNotification(addContactResponse.Message, addContactResponse.IsSuccess);
        SupplierContacts = (await TradeService.GetSupplierContactsAsync(SubNumber)).Value;
    }

    async Task CancelContactHandler(GridCommandEventArgs args)
    {
        SupplierContactModel item = (SupplierContactModel)args.Item;
    }

    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
