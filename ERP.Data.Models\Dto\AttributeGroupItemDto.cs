﻿using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class AttributeGroupItemDto : IMapFrom<AttributeGroupItem>
{
    public int AttrGroupItemId { get; set; }

    public int AttributeItemId { get; set; }

    public int AttributeGroupId { get; set; }

    public decimal? PriceChange { get; set; }

    public int Seq { get; set; }

    public bool DefaultSelection { get; set; }

    public bool? IsActive { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? Updatedby { get; set; }

    public  MasterAttributeGroupDto? AttributeGroup { get; set; } 

    public  MasterAttributeItemDto? AttributeItem { get; set; } 

    //public virtual ICollection<OptionAttributeItem> OptionAttributeItems { get; set; } = new List<OptionAttributeItem>();
}
