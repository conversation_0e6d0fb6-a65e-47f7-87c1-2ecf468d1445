﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class EstimateSourceDto : IMapFrom<EstimateSource>
{
    public int EstsourceId { get; set; }

    public string EstsourceDesc { get; set; } = null!;

    public DateTime CreatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public bool IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    //public virtual ICollection<Estheader> Estheaders { get; set; } = new List<Estheader>();
}
