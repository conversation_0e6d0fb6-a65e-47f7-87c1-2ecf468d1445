﻿@page "/constructiondashboard"
@inject SalesConfigService SalesConfigService
@inject SelectedOptionsService SelectedOptionsService
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject AuthenticationStateProvider AuthenticationStateProvider
@attribute [Authorize(Roles = "Admin, ConstructionManager, ConstructionDirector")]
@implements IDisposable
<style>
    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
        z-index: 1002;
    }
    .k-tilelayout-item {
        z-index: 5;
    }
    .k-notification-group{
        z-index: 12000 !important;
    }
    .tile-with-overflow .k-tilelayout-item-body {
        overflow: auto;
    }

    .k-card-header {
        padding-block: 0;
        padding-inline: 0;
    }
</style>
@* <TelerikMediaQuery Media="@SmallScreenMediaQuery" OnChange="@((doesMatch) => IsSmallScreen = doesMatch)"></TelerikMediaQuery> *@
<TelerikMediaQuery Media="@LargeScreenMediaQuery" OnChange="@OnMediaQueryChange"></TelerikMediaQuery>
<PageTitle>Construction Dashboard</PageTitle>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="true" ShowJob="true" ShowSubdivision="true"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
<TelerikTooltip TargetSelector=".tooltip-target" />

<div class="container-fluid">
    <div class="d-none d-sm-block ">
        @if(loading)
        {
            <p><em>Loading...</em></p>
            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />
        }
        else
        {
            <TelerikTileLayout Columns="@numberColumns"
                               ColumnWidth="300px"
                               RowHeight="250px"
                               Reorderable="true"
                               Resizable="true"
                               OnResize="@ItemResize">
                <TileLayoutItems>
                    <TileLayoutItem HeaderText="Selected Options" ColSpan="4" RowSpan="2" Class="tile-with-overflow">
                        <HeaderTemplate>
                            <div class="card" style="background-color:#2e5771">
                                <div class="card-body" style="padding:0.5rem">
                                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">My Activities Today</h7>
                                </div>
                            </div>
                        </HeaderTemplate>
                        <Content>
                            <ConstructionDailyTasks></ConstructionDailyTasks>
                        </Content>
                    </TileLayoutItem>
                    <TileLayoutItem HeaderText="Customer" ColSpan="4" RowSpan="2" Class="tile-with-overflow">
                        <HeaderTemplate>
                            <div class="card" style="background-color:#2e5771">
                                <div class="card-body" style="padding:0.5rem">
                                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">My VPO</h7>
                                </div>
                            </div>
                        </HeaderTemplate>
                        <Content>
                            <MyVPO></MyVPO>
                        </Content>
                    </TileLayoutItem>
                    <TileLayoutItem HeaderText="Lot Details" ColSpan="4" RowSpan="2" Class="tile-with-overflow">
                        <HeaderTemplate>
                            <div class="card" style="background-color:#2e5771">
                                <div class="card-body" style="padding:0.5rem">
                                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Lot Details</h7>
                                </div>
                            </div>
                        </HeaderTemplate>
                        <Content>
                            <JobDetails @ref="JobDetailsRef" JobNumber=@JobSelected></JobDetails>
                        </Content>
                    </TileLayoutItem>
                    <TileLayoutItem HeaderText="Release" ColSpan="4" RowSpan="2" Class="tile-with-overflow">
                        <HeaderTemplate>
                            <div class="card" style="background-color:#2e5771">
                                <div class="card-body" style="padding:0.5rem">
                                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Open Schedules/My Schedules</h7>
                                </div>
                            </div>
                        </HeaderTemplate>
                        <Content>
                            <OpenSchedulesComponent></OpenSchedulesComponent>
                        </Content>
                    </TileLayoutItem>
                </TileLayoutItems>
            </TelerikTileLayout>
        }
       
    </div>
    <!--MOBILE VERSION-->
    @* <div class="d-block d-sm-none">
        <TelerikTileLayout Columns="1"
                           ColumnWidth="300px"
                           RowHeight="250px"
                           Reorderable="true"
                           Resizable="true"
                           OnResize="@ItemResize">
            <TileLayoutItems>
                <TileLayoutItem HeaderText="Selected Options" ColSpan="4" RowSpan="2" Class="tile-with-overflow">
                    <HeaderTemplate>
                        <div class="card" style="background-color:#2e5771">
                            <div class="card-body" style="padding:0.5rem">
                                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">My Activities Today</h7>
                            </div>
                        </div>
                    </HeaderTemplate>
                    <Content>
                        <ConstructionDailyTasks></ConstructionDailyTasks>
                    </Content>
                </TileLayoutItem>
                <TileLayoutItem HeaderText="Customer" ColSpan="4" RowSpan="2" Class="tile-with-overflow">
                    <HeaderTemplate>
                        <div class="card" style="background-color:#2e5771">
                            <div class="card-body" style="padding:0.5rem">
                                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">My VPO</h7>
                            </div>
                        </div>
                    </HeaderTemplate>
                    <Content>
                              <MyVPO></MyVPO>  
                    </Content>
                </TileLayoutItem>
                <TileLayoutItem HeaderText="Lot Details" ColSpan="4" RowSpan="2" Class="tile-with-overflow">
                    <HeaderTemplate>
                        <div class="card" style="background-color:#2e5771">
                            <div class="card-body" style="padding:0.5rem">
                                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Lot Details</h7>
                            </div>
                        </div>
                    </HeaderTemplate>
                    <Content>
                              <JobDetails @ref="JobDetailsRef" JobNumber=@JobSelected></JobDetails> 
                    </Content>
                </TileLayoutItem>
                <TileLayoutItem HeaderText="Release" ColSpan="4" RowSpan="2" Class="tile-with-overflow">
                    <HeaderTemplate>
                        <div class="card" style="background-color:#2e5771">
                            <div class="card-body" style="padding:0.5rem">
                                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Open Schedules/My Schedules</h7>
                            </div>
                        </div>
                    </HeaderTemplate>
                    <Content>
                              <OpenSchedulesComponent></OpenSchedulesComponent> 
                    </Content>
                </TileLayoutItem>
            </TileLayoutItems>
        </TelerikTileLayout>
    </div> *@

</div>

@code {

    public bool WindowIsVisible { get; set; } = true;
    private string? SelectedJob { get; set; }
    private string? JobSelected { get; set; }
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    private bool loading { get; set; } = true;

    public JobDetails? JobDetailsRef { get; set; }

    private int numberColumns { get; set; } = 8; //8 for large screen, 1 for small

    private bool IsSmallScreen { get; set; }
    private bool IsLargeScreen { get; set; }
    private string SmallScreenMediaQuery { get; set; } = "(max-width: 767px)";
    private string LargeScreenMediaQuery { get; set; } = "(min-width: 1199px)";

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        SubdivisionJobPickService.OnChanged += JobChangedHandler;
        if (SubdivisionJobPickService.JobNumber != null)
        {
            loading = true;
            JobSelected = SubdivisionJobPickService.JobNumber;
            loading = false;
            StateHasChanged();
        }
        await Task.Delay(1); // to display loader until the UI components have been rendered
        loading = false;
        
    }
    private void OnMediaQueryChange(bool doesMatch)
    {
        IsLargeScreen = doesMatch;
        numberColumns = IsLargeScreen ? 8 : 1;

    }
    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= JobChangedHandler;
    }
    async Task JobChangedHandler()
    {
        var selected = SubdivisionJobPickService.JobNumber;
        if (JobSelected != selected)//check if actual change, so not calling this multiple times
        {
            loading = true;
            JobSelected = selected;
            
            loading = false;
            StateHasChanged();
        }
    }
    void ItemResize()
    {

    }
    private void ShowSuccessOrErrorNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
