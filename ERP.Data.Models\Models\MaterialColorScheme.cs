﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class MaterialColorScheme
{
    public int MaterialColorSchemeId { get; set; }

    public string ColorSchemeNum { get; set; } = null!;

    public int PlanOptionId { get; set; }

    public bool CanBeReplaced { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public int MaterialColorPredefinedId { get; set; }

    public virtual ICollection<Job> Jobs { get; set; } = new List<Job>();

    public virtual MaterialColorPredefined MaterialColorPredefined { get; set; } = null!;

    public virtual AvailablePlanOption PlanOption { get; set; } = null!;
}
