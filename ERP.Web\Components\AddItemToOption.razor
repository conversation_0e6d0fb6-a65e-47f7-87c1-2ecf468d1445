﻿
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService


<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Item to Option 
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@ItemToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <p>Select Item to Add to Option: @Option.OptionCode</p>
            <p><label>Trade</label>
 
                @if(AllTrades != null && AllTrades.Count != 0)
                {
                    <TelerikDropDownList @bind-Value="@SelectedTradeId"
                                         Data="@AllTrades"
                                         TextField="TradeName"
                                         ValueField="TradeId"
                                         DefaultText="Select Trade"
                                         Filterable="true"
                                         OnChange="@CascadeActivities"
                                         ScrollMode="@DropDownScrollMode.Virtual"
                                         ItemHeight="30"
                                         PageSize="20"
                                         Width="100%">
                    </TelerikDropDownList>
                }
            </p>
            <p><label>Activity</label>
            <TelerikDropDownList @bind-Value="@SelectedActivityId"
                                 Data="@AllActivitiesInTrade"
                                 TextField="Activity"
                                 ValueField="PactivityId"
                                 DefaultText="Select Activity"
                                 OnChange="@CascadeItems"
                                 Filterable="true"
                                 Width="100%">
            </TelerikDropDownList>
            </p>
            <p><label>Item</label>
            <TelerikDropDownList @bind-Value="@SelectedItemId"
                                     TextField="ItemDesc"
                                     ValueField="MasterItemId"
                                     Filterable="true"
                                    Data="@AllItemsInActivity"
                                     DefaultText="Select Item"
                                 Width="100%">
            </TelerikDropDownList>
            </p>
            <p>
                <label>Quantity</label>
                <TelerikNumericTextBox @bind-Value="@ItemToAdd.Factor"                                     
                                     Width="100%">
                </TelerikNumericTextBox>
            </p>
            <p><label>Select Multiple Options to Add Item To</label>
            <TelerikMultiSelect @bind-Value="@SelectedOptionGroups"
                                TextField="OptionGroupName"
                                ValueField="OptionGroupId"
                                OnChange="@CascadeOptions"
                                Placeholder="Select Option Groups"
                                Filterable="true"
                                FilterOperator="StringFilterOperator.Contains"
                                Data="@AllOptionGroups"
                                Width="100%">
            </TelerikMultiSelect>
            </p>
            @if (AllOptionsInSelectedOptionGroups != null && AllOptionsInSelectedOptionGroups.Count != 0)
            {
                <p><TelerikMultiSelect @bind-Value="@SelectedOptionsInOptionGroups"
                                    Filterable="true"
                                    FilterOperator="StringFilterOperator.Contains"
                                    TextField="OptionDesc"
                                    ValueField="AsmHeaderId"
                                    Placeholder="Select Options"
                                    Data="@AllOptionsInSelectedOptionGroups"
                                    Width="100%">
                </TelerikMultiSelect>
                </p>
            }
            <button type="submit" class="btn btn-primary">Add</button>                   
            <button type="button" @onclick="CancelAddItem" class="btn btn-secondary">Cancel</button> 
            <div style=@submittingStyle>Adding items. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public ModelManagerItemModel ItemToAdd { get; set; } = new ModelManagerItemModel();
    [Parameter]
    public MasterOptionHeaderModel Option { get; set; }
    [Parameter]
    public EventCallback<ResponseModel> HandleAddSubmit { get; set; }

    public List<TradeDto> AllTrades { get; set; }
    public List<OptionGroupDto>? AllOptionGroups { get; set; }
    public List<MasterOptionHeaderModel> AllMasterOptions { get; set; }
    public List<MasterOptionHeaderModel> AllOptionsInSelectedOptionGroups { get; set; }
    public int SelectedTradeId;
    public List<PactivityModel>? AllActivitiesInTrade { get; set; }
    public int SelectedActivityId;
    public List<ModelManagerItemModel>? AllItemsInActivity { get; set; }
    public int SelectedItemId;
    public List<AsmHeaderModel>? AllASMHeaders { get; set; }
    public List<int>? SelectedAsmHeaders { get; set; }
    public List<int>? SelectedOptionGroups { get; set; }
    public List<int> SelectedOptionsInOptionGroups { get; set; } = new List<int>();
    private string submittingStyle = "display:none";

    public async Task Show()
    {
        IsModalVisible = true;
        ItemToAdd.Factor = 1; //default quanity to 1
        await LoadTradesAsync();
        await LoadOptionGroupsAsync();
        await LoadAllMasterOptionsAsync();
       // AllASMHeaders = (await ItemService.GetAssembliesInPlanAsync(1)).Value;//this should be all the asm headers in master plan 1, but do they need other plans
        StateHasChanged();
    }

    private async Task LoadOptionGroupsAsync()
    {
        var getGroups = await OptionService.GetOptionGroupsAsync();
        AllOptionGroups = getGroups.Value;
    }

    private async Task LoadAllMasterOptionsAsync()
    {
        AllMasterOptions = (await OptionService.GetAllMasterOptionsAsync()).Value;
    }

    private async Task LoadTradesAsync()
    {
        AllTrades = (await ItemService.GetTradesAsync()).Value;
    }

    private async Task CascadeOptions(object selectedOptionGroups)
    {
        if (SelectedOptionGroups != null)
        {
            AllOptionsInSelectedOptionGroups = AllMasterOptions.Where(x => SelectedOptionGroups.Contains(x.OptionGroupId ?? -1)).ToList();
        }
    }

    private async Task CascadeActivities(object newVal)
    {
        AllActivitiesInTrade = (await ItemService.GetActivityByTradeAsync(SelectedTradeId)).Value;
    }

    private async Task CascadeItems(object newVal)
    {
        AllItemsInActivity = (await ItemService.GetItemsInActivityAsync(SelectedActivityId)).Value;
    }

    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        SelectedOptionsInOptionGroups.Add(Option.AsmHeaderId);  // adding the current option where the item needs to be added by default
        var items = new List<ModelManagerItemModel>();

        foreach (var optionItemAsmHeaderId in SelectedOptionsInOptionGroups)
        {
            items.Add(new ModelManagerItemModel()
            {
                    MasterItemId = SelectedItemId,
                    Factor = ItemToAdd.Factor,
                    AsmHeaderId = optionItemAsmHeaderId
            });
        }

        var responseItem = await ItemService.AddMasterOptionItems(items);
        var responseModel = new ResponseModel() { IsSuccess = true, Message = "Yay" }; // TODO: Debug not appearing
        submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(responseModel);
    }

    async void CancelAddItem()
    {
        IsModalVisible = false;
    }

    public async Task Hide()
    {
        IsModalVisible = false;        
    }
}
