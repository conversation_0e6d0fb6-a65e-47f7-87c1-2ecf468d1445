﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;


namespace ERP.Data.Models.Dto;

public class HoaJobDto : IMapFrom<HoaJob>
{
    public int HoaAssessmentId { get; set; }

    public string JobNumber { get; set; } = null!;

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    //public byte[] RecordTimeStamp { get; set; } = null!;

    //public virtual HoaAssessment HoaAssessment { get; set; } = null!;

    public void Mapping(Profile profile)
    {
        profile.CreateMap<HoaJobDto, HoaJob>().ReverseMap();
    }
    public JobDto? JobNumberNavigation { get; set; } 
    public SubdivisionDto? Subdivision { get; set; }
}
