﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class JccategoryDto : IMapFrom<Jccategory>
{
    public int JccategoryId { get; set; }

    public string? Category { get; set; }

    public string? Catdescription { get; set; }
    public string? DropdownDescription { get { return $"{Category} - {Catdescription}"; } }

    public string? CategoryType { get; set; }

    public int JccosttypeId { get; set; }

    public string? SendBudgetToJc { get; set; }

    public string? SentCommitmentToJc { get; set; }

    public string? PrintPoIfAllThisCat { get; set; }

    public string? PrintPricesOnPo { get; set; }

    public string? DefaultCoCategory { get; set; }

    public string? DefaultCostVarianceCategory { get; set; }

    public string? DefaultTaxRoundingCategory { get; set; }

    public int? IgBusinessRuleId { get; set; }

    public string? VpoApprovalUser { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }
    public bool? DesignBuild { get; set; }

    public virtual JccosttypeDto? Jccosttype { get; set; }

    // public virtual ICollection<Pactivity> Pactivities { get; set; } = new List<Pactivity>();
    public void Mapping(Profile profile)
    {
        profile.CreateMap<JccategoryDto, Jccategory>().ReverseMap();
    }
}
