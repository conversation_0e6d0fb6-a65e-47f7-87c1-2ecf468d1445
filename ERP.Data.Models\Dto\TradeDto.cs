﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class TradeDto : IMapFrom<Trade>
{
    public int TradeId { get; set; }

    public string? TradeName { get; set; }

    public string? TradeDesc { get; set; }
   // public string? ActivityCode { get; set; }//TODO: use for the 3 digit code that goes with the activity. Figure out how to use this

    public string? Workdays { get; set; }

    public string? TermsFilename { get; set; }

    public string? CubitId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }


    //public virtual ICollection<Pactivity> Pactivities { get; set; } = new List<Pactivity>();

    //public virtual ICollection<TradeSupplier> TradeSuppliers { get; set; } = new List<TradeSupplier>();

    public void Mapping(Profile profile)
    {
        profile.CreateMap<TradeDto, Trade>().ReverseMap();
    }
}
