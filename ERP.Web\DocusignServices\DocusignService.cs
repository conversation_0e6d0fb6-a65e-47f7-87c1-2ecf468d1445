﻿using Azure.Core;
using DocuSign.eSign.Api;
using DocuSign.eSign.Client;
using DocuSign.eSign.Model;
using Microsoft.Identity.Client;
using System.Diagnostics;

namespace ERP.Web.DocusignServices
{
    public interface IDocusignService : IDisposable
    {
        DocuSignClient ApiClient { get; set; }
        string SendEnvelope(string vmName, string vmEmail, string vendorName, string vendorEmail, List<byte[]> fileData);
    }

    public class DocusignService : IDocusignService
    {
        private IDocusignAuthenticator _docusignAuthenticator { get; }
        private ILogger<DocusignService> _logger { get; }
        private IConfiguration _configuration { get; }

        public DocuSignClient ApiClient { get; set; }
        private string AccountId { get; }

        public DocusignService(ILogger<DocusignService> logger, IConfiguration configuration, IDocusignAuthenticator authenticator)
        {
            _logger = logger;
            _configuration = configuration;
            _docusignAuthenticator = authenticator;
            AccountId = _configuration["DocuSign:AccountId"];
        }

        public void Dispose() { }

        public string SendEnvelope(string signerName, string signerEmail, string ccName, string ccEmail, List<byte[]> fileData)
        {
            try
            {
                ApiClient = new DocuSignClient(_configuration["Docusign:APIEndpoint"]);
                _docusignAuthenticator.Authenticate(ApiClient);
                EnvelopeDefinition env = MakeEnvelope(signerName, signerEmail,  ccName, ccEmail, fileData);

                EnvelopesApi envelopesApi = new EnvelopesApi(ApiClient);
                EnvelopeSummary results = envelopesApi.CreateEnvelope(AccountId, env);
                return results.EnvelopeId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                throw;
            }
        }

        private static EnvelopeDefinition MakeEnvelope(string signerName, string signerEmail,  string ccName, string ccEmail, List<byte[]?> docFileBytes)
        {
            // Make sure RoleName matches with what's being uploaded in the Make Template, or DocuSign uploaded template in the DocuSign Portal

            EnvelopeDefinition env = new EnvelopeDefinition();
            env.EmailSubject = "TEST: Please sign this document";

            env.CustomFields = new CustomFields()
            {
                TextCustomFields = new List<TextCustomField>() { new TextCustomField() { Name = "ErnieTestField", Value = "ErnieTestValue" } }    
            };
            env.CustomFields = new CustomFields()
            {
                TextCustomFields = new List<TextCustomField>() 
                {
                    new TextCustomField() { Name = "ErnieTest", Value = "Test Value" },
                    //new TextCustomField() { Name = "VMLType", Value = "Sales Agreement" },
                    //new TextCustomField() { Name = "CMSContractId", Value = "212" },
                    //new TextCustomField() { Name = "JobNumber", Value = "PPTH0012" }
                }
            };

            // Create document objects, one per document
            var docList = new List<Document>();
            foreach(var doc in docFileBytes)
            {
                var DocumentBase64 = Convert.ToBase64String(doc);
                Document addDoc = new Document
                {
                    DocumentBase64 = DocumentBase64,
                    Name = "Addendum", // can be different from actual file name
                    FileExtension = "pdf",
                    DocumentId = (docFileBytes.IndexOf(doc) +1).ToString(), //TODO: what should go here
                };
                docList.Add(addDoc);
            }

            // The order in the docs array determines the order in the envelope
            env.Documents = docList;

            // create a signer recipient to sign the document, identified by name and email
            // We're setting the parameters via the object creation
            Signer signer1 = new Signer
            {
                Email = signerEmail,
                Name = signerName,
                RecipientId = "1",
                RoutingOrder = "1",
            };

            // routingOrder (lower means earlier) determines the order of deliveries
            // to the recipients. Parallel routing order is supported by using the
            // same integer as the order for two or more recipients.

            // create a cc recipient to receive a copy of the documents, identified by name and email
            // We're setting the parameters via setters
            CarbonCopy cc1 = new CarbonCopy
            {
                Email = ccEmail,
                Name = ccName,
                RecipientId = "2",
                RoutingOrder = "2",
            };

            // Create signHere fields (also known as tabs) on the documents,
            // We're using anchor (autoPlace) positioning
            //
            // The DocuSign platform searches throughout your envelope's
            // documents for matching anchor strings. So the
            // signHere2 tab will be used in both document 2 and 3 since they
            // use the same anchor string for their "signer 1" tabs.
            SignHere signHere1 = new SignHere
            {
                AnchorString = "**signature_1**",
                AnchorUnits = "pixels",
                AnchorYOffset = "20",
                AnchorXOffset = "100",
            };

            SignHere signHere2 = new SignHere//this one is not in the addenda doc
            {
                AnchorString = "/sn1/",
                AnchorUnits = "pixels",
                AnchorYOffset = "10",
                AnchorXOffset = "20",
            };
            DateSigned dateTab = new DateSigned
            {
                AnchorString = "**datesigned_1**",
                AnchorUnits = "pixels",
                AnchorYOffset = "20",
                AnchorXOffset = "100",
            };

            // Tabs are set per recipient / signer
            Tabs signer1Tabs = new Tabs
            {
                SignHereTabs = new List<SignHere> { signHere1, signHere2 },
                DateSignedTabs = new List<DateSigned> { dateTab },
            };
            signer1.Tabs = signer1Tabs;

            // Add the recipients to the envelope object
            Recipients recipients = new Recipients
            {
                Signers = new List<Signer> { signer1 },
                CarbonCopies = new List<CarbonCopy> { cc1 },
            };
            env.Recipients = recipients;

            // Request that the envelope be sent by setting |status| to "sent".
            // To request that the envelope be created as a draft, set to "created"
            env.Status = "sent";

            return env;
        }
    }
}
