﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{

    public enum BudgetWarnings
    {
        [Description("Negative Quantity")]
        NegativeQty = 0,
        [Description("No Cost, Using Estimate Cost")]
        NoCostUseEstimate = 1,
        [Description("No Cost, Using Zero")]
        NoCostUseZero = 6,
        [Description("No Cost, Using Existing Cost")]
        NoCostUseExisting = 4,
        [Description("Using Lump Sum")]
        UsingLumpSum = 5,

    }
    public enum BudgetErrors
    {
        [Description("Invalid Job Cost Category")]
        InvalidJCCategory = 7,
        [Description("Invalid Job Cost Code")]
        InvalidJobCostCode = 6,
        [Description("Zero Cost Not Allowed")]
        ZeroCostNotAllowed = 4,
    }
}
