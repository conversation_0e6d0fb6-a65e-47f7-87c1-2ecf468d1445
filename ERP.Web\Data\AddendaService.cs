﻿using Azure.Core;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Models;
using ERP.Web.Pages;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Abstractions;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using NLog;
using System.Xml;


namespace ERP.Web.Data
{
    public class AddendaService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public AddendaService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<ResponseModel<List<AddendaModel>>> GetAddendaAsync(int subivisionId, int supplierNum, DateTime? effectiveDate)
        {
            var responseModel = new ResponseModel<List<AddendaModel>>();

            try
            {
                var costDate = effectiveDate ?? DateTime.Now;
                var stringCostDate = costDate.ToString("MM-dd-yyyy");
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/addenda/GetAddenda/{subivisionId}/{supplierNum}/{stringCostDate}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    responseModel = JsonConvert.DeserializeObject<ResponseModel<List<AddendaModel>>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }

        public async Task<ResponseModel<List<AddendaModel>>> GetAddendaSelectPlansAsync(SelectAddendaModel model)
        {
            var responseModel = new ResponseModel<List<AddendaModel>>();

            try
            {
                var response = await _downstreamAPI.PostForUserAsync<SelectAddendaModel, ResponseModel<List<AddendaModel>>>(
                            "DownstreamApi", model,
                             options => {
                                 options.RelativePath = "api/addenda/Addenda/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }
    }
}
