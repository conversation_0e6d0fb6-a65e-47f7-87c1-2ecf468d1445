﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class DataList
    {
        public int? TableID { get; set; }
        public string? PackageCode { get; set; }
        public List<JobPlanningLine>? JobPlanningLineList { get; set; }
    }
    //public class JobPlanningLineList
    //{
    //    public int? TableID { get; set; }
    //    public string? PackageCode { get; set; }
    //    public List<JobPlanningLine>? JobPlanningLine { get; set; }
    //}


    public class JobPlanningLine
    {
        // public string odataetag { get; set; }
        public string? JobNo { get; set; }
        public string? JobTaskNo { get; set; }
        //public int Line_No { get; set; }
        
       // public int? LineType { get; set; }
        //public bool Usage_Link { get; set; }
        public string? PlanningDate { get; set; }
       // public string? PlannedDeliveryDate { get; set; }
       // public string Currency_Date { get; set; }
        //public string Document_No { get; set; }
        public string? Type { get; set; }
        public string? No { get; set; }
        public string? Description { get; set; }
       // public bool? Link_With_ERP { get; set; }
        public string? UnitofMeasureCode { get; set; }

        //public string Description_2 { get; set; }
        //public string Price_Calculation_Method { get; set; }
        //public string Cost_Calculation_Method { get; set; }
        //public string Gen_Bus_Posting_Group { get; set; }
        //public string Gen_Prod_Posting_Group { get; set; }
        //public string Variant_Code { get; set; }
        //public string Location_Code { get; set; }
        //public string Bin_Code { get; set; }
        //public string Work_Type_Code { get; set; }
        //public string Unit_of_Measure_Code { get; set; }
        //public string ReserveName { get; set; }
        public decimal? Quantity { get; set; }
        //public int Qty_to_Assemble { get; set; }
        //public int Reserved_Quantity { get; set; }
        //public int Quantity_Base { get; set; }
        //public int Remaining_Qty { get; set; }
        //public int Direct_Unit_Cost_LCY { get; set; }
        public decimal? UnitCost { get; set; }
        public string? LineType { get; set; }
        // public decimal? UnitPrice { get; set; }
        //public int Unit_Cost_LCY { get; set; }
        public decimal? TotalCost { get; set; }
        //public int Remaining_Total_Cost { get; set; }
        //public int Total_Cost_LCY { get; set; }
        //public int Remaining_Total_Cost_LCY { get; set; }
        //public int Unit_Price { get; set; }
        //public int Unit_Price_LCY { get; set; }
        //public double? Line_Amount { get; set; }
        //public int Remaining_Line_Amount { get; set; }
        //public int Line_Amount_LCY { get; set; }
        //public int Remaining_Line_Amount_LCY { get; set; }
        //public int Line_Discount_Amount { get; set; }
        //public int Line_Discount_Percent { get; set; }
        //public int Total_Price { get; set; }
        //public int Total_Price_LCY { get; set; }
        //public int Qty_Posted { get; set; }
        //public int Qty_to_Transfer_to_Journal { get; set; }
        //public int Posted_Total_Cost { get; set; }
        //public int Posted_Total_Cost_LCY { get; set; }
        //public int Posted_Line_Amount { get; set; }
        //public int Posted_Line_Amount_LCY { get; set; }
        //public int Qty_Transferred_to_Invoice { get; set; }
        //public int Qty_to_Transfer_to_Invoice { get; set; }
        //public int Qty_Invoiced { get; set; }
        //public int Qty_to_Invoice { get; set; }
        //public int Invoiced_Amount_LCY { get; set; }
        //public int Invoiced_Cost_Amount_LCY { get; set; }
        //public string User_ID { get; set; }
        //public string Serial_No { get; set; }
        //public string Lot_No { get; set; }
        //public int Job_Contract_Entry_No { get; set; }
        //public string Ledger_Entry_Type { get; set; }
        //public int Ledger_Entry_No { get; set; }
        //public bool System_Created_Entry { get; set; }
        //public bool Overdue { get; set; }
        //public int Qty_Picked { get; set; }
        //public int Qty_Picked_Base { get; set; }
        //public bool Contract_Line { get; set; }
    }
   


}
