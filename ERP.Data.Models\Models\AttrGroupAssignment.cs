﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class AttrGroupAssignment
{
    public int AttrGroupAssignmentId { get; set; }

    public int? AttributeGroupId { get; set; }

    public int? AttributeItemId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual MasterAttributeGroup? AttributeGroup { get; set; }

    public virtual MasterAttributeItem? AttributeItem { get; set; }

    public virtual ICollection<BuildAttributeItem> BuildAttributeItems { get; set; } = new List<BuildAttributeItem>();

    public virtual ICollection<MasterOptionAttributeItem> MasterOptionAttributeItems { get; set; } = new List<MasterOptionAttributeItem>();

    public virtual ICollection<OptionAttributeGroupItem> OptionAttributeGroupItems { get; set; } = new List<OptionAttributeGroupItem>();

    public virtual ICollection<PlanOptionAttributeItem> PlanOptionAttributeItems { get; set; } = new List<PlanOptionAttributeItem>();

    public virtual ICollection<SalesconfigcooptionsAttribute> SalesconfigcooptionsAttributes { get; set; } = new List<SalesconfigcooptionsAttribute>();

    public virtual ICollection<SalesconfigoptionsAttribute> SalesconfigoptionsAttributes { get; set; } = new List<SalesconfigoptionsAttribute>();
}
