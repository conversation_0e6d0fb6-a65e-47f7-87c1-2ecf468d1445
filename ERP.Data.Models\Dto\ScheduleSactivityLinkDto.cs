﻿using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class ScheduleSactivityLinkDto : IMapFrom<ScheduleSactivityLink>
{
    public int ScheduleAid { get; set; }

    public int EstactivityId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public EstactivityDto? Estactivity { get; set; }

    public ScheduleSactivityDto? ScheduleA { get; set; }
}
