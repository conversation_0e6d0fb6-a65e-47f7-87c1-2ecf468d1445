﻿@page "/checkpayments"
@using Telerik.Documents.SpreadsheetStreaming
@inject PaymentService PaymentService
@inject SubdivisionService SubdivisionService
@inject PoService PoService
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject IJSRuntime JsRuntime
@implements IDisposable
@inject ProtectedSessionStorage ProtectedSessionStore
@inject AuthenticationStateProvider AuthenticationStateProvider
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, Accounting, ReadOnly")]
<style>
    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>

<PageTitle>Check Payments</PageTitle>
<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="true" ShowJob="true" ShowSubdivision="true"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
<TelerikTooltip TargetSelector=".tooltip-target" />
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<div class="container-fluid flex">
    <div class="col-lg-12">
        <div class="card" style="background-color: #2e5771">
            <div class="card-body" style="padding:0.5rem">
                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Payments</h7>
            </div>
        </div>
    </div>

    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item active">Payments</li>
    </ol>

    <div class="row d-flex">
        @if (displayLoadingSpinner)
        {
            <p><em>Loading...</em></p>
            <TelerikLoader></TelerikLoader>
        }
        <div class="container-fluid">
            <div class="row">
                <TelerikGrid Data="@CurrentSelectedViewPaymentsData"
                             ScrollMode="@GridScrollMode.Virtual"
                             PageSize="40"
                             Height="1000px"
                             RowHeight="40"
                             FilterMode="GridFilterMode.FilterMenu"
                            @ref="@PaymentGrid"
                                Sortable="true">
                    <GridToolBarTemplate>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                        <label for="PONumberFilter">Purchase Order #:</label>
                        <TelerikTextBox Id="PONumberFilter" @bind-Value="@PurchaseOrderFilter" OnChange="OnPurchaseOrderFilterChange" Width="100px" />

                        <TelerikToggleButton @bind-Selected="@IsSupplierSelected"
                                             OnClick="@OnJobSupplierToggleButtonClick">
                            View By Job/Supplier: <strong>@SupplierOrJobSelected</strong>
                        </TelerikToggleButton>

                       @*  <TelerikDropDownList Data="@( new List<string>() { "Active", "Canceled" } )" @bind-Value="@ActiveOrCanceled" OnChange="@ToggleCanceledIssuedPOs" Width="110px" />
                        <TelerikDropDownList Data="@( new List<string>() { "Supplier", "Job" } )" @bind-Value="@SupplierOrJobSelected" OnChange="@OnViewOptionChange" Width="110px" /> *@
                        <label for="StartDate">StartDate</label>
                        <TelerikDatePicker @bind-Value="@StartDateValue"
                                          Id="StartDate"
                                          Enabled=!IsAllDatesSelected
                                          Format="d"
                                          OnChange="@OnDateChange"
                                          AutoSwitchKeys="@AutoSwitchKeys">
                        </TelerikDatePicker>                        
                        <label for="EndDate">EndDate</label>
                        <TelerikDatePicker @bind-Value="@EndDateValue"
                                          Format="d"
                                          Enabled=!IsAllDatesSelected
                                          Id="EndDate"
                                          OnChange="@OnDateChange"
                                          AutoSwitchKeys="@AutoSwitchKeys">
                        </TelerikDatePicker>
                        <TelerikCheckBox Id="AllDates" @bind-Value="@IsAllDatesSelected" OnChange="@ToggleAllDatesView" />
                        <label for="AllDates">Show all Dates (past 3 years) for Selected @SupplierOrJobSelected</label>
@*                         <span>Time: @TimeToLoad</span>
                        <span>Total Records: @TotalRecords</span> *@
                        <GridCommandButton Command="ExcelExport" Icon="@FontIcon.FileExcel" Class="k-button-add">Export to Excel</GridCommandButton>
                    </GridToolBarTemplate>
                    <GridExport>
                        <GridExcelExport OnBeforeExport="@OnExcelBeforeExport" FileName="Payments" AllPages="true" />
                    </GridExport>
                    <GridColumns>
                        <GridColumn Field="BankAccount" Title="Bank Account" />
                        <GridColumn Field="CheckNumber" Title="Check Number" />
                        <GridColumn Field="CheckDate" Title="Check Date" DisplayFormat="{0:MM/dd/yy}" />
                        <GridColumn Field="CheckAmount" Title="Check Amount" DisplayFormat="{0:C}"/>
                        <GridColumn Field="SubNumberNavigation.SubName" Title="Supplier Name" FilterMenuType="@FilterMenuType.CheckBoxList" />
                        <GridColumn Field="InvNumber" Title="Invoice Number" FilterMenuType="@FilterMenuType.CheckBoxList" />
                        <GridColumn Field="InvAmountPaid" Title="Inv Amt Paid" DisplayFormat="{0:C}" />
                        <GridColumn Field="Poheader.Ponumber" Title="PO Number" />
                        <GridColumn Field="Poheader.Pojobnumber" Title="PO Job Number" />
                    </GridColumns>
                </TelerikGrid>
            </div>
        </div>
    </div>
</div>

@code {
    private bool displayLoadingSpinner { get; set; } = false;
    private string? JobSelected { get; set; }
    private int? SelectedSupplier { get; set; }
    private string? PurchaseOrderFilter { get; set; }
    private TelerikGrid<CheckPaymentDto>? PaymentGrid { get; set; }
    private List<CheckPaymentDto>? PaymentsForJob { get; set; } = new List<CheckPaymentDto>();
    private List<CheckPaymentDto>? PaymentsForSupplier { get; set; } = new List<CheckPaymentDto>();
    private List<CheckPaymentDto>? CurrentSelectedViewPaymentsData { get; set; }
    private string ActiveOrCanceled { get; set; } = "Active";
    private bool ExcludeClosedJobs { get; set; } = false;
    private List<string> ViewOptions { get; set; } = new List<string> { "Supplier", "Job" };
    private string SupplierOrJobSelected { get; set; } = "Job";
    private string SupplierOrJobSelectedChange { get; set; } = "Job";
    private bool IsAllDatesSelected { get; set; } = true;
    public DateTime StartDateValue { get; set; } = DateTime.Now.AddYears(-1);
    public DateTime EndDateValue { get; set; } = DateTime.Now;
    public DateTime StartDateValueChange { get; set; } = DateTime.Now.AddYears(-1);
    public DateTime EndDateValueChange { get; set; } = DateTime.Now;
    private List<object> AutoSwitchKeys { get; set; } = new List<object>() { ".", "/", " ", "-" };

    private bool AllowEdit { get; set; } = true;

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    bool IsSupplierSelected { get; set; } = false;
    bool IsCancelledSelected { get; set; } = false;


    protected override async Task OnInitializedAsync()
    {
        SubdivisionJobPickService.OnChanged += JobSelectedHandler;
        SubdivisionJobPickService.OnChanged += SupplierSelectedHandler;
        if (SubdivisionJobPickService.JobNumber != null)
        {
            JobSelected = SubdivisionJobPickService.JobNumber;
        }
        if (SubdivisionJobPickService.SupplierNumber != null)
        {
            SelectedSupplier = SubdivisionJobPickService.SupplierNumber;
        }
        await LoadDataBasedOnSelection();
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }

    async Task OnJobSupplierToggleButtonClick()
    {
        SupplierOrJobSelected = IsSupplierSelected ? "Job" : "Supplier";
        //TODO: this should also check if the current supplier data is the same as the one in the box, so it doesn't load it again
        await LoadDataBasedOnSelection();
    }

    protected async Task JobSelectedHandler()
    {
        if (SupplierOrJobSelected == "Job")
        {
            if(JobSelected != SubdivisionJobPickService.JobNumber)
            {
                displayLoadingSpinner = true;
                StateHasChanged();
                JobSelected = SubdivisionJobPickService.JobNumber;
                PurchaseOrderFilter = "";
                PaymentsForJob = IsAllDatesSelected ? (await PaymentService.GetPaymentsAsync(JobSelected)).Value :
                                                (await PaymentService.GetPaymentsAsync(JobSelected)).Value;
                CurrentSelectedViewPaymentsData = PaymentsForJob;
                displayLoadingSpinner = false;
                StateHasChanged();
            }
        }
    }

    protected async Task SupplierSelectedHandler()
    {
        if (SupplierOrJobSelected == "Supplier" )
        {
            if(SelectedSupplier != SubdivisionJobPickService.SupplierNumber)//This is to prevent it calling the database if the supplier hasn't actually changed
            {
                SelectedSupplier = SubdivisionJobPickService.SupplierNumber;
                displayLoadingSpinner = true;
                StateHasChanged();
                PurchaseOrderFilter = "";
                PaymentsForSupplier = IsAllDatesSelected ? (await PaymentService.GetPaymentsAsync((int)SelectedSupplier)).Value :
                                                (await PaymentService.GetPaymentsAsync((int)SelectedSupplier)).Value;
                CurrentSelectedViewPaymentsData = PaymentsForSupplier;
                displayLoadingSpinner = false;
                StateHasChanged();
            }
        }
    }

    protected async Task OnPurchaseOrderFilterChange(object theUserInput)
    {
        var purchaseOrderNumber = (string)theUserInput;
        if (SupplierOrJobSelected == "Job" && JobSelected != null)
        {
            CurrentSelectedViewPaymentsData = IsAllDatesSelected ? (await PaymentService.GetPaymentsAsync(JobSelected)).Value :
                                                (await PaymentService.GetPaymentsAsync(JobSelected)).Value;
        }
        if (SupplierOrJobSelected == "Supplier" && SelectedSupplier != null)
        {
            PaymentsForSupplier = IsAllDatesSelected ? (await PaymentService.GetPaymentsAsync((int)SelectedSupplier)).Value :
                                                (await PaymentService.GetPaymentsAsync((int)SelectedSupplier)).Value;
            if (SelectedSupplier != null && PaymentsForSupplier != null && SelectedSupplier.Equals(PaymentsForSupplier.First().SubNumber))
            {
                CurrentSelectedViewPaymentsData = IsAllDatesSelected ? (await PaymentService.GetPaymentsAsync((int)SelectedSupplier)).Value :
                                                (await PaymentService.GetPaymentsAsync((int)SelectedSupplier)).Value;
            }
            else
            {
                CurrentSelectedViewPaymentsData = new List<CheckPaymentDto>();
            }  
        }
        PaymentGrid.Rebind();
    }

    private async Task OnViewOptionChange()
    {
        //SupplierOrJobSelected is bound to the dropdown. SupplierOrJobSelectedChange is not. 
        //This check is used to prevent reloading the data if the selection hasn't actually changed 
        //which can happen if the user just clicks in and out of the box, or because the onchange fires onblur as well as on change
        if (SupplierOrJobSelected != SupplierOrJobSelectedChange)
        {
            SupplierOrJobSelectedChange = SupplierOrJobSelected;
            await LoadDataBasedOnSelection();
        }

    }

    private async Task ToggleAllDatesView()
    {
        await LoadDataBasedOnSelection();
    }

    private async Task OnDateChange(object dateSelected)
    {
        if (StartDateValue != StartDateValueChange || EndDateValue != EndDateValueChange)
        {
            StartDateValueChange = StartDateValue;
            EndDateValueChange = EndDateValue;
            await LoadDataBasedOnSelection();
        }
    }

    private async Task LoadDataBasedOnSelection()
    {
        PurchaseOrderFilter = "";
        displayLoadingSpinner = true;
        StateHasChanged();
        JobSelected = SubdivisionJobPickService.JobNumber;
        SelectedSupplier = SubdivisionJobPickService.SupplierNumber;
        if (SupplierOrJobSelected == "Job" && JobSelected != null)
        {
            PaymentsForJob = IsAllDatesSelected ? (await PaymentService.GetPaymentsAsync(JobSelected)).Value :
                                                (await PaymentService.GetPaymentsAsync(JobSelected)).Value;
            CurrentSelectedViewPaymentsData = PaymentsForJob;
        }
        if (SupplierOrJobSelected == "Supplier" && SelectedSupplier != null)
        {
            PaymentsForSupplier = IsAllDatesSelected ? (await PaymentService.GetPaymentsAsync((int)SelectedSupplier)).Value :
                                                (await PaymentService.GetPaymentsAsync((int)SelectedSupplier)).Value;
            CurrentSelectedViewPaymentsData = PaymentsForSupplier;
        }
        if ((SupplierOrJobSelected == "Job" && JobSelected == null) || (SupplierOrJobSelected == "Supplier" && SelectedSupplier == null))
        {
            CurrentSelectedViewPaymentsData = new List<CheckPaymentDto>();
        }
        displayLoadingSpinner = false;
        StateHasChanged();
    }

    async void ShowNotification(string message, bool isSuccess)
    {
        // Alert
        if (!isSuccess)
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = message,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = message,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }
    }

    async Task GenerateDocumentAndDownload(PoheaderDto poHeader, List<PodetailDto> poDetails)
    {
        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(poHeader, poDetails);
        // await PoService.UploadPoAsync(itemToIssue.Poheader, fileData);
        DemoFileExporter.Save(JsRuntime, fileData, "application/pdf", $"PO-{poHeader.Ponumber}.pdf");
    }
    async Task GenerateDocumentAndEmail(PoheaderDto poHeader, List<PodetailDto> poDetails)
    {
        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(poHeader, poDetails);
        var files = new List<FileModel>();
        files.Add(new FileModel()
            {
                FileData = fileData,
                FileName = $"{poHeader.Ponumber}.pdf"
            });

        var emailModel = new PoEmailModel()
            {
                Files = files,
                Poheader = poHeader,
                Subject = $"Van Metre Purchase Order: {poHeader.Ponumber}",
                Body = "Please see the attached purchase order. Visit the Van Metre Trade Portal (https://trades.vanmetrehomes.com/) for more information"
            };
        var response = await PoService.EmailPos(emailModel);
        //update the status to sent if it was issued, keep status as cancelled or approved if it is one of those
        poHeader.PostatusNavigation.Postatus1 = poHeader.Postatus == 1 ? "Sent" : poHeader.PostatusNavigation.Postatus1;
        poHeader.PostatusNavigation.PostatusId = poHeader.Postatus == 1 || poHeader.Postatus == null ? 2 : poHeader.PostatusNavigation.PostatusId;
        poHeader.Postatus = poHeader.Postatus == 1 ? 2 : poHeader.Postatus;//change from issued to sent, but if approved or cancelled don't change status
                                                                           //TODO: response with is success, show success message and only update status if it ws success
                                                                           // if (response.issuccess)
                                                                           // {
                                                                           //     //itemToIssue.Poheader.PostatusNavigation =
                                                                           //     itemToIssue.Poheader.Postatus = itemToIssue.Poheader.Postatus == 1 ? 2 : itemToIssue.Poheader.Postatus;//change from issued to sent, but if approved or cancelled don't change status
                                                                           // }

    }

    private void OnExcelBeforeExport(GridBeforeExcelExportEventArgs args)
    {
        args.Columns[0].Width = "100px";
        args.Columns[1].Width = "100px";

        args.Columns[2].Width = "100px";
        args.Columns[2].NumberFormat = BuiltInNumberFormats.GetShortDate();

        args.Columns[3].Width = "100px";
        args.Columns[4].Width = "200px";
        args.Columns[5].Width = "100px";
        args.Columns[6].Width = "100px";
        args.Columns[7].Width = "100px";
        args.Columns[8].Width = "100px";
    }

    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= JobSelectedHandler;
        SubdivisionJobPickService.OnChanged -= SupplierSelectedHandler;
    }
}
