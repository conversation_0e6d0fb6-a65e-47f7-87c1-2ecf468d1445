﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class MasterColorSchemeModel
{
    public int ColorShemeId { get; set; }
    public string? ColorSchemeName { get; set; }

    public List<MaterialColorSchemeDto>? MaterialColors { get; set; }
    public string? ColorScheme1 { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

   // public byte[] RecordTimeStamp { get; set; } = null!;

   // public virtual ICollection<MaterialColorScheme> MaterialColorSchemes { get; set; } = new List<MaterialColorScheme>();
}
