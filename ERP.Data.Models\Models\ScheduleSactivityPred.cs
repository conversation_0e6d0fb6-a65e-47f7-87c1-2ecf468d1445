﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ScheduleSactivityPred
{
    public int ScheduleAid { get; set; }

    public int PredSactivityId { get; set; }

    public int? PredScheduleAid { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public virtual Sactivity PredSactivity { get; set; } = null!;

    public virtual ScheduleSactivity ScheduleA { get; set; } = null!;
}
