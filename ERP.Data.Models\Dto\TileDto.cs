﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class TileDto
    {
        public string? TotalPendingVPO { get; set; }
        public decimal? TrendingPercentage { get; set; }
        public string? TotalOptionsSold { get; set; }
        public string? TotalEstimateBudget { get; set; }
        public string? JobNumber { get; set; }
        public string? TotalIssuedBudget { get; set; }
        public List<int>? TotalIncompleteActivitiesBySubdivision { get; set; }
        public List<string>? Communities { get; set; }
        public List<int>? CommunityIds { get; set; }
        public List<string>? JobNumbers { get; set; }
        public List<string>? ActivityNames { get; set; }
        public List<CommunityTileDto>? HouseCostTiles { get; set; }
    }

    public class CommunityTileDto
    {
        public string? Community { get; set; }
        public string? HousePlan { get; set; }
        public string? Building { get; set; }
        public string? TotalHouseCost { get; set; }
    }
}
