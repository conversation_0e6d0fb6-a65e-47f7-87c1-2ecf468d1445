﻿using Telerik.Documents.Primitives;
using Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.Export;
using Telerik.Windows.Documents.Fixed.FormatProviders.Pdf;
using Telerik.Windows.Documents.Fixed.Model.ColorSpaces;
using Telerik.Windows.Documents.Fixed.Model.Editing;
using Telerik.Windows.Documents.Fixed.Model.Fonts;
using Telerik.Windows.Documents.Fixed.Model;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Table = Telerik.Windows.Documents.Fixed.Model.Editing.Tables.Table;
using TableRow = Telerik.Windows.Documents.Fixed.Model.Editing.Tables.TableRow;
using TableCell = Telerik.Windows.Documents.Fixed.Model.Editing.Tables.TableCell;
using DocumentFormat.OpenXml.Bibliography;
using Telerik.Windows.Documents.Fixed.Model.Resources;
using Telerik.SvgIcons;
using DocuSign.eSign.Model;
using Telerik.Blazor.Components.ColorGradient;
using Telerik.Documents.Core.Fonts;
using FontFamily = Telerik.Documents.Core.Fonts.FontFamily;
using Telerik.Windows.Documents.Fixed.Model.Editing.Flow;
using Telerik.Blazor.Components.Editor;

namespace ERP.Web.DocumentProcessing
{
    public static class GenerateAddendaPdf
    {
        public static byte[] GenerateFile(List<AddendaModel> itemDetails, string SubdivsionName, string SupplierName, DateTime effectiveDate)
        {
            PdfFormatProvider formatProvider = new PdfFormatProvider();
            formatProvider.ExportSettings.ImageQuality = ImageQuality.High;

            Telerik.Documents.ImageUtils.ImagePropertiesResolver defaultImagePropertiesResolver = new Telerik.Documents.ImageUtils.ImagePropertiesResolver();
            Telerik.Windows.Documents.Extensibility.FixedExtensibilityManager.ImagePropertiesResolver = defaultImagePropertiesResolver;

            byte[] renderedBytes = null;
            using (MemoryStream ms = new MemoryStream())
            {
                RadFixedDocument document = CreateDocument(itemDetails, SubdivsionName, SupplierName, effectiveDate);
                formatProvider.Export(document, ms);
                renderedBytes = ms.ToArray();
            }

            return renderedBytes;
        }
        private static readonly double defaultLeftIndent = 50;
        private static readonly double defaultLineHeight = 18;
        public static RadFixedDocument CreateDocument(List<AddendaModel> itemDetails, string SubdivsionName, string SupplierName, DateTime effectiveDate)
        {
            RadFixedDocument document = new RadFixedDocument();

            

            double currentTopOffset = 50;          
            var Items = itemDetails.OrderBy(x => x.ItemDesc).GroupBy(x => new {x.HouseTypeName, x.Activity}).GroupBy(x => x.Key.HouseTypeName).ToList();

            var newEditor = new RadFixedDocumentEditor(document);
            newEditor.SectionProperties.PageMargins = new Thickness(50, 100, 50, 100);

            try
            {
                currentTopOffset += defaultLineHeight * 4;
                newEditor.InsertLineBreak();
                RadFixedPage currentPage = document.Pages[0];
                var width = currentPage.Size.Width;
                var middle = currentPage.Size.Width / 2;
                var widthExceptMargin = width - 100;//left right margins are set to 50 above
                var halfPageWidth = widthExceptMargin / 2;
               // FixedContentEditor editor = new FixedContentEditor(currentPage);
               // editor.Position.Translate(595, 45);
                InsertSubdivisionSupplier(newEditor, SubdivsionName, SupplierName, effectiveDate, widthExceptMargin);
                newEditor.InsertLineBreak();
                InsertMoreDescription(newEditor);
                newEditor.InsertLineBreak();
               // newEditor.InsertParagraph();
               // Block block1 = new Block();
                //block1.GraphicProperties.FillColor = new RgbColor(255, 255, 255);
              //  block1.InsertText("Sign here");
               // block1.InsertLineBreak();
               // newEditor.InsertBlock(block1);
               // newEditor.InsertLine("signature_1");
               // newEditor.InsertLineBreak();
                // newEditor.InsertImageInline(imageSource, size);
                int i = 0;//count houseType to insert page break, not after last one
                foreach (var houseType in Items)
                {
                    i++;
                    Block block = new Block();
                    block.TextProperties.FontSize = 20;
                    block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"House Type:{houseType.Key}");
                    block.TextProperties.FontSize = 14;
                    block.InsertLineBreak();
                    newEditor.InsertBlock(block);
                    foreach (var activity in houseType)
                    {
                        Block block2 = new Block();
                        block2.TextProperties.UnderlinePattern = UnderlinePattern.Single;
                        block2.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, activity.Key.Activity);
                        newEditor.InsertBlock(block2);
                        InsertItemsTable(newEditor, activity.ToList());
                        newEditor.InsertLineBreak();
                    }
                    if (i < Items.Count)
                    {
                        newEditor.InsertPageBreak();
                    }
                   
                }

            }
            catch (Exception ex)
            {
                var debug = ex.Message;
            }           
            DrawHeaderToDocument(document, SubdivsionName, SupplierName, effectiveDate);
            DrawFooterToDocument(document);
            return document;
        }

        private static void DrawAddendaHeading(FixedContentEditor editor)
        {
            Block block = new Block();
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
            block.TextProperties.Font = FontsRepository.HelveticaBoldOblique;
            block.InsertText("Addenda");
            block.InsertLineBreak();
            block.TextProperties.Font = FontsRepository.Helvetica;
            editor.DrawBlock(block, new Size(200, 40));
        }
      
        private static void InsertItemsTable(RadFixedDocumentEditor editor, List<AddendaModel> Items)
        {
            try
            {
                Table table = new Table();
                table.DefaultCellProperties.Padding = new Thickness(5, 5, 0, 0);
                table.LayoutType = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.TableLayoutType.AutoFit;
                TableRow firstRow = table.Rows.AddTableRow();
                //firstRow.Cells.AddTableCell().Blocks.AddBlock().InsertText("Item No.");
                TableCell c1 = firstRow.Cells.AddTableCell();
                c1.Blocks.AddBlock().InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Description");
                c1.PreferredWidth = 490;
                TableCell c2 = firstRow.Cells.AddTableCell();
                c2.Blocks.AddBlock().InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Qty");
                c2.PreferredWidth = 40;
                TableCell c3 = firstRow.Cells.AddTableCell();
                c3.Blocks.AddBlock().InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Unit");
                c3.PreferredWidth = 40;
                TableCell c4 = firstRow.Cells.AddTableCell();
                Block headerBlock4 = new Block();
                headerBlock4.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Right;
                headerBlock4.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Unit Price");
                c4.Blocks.Add(headerBlock4);
                c4.PreferredWidth = 60;
                TableCell c5 = firstRow.Cells.AddTableCell();
                Block headerBlock5 = new Block();
                headerBlock5.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Right;
                headerBlock5.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Total");
                c5.Blocks.Add(headerBlock5);
                c5.PreferredWidth = 60;

                foreach (var item in Items)
                {
                    TableRow addRow = table.Rows.AddTableRow();
                    TableCell cell1 = addRow.Cells.AddTableCell();
                    cell1.Blocks.AddBlock().InsertText($"{item.ItemDesc}");
                    cell1.PreferredWidth = 500;
                    TableCell cell2 = addRow.Cells.AddTableCell();
                    cell2.Blocks.AddBlock().InsertText($"{item.Qty}");
                    cell2.PreferredWidth = 40;
                    TableCell cell3 = addRow.Cells.AddTableCell();
                    cell3.Blocks.AddBlock().InsertText($"{item.OrderUnit}");
                    cell3.PreferredWidth = 40;

                    TableCell cell4 = addRow.Cells.AddTableCell();
                    Block block4 = new Block();
                    block4.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Right;
                    block4.InsertText($"{item.UnitCost:n2}");
                    cell4.Blocks.Add(block4);
                    cell4.PreferredWidth = 60;

                    TableCell cell5 = addRow.Cells.AddTableCell();
                    Block block5 = new Block();
                    block5.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Right;
                    block5.InsertText($"{item.TotalCost:n2}");
                    cell5.Blocks.Add(block5);
                    cell5.PreferredWidth = 60;

                    //addRow.Cells.AddTableCell().Blocks.AddBlock().InsertText($"{item.ItemDesc}");
                    //addRow.Cells.AddTableCell().Blocks.AddBlock().InsertText($"{item.Qty}");
                    //addRow.Cells.AddTableCell().Blocks.AddBlock().InsertText($"{item.OrderUnit}");
                    //addRow.Cells.AddTableCell().Blocks.AddBlock().InsertText($"{item.UnitCost:n2}");
                    //addRow.Cells.AddTableCell().Blocks.AddBlock().InsertText($"{item.TotalCost:n2}");
                }
                editor.InsertTable(table);
            }
            catch (Exception ex) 
            {
                var debug = ex.Message;
            }

        }
        private static void InsertSubdivisionSupplier(RadFixedDocumentEditor editor, string subdivName, string supplierName, DateTime dateEffective, double width = 700)
        {
            Block block = new Block();
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
           // editor.GraphicProperties.StrokeColor = RgbColors.Black;
           // editor.GraphicProperties.FillColor = RgbColors.White;
            block.TextProperties.Font = FontsRepository.HelveticaBold;
            block.InsertText($"{subdivName}");
            block.InsertLineBreak();
            block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Supplier Name: ");
            block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $"{supplierName}");
            block.InsertLineBreak();
            block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Date Effective: ");
            block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $"{dateEffective.ToString("MM/dd/yyyy")}");
            
            Point leftFullLineStart = new Point(0, 0);
            Point rightFullLineEnd = new Point(width, 0);
            block.InsertLine(leftFullLineStart, rightFullLineEnd);

            block.InsertLineBreak();
            block.InsertLineBreak();

            block.GraphicProperties.FillColor = new RgbColor(255, 255, 255);
            block.InsertText($"**name_1**");
            block.InsertLineBreak();
            block.GraphicProperties.FillColor = new RgbColor(0, 0, 0);
            block.InsertText("Vendor Name");
            
            Point leftLineStart = new Point(150, 0);
            Point rightLineEnd = new Point(770, 0);
            block.InsertLine(leftLineStart, rightLineEnd);
            block.InsertLineBreak();


            block.GraphicProperties.FillColor = new RgbColor(255, 255, 255);
            block.InsertText($"**signature_1**");
            block.InsertLineBreak();
            block.GraphicProperties.FillColor = new RgbColor(0, 0, 0);
            block.InsertText("Vendor Signature");
            
            leftLineStart = new Point(150, 0);
            rightLineEnd = new Point(750, 0);
            block.InsertLine(leftLineStart, rightLineEnd);
            block.InsertLineBreak();

            block.GraphicProperties.FillColor = new RgbColor(255, 255, 255);
            block.InsertText($"**datesigned_1**");
            block.InsertLineBreak();
            block.GraphicProperties.FillColor = new RgbColor(0, 0, 0);
            block.InsertText("Vendor Signed Date");

            leftLineStart = new Point(150, 0);
            rightLineEnd = new Point(730, 0);
            block.InsertLine(leftLineStart, rightLineEnd);
            block.InsertLineBreak();

            block.GraphicProperties.FillColor = new RgbColor(255, 255, 255);
            block.InsertText($"**name_2**");
            block.InsertLineBreak();
            block.GraphicProperties.FillColor = new RgbColor(0, 0, 0);
            block.InsertText("Van Metre Name");

            leftLineStart = new Point(150, 0);
            rightLineEnd = new Point(750, 0);
            block.InsertLine(leftLineStart, rightLineEnd);
            block.InsertLineBreak();

            block.GraphicProperties.FillColor = new RgbColor(255, 255, 255);
            block.InsertText($"**signature_2**");
            block.InsertLineBreak();
            block.GraphicProperties.FillColor = new RgbColor(0, 0, 0);
            block.InsertText("Van Metre Signature");

            leftLineStart = new Point(150, 0);
            rightLineEnd = new Point(730, 0);
            block.InsertLine(leftLineStart, rightLineEnd);
            block.InsertLineBreak();

            block.GraphicProperties.FillColor = new RgbColor(255, 255, 255);
            block.InsertText($"**datesigned_2**");
            block.InsertLineBreak();
            block.GraphicProperties.FillColor = new RgbColor(0, 0, 0);
            block.InsertText("Van Metre Signed Date");

            leftLineStart = new Point(150, 0);
            rightLineEnd = new Point(720, 0);
            block.InsertLine(leftLineStart, rightLineEnd);
            block.InsertLineBreak();

            editor.InsertBlock(block);
        }


        private static void InsertMoreDescription(RadFixedDocumentEditor editor)
        {
            Block block = new Block();
            block.GraphicProperties.FillColor = RgbColors.Black;
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
            block.TextProperties.Font = FontsRepository.HelveticaBoldOblique;
            block.InsertText("Contract Terms");
            block.InsertLineBreak();
            editor.InsertBlock(block);
        }
        public static void DrawFooterToDocument(RadFixedDocument document)
        {
            int numberOfPages = document.Pages.Count;
            for (int pageIndex = 0; pageIndex < numberOfPages; pageIndex++)
            {
                int pageNumber = pageIndex + 1;
                RadFixedPage currentPage = document.Pages[pageIndex];
                DrawFooterToPage(currentPage, pageNumber, numberOfPages);
            }
        }
        public static void DrawHeaderToDocument(RadFixedDocument document, string SubdivsionName, string SupplierName, DateTime effectiveDate)
        {
            int numberOfPages = document.Pages.Count;
            for (int pageIndex = 0; pageIndex < numberOfPages; pageIndex++)
            {
                int pageNumber = pageIndex + 1;
                RadFixedPage currentPage = document.Pages[pageIndex];
                DrawHeaderToPage(currentPage, pageNumber, numberOfPages, SubdivsionName, SupplierName, effectiveDate);
            }
        }
        private static void DrawHeaderToPage(RadFixedPage page, int pageNumber, int numberOfPages, string SubdivsionName, string SupplierName, DateTime effectiveDate)
        {
            FixedContentEditor pageEditor = new FixedContentEditor(page);

            //Block header = new Block();
            //header.InsertText(String.Format("Page {0} of {1}", pageNumber, numberOfPages));
            //header.Measure();
            double currentTopOffset = 50;
            //pageEditor.Position.Translate(400, currentTopOffset);
            //pageEditor.DrawBlock(header);
            pageEditor.Position.Translate(defaultLeftIndent, 50);
            var rootpath = Directory.GetCurrentDirectory();
            using (FileStream fs = new FileStream(
                System.IO.Path.Combine(rootpath, @"wwwroot/css/assets/images/vmlogo.png"),
                FileMode.Open,
                FileAccess.Read))
            {
                pageEditor.DrawImage(fs);
            }

            pageEditor.Position.Translate(400, currentTopOffset);
            pageEditor.TextProperties.FontSize = 10;
            double maxWidth = page.Size.Width - defaultLeftIndent * 2;
            DrawAddendaHeading(pageEditor);

            //currentTopOffset += defaultLineHeight * 4;
            //pageEditor.Position.Translate(defaultLeftIndent, currentTopOffset);
            //DrawSubdivisionSupplier(pageEditor, maxWidth, SubdivsionName, SupplierName, effectiveDate);

            //currentTopOffset += 80;
            //pageEditor.Position.Translate(defaultLeftIndent, currentTopOffset);

            //DrawMoreDescription(pageEditor, maxWidth);

        }
        private static void DrawFooterToPage(RadFixedPage page, int pageNumber, int numberOfPages)
        {
            FixedContentEditor pageEditor = new FixedContentEditor(page);

            Block footer = new Block();
            footer.InsertText(String.Format("Page {0} of {1}", pageNumber, numberOfPages));
            footer.Measure();

            double footerOffsetX = (page.Size.Width / 2) - (footer.DesiredSize.Width / 2);
            double footerOffsetY = page.Size.Height - 30 - footer.DesiredSize.Height;
            pageEditor.Position.Translate(footerOffsetX, footerOffsetY);
            pageEditor.DrawBlock(footer);
        }
    }
}
