﻿@using ERP.Data.Models;
@inject SubdivisionService SubdivisionService
@inject PoService PoService
@using ERP.Data.Models.Dto;

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Assign Suppliers to Selected Activities and Subdivisions
    </WindowTitle>
    <WindowContent>
        <div class="mb-3">
        <label class="form-label">Select Supplier</label><br />
        <TelerikDropDownList @bind-Value="@SelectedSupplier.SubNumber"
        DefaultText="Select Supplier"
        ScrollMode="@DropDownScrollMode.Virtual"
        ItemHeight="40"
        PageSize="20"
        Filterable="true"
        FilterOperator="StringFilterOperator.Contains"
        TextField="SubName"
        ValueField="SubNumber"
        Data="@AllSuppliers">
        </TelerikDropDownList>

        </div>
        <div class="mb-3">
            <label class="form-label">Select Subdivisions</label><br />
            <TelerikMultiSelect Data="@AllSubdivisions" 
                @bind-Value="@SelectedSubdivisionIds"
                Filterable="true"
                FilterOperator="StringFilterOperator.Contains"
                TextField="SubdivisionName" 
                ValueField="SubdivisionId">
            </TelerikMultiSelect>
        </div>
        <div class="mb-3">
            <label class="form-label">Select Activites</label><br />
            <TelerikMultiSelect Data="@AllActivities"
            @bind-Value="@SelectedActivityIds"
                                Filterable="true"
                                FilterOperator="StringFilterOperator.Contains"
                                TextField="Activity"
                                ValueField="PactivityId">
            </TelerikMultiSelect>
        </div>
        <button type="button" @onclick="AssignSelected" class="btn btn-primary">Assign</button>
        <button type="button" @onclick="CancelAddItem" class="btn btn-secondary">Cancel</button>
        <div style=@submittingStyle>Assigning. Please wait...</div>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    [Parameter]
    public List<int>? SelectedActivitiesId { get; set; }
    [Parameter]
    public List<int>? SelectedSubdivisionsId { get; set;  }
    public SupplierDto? SelectedSupplier { get; set; } = new SupplierDto();
    public List<SupplierDto>? AllSuppliers {get; set; }
    public List<SubdivisionDto>? AllSubdivisions { get; set; }
    public List<PactivityDto>? AllActivities { get; set; }
    private string submittingStyle = "display:none";
    public List<int>? SelectedSubdivisionIds { get; set; } = new List<int>();
    public List<int>? SelectedActivityIds { get; set; } = new List<int>();
    [Parameter]
    public EventCallback<List<PactivityAreaSupplierDto>> HandleAddSubmit { get; set; }
    public List<PactivityAreaSupplierDto>? SelectedPactivityAreaSuppliers { get; set; } = new List<PactivityAreaSupplierDto>();
    public async Task Show()
    {
        IsModalVisible = true;
        var activitiesTask = PoService.GetPactivitiesAsync();
        var suppliersTask =  PoService.GetSuppliersAsync(IncludeBlocked: true);
        var subdivisionTask = SubdivisionService.GetSubdivisionsAsync();
        await Task.WhenAll(new Task[] { activitiesTask, suppliersTask, subdivisionTask });
        AllSubdivisions = subdivisionTask.Result.Value;
        AllSuppliers = suppliersTask.Result.Value;
        AllActivities = activitiesTask.Result.Value;
        SelectedSubdivisionIds = SelectedSubdivisionsId;
        SelectedActivityIds = SelectedActivitiesId;
        StateHasChanged();
        
    }


    private async void AssignSelected()
    {
        submittingStyle = "";
        foreach(var activityId in SelectedActivityIds)
        {
            foreach(var subdivisionId in SelectedSubdivisionIds)
            {
                SelectedPactivityAreaSuppliers.Add(new PactivityAreaSupplierDto()
                    {
                        PactivityId = activityId,
                        SubdivisionId = subdivisionId,
                        SubNumber = SelectedSupplier.SubNumber
                    });
            }
            
        }
        var responseItem = await PoService.AssignActivitySuppliers(SelectedPactivityAreaSuppliers);
        submittingStyle = "display:none";
        //after add, a new window pops up to add the items, for now selecting by trade/activity/item and adding a Base house as the option level
        await HandleAddSubmit.InvokeAsync(SelectedPactivityAreaSuppliers);
    }
    async void CancelAddItem()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;
    }
   
}
