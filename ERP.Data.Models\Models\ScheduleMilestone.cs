﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ScheduleMilestone
{
    public int ScheduleMid { get; set; }

    public int ScheduleId { get; set; }

    public int MilestoneId { get; set; }

    public int? Seq { get; set; }

    public int? Duration { get; set; }

    public DateTime? IniStartDate { get; set; }

    public DateTime? IniEndDate { get; set; }

    public DateTime? BaseStartDate { get; set; }

    public DateTime? BaseEndDate { get; set; }

    public DateTime? SchStartDate { get; set; }

    public DateTime? SchEndDate { get; set; }

    public DateTime? ActualStartDate { get; set; }

    public DateTime? ActualEndDate { get; set; }

    public int? PlusminusDays { get; set; }

    public int? Calduration { get; set; }

    public int? ActualDuration { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual Milestone Milestone { get; set; } = null!;

    public virtual Schedule Schedule { get; set; } = null!;

    public virtual ICollection<ScheduleSactivity> ScheduleSactivities { get; set; } = new List<ScheduleSactivity>();
}
