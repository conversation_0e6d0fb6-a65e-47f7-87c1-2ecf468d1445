﻿using Openize.Heic;
using Openize.Heic.Decoder;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Formats.Jpeg;
using System.IO;

namespace ERP.Web.DocumentProcessing
{
    public static class ImageConverter
    {
        public static byte[] ConvertHeicBytesToJpeg(byte[] heicBytes)
        {
            using var heicStream = new MemoryStream(heicBytes);
            var heicImage = HeicImage.Load(heicStream);

            var width = (int)heicImage.Width;
            var height = (int)heicImage.Height;
            var pixels = heicImage.GetByteArray(Openize.Heic.Decoder.PixelFormat.Bgra32);

            // Convert raw bytes into ImageSharp image
            using var image = Image.LoadPixelData<Bgra32>(pixels, width, height);

            using var jpegStream = new MemoryStream();
            image.Save(jpegStream, new JpegEncoder());
            return jpegStream.ToArray();
        }

        public static bool IsHeicFile(string fileName)
        {
            return Path.GetExtension(fileName).Equals(".heic", StringComparison.OrdinalIgnoreCase);
        }
    }
}
