﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class ScheduleSactivityDto : IMapFrom<ScheduleSactivity>
{
    public int ScheduleAid { get; set; }
    public string? ActivityName { get; set; } 
    public string? Predecessor { get; set; }
    public List<int>? PredIds { get; set; }
    public int? ScheduleMid { get; set; }

    public int? ScheduleId { get; set; }

    public int? SactivityId { get; set; }

    public int? SubNumber { get; set; }

    public int? Seq { get; set; }

    public int? Duration { get; set; }

    public int? LagTime { get; set; }

    public string? UserAlpha1 { get; set; }

    public string? UserAlpha2 { get; set; }

    public string? UserAlpha3 { get; set; }

    public string? UserAlpha4 { get; set; }

    public string? UserAlpha5 { get; set; }

    public string? UserAlpha6 { get; set; }

    public DateTime? UserDate1 { get; set; }

    public DateTime? UserDate2 { get; set; }

    public DateTime? UserDate3 { get; set; }

    public DateTime? UserDate4 { get; set; }

    public DateTime? UserDate5 { get; set; }

    public DateTime? UserDate6 { get; set; }

    public DateTime? BaseStartDate { get; set; }

    public DateTime? BaseEndDate { get; set; }

    public DateTime? IniStartDate { get; set; }

    public DateTime? IniEndDate { get; set; }

    public DateTime? SchStartDate { get; set; }

    public DateTime? SchEndDate { get; set; }

    public DateTime? ActualStartDate { get; set; }

    public DateTime? ActualEndDate { get; set; }

    public string? Reminder { get; set; }

    public DateTime? ReminderDate { get; set; }

    public string? ReminderText { get; set; }

    public string? Note { get; set; }

    public DateTime? NotifyDate { get; set; }

    public DateTime? UpdateDate { get; set; }

    public string? VarianceCode { get; set; }

    public string? Complete { get; set; }
    public bool BoolComplete { get; set; }
    public bool? NewlyComplete { get; set; }
    public bool BoolStarted { get; set; }

    public int? SaChecklistId { get; set; }

    public int? PlusminusDays { get; set; }

    public string? AdjustReminderWithProjStart { get; set; }

    public int? Calduration { get; set; }

    public double? UserCurrency1 { get; set; }

    public double? UserCurrency2 { get; set; }

    public double? UserCurrency3 { get; set; }

    public double? UserCurrency4 { get; set; }

    public double? UserCurrency5 { get; set; }

    public double? UserCurrency6 { get; set; }

    public string? TradeCrew { get; set; }

    public int? ActualDuration { get; set; }

    public string? GrossLag { get; set; }

    public string? Excludefromschedule { get; set; }

    public string? SupplierNote { get; set; }

    public string? IsLocked { get; set; }
    public bool BoolIgnoreNoWorkDays { get; set; }//IsLocked is for Ignore no work days

    public string? GenPitBudget { get; set; }

    public int? SchDatesPinned { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }
    public DateTime? CompletedDate { get; set; }

    public string? CompletedBy { get; set; }
    public bool? IsChanged { get; set; }//track whether the item is actually changed to reduce unnecessary updates

    public SactivityDto? Sactivity { get; set; }

    public ScheduleDto? Schedule { get; set; }

    public ScheduleMilestoneDto? ScheduleM { get; set; }

    public SupplierDto? SubNumberNavigation { get; set; }

    public VarianceDto? VarianceCodeNavigation { get; set; }
   // public ICollection<ScheduleSactivityLinkDto>? ScheduleSactivityLinks { get; set; } = new List<ScheduleSactivityLinkDto>();

   // public List<ScheduleSactivityPredDto>? ScheduleSactivityPreds { get; set; } //will create loop when try to deserialize, because schedulesactivitypred dto has schedule activity dto in it
    public bool HasPo { get; set; }
    public bool SupplierEditable { get; set; }  //sactivity supplier will be editable if no issued pos
    public bool IsAllDay { get; set; } = true;
    public DateTime? ProjStartDate { get; set; }
    public DateTime ProjEndDate { get; set; }
    public List<ScheduleSactivityPredDto>? Predecessors { get; set; }
    public int? FirstPredId { get; set; }
    public double? PercentComplete { get; set; }
    public string? PercentCompleteTitle { get; set; }
    public bool HasChildren { get; set; } = false;
    public DateTime? Start { get; set; }
    public DateTime? End { get; set; }
    public string? Title { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<ScheduleSactivityDto, ScheduleSactivity>().ReverseMap();
    }
}
