﻿using System;
using System.Collections.Generic;
using ERP.Data.Models.Abstract;

namespace ERP.Data.Models.Dto;

public partial class EstdetailDto : IMapFrom<Estdetail> 
{
    public int EstdetailId { get; set; }

    public int? EstoptionId { get; set; }

    public int? EstactivityId { get; set; }

    public int? MasterItemsId { get; set; }

    public string? PhaseCode { get; set; }

    public string? ItemNumber { get; set; }

    public string? Category { get; set; }

    public int? Instance { get; set; }

    public int? SeqNumber { get; set; }

    public string? Location { get; set; }

    public string? SortLocation { get; set; }

    public string? SortWbs { get; set; }

    public string? ItemDesc { get; set; }

    public double? TakeoffQuantity { get; set; }

    public string? TakeoffUnit { get; set; }

    public short? Waste { get; set; }

    public int? VendorNumber { get; set; }

    public string? LengthWbs { get; set; }

    public string? NoOfLengths { get; set; }

    public string? ThicknessWbs { get; set; }

    public string? WidthWbs { get; set; }

    public string? ApplyWaste { get; set; }

    public double? CnvFctr { get; set; }

    public string? Multdiv { get; set; }

    public string? OrdrUnit { get; set; }

    public string? RndDir { get; set; }

    public double? RndUnit { get; set; }

    public double? OrderQty { get; set; }

    public double? Price { get; set; }

    public double? Amount { get; set; }

    public string? Lump { get; set; }
    
    public bool BoolLump { get; set; }
    
    public string? Taxable { get; set; }
    //public bool? BoolTaxable
    //{
    //    get { return Taxable == "T"; }
    //    set { }
    //}
    public bool BoolTaxable { get; set; }
    
    public bool? PoSent { get; set; }//If sent at time of issue, this tracks so po status gets set to sent
    public bool TrackingVariance//TODO: fix
    {
        get { return OrgPrice != null && OrgPrice != Price; }
        set { }
    }
    public string? TaxGroup { get; set; }

    public string? TaxGroupType { get; set; }

    public double? TaxAmount { get; set; }

    public string? JcPhase { get; set; }

    public string? JcCategory { get; set; }

    public string? UseEstPrice { get; set; }
    public bool BoolUseEstPrice { get; set; }
    //public bool? BoolUseEstPrice
    //{
    //    get { return UseEstPrice == "T"; }
    //    set { }//TODO: I don't think this works
    //}

    public double? OrgPrice { get; set; }

    public string? EstimateExported { get; set; }

    public double? EstimateExportAmt { get; set; }

    public string? PoExported { get; set; }

    public string? ChangedInHms { get; set; }

    public string? Warnings { get; set; }
    public string? DisplayWarnings { get; set; }

    public string? Errors { get; set; }//this column in db hold numbers, eg "4", "4 |6| 7"
    public string? DisplayErrors { get; set; }//text to display to users, eg "Zero Cost not Allowed"

    public string? VarianceJcCategory { get; set; }

    public int? PodetailId { get; set; }

    public double? OrigCommittedAmount { get; set; }

    public double? OrigCommittedTaxAmount { get; set; }

    public string? ReissueItem { get; set; }

    public int? EstjcedetailId { get; set; }
    public bool? IsIssued
    {
        get { return EstjcedetailId != null; }
    }

    public string? ItemNotes { get; set; }

    public string? DeletedFromPe { get; set; }

    public int? SupplierContractsId { get; set; }

    public int? SupplierContCoId { get; set; }

    public double? EstimateExportTax { get; set; }

    public string? EstimateVarianceItem { get; set; }

    public string? EstimateOnlyVarianceItem { get; set; }

    public int? OrigBudgetEstdetailId { get; set; }

    public int? CostsId { get; set; }

    public string? EstimateExportTaxGroup { get; set; }

    public double? EstimateExportQty { get; set; }

    public string? EstimateExportUnit { get; set; }

    public string? EstimateReissueItem { get; set; }

    public double? OrigCommittedQty { get; set; }

    public int? OrigEstoptionId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTim { get; set; }

    public string? UpdatedBy { get; set; }

    public int? SupplierCostreservationId { get; set; }

    public string? CubitId { get; set; }

    public int? HeadingId { get; set; }

    public int? EstdetailcalcsheetId { get; set; }

    public int? EstitemheaderId { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

   // public byte[]? RecordTimeStamp { get; set; }

    public EstactivityDto? Estactivity { get; set; }
    public MasterItemDto? MasterItems { get; set; }
    public CostDto? Costs { get; set; }
    public EstjcedetailDto? Estjcedetail { get; set; }

    public EstoptionDto? Estoption { get; set; }

    public  PodetailDto? Podetail { get; set; }

    public  SupplierDto? VendorNumberNavigation { get; set; }
}
