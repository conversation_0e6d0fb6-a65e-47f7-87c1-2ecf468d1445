﻿@page "/customestimatedetail/{custestid:int}"
@inject BudgetService BudgetService
@inject TradeService TradeService
@inject PoService POService
@inject SubdivisionService SubdivisionService
@inject AuthenticationStateProvider AuthenticationStateProvider
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling")]
@using ERP.Data.Models
@using System.Collections.ObjectModel
@using Telerik.DataSource.Extensions

<style type="text/css">
    /* Gutter */
    .row > * {
        padding-right: calc(var(--bs-gutter-x) * .10);
        padding-left: calc(var(--bs-gutter-x) * .10);
    }

</style>

<PageTitle>Custom Estimate: @EstCustOption.Optiondesc</PageTitle>

<TelerikTooltip TargetSelector=".tooltip-target" />

@if (EstHeaders == null)
{
    <p><em>Loading...</em></p>
}
else
{
    <div class="col-lg-12">
        <div class="card" style="background-color:#2e5771">
            <div class="card-body" style="padding:0.5rem">
                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Custom Estimate: @EstCustOption.Optiondesc</h7>
            </div>
        </div>
    </div>

    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/customestimate">Custom Estimate</a></li>
        <li class="breadcrumb-item active">Custom Estimate Details</li>
    </ol>

    <div class="container-fluid">
        
        <div class="row">
            <div class="card col-3">
                <div class="card-body">
                <p><strong>Option Code:</strong> @EstCustOption.Optioncode</p>
                <p><strong>Option Description:</strong> @EstCustOption.Optiondesc</p>
                <p><strong>Built Option Id:</strong> @EstCustOption.Builtoptionid</p>
                <p><strong>Subdivision:</strong> @EstCustOption.Subdivision?.SubdivisionName</p>
                <p><strong>Job Number:</strong> @EstCustOption.JobNumber</p>
                <p><strong>Estimator:</strong> @EstCustOption.Estheader?.Estimator</p>
                <p><strong>Estimator Email:</strong> @EstCustOption.EstimatorEmail</p>
                <p><strong>Sales Price:</strong> @EstCustOption.Price</p>
                <p><strong>Estimator Notes:</strong> @EstCustOption.EstimatorNotes</p>
                <p><strong>Customer Notes:</strong> @EstCustOption.Customerdesc</p>
                    <p><strong>Customer Approved:</strong> @EstCustOption.IsCustomerapproved</p>
                </div>
            </div>
            <div class="col-4" style="margin-bottom:1rem">
                
                <TelerikTreeList Data="@BudgetData"
                                 SelectionMode="@TreeListSelectionMode.Multiple"
                                 IdField="Id"
                                 ParentIdField="ParentId"
                                 @ref="@BudgetTreeList"
                                 OnRowClick="@RowSelectedHandler"
                                 OnExpand="@OnExpand"
                                 EditMode="@TreeListEditMode.Inline"
                                 ConfirmDelete="true"
                                 OnDelete="@DeleteHandler"
                                 Height="800px"
                                 Width="100%">
                    <TreeListColumns>
                        <TreeListColumn Field="Is Issued" Visible="true" Editable="true" Width="65px">
                            <Template>
                                @{
                                    SelectedRow = context as CombinedPOBudgetTreeModel;                                    
                                    bool enabled = SelectedRow.IssueEnabled != false;
                                    bool indeterminate = false;
                                    <TelerikCheckBox Indeterminate="@indeterminate" Enabled=@enabled @bind-Value="SelectedRow.IsIssued" OnChange="@ChangeHandler"></TelerikCheckBox>
                                }
                            </Template>                           
                        </TreeListColumn>
                        <TreeListColumn Field="JobNumber" Title="Job / Option / Activity / Item" Expandable="true" Editable="false" Visible="true">
                            <Template>
                                @{
                                    var item = context as CombinedPOBudgetTreeModel;
                                    if (item.Estheader != null)
                                    {
                                        @($"{item.Estheader.JobNumber} - {item.Estheader.EstheaderId}")
                                    }
                                    else if (item.Estoption != null && item.Estactivity == null)
                                    {
                                        @($"{item.Estoption.OptionNumber} - {item.Estoption.OptionDesc}")
                                    }
                                    else if (item.Estactivity != null && item.Estdetail == null)
                                    {
                                        @($"{item.Estactivity.BomClass}")
                                    }
                                    else
                                    {
                                        @($"{item.Estdetail.ItemNumber} - {item.Estdetail.ItemDesc}")
                                    }
                                }
                            </Template>
                        </TreeListColumn>
                        <TreeListColumn Field="EstheaderId" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="EstoptionId" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="OptionNumber" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="OptionDesc" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="EstactivityId" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="BomClass" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="EstdetailId" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="ItemNumber" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="ItemDesc" Visible="false"></TreeListColumn>
                        <TreeListCommandColumn Width="100px">                           
                            @{
                                var row = context as CombinedPOBudgetTreeModel;
                                if (row.Estdetail != null)
                                {
                                    <TreeListCommandButton Title="Delete"  Command="Delete" Class="tooltip-target k-button-danger"  Icon="@FontIcon.Trash"></TreeListCommandButton>
                                    <TreeListCommandButton Title="Copy Item" Class="tooltip-target k-button-success" OnClick="@CopyItem" Icon="@FontIcon.Copy"></TreeListCommandButton>                                
                                }
                                else if (row.Estactivity != null)
                                {
                                    <TreeListCommandButton Title="Delete"  Command="Delete" Class="tooltip-target k-button-danger"  Icon="@FontIcon.Trash"></TreeListCommandButton>
                                }
                                else if (row.Estoption != null)
                                {
                                    <TreeListCommandButton Title="Delete" Command="Delete" Class="tooltip-target k-button-danger" Icon="@FontIcon.Trash"></TreeListCommandButton>
                                    <TreeListCommandButton Title="Add Item" Class="tooltip-target k-button-add" OnClick="@ShowAddItemToBudget" Icon="@FontIcon.Plus"></TreeListCommandButton>
                                }
                            }
                        </TreeListCommandColumn>                        
                    </TreeListColumns>
                    <TreeListToolBarTemplate>
                        <TreeListSearchBox />
                       @*  <TreeListCommandButton Command="IssueBudgets" OnClick="@IssueBudgets">Issue Budgets</TreeListCommandButton> *@
                      @*   <TreeListCommandButton Title="Add Items" class="tooltip-target" Command="AddItems" Icon="@FontIcon.Plus" OnClick="@ShowAddItemToBudget" ></TreeListCommandButton> *@
                       @*  <TreeListCommandButton Command="RefreshCosts" OnClick="@RefreshCosts">Refresh Costs</TreeListCommandButton>
                        <TreeListCommandButton Command="RefreshSuppliers" OnClick="@RefreshSuppliers">Refresh Suppliers</TreeListCommandButton> *@
                    </TreeListToolBarTemplate>
                </TelerikTreeList>
            </div>
            <div class="col-5 card">
                 <div class="card-body">
                <h4 class="page-title mt-2">Details</h4>          
                @if (EditedRow.Estheader != null)
                {
                    <EditForm Model="@EditedRow" OnValidSubmit="@HandleValidSubmit">
                        <DataAnnotationsValidator />
                        <ValidationSummary />
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Estimate Number: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikNumericTextBox Enabled="false" @bind-Value="@EditedRow.Estheader.EstimateNumber" Width="100%"></TelerikNumericTextBox>
                                </div>
                            </div>                       
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Estimate Description: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox @bind-Value="@EditedRow.Estheader.EstimateDescPe" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Estimate Source : </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikDropDownList Data="@EstimateSources" TextField="EstsourceDesc" ValueField="EstsourceId" @bind-Value="@EditedRow.Estheader.EstimateSource" Width="100%"></TelerikDropDownList>
                                </div>
                            </div>
                        </div>
                        @* <div>
                <label class="form-label">Associated File: </label>
                <TelerikTextBox @bind-Value="@SelectedRow.ReferenceNumber" Width="200px"></TelerikTextBox>
                </div>*@
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Document Number: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox @bind-Value="@EditedRow.Estheader.ReferenceNumber" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Document Type: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikDropDownList Data="@ReferenceTypes" TextField="ReferenceType1" ValueField="ReferenceTypeId" @bind-Value="@EditedRow.Estheader.ReferenceType" Width="100%"></TelerikDropDownList>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Document Desc: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox @bind-Value="@EditedRow.Estheader.ReferenceDesc" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Selling Price: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikNumericTextBox Format="C" @bind-Value="@EditedRow.Estheader.EstimateSalesPrice" Width="100%"></TelerikNumericTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Estimator: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox @bind-Value="@EditedRow.Estheader.Estimator" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Estimate Heading 1: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox @bind-Value="@EditedRow.Estheader.Heading1" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Estimate Heading 2: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox @bind-Value="@EditedRow.Estheader.Heading2" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Estimate Heading 3: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox @bind-Value="@EditedRow.Estheader.Heading3" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Job Size: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikNumericTextBox @bind-Value="@EditedRow.Estheader.JobSize" Width="100%"></TelerikNumericTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Job Units: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox @bind-Value="@EditedRow.Estheader.JobUnit" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Update</button>
                        <TelerikButton ButtonType="@ButtonType.Button" OnClick="@CancelChanges" Class="btn btn-primary">Cancel</TelerikButton>
                    </EditForm>
                }
                else if (EditedRow.Estoption != null && EditedRow.Estactivity == null)
                {
                    <EditForm Model="@EditedRow" OnValidSubmit="@HandleValidSubmit">
                        <DataAnnotationsValidator />
                        <ValidationSummary />
                        <div class="row p-1">
                            <div class="col-lg-3">
                                <label class="form-label">Extra Number: </label>
                            </div>
                            <div class="col-lg-9">
                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.OptionNumber" Width="100%"></TelerikTextBox>
                            </div>
                        </div>
                        <div class="row p-1">
                            <div class="col-lg-3">
                                <label class="form-label">Extra Description: </label>
                            </div>
                            <div class="col-lg-9">
                                <TelerikTextBox @bind-Value="@EditedRow.Estoption.OptionDesc" Width="100%"></TelerikTextBox>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Extra Qty: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikNumericTextBox @bind-Value="@EditedRow.Estoption.OptionQty" Width="100%"></TelerikNumericTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Extra Selling Price: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikNumericTextBox Format="C" @bind-Value="@EditedRow.Estoption.OptionSalesPrice" Width="100%"></TelerikNumericTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Extra Total Cost: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.Estoption.TotalCost" Width="100%"></TelerikNumericTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Extra Sales Notes: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox @bind-Value="@EditedRow.Estoption.OptionNotes" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Extra Sales Selections: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextArea @bind-Value="@EditedRow.Estoption.OptionSelections" Width="100%"></TelerikTextArea>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Extra Master Notes: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.OptionLongDesc" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Extra Area Notes: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.OptionExtendedDesc" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Extra Base House Code: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.ElevationCode" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Extra Base House Desc: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.ElevationDesc" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Extra Model Code: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.WmsplanNum" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Extra Model Desc: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.PlanName" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <br />
                        <button type="submit" class="btn btn-primary">Update</button>
                        <TelerikButton ButtonType="@ButtonType.Button" OnClick="@CancelChanges" Class="btn btn-primary">Cancel</TelerikButton>

                    </EditForm>
                }
                else if (EditedRow.Estactivity != null && EditedRow.Estdetail == null)
                {
                    <EditForm Model="@EditedRow" OnValidSubmit="@HandleValidSubmit">
                        <DataAnnotationsValidator />
                        <ValidationSummary />
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Release Code: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estactivity.Releasecode" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Activity: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estactivity.BomClass" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Selected Supplier: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikDropDownList Data="@AllSuppliers"
                                    @bind-Value="@EditedRow.Estactivity.SelectedVendor"
                                                         TextField="SubName"
                                                         ValueField="SubNumber"
                                                         DefaultText="Select Supplier"
                                                         Width="100%"
                                                         PageSize="40"
                                                         Filterable="true"
                                                         ItemHeight="30"
                                                         ScrollMode="@DropDownScrollMode.Virtual">
                                        <DropDownListSettings>
                                            <DropDownListPopupSettings Height="600px"></DropDownListPopupSettings>
                                        </DropDownListSettings>
                                    </TelerikDropDownList>
                                    @*<TelerikNumericTextBox @bind-Value="@SelectedRow.SelectedVendor" Width="200px"></TelerikNumericTextBox>*@
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Sort By Location: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikCheckBox @bind-Value="@EditedRow.Estactivity.BoolUseLocation"></TelerikCheckBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Sort By WBS/Extra: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikCheckBox @bind-Value="@EditedRow.Estactivity.BoolUseWbsSort"></TelerikCheckBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Activity Total: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.Estactivity.ActivityTotal" Width="200px"></TelerikNumericTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Taxable: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikCheckBox @bind-Value="@EditedRow.Estactivity.BoolTaxable"></TelerikCheckBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Tax Group: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estactivity.TaxGroup" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Tax Total: </label>
                                </div>
                                <div class="col-lg-9">
                                    @* <TelerikNumericTextBox @bind-Value="@SelectedRow.TaxGroup" Width="200px"></TelerikNumericTextBox>*@
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Default Supplier: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikDropDownList Data="@AllSuppliers"
                                    @bind-Value="@EditedRow.Estactivity.DefaultVendor"
                                                         TextField="SubName"
                                                         ValueField="SubNumber"
                                                         DefaultText="Select Supplier"
                                                         Width="100%"
                                                         PageSize="40"
                                                         Filterable="true"
                                                         ItemHeight="30"
                                                         Enabled="false"
                                                         ScrollMode="@DropDownScrollMode.Virtual">
                                        <DropDownListSettings>
                                            <DropDownListPopupSettings Height="600px"></DropDownListPopupSettings>
                                        </DropDownListSettings>
                                    </TelerikDropDownList>
                                </div>
                            </div>
                            @* <TelerikNumericTextBox @bind-Value="@SelectedRow.DefaultVendor" Width="200px"></TelerikNumericTextBox>*@
                        </div>
                        <br />
                        <button type="submit" class="btn btn-primary">Update</button>
                        <TelerikButton ButtonType="@ButtonType.Button" OnClick="@CancelChanges" Class="btn btn-primary">Cancel</TelerikButton>
                    </EditForm>

                }
                else if (EditedRow.Estdetail != null)
                {
                    <EditForm Model="@EditedRow" OnValidSubmit="@HandleValidSubmit">
                        <DataAnnotationsValidator />
                        <ValidationSummary />
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Phase:  </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.PhaseCode" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Item Number: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.ItemNumber" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Item Description: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.ItemDesc" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Item Notes: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.ItemNotes" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Budget Unit: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.OrdrUnit" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Budget Qty: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikNumericTextBox Enabled="@EditedRow.IssueEnabled" OnChange="@ChangeUnitPriceQty" @bind-Value="@EditedRow.Estdetail.OrderQty" Width="100%"></TelerikNumericTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Budget Unit Price: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikNumericTextBox Format="C" Enabled="@EditedRow.IssueEnabled" OnChange="@ChangeUnitPriceQty" @bind-Value="@EditedRow.Estdetail.Price" Width="100%"></TelerikNumericTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Budget Amount: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.Estdetail.Amount" Width="100%"></TelerikNumericTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Lump Sum: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikCheckBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.BoolLump"></TelerikCheckBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Use Estimate: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikCheckBox Enabled="false" @bind-Value="@EditedRow.Estdetail.BoolUseEstPrice"></TelerikCheckBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label"> Errors: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.DisplayErrors" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Warnings: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.DisplayWarnings" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Taxable: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikCheckBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.BoolTaxable"></TelerikCheckBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Tax Details: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.TaxGroupType" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Estimate Number: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikNumericTextBox Enabled="false" @bind-Value="@EditedRow.EstimateNumber" Width="100%"></TelerikNumericTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Extra Number: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@SelectedRow.ExtraNumber" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Release Code: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.ReleaseCode" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Activity: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.BomClass" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Location: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.Location" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Sort WBS/Extra: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.SortWbs" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Job Cost Code: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.JcPhase" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Job Cost Category: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.JcCategory" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Tracking Variance: </label>
                                </div>
                                <div class="col-lg-9">
                                    @* <TelerikNumericTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@SelectedRow.VarianceJcCategory" Width="200px"></TelerikNumericTextBox>*@
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Variance JC Category: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.VarianceJcCategory" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Variance Amount: </label>
                                </div>
                                <div class="col-lg-9">
                                    @* <TelerikNumericTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@SelectedRow." Width="200px"></TelerikNumericTextBox>*@
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Original Amount: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.TaxGroupType" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">PO Number: </label>
                                </div>
                                <div class="col-lg-9">
                                    @*<TelerikNumericTextBox Enabled="false" @bind-Value="@SelectedRow.po" Width="200px"></TelerikNumericTextBox>*@
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">PO Status: </label>
                                </div>
                                <div class="col-lg-9">
                                    <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.PoExported" Width="100%"></TelerikTextBox>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">VPO Number: </label>
                                </div>
                                <div class="col-lg-9">
                                    @* <TelerikNumericTextBox Enabled="false" @bind-Value="@SelectedRow." Width="200px"></TelerikNumericTextBox>*@
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">VPO Invoice Number: </label>
                                </div>
                                <div class="col-lg-9">
                                    @* <TelerikNumericTextBox Enabled="false" @bind-Value="@SelectedRow." Width="200px"></TelerikNumericTextBox>*@
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Include in Base Option: </label>
                                </div>
                                <div class="col-lg-9">
                                    @* <TelerikNumericTextBox Enabled="false" @bind-Value="@SelectedRow." Width="200px"></TelerikNumericTextBox>*@
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Include In Base Option Desc: </label>
                                </div>
                                <div class="col-lg-9">
                                    @* <TelerikNumericTextBox Enabled="false" @bind-Value="@SelectedRow." Width="200px"></TelerikNumericTextBox>*@
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row p-1">
                                <div class="col-lg-3">
                                    <label class="form-label">Price Reservation Data: </label>
                                </div>
                                <div class="col-lg-9">
                                    @* <TelerikNumericTextBox Enabled="false" @bind-Value="@SelectedRow." Width="200px"></TelerikNumericTextBox>*@
                                </div>
                            </div>
                        </div>
                        <br />
                        <button type="submit" class="btn btn-primary">Update</button>
                        <TelerikButton ButtonType="@ButtonType.Button" OnClick="@CancelChanges" Class="btn btn-primary">Cancel</TelerikButton>
                    </EditForm>
                }
            </div>
                </div>
            </div>
        </div>        

    
}
<ERP.Web.Components.AddItemToBudget SelectedEstOptionId="@SelectedEstOptionId" @ref="AddItemToBudgetModal" HandleAddSubmit="HandleValidAddItemsSubmit"></ERP.Web.Components.AddItemToBudget>

@code {

    public CombinedPOBudgetTreeModel? EditedRow { get; set; } = new CombinedPOBudgetTreeModel() { Estoption = new EstoptionDto() };
    public CombinedPOBudgetTreeModel? SelectedRow { get; set; } = new CombinedPOBudgetTreeModel(){Estoption = new EstoptionDto()};
    private List<EstheaderDto>? EstHeaders { get; set; }
    public EstcustoptionDto EstCustOption { get; set; } = new EstcustoptionDto();
    private ObservableCollection<CombinedPOBudgetTreeModel>? BudgetData { get; set; }
    private TelerikTreeList<CombinedPOBudgetTreeModel>? BudgetTreeList { get; set; }
    public List<SupplierDto>? AllSuppliers {get; set; }
    public List<CombinedPOBudgetTreeModel>? SelectedItemsToIssue { get; set; } = new List<CombinedPOBudgetTreeModel>();
    public ERP.Web.Components.AddItemToBudget? AddItemToBudgetModal { get; set; }
    private List<ReferenceTypeDto>? ReferenceTypes { get; set; }
    private List<EstimateSourceDto>? EstimateSources { get; set; }
    public int SelectedEstOptionId { get; set; }
    [Parameter]
    public int CustEstId { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        EstHeaders = (await BudgetService.GetCustomEstimateDetailsAsync(CustEstId)).Value;
        EstCustOption = (await BudgetService.GetCustomEstimateAsync(CustEstId)).Value;
        BudgetData = new ObservableCollection<CombinedPOBudgetTreeModel>(EstHeaders.Select(x => new CombinedPOBudgetTreeModel()
            {
                HasChildren = true,
                Id = Guid.NewGuid(),
                JobNumber = x.JobNumber,
                Estheader = x,
                EstimateNumber = x.EstimateNumber,
                IsIssued = x.IsIssued ?? false,
                IssueEnabled = x.IsIssued != true
            }).ToList());

    }

    protected override async Task OnInitializedAsync()
    {
        //TODO: too slow to load - also will fail if errors
        var getSuppliersTask = POService.GetSuppliersAsync();
        var getReferenceTypesTask = BudgetService.GetReferenceTypesAsync();
        var getEstimateSourcesTask = BudgetService.GetEstimateSourcesAsync();
        await Task.WhenAll(getSuppliersTask, getReferenceTypesTask, getEstimateSourcesTask);      
        AllSuppliers = getSuppliersTask.Result.Value;
        ReferenceTypes = getReferenceTypesTask.Result.Value;
        EstimateSources = getEstimateSourcesTask.Result.Value;
    }    

    async Task OnExpand(TreeListExpandEventArgs args)
    {
        var item = args.Item as CombinedPOBudgetTreeModel;
        if (item.HasChildren && item.ParentId == null && item.Estheader != null && !BudgetData.Any(x => x.ParentId == item.Id))
        {
            var estOptions = await BudgetService.GetOptionsForHeaderAsync((int)item.Estheader.EstheaderId);
            var addOptionData = estOptions.Value.Select(x => new CombinedPOBudgetTreeModel()
                {
                    Id = Guid.NewGuid(),
                    ParentId = item.Id,
                    Estoption = x,
                    IsIssued = (bool)x.IsIssued,
                    IssueEnabled = x.IsIssued != true,
                    TotalCost = x.TotalCost,
                    HasChildren = true
                }).ToList();
            BudgetData.AddRange(addOptionData);
        }
        else if (item.HasChildren && item.ParentId != null && item.Estoption != null && item.Estactivity == null && !BudgetData.Any(x => x.ParentId == item.Id))
        {
            var estActivities = await BudgetService.GetActivitiesForOptionAsync((int)item.Estoption.EstoptionId);
            var addActivityData = estActivities.Value.Select(x => new CombinedPOBudgetTreeModel()
                {
                    Id = Guid.NewGuid(),
                    ParentId = item.Id,
                    Estactivity = x,
                    Estoption = item.Estoption,
                    BomClass = x.BomClass,
                    ActivityTotal = x.ActivityTotal,
                    ReleaseCode = x.Releasecode,
                    // Taxable = x.Taxable,
                    // BoolTaxable = x.BoolTaxable ?? false,
                    // BoolUseWbsSort = x.BoolUseWbsSort ?? false,
                    // UseWbsSort = x.UseWbsSort,
                    // UseLocation = x.UseLocation,
                    // BoolUseLocation = x.BoolUseLocation ?? false, 
                    IsIssued = (bool)x.IsIssued,
                    IssueEnabled = x.IsIssued != true,
                    HasChildren = true
                }).ToList();
            BudgetData.AddRange(addActivityData);
        }
        else if (item.HasChildren && item.ParentId != null && item.Estactivity != null && !BudgetData.Any(x => x.ParentId == item.Id))
        {
            var estDetails = await BudgetService.GetDetailsForActivitiesAsync((int)item.Estoption.EstoptionId, (int)item.Estactivity.EstactivityId);
            var addDetailData = estDetails.Value.Select(x => new CombinedPOBudgetTreeModel()
                {
                    Id = Guid.NewGuid(),
                    ParentId = item.Id,
                    Estdetail = x,
                    Estactivity = item.Estactivity,
                    Estoption = item.Estoption,
                    // ItemNotes = x.MasterItems?.ItemNotes,
                    // BoolLump = x.BoolLump ?? false,
                    // UseEstPrice = x.UseEstPrice,
                    // BoolUseEstPrice = x.BoolUseEstPrice ?? false,
                    // BoolTaxable = x.BoolTaxable ?? false,
                    IsIssued = x.IsIssued ?? false,
                    IssueEnabled = x.IsIssued != true,
                    HasChildren = false
                }).ToList();
            BudgetData.AddRange(addDetailData);
        }
    }

    protected async Task RowSelectedHandler(TreeListRowClickEventArgs args)
    {
        EditedRow = args.Item as CombinedPOBudgetTreeModel;
        if(EditedRow.Estoption != null && EditedRow.Estheader == null)
        {
            EditedRow.TotalCost = (await BudgetService.GetOptionsTotalCostAsync((int)EditedRow.Estoption.EstoptionId)).Value;//it was too slow to get the totals in the first place
        }    
    }
    protected async Task CopyItem(TreeListCommandEventArgs args)
    {
        var itemToCopy = args.Item as CombinedPOBudgetTreeModel;
        if(itemToCopy.Estdetail != null)
        {
            var estDetailToCopy = new EstdetailDto()
                {
                    EstdetailId = (int)itemToCopy.Estdetail.EstdetailId
                };
            var returnItem = await BudgetService.CopyItemAsync(estDetailToCopy);
            var returnItemToAdd = new CombinedPOBudgetTreeModel()
                {
                    Id = Guid.NewGuid(),
                    ParentId = itemToCopy.ParentId,
                    Estdetail = returnItem.Value,
                    Estactivity = itemToCopy.Estactivity,
                    Estoption = itemToCopy.Estoption,
                    // EstdetailId = returnItem.EstdetailId,
                    // ItemNumber = returnItem.ItemNumber,
                    // ItemNotes = returnItem.MasterItems?.ItemNotes,
                    // BoolLump = returnItem.BoolLump ?? false,
                    // UseEstPrice = returnItem.UseEstPrice,
                    // BoolUseEstPrice = returnItem.BoolUseEstPrice ?? false,
                    // BoolTaxable = returnItem.BoolTaxable ?? false,
                    IsIssued = false,
                    IssueEnabled = true,
                    HasChildren = false
                };
            BudgetData.Add(returnItemToAdd);
        }      
    }    
    void ChangeHandler(object value)
    {
        //checking parent should check the children
        //TODO: unchecking parent uncheck all children??
        if(SelectedRow.Estactivity != null && SelectedRow.Estdetail == null )
        {
            var findChildren = BudgetData.Where(x => x.ParentId == SelectedRow.Id);
            foreach(var child in findChildren.Where(x => x.IssueEnabled != false))
            {
                if (SelectedRow.IsIssued == true)
                {
                    SelectedItemsToIssue.Add(child);//add children if not already issued
                }
                else
                {
                    SelectedItemsToIssue.Remove(child);               
                }
                child.IsIssued = SelectedRow.IsIssued;
            }
        }
        if (SelectedRow.Estoption != null && SelectedRow.Estactivity == null )
        {
            var findChildren = BudgetData.Where(x => x.ParentId == SelectedRow.Id);
            foreach (var child in findChildren.Where(x => x.IssueEnabled != false))
            {
                var findGrandChilds = BudgetData.Where(x => x.ParentId == child.Id);
                foreach (var grandchild in findGrandChilds.Where(x => x.IssueEnabled != false))
                {
                    if(SelectedRow.IsIssued == true)
                    {
                        SelectedItemsToIssue.Add(grandchild);//add children if not already issued}
                    }
                    else
                    {
                        SelectedItemsToIssue.Remove(grandchild);//add children if not already issued}
                    }
                    grandchild.IsIssued = SelectedRow.IsIssued;
                }
                child.IsIssued = SelectedRow.IsIssued;
            }
        }
        if (SelectedRow.Estheader != null && SelectedRow.Estoption == null )
        {
            var findChildren = BudgetData.Where(x => x.ParentId == SelectedRow.Id);
            foreach (var child in findChildren.Where(x => x.IssueEnabled != false))
            {
                var findGrandChilds = BudgetData.Where(x => x.ParentId == child.Id);
                foreach (var grandchild in findGrandChilds.Where(x => x.IssueEnabled != false))
                {
                    var findGreatGrandChildren = BudgetData.Where(x => x.ParentId == grandchild.Id);
                    foreach (var greatgrandchild in findGreatGrandChildren.Where(x => x.IssueEnabled != false))
                    {
                        if (SelectedRow.IsIssued == true)
                        {
                            SelectedItemsToIssue.Add(greatgrandchild);//add children if not already issued}
                        }
                        else
                        {
                            SelectedItemsToIssue.Remove(greatgrandchild);//add children if not already issued}
                        }
                        greatgrandchild.IsIssued = SelectedRow.IsIssued;
                    }

                    grandchild.IsIssued = SelectedRow.IsIssued;
                }
                child.IsIssued = SelectedRow.IsIssued;
            }
        }
        //TODO: checking child should affect parent state as checked or indeterminate
        SelectedRow.IsIssued = (bool)value;
        if (SelectedRow.IsIssued == true && SelectedRow.Estdetail != null)
        {
            SelectedItemsToIssue.Add(SelectedRow);
        }
        else
        {
            SelectedItemsToIssue.Remove(SelectedRow);
        }
        var test = SelectedItemsToIssue;
    }
    void ChangeUnitPriceQty(object value)
    {       
        EditedRow.Estdetail.Amount = EditedRow.Estdetail.Price * EditedRow.Estdetail.OrderQty;
        EditedRow.Estdetail.BoolLump = true;
        EditedRow.Estdetail.Lump = "T";
        EditedRow.Estdetail.DisplayWarnings = string.IsNullOrWhiteSpace(EditedRow.Estdetail.DisplayWarnings) ? "Using Lump Sum" : EditedRow.Estdetail.DisplayWarnings.Contains("Using Lump Sum") ? EditedRow.Estdetail.DisplayWarnings : EditedRow.Estdetail.DisplayWarnings + ", Using Lump Sum";
        EditedRow.Estdetail.Warnings = !String.IsNullOrWhiteSpace(EditedRow.Estdetail.Warnings) ? EditedRow.Estdetail.Warnings += "|5" : "5";
    }

    protected async Task DeleteHandler(TreeListCommandEventArgs args)
    {
        //TODO: remove option?? 
        var itemToDelete = args.Item as CombinedPOBudgetTreeModel;
        if (itemToDelete.Estdetail != null)
        {
            await BudgetService.DeleteItemAsync(itemToDelete);
        }
        else if (itemToDelete.Estactivity != null)
        {
            await BudgetService.DeleteActivityAsync(itemToDelete);           
        }
        BudgetData.Remove(itemToDelete);
    }


    private async Task HandleValidSubmit()
    {

        if (EditedRow.Estheader != null)
        {
            var estHeaderToUpdate = new EstheaderDto()
                {
                    EstheaderId = (int)EditedRow.Estheader.EstheaderId,
                    JobNumber = EditedRow.Estheader.JobNumber,
                    EstimateDescPe = EditedRow.Estheader.EstimateDescPe,
                    EstimateSalesPrice = EditedRow.Estheader.EstimateSalesPrice,
                    Estimator = EditedRow.Estheader.Estimator,
                    ReferenceNumber = EditedRow.Estheader.ReferenceNumber,
                    ReferenceDesc = EditedRow.Estheader.ReferenceDesc,
                    ReferenceType = EditedRow.Estheader.ReferenceType,//TODO: seems to be only editable if it s not original estimate
                    Heading1 = EditedRow.Estheader.Heading1,
                    Heading2 = EditedRow.Estheader.Heading2,
                    Heading3 = EditedRow.Estheader.Heading3,
                    JobSize = EditedRow.Estheader.JobSize,
                    JobUnit = EditedRow.Estheader.JobUnit,
                };
            await BudgetService.UpdateEstHeaderAsync(estHeaderToUpdate);
            //TODO: return success/fail
        }
        else if(EditedRow.Estoption != null && EditedRow.Estactivity == null)
        {
            var estOptionToUpdate = new EstoptionDto()
                {
                    EstoptionId = (int)EditedRow.Estoption.EstoptionId,
                    OptionDesc = EditedRow.Estoption.OptionDesc,
                    OptionSalesPrice = EditedRow.Estoption.OptionSalesPrice,
                    OptionQty = EditedRow.Estoption.OptionQty,
                    OptionNotes = EditedRow.Estoption.OptionNotes,
                    OptionSelections = EditedRow.Estoption.OptionSelections
                };
            await BudgetService.UpdateEstOptionAsync(estOptionToUpdate);
        }
        else if(EditedRow.Estactivity != null && EditedRow.Estdetail == null)
        {
            //TODO: selected supplier can change, this triggers some extra popup about pulling in new costs
            var estActivityToUpdate = new EstactivityDto()
                {
                    EstactivityId = (int)EditedRow.Estactivity.EstactivityId,
                    UseLocation = EditedRow.Estactivity.BoolUseLocation == true ? "T" : "F",
                    UseWbsSort = EditedRow.Estactivity.BoolUseWbsSort == true ? "T" : "F",
                    Taxable = EditedRow.Estactivity.BoolTaxable == true ? "T" : "F"
                };
            await BudgetService.UpdateEstActivityAsync(estActivityToUpdate);
        }
        else if (EditedRow.Estdetail != null)
        {
            var estDetailToUpdate = new EstdetailDto()
                {
                    EstdetailId = (int)EditedRow.Estdetail.EstdetailId,
                    ItemDesc = EditedRow.Estdetail.ItemDesc,
                    ItemNotes = EditedRow.Estdetail.ItemNotes,
                    OrdrUnit = EditedRow.Estdetail.OrdrUnit,
                    OrderQty = EditedRow.Estdetail.OrderQty,
                    Price = EditedRow.Estdetail.Price,
                    Amount = EditedRow.Estdetail.Amount,
                    Lump = EditedRow.Estdetail.BoolLump == true ? "T" : "F",
                    UseEstPrice = EditedRow.Estdetail.UseEstPrice,
                    Taxable = EditedRow.Estdetail.BoolTaxable == true ? "T" : "F"
                };
            await BudgetService.UpdateEstDetailAsync(estDetailToUpdate);
        }
    }
    private async Task CancelChanges()
    {
        //refresh the item from db
        if (EditedRow.Estheader != null)
        {
            var getHeader = await BudgetService.GetEstHeaderAsync((int)EditedRow.Estheader.EstheaderId);
            EditedRow.Estheader.EstheaderId = getHeader.Value.EstheaderId;
            EditedRow.EstimateNumber = getHeader.Value.EstimateNumber;
            EditedRow.Estheader = getHeader.Value;
        }
        else if (EditedRow.Estoption != null && EditedRow.Estactivity == null)
        {
            var getOption = await BudgetService.GetEstOptionAsync((int)EditedRow.Estoption.EstoptionId);
            EditedRow.Estoption = getOption.Value;
            EditedRow.Estoption.EstoptionId = getOption.Value.EstoptionId;
            EditedRow.IsIssued = (bool)getOption.Value.IsIssued;
            EditedRow.IssueEnabled = getOption.Value.IsIssued != true;
            EditedRow.TotalCost = getOption.Value.TotalCost;
        }
        else if (EditedRow.Estactivity != null && EditedRow.Estdetail == null)
        {
            var getActivity = await BudgetService.GetEstActivityAsync((int)EditedRow.Estactivity.EstactivityId);
            EditedRow.BomClass = getActivity.Value.BomClass;
            EditedRow.ActivityTotal = getActivity.Value.ActivityTotal;
            EditedRow.Estactivity = getActivity.Value;
            EditedRow.ReleaseCode = getActivity.Value.Releasecode;
        }
        else
        {
            var getDetail = await BudgetService.GetEstDetailAsync((int)EditedRow.Estdetail.EstdetailId);
            EditedRow.Estdetail = getDetail.Value;
            EditedRow.Estdetail.Warnings = getDetail.Value.Lump == "T" ? "Warning: Using Lump Sum" : null;
           // EditedRow.Estdetail.BoolUseEstPrice = getDetail.BoolUseEstPrice ?? false;
          //  EditedRow.Estdetail.BoolTaxable = getDetail.BoolTaxable ?? false;
        }
    }
    
    async Task RefreshSuppliers(TreeListCommandEventArgs args)
    {

    }
    async Task RefreshCosts(TreeListCommandEventArgs args)
    {

    }    
    private async Task ShowAddItemToBudget(TreeListCommandEventArgs args)
    {
        var option = args.Item as CombinedPOBudgetTreeModel;
        if (option != null && option.Estoption != null)
        {
            SelectedEstOptionId = (int)option.Estoption.EstoptionId;
            await AddItemToBudgetModal.Show();
        }
        AddItemToBudgetModal.Show();
        StateHasChanged();
    }
    private async void HandleValidAddItemsSubmit(ResponseModel<List<EstdetailDto>> responseItem)
    {
        //TODO: show added data
        AddItemToBudgetModal.Hide();
        StateHasChanged();
    }
}


