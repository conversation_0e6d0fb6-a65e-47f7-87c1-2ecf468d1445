﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class MasterPlan
{
    public int MasterPlanId { get; set; }

    public int? SsmasterPlanId { get; set; }

    public int? WmsmasterPlanId { get; set; }

    public string PlanNum { get; set; } = null!;

    public string? PlanName { get; set; }

    public string? PlanModelNum { get; set; }

    public string? WmsplanNum { get; set; }

    public string? BaseHousePlan { get; set; }

    public string? ElevationCode { get; set; }

    public string? ElevationNotes { get; set; }

    public int PlanTypeId { get; set; }

    public string? Description { get; set; }

    public decimal? DefaultPrice { get; set; }

    public double? DefaultMarginPct { get; set; }

    public decimal? Cost { get; set; }

    public decimal? ConstructionDays { get; set; }

    public int? MinLotSize { get; set; }

    public int? SquareFeet { get; set; }

    public short? Bedrooms { get; set; }

    public decimal? Bathrooms { get; set; }

    public decimal? Stories { get; set; }

    public int? PlanSize { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStampe { get; set; } = null!;

    public string? ItemGroupCode { get; set; }

    public int? MasterItemGroupId { get; set; }

    public string? GroupPhaseCode { get; set; }

    public string? GroupPhaseDesc { get; set; }

    public string? GroupPhaseNotes { get; set; }

    public string? DeletedFromPe { get; set; }

    public int? AsmGroup { get; set; }

    public bool? HasGarage { get; set; }

    public bool? IsGarageReversable { get; set; }

    public string? ImangeCode { get; set; }

    public decimal? PlanWidth { get; set; }

    public decimal? PlanDepth { get; set; }

    public string? GarageBays { get; set; }

    public int? Bedroom { get; set; }

    public int? FullBath { get; set; }

    public int? HalfBath { get; set; }

    public decimal? FinishedBasement { get; set; }

    public decimal? Deck { get; set; }

    public string? PlanType { get; set; }

    public virtual ICollection<AsmHeader> AsmHeaders { get; set; } = new List<AsmHeader>();

    public virtual ICollection<PhasePlan> PhasePlans { get; set; } = new List<PhasePlan>();

    public virtual PlanType PlanTypeNavigation { get; set; } = null!;
}
