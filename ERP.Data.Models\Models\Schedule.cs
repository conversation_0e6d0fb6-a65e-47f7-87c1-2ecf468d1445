﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Schedule
{
    public int ScheduleId { get; set; }

    public int? TemplateId { get; set; }

    public string? JobNumber { get; set; }

    public string? UserCreated { get; set; }

    public DateTime? DateCreated { get; set; }

    public string? UserModified { get; set; }

    public DateTime? DateModified { get; set; }

    public DateTime? DateToStart { get; set; }

    public DateTime? DateToEnd { get; set; }

    public DateTime? BaseStartDate { get; set; }

    public DateTime? BaseEndDate { get; set; }

    public DateTime? ProjStartDate { get; set; }

    public DateTime? ProjEndDate { get; set; }

    public DateTime? ActualStartDate { get; set; }

    public DateTime? ActualEndDate { get; set; }

    public int? PlusminusDays { get; set; }

    public int? BaseDuration { get; set; }

    public int? ProjDuration { get; set; }

    public int? BaseCalduration { get; set; }

    public int? ProjCalduration { get; set; }

    public string? Published { get; set; }

    public int? ActualDuration { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? LockBaseDates { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public string? StickBuildingNum { get; set; }

    public string? IniSchCreatedby { get; set; }

    public DateTime? IniSchCreateddate { get; set; }

    public string? IniSchModifiedby { get; set; }

    public DateTime? IniSchModifieddate { get; set; }

    public DateTime? IniSchStartDate { get; set; }

    public DateTime? IniSchEndDate { get; set; }

    public DateTime? IniSchBaseStartDate { get; set; }

    public DateTime? IniSchBaseEndDate { get; set; }

    public int? IniSchDuration { get; set; }

    public int? IniSchLagTime { get; set; }

    public bool? IniSchApproved { get; set; }

    public string? IniSchApprovedby { get; set; }

    public bool? LockPreSch { get; set; }

    public DateTime? IniSchApproveDate { get; set; }

    public DateTime? PermitSubmitDate { get; set; }

    public DateTime? PermitReceivedDate { get; set; }

    public string? PermitNumber { get; set; }

    public virtual Job? JobNumberNavigation { get; set; }

    public virtual ICollection<ScheduleMasterSchedule> ScheduleMasterSchedules { get; set; } = new List<ScheduleMasterSchedule>();

    public virtual ICollection<ScheduleMilestone> ScheduleMilestones { get; set; } = new List<ScheduleMilestone>();

    public virtual ICollection<ScheduleSactivity> ScheduleSactivities { get; set; } = new List<ScheduleSactivity>();

    public virtual Template? Template { get; set; }
}
