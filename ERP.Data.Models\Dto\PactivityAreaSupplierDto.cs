﻿using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class PactivityAreaSupplierDto : IMapFrom<PactivityAreaSupplier>
{
    public int PactivityId { get; set; }

    public int SubdivisionId { get; set; }

    public int? SubNumber { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

   // public virtual Pactivity Pactivity { get; set; } = null!;

    public virtual SupplierDto? SubNumberNavigation { get; set; }

    public SubdivisionDto? Subdivision { get; set; }
}
