﻿
using AutoMapper;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Data;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class SalesPriceController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        public SalesPriceController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
        }

        [HttpGet("{worksheetId}")]
        public async Task<IActionResult> GetAllWorksheetDataAsync(int worksheetId)
        {
            try
            {
                var worksheetData = new List<WorksheetTreeModel>();
                var gePlanActData = (from a in _context.WorksheetPlans.Include("PhasePlan.Subdivision").Include("PhasePlan.MasterPlan").Where(x => x.WorksheetId == worksheetId && x.IsActive == true)
                                     join b in _context.WorksheetPlanActs.Include("SubNumberNavigation").Where(x => x.IsActive == true) on a.WorksheetPlanId equals b.WorksheetPlanId  //TODO: this will end up not showing options with no activie,
                                     from d in _context.WorksheetLogs.Where(x => x.IsActive == true && x.LogType == "B" && x.PlanOptId == b.WorksheetPlanId && x.WorksheetId == a.WorksheetId && x.Activity == b.Activity).DefaultIfEmpty()
                                     select new
                                     {
                                         SubdivisionId = a.PhasePlan.SubdivisionId,
                                         SubdivisionName = a.PhasePlan.Subdivision.SubdivisionName,
                                         PlanId = a.WorksheetPlanId,                                         
                                         PlanName = a.PhasePlan.MasterPlan.PlanName,
                                         PlanNumber = a.PhasePlan.MasterPlan.PlanNum,
                                         OptionCode = "BASE HOUSE",
                                         OptionName = "BASE HOUSE",
                                         OptionId = a.PhasePlanId,
                                         WorksheetOpt = new WorksheetOpt(),
                                         WorksheetPlan = a,
                                         WorksheetOptAct = new WorksheetOptAct(),
                                         WorksheetPlanAct = b,
                                         ActivityId = b.WorksheetPlanActId,
                                         ActivityName = b.Activity,
                                         Cost = (decimal?)b.Costprice,
                                         ErrorCount = d == null || d.ErrorCode == null ? 0 : 1,
                                         ErrorReason = d != null ? $"{d.ErrorDesc.Trim()}:{d.ItemNumber} - {d.ItemDesc}" : null,
                                     }).ToList();
                var groupedPlan = gePlanActData.GroupBy(x => new {x.WorksheetPlan, x.WorksheetPlanAct }).GroupBy(x => new { x.Key.WorksheetPlan }).ToList();

                var getData = (from a in _context.WorksheetPlans.Include("PhasePlan.Subdivision").Include("PhasePlan.MasterPlan").Where(x => x.WorksheetId == worksheetId && x.IsActive == true)
                               join b in _context.WorksheetOpts.Include("PlanOption").Where(x => x.IsActive == true) on a.WorksheetPlanId equals b.WorksheetPlanId
                               join c in _context.WorksheetOptActs.Include("SubNumberNavigation").Where(x => x.IsActive == true) on b.WorksheetOptId equals c.WorksheetOptId  //TODO: this will end up not showing options with no activie,
                               from d in _context.WorksheetLogs.Where(x => x.IsActive == true && x.LogType == "O" && x.PlanOptId == b.WorksheetOptId && x.WorksheetId == a.WorksheetId && x.Activity == c.Activity).DefaultIfEmpty()
                               select new
                               {
                                   SubdivisionId = a.PhasePlan.SubdivisionId,
                                   SubdivisionName = a.PhasePlan.Subdivision.SubdivisionName,
                                   PlanId = a.WorksheetPlanId,
                                   PlanName = a.PhasePlan.MasterPlan.PlanName,
                                   PlanNumber = a.PhasePlan.MasterPlan.PlanNum,
                                   OptionCode = $"{a.PhasePlan.MasterPlan.PlanNum}{b.PlanOption.OptionCode}",
                                   OptionName = b.PlanOption.ModifiedOptionDesc,
                                   OptionId = b.WorksheetOptId,
                                   WorksheetOpt = b,
                                   WorksheetPlan = a,
                                   WorksheetOptAct = c,
                                   WorksheetPlanAct = new WorksheetPlanAct(),
                                   ActivityId = c.WorksheetOptActId,
                                   ActivityName = c.Activity,
                                   Cost = (decimal?)c.Costprice,
                                   ErrorCount = d == null || d.ErrorCode == null ? 0 : 1,
                                   ErrorReason = d != null ? $"{d.ErrorDesc.Trim()}:{d.ItemNumber} - {d.ItemDesc}" : null,
                                   //Vendor = c.SubNumberNavigation.SubName                             
                               }).ToList();
                var grouped = getData.GroupBy(x => new { x.WorksheetOptAct, x.WorksheetOpt, x.WorksheetPlan }).ToList().GroupBy(x => new { x.Key.WorksheetOpt, x.Key.WorksheetPlan}).GroupBy(y => new { y.Key.WorksheetPlan }).ToList();
                var treedata = grouped.Select(x => new WorksheetTreeModel()
                {
                    Id = Guid.NewGuid(),
                    HasChildren = true,
                    SubdivisionId = x.Key.WorksheetPlan.PhasePlan.SubdivisionId,
                    SubdivisionName = x.Key.WorksheetPlan.PhasePlan.Subdivision.SubdivisionName,
                    PlanName = x.Key.WorksheetPlan.PhasePlan.MasterPlan.PlanName,
                    PlanNumber = x.Key.WorksheetPlan.PhasePlan.MasterPlan.PlanNum,
                    WorksheetPlanId = x.Key.WorksheetPlan.WorksheetPlanId,
                    Cost = (decimal?)x.Key.WorksheetPlan.Costprice,
                    SellPrice = (decimal?)x.Key.WorksheetPlan.Sellprice,
                    IsActive = true,
                    ErrorCount = x.Sum(y => y.Sum(z => z.Sum(w => w.ErrorCount))),
                    Children = x.Select(y => new WorksheetTreeModel()
                    {
                        Id = Guid.NewGuid(),
                        HasChildren = true,
                        WorksheetPlanId = y.Key.WorksheetPlan.WorksheetPlanId,
                        WorksheetOptionId = y.Key.WorksheetOpt.WorksheetOptId,
                        OptionCode = $"{y.Key.WorksheetPlan.PhasePlan.MasterPlan.PlanNum}{y.Key.WorksheetOpt.PlanOption.OptionCode}",
                        OptionName = y.Key.WorksheetOpt.PlanOption.ModifiedOptionDesc,
                        IsBaseHouse = false,
                        Cost = (decimal?)y.Sum(z => z.First().WorksheetOptAct.Costprice),//First here because there could be multiple of same activity in a gouping if there are multiple errors in the activity
                        Markup = (decimal?)y.Key.WorksheetOpt.Markup,
                        Margin = y.Key.WorksheetOpt.Sellprice - y.Sum(z => z.First().WorksheetOptAct.Costprice),
                        MarkupPercent = y.Key.WorksheetOpt.Markuppercent,
                        MarginPercent = y.Key.WorksheetOpt.Sellprice != null && y.Key.WorksheetOpt.Sellprice != 0 ? (y.Key.WorksheetOpt.Sellprice - y.Sum(z => z.First().WorksheetOptAct.Costprice)) / y.Key.WorksheetOpt.Sellprice : 0,
                        MarkupType = y.Key.WorksheetOpt.Markuptype,
                        MarketValue = (decimal?)y.Key.WorksheetOpt.Marketvalue,
                        SellPrice = (decimal?)y.Key.WorksheetOpt.Sellprice,
                        PriceDate = y.Key.WorksheetOpt.Pricedate,
                        IsActive = true,
                        ErrorCount = y.Sum(z => z.Sum(w => w.ErrorCount)),
                        //TODO: if there's multiple error logs in an activity, there will be two activity. needs to have another layer of grouping
                        Children = y.Select(z => new WorksheetTreeModel()
                        {
                            Id = Guid.NewGuid(),
                            ActivityName = z.First().ActivityName,
                            Vendor = z.First().WorksheetOptAct.SubNumberNavigation?.SubName,
                            WorksheetActivityId = z.First().ActivityId,
                            LumpSum = z.First().WorksheetOptAct.Lumpsum == "T",
                            SubNumber = z.First().WorksheetOptAct.SubNumber,
                            //ErrorCount = z.WorksheetOptAct.Errors,
                            ErrorCount = z.Sum(w => w.ErrorCount),
                            ErrorReason = string.Join(", ", z.Where(w => w.ErrorCount != 0).Select(v => v.ErrorReason)),
                            //ErrorReason = string.Join(", ", y.Where(y => y.ErrorCount != 0).Select(y => y.ErrorReason)),
                            Cost = (decimal?)z.First().WorksheetOptAct.Costprice,
                            HasChildren = false,
                            IsActive = true
                        }).OrderBy(x => x.ActivityName).ToList()
                    }).OrderBy(x => x.OptionCode).ToList(),
                }).OrderBy(x => x.PlanNumber).ToList();

                foreach (var plan in groupedPlan)
                {
                    //if plan in tree, add children
                    var findPlanInTree = treedata.SingleOrDefault(x => x.WorksheetPlanId == plan.Key.WorksheetPlan.WorksheetPlanId);
                    if (findPlanInTree != null)
                    {
                        var children = plan.Select(y => new WorksheetTreeModel()
                        {
                            Id = Guid.NewGuid(),
                            HasChildren = false,
                            WorksheetActivityId = y.First().WorksheetPlanAct.WorksheetPlanActId,
                            LumpSum = y.First().WorksheetPlanAct.Lumpsum == "T",
                            ActivityName = y.First().WorksheetPlanAct.Activity,
                            Vendor = y.First().WorksheetPlanAct.SubNumberNavigation?.SubName,
                            SubNumber = y.First().WorksheetPlanAct.SubNumber,
                            Cost = (decimal?)y.First().WorksheetPlanAct.Costprice,
                            ErrorCount = y.Sum(z => z.ErrorCount),
                            ErrorReason = string.Join(", ", y.Where(w => w.ErrorCount != 0).Select(v => v.ErrorReason)),
                            IsActive = true
                        }).OrderBy(x => x.ActivityName).ToList();
                        findPlanInTree.Children.Insert(0,new WorksheetTreeModel()
                        {
                            Id = Guid.NewGuid(),
                            OptionCode = $"{plan.Key.WorksheetPlan.PhasePlan.MasterPlan.PlanNum}A0000000",
                            OptionName = "BASE HOUSE",
                            WorksheetOptionId = 1,
                            WorksheetPlanId = plan.Key.WorksheetPlan.WorksheetPlanId,
                            IsBaseHouse= true,
                            Markup = (decimal?)plan.Key.WorksheetPlan.Markup,
                            Margin = plan.Key.WorksheetPlan.Sellprice - (double?)children.Sum(z => z.Cost),
                            MarkupPercent = plan.Key.WorksheetPlan.Markuppercent,
                            MarginPercent = plan.Key.WorksheetPlan.Sellprice != null && plan.Key.WorksheetPlan.Sellprice != 0 ? (plan.Key.WorksheetPlan.Sellprice - (double?)children.Sum(z => z.Cost)) / plan.Key.WorksheetPlan.Sellprice : 0,
                            MarkupType = plan.Key.WorksheetPlan.Markuptype,
                            MarketValue = (decimal?)plan.Key.WorksheetPlan.Marketvalue,
                            SellPrice = (decimal?)plan.Key.WorksheetPlan.Sellprice,
                            PriceDate = plan.Key.WorksheetPlan.Pricedate,
                            Cost = children.Sum(x => x.Cost),
                            ErrorCount = children.Sum(x => x.ErrorCount),
                            HasChildren = true,
                            Children = children,
                            IsActive = true
                        });
                    }
                    //if plan not in tree, add plan with children
                    else
                    {
                        treedata.Add(new WorksheetTreeModel()
                        {
                            Id = Guid.NewGuid(),
                            SubdivisionId = plan.Key.WorksheetPlan.PhasePlan.SubdivisionId,
                            SubdivisionName = plan.Key.WorksheetPlan.PhasePlan.Subdivision.SubdivisionName,
                            PlanName = plan.Key.WorksheetPlan.PhasePlan.MasterPlan.PlanName,
                            PlanNumber = plan.Key.WorksheetPlan.PhasePlan.MasterPlan.PlanNum,
                            WorksheetPlanId = plan.Key.WorksheetPlan.WorksheetPlanId,
                            IsActive = true,
                            ErrorCount = plan.Sum(x => x.Sum(y => y.ErrorCount)),
                            Children = new List<WorksheetTreeModel>()
                        {
                            new WorksheetTreeModel()
                            {
                                Id = Guid.NewGuid(),
                                OptionCode = $"{plan.Key.WorksheetPlan.PhasePlan.MasterPlan.PlanNum}A0000000",
                                OptionName = "BASE HOUSE",
                                WorksheetOptionId = 1,
                                WorksheetPlanId = plan.Key.WorksheetPlan.WorksheetPlanId,
                                IsBaseHouse = true,
                                Cost = (decimal?)plan.Sum(z => z.First().Cost),
                                Markup = (decimal?)plan.Key.WorksheetPlan.Markup,
                                Margin = plan.Key.WorksheetPlan.Sellprice - (double?)plan.Sum(z => z.First().Cost),
                                MarkupPercent =plan.Key.WorksheetPlan.Markuppercent,
                                MarginPercent = plan.Key.WorksheetPlan.Sellprice != null && plan.Key.WorksheetPlan.Sellprice != 0 ?  (plan.Key.WorksheetPlan.Sellprice - (double?)plan.Sum(z => z.First().Cost)) / plan.Key.WorksheetPlan.Sellprice : 0,
                                MarkupType = plan.Key.WorksheetPlan.Markuptype,
                                MarketValue = (decimal?)plan.Key.WorksheetPlan.Marketvalue,
                                SellPrice = (decimal?)plan.Key.WorksheetPlan.Sellprice,
                                HasChildren = true,
                                IsActive = true,
                                ErrorCount = plan.Sum(x => x.Sum(y => y.ErrorCount)),                                
                                Children = plan.Select(x => new WorksheetTreeModel()
                                {
                                    Id = Guid.NewGuid(),
                                    WorksheetActivityId = x.First().WorksheetPlanAct.WorksheetPlanActId,
                                     ActivityName = x.First().WorksheetPlanAct.Activity,
                                     Vendor = x.First().WorksheetPlanAct.SubNumberNavigation?.SubName,
                                     Cost = (decimal?)x.First().WorksheetPlanAct.Costprice,
                                     ErrorCount = x.Sum(y => y.ErrorCount),
                                     ErrorReason = string.Join(", ", x.Where(w => w.ErrorCount != 0).Select(v => v.ErrorReason)),
                                     IsActive = true
                                }).OrderBy(x => x.ActivityName).ToList()
                            }
                        },
                            HasChildren = true,
                        });
                    }
                }
                foreach (var plan in treedata)
                {
                    if(plan.Children != null)
                    {
                        foreach (var option in plan.Children)
                        {
                            option.ParentId = plan.Id;
                            if (option.Children != null)
                            {
                                foreach (var activity in option.Children)
                                {
                                    activity.ParentId = option.Id;
                                    activity.PercentOfTotal = option.Cost != null && option.Cost != 0 ? ((double)(activity.Cost ?? 0)/(double)(option.Cost)) * 100 : 0;
                                }
                            }
                        }
                    }
                }
                return new OkObjectResult(new ResponseModel<List<WorksheetTreeModel>> { Value = treedata.OrderBy(x => x.SubdivisionName).ToList(), IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<WorksheetTreeModel>> { IsSuccess = false, Message = "Failed to get All Worksheet Data", Value = null });
            }
        }
        [HttpGet("{worksheetId}")]
        public async Task<IActionResult> GetWorksheetPlansAndOptionsAsync(int worksheetId)
        {
            try
            {
                var worksheetOpts = new List<WorksheetOptModel>();
                var getWorksheetPlanIds = await _context.WorksheetPlans.Where(x => x.WorksheetId == worksheetId).Select(x => x.WorksheetPlanId).ToListAsync();
                worksheetOpts = await _context.WorksheetOpts.Include("WorksheetPlan").Where(x => getWorksheetPlanIds.Contains(x.WorksheetPlanId) && x.IsActive == true).Select(x => new WorksheetOptModel()
                {
                    WorksheetOptId = x.WorksheetOptId,
                    WorksheetPlanId = x.WorksheetPlanId,
                    Costprice = x.Costprice,
                    Marketvalue = x.Marketvalue,
                    Markup = x.Markup,
                    PlanOptionId = x.PlanOptionId,
                    Markuppercent = x.Markuppercent,
                    Sellprice = x.Sellprice,
                    IsActive = x.IsActive
                }).ToListAsync();
                return new OkObjectResult(new ResponseModel<List<WorksheetOptModel>> { Value = worksheetOpts, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<WorksheetOptModel>> { IsSuccess = false, Message = "Failed to get Worksheet Plans and Options", Value = null });
            }
        }
        [HttpGet("{worksheetOptId}")]
        public async Task<IActionResult> GetWorksheetOptActsAsync(int worksheetOptId)
        {
            try
            {               
                var getOptActsQuery = (
                                from a in _context.WorksheetOptActs.Where(x => x.WorksheetOptId == worksheetOptId && x.IsActive == true)                              
                                join b in _context.WorksheetOpts.Where(x => x.IsActive == true && x.WorksheetOptId == worksheetOptId) on a.WorksheetOptId equals b.WorksheetOptId
                                join c in _context.WorksheetPlans.Where(x => x.IsActive == true) on b.WorksheetPlanId equals c.WorksheetPlanId
                                from d in _context.WorksheetLogs.Where(x => x.IsActive == true && x.LogType == "O" &&  x.PlanOptId == b.WorksheetOptId && x.WorksheetId == c.WorksheetId  && x.Activity == a.Activity).DefaultIfEmpty()
                                from e in _context.Suppliers.Where(x => x.SubNumber == a.SubNumber).DefaultIfEmpty()
                                select new WorksheetOptActModel                             
                                {                                 
                                    Activity = a.Activity,                                 
                                    SubNumber = a.SubNumber,                                 
                                    SupplierName =  e != null ? e.SubName : null,                                 
                                    Costprice = a.Costprice,                                 
                                    WorksheetOptActId =   a.WorksheetOptActId,                                 
                                    WorksheetOptId = a.WorksheetOptId,                                 
                                    Lumpsum = a.Lumpsum,                                 
                                    Warnings = a.Warnings,                                 
                                    WarningReason = d.WarningDesc,                                 
                                    WarningCount = d == null || d.WarningCode == null ? 0 : 1,                                 
                                    ErrorCount = d == null || d.ErrorCode == null ? 0 : 1,                                 
                                    ErrorCode = d.ErrorCode,                                 
                                    ErrorReason = $"{d.ErrorDesc.Trim()}:{d.ItemNumber} - {d.ItemDesc}",                                
                                    IsActive = a.IsActive
                             }).ToList();
                var groupedByActivity = getOptActsQuery.GroupBy(x => x.Activity).ToList();
                var activitiesList = groupedByActivity.Select(x => new WorksheetOptActModel()
                {
                    Activity = x.Key,
                    SubNumber = x.First().SubNumber,
                    SupplierName = x.First().SupplierName,
                    Costprice = x.First().Costprice,
                    WorksheetOptActId = x.First().WorksheetOptActId,
                    WorksheetOptId = x.First().WorksheetOptId,
                    Lumpsum = x.First().Lumpsum,
                    Warnings = x.First().Warnings,
                    WarningReason = string.Join(", ", x.Where(y => y.WarningCount !=0).Select(y => y.WarningReason)),
                    WarningCount = x.Sum(y => y.WarningCount),
                    ErrorCount = x.Sum(y => y.ErrorCount),
                    ErrorCode = x.First().ErrorCode,
                    ErrorReason = string.Join(", ", x.Where(y => y.ErrorCount != 0).Select(y => y.ErrorReason)),
                    IsActive = true
                }).ToList();

                return new OkObjectResult(new ResponseModel<List<WorksheetOptActModel>> { Value = activitiesList, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<WorksheetOptActModel>> { IsSuccess = false, Message = "Failed to get Worksheet Option Activities", Value = null });
            }
        }
        [HttpGet("{worksheetPlanId}")]
        public async Task<IActionResult> GetWorksheetPlanActAsync(int worksheetPlanId)
        {
            var worksheetOpts = new List<WorksheetOptActModel>();
            try
            {               
                var getPlanActsQuery = (
                               from a in _context.WorksheetPlanActs.Where(x => x.WorksheetPlanId == worksheetPlanId && x.IsActive == true)
                               join b in _context.WorksheetPlans.Where(x => x.IsActive == true && x.WorksheetPlanId == worksheetPlanId) on a.WorksheetPlanId equals b.WorksheetPlanId
                               from d in _context.WorksheetLogs.Where(x => x.IsActive == true && x.LogType == "B" && x.PlanOptId == b.WorksheetPlanId && x.WorksheetId == b.WorksheetId && x.Activity == a.Activity).DefaultIfEmpty()
                               from e in _context.Suppliers.Where(x => x.SubNumber == a.SubNumber).DefaultIfEmpty()
                               select new WorksheetOptActModel
                               {
                                   Activity = a.Activity,
                                   SubNumber = a.SubNumber,
                                   SupplierName = e != null ? e.SubName : null,
                                   Costprice = a.Costprice,
                                   WorksheetOptActId = a.WorksheetPlanActId,
                                   WorksheetOptId = 1,
                                   Lumpsum = a.Lumpsum,
                                   Warnings = a.Warnings,
                                   WarningReason = d.WarningDesc,
                                   WarningCount = d == null || d.WarningCode == null ? 0 : 1,
                                   ErrorCount = d == null || d.ErrorCode == null ? 0 : 1,
                                   ErrorCode = d.ErrorCode,
                                   ErrorReason = $"{d.ErrorDesc}:{d.ItemNumber} {d.ItemDesc}",                              
                                   IsActive = a.IsActive
                               }).ToList();
                var groupedByActivity = getPlanActsQuery.GroupBy(x => x.Activity).ToList();
                var activitiesList = groupedByActivity.Select(x => new WorksheetOptActModel()
                {
                    Activity = x.Key,
                    SubNumber = x.First().SubNumber,
                    SupplierName = x.First().SupplierName,
                    Costprice = x.First().Costprice,
                    WorksheetOptActId = x.First().WorksheetOptActId,
                    WorksheetOptId = x.First().WorksheetOptId,
                    Lumpsum = x.First().Lumpsum,
                    Warnings = x.First().Warnings,
                    WarningReason = string.Join(", ", x.Select(y => y.WarningReason)),
                    WarningCount = x.Sum(y => y.WarningCount),
                    ErrorCount = x.Sum(y => y.ErrorCount),
                    ErrorCode = x.First().ErrorCode,
                    ErrorReason = string.Join(", ", x.Select(y => y.ErrorReason)),
                    IsActive = true
                }).ToList();

                return new OkObjectResult(new ResponseModel<List<WorksheetOptActModel>> { Value = activitiesList, IsSuccess = true, Message = "Worksheet Option activities fetched successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<WorksheetOptActModel>> { IsSuccess = false, Message = "Error while fetching Worksheet Option activities", Value = worksheetOpts });
            }
        }
        [HttpGet("{worksheetPlanId}")]
        public async Task<IActionResult> GetWorksheetPlanOptionAsync(int worksheetPlanId)
        {
            try
            {
                var worksheetOpts = new List<WorksheetOptModel>();
                var getData = await _context.WorksheetOpts.AsNoTracking().AsSplitQuery().Include(x => x.WorksheetOptActs).Include(x => x.PlanOption.PhasePlan.MasterPlan).Include(x => x.PlanOption.MasterOption).Where(x => x.WorksheetPlanId == worksheetPlanId && x.IsActive == true).ToListAsync();
                worksheetOpts = getData.Select(x => new WorksheetOptModel()
                {
                    WorksheetOptId = x.WorksheetOptId,
                    WorksheetPlanId = x.WorksheetPlanId,
                    OptionCode = $"{x.PlanOption.PhasePlan.MasterPlan.PlanNum}{x.PlanOption.OptionCode}",
                    OptionName = x.PlanOption.ModifiedOptionDesc ?? x.PlanOption.MasterOption.OptionDesc,
                    PlanOptionId = x.PlanOptionId,
                    Costprice = x.Costprice,
                    Errors = x.WorksheetOptActs.Where(y => y.WorksheetOptId == x.WorksheetOptId).Sum(y => y.Errors),
                    ErrorCount = x.WorksheetOptActs.Where(y => y.WorksheetOptId == x.WorksheetOptId).Sum(y => y.Errors),
                    Warnings = x.WorksheetOptActs.Where(y => y.WorksheetOptId == x.WorksheetOptId).Sum(y => y.Warnings),
                    WarningCount = x.WorksheetOptActs.Where(y => y.WorksheetOptId == x.WorksheetOptId).Sum(y => y.Warnings),
                    Marketvalue = x.Marketvalue,
                    Sellprice = x.Sellprice,
                    Markuppercent = x.Markuppercent,
                    Markup = x.Markup,
                    Markuptype = x.Markuptype,
                    Pricedate = x.Pricedate,
                    IsActive = x.IsActive
                }).OrderBy(x => x.OptionCode).ToList();

                var getPhasePlanId = _context.WorksheetPlans.SingleOrDefault(x => x.WorksheetPlanId == worksheetPlanId).PhasePlanId;

                var getPlanTotal = _context.WorksheetPlans.Include(x => x.WorksheetPlanActs).SingleOrDefault(x => x.WorksheetPlanId == worksheetPlanId);
                worksheetOpts.Insert(0, new WorksheetOptModel()
                {
                    WorksheetPlanId = worksheetPlanId,
                    OptionCode = "BASE HOUSE",
                    OptionName = "BASE HOUSE",
                    IsBaseHouse = true,
                    WorksheetOptId = 1,//fix this
                    PlanOptionId = 1,//fix
                    PhasePlanId = getPhasePlanId,
                    Costprice = getPlanTotal.Costprice,
                    Errors = _context.WorksheetPlanActs.Where(y => y.WorksheetPlanId == worksheetPlanId).Sum(y => y.Errors),
                    Warnings = _context.WorksheetPlanActs.Where(y => y.WorksheetPlanId == worksheetPlanId).Sum(y => y.Warnings),
                    Marketvalue = getPlanTotal.Marketvalue,
                    Sellprice = getPlanTotal.Sellprice,
                    Markuppercent = getPlanTotal.Markuppercent,
                    Markup = getPlanTotal.Markup,
                    Markuptype = getPlanTotal.Markuptype,
                    Pricedate = getPlanTotal.Pricedate,
                    IsActive = true
                });
                return new OkObjectResult(new ResponseModel<List<WorksheetOptModel>> { Value = worksheetOpts, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<WorksheetOptModel>> { IsSuccess = false, Message = "Error while fetching Worksheet Plan options", Value = new List<WorksheetOptModel>() });
            }
        }
        [HttpPost]
        public async Task<IActionResult> CreateWorksheetAsync([FromBody] WorksheetDto model)
        {
            try
            {
                var worksheetToAdd = new Worksheet()
                {
                    WorksheetDesc = model.WorksheetDesc,
                    WorksheetName = model.WorksheetName,
                    CreatedBy = User.Identity.Name.Split('@')[0]
                };
                _context.Worksheets.Add(worksheetToAdd);
                await _context.SaveChangesAsync();
                model.WorksheetId = worksheetToAdd.WorksheetId;//To return new worksheet id
                return new OkObjectResult(new ResponseModel<WorksheetDto> { Value = model, IsSuccess = true, Message = "Successfully created Worksheet" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<WorksheetDto> { IsSuccess = false, Message = "Failed to create Worksheet", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> CopyWorksheetAsync([FromBody] WorksheetDto model)
        {
            try
            {
                //The model should have the id of the worksheet to copy from, with a new name
                var worksheetToAdd = new Worksheet()
                {
                    WorksheetDesc = model.WorksheetDesc,
                    WorksheetName = model.WorksheetName,
                    CreatedBy = User.Identity.Name.Split('@')[0]
                };
                _context.Worksheets.Add(worksheetToAdd);
                await _context.SaveChangesAsync();

                var copyFromWorksheetId = model.WorksheetId;//the old one to copy from
                var copyToWorksheetId = worksheetToAdd.WorksheetId;
                model.WorksheetId = worksheetToAdd.WorksheetId;//the new worksheet id

                //copy everything in the old worksheet to the new worksheetid
                //copy the worksheet plans
                var getPlans = _context.WorksheetPlans.Where(x => x.WorksheetId == copyFromWorksheetId).ToList();
                //add those same plans to new worksheet
                var createBy = User.Identity.Name.Split('@')[0];
                var copyPlans = getPlans.Select(x => new WorksheetPlan()
                {
                    WorksheetId = copyToWorksheetId,
                    Costprice = x.Costprice,
                    CreatedBy = createBy,
                    Marketvalue = x.Marketvalue,
                    Markup = x.Markup,
                    Markuptype = x.Markuptype,
                    PhasePlanId = x.PhasePlanId,
                    Sellprice = x.Sellprice
                }).ToList();
                await _context.WorksheetPlans.BulkInsertAsync(copyPlans);

                //copy the options
                var oldWorksheetPlanIds = getPlans.Select(x => x.WorksheetPlanId).ToList();
                var getOpts = _context.WorksheetOpts.Where(x => oldWorksheetPlanIds.Contains(x.WorksheetPlanId)).ToList();

                var newOpts = (from a in getOpts
                               join b in copyPlans
                               on a.WorksheetPlan.PhasePlanId equals b.PhasePlanId
                               select new WorksheetOpt()
                               {
                                   WorksheetPlanId = b.WorksheetPlanId,
                                   PlanOptionId = a.PlanOptionId,
                                   Costprice = a.Costprice,
                                   Sellprice = a.Sellprice,
                                   Marketvalue = a.Marketvalue,
                                   Markup = a.Markup,
                                   Markuppercent = a.Markuppercent,
                                   Markuptype = a.Markuptype,
                                   CreatedBy = createBy
                               }).ToList();
                await _context.WorksheetOpts.BulkInsertAsync(newOpts);

                //copy the optacts            
                var oldWorksheetOptIds = getOpts.Select(x => x.WorksheetOptId).ToList();
                var getOptActs = _context.WorksheetOptActs.Where(x => oldWorksheetOptIds.Contains(x.WorksheetOptId)).ToList();

                var newOptActs = (from a in getOptActs
                                  join b in newOpts
                                  on a.WorksheetOpt.PlanOptionId equals b.PlanOptionId
                                  select new WorksheetOptAct()
                                  {
                                      WorksheetOptId = b.WorksheetOptId,
                                      Activity = a.Activity,
                                      Costprice = a.Costprice,
                                      SubNumber = a.SubNumber,
                                      Lumpsum = a.Lumpsum,
                                      Errors = a.Errors,
                                      CreatedBy = createBy
                                  }).ToList();


                await _context.WorksheetOptActs.BulkInsertAsync(newOptActs);

                //copy the plan acts for each plan (base house)            
                var getPlanActs = _context.WorksheetPlanActs.Where(x => oldWorksheetPlanIds.Contains(x.WorksheetPlanId)).ToList();
                var newPlanActs = (from a in getPlanActs
                                   join b in copyPlans
                               on a.WorksheetPlan.PhasePlanId equals b.PhasePlanId
                                   select new WorksheetPlanAct()
                                   {
                                       WorksheetPlanId = b.WorksheetPlanId,
                                       Costprice = a.Costprice,
                                       Activity = a.Activity,
                                       Lumpsum = a.Lumpsum,
                                       SubNumber = a.SubNumber,
                                       Errors = a.Errors,
                                       CreatedBy = createBy
                                   }).ToList();
                await _context.WorksheetPlanActs.BulkInsertAsync(newPlanActs);

                //copy the error logs
                var getErrorLogs = _context.WorksheetLogs.Where(x => x.WorksheetId == copyFromWorksheetId).ToList();
                var optErrorLogs = (from a in getErrorLogs.Where(x => x.LogType == "O")                                   
                                    join c in getOpts on a.PlanOptId equals c.WorksheetOptId
                                    join d in newOpts on c.PlanOptionId equals d.PlanOptionId
                                select new WorksheetLog()
                               {
                                   WorksheetId = worksheetToAdd.WorksheetId,
                                   LogType = a.LogType,
                                   PlanOptId = d.WorksheetOptId,
                                   Activity = a.Activity,
                                   IsActive = a.IsActive,
                                   ItemNumber = a.ItemNumber,
                                   ItemDesc = a.ItemDesc,
                                   ErrorDesc = a.ErrorDesc,
                                   ErrorCode = a.ErrorCode,
                                   WarningCode = a.WarningCode,
                                   WarningDesc = a.WarningDesc,
                                   PhaseCode = a.PhaseCode,
                                   UserStamp = createBy,
                                   CreatedBy = createBy
                               }).ToList();
                var planErrorLogs = (from a in getErrorLogs.Where(x => x.LogType == "B")
                                     join c in getPlans on a.PlanOptId equals c.WorksheetPlanId
                                     join d in copyPlans on c.PhasePlanId equals d.PhasePlanId
                                     select new WorksheetLog()
                                      {
                                          WorksheetId = worksheetToAdd.WorksheetId,
                                          LogType = a.LogType,
                                          PlanOptId = d.WorksheetPlanId,
                                         ItemDesc = a.ItemDesc,
                                         ItemNumber = a.ItemNumber,
                                         Activity = a.Activity,
                                          IsActive = a.IsActive,
                                          ErrorDesc = a.ErrorDesc,
                                          ErrorCode = a.ErrorCode,
                                          WarningCode = a.WarningCode,
                                          WarningDesc = a.WarningDesc,
                                          PhaseCode = a.PhaseCode,
                                          UserStamp = createBy,
                                          CreatedBy = createBy,                                          
                                      }).ToList();                
                await _context.BulkInsertAsync(optErrorLogs);
                await _context.BulkInsertAsync(planErrorLogs);
                return new ObjectResult(new ResponseModel<WorksheetDto> { Value = model, IsSuccess = true, Message = "Successfully copied Worksheet" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<WorksheetDto> { IsSuccess = false, Message = "Failed to Copy Worksheet", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> RepriceWorksheetAsync([FromBody] RepriceWorksheetModel model)
        {
            //This will get new costs and prices for a worksheet according to the new date picked
            try
            {
                var updateBy = User.Identity.Name.Split('@')[0];
                var worksheetOptIds = _context.WorksheetOpts.AsNoTracking().Where(x => x.WorksheetPlan.WorksheetId == model.WorksheetId).Select(x => x.WorksheetOptId).ToList();
                ///todo: isactive only on everything

                string stringRepriceDate = model.RepriceDate != null ? model.RepriceDate.Value.ToString("MM/dd/yyyy") : DateTime.Now.ToString("MM/dd/yyyy");
                var optActsList = new List<WorksheetOptActModel>();
                var connString = _configuration.GetConnectionString("ERPConnection");
                using var connection = new SqlConnection(connString);
                connection.Open();
                var query = "select q.SUB_NUMBER, t.WORKSHEET_OPT_ACT_ID, n.ACTIVITY, l.MASTER_ITEM_ID, l.ITEM_NUMBER, y.PHASE_CODE, l.ITEM_DESC, a.WORKSHEET_OPT_ID, v.WORKSHEET_ID, a.PLAN_OPTION_ID, s.UNIT_COST, u.UNIT_COST, s.NEXT_COST, u.NEXT_COST,s.LAST_COST_1, u.LAST_COST_1,s.LAST_COST_2, u.LAST_COST_2,s.LAST_COST_3, u.LAST_COST_3, s.NEXT_COST_DUE, u.NEXT_COST_DUE, s.LAST_COST_EXPIRED, U.LAST_COST_EXPIRED, s.LAST_COST_2_EXPIRED, u.LAST_COST_2_EXPIRED,s.LAST_COST_3_EXPIRED, u.LAST_COST_3_EXPIRED,cs.OLD_COST,\r\ncu.OLD_COST, cs.COST_EXPIRED,  cu.COST_EXPIRED, d.FACTOR \r\nfrom WORKSHEET_OPT a\r\njoin WORKSHEET_PLAN v on a.WORKSHEET_PLAN_ID = v.WORKSHEET_PLAN_ID\r\njoin AVAILABLE_PLAN_OPTION b on a.PLAN_OPTION_ID = b.PLAN_OPTION_ID AND b.IsActive = 1 and a.IsActive = 1\r\njoin MASTER_PLAN z on b.MASTER_PLAN_ID = z.MASTER_PLAN_ID\r\njoin ASM_HEADER c on b.MASTER_PLAN_ID = c.MASTER_PLAN_ID and b.MASTER_OPTION_ID = c.MASTER_OPTION_ID AND c.IsActive = 1\r\njoin ASM_DETAIL d on c.ASM_HEADER_ID = d.ASM_HEADER_ID and d.IsActive = 1\r\njoin MASTER_ITEMS l on d.MASTER_ITEM_ID = l.MASTER_ITEM_ID\r\njoin MASTER_ITEM_PHASES y on l.MASTER_ITEM_PHASE_ID = y.MASTER_ITEM_PHASE_ID\r\njoin BOM_CLASS m on l.BOM_CLASS_ID = m.BOM_CLASS_ID\r\njoin PACTIVITY n on m.BOM_CLASS_ID = n.BOM_CLASS_ID\r\njoin PHASE_PLAN r on b.PHASE_PLAN_ID = r.PHASE_PLAN_ID\r\nleft join PACTIVITY_AREA_SUPPLIER q on r.SUBDIVISION_ID = q.SUBDIVISION_ID and n.PACTIVITY_ID = q.PACTIVITY_ID and q.IsActive = 1\r\njoin WORKSHEET_OPT_ACT t  on a.WORKSHEET_OPT_ID = t.WORKSHEET_OPT_ID and n.ACTIVITY = t.ACTIVITY\r\nleft join COSTS s on l.MASTER_ITEM_ID = s.MASTER_ITEM_ID and q.SUB_NUMBER = s.SUB_NUMBER and q.SUBDIVISION_ID = s.SUBDIVISION_ID and s.IsActive = 1\r\nleft join COSTS u on l.MASTER_ITEM_ID = u.MASTER_ITEM_ID and q.SUB_NUMBER = u.SUB_NUMBER and u.SUBDIVISION_ID = 1 and s.COSTS_ID is null and u.IsActive = 1\r\nleft join COSTS_HISTORY cs on s.COSTS_ID = cs.COSTS_ID and cs.COST_EXPIRED >= @repriceDate and cs.IsActive = 1\r\nleft join COSTS_HISTORY cu on u.COSTS_ID = cu.COSTS_ID and cu.COST_EXPIRED >= @repriceDate and s.COSTS_ID is null and cu.IsActive = 1\r\nwhere n.DIV_ID = 1 and v.WORKSHEET_ID = @worksheetId";
                var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@worksheetId", model.WorksheetId);
                command.Parameters.AddWithValue("@repriceDate", stringRepriceDate);
                var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    optActsList.Add(new WorksheetOptActModel()
                    {
                        WorksheetOptActId = reader.GetValue(1) != DBNull.Value ? (int)reader.GetValue(1) : 0,
                        WorksheetOptId = reader.GetValue(7) != DBNull.Value ? (int)reader.GetValue(7) : 0,
                        IsActive = true,
                        SubNumber = reader.GetValue(0) != DBNull.Value ? (int?)reader.GetValue(0) : null,
                        Costprice = reader.GetValue(10) != DBNull.Value ? (double?)reader.GetValue(10) : reader.GetValue(11) != DBNull.Value ? (double?)reader.GetValue(11) : null, 
                        Activity = reader.GetValue(2) != DBNull.Value ? (string?)reader.GetValue(2) : null,
                        Lumpsum = "F",
                        ErrorCount = reader.GetValue(10) == DBNull.Value && reader.GetValue(11) == DBNull.Value ? 1 : 0,
                        PhaseCode = reader.GetValue(5) != DBNull.Value ? (string?)reader.GetValue(5) : null,
                        ItemNumber = reader.GetValue(4) != DBNull.Value ? (string?)reader.GetValue(4) : null,
                        ItemDesc = reader.GetValue(6) != DBNull.Value ? (string?)reader.GetValue(6) : null,
                        UpdatedDateTime = DateTime.Now,
                        UpdatedBy = updateBy,
                        UnitCost = reader.GetValue(10) != DBNull.Value ? (double?)reader.GetValue(10) : reader.GetValue(11) != DBNull.Value ? (double?)reader.GetValue(11) : null, 
                        NextCost = reader.GetValue(12) != DBNull.Value ? (double?)reader.GetValue(12) : reader.GetValue(13) != DBNull.Value ? (double?)reader.GetValue(13) : null, 
                        LastCost1 = reader.GetValue(14) != DBNull.Value ? (double?)reader.GetValue(14) : reader.GetValue(15) != DBNull.Value ? (double?)reader.GetValue(15) : null,
                        LastCost2 = reader.GetValue(16) != DBNull.Value ? (double?)reader.GetValue(16) : reader.GetValue(17) != DBNull.Value ? (double?)reader.GetValue(17) : null, 
                        LastCost3 = reader.GetValue(18) != DBNull.Value ? (double?)reader.GetValue(18) : reader.GetValue(19) != DBNull.Value ? (double?)reader.GetValue(19) : null, 
                        NextCostSartDate = reader.GetValue(20) != DBNull.Value ? (DateTime?)reader.GetValue(20) : reader.GetValue(21) != DBNull.Value ? (DateTime?)reader.GetValue(21) : null,
                        LastCostEndDate = reader.GetValue(22) != DBNull.Value ? (DateTime?)reader.GetValue(22) : reader.GetValue(23) != DBNull.Value ? (DateTime?)reader.GetValue(23) : null,
                        LastCost2EndDate = reader.GetValue(24) != DBNull.Value ? (DateTime?)reader.GetValue(24) : reader.GetValue(25) != DBNull.Value ? (DateTime?)reader.GetValue(25) : null,
                        LastCost3EndDate = reader.GetValue(26) != DBNull.Value ? (DateTime?)reader.GetValue(26) : reader.GetValue(27) != DBNull.Value ? (DateTime?)reader.GetValue(27) : null,
                        OldCost = reader.GetValue(28) != DBNull.Value ? (double?)reader.GetValue(28) : reader.GetValue(29) != DBNull.Value ? (double?)reader.GetValue(29) : null,
                        OldCostEndDate = reader.GetValue(30) != DBNull.Value ? (DateTime?)reader.GetValue(30) : reader.GetValue(31) != DBNull.Value ? (DateTime?)reader.GetValue(31) : null,
                        MasterItemId = reader.GetValue(3) != DBNull.Value ? (int?)reader.GetValue(3) : null,
                        ItemQuantity = reader.GetValue(32) != DBNull.Value ? (double?)reader.GetValue(32) : null
                    });
                }
                reader.Close();
               //newlist is here to get the right entry if there was cost history
               //TODO: simplify this 
                var newList = new List<WorksheetOptAct>();
                var grouped = optActsList.GroupBy(x => new { x.WorksheetOptActId, x.MasterItemId }).GroupBy(x => x.Key.WorksheetOptActId).ToList();   
                foreach(var optact in grouped)
                {
                    foreach(var masteritem in optact)
                    {
                        double? cost;
                        var item = masteritem.First();
                        var oldCosts = masteritem.Where(x => x.OldCost != null).ToList();
                        if (masteritem.Count() > 1 && oldCosts != null && oldCosts.Count != 0)
                        {
                            //there are histories, pick the one with the oldest date (expired dates before the reprice date are already excluded)
                            cost = oldCosts.MinBy(x => x.OldCostEndDate).OldCost * item.ItemQuantity;                                                       
                        }
                        else
                        {
                            //Note: there is a bug here if there are multiple costs for the item but not costs history, need to prevent that in costs module
                            //no histories                            
                            cost = ((item.NextCostSartDate != null && model.RepriceDate.Value.Date >= item.NextCostSartDate.Value.Date) ? item.NextCost : (item.LastCostEndDate == null || model.RepriceDate.Value.Date >= item.LastCostEndDate.Value.Date ) ? item.UnitCost : (item.LastCost2EndDate == null || model.RepriceDate.Value.Date >= item.LastCost2EndDate.Value.Date ) ? item.LastCost1 : (item.LastCost3EndDate == null || model.RepriceDate.Value.Date >= item.LastCost3EndDate.Value.Date) ? item.LastCost2 : item.LastCost3) * item.ItemQuantity;
                        }
                        newList.Add(new WorksheetOptAct()
                        {
                            WorksheetOptActId = masteritem.Key.WorksheetOptActId,
                            WorksheetOptId = item.WorksheetOptId,
                            IsActive = true,
                            SubNumber = item.SubNumber,
                            Activity = item.Activity,
                            Costprice = (double?)cost,
                            Lumpsum = "F",
                            UpdatedBy = updateBy,
                            UpdatedDateTime = DateTime.Now,
                            Errors = item.ErrorCount,
                            Warnings = 0
                        }); 
                    }
                }
                var grouped1 = newList.GroupBy(x => x.WorksheetOptActId).ToList();
                var updateOptActs1 = grouped1.Select(x => new WorksheetOptAct()
                {
                    WorksheetOptActId = x.Key,
                    WorksheetOptId = x.First().WorksheetOptId,
                    IsActive = true,
                    SubNumber = x.First().SubNumber,
                    Activity = x.First().Activity,
                    Costprice = (double?)x.Sum(y => y.Costprice),
                    Lumpsum = "F",
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now,
                    Errors = x.Sum(y => y.Errors),
                    Warnings = 0
                }).ToList();
                await _context.WorksheetOptActs.BulkUpdateAsync(updateOptActs1, options => options.ColumnInputExpression = x => new { x.Activity, x.Lumpsum, x.Errors, x.Warnings, x.SubNumber, x.Costprice, x.UpdatedBy, x.UpdatedDateTime });

                //deactivate any existing error logs
                var findErrorLogs = (from a in optActsList
                                     join b in _context.WorksheetLogs.Where(x => x.IsActive == true) on new { worksheetId = model.WorksheetId, activity = (string?)a.Activity, optId = (int?)a.WorksheetOptId } equals new { worksheetId = b.WorksheetId, activity = b.Activity, optId = b.PlanOptId }
                                     select new WorksheetLog()
                                     {
                                         WorksheetLogId = b.WorksheetLogId,
                                         IsActive = false,
                                         UpdatedDateTime = DateTime.Now,
                                         UpdatedBy = updateBy
                                     }).ToList();
                await _context.WorksheetLogs.BulkUpdateAsync(findErrorLogs, options => options.ColumnInputExpression = c => new { c.IsActive, c.UpdatedBy, c.UpdatedDateTime });

                //add new error logs
                var insertErrorLog = optActsList.Where(x => x.ErrorCount != 0).Select(x => new WorksheetLog()
                {
                    WorksheetId = model.WorksheetId,
                    LogType = "O",//O = option, B = Base house
                    PlanOptId = x.WorksheetOptId,//for option, fill worksheet opt id, for base house, fill plan opt id
                    Activity = x.Activity,
                    PhaseCode = x.PhaseCode,
                    ItemNumber = x.ItemNumber,
                    ItemDesc = x.ItemDesc,
                    ErrorCode = 4,
                    ErrorDesc = "No Cost Found",
                    CreatedBy = updateBy,//seems not filling correctly
                    UserStamp = updateBy,
                    CreatedDateTime = DateTime.Now,
                }).ToList();
                await _context.BulkInsertAsync(insertErrorLog);

                //sum the new sum to the opts
                var result2 = await SumWorksheetOptActs(worksheetOptIds, model.RepriceDate);

                //get repricing on the base house/plan level 
                var worksheetPlanIds = _context.WorksheetPlans.Where(x => x.Worksheet.WorksheetId == model.WorksheetId).Select(x => x.WorksheetPlanId).ToList();
                //get the costs
                var planActsList = new List<WorksheetPlanActModel>();
                string query2 = "select q.SUB_NUMBER, t.WORKSHEET_PLAN_ACT_ID, n.ACTIVITY, l.MASTER_ITEM_ID, l.ITEM_NUMBER, y.PHASE_CODE, l.ITEM_DESC, a.WORKSHEET_PLAN_ID, a.PHASE_PLAN_ID, a.WORKSHEET_ID, s.UNIT_COST, u.UNIT_COST, s.NEXT_COST, u.NEXT_COST,s.LAST_COST_1, u.LAST_COST_1,s.LAST_COST_2, u.LAST_COST_2,s.LAST_COST_3, u.LAST_COST_3, s.NEXT_COST_DUE, u.NEXT_COST_DUE, s.LAST_COST_EXPIRED, U.LAST_COST_EXPIRED, s.LAST_COST_2_EXPIRED, u.LAST_COST_2_EXPIRED,s.LAST_COST_3_EXPIRED, u.LAST_COST_3_EXPIRED,cs.OLD_COST,cu.OLD_COST, cs.COST_EXPIRED, cu.COST_EXPIRED, d.FACTOR from ASM_HEADER c \r\njoin ASM_DETAIL d on c.ASM_HEADER_ID = d.ASM_HEADER_ID and d.IsActive = 1 and c.IsActive = 1\r\njoin MASTER_ITEMS l on d.MASTER_ITEM_ID = l.MASTER_ITEM_ID\r\njoin MASTER_ITEM_PHASES y on l.MASTER_ITEM_PHASE_ID = y.MASTER_ITEM_PHASE_ID\r\njoin BOM_CLASS m on l.BOM_CLASS_ID = m.BOM_CLASS_ID\r\njoin PACTIVITY n on m.BOM_CLASS_ID = n.BOM_CLASS_ID\r\njoin MASTER_PLAN k on c.MASTER_PLAN_ID = k.MASTER_PLAN_ID\r\njoin PHASE_PLAN r on k.MASTER_PLAN_ID = r.MASTER_PLAN_ID\r\njoin WORKSHEET_PLAN a on r.PHASE_PLAN_ID = a.PHASE_PLAN_ID AND a.IsActive = 1\r\nleft join PACTIVITY_AREA_SUPPLIER q on r.SUBDIVISION_ID = q.SUBDIVISION_ID and n.PACTIVITY_ID = q.PACTIVITY_ID and q.IsActive = 1\r\njoin WORKSHEET_PLAN_ACT t  on a.WORKSHEET_PLAN_ID = t.WORKSHEET_PLAN_ID and n.ACTIVITY = t.ACTIVITY and t.IsActive = 1\r\nleft join COSTS s on l.MASTER_ITEM_ID = s.MASTER_ITEM_ID and q.SUB_NUMBER = s.SUB_NUMBER and q.SUBDIVISION_ID = s.SUBDIVISION_ID and s.IsActive = 1\r\nleft join COSTS u on l.MASTER_ITEM_ID = u.MASTER_ITEM_ID and q.SUB_NUMBER = u.SUB_NUMBER and u.SUBDIVISION_ID = 1 and s.COSTS_ID is null and u.IsActive = 1\r\nleft join COSTS_HISTORY cs on s.COSTS_ID = cs.COSTS_ID and cs.COST_EXPIRED >= @repriceDate and cs.IsActive = 1\r\nleft join COSTS_HISTORY cu on u.COSTS_ID = cu.COSTS_ID and cu.COST_EXPIRED >= @repriceDate and s.COSTS_ID is null and cu.IsActive = 1\r\nwhere n.DIV_ID = 1 and a.WORKSHEET_ID = @worksheetId and c.ASSEMBLY_DESC like '%base house%'\r\norder by l.MASTER_ITEM_ID\r\n";
                command = new SqlCommand(query2, connection);
                command.Parameters.AddWithValue("@worksheetId", model.WorksheetId);
                command.Parameters.AddWithValue("@repriceDate", stringRepriceDate);
                reader = command.ExecuteReader();

                while (reader.Read())
                {
                    planActsList.Add(new WorksheetPlanActModel()
                    {
                        WorksheetPlanActId = reader.GetValue(1) != DBNull.Value ? (int)reader.GetValue(1) : 0,
                        WorksheetPlanId = reader.GetValue(7) != DBNull.Value ? (int)reader.GetValue(7) : 0,
                        IsActive = true,
                        SubNumber = reader.GetValue(0) != DBNull.Value ? (int?)reader.GetValue(0) : null,
                        Costprice = reader.GetValue(10) != DBNull.Value ? (double?)reader.GetValue(10) : reader.GetValue(11) != DBNull.Value ? (double?)reader.GetValue(11) : null, 
                        Activity = reader.GetValue(2) != DBNull.Value ? (string?)reader.GetValue(2) : null,
                        Lumpsum = "F",
                        ErrorCount = reader.GetValue(10) == DBNull.Value && reader.GetValue(11) == DBNull.Value ? 1 : 0,
                        PhaseCode = reader.GetValue(5) != DBNull.Value ? (string?)reader.GetValue(5) : null,
                        ItemNumber = reader.GetValue(4) != DBNull.Value ? (string?)reader.GetValue(4) : null,
                        ItemDesc = reader.GetValue(6) != DBNull.Value ? (string?)reader.GetValue(6) : null,
                        UpdatedDateTime = DateTime.Now,
                        UpdatedBy = updateBy,
                        UnitCost = reader.GetValue(10) != DBNull.Value ? (double?)reader.GetValue(10) : reader.GetValue(11) != DBNull.Value ? (double?)reader.GetValue(11) : null,
                        NextCost = reader.GetValue(12) != DBNull.Value ? (double?)reader.GetValue(12) : reader.GetValue(13) != DBNull.Value ? (double?)reader.GetValue(13) : null,
                        LastCost1 = reader.GetValue(14) != DBNull.Value ? (double?)reader.GetValue(14) : reader.GetValue(15) != DBNull.Value ? (double?)reader.GetValue(15) : null,
                        LastCost2 = reader.GetValue(16) != DBNull.Value ? (double?)reader.GetValue(16) : reader.GetValue(17) != DBNull.Value ? (double?)reader.GetValue(17) : null,
                        LastCost3 = reader.GetValue(18) != DBNull.Value ? (double?)reader.GetValue(18) : reader.GetValue(19) != DBNull.Value ? (double?)reader.GetValue(19) : null,
                        NextCostSartDate = reader.GetValue(20) != DBNull.Value ? (DateTime?)reader.GetValue(20) : reader.GetValue(21) != DBNull.Value ? (DateTime?)reader.GetValue(21) : null,
                        LastCostEndDate = reader.GetValue(22) != DBNull.Value ? (DateTime?)reader.GetValue(22) : reader.GetValue(23) != DBNull.Value ? (DateTime?)reader.GetValue(23) : null,
                        LastCost2EndDate = reader.GetValue(24) != DBNull.Value ? (DateTime?)reader.GetValue(24) : reader.GetValue(25) != DBNull.Value ? (DateTime?)reader.GetValue(25) : null,
                        LastCost3EndDate = reader.GetValue(26) != DBNull.Value ? (DateTime?)reader.GetValue(26) : reader.GetValue(27) != DBNull.Value ? (DateTime?)reader.GetValue(27) : null,
                        OldCost = reader.GetValue(28) != DBNull.Value ? (double?)reader.GetValue(28) : reader.GetValue(29) != DBNull.Value ? (double?)reader.GetValue(29) : null,
                        OldCostEndDate = reader.GetValue(30) != DBNull.Value ? (DateTime?)reader.GetValue(30) : reader.GetValue(31) != DBNull.Value ? (DateTime?)reader.GetValue(31) : null,
                        MasterItemId = reader.GetValue(3) != DBNull.Value ? (int?)reader.GetValue(3) : null,
                        ItemQuantity = reader.GetValue(32) != DBNull.Value ? (double?)reader.GetValue(32) : null,
                    });
                }
                reader.Close();


                var newPlanList = new List<WorksheetPlanAct>();
                var groupedPlanList = planActsList.GroupBy(x => new { x.WorksheetPlanActId, x.MasterItemId }).GroupBy(x => x.Key.WorksheetPlanActId).ToList();
                foreach (var planact in groupedPlanList)
                {
                    foreach (var masteritem in planact)
                    {
                        double? cost;
                        var item = masteritem.First();
                        var oldCosts = masteritem.Where(x => x.OldCost != null).ToList();
                        if (masteritem.Count() > 1 && oldCosts != null && oldCosts.Count != 0)
                        {
                            //there are histories, pick the one with the oldest date (expired dates before the reprice date are already excluded)
                            cost = oldCosts.MinBy(x => x.OldCostEndDate).OldCost * item.ItemQuantity;
                        }                        
                        else
                        {
                            //no histories                            
                            cost = ((item.NextCostSartDate != null && model.RepriceDate.Value.Date >= item.NextCostSartDate.Value.Date) ? item.NextCost : (item.LastCostEndDate == null || model.RepriceDate.Value.Date >= item.LastCostEndDate.Value.Date) ? item.UnitCost : (item.LastCost2EndDate == null || model.RepriceDate.Value.Date >= item.LastCost2EndDate.Value.Date) ? item.LastCost1 : (item.LastCost3EndDate == null || model.RepriceDate.Value.Date >= item.LastCost3EndDate.Value.Date) ? item.LastCost2 : item.LastCost3) * item.ItemQuantity;
                        }
                        newPlanList.Add(new WorksheetPlanAct()
                        {
                            WorksheetPlanActId = masteritem.Key.WorksheetPlanActId,
                            WorksheetPlanId = item.WorksheetPlanId,
                            IsActive = true,
                            SubNumber = item.SubNumber,
                            Activity = item.Activity,
                            Costprice = (double?)cost,
                            Lumpsum = "F",
                            UpdatedBy = updateBy,
                            UpdatedDateTime = DateTime.Now,
                            Errors = item.ErrorCount,
                            Warnings = 0
                        });
                    }
                }
                var groupedPlan = newPlanList.GroupBy(x => x.WorksheetPlanActId).ToList();
                var planActsToUpdate = groupedPlan.Select(x => new WorksheetPlanAct()
                {
                    IsActive = true,
                    SubNumber = x.First().SubNumber,
                    Activity = x.First().Activity,
                    Costprice = (double?)x.Sum(y => y.Costprice),
                    WorksheetPlanId = x.First().WorksheetPlanId,
                    WorksheetPlanActId = x.Key,
                    Lumpsum = "F",
                    Warnings = 0,
                    Errors = x.Sum(y => y.Errors),
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now
                }).ToList();

                await _context.WorksheetPlanActs.BulkUpdateAsync(planActsToUpdate, options => options.ColumnInputExpression = x => new { x.Costprice, x.Activity, x.Errors, x.Lumpsum, x.Warnings, x.SubNumber, x.UpdatedDateTime, x.UpdatedBy });

                //deactivate any existing error logs
                var findPlanErrorLogs = (from a in planActsList
                                         join b in _context.WorksheetLogs.Where(x => x.IsActive == true) on new { worksheetId = model.WorksheetId, activity = (string?)a.Activity, planId = (int?)a.WorksheetPlanId } equals new { worksheetId = b.WorksheetId, activity = b.Activity, planId = b.PlanOptId }
                                     select new WorksheetLog()
                                     {
                                         WorksheetLogId = b.WorksheetLogId,
                                         IsActive = false,
                                         UpdatedDateTime = DateTime.Now,
                                         UpdatedBy = updateBy
                                     }).ToList();
                await _context.WorksheetLogs.BulkUpdateAsync(findPlanErrorLogs, options => options.ColumnInputExpression = c => new { c.IsActive, c.UpdatedBy, c.UpdatedDateTime });

                //add new error logs
                var insertPlanErrorLog = planActsList.Where(x => x.ErrorCount != 0).Select(x => new WorksheetLog()
                {
                    WorksheetId = model.WorksheetId,
                    LogType = "B",//O = option, B = Base house
                    PlanOptId = x.WorksheetPlanId,//for option, fill worksheet opt id, for base house, fill plan opt id
                    Activity = x.Activity,
                    PhaseCode = x.PhaseCode,
                    ItemNumber = x.ItemNumber,
                    ItemDesc = x.ItemDesc,
                    ErrorCode = 4,
                    ErrorDesc = "No Cost Found",
                    CreatedBy = updateBy,//seems not filling correctly
                    UserStamp = updateBy,
                    CreatedDateTime = DateTime.Now,
                }).ToList();
                await _context.BulkInsertAsync(insertPlanErrorLog);

                //sum the new sum to the plans
                var result4 = await SumWorksheetPlanActs(worksheetPlanIds, model.RepriceDate);

                return new OkObjectResult(new ResponseModel<RepriceWorksheetModel> { Value = model, IsSuccess = true, Message = "Repriced Worksheet successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<RepriceWorksheetModel> { IsSuccess = false, Message = "Failed to reprice Worksheet", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> RepriceWorksheetOptsAsync([FromBody] RepriceWorksheetOptsModel model)
        {
            //This will get new costs and prices for a option according to the new date picked
            try
            {
                model.RepriceDate = model.RepriceDate != null ? model.RepriceDate.Value.Date : DateTime.Now.Date;
                var worksheetOptIds = model.WorksheetOptIds;
                var optActsToUpdate = (from a in _context.WorksheetOpts.Where(x => x.IsActive == true && worksheetOptIds.Contains(x.WorksheetOptId))
                                       join v in _context.WorksheetPlans.Where(x => x.IsActive == true) on a.WorksheetPlanId equals v.WorksheetPlanId
                                       join b in _context.AvailablePlanOptions.Where(x => x.IsActive == true) on a.PlanOptionId equals b.PlanOptionId
                                       join z in _context.MasterPlans.Where(x => x.IsActive == true) on b.MasterPlanId equals z.MasterPlanId
                                       join c in _context.AsmHeaders.Where(x => x.IsActive == true) on new { mo = b.MasterOptionId ?? 0, mp = b.MasterPlanId ?? 0 } equals new { mo = c.MasterOptionId ?? 0, mp = c.MasterPlanId ?? 0 }
                                       join d in _context.AsmDetails.Where(x => x.IsActive == true) on c.AsmHeaderId equals d.AsmHeaderId
                                       join l in _context.MasterItems.Where(x => x.IsActive == true) on d.MasterItemId equals l.MasterItemId
                                       join y in _context.MasterItemPhases.Where(x => x.IsActive == true) on l.MasterItemPhaseId equals y.MasterItemPhaseId
                                       join m in _context.BomClasses.Where(x => x.IsActive == true) on l.BomClassId equals m.BomClassId
                                       join n in _context.Pactivities.Where(x => x.DivId == 1 && x.IsActive == true) on m.BomClassId equals n.BomClassId
                                       join r in _context.PhasePlans.Where(x => x.IsActive == true) on b.PhasePlanId equals r.PhasePlanId
                                       from q in _context.PactivityAreaSuppliers.Where(x => x.IsActive == true && x.SubdivisionId == r.SubdivisionId && x.PactivityId == n.PactivityId).DefaultIfEmpty()
                                       join t in _context.WorksheetOptActs.Where(x => x.IsActive == true && worksheetOptIds.Contains(x.WorksheetOptId)) on new { a.WorksheetOptId, n.Activity } equals new { t.WorksheetOptId, t.Activity }
                                       from s in _context.Costs.Include(x => x.CostsHistories).Where(x => x.IsActive == true && x.SubdivisionId == q.SubdivisionId && x.SubNumber == q.SubNumber && x.MasterItemId == l.MasterItemId).DefaultIfEmpty()
                                       from u in _context.Costs.Include(x => x.CostsHistories).Where(x => x.IsActive == true && x.MasterItemId == l.MasterItemId && x.SubNumber == q.SubNumber && (x.SubdivisionId == 1)).DefaultIfEmpty()
                                       select new
                                       {
                                           SubdivSubdiv = r.SubdivisionId,
                                           SubdivisionCosts = s != null ? s.SubdivisionId : (int?)null,
                                           SubdivTrade = q != null ? q.SubdivisionId : (int?)null,
                                           SubNumber = q != null ? q.SubNumber : (int?)null,
                                           Activity = n.Activity,
                                           MasterItemId = l.MasterItemId,
                                           ItemNumber = l.ItemNumber,
                                           ItemDesc = l.ItemDesc,
                                           PhaseCode = y.PhaseCode,
                                           WorksheetOptId = a.WorksheetOptId,
                                           WorksheetId = v.WorksheetId,
                                           PlanOptionId = a.PlanOptionId,
                                           AsmHeaderId = (int?)c.AsmHeaderId,
                                           WorksheetOptActId = t.WorksheetOptActId,                                           
                                           CostsId = s != null ? s.CostsId : u != null ? u.CostsId : (int?)null,
                                           Costprice = s == null ? (u != null ? (u.LastCost3Expired != null && model.RepriceDate < u.LastCost3Expired.Value.Date) ? (u.CostsHistories != null && u.CostsHistories.Count != 0 && (u.CostsHistories.Where(x => x.CostExpired != null && model.RepriceDate < x.CostExpired.Value.Date).ToList().Count) != 0 ? u.CostsHistories.FirstOrDefault(y => y.CostExpired == u.CostsHistories.Where(x => x.CostExpired != null && model.RepriceDate < x.CostExpired.Value.Date).Max(x => x.CostExpired)).OldCost : u.LastCost3)
                                           : ((u.LastCost2Expired != null && model.RepriceDate < u.LastCost2Expired.Value.Date) ? u.LastCost2 : ((u.LastCostExpired != null && model.RepriceDate < u.LastCostExpired.Value.Date) ? u.LastCost1 : ((u.NextCostDue == null || model.RepriceDate < u.NextCostDue.Value.Date) ? u.UnitCost : (u.NextCost2Due == null || model.RepriceDate < u.NextCost2Due.Value.Date ? u.NextCost : u.NextCost2)))) : null) :
                                           (s.LastCost3Expired != null && model.RepriceDate < s.LastCost3Expired.Value.Date) ? (s.CostsHistories != null && s.CostsHistories.Count != 0 && s.CostsHistories.Where(x => x.CostExpired != null && model.RepriceDate < x.CostExpired.Value.Date).ToList().Count != 0 ? s.CostsHistories.FirstOrDefault(y => y.CostExpired == s.CostsHistories.Where(x => x.CostExpired != null && model.RepriceDate < x.CostExpired.Value.Date).Max(x => x.CostExpired)).OldCost : s.LastCost3) : ((s.LastCost2Expired != null && model.RepriceDate < s.LastCost2Expired.Value.Date) ? s.LastCost2 : ((s.LastCostExpired != null && model.RepriceDate < s.LastCostExpired.Value.Date) ? s.LastCost1 : ((s.NextCostDue == null || model.RepriceDate < s.NextCostDue.Value.Date) ? s.UnitCost : (s.NextCost2Due == null || model.RepriceDate < s.NextCost2Due.Value.Date ? s.NextCost : s.NextCost2)))),//if no subdivision cost, use division default one, if none, then null 
                                           Errors = ((s == null && u == null) || (s.CostsId == 0 && u.CostsId == 0)) ? 1 : 0, //TODO: no supplier error on q is null
                                           ItemQuantity = d.Factor
                                       }).ToList();
                var createBy = User.Identity.Name.Split('@')[0];

                //group opt acts, sum items in them. 
                var updateOptActs = optActsToUpdate.GroupBy(x => x.WorksheetOptActId).Select(x => new WorksheetOptAct()
                {
                    IsActive = true,
                    SubNumber = x.First().SubNumber,
                    Activity = x.First().Activity,
                    Costprice = (double?)x.Sum(y => (y.Costprice * y.ItemQuantity)),
                    Lumpsum = "F",//set back to false since it's now back to cost from cost table
                    WorksheetOptId = x.First().WorksheetOptId,
                    WorksheetOptActId = x.Key,
                    Errors = x.Sum(y => y.Errors),
                    Warnings = 0,//remove lump sum warnign if there is one, since it's now back to cost from cost table
                    UpdatedBy = createBy,
                    UpdatedDateTime = DateTime.Now
                }).ToList();

                await _context.WorksheetOptActs.BulkUpdateAsync(updateOptActs, options => options.ColumnInputExpression = x => new {  x.Costprice, x.Activity, x.SubNumber,x.Warnings, x.Errors, x.Lumpsum, x.UpdatedBy, x.UpdatedDateTime} );

                //deactivate any existing error logs
                var findErrorLogs = (from a in optActsToUpdate
                                     join b in _context.WorksheetLogs.Where(x => x.IsActive == true) on new { worksheetId = a.WorksheetId, activity = (string?)a.Activity, optId = (int?)a.WorksheetOptId } equals new { worksheetId = b.WorksheetId, activity = b.Activity, optId = b.PlanOptId }
                                     select new WorksheetLog()
                                     {
                                         WorksheetLogId = b.WorksheetLogId,
                                         IsActive = false,
                                         UpdatedDateTime = DateTime.Now,
                                         UpdatedBy = createBy
                                     }).ToList();
                await _context.WorksheetLogs.BulkUpdateAsync(findErrorLogs, options => options.ColumnInputExpression = c => new { c.IsActive, c.UpdatedBy, c.UpdatedDateTime });

                //add new error logs
                var insertErrorLog = optActsToUpdate.Where(x => x.Errors != 0).Select(x => new WorksheetLog()
                {
                    WorksheetId = x.WorksheetId,
                    LogType = "O",//O = option, B = Base house
                    PlanOptId = x.WorksheetOptId,//for option, fill worksheet opt id, for base house, fill plan opt id
                    Activity = x.Activity,
                    PhaseCode = x.PhaseCode,
                    ItemNumber = x.ItemNumber,
                    ItemDesc = x.ItemDesc,
                    ErrorCode = 4,
                    ErrorDesc = "No Cost Found",
                    CreatedBy = createBy,//seems not filling correctly
                    UserStamp = createBy,
                    CreatedDateTime = DateTime.Now,
                }).ToList();
                await _context.BulkInsertAsync(insertErrorLog);

                await SumWorksheetOptActs(worksheetOptIds, model.RepriceDate);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<RepriceWorksheetModel> { IsSuccess = false, Message = "Failed to reprice worksheet options", Value = null });
            }
            var response = new ResponseModel<RepriceWorksheetOptsModel>() { IsSuccess = true, Message = "Successfully repriced options", Value = model };
            return Ok(response);
        }
        [HttpPost]
        public async Task<IActionResult> RepriceWorksheetPlansAsync([FromBody] RepriceWorksheetPlansModel model)
        {

            //reprice option in the base house plan (ie plan act)
            //This will get new costs and prices for a option according to the new date picked
            try
            {
                var updatedBy = User.Identity.Name.Split('@')[0];
                var worksheetPlanIds = model.WorksheetPlanIds;


                //get the costs
                var worksheetPlanActsToUpdate = (from c in _context.AsmHeaders.Where(x => x.IsActive == true && x.MasterOptionId == 1 && (x.AssemblyDesc.ToLower().Contains("base house") || x.AssemblyCode.Contains("A0000000")))
                                                 join d in _context.AsmDetails.Where(x => x.IsActive == true) on c.AsmHeaderId equals d.AsmHeaderId
                                                 join l in _context.MasterItems.Where(x => x.IsActive == true) on d.MasterItemId equals l.MasterItemId
                                                 join f in _context.MasterItemPhases.Where(x => x.IsActive == true) on l.MasterItemPhaseId equals f.MasterItemPhaseId
                                                 join m in _context.BomClasses.Where(x => x.IsActive == true) on l.BomClassId equals m.BomClassId
                                                 join k in _context.MasterPlans on c.MasterPlanId equals k.MasterPlanId
                                                 join p in _context.PhasePlans.Where(x => x.IsActive == true) on k.MasterPlanId equals p.MasterPlanId
                                                 join a in _context.WorksheetPlans.Where(x => x.IsActive == true && worksheetPlanIds.Contains(x.WorksheetPlanId)) on p.PhasePlanId equals a.PhasePlanId
                                                 join n in _context.Pactivities.Where(x => x.DivId == 1 && x.IsActive == true) on m.BomClassId equals n.BomClassId
                                                 join o in _context.Trades.Where(x => x.IsActive == true) on n.TradeId equals o.TradeId
                                                 from q in _context.PactivityAreaSuppliers.Where(x => x.IsActive == true && x.SubdivisionId == p.SubdivisionId && x.PactivityId == n.PactivityId).DefaultIfEmpty()
                                                 from s in _context.Costs.Include(x => x.CostsHistories).Where(x => x.IsActive == true && x.SubdivisionId == q.SubdivisionId && x.SubNumber == q.SubNumber && x.MasterItemId == l.MasterItemId).DefaultIfEmpty()
                                                 from u in _context.Costs.Include(x => x.CostsHistories).Where(x => x.IsActive == true && x.MasterItemId == l.MasterItemId && x.SubNumber == q.SubNumber && (x.SubdivisionId == 1)).DefaultIfEmpty() 
                                                 join t in _context.WorksheetPlanActs.Where(x => x.IsActive == true && worksheetPlanIds.Contains(x.WorksheetPlanId)) on new { a.WorksheetPlanId, subnum = (int?)q.SubNumber, n.Activity } equals new { t.WorksheetPlanId, subnum = (int?)t.SubNumber, t.Activity }
                                                 select new
                                                 {
                                                     SubdivSubdiv = p.SubdivisionId,
                                                     SubdivisionCosts = s != null ? s.SubdivisionId : (int?)null,
                                                     SubdivTrade = q != null ? q.SubdivisionId : (int?)null,
                                                     SubNumber = q != null ? q.SubNumber : (int?)null,
                                                     Activity = n.Activity,
                                                     MasterItemId = l.MasterItemId,
                                                     ItemNumber = l.ItemNumber,
                                                     ItemDesc = l.ItemDesc,
                                                     PhaseCode = f.PhaseCode,
                                                     WorksheetId = a.WorksheetId,                                               
                                                     WorksheetPlanId = a.WorksheetPlanId,
                                                     WorksheetPlanActId = t.WorksheetPlanActId,
                                                     CostsId = s != null ? s.CostsId : u != null ? u.CostsId : (int?)null,
                                                     Costprice = s == null ? (u != null ? (u.LastCost3Expired != null && model.RepriceDate < u.LastCost3Expired.Value.Date) ? (u.CostsHistories != null && u.CostsHistories.Count != 0 && (u.CostsHistories.Where(x => x.CostExpired != null && model.RepriceDate < x.CostExpired.Value.Date).ToList().Count) != 0 ? u.CostsHistories.FirstOrDefault(y => y.CostExpired == u.CostsHistories.Where(x => x.CostExpired != null && model.RepriceDate < x.CostExpired.Value.Date).Max(x => x.CostExpired)).OldCost : u.LastCost3)
                                           : ((u.LastCost2Expired != null && model.RepriceDate < u.LastCost2Expired.Value.Date) ? u.LastCost2 : ((u.LastCostExpired != null && model.RepriceDate < u.LastCostExpired.Value.Date) ? u.LastCost1 : ((u.NextCostDue == null || model.RepriceDate < u.NextCostDue.Value.Date) ? u.UnitCost : (u.NextCost2Due == null || model.RepriceDate < u.NextCost2Due.Value.Date ? u.NextCost : u.NextCost2)))) : null) :
                                           (s.LastCost3Expired != null && model.RepriceDate < s.LastCost3Expired.Value.Date) ? (s.CostsHistories != null && s.CostsHistories.Count != 0 && s.CostsHistories.Where(x => x.CostExpired != null && model.RepriceDate < x.CostExpired.Value.Date).ToList().Count != 0 ? s.CostsHistories.FirstOrDefault(y => y.CostExpired == s.CostsHistories.Where(x => x.CostExpired != null && model.RepriceDate < x.CostExpired.Value.Date).Max(x => x.CostExpired)).OldCost : s.LastCost3) : ((s.LastCost2Expired != null && model.RepriceDate < s.LastCost2Expired.Value.Date) ? s.LastCost2 : ((s.LastCostExpired != null && model.RepriceDate < s.LastCostExpired.Value.Date) ? s.LastCost1 : ((s.NextCostDue == null || model.RepriceDate < s.NextCostDue.Value.Date) ? s.UnitCost : (s.NextCost2Due == null || model.RepriceDate < s.NextCost2Due.Value.Date ? s.NextCost : s.NextCost2)))),//if no subdivision cost, use division default one, if none, then null 
                                                     Errors = ((s == null && u == null) || (s.CostsId == 0 && u.CostsId == null)) ? 1 : 0, //TODO: no supplier error on q is null
                                                     ItemQuantity = d.Factor
                                                 }).ToList();

                //group by activity, sum items in act
                var planActsToUpdate = worksheetPlanActsToUpdate.GroupBy(x => x.WorksheetPlanActId).Select(x => new WorksheetPlanAct()
                {
                    IsActive = true,
                    SubNumber = x.First().SubNumber,
                    Activity = x.First().Activity,
                    Costprice = (double?)x.Sum(y => (y.Costprice * y.ItemQuantity)),
                    Lumpsum = "F",
                    WorksheetPlanId = x.First().WorksheetPlanId,
                    WorksheetPlanActId = x.Key,
                    Errors = x.Sum(y => y.Errors),
                    Warnings = 0,
                    UpdatedBy = updatedBy,
                    UpdatedDateTime = DateTime.Now
                }).ToList();

                await _context.WorksheetPlanActs.BulkUpdateAsync(planActsToUpdate, options => options.ColumnInputExpression = x => new { x.IsActive, x.Costprice, x.Activity, x.Lumpsum, x.Warnings, x.Errors, x.SubNumber, x.UpdatedDateTime, x.UpdatedBy } );

                //deactivate any existing error logs
                var findErrorLogs = (from a in worksheetPlanActsToUpdate
                                    join b in _context.WorksheetLogs.Where(x => x.IsActive == true) on new { worksheetId = a.WorksheetId, activity = (string?)a.Activity, planId = (int?)a.WorksheetPlanId} equals new { worksheetId = b.WorksheetId, activity = b.Activity, planId = b.PlanOptId }
                                    select new WorksheetLog()
                                    {
                                        WorksheetLogId = b.WorksheetLogId,
                                        IsActive = false,
                                        UpdatedDateTime = DateTime.Now,
                                        UpdatedBy = updatedBy
                                    }).ToList();
                await _context.WorksheetLogs.BulkUpdateAsync(findErrorLogs, options => options.ColumnInputExpression = c => new { c.IsActive, c.UpdatedBy, c.UpdatedDateTime});

                //add new error logs
                var insertErrorLog = worksheetPlanActsToUpdate.Where(x => x.Errors != 0).Select(x => new WorksheetLog()
                {
                    WorksheetId = x.WorksheetId,
                    LogType = "B",//O = option, B = Base house
                    PlanOptId = x.WorksheetPlanId,//for option, fill worksheet opt id, for base house, fill plan opt id
                    Activity = x.Activity,
                    PhaseCode = x.PhaseCode,
                    ItemNumber = x.ItemNumber,
                    ItemDesc = x.ItemDesc,
                    ErrorCode = 4,
                    ErrorDesc = "No Cost Found",
                    CreatedBy = updatedBy,//seems not filling correctly
                    UserStamp = updatedBy,
                    CreatedDateTime = DateTime.Now,
                }).ToList();
                await _context.BulkInsertAsync(insertErrorLog);

                //sum the new sum to the plans
                var result4 = await SumWorksheetPlanActs(worksheetPlanIds, model.RepriceDate);
                var response = new ResponseModel<RepriceWorksheetPlansModel>() { IsSuccess = true, Value = model, Message = "Plans are repriced" };
                return Ok(response);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                var response = new ResponseModel<RepriceWorksheetPlansModel>() { IsSuccess = false, Value = model, Message ="Failed to reprice plan" };
                return Ok(response);
            }
        }
        [HttpPost]
        public async Task<IActionResult> AddPlansToWorksheetAsync([FromBody] List<WorksheetPlanModel> model)
        {
            try
            {
                //adds just the plan (base house)

                var planIds = new List<int>();
                foreach (var plan in model)
                {
                    //check if plan is already in the worksheet, add only if new
                    var checkPlanAlreadyInWorksheet = _context.WorksheetPlans.Where(x => x.WorksheetId == plan.WorksheetId && x.PhasePlanId == plan.PhasePlanId);
                    if (!checkPlanAlreadyInWorksheet.Any())
                    {
                        var planToAdd = new WorksheetPlan()
                        {
                            WorksheetId = plan.WorksheetId,
                            PhasePlanId = plan.PhasePlanId,
                            Costprice = plan.Costprice,
                            Sellprice = plan.Sellprice,
                            Marketvalue = plan.Marketvalue,
                            Markup = plan.Markup,
                            Markuppercent = plan.Markuppercent,
                            Pricedate = plan.Pricedate,
                            Markuptype = plan.Markuptype,
                            CreatedBy = User.Identity.Name.Split('@')[0]
                        };
                        _context.WorksheetPlans.Add(planToAdd);
                        await _context.SaveChangesAsync();
                        planIds.Add(planToAdd.WorksheetPlanId);

                        //Add the plan activities with costs
                        await InsertPlanActs(planIds);
                        await SumWorksheetPlanActs(planIds);
                    }
                }

                return new OkObjectResult(new ResponseModel<List<WorksheetPlanModel>> { Value = model, IsSuccess = true, Message = "Plans added to Worksheet successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<WorksheetPlanModel>> { IsSuccess = false, Message = "Failed to add Plans to Worksheet", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> AddPlansWithOptionsToWorksheetAsync([FromBody] List<WorksheetPlanModel> model)
        {
            try
            {
                //add the plan and all the options in the plan
                string updatedBy = User.Identity.Name.Split('@')[0];
                foreach (var plan in model)
                {
                    var planIds = new List<int>();
                    int worksheetPlanId = 0;
                    //check if plan is already in the worksheet, add only if new
                    var checkPlanAlreadyInWorksheet = _context.WorksheetPlans.Where(x => x.WorksheetId == plan.WorksheetId && x.PhasePlanId == plan.PhasePlanId);
                    if (!checkPlanAlreadyInWorksheet.Any())
                    {
                        var planToAdd = new WorksheetPlan()
                        {
                            WorksheetId = plan.WorksheetId,
                            PhasePlanId = plan.PhasePlanId,
                            Costprice = plan.Costprice,
                            Sellprice = plan.Sellprice,
                            Marketvalue = plan.Marketvalue,
                            Markup = plan.Markup,
                            Markuppercent = plan.Markuppercent,
                            Pricedate = plan.Pricedate,
                            Markuptype = plan.Markuptype,
                            CreatedBy = User.Identity.Name.Split('@')[0]
                        };
                        _context.WorksheetPlans.Add(planToAdd);
                        await _context.SaveChangesAsync();
                        worksheetPlanId = planToAdd.WorksheetPlanId;
                        planIds.Add(planToAdd.WorksheetPlanId);
                        //Add the plan activities with costs
                        await InsertPlanActs(planIds);
                        await SumWorksheetPlanActs(planIds);
                    }
                    else
                    {
                        worksheetPlanId = checkPlanAlreadyInWorksheet.First().WorksheetPlanId;
                    }
                    //find all the options, and add them if they don't exist
                    var modelOptionsThisPlan = _context.AvailablePlanOptions.Where(x => x.PhasePlanId == plan.PhasePlanId && x.IsActive == true).Select(x => new WorksheetOpt()
                    {
                        IsActive = true,
                        WorksheetPlanId = worksheetPlanId,
                        Pricedate = DateTime.Now,
                        PlanOptionId = x.PlanOptionId,
                        Markuptype = x.MarginType,
                        Markuppercent = x.MarginPercent,
                        Marketvalue = x.MarginMarketValue,
                        CreatedBy = updatedBy
                    }).ToList();

                    var planOptIds = modelOptionsThisPlan.Select(x => x.PlanOptionId).ToList();
                    var optExistsInWorksheet = _context.WorksheetOpts.Where(x => x.WorksheetPlanId == worksheetPlanId && planOptIds.Contains(x.PlanOptionId) && x.IsActive == true).Select(x => x.PlanOptionId).ToList();
                    var modelOptionsToAddThisPlan = modelOptionsThisPlan.Where(x => !optExistsInWorksheet.Contains(x.PlanOptionId)).ToList();
                    await _context.WorksheetOpts.BulkInsertAsync(modelOptionsToAddThisPlan);

                    var worksheetOptIds = modelOptionsToAddThisPlan.Select(x => x.WorksheetOptId).ToList();
                    var success = await InsertOptActs(worksheetOptIds);
                    success = await SumWorksheetOptActs(worksheetOptIds);//this will sum the cost of the activities to create the option cost

                }

                return new OkObjectResult(new ResponseModel<List<WorksheetPlanModel>> { Value = model, IsSuccess = true, Message = "Plans added to Worksheet successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<WorksheetPlanModel>> { IsSuccess = false, Message = "Failed to add Plans to Worksheet", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> AddOptionsToWorksheetAsync([FromBody] List<WorksheetOptModel> model)
        {
            try
            {
                var createdBy = User.Identity.Name.Split('@')[0];
                var planIds = new List<int>();
                var worksheetId = model.FirstOrDefault().WorksheetId;
                var distintPlansInModel = model.Where(x => x.PhasePlanId != null).Select(x => x.PhasePlanId).Distinct().ToList();
                foreach (var planId in distintPlansInModel)
                {
                    int worksheetPlanId;
                    //check if plan is already in the worksheet   
                    var checkInWorksheet = _context.WorksheetPlans.Where(x => x.PhasePlanId == planId && x.WorksheetId == worksheetId && x.IsActive == true).FirstOrDefault();
                    if (checkInWorksheet == null)
                    {
                        //Add the plan if it isn't already there
                        var planToAdd = new WorksheetPlan()
                        {
                            WorksheetId = worksheetId,
                            PhasePlanId = (int)planId,
                            Pricedate = DateTime.Now,
                            CreatedBy = createdBy
                        };
                        _context.WorksheetPlans.Add(planToAdd);
                        await _context.SaveChangesAsync();
                        worksheetPlanId = planToAdd.WorksheetPlanId;
                        var worksheetPlanIds = new List<int> { worksheetPlanId };
                        var result1 = await InsertPlanActs(worksheetPlanIds);//For base house
                        var result2 = await SumWorksheetPlanActs(worksheetPlanIds);
                    }
                    else
                    {
                        worksheetPlanId = checkInWorksheet.WorksheetPlanId;
                    }

                    //add the options selected that go with that plan 
                    var findPlanOptionIds = model.Where(x => x.PhasePlanId == planId).Select(x => x.PlanOptionId).Distinct().ToList();
                    var modelOptionsThisPlan = _context.AvailablePlanOptions.Where(x => findPlanOptionIds.Contains(x.PlanOptionId)).Select(x => new WorksheetOpt()
                    {
                        IsActive = true,
                        WorksheetPlanId = worksheetPlanId,
                        Pricedate = DateTime.Now,
                        PlanOptionId = x.PlanOptionId,
                        Markuptype = x.MarginType,
                        Markuppercent = x.MarginPercent,
                        Marketvalue = x.MarginMarketValue,
                        CreatedBy = createdBy
                    }).ToList();
                   
                    var planOptIds = modelOptionsThisPlan.Select(x => x.PlanOptionId).ToList();
                    var optExistsInWorksheet = _context.WorksheetOpts.Where(x => x.WorksheetPlanId == worksheetPlanId && planOptIds.Contains(x.PlanOptionId) && x.IsActive == true).Select(x => x.PlanOptionId).ToList();
                    var modelOptionsToAddThisPlan = modelOptionsThisPlan.Where(x => !optExistsInWorksheet.Contains(x.PlanOptionId)).ToList();
                    await _context.WorksheetOpts.BulkInsertAsync(modelOptionsToAddThisPlan);

                    var worksheetOptIds = modelOptionsToAddThisPlan.Select(x => x.WorksheetOptId).ToList();
                    var success = await InsertOptActs(worksheetOptIds);
                    success = await SumWorksheetOptActs(worksheetOptIds);//this will sum the cost of the activities to create the option cost
                }
                return new OkObjectResult(new ResponseModel<List<WorksheetOptModel>> { Value = model, IsSuccess = true, Message = "Options added to Worksheet Plan successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<WorksheetOptModel>> { IsSuccess = false, Message = "Failed to add Option to Worksheet Plan", Value = null });
            }
        }
        private async Task<bool> SumWorksheetOptActs(List<int> worksheetOptIds, DateTime? priceDate = null)
        {
            try
            {
                var updateBy = User.Identity.Name.Split('@')[0];
                var updateOpts = new List<WorksheetOpt>();
                var optsToUpdate = _context.WorksheetOptActs.Include(x => x.WorksheetOpt).Where(x => worksheetOptIds.Contains(x.WorksheetOptId) && x.IsActive == true).GroupBy(x => x.WorksheetOptId).ToList();
                foreach(var opt in optsToUpdate)
                {
                    updateOpts.Add(new WorksheetOpt()
                    {
                        IsActive = true,
                        WorksheetOptId = opt.First().WorksheetOptId,
                        PlanOptionId = opt.First().WorksheetOpt.PlanOptionId,
                        WorksheetPlanId = opt.First().WorksheetOpt.WorksheetPlanId,
                        Pricedate = priceDate ?? DateTime.Now,
                        Costprice = opt.Sum(c => c.Costprice),
                        Marketvalue = opt.First().WorksheetOpt.Markuptype == 1 ? opt.First().WorksheetOpt.Marketvalue : opt.First().WorksheetOpt.Markuptype == 0 ? RoundUpToTen(((opt.Sum(c => c.Costprice)) * (1 + opt.First().WorksheetOpt.Markuppercent / 100))) : 0,//TODO: markup type 2?  
                        CreatedDateTime = opt.First().WorksheetOpt.CreatedDateTime,
                        CreatedBy = opt.First().WorksheetOpt.CreatedBy,
                        Markup = opt.First().WorksheetOpt.Markuptype == 1 ? opt.First().WorksheetOpt.Marketvalue - (opt.Sum(c => c.Costprice)) : opt.First().WorksheetOpt.Markuptype == 0 ? (opt.Sum(c => c.Costprice)) * opt.First().WorksheetOpt.Markuppercent / 100 : 0,
                        Markuptype = opt.First().WorksheetOpt.Markuptype,
                        Markuppercent = opt.First().WorksheetOpt.Markuptype == 0 ? opt.First().WorksheetOpt.Markuppercent : null,//set default from planoptiontable if selected
                        Sellprice = opt.First().WorksheetOpt.Markuptype == 1 ? opt.First().WorksheetOpt.Marketvalue : opt.First().WorksheetOpt.Markuptype == 0 ? RoundUpToTen(((opt.Sum(c => c.Costprice)) * (1 + opt.First().WorksheetOpt.Markuppercent / 100))) : 0,//TODO: markup type 2?  
                        UpdatedBy = updateBy,
                        UpdatedDateTime = DateTime.Now
                    });
                }
                
                await _context.WorksheetOpts.BulkUpdateAsync(updateOpts, options => options.ColumnInputExpression = x => new { x.Pricedate, x.Costprice, x.Markuptype, x.Markuppercent, x.Marketvalue, x.Markup, x.Sellprice, x.UpdatedDateTime, x.UpdatedBy });
            }
            catch (Exception ex)
            {
                var test = ex.Message;
                throw;
            }
            return true;
        }
        private double RoundUpToTen(double? inputNum)
        {
            return inputNum == null ? 0 : (Math.Ceiling((double)inputNum / 10)) * 10;
        }
        private async Task<bool> SumWorksheetPlanActs(List<int> worksheetPlanIds, DateTime? priceDate = null)
        {
            try
            {
                var updateBy = User.Identity.Name.Split('@')[0];
                var updatePlans = new List<WorksheetPlan>();
                var plansToUpdate = _context.WorksheetPlanActs.Include(x => x.WorksheetPlan).Where(x => worksheetPlanIds.Contains(x.WorksheetPlanId) && x.IsActive == true).GroupBy(x => x.WorksheetPlanId).ToList();
                foreach (var plan in plansToUpdate)
                {
                    updatePlans.Add(new WorksheetPlan()
                    {
                        IsActive = true,
                        WorksheetId = plan.First().WorksheetPlan.WorksheetId,
                        PhasePlanId = plan.First().WorksheetPlan.PhasePlanId,
                        WorksheetPlanId = plan.First().WorksheetPlan.WorksheetPlanId,
                        Pricedate = priceDate ?? DateTime.Now,
                        Costprice = plan.Sum(c => c.Costprice),
                        Marketvalue = plan.First().WorksheetPlan.Markuptype == 1 ? plan.First().WorksheetPlan.Marketvalue : plan.First().WorksheetPlan.Markuptype == 0 ? RoundUpToTen((plan.Sum(c => c.Costprice)) * (1 + plan.First().WorksheetPlan.Markuppercent / 100)) : 0,//TODO: markup type 2
                        Markup = plan.First().WorksheetPlan.Markuptype == 1 ? plan.First().WorksheetPlan.Marketvalue - (plan.Sum(c => c.Costprice)) : plan.First().WorksheetPlan.Markuptype == 0 ? (plan.Sum(c => c.Costprice)) * plan.First().WorksheetPlan.Markuppercent / 100 : 0,
                        Markuptype = plan.First().WorksheetPlan.Markuptype,
                        Markuppercent = plan.First().WorksheetPlan.Markuptype == 0 ? plan.First().WorksheetPlan.Markuppercent : null,
                        Sellprice = plan.First().WorksheetPlan.Markuptype == 1 ? plan.First().WorksheetPlan.Marketvalue : plan.First().WorksheetPlan.Markuptype == 0 ? RoundUpToTen((plan.Sum(c => c.Costprice)) * (1 + plan.First().WorksheetPlan.Markuppercent / 100)) : 0,//TODO: markup type 2
                        UpdatedBy = updateBy,
                        UpdatedDateTime = DateTime.Now
                    });
                }

                await _context.WorksheetPlans.BulkUpdateAsync(updatePlans, options => options.ColumnInputExpression = x => new { x.Pricedate, x.Costprice, x.Markuptype, x.Markuppercent, x.Marketvalue, x.Sellprice, x.UpdatedDateTime, x.UpdatedBy });               
            }
            catch (Exception ex)
            {
                var test = ex.Message;
                throw;
            }
            return true;
        }                      
        private async Task<bool> InsertOptActs(List<int> worksheetOptIds)
        {
            try
            {
                if (worksheetOptIds == null || worksheetOptIds.Count == 0)
                {
                    return true;
                }           
                var optActsToInsert1 = (from a in _context.WorksheetOpts.Where(x => x.IsActive == true && worksheetOptIds.Contains(x.WorksheetOptId))
                                        join v in _context.WorksheetPlans.Where(x => x.IsActive == true) on a.WorksheetPlanId equals v.WorksheetPlanId
                                     join b in _context.AvailablePlanOptions.Where(x => x.IsActive == true) on a.PlanOptionId equals b.PlanOptionId
                                     join z in _context.MasterPlans.Where(x => x.IsActive == true) on b.MasterPlanId equals z.MasterPlanId
                                     join c in _context.AsmHeaders.Where(x => x.IsActive == true) on new { mo = b.MasterOptionId ?? 0, mp = b.MasterPlanId ?? 0 } equals new { mo = c.MasterOptionId ?? 0, mp = c.MasterPlanId ?? 0}
                                     join d in _context.AsmDetails.Where(x => x.IsActive == true) on c.AsmHeaderId equals d.AsmHeaderId
                                     join l in _context.MasterItems.Where(x => x.IsActive == true) on d.MasterItemId equals l.MasterItemId
                                     join y in _context.MasterItemPhases.Where(x => x.IsActive == true) on l.MasterItemPhaseId equals y.MasterItemPhaseId
                                     join m in _context.BomClasses.Where(x => x.IsActive == true) on l.BomClassId equals m.BomClassId
                                     join n in _context.Pactivities.Where(x => x.DivId == 1 && x.IsActive == true) on m.BomClassId equals n.BomClassId
                                     join r in _context.PhasePlans.Where(x => x.IsActive == true) on b.PhasePlanId equals r.PhasePlanId
                                     from q in _context.PactivityAreaSuppliers.Where(x => x.IsActive == true &&  x.SubdivisionId == r.SubdivisionId && x.PactivityId == n.PactivityId).DefaultIfEmpty()
                                     from s in _context.Costs.Where(x => x.IsActive == true && x.SubdivisionId == q.SubdivisionId && x.SubNumber == q.SubNumber && x.MasterItemId == l.MasterItemId).DefaultIfEmpty()
                                     from u in _context.Costs.Where(x => x.IsActive == true && x.MasterItemId == l.MasterItemId && x.SubNumber == q.SubNumber && (x.SubdivisionId == 1)).DefaultIfEmpty()
                                        select new
                                     {
                                         SubdivSubdiv = r.SubdivisionId,
                                         SubdivisionCosts = s != null ? s.SubdivisionId : (int?)null,
                                         SubdivisionTrade = q != null ? q.SubdivisionId : (int?)null,
                                         SubNumber =  q != null ? q.SubNumber : (int?)null,
                                         Activity = n.Activity,
                                         MasterItemId = l.MasterItemId,
                                         ItemNumber = l.ItemNumber,
                                         ItemDesc = l.ItemDesc,
                                         PhaseCode = y.PhaseCode,
                                         WorksheetOptId = a.WorksheetOptId,
                                         WorksheetId = v.WorksheetId,
                                         PlanOptionId = a.PlanOptionId,
                                         Lumpsum = "F",
                                         IsSubdivision = s !=null ? true : false,
                                         DivisionExists = u != null ? true: false,
                                         IsSubdivAndDivision = u != null && s!=null ? true : false,
                                         CostsId = s != null ? s.CostsId : u != null ? u.CostsId : (int?)null,
                                         Costprice = s == null ? (u != null ? u.UnitCost * d.Factor : null) : s.UnitCost * d.Factor,  //if no subdivision cost, use division default one, if none, then null // instead of the unit cost, get the cost by date 
                                         Errors = ((s == null && u == null) || (s.CostsId == 0 && u.CostsId == 0)) ? 1 : 0,//TODO: no supplier error on q is null
                                         ErrorReason = q == null || q.SubNumber == 0 || q.SubNumber == null ? "no supplier" : ((s == null && u == null)) ? "no cost" : "no error",//TODO: fix error reason
                                         ItemQuantity = d.Factor
                                        }).ToList();
                var createBy = User.Identity.Name.Split('@')[0];
                var groupedOptions = optActsToInsert1.GroupBy(x => new { x.WorksheetOptId, x.Activity }).GroupBy(x => x.Key.WorksheetOptId).ToList();
                var newOptActs = new List<WorksheetOptAct>();
                foreach(var option in groupedOptions)
                {
                    var newActs = option.Select(activity => new WorksheetOptAct()
                    {
                        WorksheetOptId = option.Key,
                        SubNumber = activity.First().SubNumber,
                        Activity = activity.Key.Activity,
                        Costprice = activity.Sum(y => (y.Costprice * y.ItemQuantity)),
                        CreatedBy = createBy,
                        Warnings = 0,
                        Errors = activity.Sum(y => y.Errors),
                        Lumpsum = "F",//set false initially, will change if user enters a cost for an activity directly in worksheet
                        IsActive = true
                    }).ToList();
                    newOptActs.AddRange(newActs);
                }               
                await _context.WorksheetOptActs.BulkInsertAsync(newOptActs);

                var insertErrorLog = optActsToInsert1.Where(x => x.Errors != 0).Select(x => new WorksheetLog()
                {
                    WorksheetId = x.WorksheetId,
                    LogType = "O",//O = option, B = Base house
                    PlanOptId = x.WorksheetOptId,//for option, fill worksheet opt id, for base house, fill plan opt id
                    Activity = x.Activity,
                    PhaseCode = x.PhaseCode,
                    ItemNumber = x.ItemNumber,
                    ItemDesc = x.ItemDesc,
                    ErrorCode = 4, 
                    ErrorDesc = "No Cost Found",
                    CreatedBy = createBy,//seems not filling correctly
                    UserStamp = createBy,
                    CreatedDateTime = DateTime.Now,                    
                }).ToList();
                await _context.BulkInsertAsync(insertErrorLog);

            }
            catch (Exception ex)
            {
                var test = ex.Message;
                throw;
            }
            return true;
        }
        private async Task<bool> InsertPlanActs(List<int> worksheetPlanIds)
        {
            try
            {
                if (worksheetPlanIds == null || worksheetPlanIds.Count == 0)
                {
                    return true;
                }
                //TODO: put in no supplier error
                var createBy = User.Identity.Name.Split('@')[0];
                var planActsToInsert = (from a in _context.WorksheetPlans.Where(x => x.IsActive == true && worksheetPlanIds.Contains(x.WorksheetPlanId))
                                        join v in _context.WorksheetPlans.Where(x => x.IsActive == true) on a.WorksheetPlanId equals v.WorksheetPlanId
                                        join p in _context.PhasePlans.Where(x => x.IsActive == true) on a.PhasePlanId equals p.PhasePlanId
                                        join c in _context.AsmHeaders.Where(x => x.IsActive == true && (x.AssemblyDesc.ToLower().Contains("base house") || x.AssemblyCode.Contains("A0000000")) && x.MasterOptionId == 1) on p.MasterPlanId equals c.MasterPlanId                                       
                join d in _context.AsmDetails.Where(x => x.IsActive == true) on c.AsmHeaderId equals d.AsmHeaderId
                join l in _context.MasterItems.Where(x => x.IsActive == true) on d.MasterItemId equals l.MasterItemId
                                        join y in _context.MasterItemPhases.Where(x => x.IsActive == true) on l.MasterItemPhaseId equals y.MasterItemPhaseId
                join m in _context.BomClasses.Where(x => x.IsActive == true) on l.BomClassId equals m.BomClassId
                join n in _context.Pactivities.Where(x => x.DivId == 1 && x.IsActive == true) on m.BomClassId equals n.BomClassId
                from q in _context.PactivityAreaSuppliers.Where(x => x.IsActive == true && x.SubdivisionId == p.SubdivisionId && x.PactivityId == n.PactivityId).DefaultIfEmpty()
                from s in _context.Costs.Where(x => x.IsActive == true && x.SubNumber == q.SubNumber && x.MasterItemId == l.MasterItemId && (x.SubdivisionId == q.SubdivisionId)).DefaultIfEmpty()
                from u in _context.Costs.Where(x => x.IsActive == true && x.MasterItemId == l.MasterItemId && x.SubNumber == q.SubNumber && (x.SubdivisionId == 1)).DefaultIfEmpty()
                                        select new                
                                        {
                                    
                                            SubdivSubdiv = p.SubdivisionId,
                                            SubdivisionCosts = s != null ? s.SubdivisionId : (int?)null,
                                            SubdivisionTrade = s != null ? q.SubdivisionId : (int?)null,
                                            SubNumber = q != null ?  q.SubNumber : (int?)null,
                                            Activity = n.Activity,
                                            MasterItemId = l.MasterItemId,
                                            ItemNumber = l.ItemNumber,
                                            ItemDesc = l.ItemDesc,
                                            PhaseCode = y.PhaseCode,
                                            WorksheetId = v.WorksheetId,
                                            WorksheetPlanId = a.WorksheetPlanId,
                                            Lumpsum = "F",
                                            CostsId = s != null ? s.CostsId : u != null ? u.CostsId : (int?)null,
                                            Costprice = s == null ? (u != null ? u.UnitCost : null) : s.UnitCost,  //if no subdivision cost, use division default one, if none, then null 
                                            Errors = ((s == null && u == null) || (s.CostsId == 0 && u.CostsId == 0)) ? 1 : 0,//TODO: no supplier error 
                                            ErrorReason = q == null || q.SubNumber == 0 || q.SubNumber == null ? "no supplier" : (s == null && u == null) ? "no cost" : "no error",
                                            ItemQuantity = d.Factor
                                        }).ToList();
                var groupedOptions = planActsToInsert.GroupBy(x => new { x.WorksheetPlanId, x.Activity }).GroupBy(x => x.Key.WorksheetPlanId).ToList();
                var newPlanActs = new List<WorksheetPlanAct>();
                foreach (var option in groupedOptions)
                {
                    var newActs = option.Select(activity => new WorksheetPlanAct()
                    {
                        WorksheetPlanId = option.Key,
                        SubNumber = activity.First().SubNumber,
                        Activity = activity.Key.Activity,
                        Costprice = activity.Sum(y => (y.Costprice * y.ItemQuantity)),
                        CreatedBy = createBy,
                        Warnings = 0,//Lump sum warning to be set later if they update a cost
                        Errors = activity.Sum(y => y.Errors),// TODO: NEEDS to be a no supplier assigned error 
                        Lumpsum = "F",
                        IsActive = true
                    }).ToList();
                    newPlanActs.AddRange(newActs);
                }
                await _context.WorksheetPlanActs.BulkInsertAsync(newPlanActs);
                var insertErrorLog = planActsToInsert.Where(x => x.Errors != 0).Select(x => new WorksheetLog()
                {
                    WorksheetId = x.WorksheetId,
                    LogType = "B", //B = Base house, O = option
                    PlanOptId = x.WorksheetPlanId,//worksheet plan id for base house plan
                    Activity = x.Activity,
                    PhaseCode = x.PhaseCode,
                    ItemNumber = x.ItemNumber,
                    ItemDesc = x.ItemDesc,
                    ErrorCode = 4,
                    ErrorDesc = "No Cost Found",
                    CreatedBy = createBy,
                    UserStamp = createBy,
                    CreatedDateTime = DateTime.Now,
                }).ToList();
                await _context.WorksheetLogs.BulkInsertAsync(insertErrorLog);
            }
            catch (Exception ex)
            {
                var test = ex.Message;
                throw;
            }
            return true;
        }

        [HttpPut]
        public async Task<IActionResult> UpdatePricingAsync([FromBody] List<AvailablePlanOptionDto> options)
        {
            try
            {
                var optionPricesHistory = new List<PlanOptionPriceHist>();
                foreach (var option in options)
                {
                    var newHistory = new PlanOptionPriceHist()
                    {
                        PlanOptionId = option.PlanOptionId,
                        OptionCode = option.OptionCode,
                        ModifiedOptionDesc = option.ModifiedOptionDesc,
                        SellingPrice = option.CurrSelling,
                        SellingPriceStartDate = option.CurrDate,
                        SellingPriceEndDate = option.NextDate,
                        CreatedBy = User.Identity.Name.Split('@')[0]
                    };

                    optionPricesHistory.Add(newHistory);
                }
                await _context.PlanOptionPriceHists.BulkInsertAsync(optionPricesHistory);
                var response = new ResponseModel<string>() { IsSuccess = true, Value = string.Empty, Message = "Updated PlanOptionHist table" };
                return Ok(response);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<string>() { IsSuccess = false, Value = string.Empty, Message = "Failed to update PlanOptionHist table" });
            }
        }
        
        [HttpPut]
        public async Task<IActionResult> ApplyPricingAsync([FromBody] ApplyPricingModel applyPricing)
        {
            try
            {
                //update the options (not the base house)
                var optionsToUpdate = _context.WorksheetOpts.Include(x => x.PlanOption).Where(x => x.WorksheetPlan.WorksheetId == applyPricing.WorksheetId && x.IsActive == true && x.WorksheetPlan.IsActive == true).Select(x => new AvailablePlanOption()
                {
                    PlanOptionId = x.PlanOptionId,
                    LastCost = applyPricing.ApplyAsCurrent ? x.PlanOption.CurrCost:  x.PlanOption.LastCost,//if apply as current, roll current to last, else keep last
                    LastSelling = applyPricing.ApplyAsCurrent ? x.PlanOption.CurrSelling : x.PlanOption.LastSelling,
                    LastDate = applyPricing.ApplyAsCurrent ? x.PlanOption.CurrDate : x.PlanOption.LastDate,
                    LastMargin = applyPricing.ApplyAsCurrent ? x.PlanOption.CurrMargin :x.PlanOption.LastMargin,
                    CurrSelling = applyPricing.ApplyAsCurrent ? x.Sellprice: x.PlanOption.CurrSelling,
                    CurrMargin = applyPricing.ApplyAsCurrent? x.Sellprice - x.Costprice : x.PlanOption.CurrMargin,
                    CurrCost = applyPricing.ApplyAsCurrent ? x.Costprice: x.PlanOption.CurrCost,
                    CurrDate = applyPricing.ApplyAsCurrent ? DateTime.Now : x.PlanOption.CurrDate,
                    NextCost = applyPricing.ApplyAsCurrent ? null : x.Costprice,
                    NextDate = applyPricing.ApplyAsCurrent ? null: applyPricing.PriceDate,
                    NextSelling = applyPricing.ApplyAsCurrent ? null: x.Sellprice,
                    NextMargin = applyPricing.ApplyAsCurrent ? null : x.Sellprice - x.Costprice,                  
                }).ToList();

                await _context.AvailablePlanOptions.BulkUpdateAsync(optionsToUpdate, options => options.ColumnInputExpression = x => new { x.LastSelling, x.LastCost, x.LastDate, x.LastMargin, x.CurrSelling, x.CurrMargin, x.CurrDate, x.CurrCost, x.NextCost, x.NextDate, x.NextMargin, x.NextSelling });

                //store the old price in the history table if applying as current
                if (applyPricing.ApplyAsCurrent)
                {
                    var findPlanOpts = _context.AvailablePlanOptions.Where(x => optionsToUpdate.Select(y => y.PlanOptionId).Contains(x.PlanOptionId)).ToList();
                    var insertHistory = findPlanOpts.Select(x => new PlanOptionPriceHist()
                    {
                        PlanOptionId = x.PlanOptionId,
                        OptionCode = x.OptionCode,
                        ModifiedOptionDesc = x.ModifiedOptionDesc,
                        SellingPrice = x.LastSelling,
                        SellingPriceStartDate = x.LastDate,
                        SellingPriceEndDate = DateTime.Now,             
                        CreatedBy = User.Identity.Name.Split('@')[0]
                    });
                    await _context.PlanOptionPriceHists.BulkInsertAsync(insertHistory);
                }
                else
                {
                    //no need to rollover and save history if saving as next, there will be a separate rollover function                   
                }

                //update the available plan options                
                //Don't update base house, pricing comes from elsewhere

                var response = new ResponseModel<ApplyPricingModel>() { IsSuccess = true, Value = applyPricing, Message = "Applied Pricing" };
                return Ok(response);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ApplyPricingModel>() { IsSuccess = false, Value = applyPricing, Message = "Apply Pricing failed" });
            }
        }
        [HttpPut]
        public async Task<IActionResult> ApplyPricingOptionAsync([FromBody] ApplyPricingModel applyPricingModel)
        {
            try
            {
                var optionIds = applyPricingModel.WorksheetData.Where(x => x.WorksheetOptionId != 1).Select(x => x.WorksheetOptionId).ToList();
                var optionsToUpdate = _context.WorksheetOpts.Include(x => x.PlanOption).Where(x => optionIds.Contains(x.WorksheetOptId) && x.IsActive == true && x.WorksheetPlan.IsActive == true).Select(x => new AvailablePlanOption()
                {
                    PlanOptionId = x.PlanOptionId,
                    LastCost = applyPricingModel.ApplyAsCurrent ? x.PlanOption.CurrCost : x.PlanOption.LastCost,//if apply as current, roll current to last, else keep last
                    LastSelling = applyPricingModel.ApplyAsCurrent ? x.PlanOption.CurrSelling : x.PlanOption.LastSelling,
                    LastDate = applyPricingModel.ApplyAsCurrent ? x.PlanOption.CurrDate : x.PlanOption.LastDate,
                    LastMargin = applyPricingModel.ApplyAsCurrent ? x.PlanOption.CurrMargin : x.PlanOption.LastMargin,
                    CurrSelling = applyPricingModel.ApplyAsCurrent ? x.Sellprice : x.PlanOption.CurrSelling,
                    CurrMargin = applyPricingModel.ApplyAsCurrent ? x.Sellprice - x.Costprice : x.PlanOption.CurrMargin,
                    CurrCost = applyPricingModel.ApplyAsCurrent ? x.Costprice : x.PlanOption.CurrCost,
                    CurrDate = applyPricingModel.ApplyAsCurrent ? DateTime.Now : x.PlanOption.CurrDate,
                    NextCost = applyPricingModel.ApplyAsCurrent ? null : x.Costprice,
                    NextDate = applyPricingModel.ApplyAsCurrent ? null : applyPricingModel.PriceDate,
                    NextSelling = applyPricingModel.ApplyAsCurrent ? null : x.Sellprice,
                    NextMargin = applyPricingModel.ApplyAsCurrent ? null : x.Sellprice - x.Costprice,
                }).ToList();

                await _context.AvailablePlanOptions.BulkUpdateAsync(optionsToUpdate, options => options.ColumnInputExpression = x => new { x.LastSelling, x.LastCost, x.LastDate, x.LastMargin, x.CurrSelling, x.CurrMargin, x.CurrDate, x.CurrCost, x.NextCost, x.NextDate, x.NextMargin, x.NextSelling });

                //store the old price in the history table if applying as current
                if (applyPricingModel.ApplyAsCurrent)
                {
                    var findPlanOpts = _context.AvailablePlanOptions.Where(x => optionsToUpdate.Select(y => y.PlanOptionId).Contains(x.PlanOptionId)).ToList();
                    var insertHistory = findPlanOpts.Select(x => new PlanOptionPriceHist()
                    {
                        PlanOptionId = x.PlanOptionId,
                        OptionCode = x.OptionCode,
                        ModifiedOptionDesc = x.ModifiedOptionDesc,
                        SellingPrice = x.LastSelling,
                        SellingPriceStartDate = x.LastDate,
                        SellingPriceEndDate = DateTime.Now,
                        CreatedBy = User.Identity.Name.Split('@')[0]
                    });
                    await _context.PlanOptionPriceHists.BulkInsertAsync(insertHistory);
                }
                else
                {
                    //no need to rollover and save history if saving as next, there will be a separate rollover function                   
                }

                return Ok(new ResponseModel<ApplyPricingModel>() { IsSuccess = true, Value = applyPricingModel, Message = "Apply Pricing succeeded" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ApplyPricingModel>() { IsSuccess = false, Value = applyPricingModel, Message = "Apply Pricing failed" });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeleteWorksheetAsync([FromBody] WorksheetDto model)
        {
            try
            {
                var findWorksheet = _context.Worksheets.SingleOrDefault(x => x.WorksheetId == model.WorksheetId);
                findWorksheet.UpdatedBy = User.Identity.Name.Split('@')[0];
                findWorksheet.IsActive = false;
                findWorksheet.UpdatedDateTime = DateTime.Now;
                _context.Worksheets.Update(findWorksheet);
                await _context.SaveChangesAsync();

                //deactivate the plans in the worksheet
                var findPlans = _context.WorksheetPlans.Where(x => x.WorksheetId == model.WorksheetId).Select(x => new WorksheetPlanModel() { WorksheetPlanId = x.WorksheetPlanId }).ToList();
                await DeleteWorksheetPlanAsync(findPlans);

                //deactivate the plans acts in the worksheet
                var planIds = findPlans.Select(x => x.WorksheetPlanId).ToList();
                var findPlanActs = _context.WorksheetPlanActs.Where(x => planIds.Contains(x.WorksheetPlanId)).Select(x => new WorksheetPlanAct() { WorksheetPlanActId = x.WorksheetPlanActId }).ToList();
                await DeleteWorksheetPlanActAsync(findPlanActs);

                //deactivate the opts
                var findOpts = _context.WorksheetOpts.Where(x => planIds.Contains(x.WorksheetPlanId)).Select(x => new WorksheetOptModel() { WorksheetOptId = x.WorksheetOptId }).ToList();
                await DeleteWorksheetOptAsync(findOpts);
                //deactivate the optacts
                var optIds = findOpts.Select(x => x.WorksheetOptId).ToList();
                var findOptActs = _context.WorksheetOptActs.Where(x => optIds.Contains(x.WorksheetOptId)).Select(x => new WorksheetOptActModel() { WorksheetOptActId = x.WorksheetOptActId }).ToList();
                await DeleteWorksheetOptActAsync(findOptActs);

                return new OkObjectResult(new ResponseModel<WorksheetDto> { Value = model, IsSuccess = true, Message = "Worksheet deleted successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<WorksheetDto> { IsSuccess = false, Message = "Failed to delete Worksheet", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeleteWorksheetPlanAsync([FromBody] List<WorksheetPlanModel> model)
        {
            var worksheetPlans = model.Select(x => x.WorksheetPlanId).ToList();
            var findPlans = _context.WorksheetPlans.Where(x => worksheetPlans.Contains(x.WorksheetPlanId));
            string updateBy = User.Identity.Name.Split('@')[0];
            findPlans.ExecuteUpdate(setters => setters.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedDateTime, DateTime.Now).SetProperty(b => b.UpdatedBy, updateBy));
            return Ok(true);
        }
        [HttpPut]
        public async Task<IActionResult> DeleteWorksheetOptAsync([FromBody] List<WorksheetOptModel> model)
        {
            var worksheetOpts = model.Select(x => x.WorksheetOptId).ToList();
            var findOpts = _context.WorksheetOpts.Where(x => worksheetOpts.Contains(x.WorksheetOptId));
            string updateBy = User.Identity.Name.Split('@')[0];
            findOpts.ExecuteUpdate(setters => setters.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedDateTime, DateTime.Now).SetProperty(b => b.UpdatedBy, updateBy));
            return Ok(true);
        }
        [HttpPut]
        public async Task<IActionResult> DeleteWorksheetOptActAsync([FromBody] List<WorksheetOptActModel> model)
        {
            var worksheetOptActs = model.Select(x => x.WorksheetOptActId).ToList();
            var findOptActs = _context.WorksheetOptActs.Where(x => worksheetOptActs.Contains(x.WorksheetOptActId));
            string updateBy = User.Identity.Name.Split('@')[0];
            findOptActs.ExecuteUpdate(setters => setters.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedDateTime, DateTime.Now).SetProperty(b => b.UpdatedBy, updateBy));
            return Ok(true);
        }
        [HttpPut]
        public async Task<IActionResult> DeleteWorksheetPlanActAsync([FromBody] List<WorksheetPlanAct> model)
        {
            var worksheetPlanActIds = model.Select(x => x.WorksheetPlanActId).ToList();
            var findPlanActs = _context.WorksheetPlanActs.Where(x => worksheetPlanActIds.Contains(x.WorksheetPlanActId));
            string updateBy = User.Identity.Name.Split('@')[0];
            findPlanActs.ExecuteUpdate(setters => setters.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedDateTime, DateTime.Now).SetProperty(b => b.UpdatedBy, updateBy));
            return Ok(true);
        }
        [HttpPut]
        public async Task<IActionResult> UpdateWorksheetAsync([FromBody] List<WorksheetTreeModel> model)
        {
            try
            {   
                //TODO: update errors/warnings                
                var updateBy = User.Identity.Name.Split('@')[0];
                //worksheet options
                var optsToUpdate = model.SelectMany(x=>x.Children).Where(x => x.WorksheetOptionId != null && x.WorksheetOptionId != 1).Select(x => new WorksheetOpt()
                {
                    WorksheetOptId = (int)x.WorksheetOptionId,
                    Marketvalue = (double?)x.MarketValue,
                    Markup = (double?)x.Markup,
                    Markuppercent = x.MarkupPercent,
                    Markuptype = x.MarkupType,
                    Costprice = (double?)x.Cost,
                    Sellprice = (double?)x.SellPrice,
                    Pricedate = x.PriceDate,
                    IsActive = x.IsActive,//This will "delete" items that were deleted individually from the worksheet
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now
                });
                await _context.WorksheetOpts.BulkUpdateAsync(optsToUpdate, options => options.ColumnInputExpression = x => new { x.Costprice, x.Markup, x.Sellprice,x.Pricedate, x.Markuptype, x.Markuppercent,x.Marketvalue, x.UpdatedBy, x.UpdatedDateTime, x.IsActive });
               
                //opt acts 
                var optActsToUpdate = model.SelectMany(x=>x.Children).Where(x=>x.WorksheetOptionId != 1).SelectMany(x=>x.Children).Where(x => x.WorksheetActivityId != null).Select(x => new WorksheetOptAct()
                {
                    
                    WorksheetOptActId = (int)x.WorksheetActivityId,
                    Lumpsum = x.LumpSum ? "T" : "F",
                    Costprice = (double?)x.Cost,
                    Errors = x.ErrorCount,
                    Warnings = x.WarningCount,
                    SubNumber = x.SubNumber,
                    IsActive = x.IsActive,
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now
                });
                await _context.WorksheetOptActs.BulkUpdateAsync(optActsToUpdate, options => options.ColumnInputExpression = x => new { x.Costprice, x.Lumpsum, x.Errors, x.Warnings, x.SubNumber,  x.UpdatedBy, x.UpdatedDateTime, x.IsActive });


                //plans
                //deactivate the ones marked for deletion
                //var plansToDelete = model.Where(x => x.PlanId != null && x.OptionId == null && x.IsActive == false).ToList();
                //var findPlansToDelete = _context.WorksheetPlans.Where(x => plansToDelete.Select(y => y.WorksheetPlanId).Contains(x.WorksheetPlanId));
                //findPlansToDelete.ExecuteUpdate(s => s.SetProperty(x => x.IsActive, false).SetProperty(x => x.UpdatedBy, updateBy).SetProperty(x => x.UpdatedDateTime, DateTime.Now));

                //update the base houses: I think
                //TODO: the problem is confusion between base house row and the plan, so when trying to delete plan from worksheet this is an issue
                //so if it's the base house row being updated becuase of price, it is dififernt than the plan being deleted.. 
                //option id = 1 is the base house row, need to designate this more clearly.
                var plansToUpdate = model.SelectMany(x=>x.Children).Where(x => x.WorksheetPlanId != null && x.WorksheetOptionId == 1).Select(x => new WorksheetPlan()
                {
                    WorksheetPlanId = (int)x.WorksheetPlanId,
                    Marketvalue = (double?)x.MarketValue,
                    Markup = (double?)x.Markup,
                    Markuppercent = x.MarkupPercent,
                    Markuptype = x.MarkupType,
                    Costprice = (double?)x.Cost,
                    Sellprice = (double?)x.SellPrice,
                    Pricedate = x.PriceDate,
                    IsActive = x.IsActive,
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now
                }).ToList();

                await _context.WorksheetPlans.BulkUpdateAsync(plansToUpdate, options => options.ColumnInputExpression = x => new { x.Costprice, x.Markup, x.Sellprice, x.Markuptype, x.Markuppercent, x.Marketvalue, x.UpdatedBy, x.UpdatedDateTime, x.IsActive });
               
                //TODO: then the plan acts//opt id = 1 is wrong 
                var planActsToUpdate = model.SelectMany(x=>x.Children).Where(x => x.WorksheetOptionId == 1).SelectMany(x=>x.Children).Where(x=> x.WorksheetActivityId != null).Select(x => new WorksheetPlanAct()
                {
                    WorksheetPlanActId = (int)x.WorksheetActivityId,
                    Costprice = (double?)x.Cost,
                    Lumpsum = x.LumpSum ? "T" : "F",
                    Errors = x.ErrorCount,
                    Warnings = x.WarningCount,
                    SubNumber = x.SubNumber,
                    IsActive = x.IsActive,
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now
                });
                await _context.WorksheetPlanActs.BulkUpdateAsync(planActsToUpdate, options => options.ColumnInputExpression = x => new { x.Costprice, x.Lumpsum, x.Errors, x.Warnings, x.SubNumber, x.UpdatedBy, x.UpdatedDateTime, x.IsActive });               

                return new OkObjectResult(new ResponseModel<List<WorksheetTreeModel>> { Value = model, IsSuccess = true, Message = "Saved worksheet" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<WorksheetTreeModel>> { IsSuccess = false, Message = "Failed to update Worksheet", Value = model });
            }
        }
        [HttpPut]
        public async Task<IActionResult> SaveUpdateWorksheetAsync([FromBody] List<WorksheetTreeModel> model)
        {
            try
            {
                //TODO: base house doesn't get inclued
                //TODO: update errors/warnings
                var updateBy = User.Identity.Name.Split('@')[0];
                //worksheet options
                var optsToUpdate = model.SelectMany(x => x.Children).Where(x => x.WorksheetOptionId != null && x.WorksheetOptionId != 1).Select(x => new WorksheetOpt()
                {
                    WorksheetOptId = (int)x.WorksheetOptionId,
                    Marketvalue = (double?)x.MarketValue,
                    Markup = (double?)x.Markup,
                    Markuppercent = x.MarkupPercent,
                    Markuptype = x.MarkupType,
                    Costprice = (double?)x.Cost,
                    Sellprice = (double?)x.SellPrice,
                    Pricedate = x.PriceDate,
                    IsActive = x.IsActive,//This will "delete" items that were deleted individually from the worksheet
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now
                });
                await _context.WorksheetOpts.BulkUpdateAsync(optsToUpdate, options => options.ColumnInputExpression = x => new { x.Costprice, x.Markup, x.Sellprice, x.Pricedate, x.Markuptype, x.Markuppercent, x.Marketvalue, x.UpdatedBy, x.UpdatedDateTime, x.IsActive });

                //opt acts 
                var optActsToUpdate = model.SelectMany(x => x.Children).Where(x=>x.WorksheetOptionId != 1).SelectMany(x => x.Children).Where(x => x.WorksheetActivityId != null).Select(x => new WorksheetOptAct()
                {

                    WorksheetOptActId = (int)x.WorksheetActivityId,
                    Lumpsum = x.LumpSum ? "T" : "F",
                    Costprice = (double?)x.Cost,
                    Errors = x.ErrorCount,
                    Warnings = x.WarningCount,
                    SubNumber = x.SubNumber,
                    IsActive = x.IsActive,
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now
                });
                await _context.WorksheetOptActs.BulkUpdateAsync(optActsToUpdate, options => options.ColumnInputExpression = x => new { x.Costprice, x.Lumpsum, x.Errors, x.Warnings, x.SubNumber, x.UpdatedBy, x.UpdatedDateTime, x.IsActive });


                //plans
                //deactivate the ones marked for deletion
                var plansToDelete = model.Where(x => x.WorksheetPlanId != null && x.WorksheetOptionId == null && x.IsActive == false).ToList();
                var findPlansToDelete = _context.WorksheetPlans.Where(x => plansToDelete.Select(y => y.WorksheetPlanId).Contains(x.WorksheetPlanId));
                findPlansToDelete.ExecuteUpdate(s => s.SetProperty(x => x.IsActive, false).SetProperty(x => x.UpdatedBy, updateBy).SetProperty(x => x.UpdatedDateTime, DateTime.Now));

                //Base house option row
                var plansToUpdate = model.SelectMany(x => x.Children).Where(x => x.WorksheetPlanId != null && x.WorksheetOptionId == 1).Select(x => new WorksheetPlan()
                {
                    WorksheetPlanId = (int)x.WorksheetPlanId,
                    Marketvalue = (double?)x.MarketValue,
                    Markup = (double?)x.Markup,
                    Markuppercent = x.MarkupPercent,
                    Markuptype = x.MarkupType,
                    Costprice = (double?)x.Cost,
                    Sellprice = (double?)x.SellPrice,
                    Pricedate = x.PriceDate,
                    IsActive = x.IsActive,
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now
                }).ToList();

                await _context.WorksheetPlans.BulkUpdateAsync(plansToUpdate, options => options.ColumnInputExpression = x => new { x.Costprice, x.Markup, x.Sellprice, x.Markuptype, x.Markuppercent, x.Marketvalue, x.UpdatedBy, x.UpdatedDateTime, x.IsActive });

                var planActsToUpdate = model.SelectMany(x => x.Children).Where(x => x.WorksheetOptionId == 1).SelectMany(x => x.Children).Where(x => x.WorksheetActivityId != null).Select(x => new WorksheetPlanAct()
                {
                    WorksheetPlanActId = (int)x.WorksheetActivityId,
                    Costprice = (double?)x.Cost,
                    Lumpsum = x.LumpSum ? "T" : "F",
                    Errors = x.ErrorCount,
                    Warnings = x.WarningCount,
                    SubNumber = x.SubNumber,
                    IsActive = x.IsActive,
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now
                });
                await _context.WorksheetPlanActs.BulkUpdateAsync(planActsToUpdate, options => options.ColumnInputExpression = x => new { x.Costprice, x.Lumpsum, x.Errors, x.Warnings, x.SubNumber, x.UpdatedBy, x.UpdatedDateTime, x.IsActive });

                return new OkObjectResult(new ResponseModel<List<WorksheetTreeModel>> { Value = model, IsSuccess = true, Message = "Saved worksheet" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<WorksheetTreeModel>> { IsSuccess = false, Message = "Failed to update Worksheet", Value = model });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdateWorksheetFromImportAsync([FromBody] List<WorksheetTreeModel> model)
        {
            try
            {
                //Trying this because excel seems missing some colums
                //
                var updateBy = User.Identity.Name.Split('@')[0];
                //worksheet options
                var optsToUpdate = model.SelectMany(x => x.Children).Where(x => x.WorksheetOptionId != null && x.IsBaseHouse == false).Select(x => new WorksheetOpt()
                {
                    WorksheetOptId = (int)x.WorksheetOptionId,
                    Marketvalue = (double?)x.MarketValue,
                    Markup = (double?)x.Markup,
                    Markuppercent = x.MarkupPercent,
                    Markuptype = x.MarkupType,
                    Costprice = (double?)x.Cost,
                    Sellprice = (double?)x.SellPrice,
                    Pricedate = x.PriceDate,
                    IsActive = x.IsActive,
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now
                });
                await _context.WorksheetOpts.BulkUpdateAsync(optsToUpdate, options => options.ColumnInputExpression = x => new { x.Costprice, x.Markup, x.Sellprice, x.Pricedate, x.Markuptype, x.Markuppercent, x.Marketvalue, x.UpdatedBy, x.UpdatedDateTime, x.IsActive });

                //opt acts 
                var optActsToUpdate = model.SelectMany(x => x.Children).Where(x=>x.IsBaseHouse == false).SelectMany(x => x.Children).Where(x => x.WorksheetActivityId != null).Select(x => new WorksheetOptAct()
                {

                    WorksheetOptActId = (int)x.WorksheetActivityId,
                    Lumpsum = x.LumpSum ? "T" : "F",
                    Costprice = (double?)x.Cost,
                  // Errors = x.ErrorCount,
                   // Warnings = x.WarningCount,
                   // SubNumber = x.SubNumber,
                    IsActive = x.IsActive,
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now
                });
                await _context.WorksheetOptActs.BulkUpdateAsync(optActsToUpdate, options => options.ColumnInputExpression = x => new { x.Costprice, x.Lumpsum,   x.UpdatedBy, x.UpdatedDateTime, x.IsActive });


                //plans
                //deactivate the ones marked for deletion
                //var plansToDelete = model.Where(x => x.PlanId != null && x.OptionId == null && x.IsActive == false).ToList();
                //var findPlansToDelete = _context.WorksheetPlans.Where(x => plansToDelete.Select(y => y.WorksheetPlanId).Contains(x.WorksheetPlanId));
                //findPlansToDelete.ExecuteUpdate(s => s.SetProperty(x => x.IsActive, false).SetProperty(x => x.UpdatedBy, updateBy).SetProperty(x => x.UpdatedDateTime, DateTime.Now));

                //update the base houses: I think
                //TODO: the problem is confusion between base house row and the plan, so when trying to delete plan from worksheet this is an issue
                //so if it's the base house row being updated becuase of price, it is dififernt than the plan being deleted.. 
                //option id = 1 is the base house row, need to designate this more clearly.
                var plansToUpdate = model.SelectMany(x => x.Children).Where(x => x.IsBaseHouse == true).Select(x => new WorksheetPlan()
                {
                    WorksheetPlanId = (int)x.WorksheetPlanId,
                    Marketvalue = (double?)x.MarketValue,
                    Markup = (double?)x.Markup,
                    Markuppercent = x.MarkupPercent,
                    Markuptype = x.MarkupType,
                    Costprice = (double?)x.Cost,
                    Sellprice = (double?)x.SellPrice,
                    Pricedate = x.PriceDate,
                    IsActive = x.IsActive,
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now
                }).ToList();

                await _context.WorksheetPlans.BulkUpdateAsync(plansToUpdate, options => options.ColumnInputExpression = x => new { x.Costprice, x.Markup, x.Sellprice, x.Markuptype, x.Markuppercent, x.Marketvalue, x.UpdatedBy, x.UpdatedDateTime, x.IsActive });

                var planActsToUpdate = model.SelectMany(x => x.Children).Where(x => x.IsBaseHouse == true).SelectMany(x => x.Children).Where(x => x.WorksheetActivityId != null).Select(x => new WorksheetPlanAct()
                {
                    WorksheetPlanActId = (int)x.WorksheetActivityId,
                    Costprice = (double?)x.Cost,
                    Lumpsum = x.LumpSum ? "T" : "F",
                   // Errors = x.ErrorCount,
                    //Warnings = x.WarningCount,
                    //SubNumber = x.SubNumber,
                    IsActive = x.IsActive,
                    UpdatedBy = updateBy,
                    UpdatedDateTime = DateTime.Now
                });
                await _context.WorksheetPlanActs.BulkUpdateAsync(planActsToUpdate, options => options.ColumnInputExpression = x => new { x.Costprice, x.Lumpsum,  x.UpdatedBy, x.UpdatedDateTime, x.IsActive });

                return new OkObjectResult(new ResponseModel<List<WorksheetTreeModel>> { Value = model, IsSuccess = true, Message = "Saved worksheet" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<WorksheetTreeModel>> { IsSuccess = false, Message = "Failed to update Worksheet", Value = model });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdateWorksheetOptsAsync([FromBody] List<WorksheetTreeModel> options)
        {
            //saves changes for a worksheet opt (or plan if it's the base plan row)
            try
            {
                var optionIds = options.Where(x => x.WorksheetOptionId != 1).Select(x => x.WorksheetOptionId).ToList();
                var worksheetOptions = await _context.WorksheetOpts.Where(x => optionIds.Contains(x.WorksheetOptId)).ToListAsync();

                foreach (var option in worksheetOptions)
                {
                    var findOption = options.SingleOrDefault(x => x.WorksheetOptionId == option.WorksheetOptId);
                    option.Marketvalue = (double?)findOption.MarketValue;
                    option.Markup = (double?)findOption.Markup;
                    option.Markuppercent = findOption.MarkupPercent;
                    option.Markuptype = findOption.MarkupType;
                    option.Sellprice = (double?)findOption.SellPrice;
                    option.Costprice = (double?)findOption.Cost;
                    option.Pricedate = findOption.PriceDate;
                    option.UpdatedBy = User.Identity.Name.Split('@')[0];
                    option.UpdatedDateTime = DateTime.Now;
                }

                await _context.WorksheetOpts.BulkUpdateAsync(worksheetOptions, options => options.ColumnInputExpression = x => new { x.Marketvalue, x.Markup, x.Markuppercent, x.Markuptype, x.Sellprice, x.Costprice, x.Pricedate, x.UpdatedBy, x.UpdatedDateTime });

                var baseOptionIds = options.Where(x => x.WorksheetOptionId == 1).Select(x => x.WorksheetOptionId).ToList();
                var worksheetBaseHouseOptions = await _context.WorksheetPlans.Where(x => baseOptionIds.Contains(x.WorksheetPlanId)).ToListAsync();

                foreach (var baseHouseOption in worksheetBaseHouseOptions)
                {
                    var findOption = options.SingleOrDefault(x => x.WorksheetPlanId == baseHouseOption.WorksheetPlanId);
                    baseHouseOption.Marketvalue = (double?)findOption.MarketValue;
                    baseHouseOption.Markup = (double?)findOption.Markup;
                    baseHouseOption.Markuppercent = findOption.MarkupPercent;
                    baseHouseOption.Markuptype = findOption.MarkupType;
                    baseHouseOption.Sellprice = (double?)findOption.SellPrice;
                    baseHouseOption.Costprice = (double?)findOption.Cost;
                    baseHouseOption.Pricedate = findOption.PriceDate;
                    baseHouseOption.UpdatedBy = User.Identity.Name.Split('@')[0];
                    baseHouseOption.UpdatedDateTime = DateTime.Now;
                }

                await _context.WorksheetPlans.BulkUpdateAsync(worksheetBaseHouseOptions, options => options.ColumnInputExpression = x => new { x.Marketvalue, x.Markup, x.Markuppercent, x.Markuptype, x.Sellprice, x.Costprice, x.Pricedate, x.UpdatedBy, x.UpdatedDateTime });

                return new OkObjectResult(new ResponseModel<List<WorksheetTreeModel>> { Value = options, IsSuccess = true, Message = "Worksheet options updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<WorksheetOptModel>> { IsSuccess = false, Message = "Failed to update Worksheet Options", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdateWorksheetPlanAsync([FromBody] WorksheetPlanModel model)
        {
            try
            {
                var findItem = _context.WorksheetPlans.SingleOrDefault(x => x.WorksheetPlanId == model.WorksheetPlanId);
                findItem.Marketvalue = model.Marketvalue;
                findItem.Markup = model.Markup;
                findItem.Markuppercent = model.Markuppercent;
                findItem.Markuptype = model.Markuptype;
                findItem.Sellprice = model.Sellprice;
                findItem.Costprice = model.Costprice;
                findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                findItem.UpdatedDateTime = DateTime.Now;
                _context.WorksheetPlans.Update(findItem);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<WorksheetPlanModel> { Value = model, IsSuccess = true, Message = "Worksheet Plan updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<WorksheetPlanModel> { IsSuccess = false, Message = "Failed to update Worksheet Plan", Value = null });
            }
        }
        [HttpGet("{worksheetId}")]
        public async Task<IActionResult> GetWorksheetLockAsync(int worksheetId)
        {
            try
            {
                string lockedBy = null;
                if (worksheetId != 0)
                {
                    var worksheet = await _context.Worksheets.SingleOrDefaultAsync(x => x.WorksheetId == worksheetId);
                    lockedBy = worksheet?.LockedBy;
                    if (lockedBy == null)
                    {
                        //if there was no lock, lock it for the current user
                        worksheet.LockedBy = User.Identity.Name.Split('@')[0];
                        _context.Worksheets.Update(worksheet);
                        await _context.SaveChangesAsync();
                        lockedBy = worksheet.LockedBy;
                    }
                }

                return Ok(lockedBy);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, "");
            }
        }
        [HttpGet("{worksheetId}")]
        public async Task<IActionResult> ClearWorksheetLockAsync(int worksheetId)
        {
            try
            {
                //set worksheetlock to null if it was locked by the current user - don't break another users lock
                var worksheet = await _context.Worksheets.SingleOrDefaultAsync(x => x.WorksheetId == worksheetId);
                if (worksheet.LockedBy == User.Identity.Name.Split('@')[0])
                {
                    worksheet.LockedBy = null;
                    _context.Worksheets.Update(worksheet);
                    await _context.SaveChangesAsync();
                }
                return Ok(worksheet.LockedBy);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, "");
            }
        }
        [HttpGet("{worksheetId}")]
        public async Task<IActionResult> BreakWorksheetLockAsync(int worksheetId)
        {
            try
            {
                //overwrite another users lock
                var worksheet = await _context.Worksheets.SingleOrDefaultAsync(x => x.WorksheetId == worksheetId);
                worksheet.LockedBy = User.Identity.Name.Split('@')[0];
                _context.Worksheets.Update(worksheet);
                await _context.SaveChangesAsync();
                return Ok(worksheet.LockedBy);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, "");
            }
        }
        [HttpGet("{worksheetId}")]
        public async Task<IActionResult> GetWorksheetPlansAsync(int worksheetId)
        {
            var worksheetPlans = new List<WorksheetPlanModel>();
            try
            {
                worksheetPlans = await _context.WorksheetPlans.Include(x => x.WorksheetOpts).ThenInclude(x => x.WorksheetOptActs).Include(x => x.WorksheetPlanActs).Where(x => x.WorksheetId == worksheetId && x.IsActive == true).Select(x => new WorksheetPlanModel()
                {
                    SubdivisionId = x.PhasePlan.SubdivisionId,
                    SubdivisionName = x.PhasePlan.Subdivision.SubdivisionName,
                    PhasePlanId = x.PhasePlanId,
                    PlanNumber = x.PhasePlan.MasterPlan.PlanNum,
                    WorksheetId = x.WorksheetId,
                    WorksheetPlanId = x.WorksheetPlanId,
                    PlanName = x.PhasePlan.MasterPlan.PlanName,
                    Costprice = x.Costprice,
                    Sellprice = x.Sellprice,
                    Markuppercent = x.Markuppercent,
                    Marketvalue = x.Marketvalue,
                    Markup = x.Markup,
                    Markuptype = x.Markuptype,
                    Pricedate = x.Pricedate,
                    Errors = x.WorksheetPlanActs.Sum(y => y.Errors) + x.WorksheetOpts.SelectMany(y => y.WorksheetOptActs).Sum(z => z.Errors),
                    IsActive = x.IsActive
                }).ToListAsync();
                return Ok(worksheetPlans);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, worksheetPlans);
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetWorksheetsAsync()
        {
            var worksheets = new List<Worksheet>();
            try
            {
                worksheets = await _context.Worksheets.Where(x => x.IsActive == true).OrderBy(x => x.WorksheetName).ToListAsync();
                var worksheetsDto = _mapper.Map<List<WorksheetDto>>(worksheets);
                return Ok(worksheets);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, worksheets);
            }
        }
        [HttpGet("{planId}")]
        public async Task<IActionResult> GetAvailablePlanOptionsByPlanAsync(int planId)
        {
            try
            {
                var options = await _context.AvailablePlanOptions
                    .Include("OptionGroup").Include(x => x.OptionTypeNavigation).Where(x => x.PhasePlanId == planId && x.IsActive == true).OrderBy(x => x.OptionCode).ToListAsync();
                var mapOptions = _mapper.Map<List<AvailablePlanOptionDto>>(options.ToList());
                foreach (var option in mapOptions)
                {
                    option.IsBaseHouse = false;
                    option.ModifiedOptionDesc = (!string.IsNullOrWhiteSpace(option.ModifiedOptionDesc)) ? option.ModifiedOptionDesc : option.OptionLongDesc;
                    option.BoolIsElevation = option.IsElevation == "T";
                    option.BoolIsStandard = option.IsStandard == "T"; 
                }
                //add the base house
                var getBaseHouse = await _context.PhasePlans.Include("MasterPlan").SingleOrDefaultAsync(x => x.PhasePlanId == planId);
                var addBaseHouseOptionToList = new AvailablePlanOptionDto()
                {
                    OptionCode = $"{getBaseHouse.MasterPlan.PlanNum}A0000000",
                    ModifiedOptionDesc = $"{getBaseHouse.MasterPlan.PlanName} BASE HOUSE",
                    OptionGroup = new OptionGroupDto() { OptionGroupId = 1, OptionGroupName = "A. Structural Options", OptionGroupLetter = "A" },
                    IsBaseHouse = true,
                    PlanOptionId = getBaseHouse.PhasePlanId,//Not really the right way to do this
                    UnitCost = (decimal?)getBaseHouse.CurrCost,//TODO: wrong??
                    CurrCost = getBaseHouse.CurrCost,
                    CurrDate = getBaseHouse.CurrDate,
                    CurrSelling = getBaseHouse.CurrSelling,
                    NextCost = getBaseHouse.NextCost,
                    LastCost = getBaseHouse.LastCost,
                    NextDate = getBaseHouse.NextDate,
                    NextMargin = getBaseHouse.NextMargin,
                    NextSelling = getBaseHouse.NextSelling,
                    LastDate = getBaseHouse.LastDate,
                    LastMargin = getBaseHouse.LastMargin,
                    LastSelling = getBaseHouse.LastSelling,
                    MarginType = getBaseHouse.MarginType,
                    MarginLumpSum = getBaseHouse.MarginLumpSum,
                    MarginMarketValue = getBaseHouse.MarginMarketValue,
                    MarginPercent = getBaseHouse.MarginPercent,
                    OptionTypeNavigation = new OptionTypeDto()

                };
                mapOptions.Insert(0, addBaseHouseOptionToList);
                return Ok(new ResponseModel<List<AvailablePlanOptionDto>>() { Value = mapOptions, IsSuccess = true, Message = "Got options" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<AvailablePlanOptionDto>>() {  IsSuccess = false, Message = "Failed to get options" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetRollOverAvailablePlanOptionsAsync()
        {
            try
            {
                var optionPlans = await _context.AvailablePlanOptions.Where(x => x.IsActive == true && x.NextDate != null &&  x.NextDate.Value.Date <= DateTime.UtcNow.Date).ToListAsync();

                var returnOptions = _mapper.Map<List<AvailablePlanOptionDto>>(optionPlans);
                var response = new ResponseModel<List<AvailablePlanOptionDto>>() { IsSuccess = true, Value = returnOptions, Message = "Retrieved options needing rollover" };
                return Ok(response);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<string>() { IsSuccess = false, Value = string.Empty, Message = "Failed to retrive all Available Plan Options needing rollover" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateAvailablePlanOptionsAsync([FromBody] List<AvailablePlanOptionDto> options)
        {
            try
            {
                var optionsToUpdate = _mapper.Map<List<AvailablePlanOption>>(options);

                foreach (var option in optionsToUpdate)
                {
                    option.CurrSelling = option.NextSelling;
                    option.NextSelling = null;
                    option.CurrDate = option.NextDate;
                    option.NextDate = null;
                    option.UpdatedBy = User.Identity.Name.Split('@')[0];
                    option.UpdatedDateTime = DateTime.Now;
                }

                await _context.AvailablePlanOptions.BulkUpdateAsync(optionsToUpdate);

                var returnOptions = _mapper.Map<List<AvailablePlanOptionDto>>(optionsToUpdate);
                var response = new ResponseModel<string>() { IsSuccess = true, Value = string.Empty, Message = "Updated Available Plan Options" };
                return Ok(response);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<string>() { IsSuccess = false, Value = string.Empty, Message = "Failed to update Available Plan Options" });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateAvailablePlanOptionAsync([FromBody] AvailablePlanOptionDto option)
        {
            try
            {
                var findOption = await _context.AvailablePlanOptions.SingleOrDefaultAsync(x => x.PlanOptionId == option.PlanOptionId);

                if(findOption.CurrSelling != option.CurrSelling)
                {
                    //save price history if update current price
                    var insertHistory = new PlanOptionPriceHist()
                    {
                        PlanOptionId = findOption.PlanOptionId,
                        OptionCode = findOption.OptionCode,
                        ModifiedOptionDesc = findOption.ModifiedOptionDesc,
                        SellingPrice = findOption.CurrSelling,
                        SellingPriceEndDate = DateTime.Now,
                        SellingPriceStartDate = findOption.CurrDate,
                        CreatedBy = User.Identity.Name.Split('@')[0]
                    };
                    _context.PlanOptionPriceHists.Add(insertHistory);
                    await _context.SaveChangesAsync();
                }

                findOption.IsActive = true;
                findOption.IsElevation = option.BoolIsElevation == true ? "T" : "F";
                findOption.IsStandard = option.BoolIsStandard == true ? "T" : "F";
                findOption.UnitPrice = option.UnitPrice;
                findOption.LastSelling = findOption.CurrSelling != option.CurrSelling ? findOption.CurrSelling : findOption.LastSelling;//roll to current to last if selling price change
                findOption.LastDate = findOption.CurrSelling != option.CurrSelling ? findOption.CurrDate : findOption.LastDate;
                findOption.CurrSelling = option.CurrSelling;
                findOption.CurrDate = findOption.CurrSelling != option.CurrSelling ? DateTime.Now : findOption.CurrDate;
                findOption.NextDate = option.NextDate;
                findOption.NextSelling = option.NextSelling;
                findOption.NextMargin = option.NextMargin;
                findOption.MarginType = option.MarginType;
                findOption.UpdatedBy = User.Identity.Name.Split('@')[0];
                findOption.UpdatedDateTime = DateTime.Now;
                findOption.ModifiedOptionDesc = option.ModifiedOptionDesc;
                findOption.OptionGroupId = option.OptionGroupId;
                findOption.OptionTypeId = option.OptionTypeId;

                _context.AvailablePlanOptions.Update(findOption);
                await _context.SaveChangesAsync();

                return new OkObjectResult(new ResponseModel<AvailablePlanOptionDto> { Value = option, IsSuccess = true, Message = "Updated option" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<AvailablePlanOptionDto> { IsSuccess = false, Message = "Failed to update Available Plan Option", Value = option });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetTotalOptionsSoldAsync()
        {
            try
            {
                var returnData = new TileDto();

                decimal lastWeekTotal = 0;
                decimal thisWeekTotal = 0;

                var conn = _configuration.GetConnectionString("ERPConnection");

                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT CAST(ISNULL(SUM(Qty * Price), 0) as int) FROM [cm].[tbBuiltOptions] WHERE MostRecent = 1 AND Removed = 0 AND CAST(CreatedDateTime AS DATE) >= CAST(DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 0) as date) AND CreatedDateTime < CAST(DATEADD(wk, DATEDIFF(wk, 6, GETDATE()), 6) AS DATE)";
                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        lastWeekTotal = Convert.ToInt32(reader.GetValue(0).ToString());
                    }
                }

                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT CAST(ISNULL(SUM(Qty * Price), 0) as int) FROM [cm].[tbBuiltOptions] WHERE MostRecent = 1 AND Removed = 0 AND CreatedDateTime >= dateadd(day, 1-datepart(dw, getdate()), CONVERT(date,getdate())) AND CreatedDateTime <  dateadd(day, 8-datepart(dw, getdate()), CONVERT(date,getdate()))";
                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        thisWeekTotal = Convert.ToInt32(reader.GetValue(0).ToString());
                    }
                }

                if (thisWeekTotal >= 10000)
                {
                    returnData.TotalOptionsSold = (thisWeekTotal / 1000).ToString("0.#") + "K";
                }
                else
                {
                    returnData.TotalOptionsSold = (thisWeekTotal / 1000) + "K";
                }

                var difference = lastWeekTotal != 0 ? (thisWeekTotal - lastWeekTotal) / lastWeekTotal * 100 : 100;

                returnData.TrendingPercentage = difference;

                return new OkObjectResult(new ResponseModel<TileDto> { Value = returnData, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Pending VPO Total"));
            }
        }

        //Not used. no updating base house pricing
        //        [HttpPut]
        //        public async Task<IActionResult> UpdateBaseHouseOptionAsync([FromBody] AvailablePlanOptionDto option)
        //        {
        //            try
        //            {
        //                var findPlan = await _context.PhasePlans.SingleOrDefaultAsync(x => x.PhasePlanId == option.PlanOptionId);//phase plan id being stored in plan option id to make it behave like an available plan option, which is not the right way to do this
        //                findPlan.IsActive = true;
        //                findPlan.PhasePlanCost = option.UnitCost;
        //                findPlan.CurrCost = option.CurrCost;
        //                findPlan.NextCost = option.NextCost;
        //                findPlan.CurrSelling = option.CurrSelling;
        //                findPlan.MarginType = option.MarginType;
        //                findPlan.MarginMarketValue = option.MarginMarketValue;
        //                findPlan.MarginLumpSum = option.MarginLumpSum;
        //                findPlan.NextSelling = option.NextSelling;
        //                findPlan.NextDate = option.NextDate;
        //                findPlan.UpdatedBy = User.Identity.Name.Split('@')[0];
        //                //TODO: update other fields as needed from option
        //                _context.PhasePlans.Update(findPlan);

        //                await _context.SaveChangesAsync();
        //                return new OkObjectResult(new ResponseModel<AvailablePlanOptionDto> { Value = option, IsSuccess = true });
        //            }
        //            catch (Exception ex)
        //            {
        //#if DEBUG
        //                _logger.Debug(ex);
        //#else
        //                _logger.Error(ex);
        //#endif
        //                return StatusCode(500, new ResponseModel<AvailablePlanOptionDto> { IsSuccess = false, Message = "Failed to update Base House Option", Value = option });
        //            }
        //        }
    }
}
