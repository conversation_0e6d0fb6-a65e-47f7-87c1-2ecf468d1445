﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class JobConstructionType
{
    public int JobConstructionTypeId { get; set; }

    public string Description { get; set; } = null!;

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual ICollection<Job> Jobs { get; set; } = new List<Job>();
}
