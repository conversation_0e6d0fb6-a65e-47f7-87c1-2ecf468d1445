﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System.ComponentModel.DataAnnotations;

namespace ERP.Data.Models;

public class PactivityDto : IMapFrom<Pactivity>
{
    public int PactivityId { get; set; }

    [Required(ErrorMessage = "The Activity Name field is required")]
    public string? Activity { get; set; }

    public string? Printlocations { get; set; }

    public string? Printschedule { get; set; }

    public string? Poindex { get; set; }

    public string? Pecategory { get; set; }

    [Required(ErrorMessage = "The Release code field is required")]
    public string? Releasecode { get; set; }

    public int? SactivityId { get; set; }

    [Required(ErrorMessage = "The Trade field is required")]
    public int? TradeId { get; set; }

    public int? MasterPactivityId { get; set; }

    public string? IncludeSelectionsOnPo { get; set; }

    public string? Notes { get; set; }

    public int? DivId { get; set; }

    [Required(ErrorMessage = "The Cost Code field is required")]
    public int? JccostcodeId { get; set; }

    public int? JccategoryId { get; set; }

    public int BomClassId { get; set; }

    public int? InvApprovalRole { get; set; }

    public string? AutoCreateInvoice { get; set; }

    public string? ProtectFromMaster { get; set; }

    public string? Taxable { get; set; }

    public string? Description { get; set; }

    public int? MasterItemId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual BomClassDto? BomClass { get; set; }

    public virtual JccategoryDto? Jccategory { get; set; }

    public virtual JccostcodeDto? Jccostcode { get; set; }

    public virtual MasterItemDto? MasterItem { get; set; }

    public virtual ICollection<PactivityAreaSupplierDto> PactivityAreaSuppliers { get; set; } = new List<PactivityAreaSupplierDto>();

    public virtual SactivityDto? Sactivity { get; set; }

    public virtual TradeDto? Trade { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<PactivityDto, Pactivity>().ReverseMap();
    }
    public SupplierDto? Supplier { get; set; }//The problem is this is pivot
    public int? SupplierNumber { get; set; }//The problem is this is pivot
    public int? SubdivisionId { get; set; }//this really doesn't belong

    public string? TradeName { get; set; }

    public string? ActivityName { get; set; }

    public string? CostCode { get; set; }

    public string? ItemNumber { get; set; }

    public string? SActivityName { get; set; }

    public string? ErrorMessage { get; set; }

    public string? SuccessMessage { get; set; }
    public string? DropdownDescription { get; set; }  //to include cost code in the text field
}
