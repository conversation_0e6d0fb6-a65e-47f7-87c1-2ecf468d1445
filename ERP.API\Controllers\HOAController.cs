﻿using AutoMapper;
using DocumentFormat.OpenXml.EMMA;
using DocumentFormat.OpenXml.InkML;
using DocumentFormat.OpenXml.Office.CoverPageProps;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using ERP.API.Data;
using ERP.API.Utilities;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Data.Models.ExtensionMethods;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Options;
using NLog;
using NLog.Fluent;
using System.Data;
using System.Diagnostics;
using System.Linq;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class HOAController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly Email _email;
        private readonly IMapper _mapper;
        public HOAController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _email = new Email(configuration);
        }

        [HttpGet]
        public async Task<IActionResult> GetHOAAsync()
        {
            try
            {
                var HOA = await _context.HoaAssessments.Include(x => x.Subdivision).Include(x => x.Hoa).Where(x => x.IsActive == true).ToListAsync();
                var HOAAssessmentDto = _mapper.Map<List<HoaAssessmentDto>>(HOA);
                return Ok(new ResponseModel<List<HoaAssessmentDto>>() { Value = HOAAssessmentDto, IsSuccess = true, Message = "success" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<HoaAssessmentDto>> { IsSuccess = false, Message = "Failed", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetSubdivisionsAsync()
        {
            try
            {
                var subdivisions = await _context.Subdivisions.Where(x => x.IsActive == true).ToListAsync();
                var subdivisionDto = _mapper.Map<List<SubdivisionDto>>(subdivisions);
                return Ok(new ResponseModel<List<SubdivisionDto>> { IsSuccess = true, Message = "success", Value = subdivisionDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SubdivisionDto>> { IsSuccess = false, Message = "Failed", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetAssociationsAsync()
        {
            try
            {
                var associations = await _context.Hoas.Where(x => x.IsActive == true).ToListAsync();
                var associationDto = _mapper.Map<List<HoaDto>>(associations);
                return Ok(new ResponseModel<List<HoaDto>> { IsSuccess = true, Message = "Success", Value = associationDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<HoaDto>> { IsSuccess = false, Message = "Failed", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetJobsAsync()
        {
            try
            {
                var jobs = await _context.Jobs.Include(x => x.Subdivision).Where(x => x.IsActive == true).ToListAsync();
                var jobsDto = _mapper.Map<List<JobDto>>(jobs);
                return Ok(new ResponseModel<List<JobDto>> { IsSuccess = true, Message = "Success", Value = jobsDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobDto>> { IsSuccess = false, Message = "Failed", Value = null });
            }
        }
        [HttpGet("{assessmentId}")]
        public async Task<IActionResult> GetHOAJobsAsync(int assessmentId)
        {
            try
            {
                var jobs = await _context.HoaJobs.Include(x => x.JobNumberNavigation.Subdivision).Where(x => x.HoaAssessmentId == assessmentId && x.IsActive == true).ToListAsync();
                var jobsDto = _mapper.Map<List<HoaJobDto>>(jobs);
                return Ok(new ResponseModel<List<HoaJobDto>> { IsSuccess = true, Message = "success", Value = jobsDto });

            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<HoaJobDto>> { IsSuccess = false, Message = "Failed", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> UpdateAssessmentAsync([FromBody] HoaAssessmentDto model)
        {
            try
            {
                var updateAssessment = await _context.HoaAssessments.FindAsync(model.HoaAssessmentId);

                updateAssessment.HoaAssessmentId = (int)model.HoaAssessmentId;
                updateAssessment.SubdivisionId = model.SubdivisionId;
                updateAssessment.AssessmentLabel = model.AssessmentLabel;
                updateAssessment.Icc = model.Icc;
                updateAssessment.MonthAssessment = model.MonthAssessment;
                updateAssessment.OtherFee = model.OtherFee;
                updateAssessment.BudgetYear = (DateTime) model.BudgetYear;
                updateAssessment.UpdatedBy = User.Identity.Name.Split('@')[0];
                updateAssessment.UpdatedDateTime = DateTime.Now;

                _context.HoaAssessments.Update(updateAssessment);
                await _context.SaveChangesAsync();

                var updateHoa = await _context.Hoas.FindAsync(model.HoaId);
                if(updateHoa != null && updateHoa.Category != model.Hoa.Category)
                {
                    updateHoa.Category = model.Hoa.Category;
                    _context.Hoas.Update(updateHoa);
                    await _context.SaveChangesAsync();
                }

                return Ok(new ResponseModel<HoaAssessmentDto> { IsSuccess = true, Message = "Successfully updated assessment", Value = model });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<HoaAssessmentDto> { IsSuccess = false, Message = "Failed to update assessment", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> DeleteAssessmentAsync([FromBody] HoaAssessmentDto model)
        {
            try
            {
                var deleteAssessment = await _context.HoaAssessments.FindAsync(model.HoaAssessmentId);
                if (deleteAssessment != null)
                {
                    deleteAssessment.IsActive = false;
                    deleteAssessment.UpdatedBy = User.Identity.Name.Split('@')[0];
                    deleteAssessment.UpdatedDateTime = DateTime.Now;
                    _context.HoaAssessments.Update(deleteAssessment);
                    await _context.SaveChangesAsync();
                    return Ok(new ResponseModel<HoaAssessmentDto> { IsSuccess = true, Message = "Successfully deleted assessment", Value = model });
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
            }
            return StatusCode(500, new ResponseModel<HoaAssessmentDto> { IsSuccess = false, Message = "Failed to delete assessment", Value = null });
        }
        [HttpPost]
        public async Task<IActionResult> AddAssessmentAsync([FromBody] HoaAssessmentDto updateAssessmentDto)
        {
            try
            {
                var findAssessment = _context.HoaAssessments.Where(x => x.HoaAssessmentId == updateAssessmentDto.HoaAssessmentId && x.HoaId == updateAssessmentDto.HoaId).FirstOrDefault();

                if (findAssessment != null)
                {
                    // if it can be found, do not add
                    return Ok(new ResponseModel<HoaAssessmentDto> { IsSuccess = true, Message = "Assessment already has an entry", Value = updateAssessmentDto });
                }
                else
                {
                    if(updateAssessmentDto.HoaId == 0)
                    {
                        //new hoa
                        var newHoa = new Hoa()
                        {
                            AssociationName = updateAssessmentDto.Hoa.AssociationName,
                            Category = updateAssessmentDto.Hoa.Category,
                            CreatedBy = User.Identity.Name.Split('@')[0]
                        };
                        _context.Hoas.Add(newHoa);
                        await _context.SaveChangesAsync();
                        updateAssessmentDto.HoaId = newHoa.HoaId;
                    }

                    var insertAssessment = new HoaAssessment()
                    {
                        HoaId = updateAssessmentDto.HoaId,
                        Icc = updateAssessmentDto.Icc,
                        OtherFee = updateAssessmentDto.OtherFee,
                        BudgetYear = (DateTime) updateAssessmentDto.BudgetYear,
                        AssessmentLabel = updateAssessmentDto.AssessmentLabel,
                        MonthAssessment = updateAssessmentDto.MonthAssessment,
                        SubdivisionId = updateAssessmentDto.SubdivisionId,
                        CreatedBy = User.Identity.Name.Split('@')[0]
                    };
                    _context.HoaAssessments.Add(insertAssessment);
                    _context.SaveChanges();
                    return Ok(new ResponseModel<HoaAssessmentDto> { IsSuccess = true, Message = "Successfully added assessment", Value = updateAssessmentDto });
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif         
                return Ok(new ResponseModel<HoaAssessmentDto> { IsSuccess = false, Message = "Failed to add assessment", Value = updateAssessmentDto });
            }
        }
        [HttpPost]
        public async Task<IActionResult> DeleteJobAsync([FromBody] HoaJobDto model)
        {
            try
            {
                var deleteJob = _context.HoaJobs.Where(x => x.HoaAssessmentId == model.HoaAssessmentId && x.JobNumber == model.JobNumber).FirstOrDefault();
                if (deleteJob != null)
                {
                    deleteJob.IsActive = false;
                    deleteJob.UpdatedBy = User.Identity.Name.Split('@')[0];
                    deleteJob.UpdatedDateTime = DateTime.Now;
                    _context.HoaJobs.Update(deleteJob);
                    _context.SaveChanges();
                    await _context.SaveChangesAsync();
                    return Ok(new ResponseModel<HoaJobDto> { IsSuccess = true, Message = "Successfully deleted job", Value = model });
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
            }
            return StatusCode(500, new ResponseModel<HoaJobDto> { IsSuccess = false, Message = "Failed to delete job", Value = null });
        }

        [HttpPost]
        public async Task<IActionResult> AddJobsAsync([FromBody] List<HoaJobDto> jobsToAdd)
        {
            try
            {
                var hoaAssessmentId = jobsToAdd.FirstOrDefault()?.HoaAssessmentId;//below didn't work, relying for now on all hoaJobs to delete are only from one assessment
                //var jobExistsList = await _context.HoaJobs.Where(x => jobsToAdd.Select(y => new { jobNum = y.JobNumber, assessmentId = y.HoaAssessmentId }).Contains(new { jobNum = x.JobNumber, assessmentId = x.HoaAssessmentId })).ToListAsync();
                //var jobsToAddList = jobsToAdd.Where(x => !jobExistsList.Select(y => new { jobNum = y.JobNumber, assessmentId = y.HoaAssessmentId }).Contains(new { jobNum = x.JobNumber, assessmentId = x.HoaAssessmentId })).ToList();
                var list1 = jobsToAdd.Select(x => x.JobNumber).ToList();
                var jobExistsList = await _context.HoaJobs.Where(x => list1.Contains(x.JobNumber) && x.HoaAssessmentId == hoaAssessmentId).ToListAsync();
                var jobsToAddList = jobsToAdd.Where(x => !jobExistsList.Any(y => y.HoaAssessmentId == x.HoaAssessmentId && y.JobNumber == x.JobNumber)).ToList();
                foreach (var findJob in jobExistsList)
                {
                    findJob.IsActive = true;
                    findJob.UpdatedBy = User.Identity.Name.Split('@')[0];
                    findJob.UpdatedDateTime = DateTime.Now;
                }
                await _context.HoaJobs.BulkUpdateAsync(jobExistsList);

                var insertJobs = _mapper.Map<List<HoaJob>>(jobsToAddList);
                await _context.HoaJobs.BulkInsertAsync(insertJobs);
                return Ok(new ResponseModel<List<HoaJobDto>>() { Value = jobsToAdd, Message = "Added jobs to hoa assessment", IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<HoaJobDto>>() { IsSuccess = false, Message = "Failed to add jobs job", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> DeleteSelectedJobsForAssessmentAsync([FromBody] List<HoaJobDto> hoaJobs)
        {
            try
            {
                var hoaAssessmentId = hoaJobs.FirstOrDefault()?.HoaAssessmentId;//below didn't work, relying for now on all hoaJobs to delete are only from one assessment
                                                                      // var list1 = hoaJobs.Select(x => new { jobNum = x.JobNumber, assessmentId = x.HoaAssessmentId }).ToList();
                var list1 = hoaJobs.Select(x => x.JobNumber).ToList();
                var jobList = await _context.HoaJobs.Where(x => list1.Contains(x.JobNumber) && x.HoaAssessmentId == hoaAssessmentId).ToListAsync();
                if (jobList != null)
                {
                    foreach (HoaJob job in jobList)
                    {
                        job.IsActive = false;
                        job.UpdatedBy = User.Identity.Name.Split('@')[0];
                        job.UpdatedDateTime = DateTime.Now;                        
                    }
                    await _context.HoaJobs.BulkUpdateAsync(jobList);
                }
                return Ok(new ResponseModel<List<HoaJobDto>>() { Value = hoaJobs, Message = "Deleted jobs from assessment", IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
            }
            return StatusCode(500, new ResponseModel<List<HoaJobDto>> { IsSuccess = false, Message = "Failed to delete jobs for assessment", Value = hoaJobs });
        }
    }
}