﻿@using ERP.Data.Models;
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject PlanService PlanService
@using ERP.Data.Models.Dto;

<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Item
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@ItemToAdd" OnValidSubmit="@HandleValidAddSubmit">
            @*TODO: they may want to pick by option group/option/item, or by Trade/purchasingActivity/item*@
            @*TODO: option-specific items?*@
            <p>Select Item to Add to Option: @Option.AssemblyDesc</p>
            <div class="mb-3">
                <label class="form-label">Trade</label><br />
                @if(AllTrades != null && AllTrades.Count != 0)
                {
                    <TelerikDropDownList Data="@AllTrades"
                    @bind-Value="@TradeId"
                                         OnChange="@OnChangeTradeHandler"
                                         TextField="TradeName"
                                         ValueField="TradeId"
                                         DefaultText="Select Trade"
                                         Width="100%"
                                         FilterOperator="@StringFilterOperator.Contains"
                                         Filterable="true">
                        <DropDownListSettings>
                            <DropDownListPopupSettings Height="200px"></DropDownListPopupSettings>
                        </DropDownListSettings>
                    </TelerikDropDownList>
                }
                else
                {
                    <div>Loading...</div>
                }
            </div>
            <div class="mb-3">
                <label class="form-label">Purchasing Activity</label><br />
                <TelerikDropDownList Data="@AllPurchasingActivities"
                                     @bind-Value="@ItemToAdd.PactivityId"
                                     OnChange="@OnChangeActivityHandler"
                                     TextField="Activity"
                                     ValueField="PactivityId"
                                     DefaultText="Select Activity"
                                     Width="100%"
                                     FilterOperator="@StringFilterOperator.Contains"
                                     Filterable="true">
                    <DropDownListSettings>
                        <DropDownListPopupSettings Height="200px"></DropDownListPopupSettings>
                    </DropDownListSettings>
                </TelerikDropDownList>
            </div>
            <div class="mb-3">
                <label class="form-label">Item</label><br />
                <TelerikDropDownList Data="@AllMasterItems"
                                     @bind-Value="@ItemToAdd.MasterItemId"
                                     OnChange="@SelectedItemChanged"
                                     TextField="ItemDesc"
                                     ValueField="MasterItemId"
                                     DefaultText="Select Item"
                                     Width="100%" Filterable="true">
                    <DropDownListSettings>
                        <DropDownListPopupSettings Height="200px"></DropDownListPopupSettings>
                    </DropDownListSettings>
                </TelerikDropDownList>
            </div>
            <div class="mb-3">
                <label class="form-label">Quantity</label><br />
                <TelerikNumericTextBox @bind-Value="@ItemToAdd.Factor"></TelerikNumericTextBox>
            </div>
           <div class="mb-3">
                <label>Select Multiple Other Plan Options to Add Selected Item To</label>
                <TelerikMultiSelect @bind-Value="@SelectedMasterPlans"
                                    TextField="PlanName"
                                    ValueField="MasterPlanId"
                                    OnChange="@LoadAsmHeaders"
                                    Placeholder="Select Master Plan"
                                    Filterable="true"
                                    FilterOperator="StringFilterOperator.Contains"
                                    Data="@MasterPlanData"
                                    Width="100%">
                </TelerikMultiSelect>
            </div>
            @if (AllOptionsInSelectedPlan != null && AllOptionsInSelectedPlan.Count != 0)
            {
                <div class="mb-3">
                    <TelerikMultiSelect @bind-Value="@SelectedOptionsInMasterPlans"
                                        Filterable="true"
                                        FilterOperator="StringFilterOperator.Contains"
                                        TextField="AssemblyDesc"
                                        ValueField="AsmHeaderId"
                                        Placeholder="Select Options"
                                        Data="@AllOptionsInSelectedPlan"
                                        Width="100%">
                    </TelerikMultiSelect>
                </div>
            }
            <div>
                <p>Plan Options that already have this item:</p>
                @foreach(var option in OptionsThatContainSelectedItem)
                {
                    <p>@option.AssemblyCode - @option.AssemblyDesc</p>
                }

            </div>
            <button type="submit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                    </svg>
                </span> Update
            </button>
            <button type="button" @onclick="CancelAddItem" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-base">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel" aria-hidden="true">
                    <!--!-->
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M256 32c-50.3 0-96.8 16.6-134.1 44.6-17.2 12.8-32.4 28.1-45.3 45.3C48.6 159.2 32 205.7 32 256c0 123.7 100.3 224 224 224 50.3 0 96.8-16.6 134.1-44.6 17.2-12.8 32.4-28.1 45.3-45.3 28-37.4 44.6-83.8 44.6-134.1 0-123.7-100.3-224-224-224zm0 384c-88.2 0-160-71.8-160-160 0-32.6 9.8-62.9 26.6-88.2l221.6 221.6C318.9 406.2 288.6 416 256 416zm133.4-71.8L167.8 122.6C193.1 105.8 223.4 96 256 96c88.2 0 160 71.8 160 160 0 32.6-9.8 62.9-26.6 88.2z"></path><!--!-->
                    </svg><!--!-->
                    <!--!-->
                </span> Cancel
            </button>
            <div style=@submittingStyle>Adding item. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public ModelManagerItemModel ItemToAdd { get; set; } = new ModelManagerItemModel() ;

    [Parameter]
    public AsmHeaderModel Option { get; set; }
    public List<TradeDto>? AllTrades;
    public List<PactivityModel>? AllPurchasingActivities;
    public List<ModelManagerItemModel>? AllMasterItems;
    public List<MasterPlanDto>? MasterPlanData { get; set; }
    public List<AsmHeaderModel> AllAsmHeaderModels { get; set; }
    public List<AsmHeaderModel> AllOptionsInSelectedPlan { get; set; }
    public List<int>? SelectedMasterPlans { get; set; }
    public List<int> SelectedOptionsInMasterPlans { get; set; } = new List<int>();
    public List<AsmHeaderDto>? OptionsThatContainSelectedItem { get; set; } = new List<AsmHeaderDto>();
    public int? SelectedMasterItemId { get; set; } 
    public int? TradeId { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }


    private string submittingStyle = "display:none";
    //public List<int>? ItemsToAdd { get; set; } = new List<int>();
    //public List<int>? AllItemsToAdd {get; set;} = new List<int>();

    [Parameter]
    public EventCallback<List<ModelManagerItemModel>> HandleAddSubmit { get; set; }

    public async Task OnChangeTradeHandler(object theUserInput)
    {
        if(theUserInput != null)
        {
            var userInput = (int)theUserInput;
            AllPurchasingActivities = (await ItemService.GetActivityByTradeAsync((int)TradeId)).Value;
        }
    }

    public async Task OnChangeActivityHandler(object theUserInput)
    {
        if(theUserInput != null)
        {
            var userInput = (int)theUserInput;
            AllMasterItems = (await ItemService.GetItemsInActivityToAddToPlanAsync((int)ItemToAdd.PactivityId)).Value;
        }
    }

    public async Task SelectedItemChanged(object theUserInput)
    {
        if (theUserInput != null && ItemToAdd.MasterItemId != SelectedMasterItemId)
        {
            SelectedMasterItemId = ItemToAdd.MasterItemId;
            var getOptionsThatHaveItem = await ItemService.GetPlanOptionsForMasterItemAsync(ItemToAdd.MasterItemId);
            OptionsThatContainSelectedItem = getOptionsThatHaveItem;
            //TODO: check actually changed
            //TODO: get all the plan options that already have this selected item
        }
    }

    public async Task Show()
    {
        //So first pick pactivity, then pick item, then add.
        IsModalVisible = true;
        ItemToAdd.Factor = 1;//Default to 1. This is quantity
        AllTrades = (await ItemService.GetTradesAsync()).Value;
        //  AllAsmHeaderModels = await ItemService.GetAllAssembliesAsync();//This is too slow. Cannot do this.
        var masterPlanDataResponse = await PlanService.GetMasterPlansAsync();
        MasterPlanData = masterPlanDataResponse.Value;
        StateHasChanged();
    }

    private async Task LoadAsmHeaders(object selectedMasterPlans)
    {
        if (SelectedMasterPlans != null)
        {
            AllOptionsInSelectedPlan = (await ItemService.GetAllAssembliesForPlansAsync(SelectedMasterPlans)).Value;
            //  AllOptionsInSelectedPlan = AllAsmHeaderModels.Where(x => SelectedMasterPlans.Contains(x.MasterPlanId ?? -1)).ToList();
        }
    }

    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        SelectedOptionsInMasterPlans.Add(Option.AsmHeaderId);  // adding the current option where the item needs to be added by default
        var items = new List<ModelManagerItemModel>();

        foreach (var optionItemAsmHeaderId in SelectedOptionsInMasterPlans)
        {
            items.Add(new ModelManagerItemModel()
                {
                    MasterItemId = ItemToAdd.MasterItemId,
                    Factor = ItemToAdd.Factor,
                    AsmHeaderId = optionItemAsmHeaderId,
                    MasterPlanId = Option.MasterPlanId,
                    ItemDesc = AllMasterItems.Where(x => x.MasterItemId == ItemToAdd.MasterItemId).SingleOrDefault().ItemDesc
                });
        }

        var responseItem = await ItemService.AddMasterOptionItems(items);
        ItemToAdd = new ModelManagerItemModel();
        SelectedOptionsInMasterPlans = new List<int>();//clear the list for next time
        submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(responseItem.Value);

        // Alert
        if (!responseItem.IsSuccess)
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = responseItem.Message,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = responseItem.Message,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }
    }

    async void CancelAddItem()
    {
        SelectedOptionsInMasterPlans = new List<int>();//clear the list for next time
        ItemToAdd = new ModelManagerItemModel();
        IsModalVisible = false;
    }

    public async Task Hide()
    {
        SelectedOptionsInMasterPlans = new List<int>();//clear the list for next time
        ItemToAdd = new ModelManagerItemModel();
        IsModalVisible = false;
    }
}
