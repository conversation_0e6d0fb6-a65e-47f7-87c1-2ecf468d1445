﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Role
{
    public int RoleId { get; set; }

    public string? RoleName { get; set; }

    public string? Notes { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual ICollection<JobContact> JobContacts { get; set; } = new List<JobContact>();

    public virtual ICollection<VpoApprovalSeq> VpoApprovalSeqs { get; set; } = new List<VpoApprovalSeq>();
}
