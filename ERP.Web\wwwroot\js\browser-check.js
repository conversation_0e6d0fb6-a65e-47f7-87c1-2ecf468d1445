﻿window.browserUtils = {
    getBrowserInfo: function () {
        const ua = navigator.userAgent;
        const platform = navigator.platform;

        let browser = "Unknown";
        let version = "";

        if (/edg/i.test(ua)) {
            browser = "Edge";
            version = ua.match(/edg\/(\d+(\.\d+)?)/i)?.[1] ?? "";
        } else if (/chrome|crios/i.test(ua)) {
            browser = "Chrome";
            version = ua.match(/chrome\/(\d+(\.\d+)?)/i)?.[1] ?? "";
        } else if (/firefox|fxios/i.test(ua)) {
            browser = "Firefox";
            version = ua.match(/firefox\/(\d+(\.\d+)?)/i)?.[1] ?? "";
        } else if (/safari/i.test(ua) && !/chrome|crios|edg/i.test(ua)) {
            browser = "Safari";
            version = ua.match(/version\/(\d+(\.\d+)?)/i)?.[1] ?? "";
        }

        return {
            browser,
            version,
            platform
        };
    }
};
