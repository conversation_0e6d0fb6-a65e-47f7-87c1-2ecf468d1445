﻿@using ERP.Data.Models;
@inject TradeService TradeService
@using ERP.Data.Models.Dto;
<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Supplier
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@SupplierToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <div class="mb-3">
                <label class="form-label">Supplier Name</label><br />
                <TelerikTextBox @bind-Value="@SupplierToAdd.SubName"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Short Name</label><br />
                <TelerikTextBox Enabled="false" @bind-Value="@SupplierToAdd.ShortName"></TelerikTextBox>
            </div>
            <button type="submit" class="btn btn-primary">Add</button>
            <button type="button" @onclick="CancelAddItem" class="btn btn-secondary">Cancel</button>
            <div style=@submittingStyle>Adding item. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    //public TradeSupplierModel SupplierToAdd { get; set; } = new TradeSupplierModel();
    public SimplerSupplierDto SupplierToAdd { get; set; } = new SimplerSupplierDto();

    private string submittingStyle = "display:none";

    [Parameter]
    //public EventCallback<ResponseModel<TradeSupplierModel>> HandleAddSubmit { get; set; }
    public EventCallback<ResponseModel<SimplerSupplierDto>> HandleAddSubmit { get; set; }

    public async Task Show()
    {
        IsModalVisible = true;
        var nextOneTimeBidNumber = (await TradeService.GetNextOneTimeBidAsync()).Value;
        SupplierToAdd.ShortName = $"ONETIMEBID{nextOneTimeBidNumber}";
        StateHasChanged();
    }

    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        var responseItem = await TradeService.AddSupplierAsync(SupplierToAdd);
        submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(responseItem);
    }
    async void CancelAddItem()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {

        IsModalVisible = false;
    }
}
