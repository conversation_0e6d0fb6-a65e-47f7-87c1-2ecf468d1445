﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class SupplierDto : IMapFrom<Supplier>
{
    public int SubNumber { get; set; }
    public bool? LinkWithErp { get; set; }//for BC integration

    public string? ShortName { get; set; }

    public string? SubName { get; set; }

    public string? SubApNumber { get; set; }

    public string? SubAddress { get; set; }

    public string? SubAddress2 { get; set; }

    public string? SubCity { get; set; }

    public string? SubState { get; set; }

    public string? SubCountry { get; set; }

    public string? SubPostcode { get; set; }

    public string? SubPhone { get; set; }

    public string? SubPhone2 { get; set; }

    public string? SubPhone3 { get; set; }

    public string? SubFax { get; set; }

    public string? SubFax2 { get; set; }

    public string? SubContact { get; set; }

    public string? SubContact2 { get; set; }

    public string? SubContact3 { get; set; }

    public string? SubContact1Pos { get; set; }

    public string? SubContact2Pos { get; set; }

    public string? SubContact3Pos { get; set; }

    public string? SubNotes { get; set; }

    public string? SubType1 { get; set; }

    public string? SubType2 { get; set; }

    public string? SubType3 { get; set; }

    public string? SubType4 { get; set; }

    public int? OrderMode { get; set; }

    public string? EdiOrders { get; set; }

    public string? Email { get; set; }

    public int? Ediorderbatchno { get; set; }

    public string? Supplierarcode { get; set; }

    public string? EdiInterchange { get; set; }

    public string? EdiGscode { get; set; }

    public string? Subtaxgroup { get; set; }

    public string? Subtaxexempt { get; set; }
    public bool? IsSubtaxexempt
    {
        get { return Subtaxexempt == "T"; }
    }

    public double? DiscountPercent { get; set; }

    public int? DiscountDays { get; set; }

    public int? PaymentDays { get; set; }

    public string? PmtDaysType { get; set; }

    public double? MiscDeductRate { get; set; }

    public double? MiscDeduct2Rate { get; set; }

    public string? PrefilledTaxAmount { get; set; }

    public string? ReceivesForm1099 { get; set; }
    public bool? IsReceivesForm1099
    {
        get { return ReceivesForm1099 == "T"; }
    }

    public string? AbnNumber { get; set; }

    public string? Phone { get; set; }

    public string? Fax { get; set; }

    public string? SubFax3 { get; set; }

    public string? SubContactDc { get; set; }

    public string? SubContact2Dc { get; set; }

    public string? SubContact3Dc { get; set; }

    public string? SubContactMobile { get; set; }

    public string? SubContact2Mobile { get; set; }

    public string? SubContact3Mobile { get; set; }

    public int? SubContactMobileSp { get; set; }

    public int? SubContact2MobileSp { get; set; }

    public int? SubContact3MobileSp { get; set; }

    public string? SubContactEmail { get; set; }

    public string? SubContact2Email { get; set; }

    public string? SubContact3Email { get; set; }

    public string? SubContactPurch { get; set; }

    public string? SubContact2Purch { get; set; }

    public string? SubContact3Purch { get; set; }

    public string? SubContactSched { get; set; }

    public string? SubContact2Sched { get; set; }

    public string? SubContact3Sched { get; set; }

    public string? GlInsRequired { get; set; }

    public DateTime? GlInsExpiryDate { get; set; }

    public string? WcompInsRequired { get; set; }

    public DateTime? WcompInsExpiryDate { get; set; }

    public string? PoPlugSupplier { get; set; }

    public double? Retainage { get; set; }

    public string? SubContactPassword { get; set; }

    public string? SubContact2Password { get; set; }

    public string? SubContact3Password { get; set; }

    public string? SubContactIsadmin { get; set; }

    public string? SubContact2Isadmin { get; set; }

    public string? SubContact3Isadmin { get; set; }

    public string? IsActive { get; set; }

    public string? WarrantyEnabled { get; set; }
    public bool? IsWarrantyEnabled
    {
        get { return WarrantyEnabled == "T"; }
    }

    public int? OrganizationId { get; set; }

    public int? OrganizationUserId { get; set; }

    public string? SubContactWrty { get; set; }

    public string? SubContactWrtyPos { get; set; }

    public string? SubPhoneWrty { get; set; }

    public string? SubFaxWrty { get; set; }

    public string? SubContactWrtyDc { get; set; }

    public string? SubContactWrtyMobile { get; set; }

    public int? SubContactWrtyMobileSp { get; set; }

    public string? SubContactWrtyEmail { get; set; }

    public string? TradePortalSupplier { get; set; }

    public string? TermsAccepted { get; set; }

    public DateTime? TermsAcceptedDate { get; set; }

    public string? TermsAcceptedUser { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive1 { get; set; }

    public string? VehicleInsRequired { get; set; }

    public DateTime? VehicleInsExpiryDate { get; set; }

    public string? MasterAgreementRequired { get; set; }

    public DateTime? MasterAgreementExpiryDate { get; set; }

    public string? Create2ndLienWaiverHold { get; set; }

    public string? UsesReservedCosts { get; set; }
    public DateTime? InsuranceStartDate { get; set; }
    public DateTime? InsuranceExpirationDate { get; set; }

    public bool InsuranceExpired { get; set; }
    public bool? Blocked { get; set; }
    public bool BoolBlocked { get; set; }

    // public byte[]? RecordTimeStamp { get; set; }

    //public virtual ICollection<Cost> Costs { get; set; } = new List<Cost>();

    // public virtual ICollection<Estdetail> Estdetails { get; set; } = new List<Estdetail>();

    //  public virtual ICollection<Poheader> Poheaders { get; set; } = new List<Poheader>();

    //  public virtual ICollection<SupplierContact> SupplierContacts { get; set; } = new List<SupplierContact>();

    //  public virtual ICollection<TradeSupplier> TradeSuppliers { get; set; } = new List<TradeSupplier>();

    //public virtual ICollection<WorksheetOptAct> WorksheetOptActs { get; set; } = new List<WorksheetOptAct>();

    //public virtual ICollection<WorksheetPlanAct> WorksheetPlanActs { get; set; } = new List<WorksheetPlanAct>();

    public string? ContactName { get; set; }

    public void Mapping(Profile profile)
    {
        profile.CreateMap<SupplierDto, Supplier>().ReverseMap();
    }
}
