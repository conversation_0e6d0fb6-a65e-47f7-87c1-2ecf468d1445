﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class HomeAreaDto: IMapFrom<HomeArea>
{
    public int HomeAreaId { get; set; }

    public string? HomeArea1 { get; set; }

    public string? Note { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }


}
