﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class WorksheetPlanModel
{
    public int WorksheetPlanId { get; set; }

    public int WorksheetId { get; set; }
    public int? SubdivisionId { get; set; }
    public string? SubdivisionName { get; set; }
    public int PhasePlanId { get; set; }
    public string? PlanName { get; set; }
    public string? PlanNumber { get; set; }

    public double? Costprice { get; set; }

    public double? Markup { get; set; }

    public double? Sellprice { get; set; }

    public DateTime? Pricedate { get; set; }

    public int? Markuptype { get; set; }

    public double? Markuppercent { get; set; }

    public double? Marketvalue { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }
    public int? Warnings { get; set; }
    public int? Errors { get; set; }



}
