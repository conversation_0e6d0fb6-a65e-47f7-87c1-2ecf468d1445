﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class SupplierType
{
    public int SubTypeId { get; set; }

    public string? SubTypeName { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual ICollection<SupplierTradeType> SupplierTradeTypes { get; set; } = new List<SupplierTradeType>();
}
