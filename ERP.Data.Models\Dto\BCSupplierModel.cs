﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class BCSupplierModel : IMapFrom<Supplier>
{
    public int SubNumber { get; set; }
    public bool? LinkWithErp { get; set; }//for BC integration

    public string? ShortName { get; set; }

    public string? SubName { get; set; }

    public string? SubApNumber { get; set; }

    public string? SubAddress { get; set; }

    public string? SubAddress2 { get; set; }

    public string? SubCity { get; set; }

    public string? SubState { get; set; }

    public string? SubCountry { get; set; }

    public string? SubPostcode { get; set; }

    public string? SubPhone { get; set; }

    public string? SubPhone2 { get; set; }

    public string? SubFax { get; set; }

    public string? SubContact { get; set; }

    public string? SubContact2 { get; set; }

    public string? SubContact3 { get; set; }

    public string? Email { get; set; }  

    public string? Phone { get; set; }

    public string? Fax { get; set; }
 
    public string? IsActive { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive1 { get; set; }

    public bool? Blocked { get; set; }

    public void Mapping(Profile profile)
    {
        profile.CreateMap<BCSupplierModel, Supplier>().ReverseMap();
    }
}
