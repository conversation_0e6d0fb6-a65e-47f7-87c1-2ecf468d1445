﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class CostsHistory
{
    public int CostsHistoryId { get; set; }

    public int? CostsId { get; set; }

    public int? MasterItemsId { get; set; }

    public int? SubNumber { get; set; }

    public int? SubdivisionId { get; set; }

    public double? OldCost { get; set; }

    public DateTime? CostExpired { get; set; }

    public string? CostRolledOverBy { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual Cost? Costs { get; set; }
}
