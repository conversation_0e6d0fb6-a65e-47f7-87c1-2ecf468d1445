﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class MasterItemPhasis
{
    public int MasterItemPhaseId { get; set; }

    public int MasterItemGroupId { get; set; }

    public string? PhaseCode { get; set; }

    public string? PhaseDesc { get; set; }

    public string? PhaseNotes { get; set; }

    public string? PhaseUnitDesc { get; set; }

    public string? JcPhaseCode { get; set; }

    public string? MatrixDesc1 { get; set; }

    public string? MatrixDesc2 { get; set; }

    public string? DeletedFromPe { get; set; }

    public int? AsmHeaderId { get; set; }

    public int? EstDbOwner { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual AsmHeader? AsmHeader { get; set; }

    public virtual ICollection<MasterItem> MasterItems { get; set; } = new List<MasterItem>();
}
