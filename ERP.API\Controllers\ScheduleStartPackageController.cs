﻿using AutoMapper;
using ERP.API.Utilities;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Data.Models.ExtensionMethods;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using NLog;
using NLog.Fluent;
using System.Collections.Generic;
using System;
using System.Data;
using System.Diagnostics;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using NLog.Targets;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class ScheduleStartPackageController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly Email _email;
        private readonly IMapper _mapper;
        public ScheduleStartPackageController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _email = new Email(configuration);
        }
       
        [HttpGet("{subdivisionId}")]
        public async Task<IActionResult> PackageNamesBySubdivisionIdAsync(int subdivisionId)
        {
            try
            {
                var packageNames = await _context.ScheStartPackages.Where(x => x.SubdivisionId == subdivisionId && x.IsActive == true).ToListAsync();
                var packageNamesDto = _mapper.Map<List<ScheStartPackageDto>>(packageNames);
                return new OkObjectResult(new ResponseModel<List<ScheStartPackageDto>> { Value = packageNamesDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ScheStartPackageDto>> { IsSuccess = false, Message = "Failed to get Package Names by Subdivision", Value = new List<ScheStartPackageDto>() });
            }
        }
        [HttpPost]
        public async Task<IActionResult> AddPackageNameAsync([FromBody] ScheStartPackageDto packageNametoAdd)
        {
            try
            {
                var checkIfExisting = await _context.ScheStartPackages.SingleOrDefaultAsync(x => x.PackageName.ToLower() == packageNametoAdd.PackageName.ToLower());
                if (checkIfExisting != null)
                {
                    checkIfExisting.IsActive = true;
                    checkIfExisting.UpdatedDateTime = DateTime.Now;
                    checkIfExisting.UpdatedBy = User.Identity.Name.Split('@')[0];
                }
                else
                {
                    var newPackageName = new ScheStartPackage();
                    newPackageName.PackageName = packageNametoAdd.PackageName;
                    newPackageName.SubdivisionId = packageNametoAdd.SubdivisionId;
                    newPackageName.IsActive = true;
                    newPackageName.CreatedBy = User.Identity.Name.Split('@')[0];
                    newPackageName.CreatedDateTime = DateTime.Now;
                    _context.ScheStartPackages.Add(newPackageName);
                }
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<ScheStartPackageDto> { Value = packageNametoAdd, IsSuccess = true, Message = "Package Name added successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheStartPackageDto> { IsSuccess = false, Message = "Failed to Add Package Name", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdatePackageNameAsync([FromBody] ScheStartPackageDto packageToUpdate)
        {
            try
            {
                var packageName = await _context.ScheStartPackages.SingleOrDefaultAsync(x => x.PackageId == packageToUpdate.PackageId);
                if (packageName != null)
                {
                    packageName.PackageName = packageToUpdate.PackageName;
                    packageName.IsActive = true;
                    packageName.UpdatedBy = User.Identity.Name.Split('@')[0];
                    packageName.UpdatedDateTime = DateTime.Now;
                    _context.ScheStartPackages.Update(packageName);
                    await _context.SaveChangesAsync();
                }

                return new OkObjectResult(new ResponseModel<ScheStartPackageDto> { Value = packageToUpdate, IsSuccess = true, Message = "Package Name updated successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheStartPackageDto> { IsSuccess = false, Message = "Failed to Update Package Name", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeletePackageNameAsync([FromBody] ScheStartPackageDto packageNameToDelete)
        {
            try
            {
                var packageName = await _context.ScheStartPackages.SingleOrDefaultAsync(x => x.PackageId == packageNameToDelete.PackageId);
                if (packageName != null)
                {
                    packageName.IsActive = false;
                    packageName.UpdatedBy = User.Identity.Name.Split('@')[0];
                    packageName.UpdatedDateTime = DateTime.Now;
                    _context.ScheStartPackages.Update(packageName);
                    await _context.SaveChangesAsync();

                    var packageItems = await _context.SchePackageItems.Where(x => x.PackageId == packageNameToDelete.PackageId).ToListAsync();
                    foreach (var packageItem in packageItems)
                    {
                        packageItem.IsActive = false;
                        packageItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                        packageItem.UpdatedDateTime = DateTime.Now;
                    }
                    await _context.BulkUpdateAsync(packageItems);
                }

                return new OkObjectResult(new ResponseModel<ScheStartPackageDto> { Value = packageNameToDelete, IsSuccess = true, Message = "Package Name deleted successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<ScheStartPackageDto> { IsSuccess = false, Message = "Failed to Delete Package Name", Value = null });
            }
        }
        [HttpGet("{packageId}")]
        public async Task<IActionResult> PackageItemsAsync(int packageId)
        {
            try
            {
                var packageItems = await _context.SchePackageItems.Where(x => x.PackageId == packageId && x.IsActive == true).Include("SchePackageAssignments").ToListAsync();
                var packageItemsDto = _mapper.Map<List<SchePackageItemDto>>(packageItems);
                return new OkObjectResult(new ResponseModel<List<SchePackageItemDto>> { Value = packageItemsDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SchePackageItemDto>> { IsSuccess = false, Message = "Failed to get Package Items", Value = new List<SchePackageItemDto>() });
            }
        }
        [HttpPost]
        public async Task<IActionResult> AddPackageItemAsync([FromBody] SchePackageItemDto packageItemtoAdd)
        {
            try
            {
                var checkIfExisting = await _context.SchePackageItems.SingleOrDefaultAsync(x => x.ItemName.ToLower() == packageItemtoAdd.ItemName.ToLower() && x.PackageId == packageItemtoAdd.PackageId);
                if (checkIfExisting != null)
                {
                    packageItemtoAdd.PackageItemId = checkIfExisting.PackageItemId;
                    checkIfExisting.IsActive = true;
                    checkIfExisting.UpdatedDateTime = DateTime.Now;
                    checkIfExisting.UpdatedBy = User.Identity.Name.Split('@')[0];
                }
                else
                {
                    var newPackageItem = new SchePackageItem();
                    newPackageItem.ItemName = packageItemtoAdd.ItemName;
                    newPackageItem.PackageId = packageItemtoAdd.PackageId;
                    newPackageItem.WebLink = packageItemtoAdd.WebLink;
                    newPackageItem.Notes = packageItemtoAdd.Notes;
                    newPackageItem.IsActive = true;
                    newPackageItem.CreatedBy = User.Identity.Name.Split('@')[0];
                    newPackageItem.CreatedDateTime = DateTime.Now;
                    _context.SchePackageItems.Add(newPackageItem);
                    await _context.SaveChangesAsync();
                    packageItemtoAdd.PackageItemId = newPackageItem.PackageItemId;
                }

                await UpdateItemAssignment(packageItemtoAdd);

                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<SchePackageItemDto> { Value = packageItemtoAdd, IsSuccess = true, Message = "Sucessfully added Package Item" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SchePackageItemDto> { IsSuccess = false, Message = "Failed to Add Package Item", Value = null });
            }
        }

        private async Task UpdateItemAssignment(SchePackageItemDto packageItemToAdd)
        {
            foreach (var itemAssignment in packageItemToAdd.SchePackageAssignments)
            {
                if (itemAssignment.ItemAssignmentId == 0)
                {
                    var checkIfExisting = await _context.SchePackageAssignments.SingleOrDefaultAsync(x => x.PackageItemId == packageItemToAdd.PackageItemId && x.DepartmentAssigned == itemAssignment.DepartmentAssigned && x.Manager == itemAssignment.Manager);
                    if (checkIfExisting != null)
                    {
                        checkIfExisting.IsActive = true;
                        checkIfExisting.UpdatedDateTime = DateTime.Now;
                        checkIfExisting.UpdatedBy = User.Identity.Name.Split('@')[0];
                        _context.SchePackageAssignments.Update(checkIfExisting);
                    }
                    else
                    {
                        var newPackageAssignment = new SchePackageAssignment();
                        newPackageAssignment.PackageItemId = packageItemToAdd.PackageItemId;
                        newPackageAssignment.Manager = itemAssignment.Manager;
                        newPackageAssignment.DepartmentAssigned = itemAssignment.DepartmentAssigned;
                        newPackageAssignment.IsActive = true;
                        newPackageAssignment.CreatedBy = User.Identity.Name.Split('@')[0];
                        newPackageAssignment.CreatedDateTime = DateTime.Now;
                        _context.SchePackageAssignments.Add(newPackageAssignment);
                    }
                }
                else
                {
                    var existingAssignment = await _context.SchePackageAssignments.SingleOrDefaultAsync(x => x.ItemAssignmentId == itemAssignment.ItemAssignmentId);
                    if (existingAssignment != null)
                    {
                        existingAssignment.DepartmentAssigned = itemAssignment.DepartmentAssigned;
                        existingAssignment.Manager = itemAssignment.Manager;
                        existingAssignment.UpdatedDateTime = DateTime.Now;
                        existingAssignment.UpdatedBy = User.Identity.Name.Split('@')[0];
                        _context.SchePackageAssignments.Update(existingAssignment);
                    }
                }
            }
            var assignmentList = await _context.SchePackageAssignments.Where(x => x.PackageItemId == packageItemToAdd.PackageItemId && x.IsActive == true).ToListAsync();
            var assignmentIdList = packageItemToAdd.SchePackageAssignments.Select(x => x.ItemAssignmentId).ToList();
            foreach (var assignment in assignmentList)
            {
                if (!assignmentIdList.Contains(assignment.ItemAssignmentId))
                {
                    assignment.IsActive = false;
                    assignment.UpdatedDateTime = DateTime.Now;
                    assignment.UpdatedBy = User.Identity.Name.Split('@')[0];
                    _context.SchePackageAssignments.Update(assignment);
                }
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdatePackageItemAsync([FromBody] SchePackageItemDto packageItemToUpdate)
        {
            try
            {
                var packageItem = await _context.SchePackageItems.SingleOrDefaultAsync(x => x.PackageItemId == packageItemToUpdate.PackageItemId);
                if (packageItem != null)
                {
                    packageItem.ItemName = packageItemToUpdate.ItemName;
                    packageItem.WebLink = packageItemToUpdate.WebLink;
                    packageItem.Notes = packageItemToUpdate.Notes;
                    packageItem.FinishDate = packageItemToUpdate.FinishDate;
                    packageItem.IsActive = true;
                    packageItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                    packageItem.UpdatedDateTime = DateTime.Now;
                    _context.SchePackageItems.Update(packageItem);

                    await UpdateItemAssignment(packageItemToUpdate);

                    await _context.SaveChangesAsync();
                }

                return new OkObjectResult(new ResponseModel<SchePackageItemDto> { Value = packageItemToUpdate, IsSuccess = true, Message = "Sucessfully updated Package Item" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SchePackageItemDto> { IsSuccess = false, Message = "Failed to Update Package Item", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeletePackageItemAsync([FromBody] SchePackageItemDto packageItemToDelete)
        {
            try
            {
                var packageItem = await _context.SchePackageItems.SingleOrDefaultAsync(x => x.PackageItemId == packageItemToDelete.PackageItemId);
                if (packageItem != null)
                {
                    packageItem.IsActive = false;
                    packageItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                    packageItem.UpdatedDateTime = DateTime.Now;
                    _context.SchePackageItems.Update(packageItem);

                    var assignmentList = await _context.SchePackageAssignments.Where(x => x.PackageItemId == packageItemToDelete.PackageItemId && x.IsActive == true).ToListAsync();
                    foreach (var assignment in assignmentList)
                    {
                        assignment.IsActive = false;
                        assignment.UpdatedDateTime = DateTime.Now;
                        assignment.UpdatedBy = User.Identity.Name.Split('@')[0];
                        _context.SchePackageAssignments.Update(assignment);
                    }
                    await _context.SaveChangesAsync();
                }


                return new OkObjectResult(new ResponseModel<SchePackageItemDto> { Value = packageItemToDelete, IsSuccess = true, Message = "Package item deleted successfully" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SchePackageItemDto> { IsSuccess = false, Message = "Failed to Delete Package Item", Value = null });
            }
        }
        [HttpGet("{packageItemId}")]
        public async Task<IActionResult> PackageItemAssignmentAsync(int packageItemId)
        {
            try
            {
                var packageItemAssignment = await _context.SchePackageAssignments.Where(x => x.PackageItemId == packageItemId && x.IsActive == true).ToListAsync();
                var packageItemAssignmentDto = _mapper.Map<List<SchePackageAssignmentDto>>(packageItemAssignment);
                return new OkObjectResult(new ResponseModel<List<SchePackageAssignmentDto>> { Value = packageItemAssignmentDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SchePackageAssignmentDto>> { IsSuccess = false, Message = "Failed to get Package Item Assignment", Value = new List<SchePackageAssignmentDto>() });
            }
        }
        [HttpGet]
        public async Task<IActionResult> AllPackageItemAssignmentAsync()
        {
            try
            {
                var packageItemAssignment = await _context.SchePackageAssignments.Where(x => x.IsActive == true).ToListAsync();
                var packageItemAssignmentDto = _mapper.Map<List<SchePackageAssignmentDto>>(packageItemAssignment);
                return new OkObjectResult(new ResponseModel<List<SchePackageAssignmentDto>> { Value = packageItemAssignmentDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SchePackageAssignmentDto>> { IsSuccess = false, Message = "Failed to get All Package Item Assignment", Value = new List<SchePackageAssignmentDto>() });
            }
        }
        [HttpGet]
        public async Task<IActionResult> CalendarAsync()
        {
            try
            {
                var calendar = await _context.CalendarsDays.Where(x => x.IsActive == true).OrderBy(x => x.WorkDate).ToListAsync();
                var calendarsDto = _mapper.Map<List<CalendarsDayDto>>(calendar);
                return new OkObjectResult(new ResponseModel<List<CalendarsDayDto>> { Value = calendarsDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CalendarsDayDto>> { IsSuccess = false, Message = "Failed to get Calendar" });
            }
        }
    }
}
