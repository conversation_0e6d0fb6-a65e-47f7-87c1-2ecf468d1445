﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class TemplateSactivityDto : IMapFrom<TemplateSactivity>
{
    public int TemplateAid { get; set; }

    public int TemplateMid { get; set; }

    public int TemplateIdNotused { get; set; }

    public int SactivityId { get; set; }

    public int? Seq { get; set; }

    public int? Duration { get; set; }

    public int? LagTime { get; set; }

    public string? UserAlpha1 { get; set; }

    public string? UserAlpha2 { get; set; }

    public string? UserAlpha3 { get; set; }

    public string? UserAlpha4 { get; set; }

    public string? UserAlpha5 { get; set; }

    public string? UserAlpha6 { get; set; }

    public DateTime? UserDate1 { get; set; }

    public DateTime? UserDate2 { get; set; }

    public DateTime? UserDate3 { get; set; }

    public DateTime? UserDate4 { get; set; }

    public DateTime? UserDate5 { get; set; }

    public DateTime? UserDate6 { get; set; }

    public string? Note { get; set; }

    public int? ChecklistId { get; set; }

    public double? UserCurrency1 { get; set; }

    public double? UserCurrency2 { get; set; }

    public double? UserCurrency3 { get; set; }

    public double? UserCurrency4 { get; set; }

    public double? UserCurrency5 { get; set; }

    public double? UserCurrency6 { get; set; }

    public string? GrossLag { get; set; }

    public string? GenPitBudget { get; set; }

    public DateTime Createddatetime { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public string? UpdatedBy { get; set; }

    // public byte[] RecordTimeStamp { get; set; } = null!;

    public TemplateMilestoneDto? TemplateM { get; set; } 

    public SactivityDto? Sactivity { get; set; } 

    public TemplateDto? Template { get; set; } 
    public string? Predecessors { get; set; }
    public List<int>? PredIds { get; set; }
   // public ICollection<TemplateSactivityPredDto>? TemplateSactivityPreds { get; set; } = new List<TemplateSactivityPredDto>();

}
