﻿@using ERP.Data.Models
@inject PoService PoService
@inject SubdivisionService SubdivisionService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject VPOItemPickService VPOItemPickService

<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<div class="container-fluid flex">   
    <div class="row d-flex">
        <div class="col-lg-12">
            @if (PoData == null)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingOptions />
            }
            else
            {
                <TelerikGrid @ref="@PoGridRef"
                Data="@PoData"
                EditMode="@GridEditMode.Popup"
                Pageable="true"
                PageSize="20">
                    <GridToolBarTemplate>
                        <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add VPO</GridCommandButton>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <GridSettings>
                        <GridPopupEditSettings Width="550px" MaxHeight="95vh" MaxWidth="95vw" Title="Add VPO"></GridPopupEditSettings>
                        <GridPopupEditFormSettings Context="POContext">
                            <FormTemplate>
                                @{
                                    EditPoHeaderDto = POContext.Item as PoheaderDto;

                                    <TelerikForm Model="@EditPoHeaderDto"
                                    ColumnSpacing="10px"
                                    Columns="1"
                                    ButtonsLayout="@FormButtonsLayout.Stretch"
                                    OnValidSubmit="@OnValidSubmit">
                                        <FormValidation>
                                            <DataAnnotationsValidator></DataAnnotationsValidator>
                                        </FormValidation>
                                        <FormItems>

                                            @{
                                                if (EditPoHeaderDto.PoheaderId == 0)
                                                {
                                                    <FormItem Field="Podescription" LabelText="Description" Enabled="true"></FormItem>
                                                    <FormItem Field="Pototal" LabelText="Total" Enabled="true"></FormItem>
                                                    <FormItem>
                                                        <Template>
                                                            <label for="position">Community</label>
                                                            <TelerikDropDownList Value="@CurrentJob.SubdivisionId"
                                                            Data="@SubdivisionData"
                                                            DefaultText="Select Subdivision"
                                                            TextField="SubdivisionName" ValueField="SubdivisionId"
                                                            ValueChanged="@( (int c) => SubdivisionSelected(c) )"
                                                            ValueExpression="@( () => CurrentJob.SubdivisionId )"
                                                            Filterable="true"
                                                            FilterOperator="@FilterOperator">
                                                            </TelerikDropDownList>
                                                        </Template>
                                                    </FormItem>
                                                    <FormItem>
                                                        <Template>
                                                            <label for="job">Job</label>
                                                            <TelerikDropDownList Data="@JobsData"
                                                            DefaultText="Select Job"
                                                            TextField="JobNumber" ValueField="JobNumber"
                                                            @bind-Value="@EditPoHeaderDto.Pojobnumber"
                                                            Enabled="@(CurrentJob.SubdivisionId > 0)"
                                                            OnChange="JobSelected"
                                                            Id="job"
                                                            Filterable="true"
                                                            FilterOperator="@FilterOperator">
                                                            </TelerikDropDownList>
                                                        </Template>
                                                    </FormItem>
                                                }
                                                else
                                                {
                                                    <FormItem>
                                                        <Template>
                                                            <label for="job">Job</label>
                                                            <TelerikDropDownList Data="@JobsData"
                                                            DefaultText="Select Job"
                                                            TextField="JobNumber" ValueField="JobNumber"
                                                            @bind-Value="@EditPoHeaderDto.Pojobnumber"
                                                            Enabled="@(CurrentJob.SubdivisionId > 0)"
                                                            OnChange="JobSelected"
                                                            Id="job"
                                                            Filterable="true"
                                                            FilterOperator="@FilterOperator">
                                                            </TelerikDropDownList>
                                                        </Template>
                                                    </FormItem>
                                                }
                                            }

                                            @{
                                                if (EditPoHeaderDto.PoheaderId == 0)
                                                {
                                                    @if (false)
                                                    {

                                                        <FormItem>
                                                            <Template>
                                                                <label for="variance">Variance</label>
                                                                <TelerikDropDownList Data="@VarianceData"
                                                                DefaultText="Select Variance"
                                                                TextField="Category" ValueField="JccategoryId"
                                                                @bind-Value="@EditPoHeaderDto.JccCategoryId"
                                                                Filterable="true"
                                                                FilterOperator="@FilterOperator"></TelerikDropDownList>
                                                            </Template>
                                                        </FormItem>
                                                    }
                                                    <FormItem Field="PurchasingActivityId">
                                                        <Template>
                                                            <label for="variance">Purchasing Activity</label>
                                                            <TelerikDropDownList Data="@PurchasingActivityData"
                                                                                 DefaultText="Select Purchasing Activity"
                                                                                 TextField="DropdownDescription" ValueField="PactivityId"
                                                                                 OnChange="PactivitySelected"
                                                                                 @bind-Value="@EditPoHeaderDto.PurchasingActivityId"
                                                                                 Filterable="true"
                                                                                 FilterOperator="@FilterOperator">
                                                            </TelerikDropDownList>
                                                        </Template>
                                                    </FormItem>
                                                }
                                            }

                                            <FormItem>
                                                <Template>
                                                    <label for="variance">Supplier</label>
                                                    <TelerikDropDownList Value="@EditPoHeaderDto.SubNumber"
                                                                         Data="@SupplierData"
                                                                         DefaultText="Select Supplier"
                                                                         TextField="SubName" ValueField="SubNumber"
                                                                         ValueChanged="@( (int c) => SupplierSelected(c) )"
                                                                         ValueExpression="@( () => CurrentSupplier.SubNumber )"
                                                                         Filterable="true"
                                                                         FilterOperator="@FilterOperator">
                                                    </TelerikDropDownList>
                                                </Template>
                                            </FormItem>

                                            @{
                                                if (EditPoHeaderDto.PoheaderId == 0)
                                                {
                                                    <!-- Backcharge Supplier -->
                                                    <FormItem>
                                                        <Template>
                                                            <label for="variance">Backcharge Supplier</label>
                                                            <TelerikDropDownList @bind-Value="@EditPoHeaderDto.BackchargeSubNumber"
                                                                                 Data="@SupplierData"
                                                                                 DefaultText="Select Backcharge Supplier"
                                                                                 TextField="SubName" ValueField="SubNumber"
                                                                                 Filterable="true"
                                                                                 FilterOperator="@FilterOperator">
                                                            </TelerikDropDownList>
                                                        </Template>
                                                    </FormItem>
                                                    <!-- End of Backcharge Supplier -->
                                                }
                                            }

                                        </FormItems>
                                        <FormButtons>
                                            <TelerikButton Icon="@FontIcon.Save" Class="k-button-success">Save</TelerikButton>
                                            <TelerikButton Icon="@FontIcon.Cancel" ButtonType="@ButtonType.Button" OnClick="@OnCancel">Cancel</TelerikButton>
                                        </FormButtons>
                                    </TelerikForm>
                                }
                            </FormTemplate>

                        </GridPopupEditFormSettings>
                    </GridSettings>
                    <GridColumns>
                        <GridColumn Field="Ponumber" Title="PO Number" Editable="false" Groupable="false" />
                        <GridColumn Field="Pojobnumber" Title="Job Number" Editable="false" Groupable="false" />
                        <GridColumn Field="Pototal" DisplayFormat="{0:C2}" Title="Amount" Editable="true" Groupable="false" />
                        <GridColumn Field="Supplier" Title="Supplier" Editable="true" Groupable="false" />
                        <GridColumn Field="Trade" Title="Trade" Editable="true" Groupable="false" />
                        <GridColumn Field="Community" Title="Community" Editable="true" Groupable="false" />
                        <GridColumn Field="AssignedTo" Title="Assigned To" Editable="true" Groupable="false" />
                        <GridColumn Field="CreatedBy" Title="Created By" Editable="false" Groupable="false" />
                        <GridColumn Field="CreatedDateTime" Title="Created" Editable="false" Groupable="false" />
                        <GridCommandColumn>
                            @{
                                var row = context as PoheaderDto;
                                if (row != null)
                                {
                                    <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                    // if (!row.IsPOApprovalStarted ?? false)
                                    // {
                                    //     <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                    // }
                                    <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                    <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                                }
                            }
                        </GridCommandColumn>
                    </GridColumns>
                    <DetailTemplate>
                        @{
                            PoheaderDto poHeader = context as PoheaderDto;
                            <ERP.Web.Components.NestedVPOs PoJobNumber="@poHeader.Pojobnumber" POHeaderId="@poHeader.PoheaderId" AvoidChanges="@poHeader.IsPOApprovalStarted"></ERP.Web.Components.NestedVPOs>
                        }
                    </DetailTemplate>
                </TelerikGrid>
            }
        </div>
    </div>
</div>


@code {
    public List<PoheaderDto>? PoData { get; set; }
    private TelerikGrid<PoheaderDto>? PoGridRef { get; set; }
    private PoheaderDto EditPoHeaderDto { get; set; }
    public bool IsLoadingOptions { get; set; } = false;
    public bool IsLoadingItem { get; set; } = false;
    private List<SubdivisionDto> SubdivisionData { get; set; }
    private List<JobDto> JobsData { get; set; }
    private List<JccategoryDto> VarianceData { get; set; }
    private List<PactivityDto> PurchasingActivityData { get; set; }
   // private List<TradeDto> TradeData { get; set; }
    private List<SupplierDto> SupplierData { get; set; }
   // private List<SupplierDto> SupplierContactData { get; set; }
    private StringFilterOperator FilterOperator { get; set; } = StringFilterOperator.Contains;
    public CascadeSubdivisionJobDto CurrentJob { get; set; } = new CascadeSubdivisionJobDto();
    public CascadePactivityTradeDto CurrentActivity { get; set; } = new CascadePactivityTradeDto();
    public CascadeSupplierContactDto CurrentSupplier { get; set; } = new CascadeSupplierContactDto();
    public CascadeSupplierContactDto CurrentBackchargeSupplier { get; set; } = new CascadeSupplierContactDto();
    private bool? _isTotalChanged { get; set; }
    private bool UserIsVMDB { get; set; } = false;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
        this.VPOItemPickService.OnDataChanged += GetChildUpdate;
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        UserIsVMDB = user.User.IsInRole("DesignBuild");
    }

    private async Task OnValidSubmit()
    {
        if (EditPoHeaderDto.PoheaderId != 0)
        {
            // Edit
            var editResponse = await PoService.EditVPOAsync(EditPoHeaderDto);
            ShowSuccessOrErrorNotification(editResponse.Message, editResponse.IsSuccess);
        }
        else
        {
            // Create
            var result = await PoService.AddVPOAsync(EditPoHeaderDto);
        }

        await ExitEditAsync();

        var poData = await PoService.GetVPOs();
        PoData = poData.Value;
    }

    private async Task LoadData()
    {
        IsLoadingItem = true;
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];

        var poDataTask = PoService.GetVPOs(userName);
        var subdivisionTask = SubdivisionService.GetSubdivisionsAsync();
        var varianceTask = PoService.GetVariances();
        //var pactivityTask = PoService.GetPurchasingActivities();
        var suplierTask = PoService.GetSupplierData();
        //var jobsTask = SubdivisionService.GetAllJobsAsync();
        await Task.WhenAll(new Task[]{poDataTask, subdivisionTask, suplierTask, varianceTask});
        PoData = poDataTask.Result.Value;
        SubdivisionData = subdivisionTask.Result.Value.OrderBy(x => x.SubdivisionName).ToList();
        VarianceData = varianceTask.Result.Value;
       // PurchasingActivityData = pactivityTask.Result.Value;
        SupplierData = suplierTask.Result.Value;
        //JobsData = jobsTask.Result.Value;
        IsLoadingItem = false;
    }

    private async Task ExitEditAsync()
    {
        var state = PoGridRef?.GetState();
        state.OriginalEditItem = null;
        state.EditItem = null;
        state.InsertedItem = null;

        await PoGridRef?.SetStateAsync(state);
    }
    private async Task OnCancel()
    {
        await ExitEditAsync();
    }
    private async Task SubdivisionSelected(int subdivisionId)
    {
        if (subdivisionId == 0)
        {
            CurrentJob = new CascadeSubdivisionJobDto();
            return;
        }

        var jobsData = await SubdivisionService.GetJobBySubdivisionAsync(subdivisionId, false);
        JobsData = jobsData.Value;

        // var subdivisionData = await SubdivisionService.GetSubdivisionsAsync();
        // var selectedSubdivision = subdivisionData.Value.Where(x => x.SubdivisionId == subdivisionId).FirstOrDefault();

        CurrentJob.SubdivisionId = subdivisionId;
    }
    private async Task JobSelected()
    {
        if (EditPoHeaderDto.Pojobnumber != null && EditPoHeaderDto.Pojobnumber != CurrentJob.JobNumber)
        {
            CurrentJob.JobNumber = EditPoHeaderDto.Pojobnumber;
            var pactivityData = await PoService.GetPurchasingActivitiesByJob(CurrentJob.JobNumber);
            PurchasingActivityData = pactivityData.Value;
            StateHasChanged();
        }
    }
    private async Task PactivitySelected()
    {
        if (EditPoHeaderDto.PurchasingActivityId == 0 && EditPoHeaderDto.PurchasingActivityId != null)
        {
            CurrentActivity = new CascadePactivityTradeDto();
            return;
        }

        // var tradeData = await PoService.GetTradeByActivityAsync(pactivityId);
        // TradeData = tradeData.Value;

        // var activityData = await PoService.GetPurchasingActivities();
        // var selectedActivity = activityData.Value.Where(x => x.PactivityId == pactivityId).FirstOrDefault();

        // EditPoHeaderDto.PurchasingActivityId = selectedActivity.PactivityId;
        // CurrentActivity.PactivityId = selectedActivity.PactivityId;

        //EditPoHeaderDto.PurchasingActivityId = pactivityId;
        CurrentActivity.PactivityId = EditPoHeaderDto.PurchasingActivityId;
    }

    private async Task SupplierSelected(int supplierId)
    {
        if (supplierId == 0)
        {
            CurrentSupplier = new CascadeSupplierContactDto();
            return;
        }

        EditPoHeaderDto.SubNumber = supplierId;

        // var supplierContactData = await PoService.GetContactBySupplierAsync(supplierId);
        // SupplierContactData = supplierContactData.Value;

        // var supplierData = await PoService.GetSupplierData();
        // var selectedSupplier = supplierData.Value.Where(x => x.SubNumber == supplierId).FirstOrDefault();
    }

    async Task GetChildUpdate()
    {
        await this.InvokeAsync(StateHasChanged);

        _isTotalChanged = this.VPOItemPickService.IsChanged;

        if (_isTotalChanged == true)
        {
            PoGridRef?.Rebind();
        }
    }

    public void Dispose()
    {
        this.VPOItemPickService.OnDataChanged -= GetChildUpdate;
    }

    private void ShowSuccessOrErrorNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
