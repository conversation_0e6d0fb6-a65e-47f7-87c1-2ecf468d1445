﻿@using ERP.Data.Models;
@inject HOAService HOAService
@using ERP.Data.Models.Dto;
<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="1000px"
               Height="600px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <h7 class="page-title" style="font-weight:bold">Add Jobs to HOA Assessment</h7>
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@JobToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />

            <div class="k-validation-summary k-messagebox k-messagebox-error p-0" role="alert">
                <ValidationSummary />
            </div>
            <div class="mb-3">
                <label class="form-label">Select Jobs</label><br />                
                <TelerikGrid Height="500px"
                             RowHeight="40"
                            
                             @bind-SelectedItems="@SelectedJobs"
                             FilterMode="@GridFilterMode.FilterMenu"
                             Data="jobs"
                             SelectionMode="GridSelectionMode.Multiple">
                    <GridColumns>
                        <GridCheckboxColumn SelectAllMode="GridSelectAllMode.All"></GridCheckboxColumn>
                        <GridColumn Field="JobNumber" Filterable="true"></GridColumn>
                        <GridColumn Field="JobAddress1" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Address"></GridColumn>                       
                       @*  <GridColumn Field="Subdivision.SubdivisionName" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Subdivision"></GridColumn> *@
                        <GridColumn Field="JobPostingGroup" Filterable="true" FilterMenuType="@FilterMenuType.CheckBoxList"></GridColumn>
                        <GridColumn Field="LotWidth" Filterable="true" FilterMenuType="@FilterMenuType.CheckBoxList"></GridColumn>
                        <GridColumn Field="Phase" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Phase"></GridColumn>
                        <GridColumn Field="BuildingNum" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Building"></GridColumn>
                        <GridColumn Field="StickBuildingNum" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Stick"></GridColumn>
                    </GridColumns>
                </TelerikGrid>
            </div>
            <button type="submit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-save" aria-hidden="true">
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M384 32H64c-17.6 0-32 14.4-32 32v384c0 17.6 14.4 32 32 32h384c17.6 0 32-14.4 32-32V128l-96-96zm-64 32v128h-64V64h64zm128 384-383.9.1-.1-.1V64.1l.1-.1H96v160h256V64h18.7l77.3 77.3V448z"></path><!--!-->
                    </svg>
                </span> Update
            </button>
            <button type="button" @onclick="CancelAddItem" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-base">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel" aria-hidden="true">
                    <!--!-->
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M256 32c-50.3 0-96.8 16.6-134.1 44.6-17.2 12.8-32.4 28.1-45.3 45.3C48.6 159.2 32 205.7 32 256c0 123.7 100.3 224 224 224 50.3 0 96.8-16.6 134.1-44.6 17.2-12.8 32.4-28.1 45.3-45.3 28-37.4 44.6-83.8 44.6-134.1 0-123.7-100.3-224-224-224zm0 384c-88.2 0-160-71.8-160-160 0-32.6 9.8-62.9 26.6-88.2l221.6 221.6C318.9 406.2 288.6 416 256 416zm133.4-71.8L167.8 122.6C193.1 105.8 223.4 96 256 96c88.2 0 160 71.8 160 160 0 32.6-9.8 62.9-26.6 88.2z"></path><!--!-->
                    </svg><!--!-->
                    <!--!-->
                </span> Cancel
            </button>
            <div style=@submittingStyle>Adding item. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public HoaJobDto JobToAdd { get; set; } = new HoaJobDto();
    public List<JobDto> jobs { get; set; }
    public IEnumerable<JobDto> SelectedJobs { get; set; } = Enumerable.Empty<JobDto>();
    public int currentHoaAssessmentId;

    private string submittingStyle = "display:none";
    //public List<int>? ItemsToAdd { get; set; } = new List<int>();
    //public List<int>? AllItemsToAdd {get; set;} = new List<int>();

    [Parameter]
    public EventCallback<ResponseModel<List<HoaJobDto>>> HandleAddSubmit { get; set; }

    public ValidationEvent ValidationEvent { get; set; } = ValidationEvent.Change;

    public async Task Show(int? currentId)
    {
        JobToAdd.HoaAssessmentId = (int)currentId;
        currentHoaAssessmentId = (int)currentId;
        SelectedJobs = Enumerable.Empty<JobDto>();
        IsModalVisible = true;
    }
    public async void TransferJobs(List<JobDto> transferedJobs)
    {
        jobs = transferedJobs;
        SelectedJobs = Enumerable.Empty<JobDto>();
    }
    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        List<HoaJobDto> jobsToAdd = new List<HoaJobDto>();
        foreach(JobDto job in SelectedJobs)
        {
            jobsToAdd.Add(new HoaJobDto { HoaAssessmentId = currentHoaAssessmentId, JobNumber = job.JobNumber });
        }
        var responseItem = await HOAService.AddJobsAsync(jobsToAdd);
        submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(responseItem);
        SelectedJobs = Enumerable.Empty<JobDto>();
    }

    async void CancelAddItem()
    {
        IsModalVisible = false;
    }

    public async Task Hide()
    {
        IsModalVisible = false;
    }
}
