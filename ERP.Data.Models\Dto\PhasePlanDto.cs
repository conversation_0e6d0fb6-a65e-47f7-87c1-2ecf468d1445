﻿using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class PhasePlanDto: IMapFrom<PhasePlan>
{
    public int PhasePlanId { get; set; }

    public int MasterPlanId { get; set; }

    public int SubdivisionId { get; set; }

    public string? Phase { get; set; }

    public int? PlanTypeId { get; set; }

    public decimal? PhasePlanPrice { get; set; }

    public decimal? PhasePlanCost { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public int? MarginType { get; set; }

    public double? MarginPercent { get; set; }

    public double? MarginMarketValue { get; set; }

    public double? MarginLumpSum { get; set; }

    public double? LastCost { get; set; }

    public double? LastMargin { get; set; }

    public double? LastSelling { get; set; }

    public DateTime? LastDate { get; set; }

    public double? CurrCost { get; set; }

    public double? CurrMargin { get; set; }

    public double? CurrSelling { get; set; }

    public DateTime? CurrDate { get; set; }

    public double? NextCost { get; set; }

    public double? NextMargin { get; set; }

    public double? NextSelling { get; set; }

    public DateTime? NextDate { get; set; }

    public int? AreaElevationSource { get; set; }

    public string? AssociatedEstimate { get; set; }

    public int? WarningCount { get; set; }

    public int? MarketingPackageId { get; set; }

    public int? SqFt { get; set; }

    public int? BedRoom { get; set; }

    public decimal? BathRoom { get; set; }

    public string? ImageCode { get; set; }

    public string? PlanName { get; set; }//not in table, using for flat model in ui
    public string? PlanNum { get; set; }//not in table, using for flat model in ui
    public string? SubdivisionPlanNamePlanNumberDisplay { get; set; }//to display in dropdowns with subdivision name and plan number
    public virtual MasterPlanDto? MasterPlan { get; set; }// = null!;


    public virtual SubdivisionDto? Subdivision { get; set; } //= null!;

}
