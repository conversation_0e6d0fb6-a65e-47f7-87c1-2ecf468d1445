﻿@page "/packageitem"
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject ScheduleStartPackageService ScheduleStartPackageService
@inject StartPackageDocumentService StartPackageDocumentService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JS
@attribute [Authorize(Roles = "Admin, ConstructionDirector, ConstructionManager")]
<style>
    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
    .link-button {
        background-color: transparent;
        border: none;
        padding: 0;
        text-decoration: underline;
        cursor: pointer;
        color: blue;
    }

    .delete-button {
        background-color: transparent;
        border: none;
        padding: 0;
        cursor: pointer;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>
<PageTitle>Schedule Package Item</PageTitle>
<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="false" ShowJob="false" ShowSubdivision="true"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<ERP.Web.Components.UploadStartPackageDocument @ref="UploadDocumentModal" SubdivisionId="SelectedSubdivisionId" PackageId="SelectedPackageId" HandleUploadSubmit="HandleUploadDocumentSubmit"></ERP.Web.Components.UploadStartPackageDocument>
<div class="container-fluid flex">
    <div class="row d-flex">
        <div class="col-lg-12">
            <div class="card" style="background-color: #2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Schedule Package Item</h7>
                </div>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item active">Schedule Package Item</li>
        </ol>

        <div class="col-lg-4">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Package Names</h7>
                </div>
            </div>
            @if (PackageNameData == null)
            {
                <p>Select a Subdivision to see Packages</p>
            }
            else
            {
                <TelerikGrid Data="@PackageNameData"
                             OnRowClick="@OnPackageRowClickHandler"
                             EditMode="@GridEditMode.Inline"
                             OnCreate="@CreatePackageHandler"
                             OnUpdate="@UpdatePackageHandler"
                             OnDelete="@DeletePackageHandler"
                             ConfirmDelete="true"
                             Sortable="true">
                    <GridToolBarTemplate>
                        <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Package Name</GridCommandButton>
                    </GridToolBarTemplate>
                    <GridColumns>
                        <GridColumn Field="PackageName" Title="Package Name"></GridColumn>
                        <GridCommandColumn Width="100px">
                            <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true" Class="k-button-success mb-1">Update</GridCommandButton>
                            <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                            <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                            <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true">Cancel</GridCommandButton>
                        </GridCommandColumn>
                    </GridColumns>
                </TelerikGrid>
            }
        </div>
        <div class="col-lg-8">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Package Items</h7>
                </div>
            </div>
            @if (PackageItemsData == null)
            {
                <p>Select a Package to see Items</p>
            }
            else
            {
                <TelerikGrid @ref="@ItemGridRef"
                             Data="@PackageItemsData"
                             EditMode="@GridEditMode.Popup"
                             OnCancel="@CloseEdit"
                             OnDelete="@DeletePackageItemHandler"
                             ConfirmDelete="true"
                             Sortable="true">
                    <GridToolBarTemplate>
                        <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Package Item</GridCommandButton>
                        <GridCommandButton Icon="@FontIcon.Upload" Class="k-button-add" OnClick="UploadPackageItemDocument">Upload Document</GridCommandButton>
                    </GridToolBarTemplate>
                    <GridSettings>
                        <GridPopupEditSettings Width="500px"
                                               Title="Package Item"
                                               MaxHeight="95vh"
                                               MaxWidth="95vw">
                        </GridPopupEditSettings>
                        <GridPopupEditFormSettings>
                            <FormTemplate>
                                @{
                                    EditPackageItem = context.Item as SchePackageItemDto;
                                    if (EditPackageItem.SchePackageAssignments == null)
                                    {
                                        EditPackageItem.SchePackageAssignments = new List<SchePackageAssignmentDto>();
                                    }
                                    <TelerikForm Model="EditPackageItem"
                                                 OnValidSubmit="@OnValidSubmit"
                                                 ColumnSpacing="20px"
                                                 ButtonsLayout="@FormButtonsLayout.Stretch">
                                        <FormValidation>
                                            <DataAnnotationsValidator />
                                        </FormValidation>
                                        <FormItems>
                                            <FormItem Field="ItemName"></FormItem>
                                            <FormItem Field="Notes"></FormItem>
                                            <FormItem Field="FinishDate"></FormItem>
                                            <FormItem>
                                                <Template>
                                                    <TelerikGrid @ref="@AssignmentGridRef"
                                                                 Data="@EditPackageItem.SchePackageAssignments"
                                                                 EditMode="@GridEditMode.Inline"
                                                                 OnCreate="@AddAssignmentHandler"
                                                                 OnDelete="@DeleteAssignmentHandler"
                                                                 OnUpdate="@UpdateAssignmentHandler">
                                                        <GridToolBarTemplate>
                                                            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Assignment</GridCommandButton>
                                                        </GridToolBarTemplate>
                                                        <GridColumns>
                                                            <GridColumn Field="DepartmentAssigned" Title="Department">
                                                                <EditorTemplate Context="EditAssignmentContext">
                                                                    @{
                                                                        SelectedAssignment = EditAssignmentContext as SchePackageAssignmentDto;
                                                                        <TelerikDropDownList Data="@DepartmentList"
                                                                                             Filterable="true"
                                                                                             FilterOperator="@StringFilterOperator.Contains"
                                                                        @bind-Value="SelectedAssignment.DepartmentAssigned"
                                                                                             Width="100%">
                                                                        </TelerikDropDownList>
                                                                        @if (SelectedAssignment.DepartmentAssigned == "Add New")
                                                                        {
                                                                            <TelerikTextBox @bind-Value="NewDepartmentName"></TelerikTextBox>
                                                                        }
                                                                    }
                                                                </EditorTemplate>
                                                            </GridColumn>
                                                            <GridColumn Field="Manager" Title="Manager">
                                                                <EditorTemplate Context="EditAssignmentContext">
                                                                    @{
                                                                        <TelerikDropDownList Data="@ManagerList"
                                                                                             Filterable="true"
                                                                                             FilterOperator="@StringFilterOperator.Contains"
                                                                        @bind-Value="SelectedAssignment.Manager"
                                                                                             Width="100%">
                                                                        </TelerikDropDownList>
                                                                        @if (SelectedAssignment.Manager == "Add New")
                                                                        {
                                                                            <TelerikTextBox @bind-Value="NewManagerName"></TelerikTextBox>
                                                                        }

                                                                    }
                                                                </EditorTemplate>
                                                            </GridColumn>
                                                            <GridCommandColumn Context="assignmentContext" Width="100px">
                                                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                                                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true">Cancel</GridCommandButton>
                                                            </GridCommandColumn>
                                                        </GridColumns>
                                                    </TelerikGrid>
                                                </Template>
                                            </FormItem>

                                        </FormItems>
                                        <FormButtons>
                                            <br />
                                            <br />
                                            <button type="submit" class="btn btn-primary">Update</button>
                                            <button type="button" @onclick="CloseEdit" class="btn btn-secondary">Cancel</button>
                                        </FormButtons>
                                    </TelerikForm>
                                }
                            </FormTemplate>
                        </GridPopupEditFormSettings>
                    </GridSettings>
                    <GridColumns>
                        <GridColumn Field="ItemName" Title="Item Name"></GridColumn>
                        <GridColumn Field="WebLink" Title="Document" Editable="false">
                            <Template>
                                @{
                                    var item = (SchePackageItemDto)context;
                                    if (item.WebLink != null && item.WebLink != "")
                                    {
                                        <button class="link-button" onclick="@( () => DownloadDocument(@item.WebLink) )">@item.WebLink</button>
                                        <button type="button" onclick="@( () => DeleteDocument(@item) )" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-base">
                                            <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel" aria-hidden="true">
                                                <!--!-->
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash" viewBox="0 0 16 16">
                                                    <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z" />
                                                    <path d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4zM2.5 3h11V2h-11z" />
                                                </svg>
                                                <!--!-->
                                            </span>
                                        </button>
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Field="Notes" Title="Item Notes"></GridColumn>
                        <GridColumn Title="Finished Date" TextAlign="@ColumnTextAlign.Center">
                            <Template>
                                @{
                                    var item = (SchePackageItemDto)context;
                                    var isFinished = item.FinishDate == null ? false : true;
                                    var dateFinished = item.FinishDate == null ? "" : item.FinishDate?.ToShortDateString();

                                    <TelerikCheckBox Value="isFinished" ValueChanged="@((bool value) => OnFinishClicked(value, item))"></TelerikCheckBox>
                                    <label>@dateFinished</label>
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Field="Id" Title="Item Assignment">
                            <Template>
                                @{
                                    var item = (SchePackageItemDto)context;

                                    foreach (var assignment in item.SchePackageAssignments)
                                    {
                                        <label>@assignment.DepartmentAssigned : @assignment.Manager</label>
                                        <br />
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridCommandColumn Context="packageItemContext" Width="100px">
                            <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                            <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                        </GridCommandColumn>
                    </GridColumns>
                </TelerikGrid>
            }
        </div>
    </div>
</div>
@code {
    public TelerikGrid<SchePackageItemDto> ItemGridRef { get; set; }
    public TelerikGrid<SchePackageAssignmentDto> AssignmentGridRef { get; set; }
    public List<ScheStartPackageDto>? PackageNameData { get; set; }
    public List<SchePackageItemDto>? PackageItemsData { get; set; }
    public List<SchePackageAssignmentDto>? AllPackageItemsAssignmentData { get; set; }
    public int SelectedSubdivisionId { get; set; }
    public int SelectedPackageId { get; set; }
    public ScheStartPackageDto? SelectedPackageName { get; set; }
    public SchePackageItemDto? SelectedPackageItem { get; set; }
    public SchePackageItemDto? EditPackageItem { get; set; }
    public SchePackageAssignmentDto? SelectedAssignment { get; set; }
    public List<string> DepartmentList { get; set; } = new List<string> { "Add New" };
    public List<string> ManagerList { get; set; } = new List<string> { "Add New" };
    public string NewManagerName { get; set; }
    public string NewDepartmentName { get; set; }
    public int LocalStateForAddingAssginment { get; set; } = 0;
    public ERP.Web.Components.UploadStartPackageDocument UploadDocumentModal { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        SubdivisionJobPickService.OnChanged += SubdivisionSelectHandler;
    }
    protected async Task SubdivisionSelectHandler()
    {
        if (SelectedSubdivisionId != SubdivisionJobPickService.SubdivisionId)
        {
            SelectedSubdivisionId = (int)SubdivisionJobPickService.SubdivisionId;
            var packageNamesResponse = await ScheduleStartPackageService.GetPackageNamesBySubdivisionIdAsync(SelectedSubdivisionId);
            if (packageNamesResponse.IsSuccess == false)
            {
                ShowSuccessOrErrorNotification(packageNamesResponse.Message, true);
            }
            var allPackageItemAssignmentDataResponse = await ScheduleStartPackageService.GetAllPackageItemAssignmentAsync();
            AllPackageItemsAssignmentData = allPackageItemAssignmentDataResponse.Value;
            DepartmentList.AddRange(AllPackageItemsAssignmentData.Select(x => x.DepartmentAssigned ?? "").Distinct().ToList());
            ManagerList.AddRange(AllPackageItemsAssignmentData.Select(x => x.Manager ?? "").Distinct().ToList());
            PackageNameData = packageNamesResponse.Value;
            PackageItemsData = null;
            StateHasChanged();
        }
    }
    protected async Task OnPackageRowClickHandler(GridRowClickEventArgs args)
    {
        SelectedPackageName = args.Item as ScheStartPackageDto;
        SelectedPackageId = SelectedPackageName.PackageId;
        await GetPackageItemsAsync(SelectedPackageName.PackageId);
        StateHasChanged();
    }
    async Task CreatePackageHandler(GridCommandEventArgs args)
    {
        ScheStartPackageDto packageName = (ScheStartPackageDto)args.Item;
        packageName.SubdivisionId = (int)SubdivisionJobPickService.SubdivisionId;
        var addPackageResponse = await ScheduleStartPackageService.AddPackageNameAsync(packageName);
        if (addPackageResponse.IsSuccess)
        {
            ShowSuccessOrErrorNotification(addPackageResponse.Message, false);
            var packageNamesResponse = await ScheduleStartPackageService.GetPackageNamesBySubdivisionIdAsync(packageName.SubdivisionId);
            PackageNameData = packageNamesResponse.Value;
        }
        else
        {
            ShowSuccessOrErrorNotification(addPackageResponse.Message, true);
        }
    }
    async Task UpdatePackageHandler(GridCommandEventArgs args)
    {
        ScheStartPackageDto packageName = (ScheStartPackageDto)args.Item;
        var updatePackageNameResponse = await ScheduleStartPackageService.UpdatePackageNameAsync(packageName);
        if (updatePackageNameResponse.IsSuccess)
        {
            ShowSuccessOrErrorNotification(updatePackageNameResponse.Message, false);
            var packageNamesResponse = await ScheduleStartPackageService.GetPackageNamesBySubdivisionIdAsync(packageName.SubdivisionId);
            PackageNameData = packageNamesResponse.Value;
        }
        else
        {
            ShowSuccessOrErrorNotification(updatePackageNameResponse.Message, true);
        }
    }
    async Task DeletePackageHandler(GridCommandEventArgs args)
    {
        ScheStartPackageDto packageName = (ScheStartPackageDto)args.Item;
        var deletePackageNameResponse = await ScheduleStartPackageService.DeletePackageNameAsync(packageName);
        if (deletePackageNameResponse.IsSuccess)
        {
            ShowSuccessOrErrorNotification(deletePackageNameResponse.Message, false);
            var packageNamesResponse = await ScheduleStartPackageService.GetPackageNamesBySubdivisionIdAsync(packageName.SubdivisionId);
            PackageNameData = packageNamesResponse.Value;
            PackageItemsData = null;
        }
        else
        {
            ShowSuccessOrErrorNotification(deletePackageNameResponse.Message, true);
        }
    }
    async Task DeletePackageItemHandler(GridCommandEventArgs args)
    {
        SchePackageItemDto packageItem = (SchePackageItemDto)args.Item;
        var deletePackageItemResponse = await ScheduleStartPackageService.DeletePackageItemAsync(packageItem);
        if (deletePackageItemResponse.IsSuccess)
        {
            ShowSuccessOrErrorNotification(deletePackageItemResponse.Message, false);
            await GetPackageItemsAsync(SelectedPackageName.PackageId);
        }
        else
        {
            ShowSuccessOrErrorNotification(deletePackageItemResponse.Message, true);
        }
    }
    async Task AddAssignmentHandler(GridCommandEventArgs args)
    {
        SchePackageAssignmentDto packageItemAssignment = (SchePackageAssignmentDto)args.Item;
        EditPackageItem.SchePackageAssignments.Add(new SchePackageAssignmentDto
            {
                DepartmentAssigned = packageItemAssignment.DepartmentAssigned == "Add New" ? NewDepartmentName : packageItemAssignment.DepartmentAssigned,
                Manager = packageItemAssignment.Manager == "Add New" ? NewManagerName : packageItemAssignment.Manager,
                Notes = "Local" + LocalStateForAddingAssginment
            });
        AssignmentGridRef.Rebind();
        LocalStateForAddingAssginment++;
    }
    async Task UpdateAssignmentHandler(GridCommandEventArgs args)
    {
        SchePackageAssignmentDto packageItemAssignment = (SchePackageAssignmentDto)args.Item;
        var assignment = packageItemAssignment.ItemAssignmentId == 0 ? EditPackageItem.SchePackageAssignments.SingleOrDefault(x => x.Notes == packageItemAssignment.Notes) //have maintained Notes for Assignments added on the UI
                                                                    : EditPackageItem.SchePackageAssignments.SingleOrDefault(x => x.ItemAssignmentId == packageItemAssignment.ItemAssignmentId);
        if (assignment != null)
        {
            assignment.DepartmentAssigned = packageItemAssignment.DepartmentAssigned == "Add New" ? NewDepartmentName : packageItemAssignment.DepartmentAssigned;
            assignment.Manager = packageItemAssignment.Manager == "Add New" ? NewManagerName : packageItemAssignment.Manager;
        }
    }
    async Task DeleteAssignmentHandler(GridCommandEventArgs args)
    {
        SchePackageAssignmentDto packageItemAssignment = (SchePackageAssignmentDto)args.Item;
        var assignment = packageItemAssignment.ItemAssignmentId == 0 ? EditPackageItem.SchePackageAssignments.SingleOrDefault(x => x.Notes == packageItemAssignment.Notes) //have maintained Notes for Assignments added on the UI
                                                                    : EditPackageItem.SchePackageAssignments.SingleOrDefault(x => x.ItemAssignmentId == packageItemAssignment.ItemAssignmentId);
        if (assignment != null)
        {
            EditPackageItem.SchePackageAssignments.Remove(assignment);
        }
    }
    private async Task OnValidSubmit()
    {
        if (EditPackageItem.PackageItemId == 0)
        {
            EditPackageItem.PackageId = SelectedPackageName.PackageId;
            var addPackageItemResponse = await ScheduleStartPackageService.AddPackageItemAsync(EditPackageItem);
            if (addPackageItemResponse.IsSuccess)
            {
                ShowSuccessOrErrorNotification(addPackageItemResponse.Message, false);
            }
            else
            {
                ShowSuccessOrErrorNotification(addPackageItemResponse.Message, true);
            }
        }
        else
        {
            var updatePackageItemResponse = await ScheduleStartPackageService.UpdatePackageItemAsync(EditPackageItem);
            if (updatePackageItemResponse.IsSuccess)
            {
                ShowSuccessOrErrorNotification(updatePackageItemResponse.Message, false);
            }
            else
            {
                ShowSuccessOrErrorNotification(updatePackageItemResponse.Message, true);
            }
        }
        await GetPackageItemsAsync(SelectedPackageName.PackageId);
        await ExitEditAsync();
    }
    async Task GetPackageItemsAsync(int packageId)
    {
        var packageItemsDataResponse = await ScheduleStartPackageService.GetPackageItemsAsync(packageId);
        if (packageItemsDataResponse.IsSuccess == false)
        {
            ShowSuccessOrErrorNotification(packageItemsDataResponse.Message, true);
        }
        PackageItemsData = packageItemsDataResponse.Value;
        PackageItemsData.ForEach(packageItem =>
            {
                packageItem.SchePackageAssignments = packageItem.SchePackageAssignments.Where(x => x.IsActive == true).ToList();
            });
    }
    async Task CloseEdit()
    {
        await GetPackageItemsAsync(SelectedPackageName.PackageId);
        await ExitEditAsync();
    }
    private async Task ExitEditAsync()
    {
        var state = ItemGridRef?.GetState();
        state.OriginalEditItem = null;
        state.EditItem = null;
        state.InsertedItem = null;

        await ItemGridRef?.SetStateAsync(state);
    }
    async Task OnFinishClicked(bool value, SchePackageItemDto packageItem)
    {
        packageItem.FinishDate = value == false ? null : DateTime.Today;
        var updatePackageItemResponse = await ScheduleStartPackageService.UpdatePackageItemAsync(packageItem);
        if (updatePackageItemResponse.IsSuccess)
        {
            ShowSuccessOrErrorNotification(updatePackageItemResponse.Message, false);
        }
        else
        {
            ShowSuccessOrErrorNotification(updatePackageItemResponse.Message, true);
        }
        ItemGridRef.Rebind();
    }
    private void UploadPackageItemDocument()
    {
        UploadDocumentModal.Show();
        StateHasChanged();
    }
    private async Task HandleUploadDocumentSubmit(ResponseModel response)
    {
        UploadDocumentModal.Hide();
        PackageItemsData = (await ScheduleStartPackageService.GetPackageItemsAsync(SelectedPackageName.PackageId)).Value;
        ItemGridRef.Rebind();
        ShowSuccessOrErrorNotification(response.Message, !response.IsSuccess);
        StateHasChanged();
    }
    async void DownloadDocument(string fileName)
    {
        var documentDetails = new DocumentUploadModel()
            {
                AttachmentType = "StartPackageItemAttachment",
                FolderName = SelectedSubdivisionId.ToString(),
                FileName = fileName
            };

        var responseBytes = await StartPackageDocumentService.DownloadDocumentAsync(documentDetails);

        await JS.InvokeAsync<object>("saveAsFile", fileName, Convert.ToBase64String(responseBytes.Value));

    }
    async void DeleteDocument(SchePackageItemDto item)
    {
        // var documentDetails = new DocumentUploadModel()
        //     {
        //         FolderName = SelectedSubdivisionId.ToString(),
        //         FileName = fileName
        //     };
        var deleteResponse = await StartPackageDocumentService.DeleteDocumentAsync(item);
        PackageItemsData = (await ScheduleStartPackageService.GetPackageItemsAsync(SelectedPackageName.PackageId)).Value;
        ItemGridRef.Rebind();
        ShowSuccessOrErrorNotification(deleteResponse.Message, !deleteResponse.IsSuccess);
        StateHasChanged();
    }
    private void ShowSuccessOrErrorNotification(string message, bool error)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = error ? ThemeConstants.Notification.ThemeColor.Error : ThemeConstants.Notification.ThemeColor.Success
            });
    }
}
