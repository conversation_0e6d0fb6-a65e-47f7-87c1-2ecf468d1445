﻿
@inject ScheduleService ScheduleService
@inject PoService PoService
@inject AuthenticationStateProvider AuthenticationStateProvider
@using ERP.Data.Models.ExtensionMethods
<style>
    /*    .k-animation-container {
    position: absolute;
    z-index: 300000 !important;
    } */
</style>
<TelerikWindow Modal="true"
@bind-Visible="@IsModalVisible"
Width="400px"
Height="550px"
CloseOnOverlayClick="true">
    <WindowTitle>
        <h4 class="page-title">Schedule Activity</h4>
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@SelectedActivity" OnValidSubmit="@HandleValidSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />           
            <div>
                <label class="form-label">Schedule Activity</label>
                <TelerikTextBox Enabled="false" @bind-Value="@SelectedActivity.Sactivity.ActivityName"></TelerikTextBox>
            </div>
            <div style="margin-top:1em">
                <label class="form-label">Started</label>
                <TelerikCheckBox OnChange="@CheckStartedChanged" @bind-Value="@SelectedActivity.BoolStarted"></TelerikCheckBox>
                <span>&nbsp;</span>
                <label class="form-label">Complete</label>
                <TelerikCheckBox OnChange="@CheckCompleteChanged" @bind-Value="@SelectedActivity.BoolComplete"></TelerikCheckBox>
            </div>
            <div style="margin-top:1em">
                <label class="form-label">Supplier</label>
                <TelerikDropDownList Data="@AllSuppliers"
                TextField="SubName"
                ValueField="SubNumber"
                FilterOperator="@StringFilterOperator.Contains"
                Filterable="true"
                Enabled = "@SupplierChangeEnabled"
                @bind-Value="SelectedActivity.SubNumber">
                    <DropDownListSettings>
                        <DropDownListPopupSettings Height="300px"></DropDownListPopupSettings>
                    </DropDownListSettings>
                </TelerikDropDownList>
                @if (!SupplierChangeEnabled)
                {
                    <em style="color:darkred">Supplier cannot be changed if there is an issued PO.</em>
                }
            </div>
            <div style="margin-top:1em">
                <label class="form-label">Predecessors</label>
                @{
                    if(SelectedActivity != null && ScheduleActivities != null )
                    {
                        var priorActivities = ScheduleActivities?.Where(x => x.Seq < SelectedActivity.Seq)?.Select(x => x.Sactivity);
                        <TelerikMultiSelect Data="@priorActivities"
                        TextField="ActivityName"
                        ValueField="SactivityId"
                        Filterable="true"
                        FilterOperator="StringFilterOperator.Contains"
                        @bind-Value="SelectedActivity.PredIds">
                        </TelerikMultiSelect>
                    }

                }


            </div>
            <div style="margin-top:1em">
                <label class="form-label">Variance  </label>
                <TelerikDropDownList Data="@VarianceCodes"
                TextField="VarianceDesc"
                ValueField="VarianceCode"
                @bind-Value="SelectedActivity.VarianceCode">
                </TelerikDropDownList>
            </div>
            @if(SelectedActivity.SchStartDate != null && SelectedActivity.SchEndDate != null && SelectedActivity.BaseStartDate != null && SelectedActivity.BaseEndDate != null)
            {
                <div style="margin-top:1em">
                    <label class="form-label">Duration</label>
                    <TelerikNumericTextBox Enabled="@(scheduledEndDatesEnabled && SelectedActivity.VarianceCode != null)" @bind-Value="@SelectedActivity.Duration"></TelerikNumericTextBox>
                </div>
                <div style="margin-top:1em">
                    <label class="form-label">Schedule Start</label>
                    <TelerikDatePicker ValueExpression="@( () => SelectedActivity.SchStartDate )" ValueChanged="@((DateTime? inputDate) =>ScheduleStartChanged(inputDate))" Enabled="@(scheduledStartDatesEnabled && SelectedActivity.VarianceCode != null)" Value="@SelectedActivity.SchStartDate.Value"></TelerikDatePicker>
                    @if (SelectedActivity.VarianceCode == null)
                    {
                        <span style="color:red; font-size: 10px; font-style: italic">Variance must be entered to edit schedule dates</span>
                    }
                </div>
                <div style="margin-top:1em">
                    <label class="form-label">Schedule Finished</label>
                    <TelerikDatePicker Enabled="@(scheduledEndDatesEnabled && SelectedActivity.VarianceCode != null)" ValueExpression="@( () => SelectedActivity.SchEndDate )" ValueChanged="@((DateTime? inputDate) =>ScheduleEndChanged(inputDate))" Value="@SelectedActivity.SchEndDate.Value"></TelerikDatePicker>
                    @if (SelectedActivity.VarianceCode == null)
                    {
                        <span style="color:red; font-size: 10px; font-style: italic">Variance must be entered to edit schedule dates</span>
                    }
                </div>
                <div style="margin-top:1em">
                    <label class="form-label">Baseline Start</label>
                    <TelerikDatePicker Enabled="false" @bind-Value="@SelectedActivity.BaseStartDate"></TelerikDatePicker>
                </div>
                <div style="margin-top:1em">
                    <label class="form-label">Baseline Finised</label>
                    <TelerikDatePicker Enabled="false" @bind-Value="@SelectedActivity.BaseEndDate"></TelerikDatePicker>
                </div>
                <div style="margin-top:1em">
                    <label class="form-label">Actual Start</label>
                    <TelerikDatePicker Enabled="false" @bind-Value="@SelectedActivity.ActualStartDate"></TelerikDatePicker>
                </div>
                <div style="margin-top:1em">
                    <label class="form-label">Actual Finished</label>
                    <TelerikDatePicker Enabled="false" @bind-Value="@SelectedActivity.ActualEndDate"></TelerikDatePicker>
                </div>
            }


            <div style="margin-top:1em">
                <label class="form-label">Supplier Note  </label>
                <TelerikTextArea @bind-Value="@SelectedActivity.SupplierNote"></TelerikTextArea>              
            </div>
            <br/>
            <div style="margin-top:1em">
                <label class="form-label">Internal Note:  </label>
                <TelerikTextArea @bind-Value="@SelectedActivity.Note"></TelerikTextArea>                           
            </div>
            <br />  
            <br />
            <div class="customSeparator">
                <button type="submit" class="btn btn-primary">Update</button>
                <button type="button" @onclick="CancelUpdate" class="btn btn-secondary">Cancel</button>
                <div style=@ShowLoading>Submitting. Please wait...</div>
                <div style=@ShowError>Something went wrong.</div>
            </div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    public bool IsModalVisible { get; set; }
    public string ShowLoading{ get; set; } = "display:none";
    public string ShowError { get; set; } = "display:none";
    private bool scheduledStartDatesEnabled { get; set; } = true;
    private bool scheduledEndDatesEnabled { get; set; } = true;
    public List<VarianceDto>? VarianceCodes { get; set; }
    public List<SupplierDto>? AllSuppliers { get; set; }
    public bool SupplierChangeEnabled { get; set; } = true;
    private bool userIsDirector { get; set; } = false;
    private List<DateTime>? holidays { get; set; }
    public List<ScheduleSactivityDto>? ScheduleActivities { get; set; } = new List<ScheduleSactivityDto>();
    [Parameter]
    public EventCallback<ResponseModel<ScheduleSactivityDto>> HandleAddSubmit { get; set; }
    [Parameter]
    public ScheduleSactivityDto? SelectedActivity { get; set; }
    protected override async Task OnInitializedAsync()
    {
        var suppliersTask = PoService.GetSuppliersAsync();
        var varianceCodesTask = ScheduleService.GetVarianceCodesAsync();
        var getCalendarTask = ScheduleService.GetHolidaysAsync();
        await Task.WhenAll(new Task[] { suppliersTask, varianceCodesTask, getCalendarTask });
        VarianceCodes = varianceCodesTask.Result.Value;
        AllSuppliers = suppliersTask.Result.Value;
        holidays = getCalendarTask.Result.Value.Select(x => x.WorkDate).ToList();
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userRoleConstructionDirector = user.User.IsInRole("ConstructionDirector");
        userIsDirector = userRoleAdmin || userRoleConstructionDirector;

    }
    protected override async Task OnParametersSetAsync()
    {        
        scheduledEndDatesEnabled = SelectedActivity?.ActualEndDate == null;
        scheduledStartDatesEnabled = SelectedActivity?.ActualStartDate == null;
        SupplierChangeEnabled = SelectedActivity?.SupplierEditable == true;
        if(SelectedActivity != null && SelectedActivity.ScheduleId != null)
        {
            ScheduleActivities = (await ScheduleService.GetScheduleActivitiesForScheduleAsync((int)SelectedActivity.ScheduleId)).Value;
        }
    }
    public void Show()
    {
        IsModalVisible = true;
    }
    private async Task ScheduleStartChanged(DateTime? inputDate)
    {
        //TODO: the dialog sometimes pops behind, or to the side, then when you click it the current window goes away
        var checkHoliday = (await ScheduleService.CheckHolidayAsync(inputDate.Value)).Value;
        if (checkHoliday)
        {
            var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");
            if (!confirm)
            {
                return;
            }
        }
        if (SelectedActivity.VarianceCode == null && inputDate > SelectedActivity.BaseStartDate)
        {
            await Dialogs.AlertAsync("You must enter a variance!");
            return;
        }
        SelectedActivity.SchStartDate = inputDate;
    }
    private async Task ScheduleEndChanged(DateTime? inputDate)
    {
        var checkHoliday = (await ScheduleService.CheckHolidayAsync(inputDate.Value)).Value;
        if (checkHoliday)
        {
            var confirm = await Dialogs.ConfirmAsync("Selected End Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");
            if (!confirm)
            {
                return;
            }
        }
        if (SelectedActivity.VarianceCode == null &&  inputDate > SelectedActivity.BaseEndDate)
        {
            await Dialogs.AlertAsync("You must enter a variance!");
            return;
        }
        SelectedActivity.SchEndDate = inputDate;
    }
    private async Task CheckStartedChanged(object value)
    {
        if(SelectedActivity.BoolStarted == true)
        {
            if (!userIsDirector && (SelectedActivity.SchStartDate.Value.Date < DateTime.Now.Date || (SelectedActivity.SchStartDate.Value.Date == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                await Dialogs.AlertAsync("You are late! You must select a variance and then adjust the scheduled dates! If the scheduled activity was started on time, but you forgot to mark it, you must contact Construction Director");
                SelectedActivity.BoolStarted = false;
            }
            else if (userIsDirector && (SelectedActivity.SchStartDate.Value.Date < DateTime.Now.Date || (SelectedActivity.SchStartDate.Value.Date == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    SelectedActivity.BoolStarted = false;
                }
                else
                {
                    SelectedActivity.ActualStartDate = SelectedActivity.SchStartDate;
                }
            }
            else
            {
                SelectedActivity.ActualStartDate = SelectedActivity.SchStartDate;
            }
        }
        else
        {
            SelectedActivity.ActualStartDate = null;
        }
    }
    private async Task CheckCompleteChanged(object value)
    {
        if (SelectedActivity.BoolComplete == true)
        {
            if (!userIsDirector && (CalendarExtension.AddWorkingDays(SelectedActivity.SchEndDate.Value.Date, 1, holidays) < DateTime.Now.Date || (CalendarExtension.AddWorkingDays(SelectedActivity.SchEndDate.Value.Date, 1, holidays) == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                //basedateeditable is user is in construction dir role
                await Dialogs.AlertAsync("Activity is late! You must select a variance and then adjust the scheduled dates. Contact Construction Director to override this if needed");
                SelectedActivity.BoolComplete = false;
            }
            else if (userIsDirector && (CalendarExtension.AddWorkingDays(SelectedActivity.SchEndDate.Value.Date, 1, holidays) < DateTime.Now.Date || (CalendarExtension.AddWorkingDays(SelectedActivity.SchEndDate.Value.Date, 1, holidays) == DateTime.Now.Date && DateTime.Now.Hour > 10)))
            {
                var confirm = await Dialogs.ConfirmAsync("Current date is after scheduled date. This will set actual date to scheduled date. Proceed anyway?");
                if (!confirm)
                {
                    SelectedActivity.BoolComplete = false;
                }
                else
                {
                    SelectedActivity.ActualEndDate = SelectedActivity.SchEndDate;
                }
            }
            else
            {
                SelectedActivity.ActualEndDate = SelectedActivity.SchEndDate;
            }
        }
        else
        {
            SelectedActivity.ActualEndDate = null;
        }
    }

    private async void HandleValidSubmit()
    {
        ShowLoading = "";
        if(SelectedActivity.SchStartDate != null)
        {
            var checkHoliday = (await ScheduleService.CheckHolidayAsync(SelectedActivity.SchStartDate.Value)).Value;
            if (checkHoliday)
            {
                var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday (Ignore No work days will be checked), cancel to select new date");
                if (!confirm)
                {
                    return;
                }
                SelectedActivity.BoolIgnoreNoWorkDays = true;//set ignore no work days if they choose to ignore the holiday
            }            
        }       
        //TODO: require a variance if sch date changed

        SelectedActivity.ActualDuration = SelectedActivity.ActualEndDate != null && SelectedActivity.ActualStartDate != null ? (SelectedActivity.ActualEndDate - SelectedActivity.ActualStartDate).Value.Days : null;

        SelectedActivity.Predecessors = SelectedActivity.PredIds.Select(x => new ScheduleSactivityPredDto()
            {
                ScheduleAid = (int)SelectedActivity.ScheduleAid,
                PredSactivityId = x,
                PredScheduleAid = ScheduleActivities?.FirstOrDefault(y => y.SactivityId == x)?.ScheduleAid
            }).ToList();

        var findActivity = ScheduleActivities.FirstOrDefault(x => x.ScheduleAid == SelectedActivity.ScheduleAid);
        if(findActivity != null)
        {
            findActivity.VarianceCode = SelectedActivity.VarianceCode;
            findActivity.Predecessors = SelectedActivity.Predecessors;
            findActivity.SchStartDate = SelectedActivity.SchStartDate;

        }
        //update all the activities, then save
        bool updateBaseDates = false;//TODO: allow Regis to update Base dates       
        var response = await ScheduleService.UpdateScheduleSActivityAsync(SelectedActivity, ScheduleActivities, updateBaseDates);//calculate new dates for whole schedule
        var activitiesToUpdate = response.Value;
        var saveResponse = await ScheduleService.UpdateScheduleSactivitysAsync(activitiesToUpdate);//save to db, send out emails
                                                                                                   
        //var response2 = await ScheduleService.UpdateScheduleSactivityAsync(SelectedActivity);//was previously doing all the updates in db in this, but to consolidate, not doing that
        ShowLoading = "display:none";
        var newResponse = new ResponseModel<ScheduleSactivityDto>()
        {
            IsSuccess = saveResponse.IsSuccess, 
            Error = saveResponse.Error, 
            Message = saveResponse.Message, 
            Value = SelectedActivity
        };//TODO: clean this up.
        await HandleAddSubmit.InvokeAsync(newResponse);
    }
    async void CancelUpdate()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;        
    }
}
