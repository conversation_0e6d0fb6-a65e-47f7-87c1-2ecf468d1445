﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class TemplateType
{
    public int TemplateTypeId { get; set; }

    public short ListOrder { get; set; }

    public bool IsActive { get; set; }

    public string Name { get; set; } = null!;

    public virtual ICollection<DocuSignSignatureTemplate> DocuSignSignatureTemplates { get; set; } = new List<DocuSignSignatureTemplate>();
}
