﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class PodetailDto : IMapFrom<Podetail>
{
    public int PodetailId { get; set; }

    public int PoheaderId { get; set; }

    public int? Poitemno { get; set; }

    public string? Pocostcode { get; set; }
    public bool? MissingJobTask { get; set; }

    public string? Pocategory { get; set; }

    public string? Poitemdesc { get; set; }

    public string? Pounit { get; set; }

    public double? Pounitqty { get; set; }

    public double? Pounitcost { get; set; }

    public double? Poamount { get; set; }

    public int? Poqtyoflengths { get; set; }

    public double? Pocutlengths { get; set; }

    public string? Poapnnumber { get; set; }

    public string? Taxgroup { get; set; }

    public double? Taxamount { get; set; }

    public string? Warrantyitem { get; set; }

    public int? Warrantydays { get; set; }

    public int? Warrantytype { get; set; }

    public double? AuthToDatePc { get; set; }

    public double? AuthToDateAmt { get; set; }

    public double? PendAuthPc { get; set; }

    public double? PendAuthAmount { get; set; }

    public DateTime? LastAuthDate { get; set; }

    public DateTime? PendAuthDate { get; set; }

    public double? PendAuthTax { get; set; }

    public double? PendAuthDisc { get; set; }

    public string? PhaseCode { get; set; }

    public string? ItemNumber { get; set; }

    public int? MasterItemsId { get; set; }

    public string? Pactivity { get; set; }

    public string? OptionNumber { get; set; }

    public string? OptionDesc { get; set; }

    public int? PojccdetailId { get; set; }

    public int? VariancePojccdetailId { get; set; }

    public string? VarianceJcCategory { get; set; }

    public double? VarianceAmount { get; set; }

    public double? VarianceTaxAmount { get; set; }

    public string? Poitemnotes { get; set; }

    public string? SortKey1 { get; set; }

    public string? SortKey2 { get; set; }

    public string? PendAuthDraw { get; set; }

    public double? VariancePendAuthAmount { get; set; }

    public double? VariancePendAuthTax { get; set; }

    public double? VariancePendAuthDisc { get; set; }

    public double? VarianceAuthToDateAmt { get; set; }

    public double? VarianceAuthToDateTax { get; set; }

    public string? Optionselections { get; set; }

    public int? CostsId { get; set; }

    public int? PochangeorderId { get; set; }

    public int? ChangeToPodetailId { get; set; }

    public double? LastAuthAmount { get; set; }

    public double? LastAuthTax { get; set; }

    public double? LastAuthDisc { get; set; }

    public string? LastAuthDraw { get; set; }

    public double? VarianceLastAuthAmount { get; set; }

    public double? VarianceLastAuthTax { get; set; }

    public double? VarianceLastAuthDisc { get; set; }

    public int? PodetailoptionsId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public int? PurchasingContDetailId { get; set; }

    public int? SupplierCostreservationId { get; set; }
    public bool? ExportBc { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }
    public PoheaderDto? Poheader { get; set; }
    public CostDto? Costs { get; set; }

   // public virtual ICollection<Estdetail> Estdetails { get; set; } = new List<Estdetail>();

    public PodetailoptionDto? Podetailoptions { get; set; }

    public PojccdetailDto? Pojccdetail { get; set; }

    //public byte[] RecordTimeStamp { get; set; } = null!;

    //public virtual ICollection<Estdetail> Estdetails { get; set; } = new List<Estdetail>();

    public int? JccCategoryId { get; set; }

    public int PurchasingActivityId { get; set; }

    public string? ErrorMessage { get; set; }

    public string? SuccessMessage { get; set; }
    public bool? IsPOApprovalStarted { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<PodetailDto, Podetail>().ReverseMap();
    }

    public List<FileModel> Files { get; set; } = new List<FileModel>();
}
