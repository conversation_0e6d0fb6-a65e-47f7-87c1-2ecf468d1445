﻿@inject TradeService TradeService
<TelerikWindow Modal="true"
@bind-Visible="@IsModalVisible"
               Width="350px"
               Height="300px">
    <WindowTitle>
        Add Supplier Type
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@SupplierTypeModel" OnValidSubmit="@HandleValidAddSubmit">
            <div class="mb-3">
                <label class="form-label">Select Supplier Type</label><br />
                <TelerikDropDownList 
                    Data="SupplierTypeData" 
                    @bind-Value="selectedSupplierType" 
                    TextField="SubTypeName"
                    ValueField="SubTypeId" />
            </div>
           @*  @if (selectedSupplierType == -1)
            {
                <div class="mb-3">
                    <label class="form-label">New Supplier Type Name</label><br />
                    <TelerikTextBox @bind-Value="@supplierTypeString" />
                </div>
            } *@
            <button type="submit" class="btn btn-primary">Add</button>
            <button type="button" @onclick="CancelAdd" class="btn btn-secondary">Cancel</button>
            <div style=@submittingStyle>Adding item. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {
    public SupplierType SupplierTypeModel { get; set; } = new SupplierType();
    public bool IsModalVisible { get; set; }
    [Parameter]
    public EventCallback<ResponseModel<SupplierTradeTypeModel>> HandleAddSubmit { get; set; }
    [Parameter]
    public int SubNumber { get; set; }

    public List<SupplierType> SupplierTypeData { get; set; }
    public int selectedSupplierType { get; set; }
    public bool isSelected { get; set; } = false;
    public string supplierTypeString { get; set; }
    private string submittingStyle = "display:none";

    public async Task Show()
    {
        IsModalVisible = true;
        isSelected = false;
        supplierTypeString = "";
        var supplierTypes = (await TradeService.GetSupplierTypesAsync(SubNumber)).Value;
        SupplierTypeData = (await TradeService.GetAllSupplierTypesAsync()).Value;
        SupplierTypeData = SupplierTypeData.Where(x => !supplierTypes.Any(y => y.SupplierTypeName == x.SubTypeName)).ToList();
        // SupplierTypeData.Insert(0, new SupplierType()
        //     {
        //         SubTypeId = -1,
        //         SubTypeName = "Add New Supplier Type"
        //     });
        StateHasChanged();
    }

    public void Hide()
    {
        IsModalVisible = false;
    }

    void CancelAdd()
    {
        IsModalVisible = false;
    }

    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        SupplierType createdSupplierType = null;
        if (selectedSupplierType == -1)
        {
            var addSupplierResponse = await TradeService.AddSupplierTypeAsync(supplierTypeString);
            createdSupplierType = addSupplierResponse.Value;
        }
        
        SupplierTradeTypeModel supplierTradeType = new SupplierTradeTypeModel()
            {
                SupplierNumber = SubNumber,
                SupplierTypeId = selectedSupplierType == -1 ? createdSupplierType.SubTypeId : selectedSupplierType // checking if the checkbox of adding new supplier type is selected, if checked then adding the new supplier type Id else adding the type id from dropdown
            };
        var addResponse = await TradeService.AddSupplierTradeTypeAsync(supplierTradeType);
        
        submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(addResponse);
    }
}
