﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace ERP.Data.Models.Dto
{

    public class BCBatchRequest<T>
    {
        public Request<T>[] requests { get; set; }
    }
    public class BCDeleteBatchRequest<T>
    {
        public DeleteRequest<T>[] requests { get; set; }
    }
    public class Request<T>
    {
        public string method { get; set; }
        public string id { get; set; }
        public string url { get; set; }
        public Headers headers { get; set; }
        public T body { get; set; }
    }
    public class DeleteRequest<T>
    {
        public string method { get; set; }
        public string id { get; set; }
        public string url { get; set; }
        public DeleteBatchHeaders headers { get; set; }
    }
    public class Headers
    {
        public string? ContentType { get; set; }
    }
    public class DeleteBatchHeaders
    {

        [JsonProperty(PropertyName = "If-Match")]
        public string? IfMatch { get; set; }
    }

    public class Body
    {
        public string accountId { get; set; }
        public string postingDate { get; set; }
        public string documentNumber { get; set; }
        public int amount { get; set; }
        public string description { get; set; }
    }

    public class BCBatchResponseRoot<T>
    {
        public BCBatchResponse<T>[] responses { get; set; }
    }

    public class BCBatchResponse<T>
    {
        public string id { get; set; }
        public int status { get; set; }
        public ResponseHeaders headers { get; set; }
        public T? body { get; set; }
    }

    public class ResponseHeaders
    {
        public string location { get; set; }
        public string contenttype { get; set; }
        public string odataversion { get; set; }
    }

    public class BCBatchResponseRoot
    {
        public BCBatchResponse[] responses { get; set; }
    }

    public class BCBatchResponse
    {
        public string id { get; set; }
        public int status { get; set; }
        public ResponseHeaders headers { get; set; }
        public dynamic body { get; set; }
    }

}
