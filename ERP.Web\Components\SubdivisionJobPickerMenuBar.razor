﻿@inject TradeService TradeService
@inject PoService PoService
@inject SubdivisionService SubdivisionService
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject ProtectedSessionStorage ProtectedSessionStore
@using System.Diagnostics;
<style>
    .row {
        --bs-gutter-x: 0rem;
    }
</style>

<div class="row">
    @if (ShowSubdivision)
    {
        <div class="card col">
            <div class="card-header">
                <h7 class="page-title" style="font-weight:bold">
                    Subdivision - <TelerikDropDownList Data="AllSubdivisions"
                                                   ScrollMode="@DropDownScrollMode.Virtual"
                                                   ItemHeight="45"
                                                   TextField="SubdivisionName"
                                                   ValueField="SubdivisionId"
                                                   OnChange="SubdivisionSelected"
                                                   @bind-Value="@SubdivisionJobPickService.SubdivisionId"
                                                   Filterable="true"
                                                   PageSize="10"
                                                   Width="100%"
                                                   FilterOperator="@StringFilterOperator.Contains">
                    </TelerikDropDownList>
                </h7>
            </div>
        </div>
    }   
    @if (ShowJob)
    {
        <div class="card col">
            <div class="card-header">
                <h7 class="page-title" style="font-weight:bold">
                    Job Number -  <TelerikDropDownList Data="JobsForSelect"
                                                       Context="jobDropdownContext"
                                                       ScrollMode="@DropDownScrollMode.Virtual"
                                                       ItemHeight="40"
                                                       TextField="JobNumber"
                                                       ValueField="JobNumber"
                                                       OnChange="JobChanged"
                                                       @bind-Value="@SubdivisionJobPickService.JobNumber"
                                                       Filterable="true"
                                                       PageSize="10"
                                                       Width="100%"
                                                       FilterOperator="@StringFilterOperator.Contains">

                        <ItemTemplate>
                            @jobDropdownContext.JobNumber
                            @if (@jobDropdownContext.LotNumber != null && @jobDropdownContext.LotNumber != "")
                            {
                                <span>- Lot# @jobDropdownContext.LotNumber</span>
                            }
                        </ItemTemplate>
                        <ValueTemplate>
                            @jobDropdownContext.JobNumber
                            @if (@jobDropdownContext.LotNumber != null && @jobDropdownContext.LotNumber != "")
                            {
                                <span>- Lot# @jobDropdownContext.LotNumber</span>
                            }
                        </ValueTemplate>
                    </TelerikDropDownList>
                </h7>
            </div>
        </div>
    }
    @if (ShowSupplier)
    {
        <div class="card col">
            <div class="card-header">
                <h7 class="page-title" style="font-weight:bold">
                    Supplier -  <TelerikDropDownList Data="AllSuppliers"
                                                 ScrollMode="@DropDownScrollMode.Virtual"
                                                 ItemHeight="40"
                                                 TextField="SubName"
                                                 ValueField="SubNumber"
                                                 OnChange="SupplierChanged"
                                                 @bind-Value="@SubdivisionJobPickService.SupplierNumber"
                                                 Filterable="true"
                                                 PageSize="10"
                                                 Width="100%"
                                                 FilterOperator="@StringFilterOperator.Contains">
                    </TelerikDropDownList>
                </h7>
            </div>

        </div>
    }    
</div>

@code {
    public List<SubdivisionDto>? AllSubdivisions { get; set; } = new List<SubdivisionDto>();
    public List<string>? AllJobsForAutoSelect { get; set; }
    public List<JobDto>? AllJobs { get; set; } = new List<JobDto>();
    public List<JobDto>? JobsForSelect { get; set; } = new List<JobDto>();
    public List<SupplierDto>? AllSuppliers { get; set; }
    private string? SelectedJob { get; set; }
    private string? JobSelected { get; set; }
    private int? SelectedSupplier { get; set; }
    private int? SelectedSubdivision { get; set; }// = -1;
    [Parameter]
    public bool ShowSubdivision { get; set; } = true;
    [Parameter]
    public bool ShowJob { get; set; } = true;
    [Parameter]
    public bool ShowSupplier { get; set; } = true;
    protected override async Task OnInitializedAsync()
    {
        //SubdivisionJobPickService.MyDelegate += ChangedHandler;
        var jobsTask = SubdivisionService.GetAllJobsAsync();
        var suppliersTask = PoService.GetSuppliersAsync(); 
        var subdivisionTask =  SubdivisionService.GetSubdivisionsAsync();
        await Task.WhenAll(new Task[] { jobsTask, suppliersTask, subdivisionTask });       
        AllJobs = jobsTask.Result.Value;
        JobsForSelect = jobsTask.Result.Value;
        AllJobsForAutoSelect = AllJobs.Select(x => x.JobNumber).ToList();
        AllSubdivisions = subdivisionTask.Result.Value;
        AllSubdivisions.Insert(0, new SubdivisionDto() { SubdivisionName = "All", SubdivisionId = -1 });
        AllSuppliers = suppliersTask.Result.Value;
        if(SubdivisionJobPickService != null)
        {
            if (SubdivisionJobPickService.SubdivisionId == null)
            {
                //see if its stored in browser storage - in case they reloaded
                var getSubdivisionId = await ProtectedSessionStore.GetAsync<int>("subdivision");
                var getSubdivisionSelected = getSubdivisionId.Value;
                if (getSubdivisionSelected != 0)
                {
                    SubdivisionJobPickService.SubdivisionId = getSubdivisionSelected;
                    SelectedSubdivision = getSubdivisionSelected;
                    if (SelectedSubdivision == -1)
                    {
                        JobsForSelect = AllJobs;//All subdvisions
                    }
                    else
                    {
                        JobsForSelect = AllJobs.Where(x => x.SubdivisionId == SelectedSubdivision).ToList();//filter jobs by subdivision
                    }
                }
            }
            if (SubdivisionJobPickService.JobNumber == null)
            {
                var getJobSelected = (await ProtectedSessionStore.GetAsync<string>("jobNumber")).Value;
                if (getJobSelected != null)
                {
                    JobSelected = getJobSelected;
                    SubdivisionJobPickService.JobNumber = JobSelected;
                }
            }
            if (SubdivisionJobPickService.SupplierNumber == null)
            {
                var getSupplierSelected = (await ProtectedSessionStore.GetAsync<int>("supplier")).Value;
                if (getSupplierSelected != 0)
                {
                    SubdivisionJobPickService.SupplierNumber = getSupplierSelected;
                }
            }
        }
        
    }
    protected async Task SubdivisionSelected(object theUserChoice)
    {
        if (theUserChoice != null && SelectedSubdivision != ((int)theUserChoice))
        {
            SelectedSubdivision = ((int)theUserChoice);
            await StoreSubdivJobSupplier();
            if(SelectedSubdivision == -1)
            {
                JobsForSelect = AllJobs;//All subdvisions
            }
            else
            {
                JobsForSelect = AllJobs.Where(x => x.SubdivisionId == SelectedSubdivision).ToList();//filter jobs by subdivision
            }
        }
    } 
    protected async Task SupplierChanged(object theUserChoice)
    {
        if (theUserChoice != null &&  SelectedSupplier != ((int)theUserChoice))
        {
            SelectedSupplier = ((int)theUserChoice);
            await StoreSubdivJobSupplier();          
        }
    }
    protected async Task JobChanged(object theUserChoice)
    {
        if (theUserChoice != null && SelectedJob != ((string)theUserChoice))
        {
            SelectedJob = ((string)theUserChoice);
            await StoreSubdivJobSupplier();            
        }
    }
    private async Task GetStoredSubdivJobSupplier()
    {
        JobSelected = (await ProtectedSessionStore.GetAsync<string>("jobNumber")).Value;
    }
    public async Task StoreSubdivJobSupplier()
    {
        if (SubdivisionJobPickService.JobNumber != null)
        {
            await ProtectedSessionStore.SetAsync("jobNumber", SubdivisionJobPickService.JobNumber);
        }
        if (SubdivisionJobPickService.SubdivisionId != null)
        {
            await ProtectedSessionStore.SetAsync("subdivision", SubdivisionJobPickService.SubdivisionId);
        }
        if (SubdivisionJobPickService.SupplierNumber != null)
        {
            await ProtectedSessionStore.SetAsync("supplier", SubdivisionJobPickService.SupplierNumber);
        }

    }
    //didin't work. trying to make it update the alljobs list on refresh pge if subdivisino was selected. 
    // async Task ChangedHandler()
    // {        
    //     SelectedSubdivision = SubdivisionJobPickService.SubdivisionId;
    //     //StateHasChanged();
    //     if (SelectedSubdivision == -1)
    //     {
    //         JobsForSelect = AllJobs;//All subdvisions
    //     }
    //     else
    //     {
    //         JobsForSelect = AllJobs.Where(x => x.SubdivisionId == SelectedSubdivision).ToList();//filter jobs by subdivision
    //     }
    //     StateHasChanged();
    // }
}
