﻿using ERP.Data.Models.Abstract;

namespace ERP.Data.Models.Dto;

public class JobPostingGroupDto : IMapFrom<JobPostingGroup>
{
    public string JobPostingGroupCode { get; set; } = null!;

    public string? JobPostingGroupDesc { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

}
