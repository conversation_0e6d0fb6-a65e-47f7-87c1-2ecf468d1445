﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Contact
{
    public int ContactId { get; set; }

    public string? ContactKey { get; set; }

    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    public string? Address1 { get; set; }

    public string? Address2 { get; set; }

    public string? City { get; set; }

    public string? State { get; set; }

    public string? Postcode { get; set; }

    public string? WorkPhone { get; set; }

    public string? HomePhone { get; set; }

    public string? MobilePhone { get; set; }

    public string? Fax { get; set; }

    public string? Email { get; set; }

    public string? ContactPassword { get; set; }

    public string? Country { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<SupplierContact> SupplierContacts { get; set; } = new List<SupplierContact>();
}
