﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class BCPoInvoiceDetail
    {
       // public string? id { get; set; }
       // public string? documentId { get; set; }
      //  public int? sequence { get; set; }
      //  public string? itemId { get; set; }
      //  public string? accountId { get; set; }
        public string? lineType { get; set; }
        public string? lineObjectNumber { get; set; }
        public string? description { get; set; }
       // public string? unitOfMeasureId { get; set; }
        public string? unitOfMeasureCode { get; set; }
        public double? directUnitCost { get; set; }
        public double? qtyToReceive { get; set; }
        //public int? discountAmount { get; set; }
        //public int? discountPercent { get; set; }
        //public bool? discountAppliedBeforeTax { get; set; }
        //public int? amountExcludingTax { get; set; }
        public string? taxCode { get; set; }
        //public int amountIncludingTax { get; set; }
        //public int? invoiceDiscountAllocation { get; set; }
        //public float? netAmount { get; set; }
        //public float? netTaxAmount { get; set; }
        //public float? netAmountIncludingTax { get; set; }
        //public string? expectedReceiptDate { get; set; }
        //public string? itemVariantId { get; set; }
        //public string? locationId { get; set; }
        public string? projectNumber { get; set; }
        public string? projectTaskNumber { get; set; }
        public string? shortcutDimension1Code { get; set; }
        public string? shortcutDimension2Code { get; set; }
    }

    public class BCResponseInvoiceDetail
    {
        public string id { get; set; }
        public string documentId { get; set; }
        public int sequence { get; set; }
        public string itemId { get; set; }
        public string accountId { get; set; }
        public string lineType { get; set; }
        public string lineObjectNumber { get; set; }
        public string description { get; set; }
        public string unitOfMeasureId { get; set; }
        public string unitOfMeasureCode { get; set; }
        public float unitCost { get; set; }
        public int quantity { get; set; }
        public int discountAmount { get; set; }
        public int discountPercent { get; set; }
        public bool discountAppliedBeforeTax { get; set; }
        public int amountExcludingTax { get; set; }
        public string taxCode { get; set; }
        public float taxPercent { get; set; }
        public int totalTaxAmount { get; set; }
        public int amountIncludingTax { get; set; }
        public int invoiceDiscountAllocation { get; set; }
        public float netAmount { get; set; }
        public float netTaxAmount { get; set; }
        public float netAmountIncludingTax { get; set; }
        public string expectedReceiptDate { get; set; }
        public string itemVariantId { get; set; }
        public string locationId { get; set; }
    }

}
