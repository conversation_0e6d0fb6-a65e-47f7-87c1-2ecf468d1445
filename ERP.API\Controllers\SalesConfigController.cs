﻿using AutoMapper;
using Azure.Identity;
using DocumentFormat.OpenXml.Drawing.Charts;
using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using DocumentFormat.OpenXml.Spreadsheet;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NLog;
using System.Linq;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class SalesConfigController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        public SalesConfigController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
        }

        //TODO: show the ones marked "d" but unapproved, so they can approve the delete these are cancellations

        [HttpGet]
        public async Task<IActionResult> GetSalesConfigCommunitiesAsync()
        {

            var salesConfigsCombinedModel = new List<SalesConfigCombinedTreeModel>();
            try
            {
               // salesConfigsCombinedModel = await _context.Salesconfigs.Include("JobNumberNavigation.Subdivision").Where(x => x.IsActive == true).Select(x => new SalesConfigCombinedTreeModel(){ SubdivisionId = x.JobNumberNavigation.SubdivisionId, SubdivisionName = x.JobNumberNavigation.Subdivision.SubdivisionName }).Distinct().ToListAsync();

                var jobs = await _context.Salesconfigs.Include("JobNumberNavigation.Subdivision").Where(x => x.IsActive == true  && x.IsDeleted == "F" && (x.SsAction == "U" || x.SsAction == "A")).Select(x => new SalesConfigCombinedTreeModel() { SubdivisionId = x.JobNumberNavigation.SubdivisionId, SubdivisionName = x.JobNumberNavigation.Subdivision.SubdivisionName, JobNumber = x.JobNumber, SalesConfigId = x.SalesconfigId, SentToPurchasing = x.SentToPurchasing }).OrderBy(x => x.JobNumber).ToListAsync();

                var hasCO = _context.Salesconfigcos.Where(x => x.IsActive == true && jobs.Select(x => x.SalesConfigId).Contains(x.SalesconfigId) && x.SentToPurchasing != "T").Select(x => x.SalesconfigId).ToList();
                jobs.RemoveAll(x => (!hasCO.Contains((int)x.SalesConfigId) && x.SentToPurchasing == "T"));

                salesConfigsCombinedModel = jobs.Select(x => new SalesConfigCombinedTreeModel() { SubdivisionId = x.SubdivisionId, SubdivisionName = x.SubdivisionName, JobsString = string.Join(",",jobs.Where(a => a.SubdivisionId == x.SubdivisionId).Select(z => z.JobNumber)) }).OrderBy(x => x.SubdivisionName).ToList();
                var distinct1 = salesConfigsCombinedModel.Select(x => new { SubdivisionId = x.SubdivisionId, SubdivisionName = x.SubdivisionName, JobString = x.JobsString }).Distinct().ToList();
                salesConfigsCombinedModel = distinct1.Select(x => new SalesConfigCombinedTreeModel() { SubdivisionId = x.SubdivisionId, SubdivisionName = x.SubdivisionName, JobsString = x.JobString }).OrderBy(x => x.SubdivisionName).ToList();
                return new OkObjectResult(new ResponseModel<List<SalesConfigCombinedTreeModel>> { Value = salesConfigsCombinedModel, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SalesConfigCombinedTreeModel>> { IsSuccess = false, Message = "Failed to get Sales Config Communities", Value = null });
            }
        }

        [HttpGet("{subdivisionId}")]
        public async Task<IActionResult> GetSalesConfigJobsByCommunityAsync(int subdivisionId)
        {
            var salesConfigsCombinedModel = new List<SalesConfigCombinedTreeModel>();
            try
            {
                //Not sure about ssaction != D 

                //salesConfigsCombinedModel = await _context.Salesconfigs.Include("JobNumberNavigation.Subdivision").Where(x => x.IsActive == true && x.JobNumberNavigation.SubdivisionId == subdivisionId && x.SsAction != "D").Select(x => new SalesConfigCombinedTreeModel() { SubdivisionId = x.JobNumberNavigation.SubdivisionId, JobNumber = x.JobNumber }).OrderBy(x => x.JobNumber).ToListAsync();
                salesConfigsCombinedModel = await _context.Salesconfigs.Include("JobNumberNavigation.Subdivision").Where(x => x.IsActive == true && x.JobNumberNavigation.SubdivisionId == subdivisionId && x.IsDeleted == "F" && (x.SsAction == "U" || x.SsAction == "A")).Select(x => new SalesConfigCombinedTreeModel() { SubdivisionId = x.JobNumberNavigation.SubdivisionId, JobNumber = x.JobNumber, SalesConfigId = x.SalesconfigId, LotNumber = x.JobNumberNavigation.LotNumber, LotAddress = x.JobNumberNavigation.JobAddress1, LotCity = x.JobNumberNavigation.JobCity, SentToPurchasing = x.SentToPurchasing }).OrderBy(x => x.JobNumber).ToListAsync();
                var salesConfigIds = salesConfigsCombinedModel.Select(x => x.SalesConfigId).ToList();
                List<int> hasUnapprovedCO = _context.Salesconfigcos.Where(x => salesConfigIds.Contains(x.SalesconfigId) && x.SentToPurchasing != "T").Select(x => x.SalesconfigId).ToList();

                salesConfigsCombinedModel.RemoveAll(x => x.SentToPurchasing == "T" && (!hasUnapprovedCO.Contains((int)x.SalesConfigId) ));

                salesConfigsCombinedModel = salesConfigsCombinedModel.Select(x => new SalesConfigCombinedTreeModel() { SubdivisionId = x.SubdivisionId, JobNumber = x.JobNumber, LotAddress = x.LotAddress, LotCity = x.LotCity, LotNumber = x.LotNumber, SentToPurchasing = x.SentToPurchasing }).OrderBy(x => x.JobNumber).ToList();
                //var distinctJobs = salesConfigsCombinedModel.Distinct().ToList();
                var distinctJobs = salesConfigsCombinedModel.DistinctBy(x => x.JobNumber).ToList();
                return new OkObjectResult(new ResponseModel<List<SalesConfigCombinedTreeModel>> { Value = distinctJobs, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SalesConfigCombinedTreeModel>> { IsSuccess = false, Message = "Failed to get Sales Config Jobs By Community", Value = null });
            }
        }
        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> GetSalesConfigsAndCosByJobAsync(string jobNumber)
        {
            var salesConfigsCombinedModel = new List<SalesConfigCombinedTreeModel>();
            try
            {
                //&& (x.SsAction == "U" || x.SsAction == "A") 
                var getSalesConfigForJob = await _context.Salesconfigs.Include("PhasePlan.MasterPlan").Where(x => x.IsActive == true && x.JobNumber == jobNumber && x.IsDeleted == "F").Select(x => new SalesConfigCombinedTreeModel()
                {
                    SalesConfigId = x.SalesconfigId,
                    Ownername = x.Ownername,
                    SalesContact = x.SalesContact,
                    Owneraddress1 = x.Owneraddress1,
                    Owneraddress2 = x.Owneraddress2,
                    Ownerstate = x.Ownerstate,
                    Ownersuburb = x.Ownersuburb,
                    Owneremail = x.Owneremail,
                    Ownermobile = x.Ownermobile,
                    Ownerphone1 = x.Ownerphone1,
                    Ownerpostcode = x.Ownerpostcode,
                    UserContact1 = x.UserContact1,
                    UserContact2 = x.UserContact2,
                    PlanName = x.PhasePlan.MasterPlan.PlanName,
                    PlanNum = x.PhasePlan.MasterPlan.PlanNum,                   
                    OptionCode = $"{x.PhasePlan.MasterPlan.PlanNum}A0000000",//Base house
                    OptionDesc = x.PhasePlan.MasterPlan.ElevationCode,
                    SubdivisionId = x.JobNumberNavigation.SubdivisionId,
                    Status = x.Status,                    
                    JobNumber = x.JobNumber,
                    Saledate = x.Saledate,
                    Closingdate = x.Closingdate,
                    Canceldate = x.Canceldate,
                    Estimatedsettlementdate = x.Estimatedsettlementdate,
                    Baseprice = x.Baseprice,                
                    LotPremium = x.LotPremium,
                    LotSwing = x.LotSwing,
                    PriceIncentive = x.PriceIncentive,
                    LotPremiumIncentive = x.LotPremiumIncentive,
                    OptionIncentive = x.OptionIncentive,
                    UpfrontIncentive = x.UpfrontIncentive,
                    UnitCost = (double?)x.PhasePlan.PhasePlanCost,
                    ListPrice = (double?)x.PhasePlan.PhasePlanPrice,
                    IsApproved = x.IsApproved,
                    BoolIsApproved = x.IsApproved == "T",
                    Approvable = x.IsApproved != "T",//TODO: also maybe not approvable if errors (ie if there are options needing estimates)
                    Ratificationdate = x.Ratificationdate,
                    SSConfigurationId = x.SsConfigurationid,
                    SalesagentEmail = x.SalesagentEmail,
                    SalesagentName = x.SalesagentName,
                    SentToPurchasing = x.SentToPurchasing,
                    SsAction = x.SsAction,
                    Salesconfig = _mapper.Map<SalesconfigDto>(x)
                }).ToListAsync();
                var hasCO = _context.Salesconfigcos.Where(x => getSalesConfigForJob.Select(y => y.SalesConfigId).Contains(x.SalesconfigId) && x.SentToPurchasing != "T").Select(x => x.SalesconfigId).ToList();
                getSalesConfigForJob.RemoveAll(x => (!hasCO.Contains((int)x.SalesConfigId) && x.SentToPurchasing == "T"));

                foreach (var item in getSalesConfigForJob)
                {                    
                    var getOptions = _context.Salesconfigoptions.Include("PlanOption").Where(x => x.SalesconfigId == item.SalesConfigId && x.IsActive == true);
                    item.SumOptionPrice = getOptions.Sum(y => (y.OptionPrice * y.OptionQuantity));//price seems to be unit price
                    item.SumBaseHouseAndOptionPrice = item.SumOptionPrice + item.Baseprice;
                    item.SumOptionCost = getOptions.Sum(x =>  x.PlanOption != null ? (item.Saledate < x.PlanOption.LastDate ? x.PlanOption.LastCost : x.PlanOption.CurrCost) : 0);//TODO: check date);
                    item.ListPrice = getOptions.Sum(x => x.PlanOption != null ? (item.Saledate < x.PlanOption.LastDate ? x.PlanOption.LastSelling : x.PlanOption.CurrSelling) : 0);//TODO: check date);
                    //TODO: sum unit cost and unit listed selling price of options, item.SumUnitCost = 
                }
                var getChangeOrders = await _context.Salesconfigcos.Where(x => x.IsActive == true && getSalesConfigForJob.Select(y => y.SalesConfigId).Contains(x.SalesconfigId)).Select(x => new SalesConfigCombinedTreeModel()
                {
                    SalesConfigId = x.SalesconfigId,
                    SalesConfigCoId = x.SalesconfigcoId,
                    CoStatusdate = x.CoStatusdate,
                    CoNumber = x.CoNumber,
                    CoStatus = x.CoStatus,                    
                    IsApproved = x.IsApproved,
                    SentToPurchasing = x.SentToPurchasing,
                    BoolIsApproved = x.IsApproved == "T",
                    Approvable = x.IsApproved != "T" && x.Salesconfig.IsApproved == "T",//TODO: also maybe not approvable if errors, also not approvable if initial config for the job not approved
                    Salesconfigco = _mapper.Map<SalesconfigcoDto>(x)
                }).ToListAsync();
                var getUnapprovedChangeOrders = await _context.Salesconfigcos.Where(x => x.IsActive == true && getSalesConfigForJob.Select(y => y.SalesConfigId).Contains(x.SalesconfigId) && x.SentToPurchasing != "T").Select(x => x.SalesconfigId).ToListAsync();
                //remove any change orders where there is no unapproved co for the job, else keep all the change orders for the job, even the approved ones
                getChangeOrders.RemoveAll(x => !getUnapprovedChangeOrders.Contains((int)x.SalesConfigId));
                foreach (var item in getChangeOrders)
                {
                    var getOptions = _context.Salesconfigcooptions.Include("PlanOption").Where(x => x.SalesconfigcoId == item.SalesConfigCoId && x.IsActive == true);
                    item.SumOptionPrice = getOptions.Sum(y => y.SalesconfigcoAction == "a" ? (y.SalesconfigcoPrice) * (y.SalesconfigcoQuantityChange) : (y.SalesconfigcoPrice) * (-y.SalesconfigcoQuantityChange));//TODO: wrong if quantity not 1, or quantity should be negative //quantity negative if action is d (drop?)
                    item.UnitCost = getOptions.Sum(x => x.PlanOption != null ? (item.Saledate < x.PlanOption.LastDate ? x.PlanOption.LastCost : x.PlanOption.CurrCost) : 0);//TODO: check date);
                    item.ListPrice = getOptions.Sum(x => x.PlanOption != null ? (item.Saledate < x.PlanOption.LastDate ? x.PlanOption.LastSelling : x.PlanOption.CurrSelling) : 0);//TODO: check date);

                    var checkConfigApproved = (_context.Salesconfigs.SingleOrDefault(x => x.SalesconfigId == item.SalesConfigId).IsApproved == "T");
                    var checkPriorCoApproved = !_context.Salesconfigcos.Any(x => x.CoNumber < item.CoNumber && x.IsApproved != "T");                    
                    var checkForNoEstimate = _context.Salesconfigcooptions.Any(x => x.SalesconfigcoId == item.SalesConfigCoId && string.IsNullOrWhiteSpace(x.AssociatedEstimate) && x.PlanOptionId == null);//TODO: check logic - is it approvable with no attached estimate?
                    item.Approvable = item.IsApproved != "T" && !checkForNoEstimate && !checkPriorCoApproved && checkConfigApproved;
                }
                salesConfigsCombinedModel.AddRange(getSalesConfigForJob);
                salesConfigsCombinedModel.AddRange(getChangeOrders);
                var returnList = salesConfigsCombinedModel.OrderBy(x => x.SalesConfigId).ThenBy(x => x.CoNumber).ToList();
                return new OkObjectResult(new ResponseModel<List<SalesConfigCombinedTreeModel>> { Value = returnList, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SalesConfigCombinedTreeModel>> { IsSuccess = false, Message = "Failed to get Sales Config and Cos by Job", Value = null });
            }
        }
        [HttpGet("{configId}")]
        public async Task<IActionResult> GetSalesConfigOptionsAsync(int configId)
        {
            var configOptions = new List<SalesConfigCombinedTreeModel>();
            try
            {
                var salesconfigoptions = _context.Salesconfigoptions.Include("PlanOption").Include(x => x.SalesconfigoptionsAttributes).ThenInclude(y => y.OpAttrGroupItem.AttrGroupAssignment.AttributeGroup).Include(x => x.SalesconfigoptionsAttributes).ThenInclude(y => y.OpAttrGroupItem.AttrGroupAssignment.AttributeItem).Where(x => x.IsActive == true && x.SalesconfigId == configId).ToList();
   
                configOptions = salesconfigoptions.Select(x => new SalesConfigCombinedTreeModel()
                {
                    UnitCost = x.Salesconfig?.Saledate < x.PlanOption?.LastDate ? x.PlanOption?.LastCost: x.PlanOption?.CurrCost,//TODO: check date
                    ListPrice = x.Salesconfig?.Saledate < x.PlanOption?.LastDate ? x.PlanOption?.LastCost :x.PlanOption?.CurrSelling,
                    SellingPrice = x.OptionPrice,
                    SalesConfigId = x.SalesconfigId,
                    SalesConfigOptionId = x.SalesconfigoptionsId,
                    Quantity = x.OptionQuantity,
                    OptionPrice = x.OptionPrice,
                    OptionQuantity = x.OptionQuantity,
                    OptionCode = x.SsOptioncode,
                    SsOptioncode = x.SsOptioncode,
                    OptionDesc = x.ScDescription,
                    ScDescription = x.ScDescription,
                    OptionSelections = x.OptionSelections,    
                    CustomOption = x.PlanOptionId == null,
                    AssociatedEstimate = x.AssociatedEstimate,
                    Salesconfigoption = _mapper.Map<SalesconfigoptionDto>(x),
                    AttributeSelectionsString = string.Join("; ", x.SalesconfigoptionsAttributes.Where(y => !string.IsNullOrWhiteSpace(y.OpAttrGroupItem?.AttrGroupAssignment?.AttributeItem?.Description)).Select(y => $"{y.OpAttrGroupItem?.AttrGroupAssignment?.AttributeGroup?.Description}:  {y.OpAttrGroupItem?.AttrGroupAssignment?.AttributeItem?.Description}"))
                }).ToList();
                return new OkObjectResult(new ResponseModel<List<SalesConfigCombinedTreeModel>> { Value = configOptions, IsSuccess = true });
            }
           
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SalesConfigCombinedTreeModel>> { IsSuccess = false, Message = "Failed to get Sales Config Options", Value = null });
            }
        }
        [HttpGet("{configCoId}")]
        public async Task<IActionResult> GetSalesConfigCoOptionsAsync(int configCoId)
        {
            var salesConfigsCosOptions = new List<SalesConfigCombinedTreeModel>();
            try
            {
                //TODO: if the cost comes from attached estimate rather than plan option, get the cost from the estimate 

               // var salesconfigcooptions = _context.Salesconfigcooptions.Include("PlanOption").Include(x => x.SalesconfigcooptionsAttributes).Where(x => x.IsActive == true && x.SalesconfigcoId == configCoId).ToList();
                var salesconfigcooptions = _context.Salesconfigcooptions.Include("PlanOption").Include(x => x.SalesconfigcooptionsAttributes).ThenInclude(y => y.OpAttrGroupItem.AttrGroupAssignment.AttributeGroup).Include(x => x.SalesconfigcooptionsAttributes).ThenInclude(y => y.OpAttrGroupItem.AttrGroupAssignment.AttributeItem).Where(x => x.IsActive == true && x.SalesconfigcoId == configCoId).ToList();
                salesConfigsCosOptions = salesconfigcooptions.Select(x => new SalesConfigCombinedTreeModel() {
                    UnitCost = x.Salesconfigco?.CoStatusdate < x.PlanOption?.LastDate ?  x.PlanOption?.LastCost : x.PlanOption?.CurrCost,//TODO: be sure to get the right one based on date
                    ListPrice = x.Salesconfigco?.CoStatusdate < x.PlanOption?.LastDate ? x.PlanOption?.LastSelling: x.PlanOption?.CurrSelling,
                    SellingPrice = x.SalesconfigcoPrice,
                    SalesConfigCoOptionId = x.SalesconfigcooptionsId,
                    SalesconfigcoPrice = x.SalesconfigcoPrice,
                    SalesConfigCoId = x.SalesconfigcoId,
                    SalesconfigcoAction = x.SalesconfigcoAction,
                    SalesconfigcoQuantityChange = x.SalesconfigcoQuantityChange,
                    OptionCode = x.SsOptioncode,
                    SsOptioncode = x.SsOptioncode,
                    OptionDesc = x.ScDescription,
                    ScDescription = x.ScDescription,
                    OptionSelections = x.SalesconfigcoSelections,
                    SalesconfigcoSelections = x.SalesconfigcoSelections,
                    SalesconfigcoNotes = x.SalesconfigcoNotes,
                    AssociatedEstimate = x.AssociatedEstimate,
                    CustomOption = x.PlanOptionId == null,
                    Salesconfigcooption = _mapper.Map<SalesconfigcooptionDto>(x),
                    AttributeSelectionsString = string.Join(",", x.SalesconfigcooptionsAttributes.Select(y => $"{y.OpAttrGroupItem?.AttrGroupAssignment?.AttributeGroup?.Description} - {y.OpAttrGroupItem?.AttrGroupAssignment?.AttributeItem?.Description}"))
                }).ToList();
               foreach(var option in salesConfigsCosOptions.Where(x => !string.IsNullOrWhiteSpace(x.AssociatedEstimate)))
                {
                    //get costs from estimate instead
                    var selectHeaderId = Convert.ToInt32(option.AssociatedEstimate?.Split('-')[0]);
                    var getHeader = _context.Estheaders.Where(x => x.EstheaderId == selectHeaderId);
                    var getEstDetails = _context.Estdetails.Where(x => x.Estoption.EstheaderId == selectHeaderId).ToList();
                    option.UnitCost = getEstDetails.Sum(x => x.Amount);
                    //TODO: if the estheader came from a estcusoptions, those can have a list price, but no way to know which, and those prices don't appear filled
                    //var getCustomOption = _context.Estcustoptions.Where(x => x.EstheaderId == selectHeaderId);
                }
                return new OkObjectResult(new ResponseModel<List<SalesConfigCombinedTreeModel>> { Value = salesConfigsCosOptions, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SalesConfigCombinedTreeModel>> { IsSuccess = false, Message = "Failed to get Sales Config Co Options", Value = null });
            }
        }
        
        [HttpPut]
        public async Task<IActionResult> ApproveCoAsync([FromBody] SalesConfigCombinedTreeModel model)
        { 
            try
            {
                //TODO: if it was a cancellation (ssaction = D), this needs to generate cancelation entries in budget? 


                var coToUpdate = _context.Salesconfigcos.SingleOrDefault(x => x.SalesconfigcoId == model.SalesConfigCoId);
                var findConfig = _context.Salesconfigs.SingleOrDefault(x => x.SalesconfigId == coToUpdate.SalesconfigId);
                var checkConfigApproved = !(_context.Salesconfigs.SingleOrDefault(x => x.SalesconfigId == coToUpdate.SalesconfigId).IsApproved == "T");
                var checkPriorCoApproved = !_context.Salesconfigcos.Any(x => x.CoNumber < coToUpdate.CoNumber && x.IsApproved != "T");
                if (checkConfigApproved)
                {
                    //return error message saying initial config and any prior change orders must be approved first
                    return Ok(new ResponseModel<SalesConfigCombinedTreeModel> { IsSuccess = false, Message = "There is a configuration and/or prior change orders that must be approved first" });
                }
                var checkForNoEstimate = _context.Salesconfigcooptions.Any(x => x.SalesconfigcoId == model.SalesConfigCoId && string.IsNullOrWhiteSpace(x.AssociatedEstimate) && x.PlanOptionId == null);
                if (checkForNoEstimate)
                {
                    //return error message saying can't approve if there are options with no estimate
                    return Ok(new ResponseModel<SalesConfigCombinedTreeModel> { IsSuccess = false, Message = "There are options that need attached estimate" });
                }
                coToUpdate.IsApproved = model.BoolIsApproved ? "T" : "F";
                coToUpdate.SentToPurchasing = model.BoolIsApproved ? "T" : "F";
                coToUpdate.UpdatedBy = User.Identity.Name.Split('@')[0];
                coToUpdate.UpdatedDateTim = DateTime.Now;
                _context.Salesconfigcos.Update(coToUpdate);
                await _context.SaveChangesAsync();
                if (model.BoolIsApproved)
                {
                    await GenerateBudgetFromCOAsync((int)model.SalesConfigCoId, findConfig.SsAction == "D");//cancel if config ssaction = d
                }
                return new OkObjectResult(new ResponseModel<SalesConfigCombinedTreeModel> { IsSuccess = true, Value = model, Message = "Approved" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SalesConfigCombinedTreeModel> { IsSuccess = false, Message = "Failed to approve Co", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> ApproveConfigAsync([FromBody] SalesConfigCombinedTreeModel model)
        {
            try
            {
                var checkForNoEstimate = _context.Salesconfigoptions.Any(x => x.SalesconfigId == model.SalesConfigId && string.IsNullOrWhiteSpace(x.AssociatedEstimate) && x.PlanOptionId == null);
                if (checkForNoEstimate)
                {
                    //return error message saying can't approve if there are options with no estimate
                    return Ok(new ResponseModel<SalesConfigCombinedTreeModel> { IsSuccess = false, Message = "There are options that need attached estimate" });
                }
                var configToUpdate = _context.Salesconfigs.Include(x => x.PhasePlan.MasterPlan).SingleOrDefault(x => x.SalesconfigId == model.SalesConfigId);
                configToUpdate.IsApproved = model.BoolIsApproved ? "T" : "F";
                configToUpdate.SentToPurchasing = model.BoolIsApproved ? "T" : "F";
                configToUpdate.UpdatedBy = User.Identity.Name.Split('@')[0];
                configToUpdate.UpdatedDateTim = DateTime.Now;
                _context.Salesconfigs.Update(configToUpdate);
                await _context.SaveChangesAsync();

                //update plan on job
                var findJob = _context.Jobs.FirstOrDefault(x => x.JobNumber == configToUpdate.JobNumber);
                findJob.PlanName = configToUpdate.PhasePlan.MasterPlan.PlanName;
                findJob.PlanCode = configToUpdate.PhasePlan.MasterPlan.PlanNum;
                findJob.UpdateDateTime = DateTime.Now;
                findJob.UpdatedBy = User.Identity.Name.Split('@')[0];
                _context.Jobs.Update(findJob);
                await _context.SaveChangesAsync();

                if (model.BoolIsApproved)
                {
                    await GenerateBudgetFromConfigAsync((int)model.SalesConfigId, configToUpdate.SsAction == "D");//ssaction = d - deleted, for cancel
                }
                return new OkObjectResult(new ResponseModel<SalesConfigCombinedTreeModel> { IsSuccess = true, Value = model, Message = "Approved" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SalesConfigCombinedTreeModel> { IsSuccess = false, Message = "Failed to approve Config", Value = null });
            }
        }
        
        private async Task GenerateBudgetFromCOAsync(int salesConfigCOId, bool isCancel = false)
        {
            try
            {
                var updateBy = User.Identity.Name.Split('@')[0];
                var getConfig = _context.Salesconfigcos.Include("Salesconfig").SingleOrDefault(x => x.SalesconfigcoId == salesConfigCOId).Salesconfig;

                //add the details
                //get all the activities 
                var getItemsQuery = (from a in _context.Salesconfigcooptions.Where(x => x.SalesconfigcoId == salesConfigCOId && x.IsActive == true)
                                join b in _context.AvailablePlanOptions on a.PlanOptionId equals b.PlanOptionId
                                join z in _context.MasterPlans on b.MasterPlanId equals z.MasterPlanId
                                join c in _context.AsmHeaders.Where(x => x.IsActive == true) on new { mo = b.MasterOptionId ?? 0, mp = b.MasterPlanId ?? 0 } equals new { mo = c.MasterOptionId ?? 0, mp = c.MasterPlanId ?? 0 }
                                join d in _context.AsmDetails.Where(x => x.IsActive == true) on c.AsmHeaderId equals d.AsmHeaderId
                                join l in _context.MasterItems.Where(x => x.IsActive == true) on d.MasterItemId equals l.MasterItemId
                                join o in _context.MasterItemPhases on l.MasterItemPhaseId equals o.MasterItemPhaseId
                                join m in _context.BomClasses on l.BomClassId equals m.BomClassId
                                join n in _context.Pactivities.Where(x => x.DivId == 1) on m.BomClassId equals n.BomClassId
                                join r in _context.PhasePlans on b.PhasePlanId equals r.PhasePlanId
                                join t in _context.Trades on n.TradeId equals t.TradeId
                                from q in _context.PactivityAreaSuppliers.Where(x => x.IsActive == true && x.PactivityId == n.PactivityId && x.SubdivisionId == r.SubdivisionId).DefaultIfEmpty()
                                join u in _context.Suppliers on q.SubNumber equals u.SubNumber
                                from w in _context.Jccostcodes.Where(x => x.IsActive == true && x.JccostcodeId == n.JccostcodeId).DefaultIfEmpty()
                                from y in _context.Jccategories.Where(x => x.IsActive == true && x.JccategoryId == n.JccategoryId).DefaultIfEmpty()
                                from s in _context.Costs.Where(x => x.IsActive == true && x.SubdivisionId == q.SubdivisionId && x.SubNumber == q.SubNumber && x.MasterItemId == l.MasterItemId).DefaultIfEmpty()
                                from v in _context.Costs.Where(x => x.IsActive == true && x.MasterItemId == l.MasterItemId && x.SubNumber == q.SubNumber && (x.SubdivisionId == 1)).DefaultIfEmpty()
                                select new
                                {
                                    SalesConfigCoOptionsId = (int?)a.SalesconfigcooptionsId,
                                    SubdivCosts = v.CostsId != 0 ? v.SubdivisionId : (int?)null,
                                    OptionNumber = a.SsOptioncode,
                                    OptionDesc = a.ScDescription,
                                    SellingPrice = isCancel == false ? a.SalesconfigcoQuantityChange * a.SalesconfigcoPrice : -a.SalesconfigcoQuantityChange * a.SalesconfigcoPrice,
                                    OptionQty = ((a.SalesconfigcoAction == "a" && isCancel == false) || (a.SalesconfigcoAction == "d" && isCancel == true)) ? a.SalesconfigcoQuantityChange : -a.SalesconfigcoQuantityChange,//option quantity, negative if it's a cancel or if co deletes option
                                    ItemDesc = l.ItemDesc,
                                    OptionSelections = a.SalesconfigcoSelections,
                                    OptionNotes = a.SalesconfigcoNotes,
                                    OptionLongDesc = b.OptionLongDesc,
                                    MasterItemId = (int?)l.MasterItemId,
                                    OrderUnit = l.OrderUnit,
                                    TakeoffUnit = l.TakeoffUnit,
                                    ItemNumber = l.ItemNumber,
                                    PhaseCode = o.PhaseCode,
                                    TakeoffQty = d.Factor,
                                    //// OrderQty = d.Factor * (isCancel == false ? a.SalesconfigcoQuantityChange : -a.SalesconfigcoQuantityChange),
                                    OrderQty = ((a.SalesconfigcoAction == "a" && isCancel == false) || (a.SalesconfigcoAction == "d" && isCancel == true)) ? a.SalesconfigcoQuantityChange * d.Factor : -a.SalesconfigcoQuantityChange * d.Factor,
                                    Pounit = l.OrderUnit,
                                    Pounitqty = ((a.SalesconfigcoAction == "a" && isCancel == false) || (a.SalesconfigcoAction == "d" && isCancel == true)) ? a.SalesconfigcoQuantityChange * d.Factor : -a.SalesconfigcoQuantityChange * d.Factor,//TODO: check this
                                    Pounitcost = s != null ? s.UnitCost : v != null && v.CostsId != 0 ? v.UnitCost : 0,
                                    PocostCode = w.CostCode,
                                    JcCategory = y.Category,
                                    Category = "M",//for estdetail, It seems it can be either M or c. not sure what those mean
                                    CostsId = s != null ? s.CostsId : v != null ? v.CostsId : (int?)null,
                                    PlanOptionId = (int?)b.PlanOptionId,
                                    Pojobnumber = getConfig.JobNumber,
                                    Releasecode = n.Releasecode,
                                    Activity = n.Activity,
                                    Podescription = n.Activity,
                                    TradeId = (int?)t.TradeId,
                                    SactivityId = n.SactivityId,
                                    SubNumber = (int?)q.SubNumber,
                                    Povendor = u.ShortName,
                                    // Amount = s != null ? s.UnitCost * d.Factor * a.SalesconfigcoQuantityChange : v.CostsId != 0 ? v.UnitCost * d.Factor * a.SalesconfigcoQuantityChange : 0,
                                    Amount = s != null ? ((a.SalesconfigcoAction == "a" && isCancel == false) || (a.SalesconfigcoAction == "d" && isCancel == true)) ? s.UnitCost * a.SalesconfigcoQuantityChange * d.Factor : s.UnitCost * -a.SalesconfigcoQuantityChange * d.Factor : v.CostsId != 0 ? ((a.SalesconfigcoAction == "a" && isCancel == false) || (a.SalesconfigcoAction == "d" && isCancel == true)) ? v.UnitCost * a.SalesconfigcoQuantityChange * d.Factor : v.UnitCost * -a.SalesconfigcoQuantityChange * d.Factor : 0,
                                     Errors = ErrorString(s != null ? s.CostsId : v != null ? v.CostsId : 0, w != null ? w.JccostcodeId : 0, y != null ? y.JccategoryId : 0),
                                     Warnings = "",
                                     CreatedBy = updateBy
                                }).ToList();

                var getCustomOptions = _context.Salesconfigcooptions.Where(x => x.SalesconfigcoId == salesConfigCOId && x.IsActive == true && !string.IsNullOrWhiteSpace(x.AssociatedEstimate)).ToList();
                var selectHeaderId = getCustomOptions.Select(x => x.EstheaderId).ToList();
                var getEstDetailOptions = _context.Estdetails.Include("Estoption").Include("Estactivity").Where(x => selectHeaderId.Contains(x.Estoption.EstheaderId) && x.IsActive == true && x.Estoption.IsActive == true).ToList();
                var getSuppliers = _context.Suppliers.ToList();
                var customOptionQuery = (from a in getCustomOptions
                                         join b in getEstDetailOptions on a.EstheaderId equals b.Estoption.EstheaderId
                                         join c in getSuppliers on b.Estactivity.SelectedVendor equals c.SubNumber //Is this the right thing to do here??
                                         select new
                                         {
                                             SalesConfigCoOptionsId = (int?)a.SalesconfigcooptionsId,
                                             SubdivCosts = (int?)null,
                                             OptionNumber = a.SsOptioncode,//b.Estoption.OptionNumber,//should this instead be something else. this gets base
                                             OptionDesc = a.ScDescription, //b.Estoption.OptionDesc,
                                             SellingPrice = isCancel == false ? a.SalesconfigcoQuantityChange * a.SalesconfigcoPrice : -a.SalesconfigcoQuantityChange * a.SalesconfigcoPrice,
                                             OptionQty = ((a.SalesconfigcoAction == "a" && isCancel == false) || (a.SalesconfigcoAction == "d" && isCancel == true)) ? a.SalesconfigcoQuantityChange : -a.SalesconfigcoQuantityChange,//option quantity, negative if it's a cancel or if co deletes option
                                             ItemDesc = b.ItemDesc,
                                             OptionSelections = a.SalesconfigcoSelections,
                                             OptionNotes = a.SalesconfigcoNotes,
                                             OptionLongDesc = b.Estoption.OptionLongDesc,
                                             MasterItemId = b.MasterItemsId,
                                             OrderUnit = b.OrdrUnit,
                                             TakeoffUnit = b.TakeoffUnit,
                                             ItemNumber = b.ItemNumber,
                                             PhaseCode = b.PhaseCode,
                                             TakeoffQty = b.TakeoffQuantity,
                                             //OrderQty = b.OrderQty,
                                             OrderQty = ((a.SalesconfigcoAction == "a" && isCancel == false) || (a.SalesconfigcoAction == "d" && isCancel == true)) ? b.OrderQty * a.SalesconfigcoQuantityChange : -b.OrderQty * a.SalesconfigcoQuantityChange,
                                             Pounit = b.OrdrUnit,
                                             Pounitqty = (a.SalesconfigcoAction == "a" && isCancel == false) || (a.SalesconfigcoAction == "d" && isCancel == true) ? b.OrderQty * a.SalesconfigcoQuantityChange : -b.OrderQty * a.SalesconfigcoQuantityChange,
                                             Pounitcost = b.Price,
                                             PocostCode = b.JcPhase,
                                             JcCategory = b.JcCategory,
                                             Category = "M",//for estdetail, It seems it can be either M or c. not sure what those mean            
                                             CostsId = b.CostsId,
                                             PlanOptionId = (int?)null,
                                             Pojobnumber = getConfig.JobNumber,
                                             Releasecode = b.Estactivity.Releasecode,
                                             Activity = b.Estactivity.BomClass,
                                             Podescription = b.Estactivity.BomClass,
                                             TradeId = b.Estactivity.TradeId,
                                             SactivityId = b.Estactivity.SactivityId,
                                             SubNumber = b.Estactivity.SelectedVendor,
                                             Povendor = c.ShortName,
                                             //Amount = b.Amount * a.SalesconfigcoQuantityChange,
                                             Amount = ((a.SalesconfigcoAction == "a" && isCancel == false) || (a.SalesconfigcoAction == "d" && isCancel == true)) ? b.Amount * a.SalesconfigcoQuantityChange : b.Amount * -a.SalesconfigcoQuantityChange,
                                             Errors = b.Errors,
                                             Warnings = b.Warnings,
                                             //TODO: it seems custom option estimate ones always have lump sum warning. Is that true?//also what about fill Lump column
                                             CreatedBy = updateBy
                                         }).ToList();
                getItemsQuery.AddRange(customOptionQuery);

                //insert the Estheaders, activities, details
                var estOptions = getItemsQuery.GroupBy(x => new { x.OptionNumber, x.Podescription }).ToList().GroupBy(x => x.Key.OptionNumber).Select(x => new Estoption()
                {
                    OptionDesc = x.First().First().OptionDesc,
                    OptionNumber = x.Key,
                    OptionQty = x.First().First().OptionQty,
                    OptionSalesPrice = x.First().First().SellingPrice,
                    OptionLongDesc = x.First().First().OptionLongDesc,
                    OptionNotes = x.First().First().OptionNotes,
                    OptionSelections = x.First().First().OptionSelections,
                    CreatedBy = updateBy,
                    SalesconfigcooptionsId = x.First().First().SalesConfigCoOptionsId,
                    Estdetails = x.SelectMany(y => y.Select(z => new Estdetail()
                    {
                        MasterItemsId = z.MasterItemId,
                        ItemDesc = z.ItemDesc,
                        ItemNumber = z.ItemNumber,
                        PhaseCode = z.PhaseCode,
                        JcPhase = z.PocostCode,
                        Category = z.Category,
                        JcCategory = z.JcCategory,
                        CostsId = z.CostsId,
                        TakeoffUnit = z.TakeoffUnit,
                        TakeoffQuantity = z.TakeoffQty,
                        OrderQty = z.OrderQty,
                        OrdrUnit = z.OrderUnit,
                        Price = z.Pounitcost,
                        Amount = z.Amount,
                        Errors = z.Errors,
                        Warnings = z.Warnings,
                        //CostCode = z.PocostCode,
                        //TODO: invalid jc category and invalid job cost code category error? 
                        CreatedBy = updateBy,
                    })).ToList()
                }).ToList();
                var getCo = _context.Salesconfigcos.Include("Salesconfig").SingleOrDefault(x => x.SalesconfigcoId == salesConfigCOId);
                var getEstThisJob = _context.Estheaders.Where(x => x.IsActive == true && x.JobNumber == getConfig.JobNumber);
                var getNextEstNumber = getEstThisJob.Any() ? getEstThisJob.Max(x => x.EstimateNumber) + 1 : 1;
                var addEstHeader = new Estheader()
                {
                    SalesconfigcoId = salesConfigCOId,
                    JobNumber = getCo.Salesconfig.JobNumber,
                    EstimateNumber = getNextEstNumber,
                    CreatedBy = updateBy,
                    Estimator = "Sales",
                    EstimateSalesPrice = estOptions.Sum(x => x.OptionSalesPrice),
                    ReferenceNumber = getNextEstNumber.ToString(),
                    ReferenceDesc = "Change Order",
                    ReferenceType = 1,//1 is customer change order 
                    EstimateSource = 1, //1 = LoadEstimate From Sales configuration/Change Order 
                    EstimateDescPe = getCo.Salesconfig.JobNumber,
                    Estoptions = estOptions,
                };
                _context.Estheaders.Add(addEstHeader);
                await _context.SaveChangesAsync();

                //TODO: it's a new estimate, insert all new activities, to accomodate chi wanted different activities per each estimate
                var findActivities = _context.Estactivities.Where(x => x.IsActive == true && x.JobNumber == getConfig.JobNumber).ToList();
                var addActivities = getItemsQuery.GroupBy(x => x.Podescription).ToList();
                var activitiesToAdd = addActivities.Where(y => !(findActivities.Select(x => x.BomClass).Contains(y.Key))).ToList();
                var newEstActivities = activitiesToAdd.Select(x => new Estactivity()
                {
                    JobNumber = getConfig.JobNumber,
                    BomClass = x.Key,
                    Releasecode = x.First().Releasecode,
                    SelectedVendor = x.First().SubNumber,
                    DefaultVendor = x.First().SubNumber,
                    TradeId = x.First().TradeId,
                    SactivityId = x.First().SactivityId,
                    SubNumber = x.First().SubNumber,
                    CreatedBy = updateBy,
                }).ToList();
                await _context.Estactivities.BulkInsertAsync(newEstActivities);


                var findInsertedEstDetailsId = addEstHeader.Estoptions.SelectMany(x => x.Estdetails).Select(x => x.EstdetailId).ToList();//TODO: check this really has them all
                var findInsertedEstDetails = _context.Estdetails.Include("MasterItems.BomClass").Where(x => findInsertedEstDetailsId.Contains(x.EstdetailId)).ToList();
                foreach (var detail in findInsertedEstDetails)
                {
                    detail.EstactivityId = _context.Estactivities.Where(x => x.BomClass == detail.MasterItems.BomClass.BomClass1 && x.JobNumber == getConfig.JobNumber).FirstOrDefault()?.EstactivityId;//TODO: might not be the same one as inserted
                }
                await _context.Estdetails.BulkUpdateAsync(findInsertedEstDetails);

                //if schedule exists and matching sactivity exists, link the new estactivity to the schedule,
                newEstActivities.AddRange(findActivities);
                var newEstactivityIds = newEstActivities.Where(x => x.SactivityId != null).Select(x => x.EstactivityId).ToList();
                var newActivitiesSactivityIds = newEstActivities.Where(x => x.SactivityId != null).Select(x => x.SactivityId).ToList();
                var findScheduleActivities = _context.ScheduleSactivities.Include(x => x.ScheduleM.Schedule).Include(x => x.ScheduleSactivityLinks).Where(x => x.ScheduleM.Schedule.JobNumber == getConfig.JobNumber && newActivitiesSactivityIds.Contains(x.SactivityId)).ToList();                
                var alreadyThereEstActivities = findScheduleActivities.SelectMany(x => x.ScheduleSactivityLinks).Where(x => newEstactivityIds.Contains(x.EstactivityId)).Select(x => x.EstactivityId).ToList();
                var newlinks = newEstActivities.Where(x => !alreadyThereEstActivities.Contains(x.EstactivityId) && x.SactivityId != null).Select(x => new ScheduleSactivityLink()
                {
                    EstactivityId = x.EstactivityId,
                    ScheduleAid = findScheduleActivities.FirstOrDefault(y => y.SactivityId == x.SactivityId)?.ScheduleAid ?? 0,
                }).DistinctBy(x => new { x.EstactivityId, x.ScheduleAid }).ToList();
                newlinks.RemoveAll(x => x.ScheduleAid == 0);
                await _context.ScheduleSactivityLinks.BulkInsertAsync(newlinks);




            }
            catch (Exception ex)
            {
                var test = ex.Message;
                throw;
            }
        }
   
        private async Task GenerateBudgetFromConfigAsync(int salesConfigId, bool isCancel = false)
        {
            try
            {               
                var updateBy = User.Identity.Name.Split('@')[0];
                var getConfig = _context.Salesconfigs.SingleOrDefault(x => x.SalesconfigId == salesConfigId);

                var getItemsQuery = (from a in _context.Salesconfigoptions.Where(x => x.SalesconfigId == salesConfigId && x.IsActive == true)
                                join b in _context.AvailablePlanOptions on a.PlanOptionId equals b.PlanOptionId
                                join z in _context.MasterPlans on b.MasterPlanId equals z.MasterPlanId
                                join c in _context.AsmHeaders.Where(x => x.IsActive == true) on new { mo = b.MasterOptionId ?? 0, mp = b.MasterPlanId ?? 0 } equals new { mo = c.MasterOptionId ?? 0, mp = c.MasterPlanId ?? 0 }
                                join d in _context.AsmDetails.Where(x => x.IsActive == true) on c.AsmHeaderId equals d.AsmHeaderId
                                join l in _context.MasterItems.Where(x => x.IsActive == true) on d.MasterItemId equals l.MasterItemId
                                join o in _context.MasterItemPhases on l.MasterItemPhaseId equals o.MasterItemPhaseId
                                join m in _context.BomClasses on l.BomClassId equals m.BomClassId
                                join n in _context.Pactivities.Where(x => x.DivId == 1) on m.BomClassId equals n.BomClassId             
                                join r in _context.PhasePlans on b.PhasePlanId equals r.PhasePlanId
                                join t in _context.Trades on n.TradeId equals t.TradeId
                                from q in _context.PactivityAreaSuppliers.Where(x => x.IsActive == true && x.PactivityId == n.PactivityId && x.SubdivisionId == r.SubdivisionId).DefaultIfEmpty() 
                                join u in _context.Suppliers on q.SubNumber equals u.SubNumber
                                from w in _context.Jccostcodes.Where(x => x.IsActive == true && x.JccostcodeId == n.JccostcodeId).DefaultIfEmpty()
                                from y in _context.Jccategories.Where(x => x.IsActive == true && x.JccategoryId == n.JccategoryId).DefaultIfEmpty()
                                from s in _context.Costs.Where(x => x.IsActive == true && x.SubdivisionId == q.SubdivisionId && x.SubNumber == q.SubNumber && x.MasterItemId == l.MasterItemId).DefaultIfEmpty()
                                from v in _context.Costs.Where(x => x.IsActive == true && x.MasterItemId == l.MasterItemId && x.SubNumber == q.SubNumber && (x.SubdivisionId == 1)).DefaultIfEmpty()
                                select new
                                {
                                    SalesConfigOptionsId = (int?)a.SalesconfigoptionsId,
                                    SubdivCosts = v != null && v.CostsId != 0 ? v.SubdivisionId : (int?)null,                                    
                                    OptionNumber = a.SsOptioncode,
                                    OptionDesc = a.ScDescription,
                                    SellingPrice = isCancel == false ? a.OptionQuantity * a.OptionPrice : -a.OptionQuantity * a.OptionPrice,
                                    OptionQty = isCancel == false ? a.OptionQuantity : -a.OptionQuantity,//option quantity, negative if it's a cancel 
                                    ItemDesc = l.ItemDesc,
                                    OptionSelections = a.OptionSelections,
                                    OptionNotes = a.OptionNotes,
                                    OptionLongDesc = b.OptionLongDesc,
                                    MasterItemId = (int?)l.MasterItemId,
                                    OrderUnit = l.OrderUnit,
                                    TakeoffUnit = l.TakeoffUnit,
                                    ItemNumber = l.ItemNumber,
                                    PhaseCode = o.PhaseCode,
                                    TakeoffQty = d.Factor,
                                    OrderQty = d.Factor * (isCancel == false ? a.OptionQuantity : -a.OptionQuantity),
                                    Pounit = l.OrderUnit,
                                    Pounitqty = d.Factor * (isCancel == false ? a.OptionQuantity : -a.OptionQuantity),//TODO: check this, also what if negative
                                    Pounitcost = s!= null ? s.UnitCost : v != null && v.CostsId != 0 ? v.UnitCost : 0,//Should come from costs table
                                    PocostCode = w.CostCode,
                                    JcCategory = y.Category,
                                    Category = "M",//for estdetail, It seems it can be either M or c. not sure what those mean
                                    CostsId = s != null ? s.CostsId : v != null ? v.CostsId : (int?)null,
                                    PlanOptionId = (int?)b.PlanOptionId,
                                    Pojobnumber = getConfig.JobNumber,
                                    Releasecode = n.Releasecode,
                                    Podescription = n.Activity,
                                    Activity = n.Activity,
                                    TradeId = (int?)t.TradeId,
                                    SactivityId = n.SactivityId,
                                    SubNumber = (int?)q.SubNumber,
                                    Povendor = u.ShortName,
                                    Amount = s != null ? s.UnitCost * d.Factor * (isCancel == false ? a.OptionQuantity : -a.OptionQuantity) : v != null && v.CostsId != 0 ? v.UnitCost * d.Factor * (isCancel == false ? a.OptionQuantity : -a.OptionQuantity) : 0,
                                    Errors = ErrorString(s != null ? s.CostsId : v != null ? v.CostsId : 0, w != null ? w.JccostcodeId : 0, y != null ? y.JccategoryId : 0),//TODO: include no supplier error
                                    Warnings = "",
                                    CreatedBy = updateBy
                                }).ToList();

                var getCustomOptions = _context.Salesconfigoptions.Where(x => x.SalesconfigId == salesConfigId && x.IsActive == true && !string.IsNullOrWhiteSpace(x.AssociatedEstimate)).ToList();
                var selectHeaderId = getCustomOptions.Select(x => x.EstheaderId).ToList();
                var getEstDetailOptions = _context.Estdetails.Include("Estoption").Include("Estactivity").Where(x => selectHeaderId.Contains(x.Estoption.EstheaderId) && x.IsActive == true && x.Estoption.IsActive == true).ToList();
                var getSuppliers = _context.Suppliers.ToList();
                var customOptionQuery = (from a in getCustomOptions
                                         join b in getEstDetailOptions on a.EstheaderId equals b.Estoption.EstheaderId
                                         join c in getSuppliers on b.Estactivity.SelectedVendor equals c.SubNumber 
                                         select new
                                         {
                                             SalesConfigOptionsId = (int?)a.SalesconfigoptionsId,
                                             SubdivCosts = (int?)null,
                                             OptionNumber = a.SsOptioncode,//b.Estoption.OptionNumber,//should this instead be something else. this gets base
                                             OptionDesc = a.ScDescription, //b.Estoption.OptionDesc,
                                             SellingPrice = isCancel == false ? a.OptionQuantity * a.OptionPrice : -a.OptionQuantity * a.OptionPrice,
                                             OptionQty = isCancel == false ? a.OptionQuantity : -a.OptionQuantity,//option quantity, negative if it's a cancel
                                             ItemDesc = b.ItemDesc,
                                             OptionSelections = a.OptionSelections,
                                             OptionNotes = a.OptionNotes,
                                             OptionLongDesc = b.Estoption.OptionLongDesc,
                                             MasterItemId = b.MasterItemsId,
                                             OrderUnit = b.OrdrUnit,
                                             TakeoffUnit = b.TakeoffUnit,
                                             ItemNumber = b.ItemNumber,
                                             PhaseCode = b.PhaseCode,
                                             TakeoffQty = b.TakeoffQuantity,
                                             OrderQty = b.OrderQty * (isCancel == false ? a.OptionQuantity : -a.OptionQuantity),                  
                                             Pounit = b.OrdrUnit,
                                             Pounitqty = b.OrderQty  * (isCancel == false ? a.OptionQuantity : -a.OptionQuantity),
                                             Pounitcost = b.Price,
                                             PocostCode = b.JcPhase,
                                             JcCategory = b.JcCategory,
                                             Category = "M",//for estdetail, It seems it can be either M or c. not sure what those mean               
                                             CostsId = b.CostsId,
                                             PlanOptionId = (int?)null,
                                             Pojobnumber = getConfig.JobNumber,
                                             Releasecode = b.Estactivity.Releasecode,
                                             Podescription = b.Estactivity.BomClass,
                                             Activity = b.Estactivity.BomClass,
                                             TradeId = b.Estactivity.TradeId,
                                             SactivityId = b.Estactivity.SactivityId,
                                             SubNumber = (int?)c.SubNumber,
                                             Povendor = c.ShortName,
                                             Amount = getConfig.SsAction == "D" ? b.Amount * -a.OptionQuantity : b.Amount * a.OptionQuantity,
                                             Errors = b.Errors,
                                             Warnings = b.Warnings,
                                             CreatedBy = updateBy
                                         }).ToList();

                getItemsQuery.AddRange(customOptionQuery);

                //Generate budget for base house only if not already on a previous config? but what about cancelled customer?
                if (!_context.Salesconfigs.Where(x => x.SalesconfigId != salesConfigId && x.JobNumber == getConfig.JobNumber && x.Status == "spec home").Any())
                {
                    var getBaseHouse = _context.Salesconfigs.Include("PhasePlan.MasterPlan").SingleOrDefault(x => x.SalesconfigId == salesConfigId);
                    var baseHouseQuery = (from c in _context.AsmHeaders.Where(x => x.IsActive == true && x.MasterOptionId == 1 && x.MasterPlanId == getBaseHouse.PhasePlan.MasterPlanId && (x.AssemblyDesc.ToLower().Contains("base house") || x.AssemblyCode.Contains("A0000000")))
                                          join d in _context.AsmDetails.Where(x => x.IsActive == true) on c.AsmHeaderId equals d.AsmHeaderId
                                          join l in _context.MasterItems.Where(x => x.IsActive == true) on d.MasterItemId equals l.MasterItemId
                                          join o in _context.MasterItemPhases.Where(x => x.IsActive == true) on l.MasterItemPhaseId equals o.MasterItemPhaseId
                                          join m in _context.BomClasses on l.BomClassId equals m.BomClassId
                                          join n in _context.Pactivities.Where(x => x.DivId == 1) on m.BomClassId equals n.BomClassId
                                          join r in _context.PhasePlans on getBaseHouse.PhasePlanId equals r.PhasePlanId
                                          join t in _context.Trades.Where(x => x.IsActive == true) on n.TradeId equals t.TradeId
                                          from q in _context.PactivityAreaSuppliers.Where(x => x.IsActive == true && x.PactivityId == n.PactivityId && x.SubdivisionId == r.SubdivisionId).DefaultIfEmpty()
                                          join u in _context.Suppliers on q.SubNumber equals u.SubNumber
                                          from w in _context.Jccostcodes.Where(x => x.IsActive == true && x.JccostcodeId == n.JccostcodeId).DefaultIfEmpty()
                                          from y in _context.Jccategories.Where(x => x.IsActive == true && x.JccategoryId == n.JccategoryId).DefaultIfEmpty()
                                          from s in _context.Costs.Where(x => x.IsActive == true && x.SubdivisionId == q.SubdivisionId && x.SubNumber == q.SubNumber && x.MasterItemId == l.MasterItemId).DefaultIfEmpty()
                                          from v in _context.Costs.Where(x => x.IsActive == true && x.MasterItemId == l.MasterItemId && x.SubNumber == q.SubNumber && (x.SubdivisionId == 1)).DefaultIfEmpty()
                                          select new
                                          {
                                              SalesConfigOptionsId = (int?)null,
                                              SubdivCosts = v != null && v.CostsId != 0 ? v.SubdivisionId : (int?)null,
                                              OptionNumber = c.AssemblyCode,
                                              OptionDesc = "BASE HOUSE",
                                              SellingPrice = isCancel == false ? getConfig.Baseprice : -getConfig.Baseprice,///??
                                              OptionQty = isCancel == false ? (double?)1 : (double?)-1,//option quantity, negative if it's a cancel (only 1 base house)
                                              ItemDesc = l.ItemDesc,
                                              OptionSelections = (string?)null,
                                              OptionNotes = (string?)null,
                                              OptionLongDesc = (string?)null,
                                              MasterItemId = (int?)l.MasterItemId,
                                              OrderUnit = l.OrderUnit,
                                              TakeoffUnit = l.TakeoffUnit,
                                              ItemNumber = l.ItemNumber,
                                              PhaseCode = o.PhaseCode,
                                              TakeoffQty = d.Factor,
                                              OrderQty = d.Factor * (isCancel == false ? (double?)1 : (double?)-1),
                                              Pounit = l.OrderUnit,
                                              Pounitqty = d.Factor * (isCancel == false ? (double?)1 : (double?)-1),
                                              Pounitcost = s != null ? s.UnitCost : v != null && v.CostsId != 0 ? v.UnitCost : 0,//Should come from costs table, what about date
                                              PocostCode = w.CostCode,
                                              JcCategory = y.Category,
                                              Category = "M",//for estdetail, It seems it can be either M or c. not sure what those mean
                                              CostsId = s != null ? s.CostsId : v != null ? v.CostsId : (int?)null,
                                              PlanOptionId = (int?)null,
                                              Pojobnumber = getConfig.JobNumber,
                                              Releasecode = n.Releasecode,
                                              Podescription = n.Activity,
                                              Activity = n.Activity,
                                              TradeId = (int?)t.TradeId,
                                              SactivityId = n.SactivityId,
                                              SubNumber = (int?)q.SubNumber,
                                              Povendor = u.ShortName,
                                              Amount = s != null ? s.UnitCost * d.Factor * (isCancel == false ? (double?)1 : (double?)-1) : v != null && v.CostsId != 0 ? v.UnitCost * d.Factor * (isCancel == false ? (double?)1 : (double?)-1) : 0,
                                              Errors = ErrorString(s != null ? s.CostsId : v != null ? v.CostsId : 0, w != null ? w.JccostcodeId : 0, y != null ? y.JccategoryId: 0),
                                              Warnings = "",
                                              CreatedBy = updateBy
                                          }).ToList();

                    getItemsQuery.AddRange(baseHouseQuery);
                }

                //insert the Estheaders, activities, details
                var estOptions = getItemsQuery.GroupBy(x => new { x.OptionNumber, x.Podescription }).ToList().GroupBy(x => x.Key.OptionNumber).Select(x => new Estoption()
                {
                    OptionDesc = x.First().First().OptionDesc,
                    OptionSalesPrice = x.First().First().SellingPrice,
                    OptionNumber = x.Key,
                    OptionLongDesc = x.First().First().OptionLongDesc,
                    OptionNotes = x.First().First().OptionNotes,
                    OptionSelections = x.First().First().OptionSelections,
                    OptionQty = x.First().First().OptionQty,
                    CreatedBy = updateBy,
                    SalesconfigoptionsId = x.First().First().SalesConfigOptionsId,                    
                    Estdetails = x.SelectMany(y => y.Select(z => new Estdetail()
                    {
                        MasterItemsId = z.MasterItemId,
                        ItemDesc = z.ItemDesc,
                        ItemNumber = z.ItemNumber,
                        PhaseCode = z.PhaseCode,
                        Category = z.Category,
                        JcPhase = z.PocostCode,
                        JcCategory = z.JcCategory,
                        CostsId = z.CostsId,
                        TakeoffUnit = z.TakeoffUnit,
                        TakeoffQuantity = z.TakeoffQty,
                        OrderQty = z.OrderQty,
                        OrdrUnit = z.OrderUnit,
                        Price = z.Pounitcost,
                        Amount = z.Amount,
                        Errors = z.Errors,
                        Warnings = z.Warnings,
                        CreatedBy = updateBy,
                    })).ToList()
                }).ToList();

                var getEstThisJob = _context.Estheaders.Where(x => x.IsActive == true && x.JobNumber == getConfig.JobNumber);
                var getNextEstNumber = getEstThisJob.Any() ? getEstThisJob.Max(x => x.EstimateNumber) + 1 : 1;
                var addEstHeader = new Estheader()
                {
                    SalesconfigId = salesConfigId,
                    JobNumber = getConfig.JobNumber,
                    EstimateNumber = getNextEstNumber,
                    CreatedBy = updateBy,
                    Estimator = "Sales", 
                    EstimateSalesPrice = estOptions.Sum(x => x.OptionSalesPrice),
                    ReferenceNumber = "Orig",
                    Estoptions = estOptions,
                    ReferenceDesc = "Sales Configuration",
                    ReferenceType = 0,//0 is original estimate, 1, is customer chagne order, 2 is non billable variance, 99 is default
                    EstimateSource = 1,//1 = LoadEstimate From Sales configuration/Change Order
                    EstimateDescPe = getConfig.JobNumber
                };
                _context.Estheaders.Add(addEstHeader);//This is adding the header with the options and details
                await _context.SaveChangesAsync();

                var findActivities = _context.Estactivities.Where(x => x.IsActive == true && x.JobNumber == getConfig.JobNumber).ToList();
                var addActivities = getItemsQuery.GroupBy(x => x.Podescription).ToList();
                var activitiesToAdd = addActivities.Where(y => !(findActivities.Select(x => x.BomClass).Contains(y.Key))).ToList();
                var newEstActivities = activitiesToAdd.Select(x => new Estactivity()
                {
                    BomClass = x.Key,
                    JobNumber = getConfig.JobNumber,
                    Releasecode = x.First().Releasecode,
                    SelectedVendor = x.First().SubNumber,
                    DefaultVendor = x.First().SubNumber,
                    TradeId = x.First().TradeId,
                    SactivityId = x.First().SactivityId,
                    SubNumber = x.First().SubNumber,
                    CreatedBy = updateBy,
                }).ToList();
                await _context.Estactivities.BulkInsertAsync(newEstActivities);


                var findInsertedEstDetailsId = addEstHeader.Estoptions.SelectMany(x => x.Estdetails).Select(x => x.EstdetailId).ToList();//TODO: check this really has them all
                var findInsertedEstDetails = _context.Estdetails.Include("MasterItems.BomClass").Where(x => findInsertedEstDetailsId.Contains(x.EstdetailId)).ToList();
                foreach (var detail in findInsertedEstDetails)
                {
                    detail.EstactivityId = _context.Estactivities.Where(x => x.BomClass == detail.MasterItems.BomClass.BomClass1 && x.JobNumber == getConfig.JobNumber).FirstOrDefault()?.EstactivityId;
                }
                await _context.Estdetails.BulkUpdateAsync(findInsertedEstDetails);

                //if schedule exists and matching sactivity exists, link the new estactivity to the schedule,
                newEstActivities.AddRange(findActivities);
                var newEstactivityIds = newEstActivities.Where(x => x.SactivityId != null).Select(x => x.EstactivityId).ToList();
                var newActivitiesSactivityIds = newEstActivities.Where(x => x.SactivityId != null).Select(x => x.SactivityId).ToList();
                var findScheduleActivities = _context.ScheduleSactivities.Include(x => x.ScheduleM.Schedule).Include(x => x.ScheduleSactivityLinks).Where(x => x.ScheduleM.Schedule.JobNumber == getConfig.JobNumber && newActivitiesSactivityIds.Contains(x.SactivityId)).ToList();
                var alreadyThereEstActivities = findScheduleActivities.SelectMany(x => x.ScheduleSactivityLinks).Where(x => newEstactivityIds.Contains(x.EstactivityId)).Select(x => x.EstactivityId).ToList();
                var newlinks = newEstActivities.Where(x => !alreadyThereEstActivities.Contains(x.EstactivityId) && x.SactivityId != null).Select(x => new ScheduleSactivityLink()
                {
                    EstactivityId = x.EstactivityId,
                    ScheduleAid = findScheduleActivities.FirstOrDefault(y => y.SactivityId == x.SactivityId)?.ScheduleAid ?? 0,
                }).DistinctBy(x => new { x.EstactivityId, x.ScheduleAid }).ToList();
                newlinks.RemoveAll(x => x.ScheduleAid == 0);
                await _context.ScheduleSactivityLinks.BulkInsertAsync(newlinks);
            }
            catch (Exception ex)
            {
                var test = ex.Message;
                throw;
            }
        }
        private static string ErrorString(int costsId, int costCodeId, int categoryId)
        {
            string error = "";
            if (costsId == 0)
            {
                error += "4";
                if (costCodeId == 0)
                {
                    error += "|6";
                }
                if (categoryId == 0)
                {
                    error += "|7";
                }
            }
            else
            {
                if (costCodeId == 0)
                {
                    error += "6";
                    if (categoryId == 0)
                    {
                        error += "|7";
                    }
                }
                else if (categoryId == 0)
                {
                    error += "7";
                }
            }
            return error;
        }
    }
}
