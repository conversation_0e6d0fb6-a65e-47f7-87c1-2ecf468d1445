﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class TemplateSactivityPredDto : IMapFrom<TemplateSactivityPred>
{
    public int TemplateAid { get; set; }

    public int PredSactivityId { get; set; }

    public DateTime Createddatetime { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public string? UpdatedBy { get; set; }

    public SactivityDto? PredSactivity { get; set; } 

    public TemplateSactivityDto? TemplateA { get; set; } 
}
