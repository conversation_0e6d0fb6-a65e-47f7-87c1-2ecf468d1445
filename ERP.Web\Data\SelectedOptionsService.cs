﻿using DocumentFormat.OpenXml.Spreadsheet;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using NLog;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class SelectedOptionsService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public SelectedOptionsService(IConfiguration configuration, HttpClient httpClient, IDownstream<PERSON>pi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;

        }
        public async Task<ResponseModel<List<JobCustomerDto>>> GetRecentJobCustomersAsync()
        {
            var items = new List<JobCustomerDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/SelectedOptions/RecentJobCustomers");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<JobCustomerDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<JobCustomerDto>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<JobCustomerDto>> { IsSuccess = false, Message = "Error while fetching customers" };
            }
        }
        public async Task<ResponseModel<byte[]>> SendCustomersToNAVAsync(List<JobCustomerDto> customers)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<JobCustomerDto>, ResponseModel<byte[]>>(
                            "DownstreamApi", customers,
                             options => {
                                 options.RelativePath = "api/selectedoptions/PushCustomersDataNav/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<byte[]>() { IsSuccess = false, Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<TbBuiltOptionDto>>> GetSelectedOptionsByJobAsync(string jobNumber)
        {
            var items = new List<TbBuiltOptionDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/SelectedOptions/SelectedOptions/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<TbBuiltOptionDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<TbBuiltOptionDto>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<TbBuiltOptionDto>> { IsSuccess = false, Message = "Error while fetching Selected Options" };
            }
        }

        public async Task<ResponseModel<List<AvailablePlanOptionDto>>> GetAllOptionsByJobAsync(string jobNumber)
        {
            var items = new List<AvailablePlanOptionDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/SelectedOptions/getalloptions/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<AvailablePlanOptionDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<AvailablePlanOptionDto>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<AvailablePlanOptionDto>> { IsSuccess = false, Message = "Error while fetching Selected Options" };
            }
        }

        public async Task<ResponseModel<List<TbBuiltOptionDto>>> GetAllSelectedOptionsByJobAsync(string jobNumber)
        {
            //above one gets only cms ones
            var items = new List<TbBuiltOptionDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/SelectedOptions/SelectedOptionsAll/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<TbBuiltOptionDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<TbBuiltOptionDto>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<TbBuiltOptionDto>> { IsSuccess = false, Message = "Error while fetching Selected Options" };
            }
        }
        public async Task<ResponseModel<List<BuyerDto>>> GetBuyersByJobAsync(string jobNumber)
        {

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/SelectedOptions/SelectedOptionsBuyer/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<BuyerDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<BuyerDto>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<BuyerDto>> { IsSuccess = false, Message = "Error getting buyer data" };
            }
        }
        public async Task<ResponseModel<CustomerDto>> GetJobCustomerAsync(string jobNumber)
        {

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
    options => options.RelativePath = $"api/SelectedOptions/JobCustomer/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<CustomerDto>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<CustomerDto> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<CustomerDto> { IsSuccess = false, Message = "Error while fetching customer" };
            }
        }
        public async Task<ResponseModel<List<TbBuiltOption>>> ApproveSelectedOptionsAsync(List<TbBuiltOptionDto> selectedOptions)
        {
            var items = new List<TbBuiltOptionDto>();
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<TbBuiltOptionDto>, ResponseModel<List<TbBuiltOption>>>(
                             "DownstreamApi", selectedOptions,
                              options => {
                                  options.RelativePath = "api/selectedoptions/approveselected/";
                              });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<TbBuiltOption>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<TbBuiltOption>> { IsSuccess = false, Message = "Error while updating Customer Data" };
            }
        }
        public async Task<ResponseModel<string>> PushCustomerDataAsync(string jobSelected)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<string, ResponseModel<string>>(
                             "DownstreamApi", jobSelected,
                              options => {
                                  options.RelativePath = "api/selectedoptions/pushcustomerdata/";
                              });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<string> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<string> { IsSuccess = false, Message = "Error while updating Customer Data" };
            }
        }
        public async Task<ResponseModel<byte[]>> PushCustomerDataNavAsync(string jobSelected)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<string, ResponseModel<byte[]>>(
                             "DownstreamApi", jobSelected,
                              options => {
                                  options.RelativePath = "api/selectedoptions/pushcustomerdatanav/";
                              });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<byte[]> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<byte[]> { IsSuccess = false, Message = "Error while updating Customer Data" };
            }
        }
        public async Task<ResponseModel<List<JobCustomerDto>>> UpdateCustomerSentToNavAsync(List<JobCustomerDto> jobCustomersToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<JobCustomerDto>, ResponseModel<List<JobCustomerDto>>>(
                            "DownstreamApi", jobCustomersToUpdate,
                             options => {
                                 options.RelativePath = "api/selectedoptions/UpdateCustomerSentToNav/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<JobCustomerDto>>() { Value = jobCustomersToUpdate, IsSuccess = false };
        }
    }
}
