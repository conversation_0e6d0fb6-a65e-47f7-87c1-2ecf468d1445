﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class HomeArea
{
    public int HomeAreaId { get; set; }

    public string? HomeArea1 { get; set; }

    public string? Note { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStampe { get; set; } = null!;

    public virtual ICollection<AsmHeader> AsmHeaders { get; set; } = new List<AsmHeader>();

    public virtual ICollection<AvailablePlanOption> AvailablePlanOptions { get; set; } = new List<AvailablePlanOption>();
}
