﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class VpoApprovalSeq
{
    public int VpoApprovalId { get; set; }

    public int VpoGroupId { get; set; }

    public string LevelCode { get; set; } = null!;

    public int? Seq { get; set; }

    public decimal? Threshold { get; set; }

    public string? EmpUserId { get; set; }

    public int? RoleId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual VpoApprovalHierarchy LevelCodeNavigation { get; set; } = null!;

    public virtual ICollection<Poapproval> Poapprovals { get; set; } = new List<Poapproval>();

    public virtual Role? Role { get; set; }

    public virtual VpoGroup VpoGroup { get; set; } = null!;
}
