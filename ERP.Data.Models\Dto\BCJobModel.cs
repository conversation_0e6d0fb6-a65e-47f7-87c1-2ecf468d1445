﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    //Job with just fields needed for BC
    public class BCJobModel : IMapFrom<Job>
    {
        public string? JobNumber { get; set; }
        public string? SubdivisionNum { get; set; } //BC won't have subdivisonId
        public string? EntityNum { get; set; }
        public string? JobAddress1 { get; set; }

        public string? JobAddress2 { get; set; }

        public string? JobCity { get; set; }

        public string? JobCounty { get; set; }

        public string? JobDesc { get; set; }

        public string? JobPostingGroup { get; set; }

        public string? JobState { get; set; }

        public string? JobZipCode { get; set; }

        public DateTime? CreatedDateTime { get; set; }

        public string? CreatedBy { get; set; }

        public DateTime? UpdateDateTime { get; set; }

        public string? UpdatedBy { get; set; }

        public bool? IsActive { get; set; }      
        public bool? Blocked { get; set; }
       // public bool BoolBlocked { get; set; }
        public bool? LinkWithErp { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<BCJobModel, Job>().ReverseMap();
        }
    }

}
