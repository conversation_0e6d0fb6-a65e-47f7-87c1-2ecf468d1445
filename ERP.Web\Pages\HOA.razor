﻿@page "/hoa/"
@inject HOAService HOAService
@inject AuthenticationStateProvider AuthenticationStateProvider
@using ERP.Web.Components
<TelerikTooltip TargetSelector=".tooltip-target" />
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<PageTitle>HOA</PageTitle>

<style type="text/css">

</style>

@if (HOAAssessmentRows == null)
{
    <p><em>Loading...</em></p>
}
else
{
    <div class="column d-flex">
        <div>
            <div class="col-lg-12">
                <div class="card" style="background-color: #2e5771">
                    <div class="card-body" style="padding:0.5rem">
                        <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Assessments</h7>
                    </div>
                </div>
            </div>
            <TelerikGrid Data=@HOAAssessmentRows
                         EditMode="@GridEditMode.Inline"
                         Pageable="true"
                         Height="800px"
                         Navigable="true"
                         OnRowClick="@OnHOAClickedHandler"
                         SelectionMode="GridSelectionMode.Single"
                         OnUpdate="@UpdateAssessmentHandler"
                         OnDelete="@DeleteAssessmentHandler"
                         ConfirmDelete="true"
                         Groupable="true"
                         FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                         FilterMenuType="@FilterMenuType.CheckBoxList"
                         Sortable="true" SortMode="@SortMode.Multiple">
                <GridColumns>
                    <GridColumn Field=Hoa.AssociationName Title="Association" Editable="false" />
                    <GridColumn Field=Hoa.Category Title="Category" Editable="true" />
                    <GridColumn Field=Subdivision.SubdivisionName Title="Subdivision" Editable="false" />
                    <GridColumn Field=AssessmentLabel Title="Label" />
                    <GridColumn Field=Icc Title="ICC" DisplayFormat="{0:C2}" />
                    <GridColumn Field=MonthAssessment Title="Month Assessment" DisplayFormat="{0:C2}" />
                    <GridColumn Field=OtherFee Title="Other Fee" DisplayFormat="{0:C2}" />
                    <GridColumn Field=BudgetYear DisplayFormat="{0:yyyy}" Title="Budget Year" />
                    <GridCommandColumn>
                        <GridCommandButton Title="Save" Class=".tooltip-target k-button-add" Command="Save" Icon="@FontIcon.Save" ShowInEdit="true"></GridCommandButton>
                        <GridCommandButton Title="Edit"  Class=".tooltip-target k-button-success" Command="Edit" Icon="@FontIcon.Pencil"></GridCommandButton>
                        <GridCommandButton Title="Delete" Class=".tooltip-target k-button-danger" Command="Delete" Icon="@FontIcon.Trash"></GridCommandButton>
                        <GridCommandButton Title="Cancel" Class=".tooltip-target" Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true"></GridCommandButton>
                    </GridCommandColumn>
                </GridColumns>
                <GridToolBarTemplate>
                    <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>
                    <GridCommandButton Command="AddFromToolbar" OnClick="@AddAssessmentFromToolbar" Icon="@FontIcon.Plus" Class=".tooltip-target k-button-add">Add Assessment</GridCommandButton>
                    <GridCommandButton Command="ExcelExport" Class=".tooltip-target k-button-success" Icon="@FontIcon.FileExcel">Export to Excel</GridCommandButton>
                </GridToolBarTemplate>
                <GridExport>
                    <GridExcelExport FileName="HOAAssessments" AllPages="true" OnBeforeExport="@OnExcelBeforeExport" />
                </GridExport>
            </TelerikGrid>
        </div>
        
        @if (HoaJobRows == null)
        {
            <div class="col-2 ml-2">
            <p><em>Select an assessment to see jobs</em></p>
            </div>

@*             <div style=@loadingItemStyle>Loading...</div> *@
            <!--<TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingItem />-->
        }
        else
        {
            <div>
                <div class="col-lg-12">
                    <div class="card" style="background-color: #2e5771">
                        <div class="card-body" style="padding:0.5rem">
                            <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Jobs for Selected Assessment</h7>
                        </div>
                    </div>
                </div>
                <TelerikGrid Data="HoaJobRows"
                             EditMode="@GridEditMode.Inline"
                             Pageable="true"
                             OnDelete="DeleteJobHandler"
                             ConfirmDelete="true"
                             Navigable="true"
                             Groupable="true"
                             Height="800px"
                             RowHeight="60" PageSize="20"
                             SelectionMode="GridSelectionMode.Multiple"
                             @bind-SelectedItems="@SelectedHoaJobs"
                             FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                             FilterMenuType="@FilterMenuType.CheckBoxList"
                             Sortable="true" SortMode="@SortMode.Multiple">
                    <GridColumns>
                        <GridCheckboxColumn SelectAllMode="GridSelectAllMode.All"></GridCheckboxColumn>
                        <GridColumn Field=JobNumber Title="Job Number" />
                        <GridColumn Field="JobNumberNavigation.JobAddress1" Title="Address"></GridColumn>
                        <GridColumn Field="JobNumberNavigation.Subdivision.SubdivisionName" Title="Subdivision" />
                        <GridCommandColumn>
                            <GridCommandButton Title="Delete" Class="k-tooltip-target k-button-danger" Command="Delete" Icon="@FontIcon.Trash"></GridCommandButton>
                        </GridCommandColumn>
                    </GridColumns>
                    <GridToolBarTemplate>
                        <GridCommandButton Command="AddFromToolbar" OnClick="@AddJobFromToolbar" Icon="@FontIcon.Plus" Class="k-button-add">Add Job</GridCommandButton>
                        <GridCommandButton Command="DeleteAll" OnClick="@DeleteAllHandler" Icon="@FontIcon.Minus" Class="k-button-danger">Remove Selected Jobs</GridCommandButton>
                        <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>
                    </GridToolBarTemplate>
                </TelerikGrid>
            </div>
            
        }
    </div>
}

<ERP.Web.Components.AddAssessment @ref="AddAssessmentModal" HandleAddSubmit="HandleValidAddAssessmentSubmit"></ERP.Web.Components.AddAssessment>

<ERP.Web.Components.AddJobToHOA @ref="AddJobModal" HandleAddSubmit="HandleValidAddJobSubmit" ></ERP.Web.Components.AddJobToHOA>

@code {
    private List<HoaAssessmentDto> HOAAssessmentRows;
    private List<HoaJobDto> HoaJobRows { get; set; }
    private IEnumerable<HoaJobDto>? SelectedHoaJobs { get; set; } = Enumerable.Empty<HoaJobDto>();
    private HoaAssessmentDto selected { get; set; }
    private List<SubdivisionDto> subdivisions;
    private List<HoaDto> associations;
    private List<JobDto> jobs;

    protected ERP.Web.Components.AddAssessment? AddAssessmentModal { get; set; }
    protected ERP.Web.Components.AddJobToHOA? AddJobModal { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var assessmentTask = HOAService.GetHOAAsync();
        var subdivisionTask = HOAService.GetSubdivisionsAsync();
        var associationsTask = HOAService.GetAssociationsAsync();
        var jobsTask = HOAService.GetJobsAsync();
        await Task.WhenAll(new Task[] { assessmentTask, subdivisionTask, assessmentTask, jobsTask });

        HOAAssessmentRows = assessmentTask.Result.Value;
        subdivisions = subdivisionTask.Result.Value;
        associations = associationsTask.Result.Value;
        jobs = jobsTask.Result.Value;
        AddAssessmentModal.TransferSubdivisions(subdivisions);
        AddAssessmentModal.TransferAssociations(associations);
        AddJobModal.TransferJobs(jobs);
    }
    private async Task OnExcelBeforeExport(GridBeforeExcelExportEventArgs args)
    {
        var budgetYearColumn = args.Columns.FirstOrDefault(x => x.Field == "BudgetYear");

        if (budgetYearColumn != null)
        {
            budgetYearColumn.NumberFormat = "yyyy";
        }
    }
    protected async Task OnHOAClickedHandler(GridRowClickEventArgs args)
    {
        selected = args.Item as HoaAssessmentDto;
        AddJobModal.TransferJobs(jobs.Where(x => x.SubdivisionId == selected.SubdivisionId).ToList());
        HoaJobRows = (await HOAService.GetHOAJobsAsync(selected)).Value;
    }

    private async void UpdateAssessmentHandler(GridCommandEventArgs args)
    {
        HoaAssessmentDto item = (HoaAssessmentDto)args.Item;
        var response1 = await HOAService.UpdateAssessmentAsync(item);
        HOAAssessmentRows = (await HOAService.GetHOAAsync()).Value;
        StateHasChanged();
    }

    private void AddAssessmentFromToolbar(GridCommandEventArgs args)
    {
        AddAssessmentModal.Show();

    }
    private async void HandleValidAddAssessmentSubmit(ResponseModel<HoaAssessmentDto> responseAssessment)
    {
        HOAAssessmentRows = (await HOAService.GetHOAAsync()).Value;
        AddAssessmentModal.Hide();

        ShowSuccessOrErrorNotification(responseAssessment.Message, responseAssessment.IsSuccess);

        StateHasChanged();
    }

    private async void DeleteAssessmentHandler(GridCommandEventArgs args)
    {
        HoaAssessmentDto item = (HoaAssessmentDto)args.Item;
        var response = await HOAService.DeleteAssessmentAsync(item);
        HOAAssessmentRows = (await HOAService.GetHOAAsync()).Value;
        StateHasChanged();
    }

    private async void DeleteJobHandler(GridCommandEventArgs args)
    {
        HoaJobDto item = (HoaJobDto)args.Item;
        var response = await HOAService.DeleteJobAsync(item);
        HoaJobRows = (await HOAService.GetHOAJobsAsync(selected)).Value;
        StateHasChanged();
    }

    private void AddJobFromToolbar(GridCommandEventArgs args)
    {
        AddJobModal.Show(selected.HoaAssessmentId);

    }
    private async void HandleValidAddJobSubmit(ResponseModel<List<HoaJobDto>> responseJob)
    {
        var getJobs = await HOAService.GetHOAJobsAsync(selected);
        HoaJobRows = getJobs.Value;
        AddJobModal.Hide();

        ShowSuccessOrErrorNotification(responseJob.Message, responseJob.IsSuccess);

        StateHasChanged();
    }
    private async void DeleteAllHandler(GridCommandEventArgs args)
    {
        var response = await HOAService.DeleteSelectedJobsForAssessmentAsync(SelectedHoaJobs.ToList());
        HoaJobRows = (await HOAService.GetHOAJobsAsync(selected)).Value;
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        StateHasChanged();
    }
    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}