﻿@page "/releaseschedules/"

@inject SubdivisionService SubdivisionService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject JobService JobService
@inject ScheduleService ScheduleService
@inject ReleaseScheduleService ReleaseScheduleService
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject NavigationManager NavManager
@inject IJSRuntime JsRuntime
@implements IDisposable
@inject ProtectedSessionStorage ProtectedSessionStore
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject LocalStorage LocalStorage
@inject IJSRuntime JSRuntime
@using Telerik.Blazor.Components.Grid
@using Telerik.Documents.SpreadsheetStreaming
@attribute [Authorize(Roles = "Admin, Operations, ReadOnly")]
@using ERP.Web.Components


<style>
    .MyTelerikNotification .k-notification-container .k-notification {
    font-family: "Roboto",sans-serif;
    font-size: .75rem;
    }

    .k-window {
    width: 350px;
    }

    .invalid-date {
        border: 1px solid red !important;
        background-color: #ffe6e6;
    }

</style>

<PageTitle>Schedule Releases | @SubdivisionName</PageTitle>
@* <ERP.Web.Components.SubdivisionJobPickerMenuBar ShowJob="true" ShowSubdivision="true"></ERP.Web.Components.SubdivisionJobPickerMenuBar> *@
<TelerikTooltip TargetSelector=".tooltip-target" />
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<div class="col-lg-12">
    <div class="card" style="background-color:#2e5771">
        <div class="card-body" style="padding:0.5rem">
            <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Schedule Releases</h7>
            @if (SubdivisionName != null)
            {
                <div style="display:block">
                    <h7 style="color:#fff">@SubdivisionName</h7>
                </div>
            }
        </div>
    </div>
</div>
<TelerikTooltip TargetSelector=".tooltip-target" />
<TelerikLoaderContainer Visible="@IsLoading" Text="Please wait..." />

@* <TelerikButton OnClick="@TestMultiple">button</TelerikButton> *@
@if (Schedules == null)
{
    <p><em>Loading...</em></p>
}
else
{
    <TelerikGrid Data=@Schedules
                 ConfirmDelete="true"
                 Groupable="true"
                 FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                 Pageable="true"
                 Height="1000px" RowHeight="40" PageSize="100"
                 Sortable="false"
                 Size="@ThemeConstants.Grid.Size.Medium"
                 OnStateInit="@((GridStateEventArgs<JobDto> args) => OnStateInitHandler(args))"
                 Resizable="true"
                 Reorderable="true"
                 EditMode="GridEditMode.Incell"
                 OnEdit="EditSchedule"
                 OnUpdate="UpdateSchedule"
                 @ref="@GridRef">
        <GridColumns>
            <GridCommandColumn Context="dataItem" Width="80px">
                @{
                    var schedule = dataItem as JobDto;
                    if (schedule != null && schedule.JobSchedule != null)
                    {
                        <TelerikButton OnClick="()=> GoToSchedule(schedule.JobSchedule)" Title="Go To Schedule" Class=" tooltip-target k-button-add">Schedule</TelerikButton>
                    }
                }
            </GridCommandColumn>
            <GridColumn Field="JobNumber" Editable="false" FilterMenuType="@FilterMenuType.CheckBoxList" Title="Job Number" />
            <GridColumn Field="Subdivision.SubdivisionName" FilterMenuType="@FilterMenuType.CheckBoxList" Filterable="false" Editable="false" Title="Subdivision" />
            <GridColumn Field="StickBuilingNum" FilterMenuType="@FilterMenuType.CheckBoxList" Editable="true" Title="Stick">
                <EditorTemplate>
                    @{
                        var itemToEdit = context as JobDto;
                        <TelerikDropDownList Data="@AllSticksInSubdivision"
                                             Enabled="@AllowEdit"
                                             Filterable="true"
                                             FilterOperator="@StringFilterOperator.Contains"
                                             @bind-Value="itemToEdit.StickBuilingNum"
                                             Width="200px">
                        </TelerikDropDownList>
                        @if (itemToEdit.StickBuilingNum == "Add New")
                        {
                            <br />
                            <TelerikTextBox @bind-Value="NewStickName" Placeholder="New Stick Name" Width="200px"></TelerikTextBox>
                        }
                    }
                </EditorTemplate>
            </GridColumn>
            <GridColumn Field="JobSchedule.Template.TemplateName" FilterMenuType="@FilterMenuType.CheckBoxList" Editable="true" Title="Template">
                <Template>
                    @{
                        var item = context as JobDto;
                        if (item?.JobSchedule != null && item?.JobSchedule.Template != null)
                        {
                            @item.JobSchedule.Template.TemplateName
                            // if(item.JobSchedule.UsingDefaultTemplate == true)
                            // {
                            //     <span style="color:red">@item.JobSchedule.Template.TemplateName</span>
                            // }
                            // else
                            // {
                            //     @item.JobSchedule.Template.TemplateName
                            // }
                        }
                        // if (item?.JobSchedule != null)
                        // {
                        //     <TelerikCheckBox @bind-Value="@item.JobSchedule.Template.TemplateName" Enabled="false" />
                        // }
                    }
                </Template>
                <EditorTemplate>
                    @{
                        var itemToEdit = context as JobDto;
                        <TelerikDropDownList Data="@Templates"
                                             Enabled="@AllowEdit"
                                             TextField="TemplateName"
                                             ValueField="TemplateId"
                                             DefaultText="Select Template"
                                             Filterable="true"
                                             FilterOperator="@StringFilterOperator.Contains"
                                             @bind-Value="itemToEdit.JobSchedule.TemplateId"
                                             Width="200px">
                        </TelerikDropDownList>
                    }
                </EditorTemplate>
            </GridColumn>
            <GridColumn Field="JobSchedule.BoolPublished" Title="Publish" FilterMenuType="@FilterMenuType.CheckBoxList" Editable="true">
                <Template>
                    @{
                        var item = context as JobDto;
                        if (item?.JobSchedule != null)
                        {
                            <TelerikCheckBox @bind-Value="@item.JobSchedule.BoolPublished" Enabled="false" />
                        }
                    }
                </Template>
            </GridColumn>
            <GridColumn Field="JobSchedule.PermitSubmitDate" Title="Permit Submit" Editable="true">
                <Template>
                    @{
                        var item = context as JobDto;
                        if (item != null && item.JobSchedule != null)
                        {
                            var date = item?.JobSchedule?.PermitSubmitDate;
                            @(date.HasValue ? date.Value.ToString("MM/dd/yyyy") : "")
                        }
                    }
                </Template>
                <EditorTemplate>
                    @{
                        var schedule = context as JobDto;
                        if (schedule?.JobSchedule != null)
                        {
                            <TelerikTextBox Value="@schedule.JobSchedule.PermitSubmitDateInputRaw"
                                            ValueChanged="@(v => OnPermitSubmitDateInputChanged(v, schedule))"
                                            ValueExpression="@(() => schedule.JobSchedule.PermitSubmitDateInputRaw)"
                                            Placeholder="MM/DD/YYYY"
                                            Width="130px"
                                            Class="@(schedule.JobSchedule.InvalidFields.ContainsKey(nameof(schedule.JobSchedule.PermitSubmitDateInputRaw)) ? "invalid-date" : "")" />

                            if (schedule.JobSchedule.InvalidFields.TryGetValue(nameof(schedule.JobSchedule.PermitSubmitDateInputRaw), out var errorMessage))
                            {
                                <div class="text-danger small">@errorMessage</div>
                            }
                        }
                    }
                </EditorTemplate>
            </GridColumn>
            <GridColumn Field="JobSchedule.PermitReceivedDate" Title="Permit Received" Editable="true">
                <Template>
                    @{
                        var item = context as JobDto;
                        if (item != null && item.JobSchedule != null)
                        {
                            var date = item?.JobSchedule?.PermitReceivedDate;
                            @(date.HasValue ? date.Value.ToString("MM/dd/yyyy") : "")
                        }
                    }
                </Template>
                <EditorTemplate>
                    @{
                        var schedule = context as JobDto;
                        if (schedule?.JobSchedule != null)
                        {
                            <TelerikTextBox Value="@schedule.JobSchedule.PermitReceivedDateInputRaw"
                                            ValueChanged="@(v => OnPermitReceivedDateInputChanged(v, schedule))"
                                            ValueExpression="@(() => schedule.JobSchedule.PermitReceivedDateInputRaw)"
                                            Placeholder="MM/DD/YYYY"
                                            Width="130px"
                                            Class="@(schedule.JobSchedule.InvalidFields.ContainsKey(nameof(schedule.JobSchedule.PermitReceivedDateInputRaw)) ? "invalid-date" : "")" />

                            if (schedule.JobSchedule.InvalidFields.TryGetValue(nameof(schedule.JobSchedule.PermitReceivedDateInputRaw), out var errorMessage))
                            {
                                <div class="text-danger small">@errorMessage</div>
                            }
                        }
                    }
                </EditorTemplate>
            </GridColumn>
            <GridColumn Field="JobSchedule.PermitNumber" Title="Permit Number" Editable="true" />
            <GridColumn Field="JobSchedule.IniSchApproved" Title="Released" FilterMenuType="@FilterMenuType.CheckBoxList" Editable="true">
                <Template>
                    @{
                        var item = context as JobDto;
                        if (item?.JobSchedule != null)
                        {
                            <TelerikCheckBox @bind-Value="@item.JobSchedule.IniSchApproved" Enabled="false" />
                        }
                    }
                </Template>
            </GridColumn>
            @*   <GridColumn Field="JobSchedule.IniSchApprovedby" Title="Released By" Editable="false" />
            <GridColumn Field="JobSchedule.IniSchApprovedDate" Title="Released Date" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" /> *@
            @* <GridColumn Field="JobSchedule.ActualStartDate" Title="Actual Start Date" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" />
            <GridColumn Field="JobSchedule.ActualEndDate" Title="Actual End Date" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" /> *@
            @*  <GridColumn Field="BaseDuration" Title="Base Duration" Editable="false" /> *@
            @*  <GridColumn Field="BaseStartDate" Title="Base Start Date" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" />
            <GridColumn Field="BaseEndDate" Title="Base End Date" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" /> *@
            @* <GridColumn Field="ProjStartDate" Title="Proj Start" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" />
            <GridColumn Field="ProjEndDate" Title="Proj End" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" /> *@
            <GridColumn Field="JobSchedule.OldIniSchStartDate" Title="Initial Start" Filterable="false" DisplayFormat="{0:MM/dd/yyyy}" FilterMenuType="@FilterMenuType.Menu" Visible="false" />
            <GridColumn Field="JobSchedule.IniSchStartDate" Title="Initial Start" Filterable="false" DisplayFormat="{0:MM/dd/yyyy}" FilterMenuType="@FilterMenuType.Menu" Editable="true">
                <Template>
                    @{
                        var item = context as JobDto;
                        if (item != null && item.JobSchedule != null)
                        {
                            var date = item?.JobSchedule?.IniSchStartDate;
                            @(date.HasValue ? date.Value.ToString("MM/dd/yyyy") : "")
                        }
                    }
                </Template>
                <EditorTemplate>
                    @{
                        var schedule = context as JobDto;
                        if (schedule?.JobSchedule != null)
                        {
                            <TelerikTextBox Value="@schedule.JobSchedule.IniSchStartDateInputRaw"
                                            ValueChanged="@(v => OnIniSchStartDateInputChanged(v, schedule))"
                                            ValueExpression="@(() => schedule.JobSchedule.IniSchStartDateInputRaw)"
                                            Placeholder="MM/DD/YYYY"
                                            Width="130px"
                                            Class="@(schedule.JobSchedule.InvalidFields.ContainsKey(nameof(schedule.JobSchedule.IniSchStartDateInputRaw)) ? "invalid-date" : "")" />

                            if (schedule.JobSchedule.InvalidFields.TryGetValue(nameof(schedule.JobSchedule.IniSchStartDateInputRaw), out var errorMessage))
                            {
                                <div class="text-danger small">@errorMessage</div>
                            }
                        }
                    }
                </EditorTemplate>
                <FilterMenuTemplate>
                    @foreach (var year in YearsRange)
                    {
                        <div>
                            <TelerikCheckBox Value="@(IsCheckboxInCurrentFilter(context.FilterDescriptor, year))"
                                             TValue="bool"
                                             ValueChanged="@((value) => UpdateCheckedYears(value, year, context))"
                                             Id="@($"year_{year}")">
                            </TelerikCheckBox>
                            <label for="@($"year_{year}")">
                                @year
                            </label>
                        </div>
                    }
                </FilterMenuTemplate>
                <FilterMenuButtonsTemplate Context="filterContext">
                    <TelerikButton OnClick="@(async _ => await filterContext.FilterAsync())">Filter </TelerikButton>
                    <TelerikButton OnClick="@(() => ClearFilterAsync(filterContext))">Clear</TelerikButton>
                </FilterMenuButtonsTemplate>
            </GridColumn>
            <GridColumn Field="JobSchedule.IniSchEndDate" Title="Initial End" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" />
            <GridColumn Field="JobSchedule.IniSchDuration" Title="Initial Duration" Editable="false" />
            @*  <GridCommandColumn>
                @{
                    if(AllowEdit)
                    {
                        <GridCommandButton Title="Save" Command="Save" Icon="@FontIcon.Save" ShowInEdit="true" Class="tooltip-target telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary"></GridCommandButton>
                        <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                        <GridCommandButton Title="Cancel" Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger tooltip-target"></GridCommandButton>
                    }
                }
            </GridCommandColumn> *@
        </GridColumns>

        <GridToolBarTemplate>
            <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>
            <GridCommandButton Command="ExcelExport" Title="Export to Excel" Icon="@FontIcon.FileExcel" Class="k-button-add">Export to Excel</GridCommandButton>
            <TelerikFileSelect Class="import"
                               AllowedExtensions="@AllowedExtensions"
                               Multiple="false"
                               MaxFileSize="@MaxSize"
                               OnSelect="@OnReleaseScheduleExcelSelectHandler">
                <SelectFilesButtonTemplate>
                    <span title="Only Permit related fields are imported" class="tooltip-target">
                    <TelerikFontIcon Icon="@FontIcon.Import"/>
                    &nbsp; Import
                    </span>
                </SelectFilesButtonTemplate>
            </TelerikFileSelect>
            <GridCommandButton Command="AssignSticks" OnClick="@OpenAssignSticks" Title="AssignSticks" Class=" tooltip-target k-button-add">Assign Sticks</GridCommandButton>
            <GridCommandButton Command="CreateSchedule" OnClick="@NewScheduleFromTemplate" Title="New schedule from template" Icon="@FontIcon.Plus" Class=" tooltip-target k-button-add"></GridCommandButton>
            <label for="SubdivisionFilter">Subdivision:</label>
            <TelerikDropDownList Id="SubdivisionFilter"
                                 Data="@AllSubdivisions"
                                 TextField="SubdivisionName"
                                 ValueField="SubdivisionId"
                                 Filterable="true"
                                 FilterOperator="StringFilterOperator.Contains"
                                 @bind-Value="@SelectedSubdivisionId" OnChange="SubdivisionSelectedHandler" Width="200px" />
            <label for="YearFilter">Year:</label>
            <TelerikDropDownList Id="YearFilter"
                                 Data="@YearsRangeSelect"
                                 TextField="YearString"
                                 ValueField="Year"
                                 Filterable="true"
                                 FilterOperator="StringFilterOperator.Contains"
                                 @bind-Value="@SelectedYear" OnChange="YearSelectedHandler" Width="200px" />
            <TelerikButton Title="Save Column Order" OnClick="@SetState">Save Column Order</TelerikButton>
            <TelerikButton Title="Reset Column Order" OnClick="@ResetState">Reset Column Order</TelerikButton>
            <TelerikButton OnClick="@(() => SortByColumn("JobSchedule.OldIniSchStartDate"))">Sort by Initial Start</TelerikButton>
        </GridToolBarTemplate>
        <GridExport>
            <GridExcelExport FileName="ReleaseSchedules" OnBeforeExport="@OnExcelBeforeExport" AllPages="true" />
        </GridExport>
    </TelerikGrid>
}
<AssignSticks HandleAddSubmit="@HandleAssignSticksSubmit" @ref="AssignSticksModal"></AssignSticks>
<ERP.Web.Components.CreateScheduleFromTemplate @ref="AddScheduleFromTemplateModal" SelectedTemplate="@SelectedTemplate.TemplateId" SelectedJobs="@SelectedJob" SelectedSubdivision="@SelectedSubdivisionId" HandleAddSubmit="@HandleValidAddScheduleFromTemplateSubmit"></ERP.Web.Components.CreateScheduleFromTemplate>
@code {
    private List<JobDto>? Schedules;
    private List<JobDto>? AllSchedules;
    private List<string> SelectedYears = new List<string>();
    private int? SelectedYear {get; set;}
    //private List<string> Years = new List<string>() { "2021", "2022", "2023", "2024", "2025" };
    private List<int>? YearsRange { get; set; } = new List<int>();
    private List<SelectYear>? YearsRangeSelect { get; set; } = new List<SelectYear>();
    public List<int>? SelectedSubdivisionIds { get; set; } = new List<int>();
    public string SelectedJobNum { get; set; } = "";
    public string? SubdivisionName { get; set; }
    public List<SubdivisionDto>? AllSubdivisions { get; set; }
    public SubdivisionDto? Subdivision { get; set; }
    private TelerikGrid<JobDto> GridRef { get; set; }
    //protected LotDetailsComponent? LotDetailsModal { get; set; }
    public string? JobNumber { get; set; }
    string UniqueStorageKey = "erp-schedule-releases";
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }                                                 
    public TelerikNotification NotificationReference { get; set; }
    private bool AllowEdit { get; set; } = true;
    public int? SelectedSubdivisionId { get; set; }
    public TemplateDto? SelectedTemplate { get; set; } = new TemplateDto();
    protected ERP.Web.Components.CreateScheduleFromTemplate? AddScheduleFromTemplateModal { get; set; }
    protected ERP.Web.Components.AssignSticks? AssignSticksModal { get; set; }
    public List<string>? SelectedJob = new List<string>();
    public JobDto? SelectedSchedule { get; set; }
    public List<string> AllSticksInSubdivision { get; set; } = new List<string> { "Add New" };
    public List<TemplateDto>? Templates { get; set; }
    public string NewStickName { get; set; }
    List<string> AllowedExtensions { get; set; } = new List<string>() { ".xlsx" };
    public int MaxSize { get; set; } = 10 * 1024 * 1024; //10 MB
    public List<int> IniSchStartDateFilterCurrentSelection { get; set; } = new List<int>();
    private List<JobDto> EditedItems = new();

    private string CurrentSortField;
    private bool SortDescending = false;
    private bool SortAscending;
    private bool IsSorted = false;
    private bool IsLoading { get; set; } = false;

    protected override async Task OnInitializedAsync()
    {
        YearsRange = Enumerable.Range(DateTime.Now.AddYears(-2).Year, 5).ToList();
        YearsRangeSelect = YearsRange.Select(y => new SelectYear { YearString = y.ToString(), Year = y }).ToList();
        YearsRangeSelect.Insert(0, new SelectYear() { YearString = "All", Year = null });
        var templatesTask = ScheduleService.GetTemplatesAsync();
        var subdivisionsTask = SubdivisionService.GetSubdivisionsAsync();
        var schedulesTask = ReleaseScheduleService.GetReleaseSchedules();
        await Task.WhenAll(new Task[] { templatesTask, subdivisionsTask, schedulesTask });
        Schedules = schedulesTask.Result.Value;
        AllSchedules = schedulesTask.Result.Value;
        var scheduleSubdivisions = AllSchedules.Select(x => x.SubdivisionId).Distinct().ToList();
        AllSubdivisions = subdivisionsTask.Result.Value.Where(x=> scheduleSubdivisions.Contains(x.SubdivisionId)).ToList();
        AllSubdivisions.Insert(0, new SubdivisionDto() { SubdivisionId = 0, SubdivisionName = "All" });//so they can "clear" the filter to show all
        SelectedSubdivisionId = 0;
        Templates = templatesTask.Result.Value;
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }

    protected override async Task OnParametersSetAsync()
    {
        // var schedulesTask = ReleaseScheduleService.GetReleaseSchedules();
        // await Task.WhenAll(new Task[] { schedulesTask });
        // Schedules = schedulesTask.Result.Value;
        // AllSchedules = schedulesTask.Result.Value;
        // StateHasChanged();
    }

    private void OnIniSchStartDateInputChanged(string newValue, JobDto schedule)
    {
        schedule.JobSchedule.IniSchStartDateInputRaw = newValue;
        schedule.JobSchedule.ParseIniSchStartDate();
    }

    private void OnPermitReceivedDateInputChanged(string newValue, JobDto schedule)
    {
        schedule.JobSchedule.PermitReceivedDateInputRaw = newValue;
        schedule.JobSchedule.ParsePermitReceivedDate();
    }

    private void OnPermitSubmitDateInputChanged(string newValue, JobDto schedule)
    {
        schedule.JobSchedule.PermitSubmitDateInputRaw = newValue;
        schedule.JobSchedule.ParsePermitSubmitDate();
    }

    

    protected async Task SubdivisionSelectedHandler()
    {

        if(SelectedSubdivisionId == 0)//"All" = clear the filter
        {
            // var filteredState = new GridState<JobDto>();                
            // var grid = GridRef;
            // if (grid != null)
            // {
            //     await grid.SetStateAsync(filteredState);
            // }
            if (SelectedYear != null)
            {
                var yearAsDate = new DateTime((int)SelectedYear, 1, 1);
                var nextYear = yearAsDate.AddYears(1);
                Schedules =  AllSchedules.Where(x => x.JobSchedule.IniSchStartDate >= yearAsDate && x.JobSchedule.IniSchStartDate < nextYear).ToList();
            }
            else
            {
                Schedules = AllSchedules;
            }

        }
        else
        {
            if (SelectedYear != null)
            {
                var yearAsDate = new DateTime((int)SelectedYear, 1, 1);
                var nextYear = yearAsDate.AddYears(1);
                Schedules = AllSchedules.Where(x => x.JobSchedule.IniSchStartDate >= yearAsDate && x.JobSchedule.IniSchStartDate < nextYear && x.SubdivisionId == SelectedSubdivisionId).ToList();
            }
            else
            {
                Schedules = AllSchedules.Where(x => x.SubdivisionId == SelectedSubdivisionId).ToList();
            }

            // var grid = GridRef;
            // var gridState = grid.GetState();
            // var existingFilters = gridState.FilterDescriptors.ToList();
            // var newcopmpositefilter = new CompositeFilterDescriptor()
            //     {
            //         FilterDescriptors = new FilterDescriptorCollection()
            //             {
            //                 new FilterDescriptor()
            //                 {
            //                     Member = nameof(JobDto.SubdivisionId),
            //                     MemberType = typeof(int),
            //                     Operator = FilterOperator.IsEqualTo,
            //                     Value = SelectedSubdivisionId
            //                 },
            //             }
            //     };
            // existingFilters.Add(newcopmpositefilter);
            // var filteredState = new GridState<JobDto>()
            //     {
            //         FilterDescriptors = existingFilters
            //     };

            // await grid.SetStateAsync(filteredState);
            // grid.Rebind();
        }
    }
    public class SelectYear()
    {
        public string? YearString { get; set; }
        public int? Year { get; set; }
    }
    protected async Task YearSelectedHandler()
    {

        if (SelectedYear == null)//clear the filter
        {
            // var filteredState = new GridState<JobDto>();
            // var grid = GridRef;
            // if (grid != null)
            // {
            //     await grid.SetStateAsync(filteredState);
            // }
            Schedules = SelectedSubdivisionId == 0 ? AllSchedules : AllSchedules.Where(x => x.SubdivisionId == SelectedSubdivisionId).ToList();
        }
        else
        {
            // var grid = GridRef;
            // var gridState = grid.GetState();
            // var existingFilters = gridState.FilterDescriptors.ToList();
            var yearAsDate = new DateTime((int)SelectedYear, 1, 1);
            var nextYear = yearAsDate.AddYears(1);

            Schedules = SelectedSubdivisionId == 0 ? AllSchedules.Where(x => x.JobSchedule.IniSchStartDate >= yearAsDate && x.JobSchedule.IniSchStartDate < nextYear).ToList() : AllSchedules.Where(x => x.SubdivisionId == SelectedSubdivisionId && x.JobSchedule.IniSchStartDate >= yearAsDate && x.JobSchedule.IniSchStartDate < nextYear).ToList();
            // var afterDateFilter = new FilterDescriptor()
            //     {
            //         Member = "JobSchedule.IniSchStartDate",
            //         MemberType = typeof(DateTime?),
            //         Operator = FilterOperator.IsGreaterThanOrEqualTo,
            //         Value = yearAsDate
            //     };
            // var beforeDateFilter = new FilterDescriptor()
            //     {
            //         Member = "JobSchedule.IniSchStartDate",
            //         MemberType = typeof(DateTime?),
            //         Operator = FilterOperator.IsLessThan,
            //         Value = nextYear
            //     };
            // var filterDescriptorsCollection = new FilterDescriptorCollection { afterDateFilter, beforeDateFilter };
            // var newcopmpositefilter = new CompositeFilterDescriptor()
            //     {
            //         FilterDescriptors = filterDescriptorsCollection,                        
            //     };
            // existingFilters.Add(newcopmpositefilter);
            // var filteredState = new GridState<JobDto>()
            //     {
            //         FilterDescriptors = existingFilters
            //     };

            // await grid.SetStateAsync(filteredState);
            // grid.Rebind();
        }
    }
    private async Task ClearFilterAsync(FilterMenuTemplateContext filterContext)
    {
        await filterContext.ClearFilterAsync();
        IniSchStartDateFilterCurrentSelection.Clear();
        GridRef.Rebind();
    }

    private bool IsCheckboxInCurrentFilter(CompositeFilterDescriptor filterDescriptor, int year)
    {
        return IniSchStartDateFilterCurrentSelection.Contains(year);
    }

    private void UpdateCheckedYears(bool isChecked, int itemValue, FilterMenuTemplateContext context)
    {
        var filterDescriptor = context.FilterDescriptor;
        filterDescriptor.LogicalOperator = FilterCompositionLogicalOperator.Or;

        var yearAsDate = new DateTime(itemValue, 1, 1);
        var nextYear = yearAsDate.AddYears(1);
        var afterDateFilter = new FilterDescriptor()
            {
                Member = "JobSchedule.IniSchStartDate",
                MemberType = typeof(DateTime?),
                Operator = FilterOperator.IsGreaterThanOrEqualTo,
                Value = yearAsDate
            };
        var beforeDateFilter = new FilterDescriptor()
            {
                Member = "JobSchedule.IniSchStartDate",
                MemberType = typeof(DateTime?),
                Operator = FilterOperator.IsLessThan,
                Value = nextYear
            };
        var filterDescriptorsCollection = new FilterDescriptorCollection { afterDateFilter, beforeDateFilter };

        if (!isChecked)
        {
            // find and remove the filter descriptor for this checkbox
            filterDescriptor.FilterDescriptors.Remove(filterDescriptor.FilterDescriptors.First(x =>
            {
                var fd = x as CompositeFilterDescriptor;            
                if (fd != null && fd.FilterDescriptors.Contains(afterDateFilter) && fd.FilterDescriptors.Contains(beforeDateFilter) && fd.LogicalOperator == FilterCompositionLogicalOperator.And)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }));
            IniSchStartDateFilterCurrentSelection.Remove(itemValue);
            if (itemValue >= DateTime.Now.Year)
            {
                filterDescriptor.FilterDescriptors.Remove(filterDescriptor.FilterDescriptors.First(x =>
                {
                    var fd = x as FilterDescriptor;
                    if (fd != null && fd.Operator == FilterOperator.IsNull && fd.Value == null)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }));
            }
        }
        else
        {
            // add a filter descriptor for this checkbox
            filterDescriptor.FilterDescriptors.Add(new CompositeFilterDescriptor { FilterDescriptors = filterDescriptorsCollection, LogicalOperator = FilterCompositionLogicalOperator.And });
            IniSchStartDateFilterCurrentSelection.Add(itemValue);
            if (itemValue >= DateTime.Now.Year)
            {
                filterDescriptor.FilterDescriptors.Add(new FilterDescriptor()
                    {
                        Member = "JobSchedule.IniSchStartDate",
                        MemberType = typeof(DateTime?),
                        Operator = FilterOperator.IsNull,
                        Value = null
                    });
            }
        }
    }

    private async Task UpdateInitialStart()
    {
        // if (SelectedSchedule?.JobSchedule != null && SelectedSchedule?.JobSchedule.IniSchStartDate != null)
        // {
        //     var checkHoliday = (await ScheduleService.CheckHolidayAsync(SelectedSchedule.JobSchedule.IniSchStartDate.Value.Date)).Value;
        //     if (checkHoliday)
        //     {
        //         var confirm = await Dialogs.ConfirmAsync("Selected Start Date is a weekend or holiday. Continue anyway? OK to use holdiday, cancel to select new date");
        //         if (!confirm)
        //         {
        //             return;
        //         }
        //     }
        //     var response = await ScheduleService.CalculateScheduleEndFromStartAsync(SelectedSchedule.JobSchedule);
        //     SelectedSchedule.JobSchedule.IniSchEndDate = response.Value.IniSchEndDate;
        //     SelectedSchedule.JobSchedule.IniSchDuration = response.Value.IniSchDuration;
        //     SelectedSchedule.JobSchedule.ScheduleId = response.Value.ScheduleId;
        //     ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        //     StateHasChanged();
        // }
        // else
        // {
        //     ShowSuccessOrErrorNotification("Missing start date", false);
        // }
    }
    private async Task GoToSchedule(ScheduleDto schedule)
    {
        if(schedule != null && schedule.JobNumber != null)
        {
            JobNumber = schedule.JobNumber;
        }
        if (JobNumber != null)
        {
            SubdivisionJobPickService.JobNumber = JobNumber;
            NavManager.NavigateTo($"schedule");
            StateHasChanged();
        }
    }
    private void OpenAssignSticks()
    {
        AssignSticksModal.Show();
        StateHasChanged();
    }
    private async void HandleAssignSticksSubmit(ScheduleDto responseSticks)
    {
        AssignSticksModal.Hide();
        var schedulesTask = ReleaseScheduleService.GetReleaseSchedules();
        await Task.WhenAll(new Task[] { schedulesTask });
        Schedules = schedulesTask.Result.Value;
        StateHasChanged();
    }
    private void NewScheduleFromTemplate()
    {
        AddScheduleFromTemplateModal.Show();
        StateHasChanged();
    }
    private async void HandleValidAddScheduleFromTemplateSubmit(ScheduleDto responseSchedule)
    {
        AddScheduleFromTemplateModal.Hide();
        var schedulesTask = ReleaseScheduleService.GetReleaseSchedules();
        await Task.WhenAll(new Task[] { schedulesTask });
        Schedules = schedulesTask.Result.Value;
        StateHasChanged();
    }
    private async Task OnExcelBeforeExport(GridBeforeExcelExportEventArgs args)
    {
        args.Columns[0].Width = "100px"; //jobnumber
        args.Columns[1].Width = "200px"; // subdivision
        args.Columns[2].Width = "100px"; // stick
        args.Columns[3].Width = "150px"; // template
        args.Columns[4].Width = "50px"; // publish
        args.Columns[5].Width = "100px"; // permit submit
        args.Columns[6].Width = "100px"; // permit received
        args.Columns[7].Width = "100px"; // permit number
        args.Columns[8].Width = "50px"; // released
        args.Columns[9].Width = "100px"; // Initial start
        args.Columns[10].Width = "100px";// Initial end
        args.Columns[11].Width = "100px"; // duration

        args.Columns[5].NumberFormat = BuiltInNumberFormats.GetShortDate(); // permit submit
        args.Columns[6].NumberFormat = BuiltInNumberFormats.GetShortDate(); // permit received
        args.Columns[9].NumberFormat = BuiltInNumberFormats.GetShortDate(); // IniSchStartDate
        args.Columns[10].NumberFormat = BuiltInNumberFormats.GetShortDate(); // IniSchEndDate
    }

    async Task OnReleaseScheduleExcelSelectHandler(FileSelectEventArgs args)
    {
        IsLoading = true;

        if (args.Files.Count() > 1)
        {
            args.IsCancelled = true;
            ShowSuccessOrErrorNotification("Too many files. You can only upload one at a time.", false);
            return;
        }
        foreach (var file in args.Files)
        {
            var ImportedSchedules = (await ReleaseScheduleService.ImportReleaseSchedulesExcel(file)).Value;
            var importSaveResponse = await ReleaseScheduleService.SaveReleaseSchedulesImportAsync(ImportedSchedules);
            if (importSaveResponse.IsSuccess)
            {
                Schedules = (await ReleaseScheduleService.GetReleaseSchedules()).Value;
                GridRef.Rebind();
            }
            ShowSuccessOrErrorNotification(importSaveResponse.Message, importSaveResponse.IsSuccess);
        }
        IsLoading = false;
    }

    private async Task EditSchedule(GridCommandEventArgs args)
    {
        var item = args.Item as JobDto;
        AllSticksInSubdivision = new List<string> { "Add New" };
        NewStickName = "";
        //preselect the subdivision containing the current lot for apply contacts other lots
        var jobsInSubdivision = (await SubdivisionService.GetJobsAsync((int)item.SubdivisionId)).Value;
        AllSticksInSubdivision.AddRange(jobsInSubdivision.Where(x => !string.IsNullOrWhiteSpace(x.StickBuilingNum)).Select(x => x.StickBuilingNum).Distinct().ToList());

        if (item.JobSchedule != null)
        {
            item.JobSchedule.IniSchStartDateInputRaw = item.JobSchedule.IniSchStartDate?.ToString("MM/dd/yyyy") ?? "";
            item.JobSchedule.PermitReceivedDateInputRaw = item.JobSchedule.PermitReceivedDate?.ToString("MM/dd/yyyy") ?? "";
            item.JobSchedule.PermitSubmitDateInputRaw = item.JobSchedule.PermitSubmitDate?.ToString("MM/dd/yyyy") ?? "";
        }
    }

    private async Task UpdateSchedule(GridCommandEventArgs args)
    {
        var item = args.Item as JobDto;

        if (item?.JobSchedule != null && 
            (!string.IsNullOrEmpty(item.JobSchedule.IniSchStartDateInputRaw) && !IsValidDate(item.JobSchedule.IniSchStartDateInputRaw, out DateTime iniSchStartDate)
            || (!string.IsNullOrEmpty(item.JobSchedule.PermitReceivedDateInputRaw) && !IsValidDate(item.JobSchedule.PermitReceivedDateInputRaw, out DateTime permitReceivedDate))
            || (!string.IsNullOrEmpty(item.JobSchedule.PermitReceivedDateInputRaw) && !IsValidDate(item.JobSchedule.PermitSubmitDateInputRaw, out DateTime permitSubmitDate))))
        {
            ShowSuccessOrErrorNotification("Invalid date format. Please use MM/DD/YYYY.", false);
            return;
        }
        
        item.StickBuilingNum = item.StickBuilingNum == "Add New" ? NewStickName : item.StickBuilingNum;

        //TODO: it won't save stick if no schedule is present
        SelectedSchedule = item;
        if (item.JobSchedule != null)
        {
            item.JobSchedule.StickBuildingNum = item.StickBuilingNum;
            if (item.JobSchedule.JobNumberNavigation == null)
            {
                item.JobSchedule.JobNumberNavigation =  new JobDto(){JobNumber = item.JobNumber};
            }
            item.JobSchedule.JobNumberNavigation.StickBuilingNum = item.StickBuilingNum;
            ResponseModel<List<ScheduleDto>> response;
            if (args.Field == "JobSchedule.IniSchStartDate")
            {
                response = await ScheduleService.UpdatePrescheduleStickAsync(item.JobSchedule);
            }
            else
            {
                response = await ScheduleService.UpdatePrescheduleAsync(item.JobSchedule);
            }
            // var schedulesTask = ReleaseScheduleService.GetReleaseSchedules();
            // await Task.WhenAll(new Task[] { schedulesTask });
            // Schedules = schedulesTask.Result.Value;
            // AllSchedules = schedulesTask.Result.Value;
            //item.JobSchedule = response.Value;

            UpdateScheduleInList(Schedules, response.Value);
            UpdateScheduleInList(AllSchedules, response.Value);
            if (SelectedSubdivisionId == 0)//"All" = clear the filter
            {
                if (SelectedYear != null)
                {
                    var yearAsDate = new DateTime((int)SelectedYear, 1, 1);
                    var nextYear = yearAsDate.AddYears(1);
                    Schedules = AllSchedules.Where(x => x.JobSchedule.IniSchStartDate >= yearAsDate && x.JobSchedule.IniSchStartDate < nextYear).ToList();
                }
                else
                {
                    Schedules = AllSchedules;
                }
            }
            else
            {
                if (SelectedYear != null)
                {
                    var yearAsDate = new DateTime((int)SelectedYear, 1, 1);
                    var nextYear = yearAsDate.AddYears(1);
                    Schedules = AllSchedules.Where(x => x.JobSchedule.IniSchStartDate >= yearAsDate && x.JobSchedule.IniSchStartDate < nextYear && x.SubdivisionId == SelectedSubdivisionId).ToList();
                }
                else
                {
                    Schedules = AllSchedules.Where(x => x.SubdivisionId == SelectedSubdivisionId).ToList();
                }
            }
            ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);

            if (IsSorted)
            {
                SortByColumn("JobSchedule.OldIniSchStartDate");
                SortByColumn("JobSchedule.OldIniSchStartDate");
            }

            StateHasChanged();
        }
    }

    public static bool IsValidDate(string input, out DateTime parsedDate)
    {
        return DateTime.TryParseExact(
            input,
            "MM/dd/yyyy",
            System.Globalization.CultureInfo.InvariantCulture,
            System.Globalization.DateTimeStyles.None,
            out parsedDate
        );
    }

    private void UpdateScheduleInList(List<JobDto> scheduleList, List<ScheduleDto> updatedSchedules)
    {
        foreach (var updateSchedule in updatedSchedules)
        {
            var existing = scheduleList.FirstOrDefault(s => s.JobNumber == updateSchedule.JobNumber);
            if (existing != null)
            {
                existing.JobSchedule = updateSchedule;
            }
        }        
    }

    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
    private async Task HandleAssignSticksSubmit(ResponseModel? response)
    {
        var schedulesTask = ReleaseScheduleService.GetReleaseSchedules();
        await Task.WhenAll(new Task[] { schedulesTask });
        Schedules = schedulesTask.Result.Value;
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        StateHasChanged();
    }


    async Task OnStateInitHandler(GridStateEventArgs<JobDto> args)
    {
        try
        {
            var state = await LocalStorage.GetItem<GridState<JobDto>>(UniqueStorageKey);

            if (state != null)
            {
                // state.FilterDescriptors = new FilterDescriptorCollection();
                // state.ColumnStates = null;
                state.EditItem = null;
                state.EditField = null;
                state.SortDescriptors = null;//This was causing errors, probably a sort was saved on a column that no longer exists or something
                args.GridState = state;
            }
        }
        catch (InvalidOperationException ex)
        {
            // JSInterop will fail on 1st render, so doing this will prevent it after re-render
            var test = ex.Message;
        }
    }

    private void SortByColumn(string fieldName)
    {
        IsSorted = true;
        SortAscending = !SortAscending;

        Schedules = (SortAscending
            ? Schedules.OrderBy(x => GetPropertyValue(x, fieldName))
            : Schedules.OrderByDescending(x => GetPropertyValue(x, fieldName)))
            .ToList();
    }

    private object GetPropertyValue(object obj, string prop)
    {
        var props = prop.Split('.');
        foreach (var p in props)
        {
            if (obj == null) return null;
            var pi = obj.GetType().GetProperty(p);
            obj = pi?.GetValue(obj);
        }
        return obj;
    }

    private async Task ResetState()
    {
        // clean up the storage
        await LocalStorage.RemoveItem(UniqueStorageKey);
        await GridRef.SetStateAsync(null); // pass null to reset the state
    }

    private void ReloadPage()
    {
        JSRuntime.InvokeVoidAsync("window.location.reload");
    }

    private async Task SetState()
    {
        var state = GridRef.GetState();
        state.ExpandedItems = null; // Set not to expand, otherwise it will overload localstorage and exceed quota
        await LocalStorage.SetItem(UniqueStorageKey, state);
    }
    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= SubdivisionSelectedHandler;
    }
}
