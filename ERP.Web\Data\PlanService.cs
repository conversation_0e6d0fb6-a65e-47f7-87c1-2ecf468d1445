﻿using Azure.Core;
using DocumentFormat.OpenXml.Spreadsheet;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Pages;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using NLog;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class PlanService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public PlanService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<ResponseModel<List<PhasePlanModel>>> GetPhasePlansAsync(int subdivisionId)
        {
            var plans = new List<PhasePlanModel>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/plan/getphaseplans/{subdivisionId}");
                var responseString = await response.Content.ReadAsStringAsync();

                var result = JsonConvert.DeserializeObject<ResponseModel<List<PhasePlanModel>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes or something
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PhasePlanModel>>() { Value = plans, IsSuccess = false, Message = "Failed to get Plan Types data" };
        }
        public async Task<ResponseModel<List<PhasePlanDto>>> GetPhasePlansInSubdivisionAsync(int subdivisionId)
        {
            var plans = new List<PhasePlanDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/plan/getphaseplansinsubdivision/{subdivisionId}");
                var responseString = await response.Content.ReadAsStringAsync();

                var result = JsonConvert.DeserializeObject<ResponseModel<List<PhasePlanDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes or something
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PhasePlanDto>>() { Value = plans, IsSuccess = false, Message = "Failed to get Plan Types data" };
        }
        public async Task<ResponseModel<List<AvailablePlanOptionDto>>> GetElevationsInPlanAsync(int phasePlanId)
        {
            var plans = new List<AvailablePlanOptionDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/plan/getelevationsinplan/{phasePlanId}");
                var responseString = await response.Content.ReadAsStringAsync();

                var result = JsonConvert.DeserializeObject<ResponseModel<List<AvailablePlanOptionDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes or something
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<AvailablePlanOptionDto>>() { Value = plans, IsSuccess = false, Message = "Failed to get Plan Types data" };
        }
        public async Task<ResponseModel<List<PlanTypeDto>>> GetPlanTypesAsync()
        {
            var plans = new List<PlanTypeDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", x => x.RelativePath = $"api/plan/getplantypes");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<PlanTypeDto>>>(responseString);
                    return result; 
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes or something
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PlanTypeDto>>() { Value = plans, IsSuccess = false, Message = "Failed to get Plan Types data" };
        }

        public async Task<ResponseModel<int?>> DeletePhasePlanAsync(int phasePlanId)
        {
            var plan = new PhasePlanModel();
            try
            {
                plan = await _downstreamAPI.GetForUserAsync<PhasePlanModel>(
                      "DownstreamApi",
                      options => options.RelativePath = $"api/plan/DeletePlanFromSubdivision/{phasePlanId}");
                return new ResponseModel<int?>() { Value = phasePlanId, IsSuccess = true, Message = "Deleted Plan from Subdivision Success" };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<int?>() { Value = null, IsSuccess = false, Message = "Failed to Delete Plan from Subdivision" };
        }
        public async Task<ResponseModel<PhasePlanModel>> UpdatePhasePlanAsync(PhasePlanModel planToUpdate)
        {
            var responsePlan = new PhasePlanModel();
            try
            {
                responsePlan = await _downstreamAPI.PutForUserAsync<PhasePlanModel, PhasePlanModel>(
                    "DownstreamApi", planToUpdate,
                    options => {
                        options.RelativePath = "api/plan/UpdatePhasePlan/";
                    });
                return new ResponseModel<PhasePlanModel>() { Value = planToUpdate, IsSuccess = true, Message = "Successfully Updated Plan" };

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<PhasePlanModel>() { Value = null, IsSuccess = false, Message = "Failed to Update Plan" };
        }

        public async Task<ResponseModel> AddPhasePlanToSubdivisionAsync(AddPlanToSubdivisionPhaseModel addPlan)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<AddPlanToSubdivisionPhaseModel, ResponseModel>(
                           "DownstreamApi", addPlan,
                            options => {
                                options.RelativePath = "api/plan/addphaseplantosubdivision/";
                            });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to login or consent to scopes
                return new ResponseModel() { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel() { IsSuccess = false, Message = ex.Message };
            }

        }
        public async Task<ResponseModel> AddPhasePlansToSubdivisionAsync(AddPlanToSubdivisionPhaseModel addPlan)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<AddPlanToSubdivisionPhaseModel, ResponseModel>(
                           "DownstreamApi", addPlan,
                            options => {
                                options.RelativePath = "api/plan/addphaseplanstosubdivision/";
                            });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to login or consent to scopes
                return new ResponseModel() { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel() { IsSuccess = false, Message = ex.Message };
            }

        }
        public async Task<ResponseModel> AddMasterPlanToSubdivisionAsync(AddPlanToSubdivisionPhaseModel addPlan)
        {
            try
            {

                var response = await _downstreamAPI.PostForUserAsync<AddPlanToSubdivisionPhaseModel, ResponseModel>(
                            "DownstreamApi", addPlan,
                             options => {
                                 options.RelativePath = "api/plan/addmasterplantosubdivision/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes
                return new ResponseModel() { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel() { IsSuccess = false, Message = ex.Message };
            }

        }
        public async Task<ResponseModel> AddMasterPlansToSubdivisionAsync(AddPlanToSubdivisionPhaseModel addPlan)
        {
            try
            {

                var response = await _downstreamAPI.PostForUserAsync<AddPlanToSubdivisionPhaseModel, ResponseModel>(
                            "DownstreamApi", addPlan,
                             options => {
                                 options.RelativePath = "api/plan/addmasterplanstosubdivision/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes
                return new ResponseModel() { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel() { IsSuccess = false, Message = ex.Message };
            }

        }

        public async Task<ResponseModel<List<MasterPlanDto>>> GetMasterPlansAsync()
        {
            var plans = new List<MasterPlanDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                  options => options.RelativePath = $"api/plan/getmasterplans/");
                // var response = await _httpClient.GetAsync($"/api/option/getmasterplans/");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<MasterPlanDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes or something
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<MasterPlanDto>>() { Value = plans, IsSuccess = false, Message = "Failed to get Master Plans" };
        }
        public async Task<ResponseModel<List<MasterPlanDto>>> GetMasterPlansInSubdivisionAsync(int subdivisionId)
        {
            var plans = new List<MasterPlanDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                  options => options.RelativePath = $"api/plan/getmasterplansinsubdivision/{subdivisionId}");
                // var response = await _httpClient.GetAsync($"/api/option/getmasterplans/");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<MasterPlanDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes or something
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<MasterPlanDto>>() { Value = plans, IsSuccess = false, Message = "Failed to get Master Plans" };
        }

        public async Task<ResponseModel<List<MasterPlanDto>>> GetMasterPlansExceptAsync(int masterPlanId)
        {
            var plans = new List<MasterPlanDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                  options => options.RelativePath = $"api/plan/getmasterplansexcept/{masterPlanId}");
                // var response = await _httpClient.GetAsync($"/api/option/getmasterplans/");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<MasterPlanDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes or something
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<MasterPlanDto>>() { Value = plans, IsSuccess = false, Message = "Failed to get Master Plans" };
        }

        public async Task<ResponseModel<List<SubdivisionPlanModel>>> GetPhasePlansAsync()
        {
            var plans = new List<SubdivisionPlanModel>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                    options => options.RelativePath = $"api/plan/getphaseplans/");
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ResponseModel<List<SubdivisionPlanModel>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to login or consent to scopes
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<SubdivisionPlanModel>>() { Value = plans, IsSuccess = false, Message = "Failed to get Phase Plans" };
        }

        public async Task<ResponseModel<MasterPlanDto>> UpdateMasterPlanAsync(MasterPlanDto Plan)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterPlanDto, ResponseModel<MasterPlanDto>>(
                            "DownstreamApi", Plan,
                             options => {
                                 options.RelativePath = "api/plan/updatemasterplan/";
                                // options.HttpMethod = HttpMethod.Put;
                             });

                //var jsonData = JsonConvert.SerializeObject(Plan);
                //var stringContent = new StringContent(jsonData);
                //var response2 = await _downstreamAPI.CallApiForUserAsync(
                //           "DownstreamApi", options => {
                //                options.RelativePath = "api/plan/updatemasterplan/";
                //                options.HttpMethod = HttpMethod.Put;
                //            }, content: stringContent);
                //if (response2.IsSuccessStatusCode)
                //{

                //}

                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to login or consent to scopes
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<MasterPlanDto>() { Value = Plan, IsSuccess = false, Message = "Failed to Update Master Plan" };
        }

        public async Task<ResponseModel<MasterPlanDto>> DeleteMasterPlanAsync(MasterPlanDto Plan)
        {
            var plan = new MasterPlanDto();
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterPlanDto, MasterPlanDto>(
                            "DownstreamApi", Plan,
                             options => {
                                 options.RelativePath = "api/plan/deletemasterplan/";
                             });

                return new ResponseModel<MasterPlanDto>() { Value = response, IsSuccess = true, Message = "Successfully Deleted Master Plan" };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to login or consent to scopes
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<MasterPlanDto>() { Value = plan, IsSuccess = false, Message = "Failed to Delete Master Plan" };
        }

        public async Task<ResponseModel<MasterPlanDto>> AddMasterPlanAsync(MasterPlanDto Plan)
        {
            var plan = new MasterPlanDto();

            try
            {
                var response = await _downstreamAPI.PostForUserAsync<MasterPlanDto, ResponseModel<MasterPlanDto>>(
                            "DownstreamApi", Plan,
                             options => {
                                 options.RelativePath = "api/plan/addmasterplan/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes or something
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<MasterPlanDto>() { Value = plan, IsSuccess = false, Message = "Failed to Add Master Plan" };
        }

        public async Task<ResponseModel<MasterPlanDto>> CopyMasterPlanAsync(MasterPlanDto Plan)
        {
            //the Plan needs to have the Id of the plan to copy from, but the new name and details
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<MasterPlanDto, ResponseModel<MasterPlanDto>>(
                            "DownstreamApi", Plan,
                             options => {
                                 options.RelativePath = "api/plan/copymasterplan/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to consent to scopes or something
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<MasterPlanDto>() { Value = Plan, IsSuccess = false, Message = "Failed to Copy Master Plan.Contact BI team if issue persists" };
        }

        public async Task<ResponseModel<List<SubdivisionDto>>> GetAvailablePlanOptionInSubdivisionAsync(int planId)
        {
            var subdivisions = new ResponseModel<List<SubdivisionDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                               options => options.RelativePath = $"api/plan/getavailableplanoptioninsubdivision/{planId}");
                var responseString = await response.Content.ReadAsStringAsync();
                subdivisions = JsonConvert.DeserializeObject<ResponseModel<List<SubdivisionDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif          
            }
            return subdivisions;
        }

        public async Task<ResponseModel<List<AvailablePlanOptionDto>>> GetAvailableOptionByPlanAndSubdivisionAsync(int planId, int subdivisionId)
        {
            var options = new ResponseModel<List<AvailablePlanOptionDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", 
                               options => options.RelativePath = $"api/plan/getavailableoptionbyplanandsubdivision/{planId}/{subdivisionId}");
                var responseString = await response.Content.ReadAsStringAsync();
                options = JsonConvert.DeserializeObject<ResponseModel<List<AvailablePlanOptionDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif          
            }
            return options;
        }

        public async Task<ResponseModel<List<OptionAttributeGroupItemDto>>> AddOptionAttributeGroupItemAsync(List<OptionAttributeGroupItemDto> items)
        {
            var responseItem = new ResponseModel<List<OptionAttributeGroupItemDto>>();

            try
            {
                responseItem = await _downstreamAPI.PostForUserAsync<List<OptionAttributeGroupItemDto>, ResponseModel<List<OptionAttributeGroupItemDto>>>(
                    "DownstreamApi", items,
                    options => {
                        options.RelativePath = "api/plan/addoptionattributegroupitem/";
                    });
                return new ResponseModel<List<OptionAttributeGroupItemDto>>() { Value = responseItem.Value ?? items, IsSuccess = true, Message = responseItem.Message };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<OptionAttributeGroupItemDto>>() { Value = items, IsSuccess = false };
        }

        public async Task<ResponseModel<List<OptionAttributeGroupItemDto>>> GetPlanWithGroupsAsync(int planOptionId)
        {
            var options = new ResponseModel<List<OptionAttributeGroupItemDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                               options => options.RelativePath = $"api/plan/getplanwithgroups/{planOptionId}");
                var responseString = await response.Content.ReadAsStringAsync();
                options = JsonConvert.DeserializeObject<ResponseModel<List<OptionAttributeGroupItemDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif        
            }
            return options;
        }

        public async Task<ResponseModel<OptionAttributeGroupItemDto>> DeleteOptionAttributeGroupItemAsync(OptionAttributeGroupItemDto model)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<OptionAttributeGroupItemDto, OptionAttributeGroupItemDto>(
                            "DownstreamApi", model,
                             options =>
                             {
                                 options.RelativePath = "api/plan/deleteoptionattributegroupitem/";
                             });

                return new ResponseModel<OptionAttributeGroupItemDto>() { IsSuccess = true, Value = response, Message = "Success" };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);//in case need to login or consent to scopes
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<OptionAttributeGroupItemDto>() { IsSuccess = false, Value = null, Message = "Sorry, there was an error." };
        }
    }
}
