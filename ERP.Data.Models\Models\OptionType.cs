﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class OptionType
{
    public int OptionTypeId { get; set; }

    public string OptionType1 { get; set; } = null!;

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStampe { get; set; } = null!;

    public virtual ICollection<AvailablePlanOption> AvailablePlanOptions { get; set; } = new List<AvailablePlanOption>();

    public virtual ICollection<TbBuiltOption> TbBuiltOptions { get; set; } = new List<TbBuiltOption>();
}
