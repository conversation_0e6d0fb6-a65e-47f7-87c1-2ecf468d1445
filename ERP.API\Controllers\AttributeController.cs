﻿
using AutoMapper;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Azure;
using Microsoft.Graph;
using Microsoft.Graph.Models.Security;
using NLog;
using System.Data;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class AttributeController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        private readonly GraphServiceClient _graphServiceClient;
        public AttributeController(IConfiguration configuration, ErpDevContext context, IMapper mapper, GraphServiceClient graphClient)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _graphServiceClient = graphClient;
        }

        [HttpGet]
        public async Task<IActionResult> GetAttributeGroupItemsAsync()
        {
            try
            {
                // TODO: find out oddity within IsActive
                var getMasterAttributeGroup = new List<MasterAttributeGroupDto>();
                var returnData = new List<MasterAttributeGroupDto>();

                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT DISTINCT ATTRIBUTE_GROUP_ID, DESCRIPTION FROM dbo.MASTER_ATTRIBUTE_GROUP ORDER BY 2";
                    var command = new SqlCommand(query, connection);

                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        getMasterAttributeGroup.Add(new MasterAttributeGroupDto
                        {
                            AttributeGroupId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                            Description = (reader.GetValue(1) != DBNull.Value) ? reader.GetValue(1).ToString() : string.Empty
                        });
                    }
                }

                if (getMasterAttributeGroup.Count > 0)
                {
                    foreach (var data in getMasterAttributeGroup)
                    {
                        var buildMasterAttributeGroup = new MasterAttributeGroupDto
                        {
                            AttributeGroupId = data.AttributeGroupId,
                            Description = data.Description.Trim(),
                            MasterAttributeItems = new List<MasterAttributeItemDto>()
                        };

                        if (data.AttributeGroupId != 0)
                        {
                            using (var connection = new SqlConnection(conn))
                            {
                                await connection.OpenAsync();

                                var query = "SELECT aga.ATTR_GROUP_ASSIGNMENT_ID, aga.ATTRIBUTE_GROUP_ID, aga.ATTRIBUTE_ITEM_ID, mag.DESCRIPTION, mai.DESCRIPTION " +
                                    "FROM dbo.ATTR_GROUP_ASSIGNMENT aga LEFT JOIN dbo.MASTER_ATTRIBUTE_GROUP mag ON aga.ATTRIBUTE_GROUP_ID = mag.ATTRIBUTE_GROUP_ID " +
                                    "LEFT JOIN dbo.MASTER_ATTRIBUTE_ITEM mai ON aga.ATTRIBUTE_ITEM_ID = mai.ATTRIBUTE_ITEM_ID" +
                                    " WHERE mag.ATTRIBUTE_GROUP_ID = @AttributeGroupId ORDER BY 4,5";
                                var command = new SqlCommand(query, connection);
                                command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = data.AttributeGroupId;

                                var reader = command.ExecuteReader();

                                while (reader.Read())
                                {
                                    buildMasterAttributeGroup.MasterAttributeItems.Add(new MasterAttributeItemDto
                                    {
                                        AttributeItemId = (reader.GetValue(2) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(2).ToString()) : 0,
                                        Description = (reader.GetValue(4) != DBNull.Value) ? reader.GetValue(4).ToString() : string.Empty,
                                        AttributeGroupAssignmentId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                                        AttributeGroupId = (reader.GetValue(1) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(1).ToString()) : 0
                                    });
                                }
                            }
                        }

                        if (buildMasterAttributeGroup.MasterAttributeItems.Count > 0)
                        {
                            returnData.Add(buildMasterAttributeGroup);
                        }
                    }

                }

                return new OkObjectResult(new ResponseModel<List<MasterAttributeGroupDto>> { Value = returnData.OrderBy(x => x.Description).ToList(), IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Attribute Group Item data"));
            }
        }

        /// <summary>
        /// This is to populate data based on MasterOptionsDDL selection
        /// </summary>
        /// <param name="MasterOptionId"></param>
        /// <returns></returns>
        [HttpGet("{MasterOptionId}")]
        public async Task<IActionResult> GetOptionAttributeGroupItemsAsync(int MasterOptionId)
        {
            try
            {
                var returnData = new List<MasterOptionDto>();
                var getMasterOptionAttributeGroup = new List<MasterOptionDto>();

                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT DISTINCT mo.OPTION_ID, mo.OPTION_CODE, mo.OPTION_DESC FROM [dbo].[OPTION_ATTRIBUTE_GROUP_ITEM] oagi RIGHT JOIN dbo.MASTER_OPTION mo ON oagi.MASTER_OPTION_ID = mo.OPTION_ID WHERE mo.OPTION_ID = @MasterOptionId ORDER BY 2,3";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@MasterOptionId", SqlDbType.Int).Value = MasterOptionId;

                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        getMasterOptionAttributeGroup.Add(new MasterOptionDto
                        {
                            OptionId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                            OptionCode = (reader.GetValue(1) != DBNull.Value) ? reader.GetValue(1).ToString() : string.Empty,
                            OptionDesc = (reader.GetValue(2) != DBNull.Value) ? reader.GetValue(2).ToString() : string.Empty,
                        });
                    }
                }

                // Get Attribute Assignments (child for Option's Attributes)
                if (getMasterOptionAttributeGroup.Count > 0)
                {
                    foreach (var data in getMasterOptionAttributeGroup)
                    {
                        var buildOptionAttributeGroup = new MasterOptionDto
                        {
                            OptionId = data.OptionId,
                            MasterAttributeGroups = new List<MasterAttributeGroupDto>(),
                            OptionCode = data.OptionCode,
                            OptionDesc = data.OptionDesc,
                        };

                        using (var connection = new SqlConnection(conn))
                        {
                            await connection.OpenAsync();

                            var query = "SELECT DISTINCT a.ATTRIBUTE_GROUP_ID, c.DESCRIPTION FROM dbo.ATTR_GROUP_ASSIGNMENT a JOIN dbo.OPTION_ATTRIBUTE_GROUP_ITEM b ON a.ATTR_GROUP_ASSIGNMENT_ID = b.ATTR_GROUP_ASSIGNMENT_ID JOIN dbo.MASTER_ATTRIBUTE_GROUP c ON a.ATTRIBUTE_GROUP_ID = c.ATTRIBUTE_GROUP_ID WHERE b.MASTER_OPTION_ID = @MasterOptionId ORDER BY 2";
                            var command = new SqlCommand(query, connection);
                            command.Parameters.Add("@MasterOptionId", SqlDbType.Int).Value = MasterOptionId;

                            var reader = command.ExecuteReader();

                            while (reader.Read())
                            {
                                buildOptionAttributeGroup.MasterAttributeGroups.Add(new MasterAttributeGroupDto
                                {
                                    AttributeGroupId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                                    Description = (reader.GetValue(1) != DBNull.Value) ? reader.GetValue(1).ToString() : string.Empty
                                });
                            }
                        }

                        if (buildOptionAttributeGroup.MasterAttributeGroups.Count > 0)
                        {
                            var buildMasterAttributeItemData = new List<MasterAttributeItemDto>();

                            foreach (var item in buildOptionAttributeGroup.MasterAttributeGroups)
                            {
                                using (var connection = new SqlConnection(conn))
                                {
                                    await connection.OpenAsync();

                                    var query = "SELECT a.ATTR_GROUP_ASSIGNMENT_ID, a.ATTRIBUTE_GROUP_ID, a.ATTRIBUTE_ITEM_ID, c.DESCRIPTION FROM dbo.ATTR_GROUP_ASSIGNMENT a JOIN dbo.OPTION_ATTRIBUTE_GROUP_ITEM b ON a.ATTR_GROUP_ASSIGNMENT_ID = b.ATTR_GROUP_ASSIGNMENT_ID JOIN dbo.MASTER_ATTRIBUTE_ITEM c ON a.ATTRIBUTE_ITEM_ID = c.ATTRIBUTE_ITEM_ID WHERE b.MASTER_OPTION_ID = @MasterOptionId AND a.ATTRIBUTE_GROUP_ID = @AttributeGroupId ORDER BY 4";

                                    var command = new SqlCommand(query, connection);
                                    command.Parameters.Add("@MasterOptionId", SqlDbType.Int).Value = MasterOptionId;
                                    command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = item.AttributeGroupId;

                                    var reader = command.ExecuteReader();
                                    while (reader.Read())
                                    {
                                        buildMasterAttributeItemData.Add(new MasterAttributeItemDto
                                        {
                                            AttributeGroupAssignmentId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                                            AttributeGroupId = (reader.GetValue(1) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(1).ToString()) : 0,
                                            AttributeItemId = (reader.GetValue(2) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(2).ToString()) : 0,
                                            Description = (reader.GetValue(3) != DBNull.Value) ? reader.GetValue(3).ToString() : string.Empty
                                        });
                                    }
                                }
                            }

                            if (buildMasterAttributeItemData.Count > 0)
                            {
                                foreach (var groupData in buildOptionAttributeGroup.MasterAttributeGroups)
                                {
                                    groupData.MasterAttributeItems = new List<MasterAttributeItemDto>();

                                    foreach (var itemData in buildMasterAttributeItemData)
                                    {
                                        if (groupData.AttributeGroupId == itemData.AttributeGroupId)
                                        {
                                            groupData.MasterAttributeItems.Add(new MasterAttributeItemDto
                                            {
                                                AttributeGroupAssignmentId = itemData.AttributeGroupAssignmentId,
                                                AttributeGroupId = itemData.AttributeGroupId,
                                                AttributeItemId = itemData.AttributeItemId,
                                                Description = itemData.Description,
                                            });
                                        }
                                    }
                                }
                            }
                        }

                        returnData.Add(buildOptionAttributeGroup);
                    }
                }

                return new OkObjectResult(new ResponseModel<List<MasterOptionDto>> { Value = returnData.OrderBy(x => x.OptionCode).ToList(), IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Option Attribute Group Item data"));
            }
        }

        [HttpGet("{MasterOptionId}")]
        public async Task<IActionResult> GetGroupByMasterOptionIdAsync(int MasterOptionId)
        {
            try
            {
                var returnData = new List<MasterAttributeGroupDto>();

                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    // Getting active assignment from OPTION_ATTRIBUTE_GROUP_ITEM. Any non-active ATTR_GROUP_ASSIGNMENT will not be available/shown.
                    //var query = "SELECT DISTINCT a.ATTRIBUTE_GROUP_ID, c.DESCRIPTION, b.PLAN_OPTION_ID, b.PLAN_NUM, f.SUBDIVISION_NAME FROM dbo.ATTR_GROUP_ASSIGNMENT a JOIN dbo.OPTION_ATTRIBUTE_GROUP_ITEM b ON a.ATTR_GROUP_ASSIGNMENT_ID = b.ATTR_GROUP_ASSIGNMENT_ID JOIN dbo.MASTER_ATTRIBUTE_GROUP c ON a.ATTRIBUTE_GROUP_ID = c.ATTRIBUTE_GROUP_ID JOIN dbo.AVAILABLE_PLAN_OPTION d on b.PLAN_OPTION_ID = d.PLAN_OPTION_ID JOIN dbo.PHASE_PLAN e on d.PHASE_PLAN_ID = e.PHASE_PLAN_ID JOIN dbo.SUBDIVISION f on e.SUBDIVISION_ID = f.SUBDIVISION_ID WHERE b.MASTER_OPTION_ID = @MasterOptionId and d.IsActive = 1 and e.IsActive = 1 and f.IsActive = 1 ORDER BY 5, 2, 4;";

                    var query = "SELECT DISTINCT a.ATTRIBUTE_GROUP_ID, c.DESCRIPTION, d.PLAN_OPTION_ID, b.PLAN_NUM, f.SUBDIVISION_NAME FROM dbo.ATTR_GROUP_ASSIGNMENT a JOIN dbo.OPTION_ATTRIBUTE_GROUP_ITEM b ON a.ATTR_GROUP_ASSIGNMENT_ID = b.ATTR_GROUP_ASSIGNMENT_ID JOIN dbo.MASTER_ATTRIBUTE_GROUP c ON a.ATTRIBUTE_GROUP_ID = c.ATTRIBUTE_GROUP_ID RIGHT JOIN dbo.AVAILABLE_PLAN_OPTION d on b.PLAN_OPTION_ID = d.PLAN_OPTION_ID JOIN dbo.PHASE_PLAN e on d.PHASE_PLAN_ID = e.PHASE_PLAN_ID JOIN dbo.SUBDIVISION f on e.SUBDIVISION_ID = f.SUBDIVISION_ID WHERE d.MASTER_OPTION_ID = @MasterOptionId and d.IsActive = 1 and e.IsActive = 1 and f.IsActive = 1 ORDER BY 5, 2, 4;";

                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@MasterOptionId", SqlDbType.Int).Value = MasterOptionId;

                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        returnData.Add(new MasterAttributeGroupDto
                        {
                            AttributeGroupId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                            Description = (reader.GetValue(1) != DBNull.Value) ? reader.GetValue(1).ToString() : string.Empty,
                            PlanOptionId = (reader.GetValue(2) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(2).ToString()) : 0,
                            SubdivisionName = (reader.GetValue(4) != DBNull.Value) ? reader.GetValue(4).ToString() : string.Empty,
                            PlanNum = (reader.GetValue(3) != DBNull.Value) ? reader.GetValue(3).ToString() : string.Empty,
                            MasterOptionId = MasterOptionId
                        });
                    }
                }

                return new OkObjectResult(new ResponseModel<List<MasterAttributeGroupDto>> { Value = returnData.OrderBy(x => x.Description).ToList(), IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Option Attribute Group Item data"));
            }
        }

        /// <summary>
        /// Populate Master Options for possible assignments
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetDistinctMasterOptionsInAttributeAsync()
        {
            try
            {
                var returnData = new List<MasterOptionDto>();
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT DISTINCT b.OPTION_ID, b.OPTION_CODE, b.OPTION_DESC FROM [dbo].[OPTION_ATTRIBUTE_GROUP_ITEM] a RIGHT JOIN dbo.MASTER_OPTION b on a.MASTER_OPTION_ID = b.OPTION_ID WHERE b.IsActive = 1 ORDER BY 1";
                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        returnData.Add(new MasterOptionDto
                        {
                            OptionId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                            OptionCode = (reader.GetValue(1) != DBNull.Value) ? reader.GetValue(1).ToString() : string.Empty,
                            OptionDesc = (reader.GetValue(2) != DBNull.Value) ? reader.GetValue(2).ToString() : string.Empty,
                            DisplayText = (reader.GetValue(1) != DBNull.Value && reader.GetValue(2) != DBNull.Value) ? string.Format("{0} - {1}", reader.GetValue(1).ToString(), reader.GetValue(2).ToString()) : string.Empty
                        });
                    }
                }

                return new OkObjectResult(new ResponseModel<List<MasterOptionDto>> { Value = returnData, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Master Options data"));
            }
        }

        [HttpGet("{AttributeGroupAssignmentId}")]
        public async Task<IActionResult> GetMasterOptionsFromAttributeGroupAssignmentAsync(int AttributeGroupAssignmentId)
        {
            try
            {
                var returnData = new List<MasterOptionDto>();

                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT DISTINCT b.OPTION_CODE, b.OPTION_DESC, b.OPTION_ID FROM [dbo].[OPTION_ATTRIBUTE_GROUP_ITEM] a JOIN [dbo].[MASTER_OPTION] b ON a.MASTER_OPTION_ID = b.OPTION_ID WHERE a.ATTR_GROUP_ASSIGNMENT_ID = @AttributeGroupAssignmentId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@AttributeGroupAssignmentId", SqlDbType.Int).Value = AttributeGroupAssignmentId;

                    var reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        returnData.Add(new MasterOptionDto
                        {
                            OptionCode = (reader.GetValue(0) != DBNull.Value) ? reader.GetValue(0).ToString() : string.Empty,
                            OptionDesc = (reader.GetValue(1) != DBNull.Value) ? reader.GetValue(1).ToString() : string.Empty,
                            OptionId = (reader.GetValue(2) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(2).ToString()) : 0
                        });
                    }
                }

                return new OkObjectResult(new ResponseModel<List<MasterOptionDto>> { Value = returnData.OrderBy(x => x.OptionCode).ToList(), IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Master Options data from Attribute Assignment"));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetMasterAttributeGroupsAsync()
        {
            try
            {
                var returnData = new List<MasterAttributeGroupDto>();

                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT a.ATTRIBUTE_GROUP_ID, a.DESCRIPTION, (SELECT COUNT(*) FROM dbo.ATTR_GROUP_ASSIGNMENT b WHERE a.ATTRIBUTE_GROUP_ID = b.ATTRIBUTE_GROUP_ID) 'Total', a.IsActive FROM dbo.MASTER_ATTRIBUTE_GROUP a";
                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        returnData.Add(new MasterAttributeGroupDto
                        {
                            AttributeGroupId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                            Description = (reader.GetValue(1) != DBNull.Value) ? reader.GetValue(1).ToString().Trim() : string.Empty,
                            TotalItems = (reader.GetValue(2) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(2).ToString()) : 0,
                            IsActive = (reader.GetValue(3) != DBNull.Value) ? Convert.ToBoolean(reader.GetValue(3)) : null
                        });
                    }
                }

                return new OkObjectResult(new ResponseModel<List<MasterAttributeGroupDto>> { Value = returnData.OrderBy(x => x.Description).ToList(), IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Master Options data from Attribute Assignment"));
            }
        }

        [HttpPut]
        public async Task<MasterAttributeItemDto> UpdateAttributeItemAsync([FromBody] MasterAttributeItemDto model)
        {
            var findItemAssignment = _context.AttrGroupAssignments.SingleOrDefault(x => x.AttrGroupAssignmentId == model.AttributeGroupAssignmentId);

            if (findItemAssignment != null)
            {
                var findItem = _context.MasterAttributeItems.SingleOrDefault(x => x.AttributeItemId == findItemAssignment.AttributeItemId);

                if (findItem != null)
                {
                    if (!string.IsNullOrWhiteSpace(model.Description))
                    {
                        // Check to ensure no assignment with the same group and item exists
                        var check = await (from c in _context.AttrGroupAssignments
                                           join g in _context.MasterAttributeGroups on c.AttributeGroupId equals g.AttributeGroupId
                                           join i in _context.MasterAttributeItems on c.AttributeItemId equals i.AttributeItemId
                                           where i.Description.Trim() == model.Description.Trim()
                                           select c).ToListAsync();

                        if (findItem.Description.Trim() != model.Description.Trim())
                        {
                            if (!check.Any())
                            {
                                findItem.Description = model.Description.Trim();
                                findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                                findItem.UpdatedDateTime = DateTime.Now;

                                _context.MasterAttributeItems.Update(findItem);
                                await _context.SaveChangesAsync();
                            }
                            else
                            {
                                model.ErrorMessage = "An item with similar description already exist within the group";
                            }
                        }
                    }
                    else
                    {
                        model.ErrorMessage = "A description cannot be empty";
                    }
                }
                else
                {
                    model.ErrorMessage = "Can't find Attribute Item with that Id";
                }
            }
            else
            {
                model.ErrorMessage = "Can't find Attribute Item within that Assignment";
            }

            return model;
        }

        [HttpPut]
        public async Task<MasterAttributeItemDto> DeleteAttributeItemAsync([FromBody] MasterAttributeItemDto model)
        {
            var findItem = _context.MasterAttributeItems.SingleOrDefault(x => x.AttributeItemId == model.AttributeItemId);

            if (findItem != null)
            {
                // Don't disable the Attribute Item (01/10/2025)
                //findItem.IsActive = false;
                //_context.MasterAttributeItems.Update(findItem);
                //await _context.SaveChangesAsync();

                // Deactivate Attribute Assignments
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    // Don't disable the Attribute Item (01/10/2025)
                    //var query = "UPDATE a SET a.IsActive = 0 FROM dbo.ATTR_GROUP_ASSIGNMENT a JOIN dbo.MASTER_ATTRIBUTE_ITEM b ON a.ATTRIBUTE_ITEM_ID = b.ATTRIBUTE_ITEM_ID WHERE a.ATTRIBUTE_ITEM_ID = @AttributeItemId";
                    //var command = new SqlCommand(query, connection);
                    //command.Parameters.Add("@AttributeItemId", SqlDbType.Int).Value = model.AttributeItemId;

                    var query = "UPDATE a SET a.IsActive = 0, a.UpdatedBy = @updatedBy, a.UpdatedDateTime = @updatedDateTime FROM dbo.ATTR_GROUP_ASSIGNMENT a WHERE a.ATTR_GROUP_ASSIGNMENT_ID = @attrGroupAssignmentId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@attrGroupAssignmentId", SqlDbType.Int).Value = model.AttributeGroupAssignmentId;
                    command.Parameters.Add("@updatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@updatedDateTime", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }

                // Deactivate OPTION_ATTRIBUTE_GROUP_ITEM
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    // Don't disable the Attribute Item (01/10/2025)
                    //var query = "UPDATE a SET a.IsActive = 0, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.OPTION_ATTRIBUTE_GROUP_ITEM a JOIN dbo.ATTR_GROUP_ASSIGNMENT b ON a.ATTR_GROUP_ASSIGNMENT_ID = b.ATTR_GROUP_ASSIGNMENT_ID WHERE b.ATTRIBUTE_ITEM_ID = @AttributeItemId";
                    //var command = new SqlCommand(query, connection);
                    //command.Parameters.Add("@AttributeItemId", SqlDbType.Int).Value = model.AttributeItemId;
                    //command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    //command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    var query = "UPDATE a SET a.IsActive = 0, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.OPTION_ATTRIBUTE_GROUP_ITEM a WHERE a.ATTR_GROUP_ASSIGNMENT_ID = @attrGroupAssignmentId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@attrGroupAssignmentId", SqlDbType.Int).Value = model.AttributeGroupAssignmentId;
                    command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }

                // Deactivate MasterOptionAttributeItem
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    // Don't disable the Attribute Item (01/10/2025)
                    //var query = "UPDATE a SET a.IsActive = 0, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.MasterOptionAttributeItem a JOIN dbo.ATTR_GROUP_ASSIGNMENT b ON a.ATTR_GROUP_ASSIGNMENT_ID = b.ATTR_GROUP_ASSIGNMENT_ID WHERE b.ATTRIBUTE_ITEM_ID = @AttributeItemId";
                    //var command = new SqlCommand(query, connection);
                    //command.Parameters.Add("@AttributeItemId", SqlDbType.Int).Value = model.AttributeItemId;
                    //command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    //command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    var query = "UPDATE a SET a.IsActive = 0, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.MasterOptionAttributeItem a WHERE a.ATTR_GROUP_ASSIGNMENT_ID = @attrGroupAssignmentId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@attrGroupAssignmentId", SqlDbType.Int).Value = model.AttributeGroupAssignmentId;
                    command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }
            }
            else
            {
                model.ErrorMessage = "Can't find the Attribute with the ID";
            }

            return model;
        }

        [HttpPut]
        public async Task<MasterAttributeItemDto> ReactivateAttributeItemAsync([FromBody] MasterAttributeItemDto model)
        {
            var findItem = _context.MasterAttributeItems.SingleOrDefault(x => x.AttributeItemId == model.AttributeItemId);

            if (findItem != null)
            {
                // Do not reactivate the whole thing (01/10/2025)
                //findItem.IsActive = true;
                //findItem.UpdatedDateTime = DateTime.UtcNow;
                //findItem.UpdatedBy = User.Identity.Name.Split('@')[0];

                //_context.MasterAttributeItems.Update(findItem);
                //await _context.SaveChangesAsync();

                // Reactivate Attribute Assignments
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "UPDATE a SET a.IsActive = 1, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.ATTR_GROUP_ASSIGNMENT a WHERE a.ATTR_GROUP_ASSIGNMENT_ID = @attrGroupAssignmentId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@attrGroupAssignmentId", SqlDbType.Int).Value = model.AttributeGroupAssignmentId;
                    command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }

                // Reactivate OPTION_ATTRIBUTE_GROUP_ITEM
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "UPDATE a SET a.IsActive = 1, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.OPTION_ATTRIBUTE_GROUP_ITEM a WHERE a.ATTR_GROUP_ASSIGNMENT_ID = @attrGroupAssignmentId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@attrGroupAssignmentId", SqlDbType.Int).Value = model.AttributeGroupAssignmentId;
                    command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }

                // Reactivate MasterOptionAttributeItem
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "UPDATE a SET a.IsActive = 1, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.MasterOptionAttributeItem a WHERE a.ATTR_GROUP_ASSIGNMENT_ID = @attrGroupAssignmentId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@attrGroupAssignmentId", SqlDbType.Int).Value = model.AttributeGroupAssignmentId;
                    command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }
            }
            else
            {
                model.ErrorMessage = "Can't find the Attribute with the ID";
            }

            return model;
        }

        [HttpPut]
        public async Task<MasterAttributeItemDto> RelinkAttributeItemAsync( [FromBody] MasterAttributeItemDto model )
        {
            bool isChanged = false;
            string? user = User?.Identity?.Name?.Split( '@' )[ 0 ];

            // Find every MasterOptionId that uses the attribute group
            var mOAIs = await _context.MasterOptionAttributeItems
                .Where( x => x.AttrGroupAssignment != null && x.AttrGroupAssignment.AttributeGroupId == model.AttributeGroupId )
                .Select
                (
                    x => new
                    {
                        IsActive = x.IsActive ?? false,
                        AttrGroupAssignmentId = x.AttrGroupAssignmentId!.Value,
                        x.MasterOptionId
                    }
                )
                .ToListAsync();
            int listLength = mOAIs.Count;

            // Mark which have any active item
            if ( listLength > 0 )
            {
                HashSet<int> activeAttributes = new(); // just means at least one of the items in the option is active
                HashSet<int> newMasterOptionIds = new();
                HashSet<int> alreadyLinkedMasterOptionIds = new(); // track the ones already linked for this item

                for ( int i = listLength - 1; i >= 0; --i )
                {
                    var mOAI = mOAIs[ i ];
                    if ( !alreadyLinkedMasterOptionIds.Contains( mOAI.MasterOptionId ) )
                    {
                        if ( mOAI.AttrGroupAssignmentId == model.AttributeGroupAssignmentId )
                        {
                            activeAttributes.Remove( mOAI.MasterOptionId );
                            newMasterOptionIds.Remove( mOAI.MasterOptionId );
                            alreadyLinkedMasterOptionIds.Add( mOAI.MasterOptionId );
                        }
                        else
                        {
                            if ( mOAI.IsActive )
                            {
                                activeAttributes.Add( mOAI.MasterOptionId );
                            }

                            newMasterOptionIds.Add( mOAI.MasterOptionId );
                        }
                    }
                }

                // Add missing
                isChanged = newMasterOptionIds.Count > 0;

                foreach ( int masterOptionId in newMasterOptionIds )
                {
                    _context.MasterOptionAttributeItems.Add
                    (
                        new()
                        {
                            MasterOptionId = masterOptionId,
                            AttrGroupAssignmentId = model.AttributeGroupAssignmentId,
                            IsActive = activeAttributes.Contains( masterOptionId ), // match what exists already, not our fault if some have mixed toggles
                            CreatedDateTime = DateTime.Now,
                            CreatedBy = user
                        }
                    );
                }
            }

            // Find every AvailableOptionId that uses the attribute group
            var oAGIs = await _context.OptionAttributeGroupItems
                .Where( x => x.AttrGroupAssignment != null && x.AttrGroupAssignment.AttributeGroupId == model.AttributeGroupId && x.PlanOption != null )
                .Select
                (
                    x => new
                    {
                        IsActive = x.IsActive ?? false,
                        PlanOptionId = x.PlanOptionId!.Value,
                        MasterOptionId = x.PlanOption!.MasterOptionId!.Value,
                        x.AttrGroupAssignmentId,
                        x.PlanOption!.PhasePlan!.MasterPlanId,
                        x.PlanOption!.PhasePlan!.MasterPlan.PlanNum
                    }
                )
                .ToListAsync();
            listLength = oAGIs.Count;

            // Mark which have any active item
            if ( listLength > 0 )
            {
                HashSet<int> activeAttributes = new(); // just means at least one of the items in the option is active
                Dictionary<int, (int, int, string)> newPlanOptions = new(); // key = PlanOptionId, value = [ MasterOptionId, MasterPlanId, PlanNum ]
                HashSet<int> alreadyLinkedPlanOptionIds = new(); // track the ones already linked for this item

                for ( int i = listLength - 1; i >= 0; --i )
                {
                    var oAGI = oAGIs[ i ];
                    if ( !alreadyLinkedPlanOptionIds.Contains( oAGI.PlanOptionId ) )
                    {
                        if ( oAGI.AttrGroupAssignmentId == model.AttributeGroupAssignmentId )
                        {
                            activeAttributes.Remove( oAGI.PlanOptionId );
                            newPlanOptions.Remove( oAGI.PlanOptionId );
                            alreadyLinkedPlanOptionIds.Add( oAGI.PlanOptionId );
                        }
                        else
                        {
                            if ( oAGI.IsActive )
                            {
                                activeAttributes.Add( oAGI.PlanOptionId );
                            }

                            newPlanOptions.TryAdd( oAGI.PlanOptionId, (oAGI.MasterOptionId, oAGI.MasterPlanId, oAGI.PlanNum) );
                        }
                    }
                }

                // Add missing
                isChanged = isChanged || newPlanOptions.Count > 0;

                foreach ( KeyValuePair<int, (int, int, string)> kvp in newPlanOptions )
                {
                    _context.OptionAttributeGroupItems.Add
                    (
                        new OptionAttributeGroupItem
                        {
                            MasterOptionId = kvp.Value.Item1,
                            AttrGroupAssignmentId = model.AttributeGroupAssignmentId,
                            PlanOptionId = kvp.Key,
                            PlanNum = kvp.Value.Item3,
                            MasterPlanId = kvp.Value.Item2,
                            IsActive = activeAttributes.Contains( kvp.Key ), // match what exists already, not our fault if some have mixed toggles
                            CreatedDateTime = DateTime.Now,
                            CreatedBy = user
                        }
                    );
                }
            }

            // Save
            if ( isChanged )
            {
                await _context.SaveChangesAsync();
            }

            return model;
        }

		[HttpPut]
        public async Task<MasterAttributeGroupDto> UpdateAttributeGroupAsync([FromBody] MasterAttributeGroupDto model)
        {
            var findAttributeGroup = _context.MasterAttributeGroups.SingleOrDefault(x => x.AttributeGroupId == model.AttributeGroupId);

            if (findAttributeGroup != null)
            {
                if (!string.IsNullOrWhiteSpace(model.Description))
                {
                    // Check to ensure there's no Attribute Group with the same name
                    var findSimilarDescription = _context.MasterAttributeGroups.Where(x => x.Description == model.Description).ToList();

                    if (!findSimilarDescription.Any())
                    {
                        findAttributeGroup.Description = model.Description;
                        findAttributeGroup.UpdatedDateTime = DateTime.Now;
                        findAttributeGroup.UpdatedBy = User.Identity.Name.Split('@')[0];

                        _context.MasterAttributeGroups.Update(findAttributeGroup);
                        await _context.SaveChangesAsync();
                    }
                    else
                    {
                        model.ErrorMessage = "Similar description already existed";
                    }
                }
                else
                {
                    model.ErrorMessage = "Description cannot be empty";
                }
            }
            else
            {
                model.ErrorMessage = "Can't find the Attribute Group with the ID";
            }

            return model;
        }

        [HttpPut]
        public async Task<MasterAttributeGroupDto> DeleteAttributeGroupAsync([FromBody] MasterAttributeGroupDto model)
        {
            var findAttributeGroup = _context.MasterAttributeGroups.SingleOrDefault(x => x.AttributeGroupId == model.AttributeGroupId);

            if (findAttributeGroup != null)
            {
                findAttributeGroup.IsActive = false;
                findAttributeGroup.UpdatedBy = User.Identity.Name.Split('@')[0];
                findAttributeGroup.UpdatedDateTime = DateTime.Now;

                _context.MasterAttributeGroups.Update(findAttributeGroup);
                await _context.SaveChangesAsync();

                var conn = _configuration.GetConnectionString("ERPConnection");

                // Deactivate Attribute Assignments & Master Attribute Items
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "UPDATE a SET a.IsActive = 0, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.ATTR_GROUP_ASSIGNMENT a JOIN dbo.MASTER_ATTRIBUTE_GROUP b ON a.ATTRIBUTE_GROUP_ID = b.ATTRIBUTE_GROUP_ID WHERE a.ATTRIBUTE_GROUP_ID = @AttributeGroupId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = model.AttributeGroupId;
                    command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }

                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "UPDATE a SET a.IsActive = 0, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.MASTER_ATTRIBUTE_ITEM a JOIN dbo.ATTR_GROUP_ASSIGNMENT b ON a.ATTRIBUTE_ITEM_ID = b.ATTRIBUTE_ITEM_ID JOIN dbo.MASTER_ATTRIBUTE_GROUP c ON b.ATTRIBUTE_GROUP_ID = c.ATTRIBUTE_GROUP_ID WHERE c.ATTRIBUTE_GROUP_ID = @AttributeGroupId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = model.AttributeGroupId;
                    command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }

                // Deactivate OPTION_ATTRIBUTE_GROUP_ITEM
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "UPDATE a SET a.IsActive = 0, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.OPTION_ATTRIBUTE_GROUP_ITEM a JOIN dbo.ATTR_GROUP_ASSIGNMENT b ON a.ATTR_GROUP_ASSIGNMENT_ID = b.ATTR_GROUP_ASSIGNMENT_ID WHERE b.ATTRIBUTE_GROUP_ID = @AttributeGroupId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = model.AttributeGroupId;
                    command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }

                // Deactivate MasterOptionAttributeItem
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "UPDATE a SET a.IsActive = 0, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.MasterOptionAttributeItem a JOIN dbo.ATTR_GROUP_ASSIGNMENT b ON a.ATTR_GROUP_ASSIGNMENT_ID = b.ATTR_GROUP_ASSIGNMENT_ID WHERE b.ATTRIBUTE_GROUP_ID = @AttributeGroupId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = model.AttributeGroupId;
                    command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }
            }
            else
            {
                model.ErrorMessage = "Can't find the Attribute Group with the ID";
            }

            return model;
        }

        [HttpPut]
        public async Task<MasterAttributeGroupDto> ReactivateAttributeGroupAsync([FromBody] MasterAttributeGroupDto model)
        {
            var findAttributeGroup = _context.MasterAttributeGroups.SingleOrDefault(x => x.AttributeGroupId == model.AttributeGroupId);

            if (findAttributeGroup != null)
            {
                findAttributeGroup.IsActive = true;
                findAttributeGroup.UpdatedDateTime = DateTime.Now;
                findAttributeGroup.UpdatedBy = User.Identity.Name.Split('@')[0];

                _context.MasterAttributeGroups.Update(findAttributeGroup);
                await _context.SaveChangesAsync();

                var conn = _configuration.GetConnectionString("ERPConnection");

                // Reactivate Attribute Assignments & Master Attribute Items
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "UPDATE a SET a.IsActive = 1, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.ATTR_GROUP_ASSIGNMENT a JOIN dbo.MASTER_ATTRIBUTE_GROUP b ON a.ATTRIBUTE_GROUP_ID = b.ATTRIBUTE_GROUP_ID WHERE a.ATTRIBUTE_GROUP_ID = @AttributeGroupId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = model.AttributeGroupId;
                    command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }

                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "UPDATE a SET a.IsActive = 1, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.MASTER_ATTRIBUTE_ITEM a JOIN dbo.ATTR_GROUP_ASSIGNMENT b ON a.ATTRIBUTE_ITEM_ID = b.ATTRIBUTE_ITEM_ID JOIN dbo.MASTER_ATTRIBUTE_GROUP c ON b.ATTRIBUTE_GROUP_ID = c.ATTRIBUTE_GROUP_ID WHERE c.ATTRIBUTE_GROUP_ID = @AttributeGroupId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = model.AttributeGroupId;
                    command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }

                // Reactivate OPTION_ATTRIBUTE_GROUP_ITEM
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "UPDATE a SET a.IsActive = 1, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.OPTION_ATTRIBUTE_GROUP_ITEM a JOIN dbo.ATTR_GROUP_ASSIGNMENT b ON a.ATTR_GROUP_ASSIGNMENT_ID = b.ATTR_GROUP_ASSIGNMENT_ID WHERE b.ATTRIBUTE_GROUP_ID = @AttributeGroupId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = model.AttributeGroupId;
                    command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }

                // Reactivate MasterOptionAttributeItem
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "UPDATE a SET a.IsActive = 1, a.UpdatedBy = @UpdatedBy, a.UpdatedDateTime = @Updated FROM dbo.MasterOptionAttributeItem a JOIN dbo.ATTR_GROUP_ASSIGNMENT b ON a.ATTR_GROUP_ASSIGNMENT_ID = b.ATTR_GROUP_ASSIGNMENT_ID WHERE b.ATTRIBUTE_GROUP_ID = @AttributeGroupId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = model.AttributeGroupId;
                    command.Parameters.Add("@UpdatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                    command.Parameters.Add("@Updated", SqlDbType.DateTime).Value = DateTime.Now;

                    command.ExecuteNonQuery();
                }
            }
            else
            {
                model.ErrorMessage = "Can't find the Attribute Group with the ID";
            }

            return model;
        }

        [HttpGet("{AttributeGroupId}")]
        public async Task<IActionResult> GetAttributeAssignmentByGroupIdAsync(int AttributeGroupId)
        {
            try
            {
                var returnData = new List<MasterAttributeItemDto>();

                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT a.ATTR_GROUP_ASSIGNMENT_ID, c.ATTRIBUTE_ITEM_ID, b.ATTRIBUTE_GROUP_ID, c.DESCRIPTION, a.IsActive FROM dbo.ATTR_GROUP_ASSIGNMENT a JOIN dbo.MASTER_ATTRIBUTE_GROUP b ON a.ATTRIBUTE_GROUP_ID = b.ATTRIBUTE_GROUP_ID JOIN dbo.MASTER_ATTRIBUTE_ITEM c ON a.ATTRIBUTE_ITEM_ID = c.ATTRIBUTE_ITEM_ID WHERE b.ATTRIBUTE_GROUP_ID = @AttributeGroupId";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = AttributeGroupId;

                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        returnData.Add(new MasterAttributeItemDto
                        {
                            AttributeGroupAssignmentId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                            AttributeItemId = (reader.GetValue(1) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(1).ToString()) : 0,
                            AttributeGroupId = (reader.GetValue(2) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(2).ToString()) : 0,
                            Description = (reader.GetValue(3) != DBNull.Value) ? reader.GetValue(3).ToString() : string.Empty,
                            IsActive = (reader.GetValue(4) != DBNull.Value) ? Convert.ToBoolean(reader.GetValue(4)) : null
                        });
                    }
                }

                return new OkObjectResult(new ResponseModel<List<MasterAttributeItemDto>> { Value = returnData.OrderBy(x => x.Description).ToList(), IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Master Attribute data from Attribute Assignment"));
            }
        }

        [HttpGet("{AttributeGroupId}/{MasterOptionId}/{PlanOptionId}")]
        public async Task<IActionResult> GetAttributeAssignmentByGroupAndOptionIdAsync(int AttributeGroupId, int MasterOptionId, int? PlanOptionId)
        {
            try
            {
                var returnData = new List<MasterAttributeItemDto>();

                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT DISTINCT b.ATTR_GROUP_ASSIGNMENT_ID, a.ATTRIBUTE_ITEM_ID, b.ATTRIBUTE_GROUP_ID, a.DESCRIPTION, c.MASTER_OPTION_ID, c.OP_ATTR_GROUP_ITEM_ID, c.PLAN_NUM, c.PLAN_OPTION_ID, e.PHASE_PLAN_ID, g.SUBDIVISION_NAME, e.MASTER_PLAN_ID, c.OP_ATTR_GROUP_ITEM_ID, c.IsActive FROM dbo.MASTER_ATTRIBUTE_ITEM a JOIN dbo.ATTR_GROUP_ASSIGNMENT b ON a.ATTRIBUTE_ITEM_ID = b.ATTRIBUTE_ITEM_ID JOIN dbo.OPTION_ATTRIBUTE_GROUP_ITEM c ON b.ATTR_GROUP_ASSIGNMENT_ID = c.ATTR_GROUP_ASSIGNMENT_ID JOIN dbo.AVAILABLE_PLAN_OPTION d on c.PLAN_OPTION_ID = d.PLAN_OPTION_ID JOIN dbo.PHASE_PLAN e JOIN dbo.SUBDIVISION g on e.SUBDIVISION_ID = g.SUBDIVISION_ID on d.PHASE_PLAN_ID = e.PHASE_PLAN_ID WHERE c.MASTER_OPTION_ID = @MasterOptionId AND b.ATTRIBUTE_GROUP_ID = @AttributeGroupId and c.PLAN_OPTION_ID = @PlanOptionId and d.IsActive = 1 and e.IsActive = 1 ORDER BY 4, 10";

                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@MasterOptionId", SqlDbType.Int).Value = MasterOptionId;
                    command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = AttributeGroupId;
                    command.Parameters.Add("@PlanOptionId", SqlDbType.Int).Value = PlanOptionId;

                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        returnData.Add(new MasterAttributeItemDto
                        {
                            AttributeGroupAssignmentId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                            AttributeItemId = (reader.GetValue(1) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(1).ToString()) : 0,
                            AttributeGroupId = (reader.GetValue(2) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(2).ToString()) : 0,
                            Description = (reader.GetValue(3) != DBNull.Value) ? reader.GetValue(3).ToString() : string.Empty,
                            MasterOptionId = (reader.GetValue(4) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(4).ToString()) : 0,
                            PlanNum = (reader.GetValue(6) != DBNull.Value) ? reader.GetValue(6).ToString() : string.Empty,
                            Subdivision = (reader.GetValue(9) != DBNull.Value) ? reader.GetValue(9).ToString() : string.Empty,
                            PlanOptionId = PlanOptionId,
                            MasterPlanId = (reader.GetValue(10) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(10).ToString()) : null,
                            OptionAttributeGroupItemId = (reader.GetValue(11) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(11).ToString()) : 0,
                            IsActive = (reader.GetValue(12) != DBNull.Value) ? Convert.ToBoolean(reader.GetValue(12)) : null
                        });
                    }
                }

                return new OkObjectResult(new ResponseModel<List<MasterAttributeItemDto>> { Value = returnData.OrderBy(x => x.Description).ToList(), IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Master Attribute data from Attribute Assignment"));
            }
        }

        [HttpPost]
        public async Task<MasterAttributeGroupDto> AddAttributeGroupAsync([FromBody] MasterAttributeGroupDto model)
        {
            if (model != null)
            {
                // Check to ensure no existing group with the same description exists
                var check = _context.MasterAttributeGroups.Where(x => x.Description.Trim() == model.Description.Trim()).ToList();

                if (!check.Any())
                {
                    var buildAttributeGroup = new MasterAttributeGroup
                    {
                        Description = model.Description,
                        IsActive = true,
                        CreatedDateTime = DateTime.Now,
                        CreatedBy = User.Identity.Name.Split('@')[0]
                    };

                    _context.MasterAttributeGroups.Add(buildAttributeGroup);

                    await _context.SaveChangesAsync();

                    _context.AttrGroupAssignments.Add(new AttrGroupAssignment
                    {
                        AttributeGroupId = buildAttributeGroup.AttributeGroupId,
                        CreatedDateTime = DateTime.Now,
                        CreatedBy = User.Identity.Name.Split('@')[0],
                        IsActive = true
                    });

                    await _context.SaveChangesAsync();
                }
                else
                {
                    model.ErrorMessage = "There's already a group with the same description";
                }

                return model;
            }
            else
            {
                var badModel = new MasterAttributeGroupDto();
                badModel.ErrorMessage = "Error in creating new Attribute Group";
                return badModel;
            }
        }

        [HttpPost]
        public async Task<MasterAttributeItemDto> AddAttributeItemAsync([FromBody] MasterAttributeItemDto model)
        {
            if (model != null)
            {
                if (!string.IsNullOrEmpty(model.Description))
                {
                    List<AttrGroupAssignment> check;
                    int getAttributeAssignmentId = 0;

                    // Check if this is an existing attribute or not
                    var findAttributeItem = _context.MasterAttributeItems.FirstOrDefault(x => x.Description.Trim().ToLower() == model.Description.Trim().ToLower());

                    // Existing
                    if (findAttributeItem != null)
                    {
                        // Check to ensure no assignment with the same group and item exists
                        check = await (from c in _context.AttrGroupAssignments
                                       join g in _context.MasterAttributeGroups on c.AttributeGroupId equals g.AttributeGroupId
                                       join i in _context.MasterAttributeItems on c.AttributeItemId equals i.AttributeItemId
                                       where i.AttributeItemId == findAttributeItem.AttributeItemId && g.AttributeGroupId == model.AttributeGroupId
                                       select c).ToListAsync();

                        if (!check.Any())
                        {
                            // Check if the Attribute Group has empty Attribute Item, if yes, update that versus adding a new one
                            var checkOrphanAttributeGroup = await (from c in _context.AttrGroupAssignments
                                                                   join g in _context.MasterAttributeGroups on c.AttributeGroupId equals g.AttributeGroupId
                                                                   where g.AttributeGroupId == model.AttributeGroupId && c.AttributeItemId == null
                                                                   select c).FirstOrDefaultAsync();

                            if (checkOrphanAttributeGroup != null)
                            {
                                // Update Group with the new Item
                                checkOrphanAttributeGroup.AttributeGroupId = model.AttributeGroupId;
                                checkOrphanAttributeGroup.AttributeItemId = findAttributeItem.AttributeItemId;
                                checkOrphanAttributeGroup.IsActive = true;
                                checkOrphanAttributeGroup.UpdatedDateTime = DateTime.Now;
                                checkOrphanAttributeGroup.UpdatedBy = User.Identity.Name.Split('@')[0];

                                _context.AttrGroupAssignments.Update(checkOrphanAttributeGroup);

                                await _context.SaveChangesAsync();
                            }
                            else
                            {
                                // Add new Attribute Item to the Group
                                var buildAttrGroupAssignment = new AttrGroupAssignment
                                {
                                    AttributeGroupId = model.AttributeGroupId,
                                    AttributeItemId = findAttributeItem.AttributeItemId,
                                    IsActive = true,
                                    CreatedDateTime = DateTime.Now,
                                    CreatedBy = User.Identity.Name.Split('@')[0]
                                };

                                _context.AttrGroupAssignments.Add(buildAttrGroupAssignment);

                                await _context.SaveChangesAsync();

                                getAttributeAssignmentId = buildAttrGroupAssignment.AttrGroupAssignmentId;
                            }
                        }
                        else
                        {
                            model.ErrorMessage = "An item with similar description exists already in this group";
                        }
                    }
                    // New
                    else
                    {
                        // Check to ensure no assignment with the same group and item exists
                        check = await (from c in _context.AttrGroupAssignments
                                       join g in _context.MasterAttributeGroups on c.AttributeGroupId equals g.AttributeGroupId
                                       join i in _context.MasterAttributeItems on c.AttributeItemId equals i.AttributeItemId
                                       where i.Description.Trim() == model.Description.Trim() && g.AttributeGroupId == model.AttributeGroupId
                                       select c).ToListAsync();

                        if (!check.Any())
                        {
                            var buildMasterItem = new MasterAttributeItem
                            {
                                Description = model.Description,
                                IsActive = true,
                                CreatedDateTime = DateTime.Now,
                                CreatedBy = User.Identity.Name.Split('@')[0]
                            };

                            _context.MasterAttributeItems.Add(buildMasterItem);

                            await _context.SaveChangesAsync();

                            // Check if the Attribute Group has empty Attribute Item, if yes, update that versus adding a new one
                            var checkOrphanAttributeGroup = await (from c in _context.AttrGroupAssignments
                                                                   join g in _context.MasterAttributeGroups on c.AttributeGroupId equals g.AttributeGroupId
                                                                   where g.AttributeGroupId == model.AttributeGroupId && c.AttributeItemId == null
                                                                   select c).FirstOrDefaultAsync();

                            if (checkOrphanAttributeGroup != null)
                            {
                                // Update Group with the new Item
                                checkOrphanAttributeGroup.AttributeGroupId = model.AttributeGroupId;
                                checkOrphanAttributeGroup.AttributeItemId = buildMasterItem.AttributeItemId;
                                checkOrphanAttributeGroup.IsActive = true;
                                checkOrphanAttributeGroup.UpdatedDateTime = DateTime.Now;
                                checkOrphanAttributeGroup.UpdatedBy = User.Identity.Name.Split('@')[0];

                                _context.AttrGroupAssignments.Update(checkOrphanAttributeGroup);

                                await _context.SaveChangesAsync();
                            }
                            else
                            {
                                var buildAttrGroupAssignment = new AttrGroupAssignment
                                {
                                    AttributeGroupId = model.AttributeGroupId,
                                    AttributeItemId = buildMasterItem.AttributeItemId,
                                    IsActive = true,
                                    CreatedDateTime = DateTime.Now,
                                    CreatedBy = User.Identity.Name.Split('@')[0]
                                };

                                _context.AttrGroupAssignments.Add(buildAttrGroupAssignment);

                                await _context.SaveChangesAsync();

                                getAttributeAssignmentId = buildAttrGroupAssignment.AttrGroupAssignmentId;
                            }
                        }
                        else
                        {
                            model.ErrorMessage = "An item with similar description exists already in this group";
                        }
                    }

                    #region add to Plan and/or Option
                    // Add the new Attribute Assignment to either Plan or Option
                    // Check for missing Attribute Assignment, add if necessary
                    var getAttributeAssignments = _context.AttrGroupAssignments.Where(x => x.AttributeGroupId == model.AttributeGroupId && x.IsActive == true).ToList();

                    if (getAttributeAssignments.Any())
                    {
                        var masterOptionAttributeItemsList = new List<MasterOptionAttributeItemDto>();
                        var optionAttributeGroupItemsList = new List<OptionAttributeGroupItemDto>();
                        var distinctMasterOptionAttributeItemsList = new List<MasterOptionAttributeItemDto>();
                        var distinctOptionAttributeGroupItemsList = new List<OptionAttributeGroupItemDto>();

                        foreach (var attributeAssignment in getAttributeAssignments)
                        {
                            // Check MasterOptionAttributeItem
                            var getMasterOptionAttributeItems = _context.MasterOptionAttributeItems.Where(x => x.AttrGroupAssignmentId == attributeAssignment.AttrGroupAssignmentId && x.IsActive == true).ToList();

                            if (getMasterOptionAttributeItems.Any())
                            {
                                foreach (var masterOptionAttributeItem in getMasterOptionAttributeItems)
                                {
                                    distinctMasterOptionAttributeItemsList.Add(new MasterOptionAttributeItemDto
                                    {
                                        MasterOptionId = masterOptionAttributeItem.MasterOptionId
                                    });

                                    masterOptionAttributeItemsList.Add(new MasterOptionAttributeItemDto
                                    {
                                        MasterOptionId = masterOptionAttributeItem.MasterOptionId,
                                        AttributeGroupAssignmentId = attributeAssignment.AttrGroupAssignmentId,
                                    });
                                }
                            }

                            // Check OPTION_ATTRIBUTE_GROUP_ITEM
                            var getOptionAttributeGroupItems = _context.OptionAttributeGroupItems.Where(x => x.AttrGroupAssignmentId == attributeAssignment.AttrGroupAssignmentId && x.IsActive == true).ToList();

                            if (getOptionAttributeGroupItems.Any())
                            {
                                foreach (var optionAttributeGroupItem in getOptionAttributeGroupItems)
                                {
                                    distinctOptionAttributeGroupItemsList.Add(new OptionAttributeGroupItemDto
                                    {
                                        MasterOptionId = optionAttributeGroupItem.MasterOptionId,
                                        PlanOptionId = optionAttributeGroupItem.PlanOptionId,
                                        PlanNum = optionAttributeGroupItem.PlanNum,
                                        MasterPlanId = optionAttributeGroupItem.MasterPlanId,
                                    });

                                    optionAttributeGroupItemsList.Add(new OptionAttributeGroupItemDto
                                    {
                                        MasterOptionId = optionAttributeGroupItem.MasterOptionId,
                                        AttrGroupAssignmentId = optionAttributeGroupItem.AttrGroupAssignmentId,
                                        PlanOptionId = optionAttributeGroupItem.PlanOptionId,
                                        PlanNum = optionAttributeGroupItem.PlanNum,
                                        MasterPlanId = optionAttributeGroupItem.MasterPlanId,
                                    });
                                }
                            }
                        }

                        // Remaining Attribute Assignment Ids
                        if (masterOptionAttributeItemsList.Any())
                        {
                            var remainingAttributeGroupAssignmentIds = getAttributeAssignments.Select(x => x.AttrGroupAssignmentId).Except(masterOptionAttributeItemsList.Select(x => Convert.ToInt32(x.AttributeGroupAssignmentId)));

                            foreach (var attributeGroupAssignmentId in remainingAttributeGroupAssignmentIds)
                            {
                                if (attributeGroupAssignmentId == getAttributeAssignmentId)
                                {
                                    foreach (var masterOptionId in distinctMasterOptionAttributeItemsList.GroupBy(x => x.MasterOptionId).Select(g => g.First()).ToList())
                                    {
                                        _context.MasterOptionAttributeItems.Add(new MasterOptionAttributeItem
                                        {
                                            MasterOptionId = masterOptionId.MasterOptionId,
                                            AttrGroupAssignmentId = attributeGroupAssignmentId,
                                            IsActive = true,
                                            CreatedDateTime = DateTime.Now,
                                            CreatedBy = User.Identity.Name.Split('@')[0]
                                        });
                                    }
                                }
                            }

                            await _context.SaveChangesAsync();
                        }

                        if (optionAttributeGroupItemsList.Any())
                        {
                            var remainingAttributeGroupAssignmentIds = getAttributeAssignments.Select(x => x.AttrGroupAssignmentId).Except(optionAttributeGroupItemsList.Select(x => x.AttrGroupAssignmentId));

                            foreach (var attributeGroupAssignmentId in remainingAttributeGroupAssignmentIds)
                            {
                                if (attributeGroupAssignmentId == getAttributeAssignmentId)
                                {
                                    foreach (var masterOptionId in distinctOptionAttributeGroupItemsList.GroupBy(x => new { MasterOptionId = x.MasterOptionId, AttrGroupAssignmentId = x.AttrGroupAssignmentId, PlanOptionId = x.PlanOptionId, PlanNum = x.PlanNum, MasterPlanId = x.MasterPlanId }).Select(g => g.First()).ToList())
                                    {
                                        _context.OptionAttributeGroupItems.Add(new OptionAttributeGroupItem
                                        {
                                            MasterOptionId = masterOptionId.MasterOptionId,
                                            AttrGroupAssignmentId = attributeGroupAssignmentId,
                                            PlanOptionId = masterOptionId.PlanOptionId,
                                            PlanNum = masterOptionId.PlanNum,
                                            MasterPlanId = masterOptionId.MasterPlanId,
                                            IsActive = true,
                                            CreatedDateTime = DateTime.Now,
                                            CreatedBy = User.Identity.Name.Split('@')[0]
                                        });
                                    }
                                }
                            }

                            await _context.SaveChangesAsync();
                        }
                    }
                    #endregion
                }
                else
                {
                    model.ErrorMessage = "Description cannot be empty";
                }

                return model;
            }
            else
            {
                var badModel = new MasterAttributeItemDto();
                badModel.ErrorMessage = "Error in adding an attribute item";
                return badModel;
            }
        }

        [HttpPost]
        public async Task<OptionAttributeGroupItemDto> AddAttributeItemByAssignmentAndOptionAsync(OptionAttributeGroupItemDto model)
        {
            try
            {
                if (model != null)
                {
                    if (model.IsActive == true)
                    {
                        // Check to make sure there's no existing assignment
                        var find = _context.OptionAttributeGroupItems.Where(x => x.AttrGroupAssignmentId == model.AttrGroupAssignmentId && x.MasterOptionId == model.MasterOptionId && x.PlanOptionId == model.PlanOptionId && x.PlanNum == model.PlanNum && x.MasterPlanId == model.MasterPlanId && x.IsActive == true).ToList();

                        if (!find.Any())
                        {
                            _context.OptionAttributeGroupItems.Add(new OptionAttributeGroupItem
                            {
                                MasterOptionId = model.MasterOptionId,
                                AttrGroupAssignmentId = model.AttrGroupAssignmentId,
                                PlanOptionId = model.PlanOptionId,
                                PlanNum = model.PlanNum,
                                MasterPlanId = _context.MasterPlans.Where(x => x.PlanNum == model.PlanNum).Select(x => x.MasterPlanId).FirstOrDefault(),
                                IsActive = true,
                                CreatedBy = User.Identity.Name.Split('@')[0],
                                CreatedDateTime = DateTime.Now
                            });

                            await _context.SaveChangesAsync();
                        }
                        else
                        {
                            model.ErrorMessage = "There's an existing group and item already assigned to this option";
                        }
                    }
                    else
                    {
                        model.ErrorMessage = "Either this item is inactive, or there's no group added yet to the plan.";
                    }

                    return model;
                }
                else
                {
                    var badModel = new OptionAttributeGroupItemDto();
                    badModel.ErrorMessage = "Error in assigning attribute to option";
                    return badModel;
                }
            }
            catch (Exception ex)
            {
                model.ErrorMessage = ex.Message;
                return model;
            }
        }

        [HttpPut]
        public async Task<MasterAttributeItemDto> DeleteOptionAttributeGroupItemAsync(MasterAttributeItemDto model)
        {
            var findItem = _context.OptionAttributeGroupItems.Where(x => x.OpAttrGroupItemId == model.OptionAttributeGroupItemId).FirstOrDefault();

            if (findItem != null)
            {
                findItem.IsActive = false;
                findItem.UpdatedDateTime = DateTime.Now;
                findItem.Updatedby = User.Identity.Name.Split('@')[0];

                _context.OptionAttributeGroupItems.Update(findItem);
                await _context.SaveChangesAsync();

                return model;
            }
            else
            {
                model.ErrorMessage = "Can't delete the selected item";

                return model;
            }
        }

        [HttpPut]
        public async Task<MasterAttributeItemDto> ReactivateOptionAttributeGroupItemAsync(MasterAttributeItemDto model)
        {
            var findItem = _context.OptionAttributeGroupItems.Where(x => x.OpAttrGroupItemId == model.OptionAttributeGroupItemId).FirstOrDefault();

            if (findItem != null)
            {
                findItem.IsActive = true;
                findItem.UpdatedDateTime = DateTime.Now;
                findItem.Updatedby = User.Identity.Name.Split('@')[0];

                _context.OptionAttributeGroupItems.Update(findItem);
                await _context.SaveChangesAsync();

                return model;
            }
            else
            {
                model.ErrorMessage = "Can't reactivate the selected item";

                return model;
            }
        }

        [HttpPut]
        public async Task<MasterAttributeGroupDto> DeleteOptionAttributeGroupAsync(MasterAttributeGroupDto model)
        {
            try
            {
                return await OptionAttributeGroupHandler(model, "Delete");
            }
            catch (Exception ex)
            {
                model.ErrorMessage = ex.Message;
                return model;
            }
        }

        [HttpPut]
        public async Task<MasterAttributeGroupDto> ReactivateOptionAttributeGroupAsync(MasterAttributeGroupDto model)
        {
            try
            {
                return await OptionAttributeGroupHandler(model, "Reactivate");
            }
            catch (Exception ex)
            {
                model.ErrorMessage = ex.Message;
                return model;
            }
        }

        private async Task<MasterAttributeGroupDto> OptionAttributeGroupHandler(MasterAttributeGroupDto model, string actionType)
        {
            var findItems = new List<int>();

            var conn = _configuration.GetConnectionString("ERPConnection");
            using (var connection = new SqlConnection(conn))
            {
                await connection.OpenAsync();

                var query = "select a.OP_ATTR_GROUP_ITEM_ID from [OPTION_ATTRIBUTE_GROUP_ITEM] a join [ATTR_GROUP_ASSIGNMENT] b on a.ATTR_GROUP_ASSIGNMENT_ID = b.ATTR_GROUP_ASSIGNMENT_ID where a.MASTER_OPTION_ID = @MasterOptionId and b.ATTRIBUTE_GROUP_ID = @AttributeGroupId and a.PLAN_NUM = @PlanNum and a.PLAN_OPTION_ID = @PlanOptionId;";

                var command = new SqlCommand(query, connection);
                command.Parameters.Add("@MasterOptionId", SqlDbType.Int).Value = model.MasterOptionId;
                command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = model.AttributeGroupId;
                command.Parameters.Add("@PlanNum", SqlDbType.VarChar).Value = model.PlanNum;
                command.Parameters.Add("@PlanOptionId", SqlDbType.Int).Value = model.PlanOptionId;

                var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    if (reader.GetValue(0) != DBNull.Value)
                    {
                        if (Convert.ToInt32(reader.GetValue(0).ToString()) != 0)
                        {
                            findItems.Add(Convert.ToInt32(reader.GetValue(0).ToString()));
                        }
                    }
                }

                if (findItems.Any())
                {
                    foreach (var item in findItems)
                    {
                        var getOptionAttributeGroupItem = _context.OptionAttributeGroupItems.Where(x => x.OpAttrGroupItemId == item).FirstOrDefault();

                        if (getOptionAttributeGroupItem != null)
                        {
                            getOptionAttributeGroupItem.IsActive = (actionType == "Delete") ? false : true;
                            getOptionAttributeGroupItem.UpdatedDateTime = DateTime.Now;
                            getOptionAttributeGroupItem.Updatedby = User.Identity.Name.Split('@')[0];

                            _context.OptionAttributeGroupItems.Update(getOptionAttributeGroupItem);
                        }
                        else
                        {
                            model.ErrorMessage = "Can't find that option attribute group";
                        }
                    }

                    await _context.SaveChangesAsync();
                    return model;
                }
                else
                {
                    model.ErrorMessage = (actionType == "Delete") ? "Error in deleting group" : "Error in reactivating the group";
                    return model;
                }
            }
        }

        [HttpPost]
        public async Task<MasterAttributeGroupDto> AddGroupToOptionAsync(MasterAttributeGroupDto model)
        {
            if (model != null)
            {
                if (model.IsActive == true)
                {
                    // Check to make sure there's no existing assignment
                    var find = new List<int>();

                    var conn = _configuration.GetConnectionString("ERPConnection");

                    using (var connection = new SqlConnection(conn))
                    {
                        await connection.OpenAsync();

                        var query = "select a.OP_ATTR_GROUP_ITEM_ID from [OPTION_ATTRIBUTE_GROUP_ITEM] a join [ATTR_GROUP_ASSIGNMENT] b on a.ATTR_GROUP_ASSIGNMENT_ID = b.ATTR_GROUP_ASSIGNMENT_ID join [MASTER_ATTRIBUTE_GROUP] c on b.ATTRIBUTE_GROUP_ID = c.ATTRIBUTE_GROUP_ID where a.MASTER_OPTION_ID = @MasterOptionId and b.ATTRIBUTE_GROUP_ID = @AttributeGroupId and a.IsActive = 1 and b.IsActive = 1 and c.IsActive = 1";

                        var command = new SqlCommand(query, connection);
                        command.Parameters.Add("@MasterOptionId", SqlDbType.Int).Value = model.MasterOptionId;
                        command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = model.AttributeGroupId;

                        var reader = command.ExecuteReader();

                        while (reader.Read())
                        {
                            if (reader.GetValue(0) != DBNull.Value)
                            {
                                if (Convert.ToInt32(reader.GetValue(0).ToString()) != 0)
                                {
                                    find.Add(Convert.ToInt32(reader.GetValue(0).ToString()));
                                }
                            }
                        }
                    }

                    if (!find.Any())
                    {
                        // Get all group's child
                        var data = new List<MasterAttributeGroupDto>();

                        using (var connection = new SqlConnection(conn))
                        {
                            await connection.OpenAsync();

                            var query = "SELECT ATTR_GROUP_ASSIGNMENT_ID FROM dbo.ATTR_GROUP_ASSIGNMENT WHERE ATTRIBUTE_GROUP_ID = @AttributeGroupId AND isActive = 1";
                            var command = new SqlCommand(query, connection);
                            command.Parameters.Add("@AttributeGroupId", SqlDbType.Int).Value = model.AttributeGroupId;

                            var reader = await command.ExecuteReaderAsync();

                            while (reader.Read())
                            {
                                if (reader.GetValue(0) != DBNull.Value)
                                {
                                    data.Add(new MasterAttributeGroupDto
                                    {
                                        MasterOptionId = model.MasterOptionId,
                                        AttributeGroupAssignmentId = Convert.ToInt32(reader.GetValue(0).ToString())
                                    });
                                }
                            }
                        }

                        var _getAvailablePlanOptions = _context.AvailablePlanOptions.Where(x => x.MasterOptionId == model.MasterOptionId && x.IsActive == true).ToList();

                        using (var connection = new SqlConnection(conn))
                        {
                            await connection.OpenAsync();

                            foreach (var item in data)
                            {
                                foreach (var option in _getAvailablePlanOptions)
                                {
                                    var insertQuery = "INSERT INTO dbo.OPTION_ATTRIBUTE_GROUP_ITEM (MASTER_OPTION_ID, ATTR_GROUP_ASSIGNMENT_ID, PLAN_OPTION_ID, PLAN_NUM, MASTER_PLAN_ID, CreatedBy, CreatedDateTime) VALUES (@MasterOptionId, @AttrGroupAssignmentId, @PlanOptionId, @PlanNum, @MasterPlanId, @CreatedBy, @CreatedDateTime)";

                                    var insertCommand = new SqlCommand(insertQuery, connection);
                                    insertCommand.Parameters.Add("@MasterOptionId", SqlDbType.Int).Value = item.MasterOptionId;
                                    insertCommand.Parameters.Add("@AttrGroupAssignmentId", SqlDbType.Int).Value = item.AttributeGroupAssignmentId;
                                    insertCommand.Parameters.Add("@PlanOptionId", SqlDbType.Int).Value = option.PlanOptionId;
                                    insertCommand.Parameters.Add("@PlanNum", SqlDbType.VarChar).Value = _context.MasterPlans.Where(x => x.MasterPlanId == option.MasterPlanId).Select(x => x.PlanNum).FirstOrDefault();
                                    insertCommand.Parameters.Add("@MasterPlanId", SqlDbType.Int).Value = option.MasterPlanId;
                                    insertCommand.Parameters.Add("@CreatedBy", SqlDbType.VarChar).Value = User.Identity.Name.Split('@')[0];
                                    insertCommand.Parameters.Add("@CreatedDateTime", SqlDbType.DateTime).Value = DateTime.Now;

                                    insertCommand.ExecuteNonQuery();
                                }
                            }
                        }
                    }
                    else
                    {
                        model.ErrorMessage = "There's an existing group and item already assigned to this option";
                    }
                }
                else
                {
                    model.ErrorMessage = "You need to reactivate this group";
                }

                return model;
            }
            else
            {
                var badModel = new MasterAttributeGroupDto();
                badModel.ErrorMessage = "Error in assigning group to option";
                return badModel;
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetMasterAttributeItemsAsync()
        {
            try
            {
                var returnData = new List<MasterAttributeItemDto>();

                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT ATTRIBUTE_ITEM_ID, DESCRIPTION FROM [dbo].[MASTER_ATTRIBUTE_ITEM] WHERE IsActive = 1 ORDER BY 2";
                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        var build = new MasterAttributeItemDto();
                        build.AttributeItemId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0;

                        var getDescription = string.Empty;

                        if (reader.GetValue(1) != DBNull.Value)
                        {
                            if (reader.GetValue(1).ToString() == " ")
                            {
                                getDescription = reader.GetValue(1).ToString();
                            }
                            else
                            {
                                getDescription = reader.GetValue(1).ToString().Trim();
                            }
                        }

                        build.Description = getDescription;

                        returnData.Add(build);
                    }
                }

                return new OkObjectResult(new ResponseModel<List<MasterAttributeItemDto>> { Value = returnData.OrderBy(x => x.Description).ToList(), IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Master Attribute Item"));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetDistinctMasterOptionsInAvailablePlanOptionsAsync()
        {
            try
            {
                var returnData = new List<MasterOptionDto>();
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT DISTINCT MASTER_OPTION_ID, OPTION_CODE, MODIFIED_OPTION_DESC FROM [dbo].[AVAILABLE_PLAN_OPTION] WHERE IsActive = 1 ORDER BY 2";
                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        returnData.Add(new MasterOptionDto
                        {
                            OptionId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                            OptionCode = (reader.GetValue(1) != DBNull.Value) ? reader.GetValue(1).ToString() : string.Empty,
                            OptionDesc = (reader.GetValue(2) != DBNull.Value) ? reader.GetValue(2).ToString() : string.Empty,
                            DisplayText = (reader.GetValue(1) != DBNull.Value && reader.GetValue(2) != DBNull.Value) ? string.Format("{0} - {1}", reader.GetValue(1).ToString(), reader.GetValue(2).ToString()) : string.Empty
                        });
                    }
                }

                return new OkObjectResult(new ResponseModel<List<MasterOptionDto>> { Value = returnData, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Master Options data"));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetDistinctMasterOptionsInAvailablePlanOptionsVirtualizeAsync()
        {
            try
            {
                var returnData = new List<MasterOptionDto>();
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT DISTINCT OPTION_ID, OPTION_CODE, OPTION_DESC FROM [dbo].[MASTER_OPTION] WHERE IsActive = 1 ORDER BY 3";
                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        returnData.Add(new MasterOptionDto
                        {
                            OptionId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                            OptionCode = (reader.GetValue(1) != DBNull.Value) ? reader.GetValue(1).ToString() : string.Empty,
                            OptionDesc = (reader.GetValue(2) != DBNull.Value) ? reader.GetValue(2).ToString() : string.Empty,
                            DisplayText = (reader.GetValue(1) != DBNull.Value && reader.GetValue(2) != DBNull.Value) ? string.Format("{0} - {1}", reader.GetValue(1).ToString(), reader.GetValue(2).ToString()) : reader.GetValue(1).ToString()
                        });
                    }
                }

                return new OkObjectResult(new ResponseModel<List<MasterOptionDto>> { Value = returnData, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Master Options data"));
            }
        }

        [HttpGet("{optionId}")]
        public async Task<IActionResult> GetDistinctMasterPlansInAvailablePlanOptionsAsync(int optionId)
        {
            try
            {
                var returnData = new List<OptionAndPlan>();
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT DISTINCT a.MASTER_PLAN_ID, b.PLAN_NUM, b.PLAN_NAME, a.MASTER_OPTION_ID FROM [dbo].[AVAILABLE_PLAN_OPTION] a JOIN [dbo].[MASTER_PLAN] b ON a.MASTER_PLAN_ID = b.MASTER_PLAN_ID WHERE a.IsActive = 1 AND b.IsActive = 1 and a.[MASTER_OPTION_ID] = @optionId ORDER BY 3";
                    var command = new SqlCommand(query, connection);
                    command.Parameters.Add("@optionId", SqlDbType.Int).Value = optionId;

                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        returnData.Add(new OptionAndPlan
                        {
                            MasterPlanId = (reader.GetValue(0) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(0).ToString()) : 0,
                            PlanNum = (reader.GetValue(1) != DBNull.Value) ? reader.GetValue(1).ToString() : string.Empty,
                            PlanName = (reader.GetValue(2) != DBNull.Value) ? reader.GetValue(2).ToString() : string.Empty,
                            DisplayName = (reader.GetValue(1) != DBNull.Value && reader.GetValue(2) != DBNull.Value) ? string.Format("{0} - {1}", reader.GetValue(1).ToString(), reader.GetValue(2).ToString()) : string.Empty,
                            MasterOptionId = (reader.GetValue(3) != DBNull.Value) ? Convert.ToInt32(reader.GetValue(3).ToString()) : 0
                        });
                    }
                }

                return new OkObjectResult(new ResponseModel<List<OptionAndPlan>> { Value = returnData, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Master Plans data"));
            }
        }
    }
}
