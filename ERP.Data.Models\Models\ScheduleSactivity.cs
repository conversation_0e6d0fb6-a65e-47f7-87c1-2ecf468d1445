﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ScheduleSactivity
{
    public int ScheduleAid { get; set; }

    public int? ScheduleMid { get; set; }

    public int? ScheduleId { get; set; }

    public int? SactivityId { get; set; }

    public int? SubNumber { get; set; }

    public int? Seq { get; set; }

    public int? Duration { get; set; }

    public int? LagTime { get; set; }

    public string? UserAlpha1 { get; set; }

    public string? UserAlpha2 { get; set; }

    public string? UserAlpha3 { get; set; }

    public string? UserAlpha4 { get; set; }

    public string? UserAlpha5 { get; set; }

    public string? UserAlpha6 { get; set; }

    public DateTime? UserDate1 { get; set; }

    public DateTime? UserDate2 { get; set; }

    public DateTime? UserDate3 { get; set; }

    public DateTime? UserDate4 { get; set; }

    public DateTime? UserDate5 { get; set; }

    public DateTime? UserDate6 { get; set; }

    public DateTime? BaseStartDate { get; set; }

    public DateTime? BaseEndDate { get; set; }

    public DateTime? IniStartDate { get; set; }

    public DateTime? IniEndDate { get; set; }

    public DateTime? SchStartDate { get; set; }

    public DateTime? SchEndDate { get; set; }

    public DateTime? ActualStartDate { get; set; }

    public DateTime? ActualEndDate { get; set; }

    public string? Reminder { get; set; }

    public DateTime? ReminderDate { get; set; }

    public string? ReminderText { get; set; }

    public string? Note { get; set; }

    public DateTime? NotifyDate { get; set; }

    public DateTime? UpdateDate { get; set; }

    public string? VarianceCode { get; set; }

    public string? Complete { get; set; }

    public int? SaChecklistId { get; set; }

    public int? PlusminusDays { get; set; }

    public string? AdjustReminderWithProjStart { get; set; }

    public int? Calduration { get; set; }

    public double? UserCurrency1 { get; set; }

    public double? UserCurrency2 { get; set; }

    public double? UserCurrency3 { get; set; }

    public double? UserCurrency4 { get; set; }

    public double? UserCurrency5 { get; set; }

    public double? UserCurrency6 { get; set; }

    public string? TradeCrew { get; set; }

    public int? ActualDuration { get; set; }

    public string? GrossLag { get; set; }

    public string? Excludefromschedule { get; set; }

    public string? SupplierNote { get; set; }

    public string? IsLocked { get; set; }

    public string? GenPitBudget { get; set; }

    public int? SchDatesPinned { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public DateTime? CompletedDate { get; set; }

    public string? CompletedBy { get; set; }

    public virtual Sactivity? Sactivity { get; set; }

    public virtual Schedule? Schedule { get; set; }

    public virtual ScheduleMilestone? ScheduleM { get; set; }

    public virtual ICollection<ScheduleSactivityLink> ScheduleSactivityLinks { get; set; } = new List<ScheduleSactivityLink>();

    public virtual ICollection<ScheduleSactivityPred> ScheduleSactivityPreds { get; set; } = new List<ScheduleSactivityPred>();

    public virtual Supplier? SubNumberNavigation { get; set; }

    public virtual Variance? VarianceCodeNavigation { get; set; }
}
