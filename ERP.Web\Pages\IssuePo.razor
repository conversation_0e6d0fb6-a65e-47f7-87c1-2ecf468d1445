﻿@page "/issuepo"
@inject PoService PoService
@inject BudgetService BudgetService
@inject SubdivisionService SubdivisionService
@inject TradeService TradeService
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject IJSRuntime JsRuntime
@using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage
@inject ProtectedSessionStorage ProtectedSessionStore
@inject AuthenticationStateProvider AuthenticationStateProvider
@implements IDisposable
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ReadOnly, Accounting")]
@using ERP.Data.Models
@using ERP.Web.DocumentProcessing
@using System.Collections.ObjectModel
@using Telerik.DataSource.Extensions
@using Telerik.SvgIcons;

<style type="text/css">
    /* Gutter */
    .row > * {
        padding-right: calc(var(--bs-gutter-x) * .10);
        padding-left: calc(var(--bs-gutter-x) * .10);
    }

    .k-checkbox:indeterminate, .k-checkbox.k-indeterminate {
        border-color: #f4cd64;
        color: white;
        background-color: #f4cd64;
    }

    .k-checkbox:checked, .k-checkbox.k-checked {
        border-color: #32a852;
        color: white;
        background-color: #32a852;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }

    .k-table-td { /*these settings are to prevent the 0 width columns from affecting the column height*/
        padding: 4px !important;
        font-size: 11px;
        height: 30px !important;
        white-space: nowrap !important;
        overflow: visible;
    }
</style>


<PageTitle>Purchase Orders</PageTitle>
<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="true" ShowJob="true" ShowSubdivision="true"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
<TelerikTooltip TargetSelector=".tooltip-target" />
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<TelerikLoaderContainer Visible="@IsLoading" Text="Please wait..." />
<div class="container-fluid flex">
   <div class="col-lg-12">
            <div class="card" style="background-color: #2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Issue Purchase Orders</h7>
                </div>
            </div>
        </div>

               <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item active">Issue Purchase Orders</li>
        </ol>
        <br />

    <div class="row d-flex">
        @if (displayLoadingSpinner)
        {
            <p><em>Loading...</em></p>
            <TelerikLoader></TelerikLoader>
        }
        else if(POData == null)
        {
            <p><em>Select a job to see purchase order data</em></p>
        }
        else
        {
            <div class="container-fluid">
                <div>
                    <TelerikTabStrip>
                        <TabStripTab Title="POs">
                            <div class="row">
                                <div class="col-5">
                                    <div class="card-header" style="padding:4px; margin-bottom:4px">
                                        <div style="text-align:center">
                                            <h7 class="page-title" style="font-weight:bold">PO's</h7>
                                        </div>
                                    </div>
                                    <TelerikTreeList Data="@POData"
                                    SelectionMode="@TreeListSelectionMode.Multiple"
                                    IdField="Id"
                                    ItemsField="Children"
                                    @bind-SelectedItems="@SelectedItems"
                                    @ref="@PoTreeList"
                                    ConfirmDelete="true"
                                    OnDelete="@DeleteHandler"
                                    OnRowClick="@RowSelectedHandler"
                                    OnStateInit="@((TreeListStateEventArgs<CombinedPOBudgetTreeModel> args) => OnStateInitHandler(args))"
                                    Height="800px"
                                    Width="100%">
                                        <TreeListColumns>
                                            <TreeListColumn Field="SearchTags" Width="0px"></TreeListColumn>
                                            <TreeListColumn Field="Is Issued" Visible="true" Editable="true" Width="40px">
                                                <Template>
                                                    @{
                                                        SelectedRow = context as CombinedPOBudgetTreeModel;
                                                        bool enabled = SelectedRow.PoIssueEnabled;
                                                        bool indeterminate = SelectedRow.PoIndeterminate;
                                                        <TelerikCheckBox Indeterminate="@indeterminate" OnChange="@ChangeSelected" Enabled=@enabled @bind-Value="SelectedRow.PoIsIssued"></TelerikCheckBox>
                                                    }
                                                </Template>
                                            </TreeListColumn>
                                            <TreeListColumn Field="Job Number" Expandable="true" Editable="false" Visible="true">
                                                <Template>
                                                    @{
                                                        var item = context as CombinedPOBudgetTreeModel;
                                                        if (item.Estheader != null)
                                                        {
                                                            @($"{item.JobNumber} - {item.Estheader.ReferenceTypeNavigation?.ReferenceType1} - {item.Estheader.ReferenceNumber} - {item.Estheader.EstheaderId}")
                                                        }
                                                        else if (item.Estoption != null && item.Estactivity == null && item.Estdetail == null)
                                                        {
                                                            if (item.HasChildren && item.Children.Any(x => (x.Estactivity != null && x.Estactivity.SelectedVendor == null) || (x.HasChildren && x.Children.Any(y => y.Estdetail != null && !string.IsNullOrWhiteSpace(y.Estdetail.Errors)))))
                                                            {
                                                                <TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Warning" Icon="@FontIcon.WarningTriangle" />
                                                            }
                                                            @($"{item.Estoption.OptionNumber} - {item.Estoption.OptionDesc}")
                                                        }
                                                        else if (item.JobNumber != null && item.ReleaseCode == null && item.Estactivity == null)
                                                        {
                                                            @($"{item.JobNumber}: ")
                                                        }
                                                        else if (item.ReleaseCode != null && item.Estactivity == null)
                                                        {
                                                            @($"Release: {item.ReleaseCode} - ")
                                                        }
                                                        else if (item.Estactivity != null && item.Estdetail == null)
                                                        {
                                                            if (item.Estactivity.SelectedVendor == null)
                                                            {
                                                                <span class="tooltip-target" title="Vendor Blocked or No Vendor Selected"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Error" Icon="@FontIcon.WarningTriangle" /></span>
                                                            }
                                                            if (item.Estactivity.VendorBlocked == true || item.Estactivity.VendorNoInsurance == true)
                                                            {
                                                                <span class="tooltip-target" title="Vendor Blocked or Insurance Expired"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Error" Icon="@FontIcon.WarningTriangle" /></span>
                                                            }
                                                            if (item.HasChildren && item.Children.Any(x => x.Estdetail != null &&  !string.IsNullOrWhiteSpace(x.Estdetail.Errors)))
                                                            {
                                                                <span class="tooltip-target" title="Items have Errors"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Warning" Icon="@FontIcon.WarningTriangle" /></span>
                                                            }
                                                            @($"{item.Estactivity.BomClass}")
                                                        }
                                                        else if (item.Poheader != null)
                                                        {

                                                            if (item.Poheader.Postatus == 5)//cancelled
                                                            {
                                                                <span class="tooltip-target" title="Cancelled"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Error" Icon="@FontIcon.XOutline" /></span>
                                                            }
                                                            else if (item.Poheader.Postatus == 2)//sent
                                                            {
                                                                <span class="tooltip-target" title="Sent"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Warning" Icon="@FontIcon.ArrowRight" /></span>
                                                            }
                                                            else if (item.Poheader.Postatus == 1)//issued
                                                            {
                                                                <span class="tooltip-target" title="Issued"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Primary" Icon="@FontIcon.Plus" /></span>
                                                            }
                                                            else if (item.Poheader.Postatus == 4)//approved
                                                            {
                                                                <span class="tooltip-target" title="Approved"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Success" Icon="@FontIcon.Check" /></span>
                                                            }
                                                            @($"Purchase Order: {item.Poheader.Ponumber}")

                                                        }
                                                        else if (item.Estdetail != null)
                                                        {
                                                            if (!string.IsNullOrWhiteSpace(item.Estdetail.Errors))
                                                            {
                                                                <span class="tooltip-target" title="Item has Errors"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Warning" Icon="@FontIcon.WarningTriangle" /></span>
                                                            }

                                                            if (string.IsNullOrEmpty(item.Estdetail?.Estoption?.OptionDesc))
                                                            {
                                                                @($"{item.Estdetail?.Estoption?.OptionNumber} - {item.Estdetail?.ItemDesc}")
                                                            }
                                                            else
                                                            {
                                                                @($"{item.Estdetail?.Estoption?.OptionDesc}")
                                                            }
                                                        }
                                                    }
                                                </Template>
                                            </TreeListColumn>
                                            <TreeListColumn Field="PoheaderId" Visible="false"></TreeListColumn>
                                            <TreeListColumn Field="PodetailoptionsId" Visible="false"></TreeListColumn>
                                            <TreeListCommandColumn>
                                                @{
                                                    var row = context as CombinedPOBudgetTreeModel;
                                                    if (row.Estdetail != null && AllowEdit)
                                                    {
                                                        <TreeListCommandButton Title="Copy Item" Class="tooltip-target k-button-success" OnClick="@CopyItem" Icon="@FontIcon.Copy"></TreeListCommandButton>
                                                        if(row.Estdetail.Podetail == null || row.Estdetail.Podetail.PodetailId == 0)
                                                        {
                                                            <TreeListCommandButton OnClick="@RefreshCosts">Refresh Costs</TreeListCommandButton>
                                                        }

                                                    }
                                                    if (!row.PoIsIssued && row.Estdetail != null && AllowEdit)
                                                    {
                                                        <TreeListCommandButton Title="Delete" Class="tooltip-target k-button-danger" Command="Delete" Icon="@FontIcon.Trash"></TreeListCommandButton>                                                       
                                                    }
                                                    if (row.PoIssueEnabled && row.Estdetail != null && AllowEdit)
                                                    {                                                       
                                                        <TreeListCommandButton Title="Issue POs" Class="tooltip-target k-button-success" OnClick="@IssuePurchaseOrder">Issue</TreeListCommandButton>
                                                    }
                                                    if (row.Poheader != null)
                                                    {
                                                        <TreeListCommandButton Title="Download PDF" Class="tooltip-target k-button-success" Icon="@FontIcon.Download" OnClick="@DownloadPdf"></TreeListCommandButton>
                                                        if(AllowEdit)
                                                        {
                                                            <TreeListCommandButton Title="Email PO to Vendor" Class="tooltip-target k-button-success" Icon="@FontIcon.Envelop" OnClick="@EmailVendor"></TreeListCommandButton>
                                                        }                                                        
                                                    }
                                                    if (AllowEdit && row.Poheader != null && row.Poheader.PostatusNavigation != null && (row.Poheader.PostatusNavigation.Postatus1 == "Issued" || row.Poheader.PostatusNavigation.Postatus1 == "Pending Payment Approval"))
                                                    {
                                                        <TreeListCommandButton Title="Delete" Class="tooltip-target k-button-danger" Icon="@FontIcon.Trash" OnClick="@DeletePo"></TreeListCommandButton>
                                                        <TreeListCommandButton Title="Cancel" Class="tooltip-target" Icon="@FontIcon.X" OnClick="@CancelPo"></TreeListCommandButton>
                                                    }
                                                    if (AllowEdit && row.Poheader != null && row.Poheader.PostatusNavigation != null && row.Poheader.PostatusNavigation.Postatus1 == "Sent")
                                                    {
                                                        <TreeListCommandButton Title="Cancel" Class="tooltip-target" Icon="@FontIcon.X" OnClick="@CancelPo"></TreeListCommandButton>
                                                    }
                                                    if (row.Estoption != null && AllowEdit)
                                                    {
                                                        if (!row.PoIsIssued && !row.IsIssued && !row.PoIndeterminate && !row.Indeterminate && row.IssueEnabled && row.PoIssueEnabled)
                                                        {
                                                            //only allow delete if there are no issued pos or budgets
                                                            <TreeListCommandButton Title="Delete" Command="Delete" Class="tooltip-target k-button-danger" Icon="@FontIcon.Trash"></TreeListCommandButton>
                                                        }                                               
                                                        <TreeListCommandButton Title="Add Item" Class="tooltip-target k-button-add" OnClick="@ShowAddItemToBudget" Icon="@FontIcon.Plus"></TreeListCommandButton>
                                                        <TreeListCommandButton OnClick="@RefreshSupplier">Refresh Supplier</TreeListCommandButton>
                                                        <TreeListCommandButton OnClick="@RefreshCosts">Refresh Costs</TreeListCommandButton>
                                                    }
                                                    if( row.Estactivity != null && row.Estdetail == null)
                                                    {
                                                        if (!row.IsIssued && AllowEdit)
                                                        {
                                                            <TreeListCommandButton OnClick="@RefreshSupplier">Refresh Supplier</TreeListCommandButton>
                                                            <TreeListCommandButton OnClick="@RefreshCosts">Refresh Costs</TreeListCommandButton>
                                                        }
                                                    }
                                                    if (row.JobNumber != null)
                                                    {
                                                        // <TreeListCommandButton Title="Add Item" Class="tooltip-target" OnClick="@ShowAddItemToBudget" Icon="@FontIcon.Plus"></TreeListCommandButton>
                                                    }
                                                }
                                            </TreeListCommandColumn>
                                        </TreeListColumns>
                                        <TreeListToolBarTemplate>
                                            <TreeListSearchBox />
                                            @if (AllowEdit)
                                            {
                                                <TreeListCommandButton Title="Issue Selected POs" Class="tooltip-target k-button-success" OnClick="@IssueSelectedPurchaseOrders">Issue</TreeListCommandButton>
                                                <TreeListCommandButton Title="Refresh Selected Supplier" Class="tooltip-target" OnClick="@RefreshSupplier">Refresh Supplier</TreeListCommandButton>
                                                <TreeListCommandButton Title="Refresh Selected Costs" Class="tooltip-target" OnClick="@RefreshCosts">Refresh Costs</TreeListCommandButton>
                                            }
                                            <TelerikDropDownList Data="@SortByOptions"
                                            @bind-Value="SelectedSort"
                                            OnChange="@ChangeSortHandler"></TelerikDropDownList>
                                            @* <TreeListCommandButton Title="Add Changes" Class="tooltip-target k-button-add" Command="AddEstimate" Icon="@FontIcon.Plus" OnClick="@ShowAddEstimate">Add Estimate</TreeListCommandButton> *@
                                            @*   <TreeListCommandButton Command="IssueBudgets" OnClick="@IssueBudgets">Issue Budgets</TreeListCommandButton>
                                        <TreeListCommandButton Command="AddItems" OnClick="@ShowAddItemToBudget" >Add Items</TreeListCommandButton> *@
                                        </TreeListToolBarTemplate>
                                    </TelerikTreeList>
                                </div>
                                <div class="col-lg-7">
                                    <div class="card-header" style="padding:4px; margin-bottom:4px">
                                        <div style="text-align:center">
                                            <h7 class="page-title" style="font-weight:bold">Details</h7>
                                        </div>
                                    </div>
                                    <div class="card" >
                                        <div class="card-body">

                                            @if (EditedRow.Poheader != null)
                                            {
                                                <EditForm Model="@EditedRow" OnValidSubmit="@HandleValidSubmit">
                                                    <DataAnnotationsValidator />
                                                    <ValidationSummary />
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">PO Number: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Poheader.Ponumber" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">VPO Number: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Enabled="false" @bind-Value="@EditedRow.Poheader.VpoNumber" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Release Code: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Poheader.Releasecode" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Description: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Poheader.Podescription" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">PO Status: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                @if (EditedRow.Poheader.PostatusNavigation != null)
                                                                {
                                                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Poheader.PostatusNavigation.Postatus1" Width="100%"></TelerikTextBox>
                                                                }
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Supplier: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Poheader.SubNumberNavigation.SubName" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Supplier Contact: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Poheader.SubNumberNavigation.SubContact" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>                                                                                                                                                            
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Total Amount: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.Poheader.Pototal" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Date Issued: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikDateInput Enabled="false" @bind-Value="@EditedRow.Poheader.Podateissued" Width="100%"></TelerikDateInput>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Date Sent: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikDateInput Enabled="false" @bind-Value="@EditedRow.Poheader.Podateprinted" Width="100%"></TelerikDateInput>
                                                            </div>
                                                        </div>
                                                    </div>                                                   
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Date Completed: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikDateInput Enabled="false" @bind-Value="@EditedRow.Poheader.TaskCompleteDate" Width="100%"></TelerikDateInput>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Date Fully Approved: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikDateInput Enabled="false" @bind-Value="@EditedRow.Poheader.Podateapproved" Width="100%"></TelerikDateInput>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @* <div>
                                            <div class="row p-1">
                                            <div class="col-lg-3">
                                            <label class="form-label">Paid To Date Amount: </label>
                                            </div>
                                            <div class="col-lg-9">
                                            <p>@(EditedRow.Poheader.Pototal.ToString())  </p>@((EditedRow.Poheader.Pototal - EditedRow.Poheader.PendInvAmount).ToString())
                                            </div>
                                            </div>
                                            </div> *@
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Pending Invoice Amount: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.Poheader.PendInvAmount" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Pending Invoice Date: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikDateInput Enabled="false" @bind-Value="@EditedRow.Poheader.PendInvPayDate" Width="100%"></TelerikDateInput>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">VPO Invoice Number: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Poheader.VpoVendorinvoice" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Last Payment Amount: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.Poheader.LastAcceptInvAmt" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Last Payment Date: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikDateInput Enabled="false" @bind-Value="@EditedRow.Poheader.LastAcceptInvDte" Width="100%"></TelerikDateInput>
                                                            </div>
                                                        </div>
                                                    </div>                                                   
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Can Cancel PO: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                @{
                                                                    bool CanCancel = EditedRow.Poheader.Podateapproved == null;
                                                                    <TelerikCheckBox Enabled="false" @bind-Value="@CanCancel"></TelerikCheckBox>
                                                                }
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Can Delete PO: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                @{
                                                                    bool CanDelete = EditedRow.Poheader.PostatusNavigation?.Postatus1 == "Issued" ? true : false;
                                                                    <TelerikCheckBox Enabled="false" @bind-Value="@CanDelete"></TelerikCheckBox>
                                                                }

                                                            </div>
                                                        </div>
                                                    </div>

                                                    @*  <button type="submit" class="btn btn-primary">Update</button> *@
                                                </EditForm>
                                            }
                                            else if (EditedRow.Estdetail != null)
                                            {
                                                <EditForm Model="@EditedRow" OnValidSubmit="@HandleValidSubmit">
                                                    <DataAnnotationsValidator />
                                                    <ValidationSummary />
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Phase:  </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.PhaseCode" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Item Number: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.ItemNumber" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Item Description: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="@EditedRow.PoIssueEnabled" @bind-Value="@EditedRow.Estdetail.ItemDesc" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Item Notes: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="@EditedRow.PoIssueEnabled" @bind-Value="@EditedRow.Estdetail.ItemNotes" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Unit: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="@EditedRow.PoIssueEnabled" @bind-Value="@EditedRow.Estdetail.OrdrUnit" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Qty: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Enabled="@EditedRow.PoIssueEnabled" OnChange="@ChangeUnitPriceQty" @bind-Value="@EditedRow.Estdetail.OrigCommittedQty" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Unit Price: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Format="C" Enabled="@EditedRow.PoIssueEnabled" OnChange="@ChangeUnitPriceQty" @bind-Value="@EditedRow.Estdetail.OrgPrice" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Amount: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.Estdetail.OrigCommittedAmount" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Lump Sum: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikCheckBox Enabled="@EditedRow.PoIssueEnabled" @bind-Value="@EditedRow.Estdetail.BoolLump"></TelerikCheckBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @* <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Use Estimate: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikCheckBox Enabled="false" @bind-Value="@EditedRow.Estdetail.BoolUseEstPrice"></TelerikCheckBox>
                                                            </div>
                                                        </div>
                                                    </div> *@
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label"> Errors: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.DisplayErrors" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Warnings: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.DisplayWarnings" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>                                                  
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Estimate Number: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Enabled="false" @bind-Value="@EditedRow.EstimateNumber" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Extra Number: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.ExtraNumber" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Release Code: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estactivity.Releasecode" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Activity: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estactivity.BomClass" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>                                                    
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Job Cost Code: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikDropDownList Data="@AllJobCostCodes"
                                                                @bind-Value="@EditedRow.Estdetail.JcPhase"
                                                                TextField="DropdownDescription"
                                                                ValueField="CostCode"
                                                                DefaultText="Select Cost Code"
                                                                Width="100%"
                                                                PageSize="40"
                                                                Filterable="true"
                                                                ItemHeight="30"
                                                                Enabled="@EditedRow.IssueEnabled"
                                                                FilterOperator="StringFilterOperator.Contains"
                                                                ScrollMode="@DropDownScrollMode.Virtual">
                                                                    <DropDownListSettings>
                                                                        <DropDownListPopupSettings Height="600px"></DropDownListPopupSettings>
                                                                    </DropDownListSettings>
                                                                </TelerikDropDownList>
                                                                @*  <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.JcPhase" Width="100%"></TelerikTextBox> *@
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Job Cost Category: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikDropDownList Data="@AllJobCostCategories"
                                                                @bind-Value="@EditedRow.Estdetail.JcCategory"
                                                                TextField="DropdownDescription"
                                                                ValueField="Category"
                                                                DefaultText="Select Category"
                                                                Width="100%"
                                                                PageSize="40"
                                                                Filterable="true"
                                                                ItemHeight="30"
                                                                Enabled="@EditedRow.IssueEnabled"
                                                                FilterOperator="StringFilterOperator.Contains"
                                                                ScrollMode="@DropDownScrollMode.Virtual">
                                                                    <DropDownListSettings>
                                                                        <DropDownListPopupSettings Height="600px"></DropDownListPopupSettings>
                                                                    </DropDownListSettings>
                                                                </TelerikDropDownList>
                                                                @*  <TelerikTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@EditedRow.Estdetail.JcCategory" Width="100%"></TelerikTextBox> *@
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Tracking Variance: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikCheckBox Enabled="false" @bind-Value="@EditedRow.Estdetail.TrackingVariance"></TelerikCheckBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Variance JC Category: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="@EditedRow.PoIssueEnabled" @bind-Value="@EditedRow.Estdetail.VarianceJcCategory" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Variance Amount: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                @{
                                                                    var variance = EditedRow.Estdetail.OrigCommittedAmount - EditedRow.Estdetail.Amount;
                                                                    <TelerikNumericTextBox Enabled="@EditedRow.IssueEnabled" @bind-Value="@variance" Width="100%"></TelerikNumericTextBox>
                                                                }
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Budget Amount: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.Amount" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @if (EditedRow.Estdetail.Podetail != null && EditedRow.Estdetail.Podetail.Poheader != null)
                                                    {
                                                        <div>
                                                            <div class="row p-1">
                                                                <div class="col-lg-3">
                                                                    <label class="form-label">PO Number: </label>
                                                                </div>
                                                                <div class="col-lg-9">
                                                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.Podetail.Poheader.Ponumber" Width="100%"></TelerikTextBox>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <div class="row p-1">
                                                                <div class="col-lg-3">
                                                                    <label class="form-label">PO Status: </label>
                                                                </div>
                                                                <div class="col-lg-9">
                                                                    @if (EditedRow.Estdetail.Podetail.Poheader.PostatusNavigation != null)
                                                                    {
                                                                        <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.Podetail.Poheader.PostatusNavigation.Postatus1" Width="100%"></TelerikTextBox>
                                                                    }                                                                    
                                                                </div>
                                                            </div>
                                                        </div>
                                                        @*                                                         <div>
                                                            <div class="row p-1">
                                                                <div class="col-lg-3">
                                                                    <label class="form-label">VPO Number: </label>
                                                                </div>
                                                                <div class="col-lg-9">
                                                                    <TelerikNumericTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.Podetail.Poheader.VpoNumber" Width="100%"></TelerikNumericTextBox>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <div class="row p-1">
                                                                <div class="col-lg-3">
                                                                    <label class="form-label">VPO Invoice Number: </label>
                                                                </div>
                                                                <div class="col-lg-9">
                                                                    <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estdetail.Podetail.Poheader.VpoVendorinvoice" Width="100%"></TelerikTextBox>
                                                                </div>
                                                            </div>
                                                        </div> *@
                                                    }


                                                    <br />
                                                    @if (EditedRow.PoIssueEnabled && AllowEdit)
                                                    {
                                                        <button type="submit" class="btn btn-primary">Update</button>
                                                        <TelerikButton ButtonType="@ButtonType.Button" OnClick="@CancelChanges" Class="btn btn-primary">Cancel</TelerikButton>
                                                    }
                                                </EditForm>
                                            }
                                            else if (EditedRow.Estheader != null)
                                            {
                                                <EditForm Model="@EditedRow" OnValidSubmit="@HandleValidSubmit">
                                                    <DataAnnotationsValidator />
                                                    <ValidationSummary />
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Estimate Number: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Enabled="false" @bind-Value="@EditedRow.Estheader.EstimateNumber" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Estimate Description: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox @bind-Value="@EditedRow.Estheader.EstimateDescPe" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Estimate Source : </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikDropDownList Data="@EstimateSources" TextField="EstsourceDesc" ValueField="EstsourceId" @bind-Value="@EditedRow.Estheader.EstimateSource" Width="100%"></TelerikDropDownList>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @* <div>
                                            <label class="form-label">Associated File: </label>
                                            <TelerikTextBox @bind-Value="@SelectedRow.ReferenceNumber" Width="200px"></TelerikTextBox>
                                            </div>*@
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Document Number: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox @bind-Value="@EditedRow.Estheader.ReferenceNumber" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Document Type: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikDropDownList Data="@ReferenceTypes" TextField="ReferenceType1" ValueField="ReferenceTypeId" @bind-Value="@EditedRow.Estheader.ReferenceType" Width="100%"></TelerikDropDownList>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Document Desc: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox @bind-Value="@EditedRow.Estheader.ReferenceDesc" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Selling Price: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Format="C" @bind-Value="@EditedRow.Estheader.EstimateSalesPrice" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Estimator: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox @bind-Value="@EditedRow.Estheader.Estimator" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>                                                    
                                                    @if (EditedRow.PoIssueEnabled && AllowEdit)
                                                    {
                                                        <button type="submit" class="btn btn-primary">Update</button>
                                                        <TelerikButton ButtonType="@ButtonType.Button" OnClick="@CancelChanges" Class="btn btn-primary">Cancel</TelerikButton>
                                                    }
                                                </EditForm>
                                            }
                                            else if (EditedRow.Estoption != null && EditedRow.Estactivity == null && EditedRow.Estdetail == null)
                                            {
                                                <EditForm Model="@EditedRow" OnValidSubmit="@HandleValidSubmit">
                                                    <DataAnnotationsValidator />
                                                    <ValidationSummary />
                                                    <div class="row p-1">
                                                        <div class="col-lg-3">
                                                            <label class="form-label">Option Number: </label>
                                                        </div>
                                                        <div class="col-lg-9">
                                                            <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.OptionNumber" Width="100%"></TelerikTextBox>
                                                        </div>
                                                    </div>
                                                    <div class="row p-1">
                                                        <div class="col-lg-3">
                                                            <label class="form-label">Option Description: </label>
                                                        </div>
                                                        <div class="col-lg-9">
                                                            <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.OptionDesc" Width="100%"></TelerikTextBox>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Option Qty: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.OptionQty" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Option Selling Price: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Enabled="false" Format="C" @bind-Value="@EditedRow.Estoption.OptionSalesPrice" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Option Total Cost: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.TotalCost" Width="100%"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Option Sales Notes: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox @bind-Value="@EditedRow.Estoption.OptionNotes" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Option Sales Selections: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextArea Enabled="false" @bind-Value="@EditedRow.Estoption.OptionSelections" Width="100%"></TelerikTextArea>
                                                            </div>
                                                        </div>
                                                    </div>                                                                                                     
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Base House Code: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.ElevationCode" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Base House Desc: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estoption.ElevationDesc" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>                                                    
                                                    <br />
                                                    @if (EditedRow.PoIssueEnabled && AllowEdit)
                                                    {
                                                        <button type="submit" class="btn btn-primary">Update</button>
                                                        <TelerikButton ButtonType="@ButtonType.Button" OnClick="@CancelChanges" Class="btn btn-primary">Cancel</TelerikButton>
                                                    }
                                                </EditForm>
                                            }
                                            else if (EditedRow.Estactivity != null && EditedRow.Estdetail == null)
                                            {
                                                <EditForm Model="@EditedRow" OnValidSubmit="@HandleValidSubmit">
                                                    <DataAnnotationsValidator />
                                                    <ValidationSummary />
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Release Code: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estactivity.Releasecode" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Activity: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikTextBox Enabled="false" @bind-Value="@EditedRow.Estactivity.BomClass" Width="100%"></TelerikTextBox>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Selected Supplier: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikDropDownList Enabled="@EditedRow.PoIssueEnabled" Data="@AllSuppliers"
                                                                @bind-Value="@EditedRow.Estactivity.SelectedVendor"
                                                                TextField="SubName"
                                                                ValueField="SubNumber"
                                                                DefaultText="Select Supplier"
                                                                Width="100%"
                                                                PageSize="40"
                                                                Filterable="true"
                                                                ItemHeight="30"
                                                                ScrollMode="@DropDownScrollMode.Virtual">
                                                                    <DropDownListSettings>
                                                                        <DropDownListPopupSettings Height="600px"></DropDownListPopupSettings>
                                                                    </DropDownListSettings>
                                                                </TelerikDropDownList>
                                                            </div>
                                                        </div>
                                                    </div>                                                   
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Activity Total: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikNumericTextBox Format="C" Enabled="false" @bind-Value="@EditedRow.ActivityTotal" Width="200px"></TelerikNumericTextBox>
                                                            </div>
                                                        </div>
                                                    </div>                                                 
                                                    <div>
                                                        <div class="row p-1">
                                                            <div class="col-lg-3">
                                                                <label class="form-label">Default Supplier: </label>
                                                            </div>
                                                            <div class="col-lg-9">
                                                                <TelerikDropDownList Data="@AllSuppliers"
                                                                @bind-Value="@EditedRow.Estactivity.DefaultVendor"
                                                                TextField="SubName"
                                                                ValueField="SubNumber"
                                                                DefaultText="Select Supplier"
                                                                Width="100%"
                                                                PageSize="40"
                                                                Filterable="true"
                                                                ItemHeight="30"
                                                                Enabled="false"
                                                                ScrollMode="@DropDownScrollMode.Virtual">
                                                                    <DropDownListSettings>
                                                                        <DropDownListPopupSettings Height="600px"></DropDownListPopupSettings>
                                                                    </DropDownListSettings>
                                                                </TelerikDropDownList>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <br />
                                                    @if (EditedRow.PoIssueEnabled && AllowEdit)
                                                    {
                                                        <button type="submit" class="btn btn-primary">Update</button>
                                                        <TelerikButton ButtonType="@ButtonType.Button" OnClick="@CancelChanges" Class="btn btn-primary">Cancel</TelerikButton>
                                                    }
                                                </EditForm>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </TabStripTab>
                        <TabStripTab Title="Issued POs">
                            <h4 class="page-title mt-2">Issued Purchase Orders</h4>
                            @* <TelerikContextMenu @ref="@ContextMenuRef" Data="@MenuItems" OnClick="@((ContextMenuItem item) => OnItemClick(item))"></TelerikContextMenu> *@
                            <TelerikGrid Data="@IssuedPOsData"
                            @ref="@IssuedPOsGrid"                                        
                            Sortable="true">
                                <GridToolBarTemplate>
                                    <GridSearchBox DebounceDelay="200" Width="250px"></GridSearchBox>
                                    <TelerikDropDownList Data="@( new List<string>() { "Active", "Canceled" } )" @bind-Value="@ActiveOrCanceled" OnChange="@ToggleCanceledIssuedPOs" Width="110px" />
                                </GridToolBarTemplate>
                                <GridColumns>
                                    <GridCommandColumn>
                                        @{
                                            var row = context as PoheaderDto;
                                            <GridCommandButton Title="Download" Command="Download" OnClick="@Download" Icon="@FontIcon.Download"></GridCommandButton>
                                           @if (AllowEdit) 
                                           {
                                               <GridCommandButton Title="Email" Command="Email" OnClick="@Email" Icon="@FontIcon.Envelop" Class="k-button-success"></GridCommandButton>
                                           }
                                            @if (AllowEdit && row != null && row.PostatusNavigation != null && (row.PostatusNavigation.Postatus1 == "Sent" || row.PostatusNavigation.Postatus1 == "Pending" || row.PostatusNavigation.Postatus1 == "Pending Payment Approval"))
                                            {
                                                <GridCommandButton Title="Cancel" Class="tooltip-target" Icon="@FontIcon.X" OnClick="@CancelPo"></GridCommandButton>
                                            }
                                        }
                                    </GridCommandColumn>
                                    <GridColumn Field="Releasecode" Title="Release Code" />
                                    <GridColumn Field="Ponumber" Title="PO Number" />
                                    <GridColumn Field="VpoNumber" Title="VPO Number" />
                                    <GridColumn Field="VpoVendorinvoice" Title="VPO Invoice" />
                                    <GridColumn Field="SubNumberNavigation.SubName" Title="Supplier Name" />
                                    <GridColumn Field="Podescription" Title="Description" />
                                    <GridColumn Field="Issuedby" Title="Issued By" />
                                    <GridColumn Field="Podateissued" Title="Date Issued" DisplayFormat="{0:MM/dd/yy}" />
                                    <GridColumn Field="Podateprinted" Title="Date Sent" DisplayFormat="{0:MM/dd/yy}" />
                                    <GridColumn Field="TaskCompleteBy" Title="Approved By" />
                                    <GridColumn Field="Podateapproved" Title="Date Fully Approved" DisplayFormat="{0:MM/dd/yy}" />
                                    <GridColumn Field="Pototal" Title="Net" DisplayFormat="{0:C2}" />                                   
                                    @if (ActiveOrCanceled == "Canceled")
                                    {
                                        <GridColumn Field="Cancelledby" Title="Canceled By" />
                                        <GridColumn Field="Podatecancelled" Title="Canceled Date" DisplayFormat="{0:MM/dd/yy}" />
                                    }
                                </GridColumns>
                            </TelerikGrid>
                        </TabStripTab>
                    </TelerikTabStrip>

                </div>
            </div>
        }
    </div>
</div>


<ERP.Web.Components.AddItemToBudget SelectedSubdivisionId="@SelectedSubdivisionId" SelectedEstOptionId="@SelectedEstOptionId" SelectedHeaderId="@SelectedHeaderId" @ref="AddItemToBudgetModal" HandleAddSubmit="HandleValidAddItemsSubmit"></ERP.Web.Components.AddItemToBudget>
<ERP.Web.Components.AddEstimate SelectedSubdivisionId="@SelectedSubdivisionId" @ref="AddEstimateModal" JobNumber="@JobSelected" HandleAddSubmit="HandleValidAddEstimateSubmit"></ERP.Web.Components.AddEstimate>
<ERP.Web.Components.SelectDate @ref="SelectDateModal" MessageToUser="Select Date To Refresh Costs" HandleDateSelected="HandleRefreshCostsSubmit"></ERP.Web.Components.SelectDate>

@code {
    // private List<JobDto>? AllJobs { get; set; }
    // private List<string>? AllJobsForAutoSelect { get; set; }

    public PoEmailModel? EmailMessage { get; set; } = new PoEmailModel();
    public List<JccategoryDto>? AllJobCostCategories { get; set; }
    public List<JccostcodeDto>? AllJobCostCodes { get; set; }
    private string? SelectedJob { get; set; }
    private string? JobSelected { get; set; }
    public int? SelectedSubdivisionId { get; set; }
    public CombinedPOBudgetTreeModel? EditedRow { get; set; } = new CombinedPOBudgetTreeModel();
    public CombinedPOBudgetTreeModel? SelectedRow { get; set; } = new CombinedPOBudgetTreeModel();
    public CombinedPOBudgetTreeModel? CopySelectedRow { get; set; } = new CombinedPOBudgetTreeModel();
    public CombinedPOBudgetTreeModel? RefreshCostClickedRow { get; set; }
    public bool selectedRowIndeterminate { get; set; } = false;
    public List<CombinedPOBudgetTreeModel>? SelectedRows { get; set; } = new List<CombinedPOBudgetTreeModel>();
    public IEnumerable<CombinedPOBudgetTreeModel>? SelectedItems { get; set; } = Enumerable.Empty<CombinedPOBudgetTreeModel>();
    private List<PoheaderDto>? POHeaders { get; set; }
    private List<PoheaderDto>? IssuedPOsData { get; set; }
    private ObservableCollection<CombinedPOBudgetTreeModel>? POData { get; set; }
    private TelerikGrid<PoheaderDto>? IssuedPOsGrid { get; set; }
    private TelerikTreeList<CombinedPOBudgetTreeModel>? PoTreeList { get; set; }
    public List<SupplierDto> AllSuppliers {get; set; }
    public List<CombinedPOBudgetTreeModel>? SelectedItemsToIssue { get; set; } = new List<CombinedPOBudgetTreeModel>();
    private bool displayLoadingSpinner { get; set; } = false;
    private bool IsLoading { get; set; } = false;
    public List<string>? SortByOptions { get; set; } = new List<string> { "Release/Activity", "Estimate/Option/Activity" };
    public string? SelectedSort { get; set; } = "Release/Activity";
    public string ActiveOrCanceled { get; set; } = "Active";
    public bool ShowOptionSort {get; set; } = false;
    public bool ShowReleaseSort {get; set; } = true; 
    private List<ReferenceTypeDto>? ReferenceTypes { get; set; }
    private List<EstimateSourceDto>? EstimateSources { get; set; }
    public int? SelectedEstOptionId { get; set; }
    public int? SelectedHeaderId { get; set; }
    public ERP.Web.Components.AddItemToBudget? AddItemToBudgetModal { get; set; }
    public ERP.Web.Components.AddEstimate? AddEstimateModal { get; set; }
    public ERP.Web.Components.SelectDate? SelectDateModal { get; set; }

    private bool AllowEdit { get; set; } = true;

    public PoheaderDto ClickedIssuedPORow { get; set; }
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }


    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        // SubdivisionJobPickService.OnChange += JobChangedHandler;
        SubdivisionJobPickService.OnChanged += JobChangedHandler;
        var suppliersTask = PoService.GetSuppliersAsync(IncludeBlocked: false);
        var refTypesTask = BudgetService.GetReferenceTypesAsync();
        var estSourceTask = BudgetService.GetEstimateSourcesAsync();
        var jcCostCodesTask = PoService.GetJcCostCodesAsync();
        var jcCategoriesTask = PoService.GetJccategoriesAsync();
        await Task.WhenAll(new Task[] {suppliersTask, refTypesTask, estSourceTask, jcCategoriesTask, jcCostCodesTask });
        AllJobCostCategories = jcCategoriesTask.Result.Value;
        AllJobCostCodes = jcCostCodesTask.Result.Value;
        AllSuppliers = suppliersTask.Result.Value;
        ReferenceTypes = refTypesTask.Result.Value;
        EstimateSources = estSourceTask.Result.Value;
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
        if (SubdivisionJobPickService.JobNumber != null)
        {
            displayLoadingSpinner = true;
            JobSelected = SubdivisionJobPickService.JobNumber;
            var getJobTask = SubdivisionService.GetLotAsync(JobSelected);
            var poDataTask = SelectedSort == "Release/Activity" ? PoService.GetPosForJobAsync(JobSelected) : BudgetService.GetBudgetByJobAsync(JobSelected);
            var poHeadersTask = PoService.GetPoHeadersForJobAsync(JobSelected);
            await Task.WhenAll(new Task[] { poDataTask, poHeadersTask, getJobTask });
            POHeaders = poHeadersTask.Result.Value;
            SelectedSubdivisionId = getJobTask.Result.Value.SubdivisionId;
            IssuedPOsData = ActiveOrCanceled == "Canceled" ? POHeaders.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                        POHeaders.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();

            if (SelectedSort == "Release/Activity")
            {
                var getData = poDataTask.Result.Value;
                POData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData);
            }
            else
            {
                var getData = poDataTask.Result.Value;
                var removedCustEsts = getData.Where(x => x.Estheader.EstimateDescPe != "Pending/Custom Estimate");
                POData = new ObservableCollection<CombinedPOBudgetTreeModel>(removedCustEsts);
            }
            SelectedRows = new List<CombinedPOBudgetTreeModel>();
            SelectedItems = Enumerable.Empty<CombinedPOBudgetTreeModel>();
            EditedRow = new CombinedPOBudgetTreeModel();
            displayLoadingSpinner = false;
        }
    }
    async Task JobChangedHandler()
    {
        displayLoadingSpinner = true;
        StateHasChanged();
        var selected = SubdivisionJobPickService.JobNumber;
        if (JobSelected != selected)//check if actual change
        {
            JobSelected = selected;
            var getJobTask = SubdivisionService.GetLotAsync(JobSelected);
            var poDataTask = SelectedSort == "Release/Activity" ? PoService.GetPosForJobAsync(JobSelected) : BudgetService.GetBudgetByJobAsync(JobSelected);
            var poHeadersTask = PoService.GetPoHeadersForJobAsync(JobSelected);
            await Task.WhenAll(new Task[] { poDataTask, poHeadersTask, getJobTask });
            SelectedSubdivisionId = getJobTask.Result.Value.SubdivisionId;
            var getData = poDataTask.Result.Value;
            POHeaders = poHeadersTask.Result.Value;
            IssuedPOsData = ActiveOrCanceled == "Canceled" ? POHeaders.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                        POHeaders.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();

            if (SelectedSort == "Release/Activity")
            {
                POData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData);
            }
            else
            {
                var removedCustEsts = getData.Where(x => x.Estheader.EstimateDescPe != "Pending/Custom Estimate");
                POData = new ObservableCollection<CombinedPOBudgetTreeModel>(removedCustEsts);
            }
            SelectedRows = new List<CombinedPOBudgetTreeModel>();
            SelectedItems = Enumerable.Empty<CombinedPOBudgetTreeModel>();
            EditedRow = new CombinedPOBudgetTreeModel();
        }
        displayLoadingSpinner = false;
        StateHasChanged();
    }
    async Task OnStateInitHandler(TreeListStateEventArgs<CombinedPOBudgetTreeModel> args)
    {
        var collapsedItemsState = new TreeListState<CombinedPOBudgetTreeModel>()
            {
                //collapse all items in the TreeList upon initialization of the state
                ExpandedItems = new List<CombinedPOBudgetTreeModel>()
            };
        args.TreeListState = collapsedItemsState;
    }
    protected async Task JobSelectedHandler(object theUserChoice)
    {
        if (JobSelected != (string)theUserChoice)
        {
            displayLoadingSpinner = true;
            StateHasChanged();
            JobSelected = (string)theUserChoice;
            POHeaders = (await PoService.GetPoHeadersForJobAsync(JobSelected)).Value;
            IssuedPOsData = ActiveOrCanceled == "Canceled" ? POHeaders.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                        POHeaders.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
            if (SelectedSort == "Release/Activity")
            {
                displayLoadingSpinner = true;
                var getData = (await PoService.GetPosForJobAsync(JobSelected)).Value;
                POData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData);
            }
            else
            {
                var getData = await BudgetService.GetBudgetByJobAsync(JobSelected);
                var removedCustEsts = getData.Value.Where(x => x.Estheader.EstimateDescPe != "Pending/Custom Estimate");
                POData = new ObservableCollection<CombinedPOBudgetTreeModel>(removedCustEsts);
            }
            SelectedRows = new List<CombinedPOBudgetTreeModel>();
            SelectedItems = Enumerable.Empty<CombinedPOBudgetTreeModel>();
            EditedRow = new CombinedPOBudgetTreeModel();
            displayLoadingSpinner = false;
        }
    }
    protected async Task ChangeSortHandler(object theUserChoice)
    {
        if(JobSelected != null)
        {
            if (SelectedSort == "Release/Activity")
            {
                displayLoadingSpinner = true;
                var getData = (await PoService.GetPosForJobAsync(JobSelected)).Value;
                POData = new ObservableCollection<CombinedPOBudgetTreeModel>(getData);
            }
            else
            {
                var getData = await BudgetService.GetBudgetByJobAsync(JobSelected);
                var removedCustEsts = getData.Value.Where(x => x.Estheader.EstimateDescPe != "Pending/Custom Estimate");
                POData = new ObservableCollection<CombinedPOBudgetTreeModel>(removedCustEsts);

            }
            displayLoadingSpinner = false;
        }
    }
    protected async Task RowSelectedHandler(TreeListRowClickEventArgs args)
    {
        EditedRow = args.Item as CombinedPOBudgetTreeModel;
    }
    void ChangeUnitPriceQty(object value)
    {
        EditedRow.Estdetail.OrigCommittedAmount = EditedRow.Estdetail.OrgPrice * EditedRow.Estdetail.OrigCommittedQty;
        EditedRow.Estdetail.BoolLump = true;
        EditedRow.Estdetail.Lump = "T";
        EditedRow.Estdetail.DisplayWarnings = string.IsNullOrWhiteSpace(EditedRow.Estdetail.DisplayWarnings) ? "Using Lump Sum" : EditedRow.Estdetail.DisplayWarnings.Contains("Using Lump Sum") ? EditedRow.Estdetail.DisplayWarnings : EditedRow.Estdetail.DisplayWarnings + ", Using Lump Sum";
        EditedRow.Estdetail.Warnings = !String.IsNullOrWhiteSpace(EditedRow.Estdetail.Warnings) ? (EditedRow.Estdetail.Warnings.Contains("5") ? EditedRow.Estdetail.Warnings : EditedRow.Estdetail.Warnings + "|5") : "5";
        if (!string.IsNullOrWhiteSpace(EditedRow.Estdetail.Errors) && !string.IsNullOrWhiteSpace(EditedRow.Estdetail.DisplayErrors) && EditedRow.Estdetail.Errors.Contains("4"))
        {
            //4 is no cost error, now they have a cost, so remove the error
            var listDisplayErrors = EditedRow.Estdetail.DisplayErrors.Split(',').ToList();
            listDisplayErrors.Remove("Zero Cost Not Allowed");
            listDisplayErrors.Remove(" Zero Cost Not Allowed");
            EditedRow.Estdetail.DisplayErrors = string.Join(", ", listDisplayErrors);
            var listErrors = EditedRow.Estdetail.Errors.Split('|').ToList();
            listErrors.Remove("4");
            EditedRow.Estdetail.Errors = string.Join("|", listErrors);
        }
        //TODO: this also sets tracking variance to true, and on save prompts a select a variance category, and the original amount is saved, but how to tell if theyve updated it multiple times
        //don't do it here, save the tracking variance on update
        // EditedRow.Estdetail.OrgPrice = 1;
        // EditedRow.Estdetail.VarianceJcCategory = "CO";
        // EditedRow.Estdetail.EstimateVarianceItem = "T"; //nope
    }
    private async Task HandleValidSubmit()
    {
        if (EditedRow.Estheader != null)
        {
            var estHeaderToUpdate = new EstheaderDto()
                {
                    EstheaderId = (int)EditedRow.Estheader.EstheaderId,
                    JobNumber = EditedRow.Estheader.JobNumber,
                    EstimateDescPe = EditedRow.Estheader.EstimateDescPe,
                    EstimateSalesPrice = EditedRow.Estheader.EstimateSalesPrice,
                    Estimator = EditedRow.Estheader.Estimator,
                    ReferenceNumber = EditedRow.Estheader.ReferenceNumber,
                    ReferenceDesc = EditedRow.Estheader.ReferenceDesc,
                    ReferenceType = EditedRow.Estheader.ReferenceType,//TODO: seems to be only editable if it s not original estimate
                    Heading1 = EditedRow.Estheader.Heading1,
                    Heading2 = EditedRow.Estheader.Heading2,
                    Heading3 = EditedRow.Estheader.Heading3,
                    JobSize = EditedRow.Estheader.JobSize,
                    JobUnit = EditedRow.Estheader.JobUnit,
                };
            var response = await BudgetService.UpdateEstHeaderAsync(estHeaderToUpdate);
            ShowNotification(response.Message, response.IsSuccess);
            //TODO: return success/fail
        }
        else if (EditedRow.Estoption != null && EditedRow.Estactivity == null)
        {
            var estOptionToUpdate = new EstoptionDto()
                {
                    EstoptionId = (int)EditedRow.Estoption.EstoptionId,
                    OptionDesc = EditedRow.Estoption.OptionDesc,
                    OptionSalesPrice = EditedRow.Estoption.OptionSalesPrice,
                    OptionQty = EditedRow.Estoption.OptionQty,
                    OptionNotes = EditedRow.Estoption.OptionNotes,
                    OptionSelections = EditedRow.Estoption.OptionSelections
                };
            var response = await BudgetService.UpdateEstOptionAsync(estOptionToUpdate);
            ShowNotification(response.Message, response.IsSuccess);
        }
        else if (EditedRow.Estactivity != null && EditedRow.Estdetail == null)
        {
            var estActivityToUpdate = new EstactivityDto()
                {
                    EstactivityId = (int)EditedRow.Estactivity.EstactivityId,
                    SelectedVendor = EditedRow.Estactivity.SelectedVendor,
                    UseLocation = EditedRow.Estactivity.BoolUseLocation == true ? "T" : "F",
                    UseWbsSort = EditedRow.Estactivity.BoolUseWbsSort == true ? "T" : "F",
                    Taxable = EditedRow.Estactivity.BoolTaxable == true ? "T" : "F"
                };
            var response = await BudgetService.UpdateEstActivityAsync(estActivityToUpdate);
            //update the est in the tree below, otherwise it will still have no supplier error
            if(response.IsSuccess)
            {
                EditedRow.Estactivity.VendorNoInsurance = response.Value.VendorNoInsurance;
                EditedRow.Estactivity.VendorBlocked = response.Value.VendorBlocked;
                EditedRow.PoIssueEnabled = response.Value.VendorNoInsurance != true && response.Value.VendorBlocked != true;
                if (EditedRow.Children != null && EditedRow.Children.Count() > 0)
                {
                    foreach (var detail in EditedRow.Children.Where(x => x.Estdetail != null))//TODO: dont update the issued ones
                    {
                        detail.Estactivity.SelectedVendor = EditedRow.Estactivity.SelectedVendor;
                        detail.Estactivity.VendorBlocked = response.Value.VendorBlocked;
                        detail.Estactivity.VendorNoInsurance = response.Value.VendorNoInsurance;
                        detail.Estdetail.VendorNumber = EditedRow.Estactivity.SelectedVendor;
                        detail.PoIssueEnabled = detail.IsIssued == false  && response.Value.VendorNoInsurance != true && response.Value.VendorBlocked != true;
                    }
                }
            }
            ShowNotification(response.Message, response.IsSuccess);
        }
        else if (EditedRow.Estdetail != null)
        {
            var estDetailToUpdate = new EstdetailDto()
                {
                    EstdetailId = (int)EditedRow.Estdetail.EstdetailId,
                    ItemNotes = EditedRow.Estdetail.ItemNotes,
                    ItemDesc = EditedRow.Estdetail.ItemDesc,
                    OrdrUnit = EditedRow.Estdetail.OrdrUnit,
                    OrderQty = EditedRow.Estdetail.OrderQty,
                    Price = EditedRow.Estdetail.Price,
                    Amount = EditedRow.Estdetail.Amount,
                    OrigCommittedQty = EditedRow.Estdetail.OrigCommittedQty,
                    OrgPrice = EditedRow.Estdetail.OrgPrice,
                    OrigCommittedAmount = EditedRow.Estdetail.OrigCommittedAmount,
                    Lump = EditedRow.Estdetail.BoolLump == true ? "T" : "F",
                    UseEstPrice = EditedRow.Estdetail.BoolUseEstPrice == true ? "T" : "F",
                    Taxable = EditedRow.Estdetail.BoolTaxable == true ? "T" : "F",
                    Warnings = EditedRow.Estdetail.Warnings,
                    Errors = EditedRow.Estdetail.Errors,
                    JcCategory = EditedRow.Estdetail.JcCategory,
                    JcPhase = EditedRow.Estdetail.JcPhase
                };
            var response = await BudgetService.UpdateEstDetailAsync(estDetailToUpdate);
            if (response.IsSuccess)
            {
                EditedRow.Estdetail = response.Value;
                ReSumActivityOption(EditedRow);
            }
            ShowNotification(response.Message, response.IsSuccess);
        }
    }
    private async Task CancelChanges()
    {
        //refresh the item from db
        if (EditedRow.Estheader != null)
        {
            var getHeader = await BudgetService.GetEstHeaderAsync((int)EditedRow.Estheader.EstheaderId);
            EditedRow.Estheader.EstheaderId = getHeader.Value.EstheaderId;
            EditedRow.EstimateNumber = getHeader.Value.EstimateNumber;
            EditedRow.Estheader = getHeader.Value;
        }
        else if (EditedRow.Estoption != null && EditedRow.Estactivity == null)
        {
            var getOption = await BudgetService.GetEstOptionAsync((int)EditedRow.Estoption.EstoptionId);
            EditedRow.Estoption = getOption.Value;
            EditedRow.Estoption.EstoptionId = getOption.Value.EstoptionId;
            // EditedRow.IsIssued = getOption.IsIssued;
            // EditedRow.Indeterminate =
            // EditedRow.IssueEnabled = getOption.IsIssued != true;
            EditedRow.TotalCost = getOption.Value.TotalCost;
        }
        else if (EditedRow.Estactivity != null && EditedRow.Estdetail == null)
        {
            var getActivity = await BudgetService.GetEstActivityAsync((int)EditedRow.Estactivity.EstactivityId);
            EditedRow.BomClass = getActivity.Value.BomClass;
            EditedRow.ActivityTotal = getActivity.Value.ActivityTotal;
            EditedRow.Estactivity = getActivity.Value;
            EditedRow.ReleaseCode = getActivity.Value.Releasecode;
        }
        else
        {
            var getDetail = await BudgetService.GetEstDetailAsync((int)EditedRow.Estdetail.EstdetailId);
            EditedRow.Estdetail = getDetail.Value;
            // EditedRow.Estdetail.Warnings = getDetail.Lump == "T" ? "Warning: Using Lump Sum" : null;
            // EditedRow.Estdetail.BoolUseEstPrice = getDetail.BoolUseEstPrice ?? false;
            // EditedRow.Estdetail.BoolTaxable = getDetail.BoolTaxable ?? false;
        }
    }
    async Task IssuePurchaseOrder(TreeListCommandEventArgs args)
    {
        var itemToIssue = (CombinedPOBudgetTreeModel)args.Item;
        if (itemToIssue.Estdetail != null)
        {
            if (!string.IsNullOrWhiteSpace(itemToIssue.Estdetail.Errors))
            {
                await Dialogs.AlertAsync("Estimates with errors cannot be issued. Please correct the errors.");
                return;
            }
            if (itemToIssue.Estactivity != null && (itemToIssue.Estactivity.SelectedVendor == 0 || itemToIssue.Estactivity.SelectedVendor == null))
            {
                await Dialogs.AlertAsync("No supplier selected for this activity. Cannot issue PO.");
                return;
            }
            var findScheduleActivity = await PoService.GetScheduleActivityforEstActivityAsync((int)itemToIssue.Estdetail.EstactivityId);
            if (findScheduleActivity.Value == null || !findScheduleActivity.Value.Any())
            {
                //no schedule activity link
                var confirm = await Dialogs.ConfirmAsync("Warning: no corresponding schedule activity. Do you wish to issue the PO anyway?");
                if (!confirm)
                {
                    return;
                }
            }            
            var sendToSupplier = await Dialogs.ConfirmAsync("Send issued PO to supplier? Cancel will issue PO's but not send to supplier.");
            var listEstDetails = new List<EstdetailDto>() { itemToIssue.Estdetail };
            var response = await PoService.IssuePos(listEstDetails);
            ShowNotification(response.Message, response.IsSuccess);
            if (response.IsSuccess)
            {
                var responseItems = response.Value;
                foreach (var item in responseItems)
                {
                    if (sendToSupplier)
                    {
                        if (item.Poheader != null)
                        {
                            var poDetails = item.Children.Select(x => x.Estdetail.Podetail).ToList();
                            byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(item.Poheader, poDetails);
                            var files = new List<FileModel>();
                            files.Add(new FileModel()
                                {
                                    FileData = fileData,
                                    FileName = $"{item.Poheader.Ponumber}.pdf"
                                });
                            var emailModel = new PoEmailModel()
                                {
                                    Files = files,
                                    Poheader = item.Poheader,
                                    Subject = $"Van Metre Purchase Order: {item.Poheader.Ponumber}",
                                    Body = "Please see the attached purchase order. Visit the Van Metre Trade Portal (https://trades.vanmetrehomes.com/) for more information"
                                };
                            var emailResponse = await PoService.EmailPos(emailModel);
                            item.Poheader = emailResponse.Value.Poheader;//update the status as sent if appropriate
                            item.Poheader.PostatusNavigation = emailResponse.Value.Poheader.PostatusNavigation;
                        }
                    }

                    if (SelectedSort == "Release/Activity")
                    {
                        //add the new po row if it's release activity sort
                        var findItemIssueParent = POData.SelectMany(x => x.Children).ToList().SelectMany(x => x.Children).ToList().Where(x => x.Children.Select(y => y.Id).Contains(itemToIssue.Id)).SingleOrDefault();
                        findItemIssueParent.Children.Remove(itemToIssue);                       
                        item.ParentId = findItemIssueParent.Id;
                        findItemIssueParent.Children.Add(item);
                        AdjustCheckBoxState(item);
                    }
                    else
                    {
                        //no need to adjust the tree structure, but need to adjust the item
                        itemToIssue.PoIssueEnabled = false;
                        itemToIssue.PoIsIssued = true;
                        itemToIssue.PoIndeterminate = false;
                        itemToIssue.Estdetail = item.Children.FirstOrDefault()?.Estdetail;//if only one item issued, there will be only one child in the response
                        itemToIssue.Estactivity = item.Children.FirstOrDefault()?.Estactivity;
                        itemToIssue.Podetail = item.Children.FirstOrDefault()?.Podetail;
                        AdjustCheckBoxState(itemToIssue);
                    }
                }
                PoTreeList.Rebind();
                //reload issued pos
                POHeaders = (await PoService.GetPoHeadersForJobAsync(JobSelected)).Value;
                IssuedPOsData = ActiveOrCanceled == "Canceled" ? POHeaders.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                            POHeaders.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
            }
        }
    }
    async Task DeletePo(TreeListCommandEventArgs args)
    {
        var reissueItems = await Dialogs.ConfirmAsync("Make items available to reissue?");
        var itemToDelete = (CombinedPOBudgetTreeModel)args.Item;
        if (itemToDelete.Poheader != null && itemToDelete.Poheader.Postatus == 1)//status 1 is "issued" or "pending payment approval" and it has not been sent to supplier
        {
            var childItems = itemToDelete.Children;
            foreach(var child in childItems)
            {
                child.PoIndeterminate = false;
                child.PoIsIssued = false;
                child.IssueEnabled = true;
                child.PoIssueEnabled = true;
                child.Podetail = null;
            }
            var allParents = POData.SelectRecursive(x => x.Children).ToList();
            var findParent = allParents.Where(x => x.Children != null && x.Children.Select(y => y.Id).Contains(itemToDelete.Id)).SingleOrDefault();
            var response = await PoService.DeletePo(itemToDelete.Poheader);
            ShowNotification(response.Message, response.IsSuccess);
            if (response.IsSuccess)
            {
                findParent.Children.Remove(itemToDelete);
                if (reissueItems)
                {
                    findParent.Children.AddRange(childItems);
                }

            }

            PoTreeList.Rebind();
            //TODO: make items available to reissue, there would be a dialog box to ask if items should be available
        }
    }
    async Task CancelPo(TreeListCommandEventArgs args)
    {
        //TODO: update the data on cancelled/issued page
        //TODO: prompt are you sure?
        var itemToCancel = (CombinedPOBudgetTreeModel)args.Item;
        if (itemToCancel.Poheader != null && itemToCancel.Poheader.Podateapproved == null)
        {
            //TODO: prompt for send message to vendor
            //TODO: does a message get sent to the vendor? does it also cancel the budget estimate? what gets sent to accounting?
            var reissueItems = await Dialogs.ConfirmAsync("Make items available to reissue?");
            var emailVendor = await Dialogs.ConfirmAsync("Notify Supplier of cancellation?");
            var response = await PoService.CancelPo(itemToCancel.Poheader);
            if(response.IsSuccess){
                if (emailVendor)
                {
                    if (itemToCancel.Poheader != null)
                    {
                        var poDetails = itemToCancel.Children.Select(x => x.Estdetail.Podetail).ToList();
                        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(itemToCancel.Poheader, poDetails);

                        var files = new List<FileModel>();
                        files.Add(new FileModel()
                            {
                                FileData = fileData,
                                FileName = $"{itemToCancel.Poheader.Ponumber}.pdf"
                            });
                        var emailModel = new PoEmailModel()
                            {
                                Files = files,
                                Poheader = itemToCancel.Poheader,
                                Subject = $"CANCELLED Van Metre Purchase Order: {itemToCancel.Poheader.Ponumber}",
                                Body = "The attached purchase order is cancelled. Visit the Van Metre Trade Portal (https://trades.vanmetrehomes.com/) for more information"
                            };
                        await PoService.EmailPos(emailModel);
                    }
                }
                if (reissueItems)
                {
                    //copy the items
                    //TODO: it looks like the budget status on the copy items is isssued ( ie jce detail generated), and the original ones set to 0 cost - no, that's wrong
                    foreach (var item in itemToCancel.Children)
                    {
                        var returnItem = await BudgetService.ReissueItemAsync(item.Estdetail);
                        var returnItemToAdd = new CombinedPOBudgetTreeModel()
                            {
                                Id = Guid.NewGuid(),
                                IsPendingCustomEstimate = item.IsPendingCustomEstimate,
                                Estdetail = returnItem.Value,
                                Estactivity = item.Estactivity,
                                Estoption = item.Estoption,
                                EstimateNumber = item.EstimateNumber,
                                IsIssued = false,
                                IssueEnabled = true,
                                HasChildren = false,
                                PoIsIssued = false,
                                PoIssueEnabled = true,
                            };
                        returnItemToAdd.Estdetail.Podetail = returnItemToAdd.Estdetail.Podetail ?? new PodetailDto() { Poheader = new PoheaderDto() };//fill empty in case of null
                        var parents = POData.SelectRecursive(x => x.Children);
                        if (SelectedSort == "Release/Activity")
                        {
                            var findParent = parents.Where(x => x.Id == item.ParentId).SingleOrDefault();
                            var findGrandParent = parents.Where(x => x.Id == findParent.ParentId).SingleOrDefault();
                            returnItemToAdd.ParentId = findGrandParent.Id;
                            findGrandParent.Children.Add(returnItemToAdd);
                            PoTreeList.Rebind();
                            AdjustCheckBoxState(item);
                        }
                        else
                        {
                            var findParent = parents.Where(x => x.Id == item.ParentId).SingleOrDefault();
                            findParent.Children.Add(returnItemToAdd);
                            PoTreeList.Rebind();
                            AdjustCheckBoxState(item);
                        }
                    }
                }
                itemToCancel.Poheader.Postatus = 5;
                itemToCancel.Poheader.PostatusNavigation.Postatus1 = "Cancelled";
                if (SelectedSort == "Release/Activity")
                {
                    foreach (var child in itemToCancel.Children)
                    {
                        child.Estdetail.Podetail.Poheader.Postatus = 5;
                        child.Estdetail.Podetail.Poheader.PostatusNavigation.Postatus1 = "Cancelled";
                    }
                }
                PoTreeList.Rebind();
                //reload issued pos
                POHeaders = (await PoService.GetPoHeadersForJobAsync(JobSelected)).Value;
                IssuedPOsData = ActiveOrCanceled == "Canceled" ? POHeaders.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                            POHeaders.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
            }
            ShowNotification(response.Message, response.IsSuccess);
        }
    }
    async Task CancelPo(GridCommandEventArgs args)
    {
        //TODO: update the data on cancelled/issued page
        //TODO: prompt are you sure?
        var itemToCancel = (PoheaderDto)args.Item;
        if (itemToCancel != null && itemToCancel.Podateapproved == null)
        {
            //TODO: prompt for send message to vendor
            //TODO: does a message get sent to the vendor? does it also cancel the budget estimate? what gets sent to accounting?
            var emailVendor = await Dialogs.ConfirmAsync("Notify Supplier of cancellation?");
            var response = await PoService.CancelPo(itemToCancel);
            if (response.IsSuccess)
            {
                if (emailVendor)
                {
                    if (itemToCancel != null)
                    {
                        var poDetailsResponse = await BudgetService.GetPoDetailsByPONumberAsync(itemToCancel.PoheaderId);
                        var poDetails = poDetailsResponse.Value;
                        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(itemToCancel, poDetails);

                        var files = new List<FileModel>();
                        files.Add(new FileModel()
                            {
                                FileData = fileData,
                                FileName = $"{itemToCancel.Ponumber}.pdf"
                            });
                        var emailModel = new PoEmailModel()
                            {
                                Files = files,
                                Poheader = itemToCancel,
                                Subject = $"CANCELLED Van Metre Purchase Order: {itemToCancel.Ponumber}",
                                Body = "The attached purchase order is cancelled. Visit the Van Metre Trade Portal (https://trades.vanmetrehomes.com/) for more information"
                            };
                        await PoService.EmailPos(emailModel);
                    }
                }

                itemToCancel.Postatus = 5;
                itemToCancel.PostatusNavigation.Postatus1 = "Cancelled";

                //reload issued pos
                POHeaders = (await PoService.GetPoHeadersForJobAsync(JobSelected)).Value;
                IssuedPOsData = ActiveOrCanceled == "Canceled" ? POHeaders.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                            POHeaders.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
            }
            ShowNotification(response.Message, response.IsSuccess);
        }
    }
    async Task DownloadPdf(TreeListCommandEventArgs args)
    {
        var itemToIssue = (CombinedPOBudgetTreeModel)args.Item;
        GenerateDocumentAndDownload(itemToIssue);
    }
    async Task GenerateDocumentAndDownload(CombinedPOBudgetTreeModel item)
    {
        if (item.Poheader != null)
        {
            var poDetails = item.Children.Select(x => x.Estdetail.Podetail).ToList();
            byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(item.Poheader, poDetails);
            // await PoService.UploadPoAsync(itemToIssue.Poheader, fileData);
            DemoFileExporter.Save(JsRuntime, fileData, "application/pdf", $"PO-{item.Poheader.Ponumber}.pdf");
        }
    }
    async Task EmailVendor(TreeListCommandEventArgs args)
    {
        var itemToIssue = (CombinedPOBudgetTreeModel)args.Item;
        await GenerateDocumentAndEmail(itemToIssue);        
    }
    async Task GenerateDocumentAndDownload(PoheaderDto poHeader, List<PodetailDto> poDetails)
    {
        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(poHeader, poDetails);
        // await PoService.UploadPoAsync(itemToIssue.Poheader, fileData);
        DemoFileExporter.Save(JsRuntime, fileData, "application/pdf", $"PO-{poHeader.Ponumber}.pdf");
    }
    async Task GenerateDocumentAndEmail(PoheaderDto poHeader, List<PodetailDto> poDetails)
    {
        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(poHeader, poDetails);

        var files = new List<FileModel>();
        files.Add(new FileModel()
            {
                FileData = fileData,
                FileName = $"{poHeader.Ponumber}.pdf"
            });

        var emailModel = new PoEmailModel()
            {
                Files = files,
                Poheader = new PoheaderDto(){PoheaderId = poHeader.PoheaderId, Podescription = poHeader.Podescription},//removing everything else for validation problem
                Subject = $"Van Metre Purchase Order: {poHeader.Ponumber}",
                Body = "Please see the attached purchase order. Visit the Van Metre Trade Portal (https://trades.vanmetrehomes.com/) for more information"
            };
        var response = await PoService.EmailPos(emailModel);
        if (response.IsSuccess)
        {
            //update the status to sent if it was issued, keep status as cancelled or approved if it is one of those
            poHeader.PostatusNavigation.Postatus1 = poHeader.Postatus == 1 ? "Sent" : poHeader.PostatusNavigation.Postatus1;
            poHeader.PostatusNavigation.PostatusId = poHeader.Postatus == 1 || poHeader.Postatus == null ? 2 : poHeader.PostatusNavigation.PostatusId;
            poHeader.Postatus = poHeader.Postatus == 1 ? 2 : poHeader.Postatus;//change from issued to sent, but if approved or cancelled don't change status
        }
        ShowNotification(response.Message, response.IsSuccess);

    } 
    async Task GenerateDocumentAndEmail(CombinedPOBudgetTreeModel item)
    {
        if (item.Poheader != null)
        {
            var poDetails = item.Children.Select(x => x.Estdetail.Podetail).ToList();
            byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(item.Poheader, poDetails);

            var files = new List<FileModel>();
            files.Add(new FileModel()
            {
                FileData = fileData,
                FileName = $"{item.Poheader.Ponumber}.pdf"
            });

            var emailModel = new PoEmailModel()
                {
                    Files = files,
                    Poheader = item.Poheader,
                    Subject = $"Van Metre Purchase Order: {item.Poheader.Ponumber}",
                    Body = "Please see the attached purchase order. Visit the Van Metre Trade Portal (https://trades.vanmetrehomes.com/) for more information"
                };
            var response = await PoService.EmailPos(emailModel);
            if (response.IsSuccess)
            {
                //update the status to sent if it was issued, keep status as cancelled or approved if it is one of those
                item.Poheader.Podateprinted = DateTime.Now;
                item.Poheader.PostatusNavigation.Postatus1 = item.Poheader.Postatus == 1 ? "Sent" : item.Poheader.PostatusNavigation.Postatus1;
                item.Poheader.PostatusNavigation.PostatusId = item.Poheader.Postatus == 1 || item.Poheader.Postatus == null ? 2 : item.Poheader.PostatusNavigation.PostatusId;
                item.Poheader.Postatus = item.Poheader.Postatus == 1 ? 2 : item.Poheader.Postatus;//change from issued to sent, but if approved or cancelled don't change status
            }
            ShowNotification(response.Message, response.IsSuccess);
        }
    }
    async Task IssueSelectedPurchaseOrders(TreeListCommandEventArgs args)
    {
        //TODO: don't issue if no supplier
        if(SelectedRows == null || SelectedRows.Count() == 0)
        {
            await Dialogs.AlertAsync("Nothing selected to issue");
            return;
        }
        var isConfirmed = await Dialogs.ConfirmAsync("Issue Selected Purchase Orders?");
        if (isConfirmed)
        {
            var selectedItems = SelectedRows;
            var listEstDetails = new List<EstdetailDto>();
            var itemsToCheck = selectedItems.Where(x => x.Estdetail != null && x.Estactivity != null).ToList();
            if (itemsToCheck.Any(x => (!string.IsNullOrWhiteSpace(x.Estdetail.Errors)) || x.Estactivity.SelectedVendor == null || x.Estactivity.SelectedVendor == 0))
            {
                await Dialogs.AlertAsync("There are items with errors and/or no selected supplier. Please correct the errors.");
                return;
            }

            //warn if missing schedule link
            var selectedActvities = selectedItems.Where(x => x.Estdetail != null && x.Estactivity != null && x.Estactivity.SelectedVendor != null && x.Estactivity.SelectedVendor != 0);
            var findScheduleActivities = (await PoService.GetScheduleActivityforEstActivitiesAsync(selectedActvities.Select(x => x.Estactivity).ToList())).Value;
            var missingScheduleLinks = selectedActvities.Where(y => !findScheduleActivities.Any(x => x.SactivityId == y.Estactivity.SactivityId && x.Schedule.JobNumber == y.Estactivity.JobNumber)).Select(x => x.Estactivity).ToList();          
            if (missingScheduleLinks.Any())
            {
                var stringListMissing = string.Join(", ", missingScheduleLinks.Select(x => x.BomClass).Distinct());
                var confirm = await Dialogs.ConfirmAsync($"Warning: no schedule activity found for the folowing activities: {stringListMissing}. Do you wish to issue the selected POs anyway?");
                if (!confirm)
                {
                    return;
                }
            }
            var sendToSupplier = await Dialogs.ConfirmAsync("Send issued POs to suppliers? Cancel will issue POs but not send to supplier.");
            foreach (var item in selectedItems.Where(x => x.Estdetail != null && x.Estactivity != null && x.Estactivity.SelectedVendor != null && x.Estactivity.SelectedVendor != 0))
            {
                item.Estdetail.PoSent = sendToSupplier;//to track if the status should be "sent" or "issued"
                listEstDetails.Add(item.Estdetail);
            }
            //issue Pos
            var response = await PoService.IssuePos(listEstDetails);
            ShowNotification(response.Message, response.IsSuccess);
            if (response.IsSuccess)
            {
                //update the tree
                var responseItems = response.Value;
                //remove the existing node, add to the tree the response node with new children
                foreach (var item in responseItems)
                {
                    if (sendToSupplier)
                    {
                        if (item.Poheader != null)
                        {
                            var poDetails = item.Children.Select(x => x.Estdetail.Podetail).ToList();
                            byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(item.Poheader, poDetails);
                            var files = new List<FileModel>();
                            files.Add(new FileModel()
                                {
                                    FileData = fileData,
                                    FileName = $"{item.Poheader.Ponumber}.pdf"
                                });
                            var emailModel = new PoEmailModel()
                                {
                                    Files = files,
                                    Poheader = item.Poheader,
                                    Subject = $"Van Metre Purchase Order: {item.Poheader.Ponumber}",
                                    Body = "Please see the attached purchase order. Visit the Van Metre Trade Portal (https://trades.vanmetrehomes.com/) for more information"
                                };                           
                            var emailResponse = await PoService.EmailPos(emailModel);
                            item.Poheader = emailResponse.Value.Poheader;//update the status as sent if appropriate
                            item.Poheader.PostatusNavigation = emailResponse.Value.Poheader.PostatusNavigation;
                        }
                    }

                    if(SelectedSort == "Release/Activity")
                    {
                        //find the item to issue, remove it from the tree, then at the same node, add the new response item
                        var findParentActivity = new CombinedPOBudgetTreeModel();
                        foreach (var child in item.Children)
                        {
                            //remove the children which were now issued
                            var findItemToRemove = POData.SelectMany(x => x.Children).ToList().SelectMany(x => x.Children).ToList().SelectMany(x => x.Children).ToList().Where(x => x.Estdetail != null && x.Estdetail.EstdetailId == child.Estdetail.EstdetailId).SingleOrDefault();
                            var findItemToRemoveParent = POData.SelectMany(x => x.Children).ToList().SelectMany(x => x.Children).ToList().Where(x => x.Children.Select(y => y.Id).Contains(findItemToRemove.Id)).SingleOrDefault();
                            findParentActivity = findItemToRemoveParent;
                            findItemToRemoveParent.Children.Remove(findItemToRemove);
                        }
                        //add the new parents (the POs) with their new children (POdetails/estdetails)
                        item.ParentId = findParentActivity.Id;
                        findParentActivity.Children.Add(item);
                        AdjustCheckBoxState(item);
                    }
                    else
                    {
                        foreach(var child in item.Children)
                        {
                            //no need to adjust the tree structure, but need to adjust the items
                            var findItemToUpdate = POData.SelectMany(x => x.Children).ToList().SelectMany(x => x.Children).ToList().SelectMany(x => x.Children).ToList().Where(x => x.Estdetail != null && x.Estdetail.EstdetailId == child.Estdetail.EstdetailId).SingleOrDefault();
                            findItemToUpdate.PoIssueEnabled = false;
                            findItemToUpdate.PoIsIssued = true;
                            findItemToUpdate.PoIndeterminate = false;
                            findItemToUpdate.Estdetail = child?.Estdetail;
                            findItemToUpdate.Estactivity = child?.Estactivity;
                            findItemToUpdate.Podetail = child?.Podetail;
                            AdjustCheckBoxState(findItemToUpdate);
                        }
                    }
                }
                //reload the issued pos tab data
                POHeaders = (await PoService.GetPoHeadersForJobAsync(JobSelected)).Value;
                IssuedPOsData = ActiveOrCanceled == "Canceled" ? POHeaders.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                            POHeaders.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
            }

            PoTreeList.Rebind();
            SelectedRows = new List<CombinedPOBudgetTreeModel>();

            //TODO: clear checked state for items that failed
        }
    }
    private void AdjustCheckBoxState(CombinedPOBudgetTreeModel item)
    {
        //TODO: cleaner way to do this
        //for use after items issued/canceled, to adjust the checkbox state of parent/grandparent items
        if (item.Estdetail != null)
        {
            // var findParent = item.IsPendingCustomEstimate == true ? PendingCustomEstimateData.Where(x => x.Id == item.ParentId).SingleOrDefault() : BudgetData.Where(x => x.Id == item.ParentId).SingleOrDefault();
            var parents = POData.SelectRecursive(x => x.Children);
            var findParent = parents.Where(x => x.Id == item.ParentId).SingleOrDefault();
            //var findParent = item.Parent;
            var findSiblings = findParent.Children;
            if (findSiblings.All(x => x.PoIsIssued == true))
            {
                findParent.PoIssueEnabled = false;
                findParent.PoIsIssued = true;
                findParent.PoIndeterminate = false;
            }
            else if (findSiblings.Any(x => x.PoIsIssued == true))
            {
                findParent.PoIsIssued = false;
                findParent.PoIndeterminate = true;
                findParent.PoIssueEnabled = true;
            }
            else
            {
                findParent.PoIsIssued = false;
                findParent.PoIssueEnabled = true;
                findParent.PoIndeterminate = false;
            }
            // var findGrandParent = item.IsPendingCustomEstimate == true ? PendingCustomEstimateData.Where(x => x.Id == findParent.ParentId).SingleOrDefault() : BudgetData.Where(x => x.Id == findParent.ParentId).SingleOrDefault();

            var findGrandParent = parents.Where(x => x.Id == findParent.ParentId).SingleOrDefault();
            var findAuntsAndUncles = findGrandParent.Children;
            if (findAuntsAndUncles.All(x => x.PoIsIssued == true))
            {
                findGrandParent.PoIssueEnabled = false;
                findGrandParent.PoIsIssued = true;
                findGrandParent.PoIndeterminate = false;
            }
            else if (findAuntsAndUncles.Any(x => x.PoIsIssued == true) || findAuntsAndUncles.Any(x => x.PoIndeterminate == true))
            {
                findGrandParent.PoIsIssued = false;
                findGrandParent.PoIndeterminate = true;
                findGrandParent.PoIssueEnabled = true;
            }
            else
            {
                findGrandParent.PoIsIssued = false;
                findGrandParent.PoIssueEnabled = true;
                findGrandParent.PoIndeterminate = false;
            }
            var findGreatGrandParent = parents.Where(x => x.Id == findGrandParent.ParentId).SingleOrDefault();
            // var findGreatGrandParent = item.IsPendingCustomEstimate == true ? PendingCustomEstimateData.Where(x => x.Id == findGrandParent.ParentId).SingleOrDefault() : BudgetData.Where(x => x.Id == findGrandParent.ParentId).SingleOrDefault();
            var findGreatAuntsAndUncles = findGreatGrandParent.Children;
            if (findGreatAuntsAndUncles.All(x => x.PoIsIssued == true))
            {
                findGreatGrandParent.PoIssueEnabled = false;
                findGreatGrandParent.PoIsIssued = true;
                findGreatGrandParent.PoIndeterminate = false;
            }
            else if (findGreatAuntsAndUncles.Any(x => x.PoIsIssued == true) || findGreatAuntsAndUncles.Any(x => x.PoIndeterminate == true))
            {
                findGreatGrandParent.PoIsIssued = false;
                findGreatGrandParent.PoIndeterminate = true;
                findGreatGrandParent.PoIssueEnabled = true;
            }
            else
            {
                findGreatGrandParent.PoIsIssued = false;
                findGreatGrandParent.PoIssueEnabled = true;
                findGreatGrandParent.PoIndeterminate = false;
            }
        }
        else if(item.Poheader != null)
        {
            var parents = POData.SelectRecursive(x => x.Children);
            var findParent = parents.Where(x => x.Id == item.ParentId).SingleOrDefault();//Activity
            var findSiblings = findParent.Children;
            if (findSiblings.All(x => x.PoIsIssued == true))
            {
                findParent.PoIssueEnabled = false;
                findParent.PoIsIssued = true;
                findParent.PoIndeterminate = false;
            }
            else if (findSiblings.Any(x => x.PoIsIssued == true))
            {
                findParent.PoIsIssued = false;
                findParent.PoIndeterminate = true;
                findParent.PoIssueEnabled = true;
            }
            else
            {
                findParent.PoIsIssued = false;
                findParent.PoIssueEnabled = true;
                findParent.PoIndeterminate = false;
            }
            var findGrandParent = parents.Where(x => x.Id == findParent.ParentId).SingleOrDefault();//Release
            var findAuntsAndUncles = findGrandParent.Children;
            if (findAuntsAndUncles.All(x => x.PoIsIssued == true))
            {
                findGrandParent.PoIssueEnabled = false;
                findGrandParent.PoIsIssued = true;
                findGrandParent.PoIndeterminate = false;
            }
            else if (findAuntsAndUncles.Any(x => x.PoIsIssued == true))
            {
                findGrandParent.PoIsIssued = false;
                findGrandParent.PoIndeterminate = true;
                findGrandParent.PoIssueEnabled = true;
            }
            else
            {
                findGrandParent.PoIsIssued = false;
                findGrandParent.PoIssueEnabled = true;
                findGrandParent.PoIndeterminate = false;
            }
            var findGreatGrandParent = parents.Where(x => x.Id == findGrandParent.ParentId).SingleOrDefault();//job           
            var findGreatAuntsAndUncles = findGreatGrandParent.Children;
            if (findGreatAuntsAndUncles.All(x => x.PoIsIssued == true))
            {
                findGreatGrandParent.PoIssueEnabled = false;
                findGreatGrandParent.PoIsIssued = true;
                findGreatGrandParent.PoIndeterminate = false;
            }
            else if (findGreatAuntsAndUncles.Any(x => x.PoIsIssued == true))
            {
                findGreatGrandParent.PoIsIssued = false;
                findGreatGrandParent.PoIndeterminate = true;
                findGreatGrandParent.PoIssueEnabled = true;
            }
            else
            {
                findGreatGrandParent.PoIsIssued = false;
                findGreatGrandParent.PoIssueEnabled = true;
                findGreatGrandParent.PoIndeterminate = false;
            }
        }
    }
    private void CheckChildren(CombinedPOBudgetTreeModel item, bool checkValue)
    {
        if (item.HasChildren == false)
        {
            item.PoIsIssued = checkValue;
            item.PoIndeterminate  = checkValue == true ? false : item.PoIndeterminate;
        }
        else
        {
            foreach (var child in item.Children.Where(x => x.PoIssueEnabled != false))
            {
                CheckChildren(child, checkValue);
                item.PoIsIssued = checkValue;
                item.PoIndeterminate = checkValue == true ? false : item.Children.Any(x => x.PoIndeterminate == true) || (item.Children.Any(x => x.PoIsIssued == false && item.Children.Any(x => x.PoIsIssued == true)));
            }
        }
    }
    private bool DeterimineIndeterminateState(CombinedPOBudgetTreeModel item)
    {
        bool indeterminate = false;
        if (item.HasChildren)
        {
            var children = item.Children.SelectRecursive(x => x.Children).ToList();
            indeterminate = children.Any(x => x.PoIndeterminate == true) || (children.Any(x => x.PoIsIssued == true) && children.Any(x => x.PoIsIssued == false));           
        }
        return indeterminate;
    }
    void ChangeSelected(object value)
    {
        //checking parent should check the children
        SelectedRow.PoIsIssued = (bool)value;       
        CheckChildren(SelectedRow, SelectedRow.PoIsIssued);
        SelectedRow.PoIndeterminate = (bool)value == true ? false : DeterimineIndeterminateState(SelectedRow);
        if (SelectedRow.PoIsIssued == true)
        {
            SelectedRows.Add(SelectedRow);
            if (SelectedRow.HasChildren)
            {
                foreach(var child in SelectedRow.Children.SelectRecursive(x => x.Children).Where(x => x.PoIssueEnabled != false))
                {
                    SelectedRows.Add(child);
                    // if (child.HasChildren)
                    // {
                    //     foreach (var grandchild in child.Children.Where(x => x.PoIssueEnabled != false))
                    //     {
                    //         SelectedRows.Add(grandchild);
                    //         if (grandchild.HasChildren)
                    //         {
                    //             foreach (var greatGrandChild in child.Children.Where(x => x.PoIssueEnabled != false))
                    //             {
                    //                 SelectedRows.Add(grandchild);
                    //             }                              
                    //         }
                    //     }
                    // }
                }
            }
        }
        else
        {
            SelectedRows.Remove(SelectedRow);
            if (SelectedRow.HasChildren)
            {
                foreach (var child in SelectedRow.Children.SelectRecursive(x => x.Children).Where(x => x.PoIssueEnabled != false))
                {
                    SelectedRows.Remove(child);
                    // if (child.HasChildren)
                    // {
                    //     foreach (var grandchild in child.Children.Where(x => x.PoIssueEnabled != false))
                    //     {
                    //         SelectedRows.Remove(grandchild);
                    //     }
                    // }
                }
            }
        }
    }
    private async void HandleValidAddItemsSubmit(ResponseModel<List<EstdetailDto>> responseItems)
    {
        //This only happens in Estimate/Option sort
        await AddItemToBudgetModal.Hide();
        ShowNotification(responseItems.Message, responseItems.IsSuccess);
        if (responseItems.IsSuccess)
        {
            foreach (var responseItem in responseItems.Value)
            {
                var treelistItemToAdd = new CombinedPOBudgetTreeModel()
                    {
                        Estdetail = responseItem,
                        Estactivity = responseItem.Estactivity,
                        Id = Guid.NewGuid(),
                        HasChildren = false,
                        IsIssued = false,
                        IssueEnabled = true,
                        Indeterminate = false,
                    };
                //find the activity in the option if it exists, if not add it,
                var findOption = POData.SelectMany(x => x.Children).ToList().SingleOrDefault(x => x.Estoption != null && x.Estoption.EstoptionId == responseItem.Estoption.EstoptionId);
                if (findOption != null)
                {
                    var findActivity = findOption.Children.Where(x => x.Estactivity.EstactivityId == responseItem.Estactivity.EstactivityId).SingleOrDefault();
                    if (findActivity != null)
                    {
                        treelistItemToAdd.ParentId = findActivity.Id;
                        findActivity.Children.Add(treelistItemToAdd);
                        findActivity.Estactivity.ActivityTotal += treelistItemToAdd.Estdetail.Amount;
                        findActivity.ActivityTotal += treelistItemToAdd.Estdetail.Amount;
                    }
                    else
                    {
                        //add the activity
                        var addActivity = new CombinedPOBudgetTreeModel()
                            {
                                Estactivity = responseItem.Estactivity,
                                Id = Guid.NewGuid(),
                                HasChildren = true,
                                ParentId = findOption.Id,
                                Children = new List<CombinedPOBudgetTreeModel>() { treelistItemToAdd },
                                IsIssued = false,
                                IssueEnabled = true,
                                Indeterminate = false,
                                ActivityTotal = treelistItemToAdd.Estdetail.Amount
                            };
                        addActivity.Estactivity.ActivityTotal = treelistItemToAdd.Estdetail.Amount;
                        treelistItemToAdd.ParentId = addActivity.Id;
                        findOption.Children.Add(addActivity);
                        findOption.Estoption.TotalCost += treelistItemToAdd.Estdetail.Amount;
                    }
                }
                else
                {
                    var findEstHeader = POData.SingleOrDefault(x => x.Estheader != null && x.Estheader.EstheaderId == responseItem.Estoption.EstheaderId);
                    if (findEstHeader != null)
                    {
                        var addActivity = new CombinedPOBudgetTreeModel()
                            {
                                Estactivity = responseItem.Estactivity,
                                Id = Guid.NewGuid(),
                                HasChildren = true,
                                Children = new List<CombinedPOBudgetTreeModel>() { treelistItemToAdd },
                                IsIssued = false,
                                IssueEnabled = true,
                                Indeterminate = false,
                            };
                        addActivity.ActivityTotal = treelistItemToAdd.Estdetail.Amount;
                        addActivity.Estactivity.ActivityTotal = treelistItemToAdd.Estdetail.Amount;
                        var addOption = new CombinedPOBudgetTreeModel()
                            {
                                Id = Guid.NewGuid(),
                                HasChildren = true,
                                Estoption = responseItem.Estoption,
                                ParentId = findEstHeader.Id,
                                IsIssued = false,
                                IssueEnabled = true,
                                Indeterminate = false,
                                Children = new List<CombinedPOBudgetTreeModel>() { addActivity },
                                TotalCost = treelistItemToAdd.Estdetail.Amount
                            };
                        findEstHeader.Children.Add(addOption);
                        findEstHeader.TotalCost += treelistItemToAdd.Estdetail.Amount;
                        addActivity.ParentId = addOption.Id;
                        treelistItemToAdd.ParentId = addActivity.Id;
                    }
                }
                AdjustCheckBoxState(treelistItemToAdd);
            }
            //TODO: adjust the checkbox state

            PoTreeList.Rebind();
        }      
        StateHasChanged();
    }
    private async void HandleValidAddEstimateSubmit(ResponseModel<CombinedPOBudgetTreeModel> responseBudget)
    {
        ShowNotification(responseBudget.Message, responseBudget.IsSuccess);
        if (responseBudget.IsSuccess)
        {
            if (SelectedSort == "Release/Activity")
            {
                var findReturnActivities = responseBudget.Value.Children.SelectMany(x => x.Children);
                foreach (var activity in findReturnActivities)
                {
                    var findActivity = POData.SelectRecursive(x => x.Children).Where(x => x.Estactivity != null && x.Estdetail == null && x.Estactivity.BomClass == activity.Estactivity.BomClass).SingleOrDefault();
                    //if the activity is there, add the children
                    if (findActivity != null)
                    {
                        foreach (var child in activity.Children)
                        {
                            child.ParentId = findActivity.Id;
                            child.Podetail = null;//TODO: fix this. it's not issued yet, this is needed to show the refresh costs button
                        }
                        findActivity.Children.AddRange(activity.Children);
                        findActivity.ActivityTotal += activity.ActivityTotal;
                    }
                    //if the activity is not there, add the activity and the children
                    else
                    {
                        var findRelease = POData.SelectMany(x => x.Children).Where(x => x.ReleaseCode == activity.Estactivity.Releasecode).SingleOrDefault();
                        foreach (var child in activity.Children)
                        {
                            child.Podetail = null;//TODO: fix this. it's not issued yet, this is needed to show the refresh costs button
                        }
                        if (findRelease != null)
                        {
                            activity.ParentId = findRelease.Id;                            
                            findRelease.Children.Add(activity);
                            findRelease.Children.OrderBy(x => x.Estactivity.BomClass);
                        }
                        else
                        {
                            //add the release
                            var newId = Guid.NewGuid();
                            activity.ParentId = newId;
                            POData.First().Children.Add(new CombinedPOBudgetTreeModel()
                                {
                                    Id = newId,
                                    ReleaseCode = activity.Estactivity.Releasecode,
                                    Children = new List<CombinedPOBudgetTreeModel> { activity },
                                    HasChildren = true,
                                    ParentId = POData.First().Id,
                                    PoIsIssued = false,
                                    PoIndeterminate = false,
                                    PoIssueEnabled = true,
                                });
                        }
                        AdjustCheckBoxState(activity.Children.First());//TODO: this doesn't work
                    }
                    //TODO: checkbox state and retotal the activities
                }
            }
            else
            {
                POData.Add(responseBudget.Value);
            }
            await AddEstimateModal.Hide();
            StateHasChanged();
            PoTreeList.Rebind();
        }       
    }
    private async Task ShowAddItemToBudget(TreeListCommandEventArgs args)
    {
        var option = args.Item as CombinedPOBudgetTreeModel;
        if (option != null && option.Estoption != null)
        {
            SelectedEstOptionId = (int)option.Estoption.EstoptionId;
            SelectedHeaderId = (int)option.Estoption.EstheaderId;
            await AddItemToBudgetModal.Show();
        }
    }
    private async Task ShowAddEstimate(TreeListCommandEventArgs args)
    {
        await AddEstimateModal.Show();
    }
    protected async Task CopyItem(TreeListCommandEventArgs args)
    {
        var itemToCopy = args.Item as CombinedPOBudgetTreeModel;
        if (itemToCopy.Estdetail != null)
        {
            var returnItem = await BudgetService.CopyItemAsync(itemToCopy.Estdetail);
            var returnItemToAdd = new CombinedPOBudgetTreeModel()
                {
                    Id = Guid.NewGuid(),
                    ParentId = itemToCopy.ParentId,
                    IsPendingCustomEstimate = itemToCopy.IsPendingCustomEstimate,
                    Estdetail = returnItem.Value,
                    Estactivity = itemToCopy.Estactivity,
                    Estoption = itemToCopy.Estoption,
                    EstimateNumber = itemToCopy.EstimateNumber,
                    ExtraNumber = itemToCopy.ExtraNumber,
                    IsIssued = false,
                    IssueEnabled = true,
                    HasChildren = false,
                    PoIsIssued = false,
                    PoIssueEnabled = true,
                };
            returnItemToAdd.Estdetail.Podetail = returnItemToAdd.Estdetail.Podetail ?? new PodetailDto() { Poheader = new PoheaderDto() };//fill empty in case of null
            var parents = POData.SelectRecursive(x => x.Children);
            if (SelectedSort == "Release/Activity")
            {
                var findParent = parents.Where(x => x.Id == itemToCopy.ParentId).SingleOrDefault();
                var findGrandParent = parents.Where(x => x.Id == findParent.ParentId).SingleOrDefault();
                //if the previous item was issued, need to add to the grandparent, else need to add to the parent
                if (itemToCopy.PoIsIssued)
                {
                    returnItemToAdd.ParentId = findGrandParent.Id;
                    findGrandParent.Children.Add(returnItemToAdd);
                }
                else
                {
                    returnItemToAdd.ParentId = findParent.Id;
                    findParent.Children.Add(returnItemToAdd);
                }

                PoTreeList.Rebind();
                AdjustCheckBoxState(itemToCopy);
                ReSumActivityOption(itemToCopy);
            }
            else
            {
                var findParent = parents.Where(x => x.Id == itemToCopy.ParentId).SingleOrDefault();                
                findParent.Children.Add(returnItemToAdd);
                PoTreeList.Rebind();
                AdjustCheckBoxState(itemToCopy);
                ReSumActivityOption(itemToCopy);
            }           
        }
    }
    protected async Task DeleteHandler(TreeListCommandEventArgs args)
    {
        //TODO: what if budget issued? correction?
        var itemToDelete = args.Item as CombinedPOBudgetTreeModel;
        // DELETING ESTDETAIL
        if (itemToDelete.Estdetail != null && itemToDelete.PoIsIssued != true)
        {
            var response = await BudgetService.DeleteItemAsync(itemToDelete);
            ShowNotification(response.Message, response.IsSuccess);
            var parents = POData.SelectRecursive(x => x.Children);
            var findParent = parents.Where(x => x.Id == itemToDelete.ParentId).SingleOrDefault();
            findParent.Children.Remove(itemToDelete);
            var activitySum = findParent.Children.Where(x => x.Estdetail != null).Sum(x => x.Estdetail.Amount) + findParent.Children.Where(x => x.Poheader != null).Sum(x => x.Poheader.Pototal);//adjust activity sum
            findParent.ActivityTotal = activitySum;
            findParent.Estactivity.ActivityTotal = activitySum;
            if (SelectedSort == "Estimate/Option/Activity")
            {
                var findGrandParent = parents.SingleOrDefault(x => x.Id == findParent.ParentId);
                findGrandParent.Estoption.TotalCost = findGrandParent.Children.Sum(x => x.ActivityTotal);//adjust option sum
                findGrandParent.TotalCost = findGrandParent.Children.Sum(x => x.ActivityTotal);
            }
            if (!findParent.Children.Any())
            {
                var findGrandParent = parents.Where(x => x.Id == findParent.ParentId).SingleOrDefault();
                findGrandParent.Children.Remove(findParent);
                if (findGrandParent.Children.Any())
                {
                    if (findGrandParent.Children.First().Children.Any())
                    {
                        AdjustCheckBoxState(findGrandParent.Children.First().Children.First());
                    }                   
                }
                // else
                // {
                //     AdjustCheckBoxState(findGrandParent);
                // }
            }
            else
            {
                AdjustCheckBoxState(findParent.Children.First());
            }
            PoTreeList.Rebind();
        }       
        else if (itemToDelete.Estactivity != null && itemToDelete.IsIssued != true)
        {
            //TODO: activity should be removed automatically if delete all the items in it
        }
        else if (itemToDelete.Estoption != null && itemToDelete.IsIssued != true)
        {
            await BudgetService.DeleteOptionAsync(itemToDelete.Estoption);
            var response = await BudgetService.DeleteOptionAsync(itemToDelete.Estoption);
            if (response.IsSuccess)
            {
                var findParent = POData.Where(x => x.Id == itemToDelete.ParentId).SingleOrDefault();
                findParent.Children.Remove(itemToDelete);
                if (!findParent.Children.Any())
                {
                    POData.Remove(findParent);//if no other options, remove the parent est
                }
                PoTreeList?.Rebind();
            }
            ShowNotification(response.Message, response.IsSuccess);
            StateHasChanged();
        }
        else if (itemToDelete.Estheader != null && itemToDelete.IsIssued != true)
        {
            await BudgetService.DeleteHeaderAsync(itemToDelete.Estheader);//TODO: Only allow delete if not any issued. But maybe not even then, wouldn't want to delete the original config?
            POData.Remove(itemToDelete);
        }
    }
    private void ToggleCanceledIssuedPOs()
    {
        IssuedPOsData = ActiveOrCanceled == "Canceled" ? POHeaders.Where(x => x.Postatus == 5).OrderBy(x => x.Ponumber).ToList() :
                                                        POHeaders.Where(x => x.Postatus != 5).OrderBy(x => x.Ponumber).ToList();
        IssuedPOsGrid.Rebind();
    }
    protected async Task RefreshSupplier(TreeListCommandEventArgs args)
    {
        //TODO: is alljobs filled? - no, it was not
        var activitySelected = args.Item as CombinedPOBudgetTreeModel;
        IsLoading = true;
        var refreshSupplierModel = new RefreshSupplierAndCostsModel() { EstActivityIds = new List<int>(), SubdivisionId = SelectedSubdivisionId };

        if (activitySelected != null && activitySelected.Estoption == null) // refresh supplier clicked at activity level
        {
            refreshSupplierModel.EstActivityIds.Add(activitySelected.Estactivity.EstactivityId);
        }
        else if (activitySelected != null && activitySelected.Estoption != null) // refresh supplier clicked at option level
        {
            refreshSupplierModel.EstActivityIds.AddRange(activitySelected.Children.Select(x => x.Estactivity.EstactivityId).ToList());
        }
        else // refresh supplier for selected rows
        {
            foreach (var item in SelectedRows)
            {
                if (item.Estoption != null && item.Estactivity == null)
                {
                    refreshSupplierModel.EstActivityIds.AddRange(item.Children.Select(x => x.Estactivity.EstactivityId).ToList());
                }
                else if (item.Estactivity != null && item.Estdetail == null)
                {
                    refreshSupplierModel.EstActivityIds.Add(item.Estactivity.EstactivityId);
                }
                else if (item.Estoption == null && item.Estactivity == null && item.Estdetail == null) // SelectedRow is an estimate
                {
                    var childrenActivitiesInEstimate = item.Children.SelectRecursive(x => x.Children).Where(x => x.Estactivity != null && x.Estdetail == null).ToList().Select(x => x.Estactivity.EstactivityId).ToList();
                    refreshSupplierModel.EstActivityIds.AddRange(childrenActivitiesInEstimate);
                }
            }
        }
        refreshSupplierModel.EstActivityIds = refreshSupplierModel.EstActivityIds.Distinct().ToList();

        var refreshedVendorResponse = await TradeService.RefreshSupplierAsync(refreshSupplierModel);
        if (refreshedVendorResponse.IsSuccess)
        {
            if (activitySelected != null && activitySelected.Estoption == null)
            {
                var parents = POData.SelectRecursive(x => x.Children);
                var activities = parents.Where(x => x.Estactivity != null && x.Estdetail == null && x.Estactivity.EstactivityId == activitySelected.Estactivity.EstactivityId).ToList();
                foreach (var activity in activities)
                {
                    activity.Estactivity.SelectedVendor = refreshedVendorResponse.Value.SupplierNumber;
                    activity.Estactivity.VendorBlocked = refreshedVendorResponse.Value.VendorBlocked;
                    activity.Estactivity.VendorNoInsurance = refreshedVendorResponse.Value.VendorNoInsurance;
                    activity.PoIssueEnabled = refreshedVendorResponse.Value.VendorNoInsurance != true && refreshedVendorResponse.Value.VendorBlocked != true;
                    foreach (var estdetail in activity.Children.Where(x => x.Estactivity != null))
                    {
                        estdetail.Estactivity.SelectedVendor = refreshedVendorResponse.Value.SupplierNumber;
                        estdetail.Estactivity.VendorBlocked = refreshedVendorResponse.Value.VendorBlocked;
                        estdetail.Estactivity.VendorNoInsurance = refreshedVendorResponse.Value.VendorNoInsurance;
                        estdetail.PoIssueEnabled = !estdetail.PoIsIssued && refreshedVendorResponse.Value.VendorNoInsurance != true && refreshedVendorResponse.Value.VendorBlocked != true;
                    }
                }
                //TODO: update the tree with errors for blocked vendor
                PoTreeList.Rebind();
            }
            else
            {
                JobSelected = null;
                await JobChangedHandler();
                SelectedRows.Clear();
                EditedRow = new CombinedPOBudgetTreeModel();
            }
        }
        IsLoading = false;
        ShowNotification(refreshedVendorResponse.Message, refreshedVendorResponse.IsSuccess);
    }
    protected async Task RefreshCosts(TreeListCommandEventArgs args)
    {
        RefreshCostClickedRow = args.Item as CombinedPOBudgetTreeModel;
        await SelectDateModal.Show();
    }
    private async void HandleRefreshCostsSubmit(DateTime selectedDate)
    {
        await SelectDateModal.Hide();
        if (RefreshCostClickedRow != null) // when Refresh Costs is clicked on any Command Column button
        {
            var refreshCostModel = new RefreshSupplierAndCostsModel()
                {
                    EstOptionIds = RefreshCostClickedRow.Estoption != null && RefreshCostClickedRow.Estdetail == null ? new List<int> { RefreshCostClickedRow.Estoption.EstoptionId } : new List<int>(),
                    EstActivityIds = RefreshCostClickedRow.Estactivity != null && RefreshCostClickedRow.Estdetail == null ? new List<int> { RefreshCostClickedRow.Estactivity.EstactivityId } : new List<int>(),
                    EstDetailIds = RefreshCostClickedRow.Estdetail != null ? new List<int> { RefreshCostClickedRow.Estdetail.EstdetailId } : new List<int>(),
                    SelectedDate = selectedDate
                };
            var response = await BudgetService.RefreshCostsPOAsync(refreshCostModel);
            ShowNotification(response.Message, response.IsSuccess);
            var parents = POData.SelectRecursive(x => x.Children);

            var activities = new List<CombinedPOBudgetTreeModel>();

            if (RefreshCostClickedRow.Estoption != null && RefreshCostClickedRow.Estactivity == null)
            {
                var estoption = parents.SingleOrDefault(x => x.Estoption != null && x.Estactivity == null && x.Estoption.EstoptionId == RefreshCostClickedRow.Estoption.EstoptionId);
                activities = estoption?.Children;
            }
            else
            {
                activities = parents.Where(x => x.Estactivity != null && x.Estdetail == null && x.Estactivity.EstactivityId == RefreshCostClickedRow.Estactivity.EstactivityId).ToList();
            }
            foreach (var activity in activities)
            {
                //update the est details and sum the activities
                double totalAmount = 0d;
                foreach (var estDetail in activity.Children.Where(x => x.Estdetail != null))//in release sort, the estdetails children need amount updated
                {
                    var detail = await BudgetService.GetEstDetailAsync(estDetail.Estdetail.EstdetailId);
                    estDetail.Estdetail.OrgPrice = detail.Value.OrgPrice ?? detail.Value.Price;
                    estDetail.Estdetail.OrigCommittedAmount = detail.Value.OrigCommittedAmount ?? detail.Value.Amount;
                    totalAmount += estDetail.Estdetail.OrigCommittedAmount ??  0d;
                    if (RefreshCostClickedRow.Estdetail != null && EditedRow.Id == RefreshCostClickedRow.Id && EditedRow.Id == estDetail.Id)
                    {
                        EditedRow.Estdetail.OrgPrice = detail.Value.OrgPrice ?? detail.Value.Price;
                        EditedRow.Estdetail.OrigCommittedAmount = detail.Value.OrigCommittedAmount ?? detail.Value.Amount;
                        EditedRow.Estdetail.Amount = detail.Value.Amount;
                        EditedRow.Estdetail.DisplayErrors = detail.Value.DisplayErrors;
                    }
                }
                foreach (var poheader in activity.Children.Where(x => x.Poheader != null))//in release sort, there will be activity children that are poheaders, those shouldn't update but should count to total
                {
                    totalAmount += poheader.Poheader.Pototal == null ? 0d : (double)poheader.Poheader.Pototal;
                }
                activity.Estactivity.ActivityTotal = totalAmount;
                activity.ActivityTotal = totalAmount;

                //sum at option level
                if (SelectedSort == "Estimate/Option/Activity")
                {
                    var findGrandParent = parents.SingleOrDefault(x => x.Id == activity.ParentId);
                    findGrandParent.Estoption.TotalCost = findGrandParent.Children.Sum(x => x.Estactivity.ActivityTotal);
                    findGrandParent.TotalCost = findGrandParent.Children.Sum(x => x.Estactivity.ActivityTotal);
                }
                if (RefreshCostClickedRow.Estdetail == null && EditedRow.Id == RefreshCostClickedRow.Id && EditedRow.Id == activity.Id)
                {
                    EditedRow.ActivityTotal = totalAmount;
                }
            }
        }
        else //when RefreshCosts is clicked on Tool Bar for Selected Items
        {
            var refreshCostModel = new RefreshSupplierAndCostsModel()
                {
                    EstOptionIds = new List<int>(),
                    EstActivityIds = new List<int>(),
                    EstDetailIds = new List<int>(),
                    SelectedDate = selectedDate
                };
            foreach (var item in SelectedRows)
            {
                if (item.Estoption != null)
                {
                    refreshCostModel.EstOptionIds.Add(item.Estoption.EstoptionId);
                }
                else if (item.Estactivity != null && item.Estdetail == null)
                {
                    refreshCostModel.EstActivityIds.Add(item.Estactivity.EstactivityId);
                }
                else if (item.Estdetail != null)
                {
                    refreshCostModel.EstDetailIds.Add(item.Estdetail.EstdetailId);
                }
                else if (item.Estoption == null && item.Estactivity == null && item.Estdetail == null) // SelectedRow is an estimate
                {
                    foreach (var child in item.Children)
                    {
                        refreshCostModel.EstOptionIds.Add(child.Estoption.EstoptionId);
                    }
                }
            }

            var response = await BudgetService.RefreshCostsPOAsync(refreshCostModel);
            ShowNotification(response.Message, response.IsSuccess);
            JobSelected = null;
            await JobChangedHandler();
            SelectedRows.Clear();
        }
        PoTreeList.Rebind();
        StateHasChanged();
    }
    private void ReSumActivityOption(CombinedPOBudgetTreeModel item)
    {
        //TODO: check newly added items have parent Id filled, else fix how this finds items parents
        //item should be estdetail level item,
        //resum activity/option, for use whenever costs change - eg update estdetail, copy item, refresh costs,
        var parents = POData.SelectRecursive(x => x.Children);
        var findParent = parents.Where(x => x.Id == item.ParentId).SingleOrDefault();
        //some children will have an estdetail, some are the poheader leve, 
        findParent.ActivityTotal = findParent.Children.Sum(x => x.Estdetail?.Amount) + findParent.Children.Sum(x => x.Poheader?.Pototal);
        //if it's in release sort, and the po is issued, activity level is two levels up, so it's actually grandparent, but need to fix the po amount. 
        //already issued po shouldn't change amount, or refrehs costs
        if(findParent.Estactivity != null)
        {
            findParent.Estactivity.ActivityTotal = findParent.Children.Sum(x => x.Estdetail?.Amount) + findParent.Children.Sum(x => x.Poheader?.Pototal);
        }
        if(findParent.Poheader != null)
        {
            //this item was in an issued purchase order, so in order to re sum, need to find the activity one more level up
            var findGrandParent = parents.SingleOrDefault(x => x.Id == findParent.ParentId);
            var issuedPosTotal = findGrandParent.Children.Sum(x => x.Poheader?.Pototal);
            var notIssuedTotal = findGrandParent.Children.Sum(x => x.Estdetail?.Amount);
            findGrandParent.ActivityTotal = notIssuedTotal + issuedPosTotal;
            findGrandParent.Estactivity.ActivityTotal = notIssuedTotal + issuedPosTotal;
        }

        if (SelectedSort == "Estimate/Option/Activity")
        {
            var findGrandParent = parents.SingleOrDefault(x => x.Id == findParent.ParentId);
            findGrandParent.Estoption.TotalCost = findGrandParent.Children.Sum(x => x.Estactivity.ActivityTotal);
            findGrandParent.TotalCost = findGrandParent.Children.Sum(x => x.Estactivity.ActivityTotal);
        }
    }
    public async void Download(GridCommandEventArgs args)
    {
        var poHeader = args.Item as PoheaderDto;
        if (poHeader != null)
        {
            var estDetailsResponse = await BudgetService.GetPoDetailsByPONumberAsync(poHeader.PoheaderId);
            if (estDetailsResponse.IsSuccess)
            {
                GenerateDocumentAndDownload(poHeader, estDetailsResponse.Value);
            }
            else
            {
                ShowNotification(estDetailsResponse.Message, false);
            }
        }
    }
    public async void Email(GridCommandEventArgs args)
    {
        var poHeader = args.Item as PoheaderDto;
        if (poHeader != null)
        {
            var estDetailsResponse = await BudgetService.GetPoDetailsByPONumberAsync(poHeader.PoheaderId);
            if (estDetailsResponse.IsSuccess)
            {
                GenerateDocumentAndEmail(poHeader, estDetailsResponse.Value);
            }
            else
            {
                ShowNotification(estDetailsResponse.Message, false);
            }
        }
    }
    // public void OnItemClick(ContextMenuItem item)
    // {
    //     var rowList = POData.SelectRecursive(x => x.Children);
    //     var itemToIssue = rowList.Where(x => x.Poheader != null && x.Poheader.Ponumber == ClickedIssuedPORow.Ponumber).FirstOrDefault();
    //     if (item.Text == "Download PO" && itemToIssue != null)
    //     {
    //         GenerateDocumentAndDownload(itemToIssue);
    //     }
    //     if (item.Text == "Email PO" && itemToIssue != null)
    //     {
    //         GenerateDocumentAndEmail(itemToIssue);
    //     }
    // }
    // private void OnContextMenu(GridRowClickEventArgs args)
    // {
    //     ClickedIssuedPORow = args.Item as PoheaderDto; 

    //     if (args.EventArgs is MouseEventArgs mouseEventArgs)
    //     {
    //         _ = ContextMenuRef.ShowAsync(mouseEventArgs.ClientX, mouseEventArgs.ClientY);
    //     }
    // }
    // public class ContextMenuItem
    // {
    //     public string Text { get; set; }
    //     public object Icon { get; set; }
    // }
    private CombinedPOBudgetTreeModel ConvertToReleaseSort(CombinedPOBudgetTreeModel treeItemToSort)
    {
        //take one estimate from est/opt/act sort to release sort
        var optionChildrenToRelease = treeItemToSort.Children.Where(x => x.Estoption != null).ToList();
        foreach (var option in optionChildrenToRelease)
        {
            option.Estoption = null;//just to convert the option row to a release row
        }
        return treeItemToSort;
    }
    async void ShowNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= JobChangedHandler;
    }
}


