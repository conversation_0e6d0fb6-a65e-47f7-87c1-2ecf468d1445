﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class SupplierCommunicationAttach
{
    public int SubAttachId { get; set; }

    public int SubCommId { get; set; }

    public string? AttachName { get; set; }

    public string? AttachDriveLink { get; set; }

    public string? Notes { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual SupplierCommunication SubComm { get; set; } = null!;
}
