﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class CostDto : IMapFrom<Cost>
{
    public int CostsId { get; set; }

    public int MasterItemId { get; set; }

    public int SubNumber { get; set; }

    public int SubdivisionId { get; set; }

    public double? UnitCost { get; set; }

    public double? NextCost { get; set; }

    public DateTime? NextCostDue { get; set; }

    public double? NextCost2 { get; set; }

    public DateTime? NextCost2Due { get; set; }

    public double? LastCost1 { get; set; }

    public DateTime? LastCostExpired { get; set; }

    public double? LastCost2 { get; set; }

    public DateTime? LastCost2Expired { get; set; }

    public double? LastCost3 { get; set; }

    public DateTime? LastCost3Expired { get; set; }

    public string? SupProductCode { get; set; }

    public string? SupAltDesc { get; set; }

    public string? SupUnit { get; set; }

    public double? SupOrigCost { get; set; }

    public double? SupOrigCostLast { get; set; }

    public double? SupOrigCostLast2 { get; set; }

    public double? SupOrigCostLast3 { get; set; }

    public double? SupOrigCostNext { get; set; }

    public double? SupOrigCostNext2 { get; set; }

    public string? IncludesTax { get; set; }

    public string? ItemTaxGroup { get; set; }

    public string? Warrantyitem { get; set; }

    public int? Warrantydays { get; set; }

    public int? Warrantytype { get; set; }

    public int? SupplierContractsId { get; set; }

    public int? SupplierContCoId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    //public byte[]? RecordTimeStamp { get; set; }

    //public virtual MasterItem MasterItem { get; set; } = null!;

    //public virtual Supplier SubNumberNavigation { get; set; } = null!;

    //public virtual Subdivision Subdivision { get; set; } = null!;

    public void Mapping(Profile profile)
    {
        profile.CreateMap<CostDto, Cost>().ReverseMap();
    }
}
