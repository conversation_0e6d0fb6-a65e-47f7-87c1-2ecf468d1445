﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class HoaAssessment
{
    public int HoaAssessmentId { get; set; }

    public int HoaId { get; set; }

    public int SubdivisionId { get; set; }

    public string AssessmentLabel { get; set; } = null!;

    public decimal? Icc { get; set; }

    public decimal? MonthAssessment { get; set; }

    public decimal? OtherFee { get; set; }

    public DateTime BudgetYear { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual Hoa Hoa { get; set; } = null!;

    public virtual ICollection<HoaJob> HoaJobs { get; set; } = new List<HoaJob>();

    public virtual Subdivision Subdivision { get; set; } = null!;
}
