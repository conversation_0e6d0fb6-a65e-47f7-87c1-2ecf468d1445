﻿@page "/lots/"
@page "/lots/{subdivisionid:int}"
@inject SubdivisionService SubdivisionService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject JobService JobService
@using Telerik.Documents.SpreadsheetStreaming
@using ERP.Web.Components
@using Telerik.Blazor.Components.Grid

<style>
    /*  .import {
    width: 100px;
    border-style: none;
    }

    .import .k-dropzone-hint {
    display: none;
    }

    .k-upload .k-upload-files {
    position: absolute;
    left: 100%;
    top: 0;
    height: 60px;
    border-style: none;
    }

    .k-upload .k-upload-files .k-file {
    padding: 5px 0 0;
    }

    .selected-items-container {
    max-height: 30px;
    overflow-y: auto;
    }
    */
    .MyTelerikNotification .k-notification-container .k-notification {
    font-family: "Roboto",sans-serif;
    font-size: .75rem;
    }

    .k-window {
    width: 350px;
    }


</style>

<PageTitle>Lots | @SubdivisionName</PageTitle>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<TelerikLoaderContainer Visible="@IsLoading" Text="Please wait..." />
<div class="col-lg-12">
    <div class="card" style="background-color:#2e5771">
        <div class="card-body" style="padding:0.5rem">
            <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Lots</h7>
            @if (SubdivisionName != null)
            {
                <div style="display:block">
                    <h7 style="color:#fff">@SubdivisionName</h7>
                </div>
            }
        </div>
    </div>
</div>
<TelerikTooltip TargetSelector=".tooltip-target" />
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="/">Home</a></li>
    <li class="breadcrumb-item"><a href="/subdivisions">Subdivisions</a></li>
    <li class="breadcrumb-item active">Lots</li>
</ol>
@* <TelerikButton OnClick="@TestMultiple">button</TelerikButton> *@
@if (lots == null)
{
    <p><em>Loading...</em></p>
}
else
{
    <TelerikGrid Data=@lots
    ConfirmDelete="true"
    ScrollMode="@GridScrollMode.Virtual"
    FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
    FilterMenuType="@FilterMenuType.CheckBoxList"
    Height="1000px" RowHeight="60" PageSize="20"
    Sortable="true"
    Resizable="true"
    EditMode="GridEditMode.Popup"
    OnAdd="AddLot"
    OnCreate="CreateLot"
    OnUpdate="UpdateLot"
    @ref="@GridRef">
        <GridColumns>
            <GridCommandColumn Context="dataItem" Width="120px">
                @{
                    var lot = dataItem as JobDto;
                    <a href=@($"/lotdetails/{lot.JobNumber}") class="btn btn-outline-primary">View Details</a>
                }
            </GridCommandColumn>
            @* <GridCommandColumn Width="120px">
                <GridCommandButton Command="ViewDetails" OnClick="@ShowDetails">Details</GridCommandButton>
            </GridCommandColumn>*@
            <GridColumn Field="Subdivision.SubdivisionName" Title="Subdivision Name" Editable="false" />
            <GridColumn Field="JobNumber" Editable="false" Title="Job Number" />
            <GridColumn Field="LotNumber" Title="Lot Number"  />
            <GridColumn Field="JobDesc" Title="Description" Editable="false" />
            <GridColumn Field="JobAddress1" Title="Job Address" Editable="false" />
            @*  <GridColumn Field="JobCity" Title="Job City" Width="0" Editable="false" />
            <GridColumn Field="JobState" Title="Job State" Width="0" Editable="false" /> *@
            @* <GridColumn Field="JobCounty" Title="Job County" Width="0" Editable="false" /> *@
            @*  <GridColumn Field="JobZipCode" Title="Job Zip" Width="0" Editable="false" />       *@
            <GridColumn Field="LotSectionCode" Title="Lot Section Code"  Width="0" Visible=false Editable="false" />
            <GridColumn Field="LotSwing" Title="Lot Swing" Width="0" Visible=false Editable="false" />
            <GridColumn Field="GarageOrientation.Name" Title="GarageOrienation" Width="0" Visible=false Editable="false" />
            <GridColumn Field="LotSize" Title="Lot Size" Width="0" Visible=false Editable="false" />
            <GridColumn Field="LotWidth" Title="Lot Width" Width="0" Visible=false Editable="false" />
            <GridColumn Field="Phase" Title="Phase" Width="0" Visible=false Editable="false" />
            <GridColumn Field="BuildingNum" Title="Building Num" Width="0" Visible=false Editable="false" />
            <GridColumn Field="StickBuildingNum" Title="Stick Num" Width="0" Visible=false Editable="false" />
            <GridColumn Field="Notes" Title="Notes" Width="0" Visible =false Editable="false" />
            <GridColumn Field="ParkingNum" Title="Parking Num" Width="0" Visible =false Editable="false" />
            <GridColumn Field="HomeOrientation" Title="Home Orientation" Width="0" Visible =false Editable="false" />
            <GridColumn Field="StorageNum" Title="Storage Num" Width="0" Visible=false Editable="false" />
            <GridColumn Field="GarageNum" Title="Garage Num" Width="0" Visible=false Editable="false" />
            <GridColumn Field="ProjectedLotTakedown" Title="Proj. Lot Takedown Date" DisplayFormat="{0:MM/dd/yy}" Width="0" Visible=false Editable="false" />
            <GridColumn Field="ProjectedSalesReleaseDate" Title="Proj. Sales Release Date" DisplayFormat="{0:MM/dd/yy}" Width="0" Visible=false Editable="false" />
            <GridColumn Field="GeneralOptionBudget" Title="General Option Budget" Width="0" Visible=false Editable="false" />
            <GridColumn Field="HomesiteOptionSpendBudget" Title="Homesite Option Budget" Width="0" Visible=false Editable="false" />
            <GridColumn Field="IsSs" Width="0" Visible=false Editable="false" />
            <GridCommandColumn>
                @{
                    if(AllowEdit)
                    {
                        <GridCommandButton Title="Save" Command="Save" Icon="@FontIcon.Save" ShowInEdit="true" Class="tooltip-target telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary"></GridCommandButton>
                        <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                        @* <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton> *@
                        <GridCommandButton Title="Cancel" Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger tooltip-target"></GridCommandButton>
                    }
                }

            </GridCommandColumn>
        </GridColumns>

        <GridToolBarTemplate>
            @*             @if(SubdivisionID != 0)
            {
                <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Lot</GridCommandButton>
            }    *@        
            <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>
            @* <label for="JobsFieldsMultiSelect">Select Job Fields to Export</label>
            <TelerikMultiSelect Id="JobsFieldsMultiSelect" 
                                Class="selected-items-container"
                                Context="multiSelectContext"
                                @ref="MultiSelectRef"
                                Data="@JobsFields"
                                @bind-Value="@SelectedJobFields"
                                AutoClose="false"
                                Filterable="true"
                                TagMode="@MultiSelectTagMode.Multiple"
                                MaxAllowedTags="1"
                                FilterOperator="StringFilterOperator.Contains"
                                Placeholder="Select the job fields to export"
                                Width="350px">

                <ItemTemplate>
                    <input type="checkbox"
                           class="k-checkbox k-checkbox-md"
                           checked="@GetChecked(multiSelectContext)">
                    @multiSelectContext
                </ItemTemplate>
            </TelerikMultiSelect> *@
            <GridCommandButton Command="ExcelExport" Icon="@FontIcon.FileExcel" Class="k-button-add">Export to Excel</GridCommandButton>
            @if(AllowEdit){
                <TelerikFileSelect @ref="ImportFileRef" Class="import"
                AllowedExtensions="@AllowedExtensions"
                Multiple="false"
                MaxFileSize="@MaxSize"
                OnSelect="@OnSelectHandler"
                OnRemove="@OnRemoveHandler">
                    <SelectFilesButtonTemplate>
                        <TelerikFontIcon Icon="@FontIcon.Import" />
                        &nbsp; Import
                    </SelectFilesButtonTemplate>
                </TelerikFileSelect>
                if (IsImported)
                {
                    <GridCommandButton Icon="@FontIcon.Save" OnClick="@SaveImportedFile" Class="k-button-add">Save</GridCommandButton>
                }
            }

        </GridToolBarTemplate>
        <GridExport>
            <GridExcelExport FileName="Lots" OnBeforeExport="@OnExcelBeforeExport" AllPages="true" />
        </GridExport>
    </TelerikGrid>
}

@*<LotDetailsComponent @ref="LotDetailsModal" JobNumber=@SelectedJobNum></LotDetailsComponent>*@

@code {
    private List<JobDto>? lots;

    [Parameter]
    public int SubdivisionID { get; set; }
    public string SelectedJobNum { get; set; } = "";
    public string? SubdivisionName { get; set; }
    public SubdivisionDto? Subdivision { get; set; }
    private TelerikGrid<JobDto> GridRef { get; set; }
    //protected LotDetailsComponent? LotDetailsModal { get; set; }
    private TelerikMultiSelect<string, string> MultiSelectRef;
    private TelerikFileSelect ImportFileRef;
    private List<JobDto> JobsToBeUpdatedInDb = new List<JobDto>();
    private bool IsImported = false;
    public List<string> JobsFields { get; set; } = new List<string> { "SubdivisionName", "JobNumber", "LotNumber", "JobDesc", "JobAddress1", "JobAddress2", "JobCity", "JobState", "JobCounty", "JobZipCode",
    "AcquisitionLotCost", "ApprovedDepositRequirement", "ElevationCode", "ElevationDesc", "GarageOrientation", "JobPostingGroup", "LotAvailability", "LotCost", "LotPremium", "LotSectionCode", "LotStatus",
    "LotSwing", "LotUnit", "LotWidth", "ModelName", "OverLot", "Phase", "PlanCode", "PlanName", "PossessionStatus", "ProjectedTakeDownDate", "Restrictions", "StatsStickStartDate", "SubdivisionClass", "TakedownDate",
    "TakedownType", "Supervisor", "ProjectMgr", "SalesContact", "FieldSuper", "UserContact1", "UserContact2", "IsModel", "IsListed", "BuildingNum", "Notes", "StickBuilingNum" };
    public List<string> SelectedJobFields { get; set; } = new List<string> { "SubdivisionName", "JobNumber", "LotNumber", "JobDesc", "JobAddress1", "JobAddress2", "JobCity", "JobState", "JobCounty", "JobZipCode" };
    List<string> AllowedExtensions { get; set; } = new List<string>() { ".xlsx" };
    public int MaxSize { get; set; } = 10 * 1024 * 1024; //10 MB
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }
    private bool AllowEdit { get; set; } = true;
    private bool IsLoading { get; set; } = false;


    protected override async Task OnInitializedAsync()
    {
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }
    private async Task ShowDetails(GridCommandEventArgs args)
    {
        JobDto item = (JobDto)args.Item;
        SelectedJobNum = item.JobNumber;
        // await LotDetailsModal.Show();
        StateHasChanged();
    }
    protected override async Task OnParametersSetAsync()
    {
        if (SubdivisionID != 0)
        {
            var lotsTask = SubdivisionService.GetJobsAsync(SubdivisionID);
            var subdivsionTask = SubdivisionService.GetSubdivisionAsync(SubdivisionID);
            await Task.WhenAll(new Task[] { lotsTask, subdivsionTask });
            Subdivision = subdivsionTask.Result;
            SubdivisionName = Subdivision.SubdivisionName;
            lots = lotsTask.Result.Value;
            StateHasChanged();
        }
        else
        {
            lots = (await SubdivisionService.GetAllJobsAsync()).Value;
            SubdivisionName = null;
            StateHasChanged();
        }
    }
    private async void UpdateLot(GridCommandEventArgs args)
    {
        var updateJob = args.Item as JobDto;
        updateJob.SubdivisionId = SubdivisionID;//adding because JobDto requires subdivision num
        updateJob.Subdivision = new SubdivisionDto() { SubdivisionId = SubdivisionID, SubdivisionName = "not null", SubdivisionNum = "not null" };//TODO: fix hack to get past validation
        var response = await SubdivisionService.UpdateLotAsync(updateJob);
        ShowMessage(response.IsSuccess, response.Message);
        if (response.IsSuccess)
        {
            //reload
            if (SubdivisionID != 0)
            {
                lots = (await SubdivisionService.GetJobsAsync(SubdivisionID)).Value;
                StateHasChanged();
            }
            else
            {
                lots = (await SubdivisionService.GetAllJobsAsync()).Value;
                StateHasChanged();
            }
        }
        //TODO: success/error
    }
    private async void TestMultiple()
    {
        var response = await JobService.TestMultipleAsync();
        ShowMessage(response.IsSuccess, response.Message);
    }
    private async void AddLot(GridCommandEventArgs args)
    {
        var addJob = args.Item as JobDto;
        if (Subdivision != null)
        {
            addJob.SubdivisionId = SubdivisionID;
            addJob.Subdivision = new SubdivisionDto() { SubdivisionId = SubdivisionID, SubdivisionName = SubdivisionName, SubdivisionNum = Subdivision.SubdivisionNum };
        }
    }
    private async void CreateLot(GridCommandEventArgs args)
    {
        var addJob = args.Item as JobDto;
        if(addJob != null)
        {
            var response = await SubdivisionService.AddJobAsync(addJob);
            ShowMessage(response.IsSuccess, response.Message);
            if (response.IsSuccess)
            {               
                //reload
                if (SubdivisionID != 0)
                {
                    lots = (await SubdivisionService.GetJobsAsync(SubdivisionID)).Value;
                    StateHasChanged();
                }
                else
                {
                    lots = (await SubdivisionService.GetAllJobsAsync()).Value;
                    StateHasChanged();
                }
            }            
        }
    }

    private async Task OnExcelBeforeExport(GridBeforeExcelExportEventArgs args)
    {
        // args.Columns.Clear();
        // var column = new GridExcelExportColumn()
        //     {
        //         Title = "Subdivision Id",
        //         Field = nameof(JobDto.SubdivisionId)
        //     };
        // foreach (var selection in SelectedJobFields)
        // {
        //     var field = selection == "SubdivisionName" ? "Subdivision.SubdivisionName" : selection;
        //     args.Columns.Add(new GridExcelExportColumn { Title = selection, Field = field });
        // }


        var hiddenColunnsToAdd = new List<GridExcelExportColumn>()
        {
            new GridExcelExportColumn(){Title = "Job Address 2", Field = "JobAddress2" },
            new GridExcelExportColumn(){Title = "City", Field = "JobCity" },
            new GridExcelExportColumn(){Title = "State", Field = "JobState" },
            new GridExcelExportColumn(){Title = "Zip", Field = "JobZipCode" },
            new GridExcelExportColumn(){Title = "County", Field = "JobCounty" },
            new GridExcelExportColumn(){Title = "Lot Cost", Field = "LotCost" },
            new GridExcelExportColumn(){Title = "Lot Section Code", Field = "LotSectionCode" },
            new GridExcelExportColumn(){Title = "Lot Swing", Field = "LotSwing" },
            new GridExcelExportColumn(){Title = "Garage Orientation", Field = "GarageOrientation.Name" },
            new GridExcelExportColumn(){Title = "Lot Size", Field = "LotSize" },
            new GridExcelExportColumn(){Title = "Lot Width", Field = "LotWidth" },
            new GridExcelExportColumn(){Title = "Phase", Field = "Phase" },
            new GridExcelExportColumn(){Title = "Building Num", Field = "BuildingNum" },
            new GridExcelExportColumn(){Title = "Stick Num", Field = "StickBuildingNum" },           
            new GridExcelExportColumn(){Title = "Home Orientation Per Plan", Field = "BoolHomeOrientationPerPlan" },
            new GridExcelExportColumn(){Title = "Parking Num", Field = "ParkingNum" },
            new GridExcelExportColumn(){Title = "Storage Num", Field = "StorageNum" },
            new GridExcelExportColumn(){Title = "Garage Num", Field = "GarageNum" },
            new GridExcelExportColumn(){Title = "Construction Type", Field = "JobConstructionType.Description" },
            new GridExcelExportColumn(){Title = "IsSS", Field = "IsSs" },
            new GridExcelExportColumn(){Title = "Projected Lot Takedown", Field = "ProjectedLotTakedown" },
            new GridExcelExportColumn(){Title = "Projected Sales Release Date", Field = "ProjectedSalesReleaseDate" },
            new GridExcelExportColumn(){Title = "General Option Budget", Field = "GeneralOptionBudget" },
            new GridExcelExportColumn(){Title = "Homesite Option Spend Budget Type", Field = "HomesiteOptionSpendBudget" },
            new GridExcelExportColumn(){Title = "Notes", Field = "Notes" }
        };
        args.Columns.AddRange(hiddenColunnsToAdd);
        args.Columns[0].Width = "250px";//subdivision
        args.Columns[1].Width = "100px";//job num
        args.Columns[2].Width = "50px";//lot numb
        args.Columns[3].Width = "150px";//job desc
        args.Columns[4].Width = "200px";//address1
        args.Columns[5].Width = "100px";//addres 2
        args.Columns[6].Width = "100px";//city
        args.Columns[7].Width = "50px";//state
        args.Columns[8].Width = "50px";//zip
        args.Columns[9].Width = "80px";//county
        args.Columns[10].Width = "80px";//lot cost
        args.Columns[11].Width = "50px";//lot section
        args.Columns[12].Width = "50px";//lot swing
        args.Columns[13].Width = "50px";//garage orient
        args.Columns[14].Width = "50px";//lot size
        args.Columns[15].Width = "50px";//lot width
        args.Columns[16].Width = "80px";//phase
        args.Columns[17].Width = "80px";//building numb
        args.Columns[18].Width = "80px";//stick num,
        args.Columns[19].Width = "80px"; //home orient per plan
        args.Columns[20].Width = "50px";//parking numb
        args.Columns[21].Width = "50px";//storage num
        args.Columns[22].Width = "50px";//garage num
        args.Columns[23].Width = "100px";//construction type
        args.Columns[24].Width = "50px";//isSS
        args.Columns[25].Width = "100px";//ProjectedLotTakedown
        args.Columns[25].NumberFormat = BuiltInNumberFormats.GetShortDate();//ProjectedLotTakedown
        args.Columns[26].Width = "100px";//ProjectedSalesReleaseDate
        args.Columns[26].NumberFormat = BuiltInNumberFormats.GetShortDate();//ProjectedSalesReleaseDate
        args.Columns[27].Width = "100px";//GeneralOptionBudget
        args.Columns[28].Width = "100px";//HomesiteOptionSpendBudget
        args.Columns[29].Width = "500px";//notes
    }

    async Task OnSelectHandler(FileSelectEventArgs args)
    {
        IsLoading = true;

        if (args.Files.Count() > 1)
        {
            args.IsCancelled = true;
            ShowMessage(false, "Too many files. You can only upload one at a time.");
            return;

        }
        foreach (var file in args.Files)
        {
            var jobsResponse = await SubdivisionService.ImportExcel(file);
            JobsToBeUpdatedInDb.Clear();

            JobsToBeUpdatedInDb = jobsResponse.Value;           
            GridRef.Rebind();
            IsImported = true;
        }

        IsLoading = false;
    }

    bool CheckIfEqual(JobDto job1, JobDto job2)
    {
        return (job1.JobAddress2 == job2.JobAddress2 && job1.JobAddress1 == job2.JobAddress1 && job1.JobState == job2.JobState && job1.JobCity == job2.JobCity && job1.JobDesc == job2.JobDesc);
    }

    async Task OnRemoveHandler(FileSelectEventArgs args)
    {
        IsImported = false;
        //  await OnParametersSetAsync();//Why is OnParametersSet called here?
    }

    async Task SaveImportedFile()
    {
        IsLoading = true;

        var updateResponse = await SubdivisionService.UpdateJobsAsync(JobsToBeUpdatedInDb);
        if (updateResponse.IsSuccess)
        {
            IsImported = false;
            ImportFileRef.ClearFiles();
            //reload
            if (SubdivisionID != 0)
            {
                lots = (await SubdivisionService.GetJobsAsync(SubdivisionID)).Value;
                StateHasChanged();
            }
            else
            {
                lots = (await SubdivisionService.GetAllJobsAsync()).Value;
                StateHasChanged();
            }
        }
        ShowMessage(updateResponse.IsSuccess, updateResponse.Message);

        IsLoading = false;
    }

    bool GetChecked(string text)
    {
        return SelectedJobFields.Contains(text);
    }

    async void ShowMessage(bool success, string message)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
