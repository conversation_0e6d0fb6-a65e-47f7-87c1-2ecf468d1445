﻿using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.Identity.Web;
using NLog;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class MaterialColorDocumentService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public MaterialColorDocumentService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }


        public async Task<ResponseModel<string>> GetTokenAsync()
        {
            var scopes = _configuration.GetSection("DownstreamApi:Scopes").Value;
            var scopesArray = new string[] { scopes };
            var token = await _tokenAcquisition.GetAccessTokenForUserAsync(scopesArray, tokenAcquisitionOptions: new TokenAcquisitionOptions { ForceRefresh = true });
            //ForceRefresh = true ignores the token cache
            //this is wrong, but seems to fix errors about multiple tokens in the cache
            return new ResponseModel<string>() { Value = token, IsSuccess = true };
        }

        public async Task<ResponseModel<MaterialColorPredefinedDto>> DeleteDocumentAsync(MaterialColorPredefinedDto document)
        {
            var returnDocument = new ResponseModel<MaterialColorPredefinedDto>();
            try
            {

                returnDocument = await _downstreamAPI.PutForUserAsync<MaterialColorPredefinedDto, ResponseModel<MaterialColorPredefinedDto>>(
                            "DownstreamApi", document,
                             options =>
                             {
                                 options.RelativePath = "api/materialcolordocument/deletedocument/";
                             });


            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return returnDocument;
        }

        public async Task<ResponseModel<byte[]>> DownloadDocumentAsync(DocumentUploadModel documentDetails)
        {
            try
            {

                var response = await _downstreamAPI.PostForUserAsync<DocumentUploadModel, ResponseModel<byte[]>>(
                           "DownstreamApi", documentDetails,
                            options => {
                                options.RelativePath = "api/materialcolordocument/DownloadDocument/";
                            });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<byte[]>() { Value = new byte[0], IsSuccess = true };

        }

    }
}
