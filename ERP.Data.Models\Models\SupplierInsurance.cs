﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class SupplierInsurance
{
    public string SubNavNumber { get; set; } = null!;

    public int LineNo { get; set; }

    public string? InsuranceTypeCode { get; set; }

    public byte InsuranceRequired { get; set; }

    public string PolicyNumber { get; set; } = null!;

    public DateTime PolicyStartDate { get; set; }

    public DateTime PolicyExpirationDate { get; set; }

    public string InsuranceCompanyName { get; set; } = null!;

    public string InsuranceContactName { get; set; } = null!;

    public string InsuranceContactPhoneNo { get; set; } = null!;

    public decimal Coverageamount { get; set; }

    public DateTime Createddatetime { get; set; }

    public string? Createdby { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? Updatedby { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public int? SubNumber { get; set; }

    public virtual SupplierInsuranceType? InsuranceTypeCodeNavigation { get; set; }

    public virtual Supplier? SubNumberNavigation { get; set; }
}
