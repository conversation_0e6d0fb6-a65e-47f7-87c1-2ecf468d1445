﻿<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="350px"
               Height="160px">
    <WindowTitle>
        Confirm delete 
    </WindowTitle>
    <WindowContent>
        <p>Are you sure you want to delete selected options?</p>
        
        <button type="submit" @onclick="HandleDeleteOk" class="btn btn-primary">Yes</button>
        <button type="button" @onclick="CancelDelete" class="btn btn-secondary">Cancel</button>
        
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {
    public bool IsModalVisible { get; set; }
    [Parameter]
    public EventCallback HandleDeleteSubmit { get; set; }

    public async Task Show()
    {
        IsModalVisible = true;
    }

    public void Hide()
    {
        IsModalVisible = false;
    }

    void CancelDelete()
    {
        IsModalVisible = false;
    }

    private async void HandleDeleteOk()
    {
        await HandleDeleteSubmit.InvokeAsync();
    }
}
