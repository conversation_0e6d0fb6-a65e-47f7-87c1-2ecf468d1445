﻿@using ERP.Data.Models;
@using ERP.Data.Models.Dto;

@inject CostService CostService
@inject SubdivisionService SubdivisionService
@inject TradeService TradeService
@inject PoService POService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Cost for Item
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@CostToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />
            <p>Cost To Add to Item: @Item?.ItemDesc
                <TelerikDropDownList @bind-Value="@CostToAdd.MasterItemId"
                                     Data="@Items"
                                     ScrollMode="@DropDownScrollMode.Virtual"
                                     ItemHeight="40"
                                     OnChange="@OnItemSelected"
                                     PageSize="20"
                                     TextField="ItemDesc"
                                     ValueField="MasterItemId"
                                     Filterable="true"
                                     FilterOperator="StringFilterOperator.Contains"
                                     Width="100%">
                </TelerikDropDownList>
            </p>
            <p>
                <label>New Cost</label>
                <TelerikNumericTextBox Width="50%" @bind-Value="@CostToAdd.UnitCost"></TelerikNumericTextBox> per @Item.TakeoffUnit
            </p>
            <p>
                <label>Effective Date</label>
                <TelerikRadioGroup Data="@EffectiveDateTypes"
                                   @bind-Value="@CostToAdd.EditType"></TelerikRadioGroup>
                @if(CostToAdd.EditType == CostEditType.Next)
                {
                    <TelerikDatePicker @bind-Value="@CostToAdd.CostEffectiveDate"></TelerikDatePicker>
                }
                
            </p>
            <p>
                <label>Select Subdivision(s)</label>
                <TelerikMultiSelect @bind-Value="@CostToAdd.Subdivisions"
                                     Data="@AllSubdivisions"
                                     TextField="SubdivisionName"
                                     ValueField="SubdivisionId"
                                     Placeholder="Select Subdivision"
                                     Filterable="true"
                                     FilterOperator="StringFilterOperator.Contains"
                                     Width="100%">                    
                </TelerikMultiSelect>
            </p>
            <p>
                <label>Select Supplier</label>
                <TelerikDropDownList @bind-Value="@CostToAdd.SubNumber"
                                     Data="@SupplierData"
                                     ScrollMode="@DropDownScrollMode.Virtual"
                                     ItemHeight="40"
                                     PageSize="20"
                                     TextField="SubName"
                                     ValueField="SubNumber"
                                     DefaultText="Select Supplier"
                                     Filterable="true"
                                     FilterOperator="StringFilterOperator.Contains"
                                     Width="100%">
                </TelerikDropDownList>
            </p>
            @*<p>TODO: Also need to add inputs for tax group, warranty info, and supplier product code</p>*@
            <button type="submit" class="btn btn-primary">Add Cost</button>                   
            <button type="button" @onclick="CancelAddCost" class="btn btn-secondary">Cancel</button> 
            <div style=@submittingStyle>Adding cost. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {
    [Parameter]
    public bool IsModalVisible { get; set; }
    public CostModel CostToAdd { get; set; } = new CostModel();
    //[Parameter]
    public ModelManagerItemModel? Item { get; set; }
    [Parameter]
    public List<ModelManagerItemModel>? Items { get; set; }
    [Parameter]
    public int? SupplierNumber { get; set; }
    [Parameter]
    public EventCallback<ResponseModel<CostModel>> HandleAddSubmit { get; set; }


    public List<SubdivisionDto>? AllSubdivisions { get; set; }

    //  public List<int>? SelectedSubdivisions;
    public DateTime? SelectedDate;
    public string? ErrorMessage;
    public bool? ShowError;
    private string submittingStyle = "display:none";
    public List<SupplierDto>? SupplierData { get; set; }
    public int? SelectedSupplierId { get; set; }
    public List<CostEditType> EffectiveDateTypes = new List<CostEditType>();
    public CostEditType? SelectedEffectiveDateType { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        Item = Items != null ? Items.FirstOrDefault() : new ModelManagerItemModel();
    }
    public async Task Show()
    {
        IsModalVisible = true;              
        CostToAdd = new CostModel();
        var tradesTask = POService.GetSuppliersAsync(true, false);
        var subdivisionTask = SubdivisionService.GetSubdivisionsAsync();
        await Task.WhenAll(new Task[] { tradesTask, subdivisionTask });
        SupplierData = tradesTask.Result.Value;
        CostToAdd.Subdivisions = new List<int> { 1 }; //set divisiion default as picked unless they pick something else
        AllSubdivisions = subdivisionTask.Result.Value;
        EffectiveDateTypes = new List<CostEditType>()
        {
            CostEditType.Current,
            CostEditType.Next,
           // CostEditType.Future //future not allowed for now
        };
        if (SupplierNumber != null)
        {
            CostToAdd.SubNumber = (int)SupplierNumber;
        }
        Item = Items?.First();
        if(Items != null)
        {
            CostToAdd.MasterItemId = Items.FirstOrDefault().MasterItemId;
        }
        StateHasChanged();
    }

    private async void HandleValidAddSubmit()
    {
        //TODO: make sure effective date is in future if they pick it 
        submittingStyle = "";
        CostToAdd.MasterItemId = Item.MasterItemId;
        var responseItem = await CostService.AddCostAsync(CostToAdd);
        submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(responseItem);
    }
    private void OnItemSelected()
    {
        Item = Items.SingleOrDefault(x => x.MasterItemId == CostToAdd.MasterItemId);

    }
    void CancelAddCost()
    {
        IsModalVisible = false;
    }
    public void Hide()
    {
        IsModalVisible = false;        
    }

}
