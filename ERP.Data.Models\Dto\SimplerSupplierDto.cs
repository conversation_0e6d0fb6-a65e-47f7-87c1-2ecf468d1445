﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    /// <summary>
    /// This may not be needed. Use this for quick add Supplier
    /// </summary>
    public class SimplerSupplierDto : IMapFrom<Supplier>
    {
        public string? ShortName { get; set; }
        public string? SubName { get; set; }
    }
}
