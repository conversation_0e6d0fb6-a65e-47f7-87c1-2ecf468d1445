﻿@page "/addenda"
@inject IJSRuntime JsRuntime
@inject AddendaService AddendaService
@inject SubdivisionService SubdivisionService
@inject TradeService TradeService
@inject PoService PoService
@inject PlanService PlanService
@inject IDocusignService DocusignService
@inject AuthenticationStateProvider AuthenticationStateProvider
@using System.Diagnostics;
<style>
    .MyTelerikNotification .k-notification-container .k-notification {
    font-family: "Roboto", sans-serif;
    font-size: .75rem;
    }

</style>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<div class="col-lg-12">
    <div class="card" style="background-color:#2e5771">
        <div class="card-body" style="padding:0.5rem">
            <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Generate Addenda</h7>
        </div>
    </div>
</div>

<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="/">Home</a></li>
    <li class="breadcrumb-item active">Generate Addenda</li>
</ol>

<br />
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-body">
                    <div class="mb-3">
                        <label for="vendors" class="form-label">Select Vendors</label>
                        <TelerikMultiSelect Data="@Suppliers"
                        Width="100%"
                        TextField="SubName"
                        ValueField="SubNumber"
                        @bind-Value="SelectedSupplierIds"
                        FilterOperator="@StringFilterOperator.Contains"
                        Filterable="true">
                        </TelerikMultiSelect>
                    </div>
                    <div class="mb-3">
                        <label for="subdivisions" class="form-label">Select Subdivisions</label>
                        <TelerikMultiSelect Data="@Subdivisions"
                        Width="100%"
                        AutoClose="false"
                        ValueField="SubdivisionId"
                        TextField="SubdivisionName"
                        @bind-Value="SelectedSubdivisionIds"
                        OnChange="@SubdivisionChanged"
                        FilterOperator="@StringFilterOperator.Contains"
                        Filterable="true">
                        </TelerikMultiSelect>
                    </div>
                    <div class="mb-3">
                        <label for="subdivisions" class="form-label">Select Plans</label>
                        <TelerikMultiSelect Data="@PhasePlans"
                        Width="100%"
                        AutoClose = "false"
                        ValueField="PhasePlanId"
                        TextField="SubdivisionPlanNamePlanNumberDisplay"
                        Context="phasePlanMultiSelectContext"
                        @ref="PhasePlanMultiSelectRef"
                        @bind-Value="SelectedPhasePlanIds"
                        FilterOperator="@StringFilterOperator.Contains"
                        Filterable="true">
                            <HeaderTemplate>
                                <label style="padding: 4px 8px;">
                                    <TelerikCheckBox TValue="bool"
                                    Value="@IsAllSelected()"
                                    ValueChanged="@( (bool v) => ToggleSelectAll(v) )">
                                    </TelerikCheckBox>
                                    &nbsp;Select All
                                </label>
                            </HeaderTemplate>
                            <ItemTemplate>
                                <input type="checkbox"
                                class="k-checkbox k-checkbox-md"
                                checked="@GetChecked(phasePlanMultiSelectContext.PhasePlanId)">
                                @phasePlanMultiSelectContext.SubdivisionPlanNamePlanNumberDisplay 
                            </ItemTemplate>
                        </TelerikMultiSelect>
                    </div>
                    <div class="mb-3">
                        <label for="effectiveDate" class="form-label">Select Effective Date</label>
                        <TelerikDatePicker Width="100%" @bind-Value="SelectedEffectiveDate">
                        </TelerikDatePicker>
                    </div>
                    <div class="d-flex justify-content-end">
                        <TelerikButton OnClick="DownloadPdfs" Class="k-button-success me-2">Generate Addenda</TelerikButton>
                       @*  <TelerikButton OnClick="SendAddendasToDocusign" Class="k-button-add">Send to Docusign</TelerikButton>  *@
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <!-- Empty column on the right for layout purposes, can be adjusted or removed -->
        </div>
    </div>
</div>




<div style="@submittingStyle">Submitting. Please wait.</div>



@code {

    private List<SubdivisionDto>? Subdivisions;
    private List<SupplierDto>? Suppliers;
    private List<PhasePlanDto>? PhasePlans { get; set; } = new List<PhasePlanDto>();
    public int? SelectedSubdivisionId { get; set; }
    public int? SelectedSupplierId { get; set; }
    public List<int>? SelectedSubdivisionIds { get; set; }
    public List<int> SelectedPhasePlanIds { get; set; } = new List<int>();
    public List<int>? SelectedSupplierIds { get; set; }
    public DateTime? SelectedEffectiveDate {get; set;}
    private TelerikMultiSelect<PhasePlanDto, int>? PhasePlanMultiSelectRef;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }
    private string? submittingStyle { get; set; } = "display:none";


    protected override async Task OnInitializedAsync()
    {
        var getSubdivisions = await SubdivisionService.GetSubdivisionsAsync();
        Subdivisions = getSubdivisions.Value;
        var getSuppliers = await PoService.GetSuppliersAsync();
        Suppliers = getSuppliers.Value;

    }   
    async Task SendAddendasToDocusign()
    {
        submittingStyle = "";
        if (SelectedSubdivisionIds != null && SelectedSupplierIds != null && SelectedSubdivisionIds.Any() && SelectedSupplierIds.Any())
        {
            foreach(var supplierId in SelectedSupplierIds)
            {
                var docList = new List<byte[]>();
                foreach(var subdivId in SelectedSubdivisionIds)
                {
                    var selectedSubdivision = Subdivisions.FirstOrDefault(x => x.SubdivisionId == subdivId).SubdivisionName;
                    var selectedSupplierName = Suppliers.FirstOrDefault(x => x.SubNumber == supplierId).SubName;
                    //var getItems = (await AddendaService.GetAddendaAsync((int)subdiv, (int)supplier, SelectedEffectiveDate ?? DateTime.Now)).Value;
                    var model = new SelectAddendaModel()
                        {
                            SubdivsionId = subdivId,
                            SupplierId = supplierId,
                            SelectedPhasePlanIds = SelectedPhasePlanIds,//note some of the phase plans may not go with this subdivision, but that will be filtered on the back end
                            CostDate = SelectedEffectiveDate ?? DateTime.Now
                        };
                    var getItems = (await AddendaService.GetAddendaSelectPlansAsync(model)).Value;
                    var document = await GenerateDocument(getItems, selectedSubdivision, selectedSupplierName);
                    // await DownloadDocuent(document);
                    docList.Add(document);
                }
                await SendToDocusign(docList);
            }

            ShowSuccessOrErrorNotification("Sent to Docusign", true);
        }
        submittingStyle = "display:none";
    }
    //not used
    async Task DownloadPdf()
    {
        //not used
        if(SelectedSubdivisionId != null && SelectedSupplierId != null)
        {
            var selectedSubdivision = Subdivisions.FirstOrDefault(x => x.SubdivisionId == SelectedSubdivisionId).SubdivisionName;
            var selectedSupplierName = Suppliers.FirstOrDefault(x => x.SubNumber == SelectedSupplierId).SubName;
            var getItems = (await AddendaService.GetAddendaAsync((int)SelectedSubdivisionId, (int)SelectedSupplierId, SelectedEffectiveDate ?? DateTime.Now)).Value;
            var document = await GenerateDocument(getItems, selectedSubdivision, selectedSupplierName);
            await DownloadDocuent(document);
        }
    }
    async Task DownloadPdfs()
    {
        if (SelectedSubdivisionIds != null && SelectedSupplierIds != null && SelectedSubdivisionIds.Any() && SelectedSupplierIds.Any())
        {
            foreach(var subdivId in SelectedSubdivisionIds)
            {
                foreach(var supplierId in SelectedSupplierIds)
                {
                    var selectedSubdivision = Subdivisions.FirstOrDefault(x => x.SubdivisionId == subdivId).SubdivisionName;
                    var selectedSupplierName = Suppliers.FirstOrDefault(x => x.SubNumber == supplierId).SubName;
                    // var getItems = (await AddendaService.GetAddendaAsync((int)subdivId, (int)supplierId, SelectedEffectiveDate ?? DateTime.Now)).Value;
                    var model = new SelectAddendaModel()
                        {
                            SubdivsionId = subdivId,
                            SupplierId = supplierId,
                            SelectedPhasePlanIds = SelectedPhasePlanIds,//note some of the phase plans may not go with this subdivision, but that will be filtered on the back end
                            CostDate = SelectedEffectiveDate ?? DateTime.Now
                        };
                    var getItems = (await AddendaService.GetAddendaSelectPlansAsync(model)).Value;
                    var document = await GenerateDocument(getItems, selectedSubdivision, selectedSupplierName);
                    await DownloadDocuent(document);
                }
            }
        }

    }
    private async Task SubdivisionChanged()
    {
        if (SelectedSubdivisionIds != null && SelectedSubdivisionIds.Count != 0)
        {
            var plans = new List<PhasePlanDto>();
            foreach(var subdiv in SelectedSubdivisionIds)
            {
                var getData = await PlanService.GetPhasePlansInSubdivisionAsync(subdiv);
                plans.AddRange(getData.Value);
            }
            PhasePlans = plans;
        }

    }
    // async Task GenerateDocumentAndDownload(List<AddendaModel> items)
    // {
    //     var selectedSubdivision = Subdivisions.FirstOrDefault(x => x.SubdivisionId == SelectedSubdivisionId).SubdivisionName;
    //     var selectedSupplierName = Suppliers.FirstOrDefault(x => x.SubNumber == SelectedSupplierId).SubName;
    //     byte[] fileData = ERP.Web.DocumentProcessing.GenerateAddendaPdf.GenerateFile(items, selectedSubdivision, selectedSupplierName, SelectedEffectiveDate ?? DateTime.Now);
    //     DemoFileExporter.Save(JsRuntime, fileData, "application/pdf", $"Addendum.pdf");
    // }
    async Task DownloadDocuent(byte[] fileData)
    {        
        DemoFileExporter.Save(JsRuntime, fileData, "application/pdf", $"Addendum.pdf");
    }
    async Task SendToDocusign(List<byte[]> fileData)
    {
        DocusignService.SendEnvelope("Ernie", "<EMAIL>", "Ernie2", "<EMAIL>", fileData);
    }
    async Task<byte[]> GenerateDocument(List<AddendaModel> items, string SubdivName, string SupplierName)
    {
        // var selectedSubdivision = Subdivisions.FirstOrDefault(x => x.SubdivisionId == SelectedSubdivisionId).SubdivisionName;
        // var selectedSupplierName = Suppliers.FirstOrDefault(x => x.SubNumber == SelectedSupplierId).SubName;
        byte[] fileData = ERP.Web.DocumentProcessing.GenerateAddendaPdf.GenerateFile(items, SubdivName, SupplierName, SelectedEffectiveDate ?? DateTime.Now);
        return fileData;
    }
    void ToggleSelectAll(bool selectAll)
    {
        SelectedPhasePlanIds.Clear();
        if (selectAll)
        {
            SelectedPhasePlanIds.AddRange(PhasePlans.Select(x => x.PhasePlanId));
        }
        PhasePlanMultiSelectRef.Rebind();
    }

    bool IsAllSelected()
    {
        return SelectedPhasePlanIds?.Count == PhasePlans?.Count;
    }

    // for the item checkboxes
    bool GetChecked(int selected)
    {
        return SelectedPhasePlanIds.Contains(selected);
    }
    private void ShowSuccessOrErrorNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
