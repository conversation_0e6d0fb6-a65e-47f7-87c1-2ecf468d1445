﻿using Telerik.Documents.Primitives;
using Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.Export;
using Telerik.Windows.Documents.Fixed.FormatProviders.Pdf;
using Telerik.Windows.Documents.Fixed.Model.Editing;
using Telerik.Windows.Documents.Fixed.Model.Fonts;
using Telerik.Windows.Documents.Fixed.Model;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Table = Telerik.Windows.Documents.Fixed.Model.Editing.Tables.Table;
using TableRow = Telerik.Windows.Documents.Fixed.Model.Editing.Tables.TableRow;
using Telerik.Documents.Core.Fonts;
using FontFamily = Telerik.Documents.Core.Fonts.FontFamily;
using TableCell = Telerik.Windows.Documents.Fixed.Model.Editing.Tables.TableCell;
using Telerik.Windows.Documents.Fixed.Model.Editing.Tables;
using static ERP.Web.Components.ApproveSelectedOptions;
using ERP.Data.Models.ExtensionMethods;
using Telerik.Windows.Documents.Fixed.Model.ColorSpaces;

namespace ERP.Web.DocumentProcessing
{
    public static class GenerateReleasePdf
    {
        public static byte[] GenerateReleaseFile(List<TbBuiltOptionDto> options, JobDto job, List<BuyerDto> buyers, SelectedApprovedOrAllOptions selectedApprovedOrAll, List<AvailablePlanOptionDto> allOptions = null)
        {           
            PdfFormatProvider formatProvider = new PdfFormatProvider();
            formatProvider.ExportSettings.ImageQuality = ImageQuality.High;

            Telerik.Documents.ImageUtils.ImagePropertiesResolver defaultImagePropertiesResolver = new Telerik.Documents.ImageUtils.ImagePropertiesResolver();
            Telerik.Windows.Documents.Extensibility.FixedExtensibilityManager.ImagePropertiesResolver = defaultImagePropertiesResolver;

            byte[] renderedBytes = null;
            using (MemoryStream ms = new MemoryStream())
            {
                RadFixedDocument document = null;
                if (allOptions == null)
                {
                    document = CreateReleaseDocument(options, job, buyers, selectedApprovedOrAll);
                }
                else
                {
                    document = CreateReleaseDocument(options, job, buyers, selectedApprovedOrAll, allOptions);
                }
                
                formatProvider.Export(document, ms);
                renderedBytes = ms.ToArray();
            }

            return renderedBytes;
        }

        //public static byte[] GenerateReleaseFile(List<AvailablePlanOptionDto> options, List<TbBuiltOptionDto> builtOptionDtos)
        //{
        //    PdfFormatProvider formatProvider = new PdfFormatProvider();
        //    formatProvider.ExportSettings.ImageQuality = ImageQuality.High;

        //    Telerik.Documents.ImageUtils.ImagePropertiesResolver defaultImagePropertiesResolver = new Telerik.Documents.ImageUtils.ImagePropertiesResolver();
        //    Telerik.Windows.Documents.Extensibility.FixedExtensibilityManager.ImagePropertiesResolver = defaultImagePropertiesResolver;

        //    byte[] renderedBytes = null;
        //    using (MemoryStream ms = new MemoryStream())
        //    {
        //        RadFixedDocument document = CreateReleaseDocument(options, builtOptionDtos);
        //        formatProvider.Export(document, ms);
        //        renderedBytes = ms.ToArray();
        //    }

        //    return renderedBytes;
        //}

        private static readonly double defaultLeftIndent = 100;
        private static readonly double defaultLineHeight = 18;
        public static RadFixedDocument CreateReleaseDocument(List<TbBuiltOptionDto> options, JobDto job, List<BuyerDto> buyers, SelectedApprovedOrAllOptions selectedApprovedOrAll, List<AvailablePlanOptionDto> allOptions = null)
        {
            RadFixedDocument document = new RadFixedDocument();

            // var Items = itemDetails.OrderBy(x => x.ItemDesc).GroupBy(x => new {x.HouseType, x.Activity}).GroupBy(x => x.Key.HouseType).ToList();

            var newEditor = new RadFixedDocumentEditor(document);
            newEditor.SectionProperties.PageMargins = new Thickness(100, 200, 100, 100);

            try
            {
                //TODO: Approved ones is not really correct, need to sum up the correct qty, etc, if there has been an add and delete
                //Use most recent, but not if it's deleted
                //var mostRecentOptions = options.Where(x => x.MostRecent == true && x.Removed == false).ToList();
                //var approvedOptions = options.Where(x => x.BuilderApproved == 1).ToList();

                Block block1 = new Block();
                block1.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Center;
                block1.TextProperties.UnderlinePattern = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.UnderlinePattern.Single;
                block1.TextProperties.UnderlinePattern = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.UnderlinePattern.Single;
                block1.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Construction Release - Options and Selections List");
                block1.InsertLineBreak();

                newEditor.InsertBlock(block1);

                Block block2 = new Block();
                block2.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Center;
                block2.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $"Generated {DateTime.Now.ToString("MM/dd/yyyy")}");
                block2.InsertLineBreak();
                block2.InsertLineBreak();
                block2.InsertLineBreak();
                newEditor.InsertBlock(block2);

                var info = "Before starting any work, the Subcontractor is responsible for thoroughly reviewing all project documentation, including the options and selections listed below, the Scope of Work, plans, specifications, and site conditions.  Subcontractor must identify and report any errors, omissions, or conflicts that could affect performance, compliance, or warranties. If any discrepancies are found, the Subcontractor must immediately notify Van Metre's on-site Project Manager and may not proceed until the issue is resolved.";
                Block block3 = new Block();
                block3.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
                block3.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, info);
                block3.InsertLineBreak();
                block3.InsertLineBreak();
                block3.InsertLineBreak();
                newEditor.InsertBlock(block3);

                Block block4 = new Block();
                block4.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
                block4.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Model / Floor Plan: ");
                block4.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $"{job.PlanName} / {options.First()?.PlanOption?.PhasePlan?.MasterPlan?.PlanNum}");
                block4.InsertLineBreak();
                block4.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Home Orientation: ");
                block4.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $"{(job.HomeOrientationPerPlan == null ? "N/A" : job.HomeOrientationPerPlan == true ? "Per Plan" : "Reversed")}");
                block4.InsertLineBreak();
                block4.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Garage Orientation: ");
                block4.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $"{job.GarageOrientation?.Name}");
                block4.InsertLineBreak();
                block4.InsertLineBreak();
                block4.InsertLineBreak();
                newEditor.InsertBlock(block4);

                //Block block4 = new Block();
                //var selectedOptionsHeadline = selectedApprovedOrAll == SelectedApprovedOrAllOptions.Selected ? $"Options currently selected for the Home" : selectedApprovedOrAll == SelectedApprovedOrAllOptions.All ? $"Highlighted are Standard Options in Group A and B currently selected for the Home" : $"Approved Options for the Home";
                //block4.TextProperties.HighlightColor = allOptions != null ? new RgbColor(255, 255, 0) : new RgbColor(255, 255, 255);
                //block4.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, selectedOptionsHeadline);
                //block4.InsertLineBreak();
                //block4.InsertLineBreak();
                //block4.InsertLineBreak();
                //newEditor.InsertBlock(block4);

                if (allOptions == null)
                {
                    var optionsGrouped = options.GroupBy(x => x.OptionGroupId);
                    foreach (var group in optionsGrouped)
                    {
                        Block block = new Block();
                        block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"{group.First()?.OptionGroup?.OptionGroupName}");
                        newEditor.InsertBlock(block);
                        InsertOptionsTable(newEditor, group.ToList()); 
                        newEditor.InsertLineBreak();
                        newEditor.InsertLineBreak();
                    }
                }
                else
                {
                    var optionsGrouped = allOptions.Where(x => x.OptionTypeId == 7 && (x.OptionGroupId == 1 || x.OptionGroupId == 2)).GroupBy(x => x.OptionGroupId);// option group Id = 1 for A. Structural and 2 for B. Non Structural. Option type id = 7 for standard options
                    foreach (var group in optionsGrouped)
                    {
                        Block block = new Block();
                        block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"{group.First()?.OptionGroup?.OptionGroupName}");
                        newEditor.InsertBlock(block);
                        InsertOptionsTable(newEditor, options, group.ToList());
                        newEditor.InsertLineBreak();
                        newEditor.InsertLineBreak();
                    }
                }
            }
            catch (Exception ex)
            {
                var debug = ex.Message;
            }
            DrawHeaderToDocument(document, options, job, buyers);
            DrawFooterToDocument(document, selectedApprovedOrAll);
            return document;
        }

        private static void DrawReleaseHeading(FixedContentEditor editor, List<TbBuiltOptionDto> options, JobDto job, List<BuyerDto> buyers)
        {
            Block block = new Block();
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
            block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Subdivision: {job.Subdivision.MarketingName}");
            block.InsertLineBreak();
            block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Lot: {job.LotNumber}");
            block.InsertLineBreak(); 
            block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"{job.JobAddress1}");
            block.InsertLineBreak();
            block.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"{job.JobCity}, {job.JobState} {job.JobZipCode}");
            block.InsertLineBreak();
            editor.DrawBlock(block, new Size(300, 100));
        }

        private static void DrawReleaseHeading(FixedContentEditor editor, List<AvailablePlanOptionDto> options, List<TbBuiltOptionDto> tbBuiltOptionDtos)
        {
            Block block = new Block();
            block.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Left;
            block.TextProperties.Font = FontsRepository.HelveticaBoldOblique;
            block.InsertText("Selected Options");
            block.InsertLineBreak();
            editor.DrawBlock(block, new Size(200, 100));
            editor.Position.Translate(50, 120);
            Block block2 = new Block();
            block2.TextProperties.Font = FontsRepository.Helvetica;
            block2.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Sales Manager:");
            block2.InsertLineBreak();
            block2.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Customer: ");
            block2.InsertLineBreak();
            block2.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Community: ");
            block2.InsertLineBreak();
            block2.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Phase: ");
            block2.InsertText($"");
            block2.InsertLineBreak();
            editor.DrawBlock(block2, new Size(300, 120));
            editor.Position.Translate(400, 120);
            Block block3 = new Block();
            block3.TextProperties.Font = FontsRepository.Helvetica;
            block3.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Date: ");
            block3.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $"{DateTime.Now}");
            block3.InsertLineBreak();
            block3.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Contract Date: ");
            block3.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $" ");
            block3.InsertLineBreak();
            block3.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Model: ");
            block3.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $"{options.FirstOrDefault()?.PhasePlan?.MasterPlan?.PlanName}");
            block3.InsertLineBreak();
            block3.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, $"Elevation: ");
            block3.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Normal, $"{options.FirstOrDefault(x => x.Elevation == true)?.OptionLongDesc}");
            block3.InsertLineBreak();
            editor.DrawBlock(block3, new Size(300, 120));
            block2.InsertLineBreak();
            editor.Position.Translate(50, 120);
            editor.DrawLine(new Point(50, 90), new Point(650, 90));
        }


        private static void InsertOptionsTable(RadFixedDocumentEditor editor, List<TbBuiltOptionDto> options)
        {
            try
            {
                Table table = CreateTableWithHeaderRow();

                foreach (var option in options)
                {
                    TableRow addRow = table.Rows.AddTableRow();

                    TableCell cell1 = addRow.Cells.AddTableCell();
                    Block cellBlock1 = cell1.Blocks.AddBlock();
                    cellBlock1.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Center;
                    cellBlock1.TextProperties.FontSize = 11;
                    cellBlock1.InsertText($"{option.OptionCode}");
                    cell1.PreferredWidth = 120;

                    TableCell cell2 = addRow.Cells.AddTableCell();
                    Block cellBlock2 = cell2.Blocks.AddBlock();
                    cellBlock2.TextProperties.FontSize = 11;
                    cellBlock2.InsertText($"{option.OptionDesc.ToUpper()}");
                    cell2.PreferredWidth = 450;

                    TableCell cell3 = addRow.Cells.AddTableCell();
                    Block cellBlock3 = cell3.Blocks.AddBlock();
                    cellBlock3.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Center;
                    cellBlock3.TextProperties.FontSize = 11;
                    cellBlock3.InsertText($"{option.Qty}");
                    cell3.PreferredWidth = 100;

                    foreach (var attr in option.BuildAttributeItems)
                    {
                        if (!string.IsNullOrWhiteSpace(attr.AttrGroupAssignment?.AttributeItem?.Description) && attr.AttrGroupAssignment?.AttributeItem?.Description != "Not Applicable")
                        {
                            Block attributeBlock = cell2.Blocks.AddBlock();
                            attributeBlock.LeftIndent = 10;
                            attributeBlock.TextProperties.FontSize = 11;
                            attributeBlock.InsertText(new FontFamily("Helvetica"), FontStyles.Italic, FontWeights.Normal, $"{attr.AttrGroupAssignment.AttributeItem.Description}");
                        }

                    }
                    if (!string.IsNullOrWhiteSpace(option.CustomerDesc))
                    {
                        var descriptions = option.CustomerDesc.Split("\n");
                        foreach (var desc in descriptions)
                        {
                            Block notesBlock = cell2.Blocks.AddBlock();
                            notesBlock.LeftIndent = 10;
                            notesBlock.TextProperties.FontSize = 11;
                            notesBlock.InsertText(new FontFamily("Helvetica"), FontStyles.Italic, FontWeights.Normal, $"{desc}");
                        }
                    }
                }
                editor.InsertTable(table);
            }
            catch (Exception ex)
            {
                var debug = ex.Message;
            }
        }

        private static void InsertOptionsTable(RadFixedDocumentEditor editor, List<TbBuiltOptionDto> tbBuiltOptionDtos, List<AvailablePlanOptionDto> options)
        {
            try
            {
                Table table = CreateTableWithHeaderRow();

                foreach (var option in options)
                {
                    TableRow addRow = table.Rows.AddTableRow();

                    TableCell cell1 = addRow.Cells.AddTableCell();
                    Block cellBlock1 = cell1.Blocks.AddBlock();
                    cellBlock1.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Center;
                    cellBlock1.TextProperties.FontSize = 11;
                    cellBlock1.InsertText($"{option.OptionCode}");
                    cell1.PreferredWidth = 120;

                    TableCell cell2 = addRow.Cells.AddTableCell();
                    Block cellBlock2 = cell2.Blocks.AddBlock();
                    cellBlock2.TextProperties.FontSize = 11;
                    cellBlock2.InsertText($"{option.ModifiedOptionDesc.ToUpper()}");
                    cell2.PreferredWidth = 450;

                    TableCell cell3 = addRow.Cells.AddTableCell();
                    Block cellBlock3 = cell3.Blocks.AddBlock();
                    cellBlock3.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Center;
                    cellBlock3.TextProperties.FontSize = 11;
                    cellBlock3.InsertText($"{option.UnitQty}");
                    cell3.PreferredWidth = 100;

                    if (tbBuiltOptionDtos.Any(x => x.OptionCode == option.OptionCode))
                    {
                        RgbColor yellow = new RgbColor(255, 255, 0);
                        cell1.Background = yellow;
                        cell2.Background = yellow;
                        cell3.Background = yellow;
                    }
                }
                editor.InsertTable(table);
            }
            catch (Exception ex)
            {
                var debug = ex.Message;
            }
        }

        private static Table CreateTableWithHeaderRow()
        {
            Table table = new Table();
            Border border = new Border(2, new RgbColor(0, 0, 0));
            table.DefaultCellProperties.Padding = new Thickness(10, 10, 10, 10);
            table.DefaultCellProperties.Borders = new TableCellBorders(border, border, border, border);
            table.LayoutType = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.TableLayoutType.FixedWidth;

            TableRow firstRow = table.Rows.AddTableRow();
            TableCell firstRowCell1 = firstRow.Cells.AddTableCell();
            Block block1 = firstRowCell1.Blocks.AddBlock();
            block1.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Center;
            block1.TextProperties.FontSize = 11;
            block1.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Code");
            firstRowCell1.PreferredWidth = 120;

            TableCell firstRowCell2 = firstRow.Cells.AddTableCell();
            Block block2 = firstRowCell2.Blocks.AddBlock();
            block2.TextProperties.FontSize = 11;
            block2.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Description");
            firstRowCell2.PreferredWidth = 400;

            TableCell firstRowCell3 = firstRow.Cells.AddTableCell();
            Block block3 = firstRowCell3.Blocks.AddBlock();
            block3.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Center;
            block3.TextProperties.FontSize = 11;
            block3.InsertText(new FontFamily("Helvetica"), FontStyles.Normal, FontWeights.Bold, "Quantity");
            firstRowCell3.PreferredWidth = 100;
            return table;
        }

        public static void DrawFooterToDocument(RadFixedDocument document, SelectedApprovedOrAllOptions selectedApprovedOrAll)
        {
            int numberOfPages = document.Pages.Count;
            for (int pageIndex = 0; pageIndex < numberOfPages; pageIndex++)
            {
                int pageNumber = pageIndex + 1;
                RadFixedPage currentPage = document.Pages[pageIndex];
                DrawFooterToPage(currentPage, pageNumber, numberOfPages, selectedApprovedOrAll);
            }
        }
        public static void DrawHeaderToDocument(RadFixedDocument document, List<TbBuiltOptionDto> options, JobDto job, List<BuyerDto> buyers)
        {
            int numberOfPages = document.Pages.Count;
            for (int pageIndex = 0; pageIndex < numberOfPages; pageIndex++)
            {
                int pageNumber = pageIndex + 1;
                RadFixedPage currentPage = document.Pages[pageIndex];
                DrawHeaderToPage(currentPage, pageNumber, numberOfPages, options, job, buyers );
            }
        }

        private static void DrawHeaderToPage(RadFixedPage page, int pageNumber, int numberOfPages, List<TbBuiltOptionDto> options, JobDto job, List<BuyerDto> buyers)
        {
            FixedContentEditor pageEditor = new FixedContentEditor(page);

            pageEditor.Position.Translate(defaultLeftIndent, 50);

            var rootpath = Directory.GetCurrentDirectory();
            using (FileStream fs = new FileStream(
                System.IO.Path.Combine(rootpath, @"wwwroot/css/assets/images/vmlogo.png"),
                FileMode.Open,
                FileAccess.Read))
            {
                pageEditor.DrawImage(fs);
            }

            pageEditor.Position.Translate(defaultLeftIndent, 120);

            DrawReleaseHeading(pageEditor, options, job, buyers);
        }

        private static void DrawFooterToPage(RadFixedPage page, int pageNumber, int numberOfPages, SelectedApprovedOrAllOptions selectedApprovedOrAll)
        {
            FixedContentEditor pageEditor = new FixedContentEditor(page);

            Block footer = new Block();
            footer.HorizontalAlignment = Telerik.Windows.Documents.Fixed.Model.Editing.Flow.HorizontalAlignment.Right;
            footer.TextProperties.FontSize = 9;
            footer.InsertText(String.Format($"{EnumExtension.GetDescriptionFromEnum(selectedApprovedOrAll)} Options - Generated {DateTime.Now.ToString("MM/dd/yyyy")} - Page {pageNumber}"));
            footer.Measure();

            //double footerOffsetX = (page.Size.Width / 2) - (footer.DesiredSize.Width / 2);
            double footerOffsetY = page.Size.Height - 30 - footer.DesiredSize.Height;
            pageEditor.Position.Translate(500, footerOffsetY);
            pageEditor.DrawBlock(footer);
        }
    }
}
