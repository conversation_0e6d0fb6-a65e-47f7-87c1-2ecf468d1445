﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Admin
{
    public int AdminId { get; set; }

    public int? AdminRoleId { get; set; }

    public bool IsActive { get; set; }

    public Guid AzureId { get; set; }

    public string LastName { get; set; } = null!;

    public string FirstName { get; set; } = null!;

    public string Email { get; set; } = null!;

    public virtual ICollection<ContractAdmin> ContractAdmins { get; set; } = new List<ContractAdmin>();

    public virtual ICollection<Envelope> EnvelopeSenders { get; set; } = new List<Envelope>();

    public virtual ICollection<Envelope> EnvelopeVoiders { get; set; } = new List<Envelope>();
}
