﻿
using AutoMapper;
using ERP.API.Utilities;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NLog;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class StartPackageDocumentController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private readonly Email _email;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        public StartPackageDocumentController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _email = new Email(configuration);
        }

        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> ListDocumentsAsync(string jobNumber)
        {
            var documentsDto = new List<JobAttachmentDto>();
            try
            {
                var documents = await _context.JobAttachments.Include("Doctype").Where(x => x.JobNumber == jobNumber && x.IsActive == true).ToListAsync();
                documentsDto = _mapper.Map<List<JobAttachmentDto>>(documents);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobAttachmentDto>>() { IsSuccess = false, Message = "failed to alist job documents", Value = null });
            }
            return Ok(new ResponseModel<List<JobAttachmentDto>>() { Value = documentsDto, IsSuccess = true });
        }
        
        [HttpPost]
        public async Task<IActionResult> UploadDocument([FromForm]DocumentUploadModel model)
        {

            try
            {
                var getType = _context.Doctypes.SingleOrDefault(x => x.DoctypeId == model.DocTypeId)?.Doctype1 ?? "";
                var response = await BlobStorage.UploadFileToBlobAsync(model.files, model.FolderName.ToLower(), model.FileName, getType);
                //containers ("folders") named by job number in case job document or subdivision number is case of start package item document
                var nameContents = model.FileName.Split('-');
                int.TryParse(nameContents[1], out int packageItemId);

                var packageItem = await _context.SchePackageItems.SingleOrDefaultAsync(x => x.PackageItemId == (int)packageItemId);
                if (packageItem != null)
                {
                    packageItem.WebLink = response.FileName;
                    _context.SchePackageItems.Update(packageItem);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<bool>() { IsSuccess = false, Message = "failed upload job document", Value = false });
            }
            return Ok(new ResponseModel<bool>() { Value = true, IsSuccess = true });
        }

        
        
        [HttpPut]
        public async Task<IActionResult> DeleteDocumentAsync([FromBody] SchePackageItemDto item)
        {
            
            try
            {
                var packageItem = await _context.SchePackageItems.SingleOrDefaultAsync(x => x.PackageItemId == (int)item.PackageItemId);
                if (packageItem != null)
                {

                    var fileLink = packageItem.WebLink;
                    var nameContents = fileLink.Split('-');
                    if (nameContents.Length >= 2)
                    {
                        int.TryParse(nameContents[0], out int packageId);
                        var packageSubdiv = _context.ScheStartPackages.Find(packageId).SubdivisionId;//Todo: don't name the folder this way
                        await BlobStorage.MoveFileToInactiveFolderBlobAsync(packageSubdiv.ToString(), fileLink);

                    }
                    packageItem.WebLink = "";
                    _context.SchePackageItems.Update(packageItem);
                    await _context.SaveChangesAsync();

                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<SchePackageItemDto>() { IsSuccess = false, Message = "failed to delete document", Value = null });
            }
            return Ok(new ResponseModel<SchePackageItemDto>() { Value = item, IsSuccess = true, Message = "Package Item document deleted successfully" });
        }

        [HttpPost]
        public async Task<IActionResult> DownloadDocument([FromBody] DocumentUploadModel documentDetails)
        {
            try
            {
                var folderName = documentDetails.FolderName;
                var filename = documentDetails.FileName;
                var docDownload = BlobStorage.DownloadFileBytes(filename, folderName);
                return Ok(new ResponseModel<byte[]>() { Value = docDownload, IsSuccess = true });//returns the byte array, won't work for large files
                                                                                                 //  return File(_service.CreateCostsExport(costs), contentType, fileName);
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<byte[]>() { IsSuccess = false, Message = "failed to download document", Value = null });
            }
            return BadRequest();
        }
    }
}
