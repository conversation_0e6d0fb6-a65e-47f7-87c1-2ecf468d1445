﻿using Azure.Identity;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Sas;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using System.Net;

namespace ERP.API.Utilities
{
    public static class BlobStorage
    {

        static readonly string connectionString = ConfigurationHelper.Configuration.GetConnectionString("BlobStorageConnection");
        static readonly string clientId = ConfigurationHelper.Configuration.GetSection("ManagedIdentityId").Value;
        // private static readonly string apiKey = ConfigurationHelper.Configuration["SendGridAPIKey"];
        /// <summary>
        /// Upload file to blob
        /// </summary>
        /// <returns>A Task object.</returns>
        public static async Task<(string Path, string FileName)> UploadFileToBlobAsync(IFormFile fileToUpload, string folderName, string fileName, string type)
        {

            //TODO: foldername needs to avoid special characters and stuff
            BlobContainerClient container = new BlobContainerClient(connectionString, folderName.ToLower());
            await container.CreateIfNotExistsAsync();
            string originalFileName = fileName;
           
            //if filename exists, copy with new name
            bool exists = await container.GetBlobClient(fileName).ExistsAsync();
            int i = 0;
            var extension = System.IO.Path.GetExtension(originalFileName);
            var originalFileNameNoExtension = originalFileName.Remove(originalFileName.Length - extension.Length);
            while (exists)
            {
                //rename file                
                fileName = $"{originalFileNameNoExtension}({++i}){extension}";
                exists = await container.GetBlobClient(fileName).ExistsAsync();
            }
            string contentType;
            new FileExtensionContentTypeProvider().TryGetContentType(fileName, out contentType);
            var blobHeader = new BlobHttpHeaders
            {
                ContentType = contentType ?? "application/octet-stream",
                ContentDisposition = string.Format("attachment;filename=\"{0}\"", fileName)

            };
            var initialMetadata = new Dictionary<string, string> { { "Category", type.Trim() } };
            var blobOptions = new BlobUploadOptions()
            {
                Metadata = initialMetadata, //TODO: add other information to metadata, such as customer name, is approved, etc

            };
            BlobClient blobClient = container.GetBlobClient(fileName);
            var stream = fileToUpload.OpenReadStream();
            await blobClient.UploadAsync(stream, blobOptions);
            await blobClient.SetHttpHeadersAsync(blobHeader);
            var path = blobClient.Uri.AbsoluteUri;
            return (path, fileName);
        }

        /// <summary>
        /// Upload file to blob
        /// </summary>
        /// <returns>A Task object.</returns>
        public static async Task<(string Path, string FileName)> UploadFileToBlobAsync(byte[] fileData, string folderName, string fileName)
        {
            // Sanitize folderName to avoid special characters
            folderName = folderName.Replace(" ", "_").ToLower();

            // Ensure the blob container name is valid (lowercase, alphanumeric, and dashes)
            BlobContainerClient container = new BlobContainerClient(connectionString, folderName);
            await container.CreateIfNotExistsAsync();

            string originalFileName = fileName;
            //if filename exists, copy with new name
            bool exists = await container.GetBlobClient(fileName).ExistsAsync();
            int i = 0;
            var extension = System.IO.Path.GetExtension(originalFileName);
            var originalFileNameNoExtension = originalFileName.Remove(originalFileName.Length - extension.Length);
            while (exists)
            {
                //rename file                
                fileName = $"{originalFileNameNoExtension}_V{++i}{extension}";
                exists = await container.GetBlobClient(fileName).ExistsAsync();
            }

            BlobClient blobClient = container.GetBlobClient(fileName);

            // Determine the content type
            string contentType;
            new FileExtensionContentTypeProvider().TryGetContentType(fileName, out contentType);
            contentType ??= "application/octet-stream"; // Default to binary stream if type not found

            var blobHeader = new BlobHttpHeaders
            {
                ContentType = contentType
            };

            try
            {
                using var stream = new MemoryStream(fileData);
                await blobClient.UploadAsync(stream, overwrite: false); // Do not overwrite existing files
                await blobClient.SetHttpHeadersAsync(blobHeader);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\"Error uploading the file to Blob Storage :\n {ex.ToString()}");
                return (string.Empty, string.Empty);
            }

            var path = blobClient.Uri.AbsoluteUri;
            return (path, fileName);
        }


        /// <summary>
        /// Check if file exists
        /// </summary>
        /// <returns>A Task object.</returns>
        public static async Task<bool> CheckExistsAsync(string folderName, string fileName)
        {
            BlobContainerClient container = new BlobContainerClient(connectionString, folderName);
            bool exists = false;
            if (await container.ExistsAsync())
            {
                exists = await container.GetBlobClient(fileName).ExistsAsync();
            }

            return exists;

        }

        /// <summary>
        /// Delete file from blob
        /// </summary>
        /// <returns>A Task object.</returns>
        public static async Task DeleteFileFromBlobAsync(string folderName, string fileName)
        {
            //will throw exception if bad container name
            BlobContainerClient container = new BlobContainerClient(connectionString, folderName);
            if (container.Exists())
            {
                BlobClient blobClient = container.GetBlobClient(fileName);
                if (blobClient.Exists())
                {
                    await blobClient.DeleteAsync();
                }
            }
        }

        /// <summary>
        /// Move file to folder for inactivated (copies the file then deletes the original)
        /// </summary>
        /// <param name="folderName"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static async Task MoveFileToInactiveFolderBlobAsync(string folderName, string fileName)
        {
            //will throw exception if bad container name
            BlobContainerClient container = new BlobContainerClient(connectionString, folderName);
            if (container.Exists())
            {
                BlobClient sourceBlob = container.GetBlobClient(fileName);
                if (sourceBlob.Exists())
                {
                    // Lease the source blob for the copy operation 
                    // to prevent another client from modifying it.
                    BlobLeaseClient lease = sourceBlob.GetBlobLeaseClient();
                    try
                    {
                        // Specifying -1 for the lease interval creates an infinite lease.
                        await lease.AcquireAsync(TimeSpan.FromSeconds(30));

                        // Get a BlobClient representing the destination blob with a unique name.
                        BlobContainerClient destContainer = new BlobContainerClient(connectionString, "inactive-docs");
                        await destContainer.CreateIfNotExistsAsync();
                        BlobClient destBlob =
                            destContainer.GetBlobClient(folderName + "-" + sourceBlob.Name);  //copy file to inactive folder
                        // Start the copy operation. This will create new version if there already was a file with the same name (versioning has to be set in storage account)
                        await destBlob.StartCopyFromUriAsync(sourceBlob.Uri);

                        // Update the source blob's properties.
                        BlobProperties sourceProperties = await sourceBlob.GetPropertiesAsync();
                        if (sourceProperties.LeaseState == LeaseState.Leased)
                        {
                            // Break the lease on the source blob.
                            lease.Release();
                        }
                        //now delete the original
                        await sourceBlob.DeleteAsync();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.ToString());
                    }
                }
            }
        }


        /// <summary>
        /// Get file from blob
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="folderName"></param>
        /// <returns></returns>
        public static async Task<BlobFileDetails> GetFileFromBlobAsync(string fileName, string folderName, string downloadName)
        {
            BlobFileDetails file = null;

            try
            {
                // Initialize the BlobContainerClient with your connection string and folder name (container name)
                BlobContainerClient container = new BlobContainerClient(connectionString, folderName);

                // Get the reference for the specific blob in the container
                var blobClient = container.GetBlobClient(fileName);

                // Download the blob into memory stream
                using (MemoryStream stream = new MemoryStream())
                {
                    // Try downloading the blob into memory
                    await blobClient.DownloadToAsync(stream);

                    // Convert the memory stream to byte array
                    byte[] imageArray = stream.ToArray();

                    // Get blob's content type and last modified date
                    var type = await blobClient.GetPropertiesAsync();
                    var fileType = type.Value.ContentType;
                    var lastModifiedDate = type.Value.LastModified;

                    // Create the FileContentResult object with content type from the blob
                    file = new BlobFileDetails()
                    {
                        Content = imageArray,
                        ContentType = fileType,
                        DownloadName = downloadName,
                        SasUri = string.Empty
                    };
                }
            }
            catch (Azure.RequestFailedException ex)
            {
                // Handle blob not found or other errors
                Console.WriteLine($"Error: {ex.Message}");
            }

            // Return the file content result or null if something went wrong
            return file;
        }

        public static async Task<BlobFileDetails> GenerateSasDownloadUrl(string containerName, string blobName)
        {
            string encodedBlobName = string.Empty;

            // Manually replace characters that need special handling (like parentheses)


            // Get the container client and blob client
            var containerClient = new BlobContainerClient(connectionString, containerName.ToLower());
            var blobClient = containerClient.GetBlobClient(blobName);

            try
            {
                // Step 1: Get the properties of the blob (e.g., content type)
                BlobProperties properties = await blobClient.GetPropertiesAsync();
                string contentType = properties.ContentType;

                // Set the SAS options (permissions and expiration)
                var sasBuilder = new BlobSasBuilder
                {
                    BlobContainerName = containerName.ToLower(),
                    BlobName = blobName,
                    ExpiresOn = DateTimeOffset.UtcNow.AddMinutes(30)  // Set expiration time
                };

                // Set the permissions to allow reading the file
                sasBuilder.SetPermissions(BlobSasPermissions.Read);

                // Generate the SAS token
                Uri sasUri = blobClient.GenerateSasUri(sasBuilder);

                var blobFileDetails = new BlobFileDetails
                {
                    SasUri = sasUri.AbsoluteUri,
                    ContentType = contentType
                };

                // Return the full SAS URL
                return await Task.FromResult(blobFileDetails);
            }
            catch (Exception ex)
            {
                var blobFileDetails = new BlobFileDetails
                {
                    SasUri = string.Empty,
                    ContentType = string.Empty
                };
                return await Task.FromResult(blobFileDetails);

            }
        }

        /// <summary>
        /// Download file from blob
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="folderName"></param>
        /// <returns></returns>
        public static FileContentResult GetFileFromBlob(string fileName, string folderName)
        {
           // DocumentViewModel document = new DocumentViewModel();
            FileContentResult file = null;

            BlobContainerClient container = new BlobContainerClient(connectionString, folderName);

            var blobClient = container.GetBlobClient(fileName);

            if (blobClient.Exists())//check if file exists
            {
                byte[] imageArray;
                using (MemoryStream stream = new MemoryStream())
                {
                    //await blockBlob.DownloadToStreamAsync(stream);
                    blobClient.DownloadTo(stream);
                    imageArray = stream.ToArray();
                }
                // file = new FileContentResult(imageArray, blockBlob.Properties.ContentType);
                var type = blobClient.GetProperties();
                var fileType = type.GetType().ToString();
                var date = type.Value.LastModified;
                //try type.ContentType or type.Value.ContentType
                file = new FileContentResult(imageArray, fileType);
                //document.Document = (IFormFile)file;
                //document.File = file;
                //document.UpdateDate = date.DateTime;
                //document.FileName = fileName;
                //document.FileType = fileType;
            }

            return file;

        }
        /// <summary>
        /// Download file from blob
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="folderName"></param>
        /// <returns></returns>
        public static byte[] DownloadFileBytesTEST(string fileName, string folderName)
        {
            // DocumentViewModel document = new DocumentViewModel();
            FileContentResult file = null;
            byte[] fileBytesArray = null;
            try
            {
                // Specify the Client ID if using user-assigned managed identities
                var credentialOptions = new DefaultAzureCredentialOptions
                {
                    ManagedIdentityClientId = clientId,
                   // ExcludeEnvironmentCredential = true, did not work
                };
                var credential = new DefaultAzureCredential(credentialOptions);

                var blobServiceClient1 = new BlobServiceClient(new Uri("https://vanmetreerp.blob.core.windows.net"), credential);
                BlobContainerClient containerClient1 = blobServiceClient1.GetBlobContainerClient(folderName);
                BlobClient blobClient1 = containerClient1.GetBlobClient(fileName);

                if (blobClient1.Exists())
                {
                    var downloadedBlob = blobClient1.Download();
                    string blobContents = downloadedBlob.Value.Content.ToString();


                    //using (MemoryStream stream = new MemoryStream())
                    //{
                    //    //await blockBlob.DownloadToStreamAsync(stream);
                    //    blobClient.DownloadTo(stream);
                    //    fileBytesArray = stream.ToArray();
                    //}
                    //return fileBytesArray;
                }


            }
            catch (Exception ex)
            {
                var message = ex.Message;
            }


            return fileBytesArray;

        }
        /// <summary>
        /// Download file from blob
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="folderName"></param>
        /// <returns></returns>
        public static byte[] DownloadFileBytes(string fileName, string folderName)
        {
            // DocumentViewModel document = new DocumentViewModel();
            FileContentResult file = null;
            byte[] fileBytesArray = null;
            BlobContainerClient container = new BlobContainerClient(connectionString, folderName);

            var blobClient = container.GetBlobClient(fileName);
            try 
            {
                if (blobClient.Exists())//check if file exists
                {

                    using (MemoryStream stream = new MemoryStream())
                    {
                        //await blockBlob.DownloadToStreamAsync(stream);
                        blobClient.DownloadTo(stream);
                        fileBytesArray = stream.ToArray();
                    }
                    // file = new FileContentResult(imageArray, blockBlob.Properties.ContentType);
                    //var type = blobClient.GetProperties();
                    //var fileType = type.GetType().ToString();
                    //var date = type.Value.LastModified;
                    //try type.ContentType or type.Value.ContentType
                    // file = new FileContentResult(fileBytesArray, fileType);
                    return fileBytesArray;
                }
            }
            catch (Exception ex)
            {
                var debug = ex.Message;
            }

            return fileBytesArray;

        }
        /// <summary>
        /// "Renames" blob by copying then deleting original
        /// </summary>
        /// <param name="folderName"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static async Task<(string Path, string FileName)> RenameBlobAsync(string folderName, string sourceFileName, string destFileName)
        {
            string path = "";            
            //will throw exception if bad container name
            BlobContainerClient container = new BlobContainerClient(connectionString, folderName);
            if (container.Exists())
            {
                BlobClient sourceBlob = container.GetBlobClient(sourceFileName);
                if (sourceBlob.Exists())
                {
                    // Lease the source blob for the copy operation 
                    // to prevent another client from modifying it.
                    BlobLeaseClient lease = sourceBlob.GetBlobLeaseClient();
                    // Specifying -1 for the lease interval creates an infinite lease.
                    await lease.AcquireAsync(TimeSpan.FromSeconds(30));

                    // Get a BlobClient representing the destination blob with a unique name.
                    BlobContainerClient destContainer = new BlobContainerClient(connectionString, folderName);
                    await destContainer.CreateIfNotExistsAsync();

                    //if filename exists, copy with new name
                    string originalFileName = destFileName;//TODO: validate they've typed a valid filename with a valid extension
                    bool exists = await destContainer.GetBlobClient(destFileName).ExistsAsync();
                    int i = 0;
                    var extension = System.IO.Path.GetExtension(originalFileName);
                    var originalFileNameNoExtension = originalFileName.Remove(originalFileName.Length - extension.Length);
                    while (exists)
                    {
                        //rename file                
                        destFileName = $"{originalFileNameNoExtension}({++i}){extension}";
                        exists = await container.GetBlobClient(destFileName).ExistsAsync();
                    }


                    BlobClient destBlob =
                        destContainer.GetBlobClient(destFileName);
                    // Start the copy operation. This will create new version if there already was a file with the same name (versioning has to be set in storage account)
                    await destBlob.StartCopyFromUriAsync(sourceBlob.Uri);
                    //TODO: if the rename is changing the type or subtype, update the metadata

                    // Update the source blob's properties.
                    BlobProperties sourceProperties = await sourceBlob.GetPropertiesAsync();
                    if (sourceProperties.LeaseState == LeaseState.Leased)
                    {
                        // Break the lease on the source blob.
                        lease.Release();
                    }
                    //now delete the original
                    await sourceBlob.DeleteAsync();
                    path = destBlob.Uri.AbsoluteUri;
                    return (path, destFileName);
                }
            }
            return (path, destFileName);
        }




        /// <summary>
        /// Mark metadata for blob file to inactive
        /// </summary>
        /// <param name="folderName"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static async Task MarkBlobInactiveAsync(string folderName, string fileName)
        {

            BlobContainerClient container = new BlobContainerClient(connectionString, folderName);
            await container.CreateIfNotExistsAsync();

            BlobClient blobClient = container.GetBlobClient(fileName);
            string contentType;
            new FileExtensionContentTypeProvider().TryGetContentType(fileName, out contentType);
            var blobHeader = new BlobHttpHeaders
            {
                ContentType = contentType ?? "application/octet-stream",
            };
            var setMetadata = new Dictionary<string, string> { { "IsActive", "Inactive" } };

            await blobClient.SetMetadataAsync(setMetadata);

        }

    }
}
