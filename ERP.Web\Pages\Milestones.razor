﻿@page "/milestones"

@inject ScheduleService ScheduleService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector")]
@using ERP.Data.Models.Dto
@using ERP.Data.Models
@using Telerik.DataSource.Extensions
@using System.Collections.ObjectModel

<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }
    /*.mytreeclass .k-drag-cell {
            display:none;
        }*/
/*    .mytreeclass .k-table-th.k-header.k-drag-cell{
        visibility: collapse;
    }
    .mytreeclass .k-drag-cell.k-touch-action-none{
        visibility: collapse;

    }*/
    .mytreeclass .k-drag-col{
        visibility: collapse;
    }
    .mytreeclass .k-header {
        min-width: 200px;
    }

    .mytreeclass  {
       overflow-x: auto;
    }
    .k-drag-clue {
        color: white;
    }

    .k-icon.k-icon-with-modifier {
        display: none;
    }

    .SubdivisionTable .k-hierarchy-cell {
        display: none;
    }
    
    .k-splitbar {
        width: 15px;
        color: black;
        padding-left:5px;
        padding-right:5px;
    }

    .pane {
        padding: 8px;
        overflow-x: auto;
    }

    .k-splitter .k-pane {
        overflow-x: auto !important;
    }

    .k-splitter-horizontal{
        overflow-x: auto !important;
    }
    .page-wrapper .page-content{     
       /* padding-right: 100px !important;
        margin-right: 100px !important;*/ 
/*        width: 95% !important;*/
    }
    .page-wrapper{
       /* padding-right: 100px !important;
        margin-right: 100px !important;
            width: 80% !important;*/
    }
    .k-table-td{
        padding: 4px !important;
        font-size: 11px;
    }

    .k-table-td { /*these settings are to prevent the 0 width columns from affecting the column height*/
        padding: 4px !important;
        font-size: 11px;
        height: 30px !important;
        white-space: nowrap !important; 
        overflow: visible;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>

   <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Schedule Milestones</h7>
                </div>
            </div>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<TelerikTooltip TargetSelector=".tooltip-target" />
    <div class="row">
    <TelerikGrid Data=@AllMilestones
                 ConfirmDelete="true"
                 OnUpdate="@UpdateMilestone"
                 OnEdit="@EditMilestone"
                 OnDelete="@DeleteMilestone"
                 OnCreate="@CreateMilestone"
                 EditMode="@GridEditMode.Inline"
                 ScrollMode="@GridScrollMode.Virtual"
                 FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                 Height="1000px" RowHeight="60" PageSize="20"
                 Sortable="true"
                 Resizable="true"
                 Reorderable="true"
                 RowDraggable="true"
                 OnRowDrop="@((GridRowDropEventArgs<MilestoneDto> args) => OnRowDropHandler(args))">
        <GridColumns>
            <GridColumn Field="MilestoneName" Title="Milestone Name" Editable="true" /> 
            <GridColumn Field="Seq" Title="Milestone Sequence" Editable="false" />
            <GridCommandColumn>
                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true" Class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-primary">Update</GridCommandButton>
                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
            </GridCommandColumn>
        </GridColumns>

        <GridToolBarTemplate>            
            <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>
            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add</GridCommandButton>
        </GridToolBarTemplate>
    </TelerikGrid>

    </div>

@code {

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    public List<MilestoneDto>? AllMilestones { get; set; }
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        AllMilestones = (await ScheduleService.GetMilestonesAsync()).Value;
    }

    void EditMilestone(GridCommandEventArgs args)
    {
        MilestoneDto item = (MilestoneDto)args.Item;
    }

    async Task UpdateMilestone(GridCommandEventArgs args)
    {
        MilestoneDto item = (MilestoneDto)args.Item;
        var updateResponse = await ScheduleService.UpdateMilestoneAsync(item);
        AllMilestones = (await ScheduleService.GetMilestonesAsync()).Value;
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
    }

    async Task DeleteMilestone(GridCommandEventArgs args)
    {
        MilestoneDto item = (MilestoneDto)args.Item;
        var deleteResponse = await ScheduleService.DeleteMilestoneAsync(item);
        AllMilestones = (await ScheduleService.GetMilestonesAsync()).Value;
        ShowSuccessOrErrorNotification(deleteResponse.Message, deleteResponse.IsSuccess);
    }

    async Task CreateMilestone(GridCommandEventArgs args)
    {
        MilestoneDto item = (MilestoneDto)args.Item;
        var addResponse = await ScheduleService.AddMilestoneAsync(item);
        AllMilestones = (await ScheduleService.GetMilestonesAsync()).Value;
        ShowSuccessOrErrorNotification(addResponse.Message, addResponse.IsSuccess);
    }

    private async Task OnRowDropHandler(GridRowDropEventArgs<MilestoneDto> args)
    {
        var initialItemIndex = AllMilestones.IndexOf(args.Item);
        AllMilestones.Remove(args.Item);

        var destinationItemIndex = AllMilestones.IndexOf(args.DestinationItem);

        if (args.DropPosition == GridRowDropPosition.After)
        {
            destinationItemIndex++;
        }

        AllMilestones.Insert(destinationItemIndex, args.Item);
        foreach(var item in AllMilestones)
        {
            item.Seq = AllMilestones.IndexOf(item) + 1;
        }        
        var updateResponse = await ScheduleService.UpdateMilestonesSeqAsync(AllMilestones);
        ShowSuccessOrErrorNotification(updateResponse.Message, updateResponse.IsSuccess);
    }

    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
