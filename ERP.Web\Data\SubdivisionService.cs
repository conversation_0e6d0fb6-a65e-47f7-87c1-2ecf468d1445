﻿using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using NLog;
using System.Diagnostics;
using Telerik.DataSource.Extensions;
using Microsoft.Identity.Abstractions;
using Microsoft.Graph;
using Telerik.Blazor.Components;
using Microsoft.Graph.Models;

namespace ERP.Web.Data
{
    public class SubdivisionService
    {
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly GraphServiceClient _graphServiceClient;
        private readonly IConfiguration _configuration;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public SubdivisionService(HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, GraphServiceClient graphServiceClient, IConfiguration configuration, AuthenticationStateProvider authenticationStateProvider, ScheduleService scheduleService)
        {
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _graphServiceClient = graphServiceClient;
            _configuration = configuration;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<ResponseModel> DoNothingAsync()
        {
            //This method is being called on the dashboard, basically to get the token so it doesn't do that login later on
            var responseModel = new ResponseModel();
           
            try
            {

                //Effectively just getting the token so it doesn't happen on other pages
                var response1 = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/subdivision/donothing");
                var responseString = await response1.Content.ReadAsStringAsync();
                responseModel = JsonConvert.DeserializeObject<ResponseModel>(responseString);

               // await TestStuffAsync();
               
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }

        private async Task<string> GetBCTokenAsync()
        {
            //BC Sandbox
            var clientId = "3c2176ad-0ebb-4bba-8651-7660adaf1c98";
            var clientSecret = "****************************************";
            var tenantId = "bffefab0-2b5f-4421-b5c1-9e402001e131";
            var token_url = "https://login.microsoftonline.com/" + tenantId + "/oauth2/v2.0/token";

            var client = new HttpClient();

            var content = new StringContent(
                "grant_type=client_credentials" +
                "&scope=https://api.businesscentral.dynamics.com/.default" +
                "&client_id=" + System.Web.HttpUtility.UrlEncode(clientId) +
                "&client_secret=" + System.Web.HttpUtility.UrlEncode(clientSecret));

            content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");

            var response = await client.PostAsync(token_url, content);
            var tokenresponse = await response.Content.ReadAsStringAsync();
            var access_token_Object = JsonConvert.DeserializeObject<dynamic>(tokenresponse);
            var access_token = access_token_Object.access_token;

            return access_token;
        }
        public async Task<ResponseModel> TestStuffAsync()
        {
            var responseModel = new ResponseModel();

            try
            {
                var requestUrl = "https://api.businesscentral.dynamics.com/v2.0/bffefab0-2b5f-4421-b5c1-9e402001e131/copypilotplay/api/v2.0/companies(617d495f-c32f-ee11-bdfb-6045bdc8a7d3)";
                var response1 = await _downstreamAPI.GetForAppAsync<BCResponsePlanningLine>("BusinessCentral", options => {
                    options.BaseUrl = requestUrl;
                    options.RelativePath = "/purchaseOrders?$expand=purchaseOrderLines";
                });

                var test = response1;

                // var client = new HttpClient();

                // var content = new StringContent(
                //     "grant_type=client_credentials" +
                //     "&scope=https://graph.microsoft.com/.default" +
                //     "&client_id=" + System.Web.HttpUtility.UrlEncode(clientId) +
                //     "&client_secret=" + System.Web.HttpUtility.UrlEncode(clientSecret));

                // content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");

                // var response = await client.PostAsync(token_url, content);
                // var tokenresponse = await response.Content.ReadAsStringAsync();
                // var access_token_Object = JsonConvert.DeserializeObject<dynamic>(tokenresponse);
                // var access_token = access_token_Object.access_token;//Appears successful

                // var client2 = new HttpClient();
                // client2.DefaultRequestHeaders.Add("Authorization", $"Bearer {access_token}");

                // var RequestUri = new Uri("https://graph.microsoft.com/v1.0/groups/a6d98200-dd16-4d15-bb61-7857ae25431c/members?$count=true");
                // var res = await client2.GetAsync(RequestUri);
                // var response2 = await res.Content.ReadAsStringAsync();
                // var obj = JsonConvert.DeserializeObject<dynamic>(response2);
                // var test = obj;

                // credential = new DefaultAzureCredential(credentialOptions);
                // var secretClient = new SecretClient(
                //     new Uri("https://erpkeyvault.vault.azure.net/"),
                //     credential);
                // KeyVaultSecret secret = secretClient.GetSecret("ErnieTest");
                // string secretValue = secret.Value;
                // responseModel.Message = $"secret {secretValue}";//Yay! it worked! -- in dev, user needs to enter azure credentials, tools - > azure credentials 

                //var clientId = _configuration.GetSection("AzureAd:ClientId").Value;
                //var clientSecret = _configuration.GetSection("AzureAd:ClientSecret").Value;
                //var tenantId = _configuration.GetSection("AzureAd:TenantId").Value;




                //   ////try graph api for users list
                //   //var user = await _graphServiceClient.Me.GetAsync();

                //   ////var result4356 = await _graphServiceClient.Users["fdbce925-3a9e-43a9-82fc-dc2c901970ab"].GetAsync();
                //   ////group id is huddle users group to test

                //   //var groups = await _graphServiceClient.Groups["2439881b-0229-490c-aa05-6613de81fa4a"].Members.GetAsync(requestConfiguration =>
                //   //{
                //   //    requestConfiguration.QueryParameters.Select = new string[] { "DisplayName", "id", "mail", "userPrincipalName" };
                //   //});//has right count, but missing all data except id
                //   //var groups2 = await _graphServiceClient.Groups["2439881b-0229-490c-aa05-6613de81fa4a"].Members.GraphUser.GetAsync();//has right count, but missing all data except id
                //   ////var result = await _graphServiceClient.Groups["2439881b-0229-490c-aa05-6613de81fa4a"].Members.GetAsync((requestConfiguration) =>
                //   ////{
                //   ////    requestConfiguration.QueryParameters.Expand = new string[] { "members" };
                //   ////});
                //   //var result = await _graphServiceClient.Groups["2439881b-0229-490c-aa05-6613de81fa4a"].Members.GraphUser.GetAsync((requestConfiguration) =>
                //   //{
                //   //    requestConfiguration.QueryParameters.Count = true;
                //   //    requestConfiguration.QueryParameters.Orderby = new string[] { "displayName" };
                //   //    requestConfiguration.QueryParameters.Search = "\"displayName:Pr\"";
                //   //    requestConfiguration.QueryParameters.Select = new string[] { "displayName", "id" };
                //   //    requestConfiguration.Headers.Add("ConsistencyLevel", "eventual");
                //   //});

                //   var managedIdentityClientId = _configuration.GetSection("ManagedIdentityId").Value;
                //   var credentialOptions = new DefaultAzureCredentialOptions
                //   {
                //       ManagedIdentityClientId = clientId,
                //       //ExcludeEnvironmentCredential = true, //did not work
                //       // ExcludeWorkloadIdentityCredential = true,// did not work
                //   };
                //   // var newCredential = new ManagedIdentityCredential(clientId, credentialOptions);
                //   //var newCredential = new ManagedIdentityCredential(clientId);
                //   //var credential = new DefaultAzureCredential();
                //   var credential = new DefaultAzureCredential(credentialOptions);
                //   var secretClient = new SecretClient(
                //       new Uri("https://erpkeyvault.vault.azure.net/"),
                //       credential);
                //   KeyVaultSecret secret = secretClient.GetSecret("ErnieTest");
                //   string secretValue = secret.Value;
                //   responseModel.Message = $"secret {secretValue}";//Yay! it worked! -- in dev, user needs to enter azure credentials, tools - > azure credentials 
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseModel;
        }


        public async Task<ResponseModel<List<SubdivisionDto>>> GetSubdivisionsAsync()
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            var subdivisons = new List<SubdivisionDto>();

            try
            {                 
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/subdivision/getsubdivisions");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<SubdivisionDto>>>(responseString);
                    sw.Stop();
                    Console.WriteLine($"subdivisions task: {sw.Elapsed}");
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<SubdivisionDto>>() { Value = subdivisons, IsSuccess = false, Message = "Failed to get subdivision data" };
        }

        public async Task<SubdivisionDto> GetSubdivisionAsync(int subdivisionId)
        {
            var subdivison = new SubdivisionDto();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/subdivision/getsubdivision/{subdivisionId}");
                var responseString = await response.Content.ReadAsStringAsync();
                subdivison = JsonConvert.DeserializeObject<SubdivisionDto>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return subdivison;
        }

        public async Task<List<string>> GetSubdivisionPhasesAsync(int subdivisionId)
        {
            var phases = new List<string>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                       "DownstreamApi",
                       options => options.RelativePath = $"api/subdivision/getsubdivisionphases/{subdivisionId}");
                var responseString = await response.Content.ReadAsStringAsync();
                phases = JsonConvert.DeserializeObject<List<string>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return phases;
        }

        public async Task<ResponseModel<SubdivisionDto>> UpdateSubdivisionAsync(SubdivisionDto subdivision, bool includeDetails = false)
        {
            try
            {
                //includeDetails added because some updates from the subdivision list only have marketing name, but from the details page details are included
                var responseSubdivision = await _downstreamAPI.PutForUserAsync<SubdivisionDto, ResponseModel<SubdivisionDto>>(
                    "DownstreamApi", subdivision,
                    options =>
                    {
                        options.RelativePath = $"api/subdivision/updatesubdivision/{includeDetails}";
                    });
                return responseSubdivision;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

               
            }
            return new ResponseModel<SubdivisionDto> { IsSuccess = false, Message = "Error while updating Subdivision" };
        }
        public async Task<ResponseModel<SubdivisionDto>> AddSubdivisionAsync(SubdivisionDto subdivision)
        {
            try
            {
               var responseSubdivision = await _downstreamAPI.PostForUserAsync<SubdivisionDto, ResponseModel<SubdivisionDto>>(
                    "DownstreamApi", subdivision,
                    options =>
                    {
                        options.RelativePath = "api/subdivision/addsubdivision/";
                    });
                return responseSubdivision;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<SubdivisionDto> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<SubdivisionDto> { IsSuccess = false, Message = "Error while adding Subdivision" };
            }
        }
        public async Task<ResponseModel<List<JobDto>>> GetJobsAsync(int subdivisionId)
        {
            try
            {
                var jobs = await _downstreamAPI.CallApiForUserAsync<ResponseModel<List<JobDto>>>(
                     "DownstreamApi",
                     options => options.RelativePath = $"api/subdivision/getjobs/{subdivisionId}");
                return jobs;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<JobDto>> { IsSuccess = false, Message = "Login or consent needed", Value = new List<JobDto>() };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<JobDto>> { IsSuccess = false, Message = "Error while fetching jobs", Value = new List<JobDto>() };
            }
        }       
        public async Task<ResponseModel<List<JobDto>>> GetAllJobsAsync()
        {
            try
            {
                var jobs = await _downstreamAPI.CallApiForUserAsync<ResponseModel<List<JobDto>>>(
                     "DownstreamApi",
                     options => options.RelativePath = $"api/subdivision/jobs/");
                return jobs;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<JobDto>> { IsSuccess = false, Message = "Login or consent needed", Value = new List<JobDto>() };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<JobDto>> { IsSuccess = false, Message = "Error while fetching jobs", Value = new List<JobDto>() };
            }
        }
        public async Task<ResponseModel<JobDto>> GetLotAsync(string jobNumber)
        {
            try
            {
               var lot = await _downstreamAPI.CallApiForUserAsync<ResponseModel<JobDto>>(
                     "DownstreamApi",
                     options => options.RelativePath = $"api/subdivision/getlot/{jobNumber}");
                return lot;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
               
            }
            return new ResponseModel<JobDto> { IsSuccess = false, Message = "Error while fetching job" };
        }

        public async Task<ResponseModel<List<JobContactDto>>> GetJobContactsAsync(string jobNumber)
        {
            try
            {
                var lot = await _downstreamAPI.CallApiForUserAsync<ResponseModel<List<JobContactDto>>>(
                     "DownstreamApi",
                     options => options.RelativePath = $"api/subdivision/getjobcontacts/{jobNumber}");
                return lot;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif             
            }
            return new ResponseModel<List<JobContactDto>> { IsSuccess = false, Message = "Error while fetching job contacts" };
        }

        public async Task<ResponseModel<JobContactDto>> AddJobContactAsync(JobContactDto addJobContact)
        {
            try
            {
                var job = await _downstreamAPI.PostForUserAsync<JobContactDto, ResponseModel<JobContactDto>>(
                     "DownstreamApi", addJobContact,
                     options =>
                     {
                         options.RelativePath = $"api/subdivision/addjobcontact/";
                     });
                return job;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<JobContactDto> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<JobContactDto> { IsSuccess = false, Message = "Error while adding job contact" };
            }
        }

        public async Task<ResponseModel<JobContactDto>> DeleteJobContactAsync(JobContactDto updateJob)
        {
            try
            {
                var job = await _downstreamAPI.PutForUserAsync<JobContactDto, ResponseModel<JobContactDto>>(
                     "DownstreamApi", updateJob,
                     options =>
                     {
                         options.RelativePath = $"api/subdivision/deletejobcontact/";
                     });
                return job;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<JobContactDto> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<JobContactDto> { IsSuccess = false, Message = "Error while deleting job contact" };
            }
        }

        public async Task<ResponseModel<List<SubdivisionContactModel>>> GetSubdivisionContactsAsync(int subdivId)
        {
            try
            {
                var lot = await _downstreamAPI.CallApiForUserAsync<ResponseModel<List<SubdivisionContactModel>>>(
                     "DownstreamApi",
                     options => options.RelativePath = $"api/subdivision/getsubdivisioncontacts/{subdivId}");
                return lot;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<SubdivisionContactModel>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<SubdivisionContactModel>> { IsSuccess = false, Message = "Error while fetching subdivision contacts" };
            }
        }

        public async Task<ResponseModel<SubdivisionContactModel>> AddSubdivisionContactAsync(SubdivisionContactModel updateJob)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<SubdivisionContactModel, ResponseModel<SubdivisionContactModel>>(
                     "DownstreamApi", updateJob,
                     options =>
                     {
                         options.RelativePath = $"api/subdivision/addsubdivisioncontact/";
                     });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<SubdivisionContactModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<SubdivisionContactModel> { IsSuccess = false, Message = "Error while adding subdivision contact" };
            }
        }

        public async Task<ResponseModel<SubdivisionContactModel>> DeleteSubdivisionContactAsync(SubdivisionContactModel updateJob)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<SubdivisionContactModel, ResponseModel<SubdivisionContactModel>>(
                     "DownstreamApi", updateJob,
                     options =>
                     {
                         options.RelativePath = $"api/subdivision/deletesubdivisioncontact/";
                     });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<SubdivisionContactModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<SubdivisionContactModel> { IsSuccess = false, Message = "Error while deleting subdivision contact" };
            }
        }
        public async Task<ResponseModel<SubdivisionContactModel>> UpdateSubdivisionContactAsync(SubdivisionContactModel updateJob)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<SubdivisionContactModel, ResponseModel<SubdivisionContactModel>>(
                     "DownstreamApi", updateJob,
                     options =>
                     {
                         options.RelativePath = $"api/subdivision/updatesubdivisioncontact/";
                     });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<SubdivisionContactModel> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<SubdivisionContactModel> { IsSuccess = false, Message = "Error while updating subdivision contact" };
            }
        }
        public async Task<ResponseModel<List<UserDto>>> GetUserContactsAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync<ResponseModel<List<UserDto>>>(
                     "DownstreamApi",
                     options =>
                     {
                         options.RelativePath = $"api/subdivision/getusercontacts/";
                     });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<UserDto>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<UserDto>> { IsSuccess = false, Message = "Error while fetching user contacts" };
            }
        }
        public async Task<ResponseModel<List<UserDto>>> GetUserContactsFromAzureAsync()
        {
            try
            {                
                //transitive members gets the members of the nested groups 
                var usersList = new List<UserDto>();
                var groupId = _configuration.GetSection("MicrosoftGraph:ERPUsersGroupId").Value;
                var response = await _graphServiceClient
                  .Groups[groupId]
                  .TransitiveMembers
                  .GraphUser
                  .GetAsync();

                // It is possible for the response to only return a subset of users. If it does, it will contain a link in response.OdataNextLink to get more of the users. 
                while (response?.Value != null)
                {
                    usersList.AddRange(response.Value.Select(x => new UserDto
                    {
                        FullName = x.DisplayName,
                        EmailAddress = x.UserPrincipalName,
                        FirstName = x.GivenName,
                        LastName = x.Surname,
                        UserId = x.UserPrincipalName?.Split('@')[0]
                    }));

                    if (response.OdataNextLink == null)
                        break;

                    // Use the nextLink to get the next page
                    response = await _graphServiceClient
                        .Groups[groupId]
                        .TransitiveMembers
                        .GraphUser
                        .WithUrl(response.OdataNextLink)
                        .GetAsync();
                }
               
                return new ResponseModel<List<UserDto>> { IsSuccess = true, Message = "contacts", Value = usersList };
            }

            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<UserDto>> { IsSuccess = false, Message = "Error while fetching user contacts" };
            }
        }
        public async Task<ResponseModel<List<RoleDto>>> GetAllRolesAsync()
        {
            try
            {
                var response = await _downstreamAPI.GetForUserAsync<ResponseModel<List<RoleDto>>>("DownstreamApi", 
                    options => options.RelativePath = "api/subdivision/roles");
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif              
            }
            return new ResponseModel<List<RoleDto>> { IsSuccess = false, Message = "Error while fetching roles" };
        }
        public async Task<ResponseModel<JobContactDto>> UpdateJobContactAsync(JobContactDto updateJob)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<JobContactDto, ResponseModel<JobContactDto>>(
                     "DownstreamApi", updateJob,
                     options =>
                     {
                         options.RelativePath = $"api/subdivision/updatejobcontact/";
                     });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<JobContactDto> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<JobContactDto> { IsSuccess = false, Message = "Error while updating job contact" };
            }
        }
        
        public async Task<ResponseModel<UpdateJob>> UpdateMultipleJobsContactsAsync(UpdateJob updateJob)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<UpdateJob, ResponseModel<UpdateJob>>(
                     "DownstreamApi", updateJob,
                     options =>
                     {
                         options.RelativePath = $"api/subdivision/updatemultiplejobscontacts/";
                     });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<UpdateJob> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<UpdateJob> { IsSuccess = false, Message = "Error while updating multiple job contacts" };
            }
        }
        public async Task<ResponseModel<JobDto>> UpdateLotAsync(JobDto updateJob)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<JobDto, ResponseModel<JobDto>>(
                     "DownstreamApi", updateJob,
                     options =>
                     {
                         options.RelativePath = $"api/subdivision/updatelot/";
                     });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<JobDto> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<JobDto> { IsSuccess = false, Message = "Error while updating Lot" };
            }
        }
        public async Task<ResponseModel<JobDto>> AddJobAsync(JobDto updateJob)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<JobDto, ResponseModel<JobDto>>(
                     "DownstreamApi", updateJob,
                     options =>
                     {
                         options.RelativePath = $"api/subdivision/AddJob/";
                     });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<JobDto>() { IsSuccess = false, Value = updateJob, Message = "Failed to add job" };
        }
        public async Task<ResponseModel<UpdateJob>> UpdateLotsAsync(JobDto updateJob, List<string> updateLotNumbers)
        {
            //this method is for updating multiple jobs stick, phase, or building number
            var body = new UpdateJob()
            {
                JobNumber = updateJob.JobNumber,
                Phase = updateJob.Phase,
                BuildingNum = updateJob.BuildingNum,
                Stick = updateJob.StickBuilingNum,
                JobsToUpdate = updateLotNumbers
            };
            try
            {
                var job = await _downstreamAPI.PutForUserAsync<UpdateJob, ResponseModel<UpdateJob>>(
                     "DownstreamApi", body,
                     options =>
                     {
                         options.RelativePath = $"api/subdivision/updatelots/";
                     });
                return job;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<UpdateJob> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<UpdateJob> { IsSuccess = false, Message = "Error while updating Lots" };
            }
        }
        public async Task<ResponseModel<List<JobDto>>> UpdateJobsAsync(List<JobDto> jobsToBeUpdated)
        {
            try
            {
                var job = await _downstreamAPI.PutForUserAsync<List<JobDto>, ResponseModel<List<JobDto>>>(
                     "DownstreamApi", jobsToBeUpdated,
                     options =>
                     {
                         options.RelativePath = $"api/subdivision/UpdateJobs/";
                     });
                return job;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<List<JobDto>> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<JobDto>> { IsSuccess = false, Message = "Error while updating Jobs" };
            }
        }
        public async Task<ResponseModel<UpdateJob>> UpdateFieldsInLotsAsync(UpdateJob updateJob)
        {
            try
            {
                var job = await _downstreamAPI.PutForUserAsync<UpdateJob, ResponseModel<UpdateJob>>(
                     "DownstreamApi", updateJob,
                     options =>
                     {
                         options.RelativePath = $"api/subdivision/updatefieldsinlots/";
                     });
                return job;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
                return new ResponseModel<UpdateJob> { IsSuccess = false, Message = "Login or consent needed" };
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<UpdateJob> { IsSuccess = false, Message = "Error while updating Phase / Building Num / Stick Num" };
            }
        }

        public async Task<ResponseModel<List<JobDto>>> ImportExcel(FileSelectFileInfo file)
        {
            var ImportedJobs = new ResponseModel<List<JobDto>>() { Value = new List<JobDto>() };

            try
            {
                var fileData = new byte[file.Stream.Length];
                await file.Stream.ReadAsync(fileData);

                using (var ms = new MemoryStream(fileData))
                {
                    using (var excelWorkbook = new ClosedXML.Excel.XLWorkbook(ms))
                    {
                        var headerDictionary = new Dictionary<string, int>();
                        var headerRow = excelWorkbook.Worksheet(1).Row(1);
                        var nonEmptyDataRows = excelWorkbook.Worksheet(1).RowsUsed().Skip(1);
                        //for (int i = 1; i <= headerRow.CellCount(); i++)
                        //{
                        //    headerDictionary.Add((string)headerRow.Cell(i).Value, i);
                        //}

                        foreach (var dataRow in nonEmptyDataRows)
                        {
                            var jobDto = new JobDto()
                            {
                                JobNumber = dataRow.Cell(2).IsEmpty() ? "" : dataRow.Cell(2).Value.ToString(),
                                LotNumber = dataRow.Cell(3).IsEmpty() ? "" : dataRow.Cell(3).Value.ToString(),
                                JobDesc = dataRow.Cell(4).IsEmpty() ? "" : dataRow.Cell(4).Value.ToString(),
                                JobAddress1 = dataRow.Cell(5).IsEmpty() ? "" : dataRow.Cell(5).Value.ToString(),
                                JobAddress2 = dataRow.Cell(6).IsEmpty() ? null : dataRow.Cell(6).Value.ToString(),
                                JobCity = dataRow.Cell(7).IsEmpty() ? null : dataRow.Cell(7).Value.ToString(),
                                JobState = dataRow.Cell(8).IsEmpty() ? null : dataRow.Cell(8).Value.ToString(),
                                JobZipCode = dataRow.Cell(9).IsEmpty() ? null : dataRow.Cell(9).Value.ToString(),
                                JobCounty = dataRow.Cell(10).IsEmpty() ? null : dataRow.Cell(10).Value.ToString(),
                                LotCost = dataRow.Cell(11).IsEmpty() ? null : dataRow.Cell(11).GetValue<decimal>(),
                                LotSectionCode = dataRow.Cell(12).IsEmpty() ? null : dataRow.Cell(12).Value.ToString(),
                                LotSwing = dataRow.Cell(13).IsEmpty() ? null : dataRow.Cell(13).Value.ToString(),
                                GarageOrientation = new GarageOrientationDto() { GarageOrientationId = 0, Name = dataRow.Cell(14).IsEmpty() ? null : dataRow.Cell(14).Value.ToString() },
                                //LotUnit = dataRow.Cell(9).IsEmpty() ? null : dataRow.Cell(9).Value.ToString(),
                                LotSize = dataRow.Cell(15).IsEmpty() ? null : dataRow.Cell(15).GetValue<decimal>(),
                                LotWidth = dataRow.Cell(16).IsEmpty() ? null : dataRow.Cell(16).Value.ToString(),
                                Phase = dataRow.Cell(17).IsEmpty() ? null : dataRow.Cell(17).Value.ToString(),
                                BuildingNum = dataRow.Cell(18).IsEmpty() ? null : dataRow.Cell(18).Value.ToString(),
                                StickBuilingNum = dataRow.Cell(19).IsEmpty() ? null : dataRow.Cell(19).Value.ToString(),
                                HomeOrientationPerPlan = dataRow.Cell(20).IsEmpty() ? null : dataRow.Cell(20).GetBoolean(),
                                ParkingNum = dataRow.Cell(21).IsEmpty() ? null : dataRow.Cell(21).Value.ToString(),
                                StorageNum = dataRow.Cell(22).IsEmpty() ? null : dataRow.Cell(22).Value.ToString(),
                                GarageNum = dataRow.Cell(23).IsEmpty() ? null : dataRow.Cell(23).Value.ToString(),
                                JobConstructionType = new JobConstructionTypeDto() { JobConstructionTypeId = 0, Description = dataRow.Cell(24).IsEmpty() ? null : dataRow.Cell(24).Value.ToString() },
                                IsSs = dataRow.Cell(25).IsEmpty() ? null : dataRow.Cell(25).GetBoolean(),
                                ProjectedLotTakedown = dataRow.Cell(26).IsEmpty() ? null : dataRow.Cell(26).GetValue<DateTime>(),
                                ProjectedSalesReleaseDate = dataRow.Cell(27).IsEmpty() ? null : dataRow.Cell(27).GetValue<DateTime>(),
                                GeneralOptionBudget = dataRow.Cell(28).IsEmpty() ? null : dataRow.Cell(28).GetValue<decimal>(),
                                HomesiteOptionSpendBudget = dataRow.Cell(29).IsEmpty() ? null : dataRow.Cell(29).GetValue<decimal>(),
                                Notes = dataRow.Cell(30).IsEmpty() ? null : dataRow.Cell(30).Value.ToString()
                            };


                            ImportedJobs.Value.Add(jobDto);
                        }
                    }
                    ms.Close();
                }
                return ImportedJobs;
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return ImportedJobs;
        }

        public async Task<ResponseModel<List<JobDto>>> GetJobBySubdivisionAsync(int subdivisionId, bool? includeBlocked = true)
        {
            var jobs = new List<JobDto>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/subdivision/getjobbysubdivision/{subdivisionId}/{includeBlocked}");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<JobDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<JobDto>>() { Value = jobs, IsSuccess = false, Message = "Failed to get jobs data" };
        }
    }

    
}
