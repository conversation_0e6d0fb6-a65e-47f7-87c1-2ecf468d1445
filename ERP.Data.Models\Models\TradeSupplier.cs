﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class TradeSupplier
{
    public int TradeId { get; set; }

    public int SubNumber { get; set; }

    public int SubdivisionId { get; set; }

    public string DefaultSupplier { get; set; } = null!;

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual Supplier SubNumberNavigation { get; set; } = null!;

    public virtual Subdivision Subdivision { get; set; } = null!;

    public virtual Trade Trade { get; set; } = null!;
}
