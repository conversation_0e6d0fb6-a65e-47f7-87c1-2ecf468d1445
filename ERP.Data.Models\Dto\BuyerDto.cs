﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class BuyerDto : IMapFrom<Buyer>
{
    public int BuyerId { get; set; }

    public int ContractId { get; set; }

    public int? ContactId { get; set; }

    public int MilitaryStatusId { get; set; }

    public byte ListOrder { get; set; }

    public string? FirstName { get; set; }

    public string? MiddleInitial { get; set; }

    public string? LastName { get; set; } 

    public string? Address { get; set; } 

    public string? City { get; set; }

    public string? State { get; set; }

    public string? Zip { get; set; } 

    public string? Country { get; set; } 

    public string? Email { get; set; } 

    public string? Phone { get; set; } 

    public string? HomePhone { get; set; }

    public string? CellPhone { get; set; }

    public string? WorkPhone { get; set; }
    public bool? IsCancelled { get; set; }
    public string? Status { get; set; }//Message for spec, cancelled or sale

   // public Contract? Contract { get; set; }
}
