﻿@page "/approvesalesconfig"
@inject SalesConfigService SalesConfigService
@inject AuthenticationStateProvider AuthenticationStateProvider
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling")]
@using System.Collections.ObjectModel
@using Telerik.DataSource.Extensions
@using ERP.Data.Models

<style>
    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>
<PageTitle>Approve Sales Configurations</PageTitle>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<TelerikTooltip TargetSelector=".tooltip-target" />

<div class="container-fluid">
    <div class="row">
        <div class="card" style="background-color:#2e5771">
            <div class="card-body" style="padding:0.5rem">
                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Approve Sales Configurations</h7>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item active">Approve Sales Configuration</li>
        </ol>

        @if (SalesConfigData == null)
        {
            <p><em>Loading...</em></p>
            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />

        }
        else
        {
            <div class="col-lg-9">
                <TelerikTreeList Data="@SalesConfigData"
                                 SelectionMode="@TreeListSelectionMode.Single"
                @bind-SelectedItems="@SelectedItems"
                                 OnStateInit="((TreeListStateEventArgs<SalesConfigCombinedTreeModel> args) => OnStateInitHandler(args))"
                                     ScrollMode="@TreeListScrollMode.Virtual"
                                 IdField="Id"
                                 Id="WorksheetTree"
                                 Class="mytreeclass"
                                 ParentIdField="ParentId"
                                 
                                 PageSize="50"
                                 Resizable="true"
                                 Width="100%"
                                 Height="1000px"
                                 RowHeight="40"
                                 OnRowClick="@OnRowClickHandler"
                                 OnUpdate="@UpdateItem"
                                 OnExpand="@OnExpand"
                                 EditMode="@TreeListEditMode.Incell"
                @ref="@SalesConfigTreeList">
                    <TreeListColumns>
                        @* <TreeListCheckboxColumn />*@
                        <TreeListColumn Field="SubdivisionName" Title="Subdivision/ Job/ Configuration" Expandable="true" Editable="false" Visible="true" Width="280px">
                            <Template>
                                @{
                                    var item = context as SalesConfigCombinedTreeModel;
                                    if (item.SubdivisionId != null && item.JobNumber == null)
                                    {
                                        <span>@($"{item.SubdivisionName}")</span>
                                    }
                                    else if (item.JobNumber != null && item.SalesConfigCoId == null && item.SalesConfigId == null && item.SalesConfigOptionId == null && item.SalesConfigCoOptionId == null)
                                    {
                                        <span>@($"{item.JobNumber}")</span>
                                    }
                                    else if (item.SalesConfigId != null && item.SalesConfigCoId == null)
                                    {
                                        if (item.IsBaseHouseRow == true)
                                        {
                                            //layer for base house
                                            <span>@($"{item.PlanName} BASE HOUSE")</span>

                                        }
                                        else if (item.IsExpandOptionsRow == true)
                                        {
                                            //layer for sum/expand options row
                                            @("Options")
                                        }
                                        else
                                        {
                                            <span>@($"Sales Config Date: {(item.Ratificationdate != null ? item.Ratificationdate.Value.ToString("d") : "")} Customer: {(item.Status == "spec" ? "Spec" : item.SsAction == "D" ? $"Cancelled {item.Ownername} " :item.Ownername)}")</span>
                                        }

                                    }
                                    else if (item.SalesConfigCoId != null && item.SalesConfigCoOptionId == null)
                                    {
                                        <span> @($"Change Order: {item.CoNumber} Date: {(item.CoStatusdate != null ? item.CoStatusdate.Value.ToString("d") : "")}")</span>
                                    }
                                    else if (item.SalesConfigCoOptionId != null)
                                    {
                                        <span>
                                            @if (item.SalesConcessionWarning == true)
                                            {
                                                <span class="tooltip-target" title="Warning: Sales Price Concession"><TelerikFontIcon Icon="@FontIcon.ExclamationCircle" /></span>
                                            }
                                            @if (item.RequiresEstimate == true)
                                            {
                                                <TelerikButton Class="tooltip-target k-button-success mr-1" Title="Attach Estimate" Icon="@FontIcon.Paperclip" OnClick="@ShowAttachEstimate"></TelerikButton>
                                            }
                                            @($" Option: {item.OptionCode} ")
                                        </span>
                                    }
                                    else if (item.SalesConfigOptionId != null)
                                    {
                                        <span>
                                            @if (item.SalesConcessionWarning == true)
                                            {
                                                <span class="tooltip-target" title="Warning: Sales Price Concession"><TelerikFontIcon Icon="@FontIcon.ExclamationCircle" /></span>
                                            }
                                            @if (item.RequiresEstimate == true)
                                            {
                                                <TelerikButton Class="tooltip-target k-button-success mr-1" Title="Attach Estimate" Icon="@FontIcon.Paperclip" OnClick="@ShowAttachEstimate"></TelerikButton>
                                            }
                                            @($" Option: {item.OptionCode} ")
                                        </span>
                                    }
                                }

                            </Template>
                        </TreeListColumn>
                        <TreeListColumn Field="BoolIsApproved" Title="Approved" Visible="true" Editable="true" Width="50px">
                            <Template>
                                @{
                                    ItemToEdit = context as SalesConfigCombinedTreeModel;
                                    if (ItemToEdit.CoNumber != null || (ItemToEdit.SalesConfigId != null && ItemToEdit.IsExpandOptionsRow != true && ItemToEdit.IsBaseHouseRow != true))
                                    {
                                        <TelerikCheckBox Enabled="@ItemToEdit.Approvable" @bind-Value="ItemToEdit.BoolIsApproved"></TelerikCheckBox>
                                    }
                                }
                            </Template>
                            <EditorTemplate>
                                @{
                                    ItemToEdit = context as SalesConfigCombinedTreeModel;
                                    if (ItemToEdit.CoNumber != null || (ItemToEdit.SalesConfigId != null && ItemToEdit.IsExpandOptionsRow != true && ItemToEdit.IsBaseHouseRow != true))
                                    {
                                        <TelerikCheckBox Enabled="@ItemToEdit.Approvable" @bind-Value="ItemToEdit.BoolIsApproved"></TelerikCheckBox>
                                    }
                                }
                            </EditorTemplate>
                        </TreeListColumn>
                        <TreeListColumn Field="JobNumber" Editable="false" Visible="true" Width="0px"></TreeListColumn>
                        <TreeListColumn Field="JobsString" Editable="false" Visible="true" Width="0px"></TreeListColumn>
                        <TreeListColumn Field="PlanName" Editable="false" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="Ownername" Editable="false" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="DisplayRowIndex" Editable="false" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="SalesConfigId" Title="SalesConfigId" Editable="false" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="SalesConfigCoId" Title="SalesConfigCoId" Editable="false" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="SalesConfigOptionId" Title="SalesConfigOptionId" Editable="false" Visible="false"></TreeListColumn>
                        @*  <TreeListColumn Field="CustomOption" Title="IsCustom Option" Editable="false" Width="50px"></TreeListColumn> *@
                        <TreeListColumn Field="Quantity" Title="Quantity" Editable="false" Width="50px"></TreeListColumn>
                        <TreeListColumn Field="OptionNotes" Title="Option Notes" Editable="false" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="OptionSelections" Title="Option Selections" Editable="false" Visible="false"></TreeListColumn>
                        <TreeListColumn Field="OptionCode" Title="Option Code" Editable="false" Width="100px"></TreeListColumn>
                        <TreeListColumn Field="OptionDesc" Title="Option Desc" Editable="false" Width="120px"></TreeListColumn>
                        <TreeListColumn Field="UnitCost" Title="Unit Cost" Editable="false" DisplayFormat="{0:C2}" Width="100px"></TreeListColumn>
                        <TreeListColumn Field="ListPrice" Title="Unit List Price" DisplayFormat="{0:C2}" Editable="false" Width="100px"></TreeListColumn>
                        <TreeListColumn Field="SellingPrice" Title="Selling Price" DisplayFormat="{0:C2}" Editable="false" Width="100px"></TreeListColumn>
                        <TreeListColumn Field="SalesVariance" Title="Sales Variance" DisplayFormat="{0:C2}" Editable="false" Width="100px"></TreeListColumn>
                        <TreeListColumn Field="SellingPriceMargin" Title="Selling Price Margin" DisplayFormat="{0:C2}" Editable="false" Width="100px"></TreeListColumn>
                        <TreeListColumn Field="SellingPriceMarginPercent" Title="Selling Price Margin Percent" DisplayFormat="{0:0.##}" Editable="false" Width="100px"></TreeListColumn>
                    </TreeListColumns>
                    <TreeListToolBarTemplate>
                        <TreeListSearchBox DebounceDelay="200">
                        </TreeListSearchBox>
                    </TreeListToolBarTemplate>
                </TelerikTreeList>
            </div>
            <div class="card col-lg-3">
                <h5>Details</h5>
                @if (ItemDetails != null)
                {
                    @if (ItemDetails.SalesConfigId != null && ItemDetails.IsBaseHouseRow != true && ItemDetails.IsExpandOptionsRow != true && ItemDetails.SalesConfigCoId == null)
                    {
                        <p><strong>Configuration Id:</strong> @ItemDetails.SalesConfigId</p>
                        <p><strong>Sequence Number:</strong> </p>
                        <p><strong>Buyer Name:</strong> @ItemDetails.Ownername</p>
                        <p><strong>Buyer Address:</strong> @ItemDetails.Owneraddress1</p>
                        <p><strong>Buyer City:</strong> @ItemDetails.Ownersuburb</p>
                        <p><strong>Buyer State:</strong> @ItemDetails.Ownerstate</p>
                        <p><strong>Buyer Zip:</strong> @ItemDetails.Ownerpostcode</p>
                        <p><strong>Buyer Phone:</strong> @ItemDetails.Ownerphone1</p>
                        <p><strong>Buyer Email:</strong> @ItemDetails.Owneremail</p>
                        <p><strong>CoBuyer:</strong> @ItemDetails.UserContact1</p>
                        <p><strong>Sales Agent:</strong> @ItemDetails.SalesContact</p>
                        <p><strong>Lot Premium:</strong> @ItemDetails.LotPremium</p>
                        <p><strong>Lot Swing:</strong> @ItemDetails.LotSwing</p>
                        <p><strong>Status:</strong> @ItemDetails.Status</p>
                        <p><strong>Estimated settlement date:</strong> @ItemDetails.Estimatedsettlementdate</p>
                        <p><strong>Ratification:</strong> @ItemDetails.Ratificationdate</p>
                        <p><strong>Closing date:</strong> @ItemDetails.Closingdate</p>
                        <p><strong>Cancel date:</strong> @ItemDetails.Canceldate</p>
                        <p><strong>Sales Date:</strong> @ItemDetails.Saledate</p>
                    }
                    else if (ItemDetails.SalesConfigId != null && ItemDetails.IsBaseHouseRow == true && ItemDetails.IsExpandOptionsRow != true)
                    {
                        <p><strong>Plan Name:</strong> @ItemDetails.PlanName</p>
                        <p><strong>Plan Code:</strong> @ItemDetails.OptionCode</p>
                        @*  <p>Elevation Code: @ItemDetails.OptionCode</p> *@
                        <p><strong>Selling Price:</strong> @(ItemDetails.SellingPrice != null ? ItemDetails.SellingPrice.Value.ToString("c") : "")</p>
                    }
                    else if (ItemDetails.SubdivisionId != null && ItemDetails.JobNumber == null)
                    {
                        @*   <p>Subdivision Id: @ItemDetails.SubdivisionId</p> *@
                        <p><strong>Subdivision Name:</strong> @ItemDetails.SubdivisionName</p>
                    }
                    else if (ItemDetails.JobNumber != null && ItemDetails.SalesConfigCoId == null && ItemDetails.SalesConfigId == null && ItemDetails.SalesConfigOptionId == null && ItemDetails.SalesConfigCoOptionId == null)
                    {
                        <p><strong>Job Number:</strong> @ItemDetails.JobNumber</p>
                        <p><strong>Lot Number:</strong> @ItemDetails.LotNumber</p>
                        <p><strong>Address:</strong> @ItemDetails.LotAddress </p>
                        <p><strong>City:</strong> @ItemDetails.LotCity</p>
                    }
                    else if (ItemDetails.SalesConfigOptionId != null)
                    {
                        <p><strong>Option Code:</strong> @ItemDetails.OptionCode</p>
                        <p><strong>Desc:</strong> @ItemDetails.OptionDesc</p>
                        <p><strong>Selling Price:</strong> @ItemDetails.SellingPrice</p>
                        <p><strong>Selections:</strong> @ItemDetails.OptionSelections</p>
                        <p><strong>Notes:</strong> @ItemDetails.OptionNotes</p>
                        <p><strong>Quantity:</strong> @ItemDetails.Quantity</p>
                        <p><strong>Attribute Selections:</strong> @ItemDetails.AttributeSelectionsString</p>
                        <span>
                            <p><strong>Associated Estimate:</strong> @ItemDetails.AssociatedEstimate</p>
                            @if (ItemDetails.CustomOption == true)
                            {
                                <TelerikButton Class="tooltip-target" Title="Attach Estimate" ButtonType="@ButtonType.Button" Icon="@FontIcon.Pencil" OnClick="@ShowAttachEstimate"></TelerikButton>
                            }
                        </span>
                    }
                    else if (ItemDetails.SalesConfigCoOptionId != null)
                    {
                        <p><strong>Option Code:</strong> @ItemDetails.OptionCode</p>
                        <p><strong>Desc:</strong> @ItemDetails.OptionDesc</p>
                        <p><strong>Selling Price:</strong> @ItemDetails.SellingPrice</p>
                        <p><strong>Selections:</strong> @ItemDetails.OptionSelections</p>
                        <p><strong>Notes:</strong> @ItemDetails.OptionNotes</p>
                        
                        <p><strong>Quantity:</strong> @ItemDetails.Quantity</p>
                        <p><strong>Attribute Selections:</strong> @ItemDetails.AttributeSelectionsString</p>
                        <span>
                            <p><strong>Associated Estimate:</strong> @ItemDetails.AssociatedEstimate</p>
                            @if (ItemDetails.CustomOption == true)
                            {
                                <TelerikButton Class="tooltip-target" Title="Attach Estimate" ButtonType="@ButtonType.Button" Icon="@FontIcon.Pencil" OnClick="@ShowAttachEstimate"></TelerikButton>
                            }
                        </span>
                    }
                    else if (ItemDetails.SalesConfigCoId != null)
                    {
                        <p><strong>CO Id:</strong>  @ItemDetails.SalesConfigCoId</p>
                        <p><strong>Number:</strong>  @ItemDetails.CoNumber</p>
                        <p><strong>Co Status:</strong>  @ItemDetails.CoStatus</p>
                        <p><strong>Co Status Date:</strong>  @ItemDetails.CoStatusdate</p>
                    }
                }

            </div>
        }
    </div>
</div>
    
<ERP.Web.Components.AttachEstimate @ref="AttachEstimateModal" JobNumber = "@ItemDetails.JobNumber" SalesConfigOptionId="@ItemDetails.SalesConfigOptionId" SalesConfigCoOptionId="@ItemDetails.SalesConfigCoOptionId" HandleAddSubmit="HandleValidAttachEstimateSubmit"></ERP.Web.Components.AttachEstimate>

@code {
    public List<SalesconfigDto>? SalesConfigs { get; set;  }
    public List<SalesconfigcoDto>? SalesConfigCos { get; set; }
    public List<SalesconfigoptionDto>? SalesConfigOptions { get; set;  }
    public List<SalesconfigcooptionDto>? SalesConfigCoOptions { get; set; }
    public ObservableCollection<SalesConfigCombinedTreeModel>? SalesConfigData { get; set; }
    private TelerikTreeList<SalesConfigCombinedTreeModel>? SalesConfigTreeList { get; set; }
    public SalesConfigCombinedTreeModel? ItemToEdit { get; set; }
    public SalesConfigCombinedTreeModel? ItemDetails { get; set; } = new SalesConfigCombinedTreeModel();
    public IEnumerable<SalesConfigCombinedTreeModel>? SelectedItems { get; set; } 
    public bool WindowIsVisible { get; set; } = true;
    public ERP.Web.Components.AttachEstimate? AttachEstimateModal { get; set; }
    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    private bool AllowEdit { get; set; } = true;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        //await LoadDataAsync();
        // var testItems = await SalesConfigService.GetSalesConfigTreeAsync();//Trying to load whole tree instead of in the onexpand. Too slow. 
        SelectedItems = Enumerable.Empty<SalesConfigCombinedTreeModel>();
        var getSalesConfigCommunities = await SalesConfigService.GetSalesConfigCommunitiesAsync();
        if (getSalesConfigCommunities.IsSuccess == false)
        {
            ShowSuccessOrErrorNotification(getSalesConfigCommunities.Message, true);
        }
        SalesConfigData = new ObservableCollection<SalesConfigCombinedTreeModel>(getSalesConfigCommunities.Value.Select(x => new SalesConfigCombinedTreeModel()
            {
                Id = Guid.NewGuid(),
                HasChildren = true,
                SubdivisionId = x.SubdivisionId,
                SubdivisionName = x.SubdivisionName,
                JobsString = x.JobsString//adding just to make search on job work even when that level is not in table due to fetch data on expand
            }));
        var addDummyDataForSearch = SalesConfigData.Select(x => new SalesConfigCombinedTreeModel()
            {
                Id = Guid.NewGuid(),
                ParentId = x.Id,
                SubdivisionName = x.SubdivisionName,
                SubdivisionId = x.SubdivisionId,
                JobsString = x.JobsString,
                DragClue = x.SubdivisionName,
                HasChildren = false,               
                IsDummyForSearch = true
            }).OrderBy(x => x.SubdivisionName).ToList();
        SalesConfigData.AddRange(addDummyDataForSearch);//Hack to make the expandable rows stay expandable when filtering, so it appears there are children
    }
    async Task OnStateInitHandler(TreeListStateEventArgs<SalesConfigCombinedTreeModel> args)
    {
        var collapsedItemsState = new TreeListState<SalesConfigCombinedTreeModel>()
            {
                //collapse all items in the TreeList upon initialization of the state
                ExpandedItems = new List<SalesConfigCombinedTreeModel>()
            };
        args.TreeListState = collapsedItemsState;
    }
    async Task OnExpand(TreeListExpandEventArgs args)
    {
        //TODO: different model for differnt level, use interface
        var item = args.Item as SalesConfigCombinedTreeModel;
        if (item.HasChildren && item.ParentId == null)//subdivision row
        {
            if (item.SubdivisionId != null && !SalesConfigData.Any(x => x.ParentId == item.Id && x.IsDummyForSearch != true))
            {
                //remove search dummy now that real data to be added
                var findDummyForSearch = SalesConfigData.Where(x => x.ParentId == item.Id && x.IsDummyForSearch == true).ToList();
                foreach (var dummy in findDummyForSearch)
                {
                    SalesConfigData.Remove(dummy);
                };
                //get jobs in the subdivision 
                var getJobs = await SalesConfigService.GetSalesConfigJobsByCommunityAsync((int)item.SubdivisionId);
                if (getJobs.IsSuccess == false)
                {
                    ShowSuccessOrErrorNotification(getJobs.Message, true);
                }
                var jobsToAdd = getJobs.Value.Select(x => new SalesConfigCombinedTreeModel()
                    {
                        Id = Guid.NewGuid(),
                        ParentId = item.Id,
                        JobNumber = x.JobNumber,
                        LotAddress = x.LotAddress,
                        LotCity = x.LotCity,
                        LotNumber = x.LotNumber,
                        SalesConfigId = x.SalesConfigId,
                        SubdivisionId = item.SubdivisionId,
                        SubdivisionName = item.SubdivisionName,
                        HasChildren = true
                    }).ToArray();
                SalesConfigData.AddRange(jobsToAdd);
                var addDummyData = jobsToAdd.Select(x => new SalesConfigCombinedTreeModel()
                    {
                        Id = Guid.NewGuid(),
                        ParentId = x.Id,
                        JobNumber = x.JobNumber,
                        LotAddress = x.LotAddress,
                        LotCity = x.LotCity,
                        LotNumber = x.LotNumber,
                        SalesConfigId = x.SalesConfigId,
                        SubdivisionId = item.SubdivisionId,
                        SubdivisionName = item.SubdivisionName,
                        HasChildren = false,
                        IsDummyForSearch = true
                    }).ToList();
                SalesConfigData.AddRange(addDummyData);
            }
        }
        else if (item.HasChildren && item.JobNumber != null && item.SalesConfigCoId == null && item.SalesConfigId == null && !SalesConfigData.Any(x => x.ParentId == item.Id && x.IsDummyForSearch != true)) //job row
        {
            var findDummyForSearch = SalesConfigData.Where(x => x.ParentId == item.Id && x.IsDummyForSearch == true).ToList();
            foreach (var dummy in findDummyForSearch)
            {
                SalesConfigData.Remove(dummy);
            };
            //get configs and options in job
            var getSalesConfigsAndCos = await SalesConfigService.GetSalesConfigsAndCosByJobAsync(item.JobNumber);
            if (getSalesConfigsAndCos.IsSuccess == false)
            {
                ShowSuccessOrErrorNotification(getSalesConfigsAndCos.Message, true);
            }
            var configsToAdd = getSalesConfigsAndCos.Value.Select(x => new SalesConfigCombinedTreeModel()
                {
                    Id = Guid.NewGuid(),
                    SubdivisionId = item.SubdivisionId,
                    SubdivisionName = item.SubdivisionName,
                    SalesConfigId = x.SalesConfigId,
                    SalesConfigCoId = x.SalesConfigCoId,
                    IsApproved = x.IsApproved,
                    BoolIsApproved = x.BoolIsApproved,
                    Approvable = true,//x.Approvable, //TODO: don't reapprove, approve only in correct order, but for testing, need to be able to reapprove, if it's a co, can't approve unless initail is approved
                    Ownername = x.Ownername,
                    Owneraddress1 = x.Owneraddress1,
                    Owneraddress2 = x.Owneraddress2,
                    Owneremail = x.Owneremail,
                    Ownermobile = x.Ownermobile,
                    Ownerphone1 = x.Ownerphone1,
                    Ownerpostcode = x.Ownerpostcode,
                    Ownerstate = x.Ownerstate,
                    Ownersuburb = x.Ownersuburb,
                    UserContact1 = x.UserContact1,
                    SalesagentName = x.SalesagentName,
                    SalesContact = x.SalesContact,
                    PlanName = x.PlanName,
                    PlanNum = x.PlanNum,
                    OptionName = x.OptionName,                   
                    ParentId = item.Id,
                    CoNumber = x.CoNumber,
                    CoStatus = x.CoStatus,
                    CoStatusdate = x.CoStatusdate,
                    Saledate = x.Saledate,
                    Status = x.Status,
                    UnitCost =  x.UnitCost,
                    OptionCode = x.OptionCode,
                    OptionDesc = x.OptionDesc,
                    SellingPrice = x.SalesConfigCoId != null ? x.SumOptionPrice : x.SumBaseHouseAndOptionPrice,//This one actually needs to be sum of options + base house
                    Baseprice = x.Baseprice,
                    HasChildren = true,
                    SumOptionPrice = x.SumOptionPrice,
                    SSConfigurationId = x.SSConfigurationId,
                    Ratificationdate = x.Ratificationdate,
                    Estimatedsettlementdate = x.Estimatedsettlementdate,
                    Canceldate = x.Canceldate,
                    Closingdate = x.Closingdate,
                    LotPremium = x.LotPremium,
                    ListPrice = x.ListPrice,
                    LotPremiumIncentive = x.LotPremiumIncentive,
                    LotSwing = x.LotSwing,
                    JobNumber = item.JobNumber,
                    SsAction = x.SsAction,                   
                }).ToList();
            SalesConfigData.AddRange(configsToAdd);
            var addDummyData = configsToAdd.Where(x => x.SalesConfigCoId != null).Select(x => new SalesConfigCombinedTreeModel()
                {
                    Id = Guid.NewGuid(),
                    ParentId = x.Id,
                    JobNumber = x.JobNumber,
                    LotAddress = x.LotAddress,
                    LotCity = x.LotCity,
                    LotNumber = x.LotNumber,
                    SalesConfigId = x.SalesConfigId,
                    SalesConfigCoId = x.SalesConfigCoId,
                    SubdivisionId = item.SubdivisionId,
                    SubdivisionName = item.SubdivisionName,
                    HasChildren = false,
                    IsDummyForSearch = true
                }).ToList();
            SalesConfigData.AddRange(addDummyData);
            //add another layer in order to sum the base house and options
            foreach (var config in configsToAdd.Where(x => x.SalesConfigCoId == null))
            {
                var rowToAdd = new SalesConfigCombinedTreeModel()//for base house
                {
                        Id = Guid.NewGuid(),
                        SubdivisionId = item.SubdivisionId,
                        SubdivisionName = item.SubdivisionName,
                        ParentId = config.Id,
                        PlanName = config.PlanName,
                        PlanNum = config.PlanNum,                        
                        OptionCode = config.OptionCode,
                        OptionDesc = config.OptionDesc,
                        HasChildren = false,
                        SalesConfigId = config.SalesConfigId,
                        SellingPrice = config.Baseprice,
                        ListPrice = config.ListPrice,
                        UnitCost = config.UnitCost,
                        JobNumber = item.JobNumber,
                        IsBaseHouseRow = true
                };
                SalesConfigData.Add(rowToAdd);
                var rowToAdd2 = new SalesConfigCombinedTreeModel()//for sum options and expand
                    {
                        Id = Guid.NewGuid(),
                        SubdivisionId = item.SubdivisionId,
                        SubdivisionName = item.SubdivisionName,
                        ParentId = config.Id,
                        HasChildren = true,
                        SalesConfigId = config.SalesConfigId,
                        SellingPrice = config.SumOptionPrice,
                        ListPrice = config.ListPrice,
                        UnitCost = config.SumOptionCost,
                        JobNumber = item.JobNumber,
                        IsExpandOptionsRow = true,
                    };
                SalesConfigData.Add(rowToAdd2);
                var addDummyData3 = new SalesConfigCombinedTreeModel()
                    {
                        Id = Guid.NewGuid(),
                        ParentId = rowToAdd2.Id,
                        JobNumber = rowToAdd2.JobNumber,
                        LotAddress = rowToAdd2.LotAddress,
                        LotCity = rowToAdd2.LotCity,
                        LotNumber = rowToAdd2.LotNumber,
                        SalesConfigId = rowToAdd2.SalesConfigId,
                        SubdivisionId = item.SubdivisionId,
                        SubdivisionName = item.SubdivisionName,
                        HasChildren = false,
                        IsDummyForSearch = true
                    };
                SalesConfigData.Add(addDummyData3);
            }
        }        
        else if (item.HasChildren && item.SalesConfigId != null && item.IsExpandOptionsRow == true && !SalesConfigData.Any(x => x.ParentId == item.Id && x.IsDummyForSearch != true))
        {
            var findDummyForSearch = SalesConfigData.Where(x => x.ParentId == item.Id && x.IsDummyForSearch == true).ToList();
            foreach (var dummy in findDummyForSearch)
            {
                SalesConfigData.Remove(dummy);
            };
            var getSalesOpts = await SalesConfigService.GetSalesConfigOptionsAsync((int)item.SalesConfigId);
            if (getSalesOpts.IsSuccess == false)
            {
                ShowSuccessOrErrorNotification(getSalesOpts.Message, true);
            }
            //TODO: why on earth is it reselecting here??? get sales config optionss returns the right model
            var salesOptsToAdd = getSalesOpts.Value.Select(x => new SalesConfigCombinedTreeModel()
                {
                    Id = Guid.NewGuid(),
                    ParentId = item.Id,
                    SalesConfigOptionId = x.SalesConfigOptionId,
                    OptionSelections = x.OptionSelections,
                    PlanOptionId = x.PlanOptionId,
                    IsActive = x.IsActive,
                    Quantity = x.OptionQuantity,
                    SellingPrice = x.OptionPrice,
                    ListPrice = x.ListPrice,
                    SalesVariance = x.SellingPrice - (x.ListPrice * x.OptionQuantity), //TODO: should this be multiplying the unit list price * quantity??? still wrong on deleted optins
                    SellingPriceMargin = x.SellingPrice - x.UnitCost,//TODO: quantity??
                    SellingPriceMarginPercent = x.UnitCost != null && x.UnitCost != 0 ? 100 * (x.SellingPrice - x.UnitCost) / x.UnitCost : 0,
            UnitCost = x.UnitCost,
                    OptionNotes = x.OptionNotes,
                    OptionCode = x.SsOptioncode,
                    OptionDesc = x.ScDescription,
                    CustomOption = x.CustomOption,
                    AssociatedEstimate = x.AssociatedEstimate,
                    RequiresEstimate = x.CustomOption == true && String.IsNullOrWhiteSpace(x.AssociatedEstimate),
                    JobNumber = item.JobNumber,
                    SubdivisionName = item.SubdivisionName,
                    SubdivisionId = item.SubdivisionId,
                    AttributeSelectionsString = x.AttributeSelectionsString,
                    HasChildren = false
                }).ToList();
            SalesConfigData.AddRange(salesOptsToAdd);            
        }
        else if (item.HasChildren && item.SalesConfigCoId != null && !SalesConfigData.Any(x => x.ParentId == item.Id && x.IsDummyForSearch != true))
        {
            var findDummyForSearch = SalesConfigData.Where(x => x.ParentId == item.Id && x.IsDummyForSearch == true).ToList();
            foreach (var dummy in findDummyForSearch)
            {
                SalesConfigData.Remove(dummy);
            };
            var getSalesCoOpts = await SalesConfigService.GetSalesConfigsCoOptionsAsync((int)item.SalesConfigCoId);
            if (getSalesCoOpts.IsSuccess == false)
            {
                ShowSuccessOrErrorNotification(getSalesCoOpts.Message, true);
            }
            var salesOptsToAdd = getSalesCoOpts.Value.Select(x => new SalesConfigCombinedTreeModel()
                {
                    Id = Guid.NewGuid(),
                    ParentId = item.Id,
                    SalesConfigCoOptionId = x.SalesConfigCoOptionId,
                    Quantity = x.SalesconfigcoAction == "a" ? x.SalesconfigcoQuantityChange : -x.SalesconfigcoQuantityChange,//quantity negative if action is d (drop?)
                    SellingPrice = x.SalesconfigcoAction == "a" ?  x.SalesconfigcoPrice : -x.SalesconfigcoPrice,
                    UnitCost = x.UnitCost,
                    ListPrice = x.ListPrice,
                    SalesVariance = x.SellingPrice - x.ListPrice,//TODO: wrong if quantity not 1???
                    SalesConcessionWarning = x.CustomOption != true && x.SellingPrice < x.ListPrice,
                    SellingPriceMargin = x.SellingPrice - x.UnitCost,
                    SellingPriceMarginPercent = x.UnitCost != null && x.UnitCost != 0 ?  100 * (x.SellingPrice - x.UnitCost) / x.UnitCost : 0,
                    OptionSelections = x.SalesconfigcoSelections,
                    OptionNotes = x.SalesconfigcoNotes,
                    OptionCode = x.SsOptioncode,
                    OptionDesc = x.ScDescription,
                    CustomOption = x.CustomOption,
                    AssociatedEstimate = x.AssociatedEstimate,
                    RequiresEstimate = x.CustomOption == true && String.IsNullOrWhiteSpace(x.AssociatedEstimate),
                    IsActive = x.IsActive,
                    JobNumber = item.JobNumber,
                    SubdivisionName = item.SubdivisionName,
                    SubdivisionId = item.SubdivisionId,
                    AttributeSelectionsString = x.AttributeSelectionsString,
                    HasChildren = false
                }).ToList();
            SalesConfigData.AddRange(salesOptsToAdd);
        }        
    }
    async Task OnCollapse(TreeListCollapseEventArgs args)
    {
        //use this to track display row index
        var item = args.Item as SalesConfigCombinedTreeModel;
        //var children = SelectedWorksheetData.Where(x => x.ParentId == item.Id).Count();//TODO: what if multiple levels of children
        //foreach (var shiftItemIndex in SelectedWorksheetData.Where(x => x.DisplayRowIndex > item.DisplayRowIndex))
        //{
        //    shiftItemIndex.DisplayRowIndex = shiftItemIndex.DisplayRowIndex - children;
        //}
    }
    private async Task UpdateItem(TreeListCommandEventArgs args)
    {
        var item = args.Item as SalesConfigCombinedTreeModel;

        if(item.SalesConfigCoId != null)
        {
            //update/approve the change order
            var response = await SalesConfigService.ApproveCoAsync(item);
            if (response.IsSuccess)
            {
                var findItem = SalesConfigData.SingleOrDefault(x => x.Id == item.Id);
                findItem.BoolIsApproved = item.BoolIsApproved;
            }
            ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        }
        else if(item.SalesConfigId != null)
        {
            //update/approve the config
            var response = await SalesConfigService.ApproveConfigAsync(item);
            if (response.IsSuccess)
            {
                var findItem = SalesConfigData.SingleOrDefault(x => x.Id == item.Id);
                findItem.BoolIsApproved = item.BoolIsApproved;
            }
            ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        }
    }
    void OnRowClickHandler(TreeListRowClickEventArgs args)
    {
        var keypress = args.EventArgs as MouseEventArgs;
        var row = args.Item as SalesConfigCombinedTreeModel;
        ItemDetails = row;
        SelectedItems = new List<SalesConfigCombinedTreeModel>(){ItemDetails};
        //TODO: it seems to go wrong after edit a row
        //use to show details in sidebar
    }
    private async Task ShowAttachEstimate()
    {
        await AttachEstimateModal.Show();
    }
    private async void HandleValidAttachEstimateSubmit(AttachEstimateModel responseModel)
    {
        //TODO: show added data
        var newEstimateName = responseModel.AttachEstimateName;
        ItemDetails.AssociatedEstimate = responseModel.AttachEstimateName;//TODO: what if the same item is not selected. or if it never was selected
                                                                          //TOOD: also need to remove the button and warning about attach, but need to be able to change the attached estimate in the details 
        var findItem = SalesConfigData.SingleOrDefault(x => x.Id == ItemDetails.Id);
        findItem.RequiresEstimate = false;
        findItem.UnitCost = responseModel.EstimateCost;
        //TODO: attaching the estimate might change whether the co or config is now ready to approve or not
        var findParent = SalesConfigData.SingleOrDefault(x => x.Id == ItemDetails.ParentId);//TODO: if it's a config not a change order, the direct parent is different
        if(findParent.Approvable == false)
        {
            var findSiblings = SalesConfigData.Where(x => x.ParentId == findParent.Id);
            if(findSiblings.Any(x => x.PlanOptionId == null && string.IsNullOrWhiteSpace(x.AssociatedEstimate)))
            {
                findParent.Approvable = false;
            }
            else
            {
                findParent.Approvable = true;
            }
        }
        AttachEstimateModal.Hide();
        StateHasChanged();
    }
    private void ShowSuccessOrErrorNotification(string message, bool IsSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = IsSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
