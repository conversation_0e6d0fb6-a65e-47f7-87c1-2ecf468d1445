﻿using Azure.Core;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.ExtendedProperties;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using NLog;
using System.Diagnostics;
using System.Reflection.Metadata.Ecma335;
using System.Text;
using Telerik.Blazor.Components.FileSelect;
using Microsoft.Identity.Abstractions;
using System.Collections.Generic;
using static Microsoft.Graph.CoreConstants;
using Microsoft.AspNetCore.Components.Authorization;
using ERP.Web.Pages;

namespace ERP.Web.Data
{
    public class BudgetService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        public BudgetService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }
        public async Task<ResponseModel<EstheaderDto>> GetEstHeaderAsync(int estHeaderId)
        {
            var header = new ResponseModel<EstheaderDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => { options.RelativePath = $"api/budget/GetEstHeader/{estHeaderId}"; });
                var responseString = await response.Content.ReadAsStringAsync();

                header = JsonConvert.DeserializeObject<ResponseModel<EstheaderDto>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return header;
        }       
        public async Task<ResponseModel<EstoptionDto>> GetEstOptionAsync(int estOptionId)
        {
            var option = new ResponseModel<EstoptionDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetEstOption/{estOptionId}");
                var responseString = await response.Content.ReadAsStringAsync();

                option = JsonConvert.DeserializeObject<ResponseModel<EstoptionDto>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return option;
        }
        public async Task<ResponseModel<EstactivityDto>> GetEstActivityAsync(int estActivityId)
        {
            var activity = new ResponseModel<EstactivityDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetEstActivity/{estActivityId}");
                var responseString = await response.Content.ReadAsStringAsync();

                activity = JsonConvert.DeserializeObject<ResponseModel<EstactivityDto>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return activity;
        }
        public async Task<List<EstdetailDto>> GetEstDetailsForActivityAsync(int estActivityId)
        {
            var activity = new List<EstdetailDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetEstDetailsForActivity/{estActivityId}");
                var responseString = await response.Content.ReadAsStringAsync();

                activity = JsonConvert.DeserializeObject<List<EstdetailDto>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return activity;
        }
        public async Task<ResponseModel<EstdetailDto>> GetEstDetailAsync(int estDetailId)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetEstDetail/{estDetailId}");
                var responseString = await response.Content.ReadAsStringAsync();

                var detail = JsonConvert.DeserializeObject<ResponseModel<EstdetailDto>>(responseString);
                return detail;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<EstdetailDto>() { IsSuccess = false, Message = "Failed to get details."};
        }
        public async Task<ResponseModel<List<PodetailDto>>> GetPoDetailsByPONumberAsync(int poHeaderId)
        {
            var detail = new List<PodetailDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetPODetailsByPoNumber/{poHeaderId}");
                var responseString = await response.Content.ReadAsStringAsync();

                var result = JsonConvert.DeserializeObject<ResponseModel<List<PodetailDto>>>(responseString);
                return result;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<PodetailDto>>() { Value = detail, IsSuccess = false, Message = "Failed to get EstDetails by PO Number" };
        }
        public async Task<ResponseModel<List<ReferenceTypeDto>>> GetReferenceTypesAsync()
        {
            var refTypes = new ResponseModel<List<ReferenceTypeDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetReferenceTypes/");
                var responseString = await response.Content.ReadAsStringAsync();

                refTypes = JsonConvert.DeserializeObject<ResponseModel<List<ReferenceTypeDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return refTypes;
        }
        public async Task<ResponseModel<List<EstimateSourceDto>>> GetEstimateSourcesAsync()
        {
            var estSources = new ResponseModel<List<EstimateSourceDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetEstimateSources/");
                var responseString = await response.Content.ReadAsStringAsync();

                estSources = JsonConvert.DeserializeObject<ResponseModel<List<EstimateSourceDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return estSources;
        }
        public async Task<ResponseModel<List<string>>> GetVarianceCategoriesAsync()
        {
            var varianceCategories = new ResponseModel<List<string>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/getvariancecategories/");
                var responseString = await response.Content.ReadAsStringAsync();

                varianceCategories = JsonConvert.DeserializeObject <ResponseModel<List<string>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return varianceCategories;
        }
        
        public async Task<ResponseModel<List<CombinedPOBudgetTreeModel>>> GetBudgetByJobAsync(string jobNumber)
        {
            try
            {

                var response = await _downstreamAPI.GetForUserAsync<ResponseModel<List<CombinedPOBudgetTreeModel>>>("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetBudgetByJob/{jobNumber}");
                
                return response;
                
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<CombinedPOBudgetTreeModel>>(); ;
        }
        public async Task<ResponseModel<List<CombinedPOBudgetTreeModel>>> GetBudgetByJobReleaseSortAsync(string jobNumber)
        {
            var jobs = new ResponseModel<List<CombinedPOBudgetTreeModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetBudgetByJobRelesaseSort/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();

                jobs = JsonConvert.DeserializeObject<ResponseModel<List<CombinedPOBudgetTreeModel>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return jobs;
        }
        public async Task<ResponseModel<List<CombinedJCETreeModel>>> GetSendToAccountingAsync()
        {
            var jobs = new ResponseModel<List<CombinedJCETreeModel>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetSendToAccounting/");
                var responseString = await response.Content.ReadAsStringAsync();

                jobs = JsonConvert.DeserializeObject< ResponseModel<List<CombinedJCETreeModel>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return jobs;
        }
       
        public async Task<ResponseModel<List<EstcustoptionDto>>> GetCustomEstimatesAsync()
        {
            var customEstimates = new ResponseModel<List<EstcustoptionDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetCustomEstimates/");
                var responseString = await response.Content.ReadAsStringAsync();

                customEstimates = JsonConvert.DeserializeObject <ResponseModel<List<EstcustoptionDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return customEstimates;
        }
        public async Task<ResponseModel<EstcustoptionDto>> GetCustomEstimateAsync(int custEstId)
        {
            var customEstimate = new ResponseModel<EstcustoptionDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetCustomEstimate/{custEstId}");
                var responseString = await response.Content.ReadAsStringAsync();

                customEstimate = JsonConvert.DeserializeObject<ResponseModel<EstcustoptionDto>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return customEstimate;
        }
        public async Task<ResponseModel<List<EstheaderDto>>> GetCustomEstimateDetailsAsync(int custEstId)
        {
            var customEstimates = new ResponseModel<List<EstheaderDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetCustomEstimateDetails/{custEstId}");
                var responseString = await response.Content.ReadAsStringAsync();

                customEstimates = JsonConvert.DeserializeObject<ResponseModel<List<EstheaderDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return customEstimates;
        }
        public async Task<ResponseModel<List<EstheaderDto>>> GetEstimatesForJobAsync(string jobNumber)
        {
            var customEstimates = new ResponseModel<List<EstheaderDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetEstimatesForJob/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();

                customEstimates = JsonConvert.DeserializeObject<ResponseModel<List<EstheaderDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return customEstimates;
        }
        public async Task<ResponseModel<AttachEstimateModel>> AttachEstimateAsync(AttachEstimateModel estToAttach)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<AttachEstimateModel, ResponseModel<AttachEstimateModel>>(
                            "DownstreamApi", estToAttach,
                             options => {
                                 options.RelativePath = "api/budget/AttachEstimateToOption/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<AttachEstimateModel>() { Value = estToAttach, IsSuccess = false };
        }
        public async Task<ResponseModel<EstcustoptionDto>> DeleteCustomOptionEstimateAsync(EstcustoptionDto estCustOptionToDelete)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<EstcustoptionDto, ResponseModel<EstcustoptionDto>>(
                            "DownstreamApi", estCustOptionToDelete,
                             options => {
                                 options.RelativePath = "api/budget/DeleteCustomEstimate/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<EstcustoptionDto>() { Value = estCustOptionToDelete, IsSuccess = false };
        }
        public async Task<ResponseModel<EstcustoptionDto>> UpdateCustomOptionEstimateAsync(EstcustoptionDto estCustOptionToDelete
            )
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<EstcustoptionDto, ResponseModel<EstcustoptionDto>>(
                            "DownstreamApi", estCustOptionToDelete,
                             options => {
                                 options.RelativePath = "api/budget/UpdateCustomEstimate/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<EstcustoptionDto>() { Value = estCustOptionToDelete, IsSuccess = false };
        }
        public async Task<ResponseModel<EstoptionDto>> AddCustomEstimateAsync(AddCustomEstimateModel addCustomEstimate)
        {
            var customEstimates = new EstoptionDto();
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<AddCustomEstimateModel, ResponseModel<EstoptionDto >>(
                            "DownstreamApi", addCustomEstimate,
                             options => {
                                 options.RelativePath = "api/budget/addcustomestimate/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<EstoptionDto>() { Value = customEstimates, IsSuccess = false };
        }
        public async Task<ResponseModel<EstoptionDto>> AddPendingEstimateAsync(EstheaderDto addCustomEstimate)
        {
            var customEstimates = new EstoptionDto();
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<EstheaderDto, ResponseModel<EstoptionDto>>(
                            "DownstreamApi", addCustomEstimate,
                             options => {
                                 options.RelativePath = "api/budget/addpendingestimate/";;
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<EstoptionDto>() { Value = customEstimates, IsSuccess = false };
        }
        public async Task<ResponseModel<EstoptionDto>> AddEstimateAsync(EstheaderDto addCustomEstimate)
        {
            var customEstimates = new EstoptionDto();
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<EstheaderDto, ResponseModel<EstoptionDto>>(
                            "DownstreamApi", addCustomEstimate,
                             options => {
                                 options.RelativePath = "api/budget/addestimate/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<EstoptionDto>() { Value = customEstimates, IsSuccess = false };
        }

        public async Task<ResponseModel<EstoptionDto>> AddEstimateWithItemsAsync(EstdetailDto addEstimate)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<EstdetailDto, ResponseModel<EstoptionDto>>(
                            "DownstreamApi", addEstimate,
                             options => {
                                 options.RelativePath = "api/budget/addestimatewithitems/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<EstoptionDto>() { IsSuccess = false };
        }
        public async Task<ResponseModel<EstheaderDto>> AddEstheaderAsync(EstheaderDto addEstimate)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<EstheaderDto, ResponseModel<EstheaderDto>>(
                            "DownstreamApi", addEstimate,
                             options => {
                                 options.RelativePath = "api/budget/addestheader/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<EstheaderDto>() { IsSuccess = false };
        }
        public async Task<ResponseModel<EstoptionDto>> AddEstoptionAsync(EstoptionDto addEstOption)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<EstoptionDto, ResponseModel<EstoptionDto>>(
                            "DownstreamApi", addEstOption,
                             options => {
                                 options.RelativePath = "api/budget/addestoption/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<EstoptionDto>() { IsSuccess = false };
        }
        public async Task<ResponseModel<List<EstoptionDto>>> AddEstoptionsAsync(List<EstoptionDto> addEstOption)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<EstoptionDto>, ResponseModel<List<EstoptionDto>>>(
                            "DownstreamApi", addEstOption,
                             options => {
                                 options.RelativePath = "api/budget/addestoptions/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.Error(ex);
#endif
            }
            return new ResponseModel<List<EstoptionDto>>() { IsSuccess = false, Message = "Could not add option. If issue persists please contact BI" };
        }
        public async Task<ResponseModel<EstdetailDto>> AddEstdetaiAsync(EstdetailDto addEstDetail)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<EstdetailDto, ResponseModel<EstdetailDto>>(
                            "DownstreamApi", addEstDetail,
                             options => {
                                 options.RelativePath = "api/budget/addestdetail/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<EstdetailDto>() { IsSuccess = false, Message = "Failed to add item" };
        }
        public async Task<ResponseModel<List<EstdetailDto>>> AddEstdetailsAsync(List<EstdetailDto> addEstDetail)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<EstdetailDto>, ResponseModel<List<EstdetailDto>>>(
                            "DownstreamApi", addEstDetail,
                             options => {
                                 options.RelativePath = "api/budget/addestdetails/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<EstdetailDto>>() { IsSuccess = false, Message = "Failed to add item" };
        }
        public async Task<ResponseModel<List<EstdetailDto>>> AddEstOptionsAndEstdetailsAsync(List<EstdetailDto> addEstDetail)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<EstdetailDto>, ResponseModel<List<EstdetailDto>>>(
                            "DownstreamApi", addEstDetail,
                             options => {
                                 options.RelativePath = "api/budget/AddEstoptionsAndDetails/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<EstdetailDto>>() { IsSuccess = false, Message = "Failed to add item" };
        }
        public async Task<ResponseModel<List<EstoptionDto>>> GetOptionsForHeaderAsync(int estHeaderId)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/getoptionsforbudget/{estHeaderId}");
                var responseString = await response.Content.ReadAsStringAsync();

                var budgets = JsonConvert.DeserializeObject<ResponseModel<List<EstoptionDto>>>(responseString);
                return budgets;     
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<EstoptionDto>>() { IsSuccess = false, Message = "Failed to get options"};
        }
        public async Task<ResponseModel<double?>> GetOptionsTotalCostAsync(int estOptionId)
        {
            ResponseModel<double?> totalCost = null;
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetOptionCostTotal/{estOptionId}");
                var responseString = await response.Content.ReadAsStringAsync();

                totalCost = JsonConvert.DeserializeObject<ResponseModel<double?>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return totalCost;
        }
        public async Task<ResponseModel<List<EstactivityDto>>> GetActivitiesForOptionAsync(int estOptionId)
        {
            var activities = new ResponseModel<List<EstactivityDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetActivitiesForOption/{estOptionId}");
                var responseString = await response.Content.ReadAsStringAsync();

                activities = JsonConvert.DeserializeObject<ResponseModel<List<EstactivityDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return activities;
        }
        public async Task<ResponseModel<List<EstdetailDto>>> GetDetailsForActivitiesAsync(int estOptionId, int activityId)
        {
            var details = new ResponseModel<List<EstdetailDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi",
                      options => options.RelativePath = $"api/budget/GetDetailsForActivities/{estOptionId}/{activityId}");
                var responseString = await response.Content.ReadAsStringAsync();

                details = JsonConvert.DeserializeObject<ResponseModel<List<EstdetailDto>>>(responseString);
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return details;
        }        
        public async Task<ResponseModel<EstjcedetailDto>> DeleteJceDetailAsync(EstjcedetailDto estDetailToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<EstjcedetailDto, ResponseModel<EstjcedetailDto>>(
                            "DownstreamApi", estDetailToUpdate,
                             options => {
                                 options.RelativePath = "api/budget/deletejcedetail/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<EstjcedetailDto>() { Value = estDetailToUpdate, IsSuccess = false };
        }
        public async Task<ResponseModel<List<EstjcedetailDto>>> DeleteJceDetailsAsync(List<EstjcedetailDto> estDetailToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<EstjcedetailDto>, ResponseModel<List<EstjcedetailDto>>>(
                            "DownstreamApi", estDetailToUpdate,
                             options => {
                                 options.RelativePath = "api/budget/deletejcedetails/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<EstjcedetailDto>>() { Value = estDetailToUpdate, IsSuccess = false };
        }
        public async Task<ResponseModel<List<EstjcedetailDto>>> UpdateJceDetailsExportedAsync(List<EstjcedetailDto> estDetailToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<List<EstjcedetailDto>, ResponseModel<List<EstjcedetailDto>>>(
                            "DownstreamApi", estDetailToUpdate,
                             options => {
                                 options.RelativePath = "api/budget/updatejcedetailsexported/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<EstjcedetailDto>>() { Value = estDetailToUpdate, IsSuccess = false };
        }
        public async Task<ResponseModel<EstdetailDto>> UpdateEstDetailAsync(EstdetailDto estDetailToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<EstdetailDto, ResponseModel<EstdetailDto>>(
                            "DownstreamApi", estDetailToUpdate,
                             options => {
                                 options.RelativePath = "api/budget/updateestdetail/";
                             });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif

            }
            return new ResponseModel<EstdetailDto>() { Value = estDetailToUpdate, IsSuccess = false, Message = "failed to update est detail" };
        }
        public async Task<ResponseModel<EstactivityDto>> UpdateEstActivityAsync(EstactivityDto estActivityToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<EstactivityDto, ResponseModel<EstactivityDto>>(
                            "DownstreamApi", estActivityToUpdate,
                             options => {
                                 options.RelativePath = "api/budget/updateestactivity/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<EstactivityDto>() { Value = estActivityToUpdate, IsSuccess = false, Message = "failed to update est activity" };
        }
        public async Task<ResponseModel<EstoptionDto>> UpdateEstOptionAsync(EstoptionDto estOptionToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<EstoptionDto, ResponseModel<EstoptionDto>>(
                            "DownstreamApi", estOptionToUpdate,
                             options => {
                                 options.RelativePath = "api/budget/UpdateEstOption/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<EstoptionDto>() { Value = estOptionToUpdate, IsSuccess = false };
        }
        public async Task<ResponseModel<EstheaderDto>> UpdateEstHeaderAsync(EstheaderDto estHeaderToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<EstheaderDto, ResponseModel<EstheaderDto>>(
                            "DownstreamApi", estHeaderToUpdate,
                             options => {
                                 options.RelativePath = "api/budget/UpdateEstheader/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<EstheaderDto>() { IsSuccess = false, Message = "failed to update est header", Value = estHeaderToUpdate };
        }
        public async Task<ResponseModel<EstdetailDto?>> CopyItemAsync(EstdetailDto itemToCopy)
        {

            try
            {

                var newItemToCopy = new EstdetailDto()
                {
                    EstdetailId = itemToCopy.EstdetailId,//TODO: only the detail id is actually needed. something else is causing bad request error, need to debug
                };
                var response = await _downstreamAPI.PostForUserAsync<EstdetailDto, ResponseModel<EstdetailDto>>("DownstreamApi", newItemToCopy,
                      options =>
                      {
                          options.RelativePath = $"api/budget/CopyItem/";
                      });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return  new ResponseModel<EstdetailDto?>() { IsSuccess = false, Message = "Failed to copy item", Value = null }; ;
        }
        public async Task<ResponseModel<EstdetailDto?>> ReissueItemAsync(EstdetailDto itemToCopy)
        {

            try
            {
                var response = await _downstreamAPI.PostForUserAsync<EstdetailDto, ResponseModel<EstdetailDto>>("DownstreamApi", itemToCopy,
                      options => {
                          options.RelativePath = $"api/budget/ReissueItem/";
                      });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return null;
        }        
        public async Task<ResponseModel<EstdetailDto?>> DeleteItemAsync(CombinedPOBudgetTreeModel itemToDelete)
        {

            try
            {
                var deleteItem = new EstdetailDto() { EstdetailId = itemToDelete.Estdetail.EstdetailId };
                var response = await _downstreamAPI.PutForUserAsync<EstdetailDto, ResponseModel<EstdetailDto>>("DownstreamApi", deleteItem,
                      options => {
                          options.RelativePath = $"api/budget/DeleteItem/";
                      });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<EstdetailDto>() { IsSuccess = false, Message = "Failed to delete item" };
            }
            return null;
        }
        public async Task<ResponseModel<EstheaderDto?>> DeleteHeaderAsync(EstheaderDto itemToDelete)
        {

            try
            {
                var deleteItem = new EstheaderDto() { EstheaderId = itemToDelete.EstheaderId };
                var response = await _downstreamAPI.PutForUserAsync<EstheaderDto, ResponseModel<EstheaderDto>>("DownstreamApi", itemToDelete,
                      options => {
                          options.RelativePath = $"api/budget/DeleteHeader/";
                      });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<EstheaderDto?>() { IsSuccess = false, Message = "Failed to delete header" };
            }
            return null;
        }
        public async Task<ResponseModel<EstoptionDto?>> DeleteOptionAsync(EstoptionDto itemToDelete)
        {

            try
            {
                var response = await _downstreamAPI.PutForUserAsync<EstoptionDto, ResponseModel<EstoptionDto>>("DownstreamApi", itemToDelete,
                      options => {
                          options.RelativePath = $"api/budget/DeleteOption/";
                      });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<EstoptionDto?>() { IsSuccess = false, Message = "Failed to delete option" };
            }
            return null;
        }
        public async Task<ResponseModel<CombinedPOBudgetTreeModel?>> DeleteActivityAsync(CombinedPOBudgetTreeModel itemToDelete)
        {

            try
            {
                var deleteItem = new CombinedPOBudgetTreeModel()
                {
                  //  Estoption = new EstoptionDto() { EstoptionId = itemToDelete.Estoption.EstoptionId },
                    Estactivity = new EstactivityDto() { EstactivityId = itemToDelete.Estactivity.EstactivityId }
                };
                var response = await _downstreamAPI.PutForUserAsync<CombinedPOBudgetTreeModel, ResponseModel<CombinedPOBudgetTreeModel>>("DownstreamApi", deleteItem,
                      options => {
                          options.RelativePath = $"api/budget/DeleteActivity/";
                      });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return null;
        }
        public async Task<ResponseModel<CombinedPOBudgetTreeModel?>> DeleteActivityOptionSortAsync(CombinedPOBudgetTreeModel itemToDelete)
        {

            try
            {
                var deleteItem = new CombinedPOBudgetTreeModel()
                {
                    Estoption = new EstoptionDto() { EstoptionId = itemToDelete.Estoption.EstoptionId },
                    Estactivity = new EstactivityDto() { EstactivityId = itemToDelete.Estactivity.EstactivityId }
                };
                var response = await _downstreamAPI.PutForUserAsync<CombinedPOBudgetTreeModel, ResponseModel<CombinedPOBudgetTreeModel>>("DownstreamApi", deleteItem,
                      options => {
                          options.RelativePath = $"api/budget/DeleteActivityOptionSort/";
                      });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return null;
        }
        public async Task<ResponseModel<EstdetailDto?>> CancelBudgetItemAsync(EstdetailDto itemToCancel)
        {

            try
            {
                itemToCancel.Podetail = null;//otherwise validation issues, need to fix this properly
                var response = await _downstreamAPI.PutForUserAsync<EstdetailDto, ResponseModel<EstdetailDto>>("DownstreamApi", itemToCancel,
                      options => {
                          options.RelativePath = $"api/budget/CancelBudgetItem/";
                      });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<EstdetailDto?> { IsSuccess = false, Message = "Failed to cancel."};
        }
        public async Task<ResponseModel<EstdetailDto?>> IssueBudgetItemAsync(EstdetailDto itemToIssue)
        {

            try
            {
                var issueBudgetItem = new EstdetailDto() { EstdetailId = itemToIssue.EstdetailId };//All that is needed is actually the detail id,
               //PO header is not needed here, but validation requires Poheader to have Description, 
                var response = await _downstreamAPI.PutForUserAsync<EstdetailDto, ResponseModel<EstdetailDto>>("DownstreamApi", issueBudgetItem,
                      options => {
                          options.RelativePath = $"api/budget/IssueBudgetItem/";
                      });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<EstdetailDto?> { IsSuccess = false, Message = "Failed to issue budget item", Value = itemToIssue };
            }
            return null;
        }
        public async Task<ResponseModel<List<CombinedPOBudgetTreeModel>?>> IssueBudgetsAsync(List<CombinedPOBudgetTreeModel> itemsToIssue)
        {

            try
            {
                var budgetsToIssue = itemsToIssue.Select(x => new CombinedPOBudgetTreeModel()
                {
                    Estheader = x.Estheader,
                    Estactivity = x.Estactivity,
                    Estoption = x.Estoption,
                    Estdetail = x.Estdetail,
                }).ToList();//temporary fix to get around validation in poheader reuqired field description
                foreach (var budget in budgetsToIssue.Where(x => x.Estdetail != null))
                {
                    // budget.Estdetail.Podetail.Poheader = null;
                    budget.Estdetail.Podetail = null;//po not needed to issue budget

                    //budget.Estdetail.Podetail.Poheader.Podescription = !string.IsNullOrWhiteSpace(budget.Estdetail.Podetail.Poheader.Podescription) ? budget.Estdetail.Podetail.Poheader.Podescription : "test";//TODO: fix this
                }
                var response = await _downstreamAPI.PutForUserAsync<List<CombinedPOBudgetTreeModel>, ResponseModel<List<CombinedPOBudgetTreeModel>>>("DownstreamApi", budgetsToIssue,
                      options => {
                          options.RelativePath = $"api/budget/IssueBudgets/";
                      });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
                return new ResponseModel<List<CombinedPOBudgetTreeModel>?>() { IsSuccess = false, Value = itemsToIssue, Message = "Failed to issue budgets"};
            }
            return null;
        }
        public async Task<ResponseModel<RefreshSupplierAndCostsModel>> RefreshCostsAsync(RefreshSupplierAndCostsModel costsModel)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<RefreshSupplierAndCostsModel, ResponseModel<RefreshSupplierAndCostsModel>>("DownstreamApi", costsModel,
                      options => {
                          options.RelativePath = $"api/budget/RefreshCosts/";
                      });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<RefreshSupplierAndCostsModel>() { Value = costsModel, IsSuccess = false };
        }
        public async Task<ResponseModel<RefreshSupplierAndCostsModel>> RefreshCostsPOAsync(RefreshSupplierAndCostsModel costsModel)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<RefreshSupplierAndCostsModel, ResponseModel<RefreshSupplierAndCostsModel>>("DownstreamApi", costsModel,
                      options => {
                          options.RelativePath = $"api/budget/RefreshCostsPO/";
                      });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<RefreshSupplierAndCostsModel>() { Value = costsModel, IsSuccess = false };
        }
        public async Task<ResponseModel<EstjcedetailDto>> SendBudgetToBCAsync(EstjcedetailDto estHeader)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<EstjcedetailDto, ResponseModel<EstjcedetailDto>>(
                            "DownstreamApi", estHeader,
                             options => {
                                 options.RelativePath = "api/budget/SendBudgetToBC/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<EstjcedetailDto>() { IsSuccess = false, Value = new EstjcedetailDto(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<byte[]>> SendBudgetToNAVAsync(EstjcedetailDto estHeader)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<EstjcedetailDto, ResponseModel<byte[]>>(
                            "DownstreamApi", estHeader,
                             options => {
                                 options.RelativePath = "api/budget/SendBudgetToNAV/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<byte[]>() { IsSuccess = false,  Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<EstjcedetailDto>>> SendBudgetsToBCAsync(List<EstjcedetailDto> estHeaders)
        {
            try
            {

                //Try sending in batches of 500 because else request times out
                int count = 0; 
                var tasksToSend = new List<Task<ResponseModel<List<EstjcedetailDto>>>>();
                while (count < estHeaders.Count)
                {
                    var budgetsToSend = estHeaders.Skip(count).Take(500).ToList();
                    var request = _downstreamAPI.PostForUserAsync<List<EstjcedetailDto>, ResponseModel<List<EstjcedetailDto>>>(
                            "DownstreamApi", budgetsToSend,
                             options => {
                                 options.RelativePath = "api/budget/SendBudgetsToBC/";
                             });
                    tasksToSend.Add(request);
                    //var response = await _downstreamAPI.PostForUserAsync<List<EstjcedetailDto>, ResponseModel<List<EstjcedetailDto>>>(
                    //        "DownstreamApi", budgetsToSend,
                    //         options => {
                    //             options.RelativePath = "api/budget/SendBudgetsToBC/";
                    //         });
                    count += 500;
                }
                await Task.WhenAll(tasksToSend);
                string message = tasksToSend.Any(x => x.Result.IsSuccess == false) ? "Some budgets failed to send to BC. See logs for details." : "Sent budgets to BC. See logs for details.";

                return new ResponseModel<List<EstjcedetailDto>>() { Value = estHeaders, IsSuccess = true, Message = message };
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<EstjcedetailDto>>() { IsSuccess = false, Value = new List<EstjcedetailDto>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<byte[]>> SendBudgetsToNAVAsync(List<EstjcedetailDto> estHeaders)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<List<EstjcedetailDto>, ResponseModel<byte[]>>(
                            "DownstreamApi", estHeaders,
                             options => {
                                 options.RelativePath = "api/budget/SendBudgetsToNAV/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<byte[]>() { IsSuccess = false, Value = null, Message = "Something went wrong" };
        }

        public async Task<ResponseModel<TileDto>> GetEstimateBudget()
        {
            var budget = new ResponseModel<TileDto>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/budget/getestimatebudget/");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    budget = JsonConvert.DeserializeObject<ResponseModel<TileDto>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return budget;
        }

        public async Task<ResponseModel<TileDto>> GetIssuedBudget()
        {
            var budget = new ResponseModel<TileDto>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/budget/getissuedbudget/");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    budget = JsonConvert.DeserializeObject<ResponseModel<TileDto>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return budget;
        }

        public async Task<ResponseModel<TileDto>> GetPricingAssumptionCost()
        {
            var budget = new ResponseModel<TileDto>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/budget/getpricingassumptioncost/");

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    budget = JsonConvert.DeserializeObject<ResponseModel<TileDto>>(responseString);
                }
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return budget;
        }
    }
}
