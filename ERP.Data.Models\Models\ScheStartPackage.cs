﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ScheStartPackage
{
    public int PackageId { get; set; }

    public string PackageName { get; set; } = null!;

    public int SubdivisionId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual ICollection<SchePackageItem> SchePackageItems { get; set; } = new List<SchePackageItem>();
}
