﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Spec
{
    public int SpecId { get; set; }

    public string JobId { get; set; } = null!;

    public int SelectedFloorplanId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? DesignCenterCompletedDate { get; set; }

    public virtual Job Job { get; set; } = null!;

    public virtual PhasePlan SelectedFloorplan { get; set; } = null!;

    public virtual ICollection<TbBuiltOption> BuiltOptions { get; set; } = new List<TbBuiltOption>();
}
