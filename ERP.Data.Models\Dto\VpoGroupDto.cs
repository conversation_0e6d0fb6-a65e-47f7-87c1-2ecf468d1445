﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class VpoGroupDto : IMapFrom<VpoGroup>
    {
        public int VpoGroupId { get; set; }

        public string? GroupDesc { get; set; }

        public DateTime CreatedDateTime { get; set; }

        public string? CreatedBy { get; set; }

        public DateTime? UpdatedDateTime { get; set; }

        public string? UpdatedBy { get; set; }

        public bool? IsActive { get; set; }

        public List<VpoApprovalSeqDto> VpoApprovalSeqs { get; set; }

        public string? SuccessMessage { get; set; }

        public string? ErrorMessage { get; set; }
    }
}
