﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{

    public class BCPoDetail
    {
        //public string id { get; set; }
       // public string? documentId { get; set; }
        //public int sequence { get; set; }
      //  public string itemId { get; set; }
       // public string accountId { get; set; }
        public string lineType { get; set; }
        public string lineObjectNumber { get; set; }
        public string description { get; set; }
        public string? memo { get; set; }
        //public string? memo2 { get; set; }

        // public string unitOfMeasureId { get; set; }
        public string unitOfMeasureCode { get; set; } //unit, eg "EA", "SF"
        public double? quantity { get; set; }
        public double? directUnitCost { get; set; }

        public string? ProjectNumber { get; set; }
        public string? ProjectTaskNumber { get; set; }
        public string? shortcutDimension1Code { get; set; }
        public string? shortcutDimension2Code {  get; set; }
        //public int discountAmount { get; set; }
        //public int discountPercent { get; set; }
        public string? taxCode { get; set; }
        //public string expectedReceiptDate { get; set; }
        //public int invoiceQuantity { get; set; }
        //public int receiveQuantity { get; set; }
        //public string itemVariantId { get; set; }
        //public string locationId { get; set; }
    }
    public class BCCreditMemoDetail
    {
        public string lineType { get; set; }
        public string lineObjectNumber { get; set; }
        public string description { get; set; }
        public string? memo { get; set; }
      //  public string? memo2 { get; set; }
        public string? unitOfMeasureCode { get; set; } //unit, eg "EA", "SF"
        public double? quantity { get; set; }
        public double? unitCost { get; set; }

        public string? ProjectNumber { get; set; }
        public string? ProjectTaskNumber { get; set; }
        public string? shortcutDimension1Code { get; set; }
        public string? shortcutDimension2Code { get; set; }
        public string? taxCode { get; set; }

    }

    public class ResponseBCPoDetail
    {
        public string odatacontext { get; set; }
        public string odataetag { get; set; }
        public string id { get; set; }
        public string documentId { get; set; }
        public int sequence { get; set; }
        public string itemId { get; set; }
        public string accountId { get; set; }
        public string lineType { get; set; }
        public string lineObjectNumber { get; set; }
        public string description { get; set; }
        public string description2 { get; set; }
        public string unitOfMeasureId { get; set; }
        public string unitOfMeasureCode { get; set; }
        public int quantity { get; set; }
        public float directUnitCost { get; set; }
        public int discountAmount { get; set; }
        public int discountPercent { get; set; }
        public bool discountAppliedBeforeTax { get; set; }
        public float amountExcludingTax { get; set; }
        public string taxCode { get; set; }
        public int taxPercent { get; set; }
        public int totalTaxAmount { get; set; }
        public float amountIncludingTax { get; set; }
        public int invoiceDiscountAllocation { get; set; }
        public float netAmount { get; set; }
        public int netTaxAmount { get; set; }
        public float netAmountIncludingTax { get; set; }
        public string expectedReceiptDate { get; set; }
        public int receivedQuantity { get; set; }
        public int invoicedQuantity { get; set; }
        public int invoiceQuantity { get; set; }
        public int receiveQuantity { get; set; }
        public string itemVariantId { get; set; }
        public string locationId { get; set; }
    }

}
