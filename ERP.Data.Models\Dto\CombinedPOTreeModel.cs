﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public record CombinedPOTreeModel
    {
        //TODO: make an interface instead
        public Guid Id { get; set; }
        public Guid? ParentId { get; set; }
        public bool HasChildren { get; set; }
        public string? JobNumber { get; set; }
        public string? PoNumber { get; set; }
        public string? ReleaseCode { get; set; }
        public string? Activity { get; set; }
        public double? ActivityTotal { get; set; }
        public bool? IsActive { get; set; }
        public bool IsIssued { get; set; }
        public bool IssueEnabled { get; set; } = true;
        public bool Indeterminate { get; set; } = false;
        public List<CombinedPOTreeModel>? Children { get; set; }

        public PoheaderDto? Poheader { get; set; }       
        public PodetailoptionDto? Podetailoption { get; set; }
        public PodetailDto? Podetail { get; set; }
        public EstdetailDto? Estdetail { get; set; }
        public EstactivityDto? Estactivity { get; set; }
        public PoapprovalDto? Poapproval { get; set; }
               
    }
}
