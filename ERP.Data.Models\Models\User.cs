﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class User
{
    public string UserId { get; set; } = null!;

    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    public string? IsActive { get; set; }

    public DateTime? DateInactivated { get; set; }

    public string? UserInactivated { get; set; }

    public string? Notes { get; set; }

    public DateTime? DateStamp { get; set; }

    public string? UserStamp { get; set; }

    public string? EmailAddress { get; set; }

    public string? Supervisor { get; set; }

    public string? Administrator { get; set; }

    public string? IsProtected { get; set; }

    public string? Title { get; set; }

    public string? Extension { get; set; }

    public string? WorkPhone { get; set; }

    public string? MobilePhone { get; set; }

    public int? CorrigoUserId { get; set; }

    public double? ApprovalLimit { get; set; }

    public string? Accesstobmtsupport { get; set; }

    public string? MobileSchedUser { get; set; }

    public string? SuptPortalUser { get; set; }

    public byte[]? Picture { get; set; }

    public string? VpoPortalUser { get; set; }

    public DateTime Createddatetime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive1 { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public bool? PriceChangeNotification { get; set; }

    public virtual ICollection<JobContact> JobContacts { get; set; } = new List<JobContact>();

    public virtual ICollection<SubdivisionContact> SubdivisionContacts { get; set; } = new List<SubdivisionContact>();
}
