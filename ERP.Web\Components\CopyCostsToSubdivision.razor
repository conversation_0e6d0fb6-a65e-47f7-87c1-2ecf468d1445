﻿@using ERP.Data.Models.Dto;

@inject CostService CostService
@inject SubdivisionService SubdivisionService


<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Copy Cost from Subdivision to Another Subdivision
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@CopyCost" OnValidSubmit="@HandleValidAddSubmit">
            <div class="card">
                <div class="card-body">
                    <div class="pricingTable1 text center">
                        <h7 class="title1 pt-3 pb-2 m-0">Select Subdivision to Copy From</h7>
                        <DataAnnotationsValidator />
                        <ValidationSummary />
                        <TelerikDropDownList Data="@AllSubdivisions"
                                             @bind-Value="@CopyCost.FromSubdivisionId"
                                             DefaultText="Select A Subdivision"
                                             TextField ="SubdivisionName"
                                             ValueField="SubdivisionId"
                                             ScrollMode="@DropDownScrollMode.Virtual"
                                             PageSize="10"
                                             Filterable="true"
                                             ItemHeight="35"
                                             FilterOperator="@StringFilterOperator.Contains"
                                             Class="dropdownControl">
                            <DropDownListSettings>
                                <DropDownListPopupSettings Height="350px" Class="popupPlansContainer"></DropDownListPopupSettings>
                            </DropDownListSettings>
                        </TelerikDropDownList>
                        <br />
                        
                        <br />
                        <label class="form-label">Subdivision to Add To</label>
                        <TelerikDropDownList Data="@AllSubdivisions"
                                             @bind-Value="@CopyCost.ToSubdivisionId"
                                             DefaultText="Select A Subdivision"
                                             TextField ="SubdivisionName"
                                             ValueField="SubdivisionId"
                                             ScrollMode="@DropDownScrollMode.Virtual"
                                             PageSize="10"
                                             Filterable="true"
                                             ItemHeight="35"
                                             FilterOperator="@StringFilterOperator.Contains"
                                             Class="dropdownControl">
                            <DropDownListSettings>
                                <DropDownListPopupSettings Height="350px" Class="popupPlansContainer"></DropDownListPopupSettings>
                            </DropDownListSettings>
                        </TelerikDropDownList>
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-primary">Add</button>
            <button type="button" @onclick="CancelCopyCost" class="btn btn-secondary">Cancel</button>
            <div style=@ShowLoading>Submitting. Please wait...</div>
            <div style=@ShowError>Something went wrong.</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public CopyCostToSubdivisionModel CopyCost { get; set; } = new CopyCostToSubdivisionModel();
    public string ShowNewPhaseInput { get; set; } = "display:none";
    public string ShowLoading{ get; set; } = "display:none";
    public string ShowError { get; set; } = "display:none";
    public List<SubdivisionDto>? AllSubdivisions;

    [Parameter]
    public EventCallback<ResponseModel<List<CostDto>>> HandleAddSubmit { get; set; }

    public async Task Show()
    {
        IsModalVisible = true;
        var data = await SubdivisionService.GetSubdivisionsAsync();
        AllSubdivisions = data.Value;
        StateHasChanged();
    }  
    

    private async void HandleValidAddSubmit()
    {
        ShowLoading = "";
        
        // var response = await CostService.CopyCostsToSubdivisionAsync(CopyCost);
        // //TODO: show success/error
        // if (response.IsSuccess)
        // {
        //     ShowLoading = "display:none";
        // }
        // else
        // {
        //     ShowLoading = "display:none";
        //     ShowError = ""; //this won't actually show, since invoking the handle add hides the modal, so that function needs to display the error
        // }
       // await HandleAddSubmit.InvokeAsync(response);        
    }
    async void CancelCopyCost()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;        
    }
}
