﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Hoa
{
    public int HoaId { get; set; }

    public string AssociationName { get; set; } = null!;

    public string? Category { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual ICollection<HoaAssessment> HoaAssessments { get; set; } = new List<HoaAssessment>();
}
