﻿
@inject ScheduleService ScheduleService


<TelerikWindow Modal="true"
@bind-Visible="@IsModalVisible"
CloseOnOverlayClick="true">
    <WindowTitle>
        @{
            if (SaveAs)
            {
                <h4 class="page-title">Save Template: @SaveAsTemplate.TemplateName As</h4>
            }
            else
            {
                <h4 class="page-title">Create a New Template</h4>
            }
        }
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@TemplateToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />
            <label class="form-label">Template Name </label>
            <TelerikTextBox @bind-Value="@TemplateToAdd.TemplateName"></TelerikTextBox>
            <br />
            <label class="form-label">Template Description </label>
            <TelerikTextBox @bind-Value="@TemplateToAdd.Description"></TelerikTextBox>
            <br />
            @{
                if (!SaveAs)
                {
                    <label class="form-label">Copy Template From:  </label>

                    <TelerikDropDownList Data="@AvailableTemplates"
                    @bind-Value="@SelectedTemplateToCopy"
                    TextField="TemplateName"
                    ValueField="TemplateId"
                    DefaultText="Select A Template to Copy"
                    ScrollMode="@DropDownScrollMode.Virtual"
                    PageSize="10"
                    Filterable="true"
                    ItemHeight="35"
                    FilterOperator="@StringFilterOperator.Contains">

                    </TelerikDropDownList>
                    <br />
                }
            }
            <div class="customSeparator">
                <button type="submit" class="btn btn-primary">Add</button>
                <button type="button" @onclick="CancelAddWorksheet" class="btn btn-secondary">Cancel</button>
                <div style=@ShowLoading>Submitting. Please wait...</div>
                <div style=@ShowError>Something went wrong.</div>
            </div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public TemplateDto TemplateToAdd { get; set; } = new TemplateDto();
    public string ShowLoading{ get; set; } = "display:none";
    public string ShowError { get; set; } = "display:none";
    public List<TemplateDto>? AvailableTemplates { get; set; }
    public int? SelectedTemplateToCopy { get; set; }
    [Parameter] public TemplateDto? SaveAsTemplate { get; set; }
    [Parameter] public EventCallback<ResponseModel<TemplateDto>> HandleAddSubmit { get; set; }
    [Parameter] public bool SaveAs { get; set; }

    protected override async Task OnInitializedAsync()
    {
        AvailableTemplates = (await ScheduleService.GetTemplatesAsync()).Value;
    }
    public void Show()
    {
        IsModalVisible = true;
    }      

    private async void HandleValidAddSubmit()
    {
        ShowLoading = "";
        var response = new ResponseModel<TemplateDto>();
        if(SaveAs || SelectedTemplateToCopy != null)
        {
            TemplateToAdd.TemplateId = SaveAs ? (int)SaveAsTemplate.TemplateId : (int)SelectedTemplateToCopy;
            response = await ScheduleService.CopyTemplateAsync(TemplateToAdd);
        }
        else
        {
            response = await ScheduleService.AddTemplateAsync(TemplateToAdd);
        }       
        ShowLoading = "display:none";
        await HandleAddSubmit.InvokeAsync(response);
        SelectedTemplateToCopy = null;
        TemplateToAdd = new TemplateDto();
    }
    async void CancelAddWorksheet()
    {
        SelectedTemplateToCopy = null;
        TemplateToAdd = new TemplateDto();
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        SelectedTemplateToCopy = null;
        TemplateToAdd = new TemplateDto();
        IsModalVisible = false;
    }
}
