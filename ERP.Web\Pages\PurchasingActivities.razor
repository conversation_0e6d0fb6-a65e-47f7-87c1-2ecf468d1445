﻿@page "/purchasingactivities"
@using ERP.Data.Models.Dto;
@using Telerik.DataSource.Extensions
@inject PoService PoService
@inject SubdivisionService SubdivisionService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ReadOnly, Accounting")]
<PageTitle>Purchasing Activities</PageTitle>

<ErrorBoundary>
    <ChildContent>        
        <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Purchasing Activities</h7>
                </div>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item active">Purchasing Activities</li>
        </ol>

        <TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

        <TelerikTabStrip>
            <TabStripTab Title="View All Activities">
                @if (AllPactivites == null)
                {
                    <p><em>Loading...</em></p>
                    <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />
                }
                else
                {
                    <TelerikGrid EditMode="@GridEditMode.Popup"
                                 Pageable="true"
                                 PageSize="20"
                                 @ref="@GridRef"
                                 OnRead="@ReadItems"
                                 TItem="@PactivityDto"
                                 OnDelete="DeleteActivity"
                                 ConfirmDelete="true">
                        <GridToolBarTemplate>
                            @if(AllowEdit){
                                <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Activity</GridCommandButton>
                            }
                            <GridSearchBox DebounceDelay="200"></GridSearchBox>
                            <GridCommandButton Command="ExcelExport" Icon="@FontIcon.FileExcel" Class="k-button-add">Export to Excel</GridCommandButton>
                        </GridToolBarTemplate>
                        <GridSettings>
                            <GridPopupEditSettings Width="550px" MaxHeight="95vh" MaxWidth="95vw" Title="Add Activity"></GridPopupEditSettings>
                            <GridPopupEditFormSettings Context="PActivityContext">
                                <FormTemplate>
                                    @{
                                        EditPActivity = PActivityContext.Item as PactivityDto;

                                        <TelerikForm Model="@EditPActivity"
                                                     ColumnSpacing="10px"
                                                     Columns="1"
                                                     ButtonsLayout="@FormButtonsLayout.Stretch"
                                                     OnValidSubmit="@OnValidSubmit">
                                            <FormValidation>
                                                <DataAnnotationsValidator />
                                            </FormValidation>
                                            <FormItems>
                                                <FormItem Field="Activity" LabelText="Activity" Enabled="true"></FormItem>
                                                <FormItem Field="Releasecode" LabelText="Release Code" Enabled="true"></FormItem>
                                                <FormItem>
                                                    <Template>
                                                        <label for="Schedule Activity">Schedule Activity</label>
                                                        <TelerikDropDownList Data="@AllSActivities"
                                                                             DefaultText="Select Schedule Activity"
                                                                             TextField="ActivityName" ValueField="SactivityId"
                                                                             @bind-Value="@EditPActivity.SactivityId"></TelerikDropDownList>
                                                    </Template>
                                                </FormItem>
                                                <FormItem>
                                                    <Template>
                                                        <label for="Trade">Trade</label>
                                                        <TelerikDropDownList Data="@AllTrades"
                                                                             DefaultText="Select Trades"
                                                                             TextField="TradeName" ValueField="TradeId"
                                                                             @bind-Value="@EditPActivity.TradeId"></TelerikDropDownList>
                                                    </Template>
                                                </FormItem>
                                                <FormItem>
                                                    <Template>
                                                        <label for="JCC">Cost Code</label>
                                                        <TelerikDropDownList Data="@AllJccostcodes"
                                                                             DefaultText="Select Cost Code"
                                                                             TextField="DropdownDescription" ValueField="JccostcodeId"
                                                                             @bind-Value="@EditPActivity.JccostcodeId"></TelerikDropDownList>
                                                    </Template>
                                                </FormItem>
                                                <FormItem>
                                                    <Template>
                                                        <label for="Category">Category</label>
                                                        <TelerikDropDownList Data="@AllJccategories"
                                                                             DefaultText="Select Category"
                                                                             TextField="DropdownDescription" ValueField="JccategoryId"
                                                                             @bind-Value="@EditPActivity.JccategoryId"></TelerikDropDownList>
                                                    </Template>
                                                </FormItem>
                                                <FormItem>
                                                    <Template>
                                                        <label for="Master Item">Item Number</label>
                                                        <TelerikDropDownList Data="@DefaultMasterItems"
                                                                             DefaultText="Select Item"
                                                                             TextField="ItemNumber" ValueField="MasterItemId"
                                                                             @bind-Value="@EditPActivity.MasterItemId"></TelerikDropDownList>
                                                    </Template>
                                                </FormItem>
                                            </FormItems>   
                                        </TelerikForm>
                                    }
                                </FormTemplate>
                            </GridPopupEditFormSettings>
                        </GridSettings>
                        <GridColumns>
                            <GridColumn Field="Activity" Title="Activity" Editable="true" Groupable="false" />
                            <GridColumn Field="ActivityName" Title="Schedule Activity" Editable="true" Groupable="false" />
                            <GridColumn Field="TradeName" Title="Trade" Editable="true" Groupable="false" />
                            <GridColumn Field="CostCode" Title="Cost Code" Editable="true" Groupable="false" />
                            <GridColumn Field="ItemNumber" Title="Item Number" Editable="true" Groupable="false" />
                            <GridCommandColumn>
                                @if(AllowEdit){
                                    <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                                    <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                    <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                    <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                                }
                            </GridCommandColumn>
                        </GridColumns>
                        <GridExport>
                             <GridExcelExport FileName="PurchasingActivities" OnBeforeExport="@OnExcelBeforeExport" AllPages="true" />
                         </GridExport>
                    </TelerikGrid>
                }
            </TabStripTab>
            <TabStripTab Title="View Activities By Subdivision">
                @if(AllSubdivisions != null)
                {
                    <label style="margin-right:8px">Subdivision</label>
                    <TelerikDropDownList @bind-Value="@SelectedSubdivision"
                                         Data="@AllSubdivisions"
                                         ScrollMode="@DropDownScrollMode.Virtual"
                                         ItemHeight="40"
                                         PageSize="75"
                                         OnChange="@SubdivisionSelectedHandler"
                                         TextField="SubdivisionName"
                                         ValueField="SubdivisionId"
                                         DefaultText="Select Subdivision"
                                         Filterable="true"
                                         FilterOperator="StringFilterOperator.Contains"
                                         Width="400px">
                    </TelerikDropDownList>
                }
                else
                {
                    <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner"  />
                }
                <br /><br />
                @if (PactivitiesGridData == null)
                {
                    @*   <p><em>Loading...</em></p> *@
                }
                else
                {
                    <TelerikGrid Data=@PactivitiesGridData
                                 ConfirmDelete="true"
                                 ScrollMode="@GridScrollMode.Virtual"
                                 FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                 Height="1000px" RowHeight="60" PageSize="20"
                                 Sortable="true"
                                 Resizable="true"
                    @bind-SelectedItems="SelectedRows"
                                 SelectionMode="@GridSelectionMode.Multiple"
                                 Width="100%"
                    @ref="@GridRef">
                        <GridSettings>
                            <GridPopupEditSettings Width="800px"></GridPopupEditSettings>
                        </GridSettings>
                        <GridColumns>
                            <GridCheckboxColumn Width="40px" />
                            <GridColumn Field="Activity" Title="Activity" Editable="false" Width="200px" />
                            <GridColumn Field="Poindex" Title="PO Index" Width="50px" />
                            <GridColumn Field="Releasecode" Title="Release Code" Width="50px" />
                            <GridColumn Field="Trade.TradeName" Title="Trade" Width="200px">
                                <EditorTemplate>
                                    @{
                                        ItemToEdit = context as PactivityDto;
                                        <TelerikDropDownList @bind-Value="@ItemToEdit.TradeId"
                                                             Data="@AllTrades"
                                                             ScrollMode="@DropDownScrollMode.Virtual"
                                                             ItemHeight="40"
                                                             PageSize="20"
                                                             TextField="TradeName"
                                                             ValueField="TradeId"
                                                             DefaultText="Select Trade"
                                                             Filterable="true"
                                                             FilterOperator="StringFilterOperator.Contains"
                                                             Width="100%">
                                        </TelerikDropDownList>
                                    }
                                </EditorTemplate>
                            </GridColumn>
                            <GridColumn Field="Sactivity.ActivityName" Title="Schedule Activity" Width="200px">
                                <EditorTemplate>
                                    @{
                                        ItemToEdit = context as PactivityDto;
                                        <TelerikDropDownList @bind-Value="@ItemToEdit.SactivityId"
                                                             Data="@AllSActivities"
                                                             ScrollMode="@DropDownScrollMode.Virtual"
                                                             ItemHeight="40"
                                                             PageSize="20"
                                                             TextField="ActivityName"
                                                             ValueField="SactivityId"
                                                             DefaultText="Select Sactivity"
                                                             Filterable="true"
                                                             FilterOperator="StringFilterOperator.Contains"
                                                             Width="100%">
                                        </TelerikDropDownList>
                                    }
                                </EditorTemplate>
                            </GridColumn>
                            <GridColumn Field="Jccategory.Category" Title="Default Job Cost Category" Width="100px">
                                <EditorTemplate>
                                    @{
                                        ItemToEdit = context as PactivityDto;
                                        <TelerikDropDownList @bind-Value="@ItemToEdit.JccategoryId"
                                                             Data="@AllJccategories"
                                                             ScrollMode="@DropDownScrollMode.Virtual"
                                                             ItemHeight="40"
                                                             PageSize="20"
                                                             TextField="DropdownDescription"
                                                             ValueField="JccategoryId"
                                                             DefaultText="Select Category"
                                                             Filterable="true"
                                                             FilterOperator="StringFilterOperator.Contains"
                                                             Width="300px">
                                        </TelerikDropDownList>
                                    }
                                </EditorTemplate>
                            </GridColumn>
                            <GridColumn Field="Jccostcode.CostCode" Title="Default Job Cost Code" Width="100px">
                                <EditorTemplate>
                                    @{
                                        ItemToEdit = context as PactivityDto;
                                        <TelerikDropDownList @bind-Value="@ItemToEdit.JccostcodeId"
                                                             Data="@AllJccostcodes"
                                                             ScrollMode="@DropDownScrollMode.Virtual"
                                                             ItemHeight="40"
                                                             PageSize="20"
                                                             TextField="DropdownDescription"
                                                             ValueField="JccostcodeId"
                                                             DefaultText="Select Cost Code"
                                                             Filterable="true"
                                                             FilterOperator="StringFilterOperator.Contains"
                                                             Width="300px">
                                        </TelerikDropDownList>
                                    }
                                </EditorTemplate>
                            </GridColumn>
                            <GridColumn Field="MasterItem.ItemDesc" Title="Default Item" Width="200px">
                                <EditorTemplate>
                                    @{
                                        ItemToEdit = context as PactivityDto;
                                        <TelerikDropDownList @bind-Value="@ItemToEdit.MasterItemId"
                                                             Data="@DefaultMasterItems.Where(x => x.BomClassId == ItemToEdit.BomClassId).ToList()"
                                                             ScrollMode="@DropDownScrollMode.Virtual"
                                                             ItemHeight="40"
                                                             PageSize="20"
                                                             TextField="ItemDesc"
                                                             ValueField="MasterItemId"
                                                             DefaultText="Select Item"
                                                             Filterable="true"
                                                             FilterOperator="StringFilterOperator.Contains"
                                                             Width="100%">
                                        </TelerikDropDownList>
                                    }
                                </EditorTemplate>
                            </GridColumn>
                            <GridColumn Field="Supplier.SubName" Title="Supplier" Width="200px">
                                <EditorTemplate>
                                    @{
                                        ItemToEdit = context as PactivityDto;
                                        <TelerikDropDownList @bind-Value="@ItemToEdit.SupplierNumber"
                                                             Data="@AllSuppliers"
                                                             ScrollMode="@DropDownScrollMode.Virtual"
                                                             ItemHeight="40"
                                                             PageSize="20"
                                                             TextField="SubName"
                                                             ValueField="SubNumber"
                                                             DefaultText="Select Supplier"
                                                             Filterable="true"
                                                             FilterOperator="StringFilterOperator.Contains"
                                                             Width="100%">
                                        </TelerikDropDownList>
                                    }
                                </EditorTemplate>
                            </GridColumn>
                        </GridColumns>
                        <GridToolBarTemplate>
                            @if(AllowEdit){
                                <GridCommandButton OnClick="@AssignSuppliersSubdivisionGridAsync" Command="AssignSuppliersCheckedRows" Icon="@FontIcon.Plus" Class="k-button-add">Assign Suppliers</GridCommandButton>
                            }
                            <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>
                            <GridCommandButton Command="ExcelExport" Icon="@FontIcon.FileExcel" Class="k-button-add">Export to Excel</GridCommandButton>
                        </GridToolBarTemplate>
                        <GridExport>
                            <GridExcelExport FileName="PurchasingActivitesBySubdivision" OnBeforeExport="@OnSubdivisionExcelBeforeExport" AllPages="true" />
                        </GridExport>
                        <NoDataTemplate>
                            <p>@Message</p>
                        </NoDataTemplate>
                    </TelerikGrid>
                }
            </TabStripTab>
            <TabStripTab Title="View Subdivision Suppliers By Activity">
                <label style="margin-right:8px">Activity</label>
                <TelerikDropDownList @bind-Value="@SelectedActivity"
                                     Data="@AllPactivites"
                                     ScrollMode="@DropDownScrollMode.Virtual"
                                     ItemHeight="40"
                                     PageSize="75"                                     
                                     OnChange="@ActivitySelectedHandler"
                                     TextField="Activity"
                                     ValueField="PactivityId"
                                     DefaultText="Select Activity"
                                     Filterable="true"                                     
                                     FilterOperator="StringFilterOperator.Contains"
                                     Width="400px">
                </TelerikDropDownList>
                <br /><br />
                @if (SubdivisionsSuppliersData == null)
                {
                    @*   <p><em>Loading...</em></p> *@
                }

                else
                {

                    <TelerikGrid Data=@SubdivisionsSuppliersData
                                 ConfirmDelete="true"
                                 ScrollMode="@GridScrollMode.Virtual"
                                 FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                 Height="1000px" RowHeight="60" PageSize="20"
                                 Sortable="true"
                                 Resizable="true"
                    @bind-SelectedItems="SelectedRowsActivityGrid"
                                 SelectionMode="@GridSelectionMode.Multiple"
                    @ref="@ActivityGridRef">
                        <GridSettings>
                            <GridPopupEditSettings Width="800px"></GridPopupEditSettings>
                        </GridSettings>
                        <GridColumns>
                            <GridCheckboxColumn />
                            <GridColumn Field = "Subdivision.SubdivisionName" Title="Subdivision" Editable="false"/>
                            <GridColumn Field="SubNumberNavigation.SubName" Title="Supplier">
                                <EditorTemplate>
                                    @{
                                        PactivityAreaSupplierToEdit = context as PactivityAreaSupplierDto;
                                        <TelerikDropDownList @bind-Value="@PactivityAreaSupplierToEdit.SubNumber"
                                                             Data="@AllSuppliers"
                                                             ScrollMode="@DropDownScrollMode.Virtual"
                                                             ItemHeight="40"
                                                             PageSize="20"
                                                             TextField="SubName"
                                                             ValueField="SubNumber"
                                                             DefaultText="Select Supplier"
                                                             Filterable="true"
                                                             FilterOperator="StringFilterOperator.Contains"
                                                             Width="200px">
                                        </TelerikDropDownList>
                                    }
                                </EditorTemplate>
                            </GridColumn>
                        </GridColumns>
                        <GridToolBarTemplate>
                            @if(AllowEdit){
                                <GridCommandButton OnClick="@AssignSuppliersActivityGridAsync" Command="AssignSuppliersCheckedRows" Icon="@FontIcon.Plus" Class="k-button-add">Assign Suppliers</GridCommandButton>
                            }
                            <GridCommandButton Command="ExcelExport" Icon="@FontIcon.FileExcel" Class="k-button-add">Export to Excel</GridCommandButton>
                            <GridSearchBox DebounceDelay="200" Class="searchBox"></GridSearchBox>
                        </GridToolBarTemplate>
                        <GridExport>
                            <GridExcelExport FileName="SuppliersByActivity" OnBeforeExport="@OnActivityExcelBeforeExport" AllPages="true" />
                        </GridExport>
                        <NoDataTemplate>
                            <p>@Message</p>
                        </NoDataTemplate>
                    </TelerikGrid>
                }
            </TabStripTab>
        </TelerikTabStrip>
        
    </ChildContent>
</ErrorBoundary>

<ERP.Web.Components.AssignActivitySuppliers @ref="AssignActivitySuppliers" SelectedActivitiesId="@SelectedActivityIds" HandleAddSubmit="@HandleAssignSuppliersAsync" SelectedSubdivisionsId="@SelectedSubdivisionIds"></ERP.Web.Components.AssignActivitySuppliers>
@code {
    private List<PactivityDto>? PactivitiesGridData;
    private List<PactivityAreaSupplierDto>? SubdivisionsSuppliersData { get; set; }
    private TelerikGrid<PactivityDto>? GridRef { get; set; }
    private TelerikGrid<PactivityAreaSupplierDto>? ActivityGridRef { get; set; }
    public string? Message { get; set; } = "No data to display";
    public List<SubdivisionDto>? AllSubdivisions { get; set; }
    public List<PactivityDto>? AllPactivites { get; set; }
    public int? SelectedActivity { get; set; }
    public int? SelectedSubdivision { get; set; }
    public List<int>? SelectedSubdivisionIds { get; set; }
    public List<int>? SelectedActivityIds { get; set; }
    public int? SelectedSubdivisionValue {get; set; }
    public int? SelectedActivityValue {get; set; }
    public List<SupplierDto>? AllSuppliers { get; set; }
    public List<TradeDto>? AllTrades { get; set; }
    public List<SactivityDto>? AllSActivities { get; set; }
    public List<JccategoryDto>? AllJccategories { get; set; }
    public List<JccostcodeDto>? AllJccostcodes { get; set; }
    public List<MasterItemDto>? DefaultMasterItems { get; set; }
    public PactivityDto? ItemToEdit { get; set; }
    public PactivityAreaSupplierDto? PactivityAreaSupplierToEdit { get; set; }
    public IEnumerable<PactivityDto>? SelectedRows { get; set; } = new List<PactivityDto>();
    public IEnumerable<PactivityAreaSupplierDto>? SelectedRowsActivityGrid { get; set; } = new List<PactivityAreaSupplierDto>();
    protected ERP.Web.Components.AssignActivitySuppliers? AssignActivitySuppliers { get; set; }
    private PactivityDto EditPActivity { get; set; }
    //public List<BomClassDto> AllBOMs { get; set; }
    private bool AllowEdit { get; set; } = true;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            var activitiesTask = PoService.GetPactivitiesAsync();
            var subdivisionTask = SubdivisionService.GetSubdivisionsAsync();
            var sactivitiesTask = PoService.GetSactivitiesAsync();
            var tradeTask = PoService.GetTradesAsync();
            var costCodeTask = PoService.GetJcCostCodesAsync();
            var itemTask = PoService.GetDefaultMasterItemsAsync();
            // var bomTask = PoService.GetBOMsAsync();
            var jccategoryTask = PoService.GetJccategoriesAsync();

            await Task.WhenAll(activitiesTask, subdivisionTask, sactivitiesTask, tradeTask, costCodeTask, itemTask, jccategoryTask);

            AllPactivites = activitiesTask.Result.Value;
            AllSubdivisions = subdivisionTask.Result.Value;
            AllSActivities = sactivitiesTask.Result.Value;
            AllTrades = tradeTask.Result.Value;
            AllJccostcodes = costCodeTask.Result.Value;
            DefaultMasterItems = itemTask.Result.Value;
            // AllBOMs = bomTask.Result.Value;
            AllJccategories = jccategoryTask.Result.Value;
            var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userName = user.User.Identity.Name.Split('@')[0];
            var userRoleAdmin = user.User.IsInRole("Admin");
            var userReadOnly = user.User.IsInRole("ReadOnly");
            AllowEdit = !userReadOnly;
        }
        catch(Exception ex)
        {
            var test = ex.Message;
        }
    }

    private async Task AssignSuppliersSubdivisionGridAsync()
    {
        if(SelectedSubdivision != null)
        {
            SelectedSubdivisionIds = new List<int>() { (int)SelectedSubdivision };
        }
        if(SelectedRows != null)
        {
            SelectedActivityIds = SelectedRows.Select(x => x.PactivityId).ToList();
        }
        await AssignActivitySuppliers.Show();
    }

    private async Task AssignSuppliersActivityGridAsync()
    {
        if(SelectedRowsActivityGrid != null)
        {
            SelectedSubdivisionIds = SelectedRowsActivityGrid.Select(x => x.SubdivisionId).ToList();
        }       
        if(SelectedActivity != null)
        {
            SelectedActivityIds = new List<int>() { (int)SelectedActivity };
        }        
        await AssignActivitySuppliers.Show();
    }

    private async Task HandleAssignSuppliersAsync(List<PactivityAreaSupplierDto> responseModel)
    {
        if (SelectedActivity != null)
        {
            SubdivisionsSuppliersData = (await PoService.GetSubdivisionActivitesSupplierAsync((int)SelectedActivity)).Value;
        }
        if (SelectedSubdivision != null)
        {
            PactivitiesGridData = (await PoService.GetPactivitiesBySubdivision((int)SelectedSubdivision)).Value;
        }
        await AssignActivitySuppliers.Hide();
    }

    private async Task SubdivisionSelectedHandler(object theValue)
    {
        if(theValue != null && (int)theValue != SelectedSubdivisionValue)
        {
            SelectedSubdivisionValue = (int)theValue;
            if (SelectedSubdivision != null)
            {
                PactivitiesGridData = (await PoService.GetPactivitiesBySubdivision((int)SelectedSubdivision)).Value;
            }
            if (AllTrades == null || AllJccategories == null || AllJccostcodes == null || AllSuppliers == null || AllSActivities == null || DefaultMasterItems == null)
            {
                var suppliersTask = PoService.GetSuppliersAsync(IncludeBlocked: false);
                var tradesTask = PoService.GetTradesAsync();
                var sactivitiesTask = PoService.GetSactivitiesAsync();
                var categoriesTask = PoService.GetJccategoriesAsync();
                var costCodesTask = PoService.GetJcCostCodesAsync();
                var masterItemsTask = PoService.GetDefaultMasterItemsAsync();
                await Task.WhenAll(suppliersTask, tradesTask, sactivitiesTask, categoriesTask, costCodesTask, masterItemsTask);
                DefaultMasterItems = masterItemsTask.Result.Value;
                AllSuppliers = suppliersTask.Result.Value;
                AllSActivities = sactivitiesTask.Result.Value;
                AllTrades = tradesTask.Result.Value;
                AllJccostcodes = costCodesTask.Result.Value.Select(x => new JccostcodeDto() { JccostcodeId = x.JccostcodeId, CcDescription = $"{x.CostCode} - {x.CcDescription}" }).ToList();
                AllJccategories = categoriesTask.Result.Value.Select(x => new JccategoryDto() { JccategoryId = x.JccategoryId, Catdescription = $"{x.Category} - {x.Catdescription}" }).ToList();
            }
        }
    }

    private async Task ActivitySelectedHandler(object theValue)
    {
        if (theValue != null && (int)theValue != SelectedActivityValue)
        {
            SelectedActivityValue = (int)theValue;
            if (SelectedActivity != null)
            {
                SubdivisionsSuppliersData = (await PoService.GetSubdivisionActivitesSupplierAsync((int)SelectedActivity)).Value;
            }
        }
    }

    async Task DeleteActivity(GridCommandEventArgs args)
    {
        PactivityDto item = (PactivityDto)args.Item;
        var deletePActivityResponse = await PoService.DeletePActivityAsync(item);
        if (deletePActivityResponse.IsSuccess)
        {
            PactivitiesGridData = (await PoService.GetPactivitiesAsync()).Value;
            ShowSuccessOrErrorNotification(deletePActivityResponse.Message, false);
        }
        else
        {
            ShowSuccessOrErrorNotification(deletePActivityResponse.Message, true);
        }
    }

    async Task UpdateActivitySubdivision(GridCommandEventArgs args)
    {
        //TODO: check if actually changed
        PactivityAreaSupplierDto item = (PactivityAreaSupplierDto)args.Item;
        await PoService.UpdateActivitySupplier(item);
        if (SelectedActivity != null)
        {
            SubdivisionsSuppliersData = (await PoService.GetSubdivisionActivitesSupplierAsync((int)SelectedActivity)).Value;
        }
        if (SelectedSubdivision != null)
        {
            PactivitiesGridData = (await PoService.GetPactivitiesBySubdivision((int)SelectedSubdivision)).Value;
        }
    }

    async Task CancelActivitySubdivsion(GridCommandEventArgs args)
    {
        PactivityAreaSupplierDto item = (PactivityAreaSupplierDto)args.Item;

        // if necessary, perform actual data source operation here through your service

        Console.WriteLine("Cancel event is fired.");
    }

    private async Task OnValidSubmit()
    {
        ResponseModel<PactivityDto> result;

        if (EditPActivity.PactivityId != 0)
        {
            result = await PoService.EditPActivityAsync(EditPActivity);
        }
        else
        {
            result = await PoService.AddPActivityAsync(EditPActivity);
        }

        if (!result.IsSuccess)
        {
            ShowSuccessOrErrorNotification(result.Message, true);
        }
        else
        {
            ShowSuccessOrErrorNotification(result.Message, false);
        }

        await ExitEditAsync();

        var activitiesTask = await PoService.GetPactivitiesAsync();
        AllPactivites = activitiesTask.Value;
    }

    protected async Task ReadItems(GridReadEventArgs args)
    {
        var pactivitiesData = await PoService.GetPactivitiesAsync();
        var datasourceResult = pactivitiesData.Value.ToDataSourceResult(args.Request);

        args.Data = datasourceResult.Data;
        args.Total = datasourceResult.Total;
    }

    private async Task ExitEditAsync()
    {
        var state = GridRef?.GetState();
        state.OriginalEditItem = null;
        state.EditItem = null;
        state.InsertedItem = null;

        await GridRef?.SetStateAsync(state);
    }

    private void ShowSuccessOrErrorNotification(string message, bool error)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = error ? ThemeConstants.Notification.ThemeColor.Error : ThemeConstants.Notification.ThemeColor.Success
            });
    }

    private async Task OnExcelBeforeExport(GridBeforeExcelExportEventArgs args)
    {

        args.Columns[0].Width = "200px";
        args.Columns[1].Width = "200px";
        args.Columns[2].Width = "150px";
        args.Columns[3].Width = "50px";
        args.Columns[4].Width = "100px";

    }
    private async Task OnSubdivisionExcelBeforeExport(GridBeforeExcelExportEventArgs args)
    {

        args.Columns[0].Width = "200px";
        args.Columns[1].Width = "50px";
        args.Columns[2].Width = "50px";
        args.Columns[3].Width = "150px";
        args.Columns[4].Width = "200px";
        args.Columns[5].Width = "50px";
        args.Columns[6].Width = "50px";
        args.Columns[7].Width = "200px";
        args.Columns[7].Width = "200px";
    }
    private async Task OnActivityExcelBeforeExport(GridBeforeExcelExportEventArgs args)
    {
        args.Columns[0].Width = "300px";
        args.Columns[1].Width = "250px";
    }
}
