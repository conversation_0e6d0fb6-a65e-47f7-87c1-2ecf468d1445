﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class JobTaskDto: IMapFrom<JobTask>
{
    public string? JobNumber { get; set; } 

    public string? JobTaskNo { get; set; } 

    public string? Description { get; set; } 

    public int JobTaskType { get; set; }

    public string? JobPostingGroup { get; set; } 

    public string? CostCode { get; set; } 

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<JobTaskDto, JobTask>().ReverseMap();
    }
}
