﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class WorksheetOptModel

{
    public int WorksheetId { get; set; }
    public int WorksheetOptId { get; set; }
    public string? OptionName { get; set; }
    public string? OptionCode { get; set; }
    public int WorksheetPlanId { get; set; }
    public int? PhasePlanId { get; set; }
    public int PlanOptionId { get; set; }
    public bool? IsBaseHouse { get; set; }

    public double? Costprice { get; set; }

    public double? Markup { get; set; }

    public double? Sellprice { get; set; }

    public DateTime? Pricedate { get; set; }

    public int? Markuptype { get; set; }

    public double? Markuppercent { get; set; }

    public double? Marketvalue { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }
    public int? Warnings { get; set; }
    public int? WarningCount { get; set; }
    public string? WarningReason { get; set; }
    public int? Errors { get; set; }
    public int? ErrorCount { get; set; }
    public string? ErrorReason { get; set; }

}
