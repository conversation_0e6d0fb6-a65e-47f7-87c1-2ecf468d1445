﻿@page "/attributesbyoption"
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager NavManager
@* @attribute [Authorize(Roles = "Admin, Purchasing, Scheduling")] *@
@using ERP.Data.Models.Dto;
@using Telerik.DataSource
@using Telerik.DataSource.Extensions
@inject PlanService PlanService

<style type="text/css">
    /* Gutter */
    .row > * {
        padding-right: calc(var(--bs-gutter-x) * .10);
        padding-left: calc(var(--bs-gutter-x) * .10);
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto",sans-serif;
        font-size: .75rem;
    }

    .k-form .k-form-label, .k-form .k-form-field-wrap {
        display: inline;
    }
</style>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<PageTitle>Manage Attributes By Option</PageTitle>
<div class="container-fluid flex">
    <div class="row d-flex">
        <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Manage Attributes By Option</h7>
                </div>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item"><a href="/attributes">Attributes</a></li>
            <li class="breadcrumb-item active">Attributes By Option</li>
        </ol>

        <div class="col-lg-3">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Option Group</h7>
                </div>
            </div>
            @if (MasterOptionGroupData == null)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingGroup />
            }

            else
            {
                <TelerikGrid Data=@MasterOptionGroupData
                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                Height="1000px" RowHeight="60" PageSize="20"
                Size="@ThemeConstants.Grid.Size.Small"
                Sortable="true"
                Resizable="true"
                Reorderable="true"
                Groupable="false"
                OnRowClick="@OnOptionGroupRowClickHandler"
                @ref="@GroupGridRef">
                    <GridColumns>
                        <GridColumn Field="OptionGroupName" Title="Option Group" Editable="true" Groupable="false" />
                        <GridColumn Field="OptionGroupLetter" Title="Option Group Letter" Editable="true" Groupable="false" Width="0" />
                        <GridColumn Field="OptionGroupId" Visible="false" Editable="false" Groupable="false" />
                    </GridColumns>
                    <GridToolBarTemplate>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <NoDataTemplate>
                        <p>@Message</p>
                    </NoDataTemplate>
                </TelerikGrid>

            }
        </div>
        <div class="col-lg-4">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Options for Group: @SelectedGroup?.OptionGroupName</h7>
                </div>
            </div>
            @if (MasterOptionData == null)
            {
                <p><em>Select a group to see options</em></p>
                <div style=@optionLoadingStyle>Loading...</div>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingOption />
            }
            else
            {
                <TelerikGrid Data="@MasterOptionData"
                         FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                         Height="1000px" RowHeight="60"
                         Pageable="true" PageSize="20"
                         SelectionMode="GridSelectionMode.Single"
                         Size="@ThemeConstants.Grid.Size.Small"
                         Sortable="true"
                         Reorderable="true"
                         Resizable="true"
                         Groupable="false"
                         OnRowClick="@OnOptionRowClickHandler"
                         @ref="@OptionGridRef">
                    <GridColumns>
                        <GridColumn Field="OptionCode" Title="Option Code" Editable="true" Groupable="false" />
                        <GridColumn Field="OptionDesc" Title="Option Description" Editable="true" Groupable="false" />                      
                        <GridColumn Field="OptionGroupId" Title="Option Group" Editable="true" Groupable="false" Width="0" />
                        <GridColumn Field="AsmHeaderId" Visible="false" Editable="false" Groupable="false" />
                    </GridColumns>
                    <GridToolBarTemplate>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <NoDataTemplate>
                        <p>@Message</p>
                    </NoDataTemplate>
                </TelerikGrid>

            }
        </div>
        <div class="col-lg-5">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Attributes for Option: @SelectedOption?.OptionDesc</h7>
                </div>
            </div>
            @if (OptionWithAttributeGroup == null)
            {
                <p><em>Select an option to see group attached to it</em></p>
                <div style=@itemLoadingStyle>Loading...</div>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingItem />
            }
            else
            {
                <TelerikGrid Data=@OptionWithAttributeGroup
                         FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                         Height="1000px" RowHeight="60" PageSize="20"
                         Sortable="true"
                         Resizable="true"
                         Groupable="false"
                         Reorderable="true"
                         Size="@ThemeConstants.Grid.Size.Small"
                         SelectionMode="GridSelectionMode.Single"
                         @ref="@ItemGridRef"
                         OnDelete="@OnDeleteGroupHandler">
                    <GridColumns>
                        <GridColumn Field="Description" Width="200px" Title="Description" Editable="false" Groupable="false" />
                        <GridCommandColumn>
                            <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                        </GridCommandColumn>
                    </GridColumns>
                    <GridToolBarTemplate>
                        @if (AllowEdit)
                        {
                            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" OnClick="@MyCommandFromToolbar" Class="k-button-add">Add Group</GridCommandButton>
                        }
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                </TelerikGrid>
            }

        </div>
    </div>
</div>

<ERP.Web.Components.AddGroupToOption @ref="AddMasterAttributeGroupModal" Option=@SelectedOption HandleAddSubmit="HandleValidAddItemSubmit"></ERP.Web.Components.AddGroupToOption>

@code {
    private TelerikGrid<OptionGroupDto>? GroupGridRef { get; set; }
    private TelerikGrid<MasterOptionHeaderModel>? OptionGridRef { get; set; }
    private TelerikGrid<MasterAttributeGroupDto>? ItemGridRef { get; set; }
    public List<OptionGroupDto>? MasterOptionGroupData { get; set; }
    public List<MasterOptionHeaderModel>? MasterOptionData { get; set; }
    public MasterOptionHeaderModel SelectedOption { get; set; }
    public OptionGroupDto? SelectedGroup { get; set; }
    public int SelectedOptionGroupId { get; set; } = 0;
    protected ERP.Web.Components.AddGroupToOption? AddMasterAttributeGroupModal { get; set; }
    public int OptionGroupIdForAddingOption { get; set; }
    private string optionLoadingStyle = "display:none";
    private string itemLoadingStyle = "display:none";
    public bool IsLoadingGroup { get; set; } = false;
    public bool IsLoadingOption { get; set; } = false;
    public bool IsLoadingItem { get; set; } = false;
    public string? Message { get; set; } = "No data to display";
    public bool? ShowError;

    // Attributes Data
    public List<MasterAttributeGroupDto>? OptionWithAttributeGroup { get; set; }

    public List<MasterPlanDto>? AllMasterPlans { get; set; }
    public MasterOptionHeaderModel CurrentlySelectedMasterPlans { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    private bool AllowEdit { get; set; } = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDataAsync();
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }

    private async Task LoadDataAsync()
    {
        IsLoadingGroup = true;

        var getGroups = await OptionService.GetOptionGroupsAsync();

        ShowError = getGroups.IsSuccess;
        Message = getGroups.Message;
        MasterOptionGroupData = getGroups.Value;

        IsLoadingGroup = false;
    }

    protected async Task OnOptionGroupRowClickHandler(GridRowClickEventArgs args)
    {
        IsLoadingOption = true;
        MasterOptionData = null;
        optionLoadingStyle = "";
        SelectedGroup = args.Item as OptionGroupDto;
        SelectedOptionGroupId = SelectedGroup.OptionGroupId;
        OptionGroupIdForAddingOption = SelectedGroup.OptionGroupId;
        MasterOptionData = (await OptionService.GetMasterOptionsByGroupAsync(SelectedGroup.OptionGroupId)).Value;

        OptionWithAttributeGroup = null;//reset

        optionLoadingStyle = "display:none";
        IsLoadingOption = false;
    }

    protected async Task OnOptionRowClickHandler(GridRowClickEventArgs args)
    {
        IsLoadingItem = true;
        OptionWithAttributeGroup = null;
        itemLoadingStyle = "";
        SelectedOption = args.Item as MasterOptionHeaderModel;

        OptionWithAttributeGroup = (await OptionService.GetOptionWithGroupsAsync(SelectedOption.OptionId)).Value;

        itemLoadingStyle = "display:none";
        IsLoadingItem = false;
    }

    private void MyCommandFromToolbar(GridCommandEventArgs args)
    {
        //var headerId = SelectedOption.AsmHeaderId;
        AddMasterAttributeGroupModal.Show();
        StateHasChanged();
    }

    private async void HandleValidAddItemSubmit(ResponseModel responseItem)
    {
        OptionWithAttributeGroup = (await OptionService.GetOptionWithGroupsAsync(SelectedOption.OptionId)).Value;
        ShowSuccessOrErrorMessage(responseItem.Message, responseItem.IsSuccess);
        AddMasterAttributeGroupModal.Hide();
        StateHasChanged();
    }

    async void ShowSuccessOrErrorMessage(string message, bool isSuccess)
    {
        // Alert
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }

    private async Task OnDeleteGroupHandler(GridCommandEventArgs args)
    {
        var attributeGroup = (MasterAttributeGroupDto)args.Item;

        var result = await OptionService.DeleteMasterOptionAttributeItemAsync(attributeGroup);

        // Reload
        OptionWithAttributeGroup = (await OptionService.GetOptionWithGroupsAsync(SelectedOption.OptionId)).Value;

        // Alert
        ShowSuccessOrErrorMessage(result.Message, result.IsSuccess);

        StateHasChanged();
    }
}
