﻿using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace ERP.Data.Models;

public class PoheaderDto :IMapFrom<Poheader>
{
    public int PoheaderId { get; set; }

    public string? Ponumber { get; set; }

    public string? Releasecode { get; set; }

    [Required]
    public string? Pojobnumber { get; set; }

    public int? Potype { get; set; }

    [Required]
    [StringLength(950, ErrorMessage = "The description is too long")]
    [RegularExpression(@"^[a-zA-Z0-9&\s.,'-]*$", ErrorMessage = "Description contains invalid characters.")]
    public string? Podescription { get; set; }

    public int SubNumber { get; set; }

    public string? Povendor { get; set; }

    public DateTime? Podateissued { get; set; }

    public DateTime? Podateprinted { get; set; }

    public DateTime? Podateapproved { get; set; }

    [DisplayFormat(DataFormatString = "{0:C0}")]
    public double? Pototal { get; set; }

    public double? PartPaymentAmt { get; set; }

    public double? ApproveTillDate { get; set; }

    public double? RemainingTotal { get; set; }

    public string? Printable { get; set; }

    public double? Taxamount { get; set; }

    public int? Postatus { get; set; }

    public string? Approvedby { get; set; }

    public string? PendInvNumber { get; set; }

    public DateTime? PendInvDate { get; set; }

    public string? RetentionType { get; set; }

    public double? Retention { get; set; }

    public double? MaximumRetention { get; set; }

    public string? LastAcceptInvNo { get; set; }

    public DateTime? LastAcceptInvDte { get; set; }

    public double? LastAcceptInvAmt { get; set; }

    public double? LastAcceptRetent { get; set; }

    public DateTime? PendInvAcntDate { get; set; }

    public DateTime? PendInvPayDate { get; set; }

    public DateTime? PendInvDiscDate { get; set; }

    public double? PendInvAmount { get; set; }

    public double? PendInvTax { get; set; }

    public double? PendInvDisc { get; set; }

    public double? PendInvRetention { get; set; }

    public string? PendInvDescrip { get; set; }

    public DateTime? TaskCompleteDate { get; set; }

    public int? TradeId { get; set; }

    public int? SactivityId { get; set; }

    public string? Issuedby { get; set; }

    public string? PoSelections { get; set; }

    public string? JccOnHold { get; set; }

    public string? WcompOnHold { get; set; }

    public DateTime? Podatecancelled { get; set; }

    public string? Cancelledby { get; set; }

    public int? PurchasingContractsId { get; set; }

    public string? DocId { get; set; }

    public string? TradeArchived { get; set; }

    public DateTime? TradeDateViewed { get; set; }

    public string? TradeUserViewed { get; set; }

    public DateTime? TradeDateArchived { get; set; }

    public string? TradeUserArchived { get; set; }

    public string? PendInvNotes { get; set; }

    public string? TaskCompleteBy { get; set; }

    public string? ApprovalHold { get; set; }

    public string? ApprovalHoldReason { get; set; }

    public string? PendClose { get; set; }

    public int? PurchasingContCoId { get; set; }

    public string? UserModified { get; set; }

    public DateTime? DateModified { get; set; }

    public string? Ponotes { get; set; }

    public string? JobClosedHold { get; set; }

    public DateTime? PosentTimestamp { get; set; }

    public int? VpoNumber { get; set; }

    public string? VpoVendorinvoice { get; set; }

    public int? LienWaiverId { get; set; }

    public string? LienWaiverHold { get; set; }

    public DateTime? TradeDateAccepted { get; set; }

    public string? TradeAcceptedOverrideuser { get; set; }

    public int? TradeUserAccepted { get; set; }

    public string? TradeAcceptedHold { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public int? MasterPo { get; set; }

    public int? IntegrationPo { get; set; }

    public string? IntegrationPonumber { get; set; }

    public string? TbdChange { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    //public byte[]? RecordTimeStamp { get; set; }

    //public virtual ICollection<Poapproval> Poapprovals { get; set; } = new List<Poapproval>();
    public List<PoapprovalDto?>? PoapprovalsList { get; set; } 
    public Guid? BcId { get; set; }
    public JobDto? PojobnumberNavigation { get; set; } = new JobDto();

    public SupplierDto? SubNumberNavigation { get; set; } = new SupplierDto { };

    //public virtual Trade? Trade { get; set; }
    public  PostatusDto? PostatusNavigation { get; set; }= new PostatusDto();

    public string? Trade { get; set; }

    public string? Supplier { get; set; }

    public string? AssignedTo { get; set; }

    public string? Community { get; set; }

  //  public List<PodetailDto> Podetails { get; set; } = new List<PodetailDto>();

    public string? SubdivisionName { get; set; }

    public int? JccCategoryId { get; set; }

   // [Required(ErrorMessage = "Error Test")]//This validation causes errors elsewhere
    public int? PurchasingActivityId { get; set; }

    public int? VPOPurchasingActivityId { get; set; }
    public int BackchargeSubNumber { get; set; }

    public string? ErrorMessage { get; set; }

    public int SubdivisionId { get; set; }

    public string? SuccessMessage { get; set; }
    public bool? SupplierBlocked { get; set; }
    public bool? SupplierNoInsurance { get; set; }

    public bool? IsPOApprovalStarted { get;set; }

    public string? VpoNumberSearch { get; set; }
    public bool? IsSchedulePayPoint { get; set; }

    public List<FileModel> Files { get; set; } = new List<FileModel>();

    public List<PoAttachmentDto> PoAttachments { get; set; } = new List<PoAttachmentDto>();

    public string? PaymentCheckNumbers { get; set; }

    public string? PaymentCheckDates { get; set; }

    public string? HouseJob { get; set; }

    public JobDto? HouseJobNavigation { get; set; }

    [JsonIgnore]
    public string? Status => PostatusNavigation?.Postatus1;
}
