﻿@page "/subdivisionsplansoptionsnested"
@using ERP.Data.Models.Dto;
@using System.Diagnostics;
@inject SubdivisionService SubdivisionService
@inject NavigationManager NavManager

<style>

</style>

<PageTitle>Subdivisions</PageTitle>

<ErrorBoundary>
    <ChildContent>
        <div class="col-lg-12">
            <div class="card" style="background-color: #2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Subdivisions</h7>
                </div>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item active">Subdivisions</li>
        </ol>

        @if (displaySuccessStatus)
        {
            if (isError == false)
            {
                <div class="alert alert-success" role="alert">
                    @displaySuccessMessage
                </div>
            }
            else if (isError == true)
            {
                <div class="alert alert-danger" role="alert">
                    @displaySuccessMessage
                </div>
            }
        }

       
        <br />
        @if (subdivisions == null)
        {
            <p><em>Loading...</em></p>
            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner"/>
        }
        else
        {
            <TelerikGrid Data="@subdivisions"
             Width="100%"
            >
            <DetailTemplate Context="subdivContext">
                <NestedPlanGrid SubdivisionId="subdivContext.SubdivisionId"></NestedPlanGrid>
            </DetailTemplate>
        <GridColumns>
                    <GridColumn Field="SubdivisionNum" Visible="false"></GridColumn>
        <GridColumn Field="SubdivisionName" Title="Plan"></GridColumn>
    </GridColumns>
</TelerikGrid>
            
        }
    </ChildContent>
</ErrorBoundary>


@code {
    private List<SubdivisionDto>? subdivisions;
    public List<JobDto> SelectLots { get; set; } = new List<JobDto>();
    public string? SelectedLot { get; set; }
    public List<string>? AllLots { get; set; }
    private TelerikGrid<SubdivisionDto> GridRef { get; set; }
    public string? Message { get; set; } = "No data to display";
    protected ERP.Web.Components.AddSubdivision? AddSubdivisionModal { get; set; }

    // Success/Error
    private bool displaySuccessStatus = false;
    private string displaySuccessMessage;
    private bool isError = false;

    protected override async Task OnInitializedAsync()
    {
        Stopwatch sw = new Stopwatch();
        sw.Start();
        var getSubdivisions = await SubdivisionService.GetSubdivisionsAsync();
        subdivisions = getSubdivisions.Value;
        Message = getSubdivisions.Message;      
        sw.Stop();
        Console.WriteLine($"get subdivisions: {sw.Elapsed}");
    }
        
}
