﻿@page "/schedulecalendar/"
@inject ScheduleService ScheduleService
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector, ConstructionManager")]
@using ERP.Web.Components
<PageTitle>Schedule Calendar</PageTitle>
<style>
    .weekend {
        color: green;
        font-weight: bold;
    }

    .holiday {
        color: blue;
        font-weight: bold;
    }

    .k-calendar td.special:hover .k-link {
        background-color: grey;
        color: grey;
    }
</style>


@if (CalendarSchedule == null)
{
    <p><em>Loading...</em></p>
}
else
{
    <TelerikCalendar Date="@selectedDate"
                     Min="@Min"
                     Max="@Max"
                     View="CalendarView.Month"
                     SelectionMode="@CalendarSelectionMode.Multiple"
                     ValueChanged="@MyValueChangeHandler"
                     ShowWeekNumbers="false"
                     OnCellRender="@OnCellRender"
                     Size="lg">

    </TelerikCalendar>

    <TelerikGrid Data=@SelectedDay
                 EditMode="@GridEditMode.Inline"
                 Pageable="false"
                 Height="1000px"
                 Navigable="true"
                 OnUpdate=UpdateCalendar
                 ConfirmDelete="true"
                 OnDelete=DeleteCalendarItem>
        <GridColumns>
            <GridColumn Field=WorkDate DisplayFormat="{0:MMMM dd, yyyy}" Title="Day" Editable="true" />
            <GridColumn Field=Description Title="Description" Editable="true">
                <EditorTemplate Context="CalendarContext">
                    @{
                        EditedDay = CalendarContext as CalendarsDayDto;

                        <TelerikTextArea @bind-Value="@EditedDay.Description" />
                    }
                </EditorTemplate>
            </GridColumn>
            <GridCommandColumn>
                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true" Class="k-button-success">Update</GridCommandButton>
                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success">Edit</GridCommandButton>
                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger">Delete</GridCommandButton>
                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true">Cancel</GridCommandButton>
            </GridCommandColumn>
        </GridColumns>
    </TelerikGrid>

    <div>The selected date is: @selectedDate</div>
}


@code {
    private List<CalendarsDayDto> CalendarSchedule;
    private List<CalendarsDayDto> SelectedDay;
    private DateTime Min = DateTime.UtcNow.AddYears(-5);
    private DateTime Max = DateTime.UtcNow.AddYears(5);
    private CalendarsDayDto EditedDay { get; set; }

    private DateTime selectedDate = DateTime.UtcNow;

    protected override async Task OnInitializedAsync()
    {
        CalendarSchedule = (await ScheduleService.GetCalendarAsync()).Value;
    }

    private void OnCellRender(CalendarCellRenderEventArgs args)
    {
        if (args.View == CalendarView.Month)
        {
            args.Class = args.Date.DayOfWeek == DayOfWeek.Sunday || args.Date.DayOfWeek == DayOfWeek.Saturday ? "weekend" : "";
        }

        foreach (CalendarsDayDto calendar in CalendarSchedule)
        {
            if (calendar.WorkDate == args.Date)
            {
                args.Class = "holiday";
            }
        }
    }
    private void MyValueChangeHandler(DateTime newValue)
    {
        // only have one day in the Grid at a time
        SelectedDay = new List<CalendarsDayDto>();

        bool isHoliday = false;
        foreach (CalendarsDayDto calendar in CalendarSchedule)
        {
            if (calendar.WorkDate == newValue.Date)
            {
                SelectedDay.Add(calendar);
                isHoliday = true;
            }
        }
        if (!isHoliday)
        {
            SelectedDay.Add(new CalendarsDayDto() { WorkDate = newValue, Description = null });
        }
        selectedDate = newValue;
    }

    private async void UpdateCalendar(GridCommandEventArgs args)
    {
        CalendarsDayDto item = (CalendarsDayDto)args.Item;
        var response1 = await ScheduleService.UpdateCalendarAsync(item);
        CalendarSchedule = (await ScheduleService.GetCalendarAsync()).Value;
        MyValueChangeHandler(item.WorkDate);
        StateHasChanged();
    }

    private async void DeleteCalendarItem(GridCommandEventArgs args)
    {
        CalendarsDayDto item = (CalendarsDayDto)args.Item;
        var response = await ScheduleService.DeleteCalendarItemAsync(item);
        CalendarSchedule = (await ScheduleService.GetCalendarAsync()).Value;
        MyValueChangeHandler(item.WorkDate);
        StateHasChanged();
    }
}