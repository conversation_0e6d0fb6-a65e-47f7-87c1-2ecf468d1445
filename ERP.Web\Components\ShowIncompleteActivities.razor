﻿@if (IncompleteSubdivisionActivities is not null && IncompleteSubdivisionActivities.Activities is not null && IncompleteSubdivisionActivities.Activities.Any())
{
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h4 class="card-title"> @IncompleteSubdivisionActivities.Activities.Count Incomplete Activities at @IncompleteSubdivisionActivities.Subdivision.MarketingName</h4>
            </div><!--end col-->
        </div>  <!--end row-->
    </div>

    <TelerikGrid Data="@IncompleteSubdivisionActivities.Activities"
                 Sortable="true"
                 ScrollMode="@GridScrollMode.Virtual"
                 Height="545px"
                 RowHeight="35"
                 PageSize="12">
        <GridToolBarTemplate>
            <GridSearchBox Width="300px"/>
        </GridToolBarTemplate>
        <GridColumns>
            <GridColumn Field="ActivityName" Title="Activity" Groupable="true" />
            <GridColumn Field="ScheduleM.Schedule.JobNumberNavigation.JobAndLotNumString" Title="Job" Groupable="true" />
            <GridColumn Field="SchEndDate" Title="Sch. End" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Groupable="true" />
        </GridColumns>
    </TelerikGrid>
}
else
{
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h4 class="card-title">Total Incomplete Activities</h4>
            </div><!--end col-->
        </div>  <!--end row-->
    </div>
    <!--end card-header-->
    <div class="card-body">
        @if (TotalIncompleteActivities is not null)
            @TotalIncompleteActivities
    </div>
}

@code {
    [Parameter]
    public int? TotalIncompleteActivities { get; set; }
    [Parameter]
    public IncompleteSubdivisionActivitiesDto? IncompleteSubdivisionActivities { get; set; }

}
