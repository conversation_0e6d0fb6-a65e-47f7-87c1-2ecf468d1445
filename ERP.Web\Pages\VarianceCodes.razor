﻿@page "/variancecodes"
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector, ConstructionManager")]
@inject ScheduleService ScheduleService
@inject AuthenticationStateProvider AuthenticationStateProvider
<style>
    .MyTelerikNotification .k-notification-container .k-notification{
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>
<PageTitle>Variance Codes</PageTitle>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<div class="col-lg-12">
    <div class="card" style="background-color: #2e5771">
        <div class="card-body" style="padding:0.5rem">
            <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Variance Codes</h7>
        </div>
    </div>
</div>

<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="/">Home</a></li>
    <li class="breadcrumb-item active">Variance Codes</li>
</ol>

<div class="row">
    @if (displayLoadingSpinner)
    {
        <p><em>Loading...</em></p>
        <TelerikLoader></TelerikLoader>
    }
    else
    {
        <TelerikGrid Data="@VarianceCodesData"
                     EditMode="@GridEditMode.Inline"
                     OnUpdate="@UpdateHandler"
                     OnDelete="@DeleteHandler"
                     ConfirmDelete="true"
                     Sortable="true">
            <GridToolBarTemplate>
                <GridCommandButton Command="AddVarianceCode" OnClick="ShowAddVarianceCode" Icon="@FontIcon.Plus" Class="k-button-add">Add Variance Code</GridCommandButton>
            </GridToolBarTemplate>
            <GridColumns>
                <GridColumn Field="VarianceCode" Title="Variance Code" Editable="false"/>
                <GridColumn Field="VarianceDesc" Title="Description" />
                <GridCommandColumn Width="100px">
                    <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true" Class="k-button-success mb-1">Update</GridCommandButton>
                    <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                    <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                    <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true">Cancel</GridCommandButton>
                </GridCommandColumn>
            </GridColumns>
        </TelerikGrid>
    }
</div>
<ERP.Web.Components.AddVarianceCode @ref="AddVarianceCodeModal" HandleAddSubmit="@HandleValidAddSubmit"></ERP.Web.Components.AddVarianceCode>

@code {
    public List<VarianceDto>? VarianceCodesData { get; set; } = new List<VarianceDto>();
    private bool displayLoadingSpinner { get; set; } = false;
    protected ERP.Web.Components.AddVarianceCode? AddVarianceCodeModal { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        displayLoadingSpinner = true;
        var varianceCodesResponse = await ScheduleService.GetVarianceCodesAsync();
        VarianceCodesData = varianceCodesResponse.Value;
        if (varianceCodesResponse.IsSuccess == false)
        {
            ShowSuccessOrErrorNotification(varianceCodesResponse.Message, true);
        }
        displayLoadingSpinner = false;
    }
    async Task UpdateHandler(GridCommandEventArgs args)
    {
        VarianceDto item = (VarianceDto)args.Item;
        var updateVarianceCodeResponse = await ScheduleService.UpdateVarianceCodeAsync(item);
        var varianceCodesResponse = await ScheduleService.GetVarianceCodesAsync();
        VarianceCodesData = varianceCodesResponse.Value;
        if (updateVarianceCodeResponse.IsSuccess)
        {
            ShowSuccessOrErrorNotification(updateVarianceCodeResponse.Message, false);
        }
        else
        {
            ShowSuccessOrErrorNotification(updateVarianceCodeResponse.Message, true);
        }
    }
    async Task DeleteHandler(GridCommandEventArgs args)
    {
        VarianceDto item = (VarianceDto)args.Item;
        var deleteVarianceCodeResponse = await ScheduleService.DeleteVarianceCodeAsync(item);
        var varianceCodesResponse = await ScheduleService.GetVarianceCodesAsync();
        VarianceCodesData = varianceCodesResponse.Value;
        if (deleteVarianceCodeResponse.IsSuccess)
        {
            ShowSuccessOrErrorNotification(deleteVarianceCodeResponse.Message, false);
        }
        else
        {
            ShowSuccessOrErrorNotification(deleteVarianceCodeResponse.Message, true);
        }
    }
    async Task ShowAddVarianceCode(GridCommandEventArgs args)
    {

        AddVarianceCodeModal.Show();
    }
    private async void HandleValidAddSubmit(ResponseModel<VarianceDto> responseItem)
    {
        AddVarianceCodeModal.Hide();
        displayLoadingSpinner = true;
        var varianceCodesResponse = await ScheduleService.GetVarianceCodesAsync();
        VarianceCodesData = varianceCodesResponse.Value;
        if (responseItem.IsSuccess)
        {
            ShowSuccessOrErrorNotification(responseItem.Message, false);
        }
        else
        {
            ShowSuccessOrErrorNotification(responseItem.Message, true);
        }
        displayLoadingSpinner = false;
        StateHasChanged();
    }
    private void ShowSuccessOrErrorNotification(string message, bool error)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = error ? ThemeConstants.Notification.ThemeColor.Error : ThemeConstants.Notification.ThemeColor.Success
            });
    }
}
