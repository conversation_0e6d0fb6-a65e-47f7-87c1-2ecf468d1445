﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Postatus
{
    public int PostatusId { get; set; }

    public string Postatus1 { get; set; } = null!;

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public string? StatusTranslationForVendor { get; set; }

    public virtual ICollection<Poheader> Poheaders { get; set; } = new List<Poheader>();
}
