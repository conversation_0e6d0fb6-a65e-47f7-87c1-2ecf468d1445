﻿
using AutoMapper;
using ERP.API.Utilities;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class ScheduleReleaseController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private readonly Email _email;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        public ScheduleReleaseController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _email = new Email(configuration);
        }

        
        [HttpGet]
        public async Task<IActionResult> ReleaseSchedulesAsync()
        {
            try
            {
                var constructionJobPostingGroups = new List<string>() { "ADUCONST", "CCONST", "SFCONST", "CSHELL", "THCONST", "TECONST", "TICONST", "VMDB", "VMDBHB" };
                var getJobs = _context.Jobs.AsNoTracking().Include(x => x.Schedule.Template).Include(x => x.Schedule.JobNumberNavigation).Include(x => x.Subdivision).Where(x => x.IsActive == true && x.Closed != "1" && x.Subdivision != null && x.Subdivision.CommunityStatus != "Sold Out" && constructionJobPostingGroups.Contains(x.JobPostingGroup) && (x.Schedule == null || (x.Schedule.IniSchStartDate > DateTime.Now.AddYears(-2) || x.Schedule.IniSchStartDate == null) && x.Schedule.DateToStart > DateTime.Now.AddYears(-2) || x.Schedule.DateToStart == null)).Select(x => new JobDto()
                {
                    JobNumber = x.JobNumber,
                    Subdivision = _mapper.Map<SubdivisionDto>(x.Subdivision),
                    SubdivisionId = x.SubdivisionId,
                    JobPostingGroup = x.JobPostingGroup,
                    JobSchedule = _mapper.Map<ScheduleDto>(x.Schedule) ?? new ScheduleDto() { JobNumber = x.JobNumber},
                    StickBuilingNum = x.StickBuilingNum
                }).OrderBy(x => x.JobNumber).ToList();
                //var getSchedules = _context.Schedules.Include(x => x.Template).Include(x => x.JobNumberNavigation.Subdivision).AsNoTracking().Where(x => x.IsActive == true && (x.IniSchStartDate > DateTime.Now.AddYears(-1) || x.IniSchStartDate == null) && (x.DateToStart > DateTime.Now.AddYears(-2) || x.DateToStart == null)).OrderByDescending(x => x.IniSchStartDate).ToList();
                // var returnJobSchedules = _mapper.Map<List<JobDto>>(getJobs);
                var findDefaultTHTemplate = _context.Templates.Where(x => x.TemplateName == "Standard Townhome" && x.IsActive == true).FirstOrDefault();
                var findDefaultTHFactoryFrameTemplate = _context.Templates.Where(x => x.TemplateName == "Standard Townhome - Factory Frame" && x.IsActive == true).FirstOrDefault();
                var findDefaultSFTemplate = _context.Templates.Where(x => x.TemplateName == "Standard Single Family" && x.IsActive == true).FirstOrDefault();
                var findDefaultSFFactoryFrameTemplate = _context.Templates.Where(x => x.TemplateName == "Standard Single Family - Factory Frame" && x.IsActive == true).FirstOrDefault();
                foreach (var job in getJobs) 
                {
                    var selectTemplate = new Template();
                    switch (job.JobPostingGroup)
                    {
                        case "THCONST":
                            selectTemplate = job.JobConstructionTypeId == 2 ? findDefaultTHFactoryFrameTemplate : findDefaultTHTemplate;
                            break;
                        case "TECONST":
                            selectTemplate = job.JobConstructionTypeId ==  2? findDefaultTHFactoryFrameTemplate : findDefaultTHTemplate;
                            break;
                        case "TICONST":
                            selectTemplate = job.JobConstructionTypeId == 2 ? findDefaultTHFactoryFrameTemplate : findDefaultTHTemplate;
                            break;
                        case "SFCONST":
                            selectTemplate = job.JobConstructionTypeId == 2 ? findDefaultSFFactoryFrameTemplate : findDefaultSFTemplate;
                            break;
                        default:
                            break;

                    }
                    if (job.JobSchedule != null)
                    {
                        job.JobSchedule.BoolPublished = job.JobSchedule.Published == "T";
                        job.JobSchedule.UsingDefaultTemplate = job.JobSchedule.TemplateId == null;
                        job.JobSchedule.TemplateId = job.JobSchedule.TemplateId != null ? job.JobSchedule.TemplateId : selectTemplate?.TemplateId ;
                        job.JobSchedule.Template = job.JobSchedule.Template ?? _mapper.Map<TemplateDto>(selectTemplate);
                        job.JobSchedule.JobNumberNavigation = new JobDto() { JobNumber = job.JobNumber, SubdivisionId = job.SubdivisionId };
                        job.JobSchedule.OldIniSchStartDate = job.JobSchedule.IniSchStartDate;
                        job.JobSchedule.IniSchStartDateInputRaw = job.JobSchedule.IniSchStartDate?.ToString("MM/dd/yyyy") ?? "";
                    }
                }
                return Ok(new ResponseModel<List<JobDto>>(){ Value = getJobs, Message = "Got schedules", IsSuccess = true });
            }
            catch(Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobDto>> { IsSuccess = false, Message = "Failed to get job status list", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveReleaseSchedulesExcelAsync([FromBody] List<JobDto> releaseSchedules)
        {
            try
            {
                var userName = User.Identity.Name.Split('@')[0]; 
                var jobSchedules = await _context.Schedules.Where(x => releaseSchedules.Select(x => x.JobNumber).Contains(x.JobNumber)).ToListAsync();
                foreach (var schedule in jobSchedules)
                {
                    var importedSchedule = releaseSchedules.First(x => x.JobNumber == schedule.JobNumber).JobSchedule;
                    schedule.PermitSubmitDate = importedSchedule.PermitSubmitDate;
                    schedule.PermitReceivedDate = importedSchedule.PermitReceivedDate;
                    schedule.PermitNumber = importedSchedule.PermitNumber;
                    schedule.UpdatedBy = userName;
                    schedule.UpdatedDateTime = DateTime.Now;
                }
                await _context.Schedules.BulkUpdateAsync(jobSchedules, options => options.ColumnInputExpression = x => new { x.PermitSubmitDate, x.PermitReceivedDate, x.PermitNumber, x.UpdatedBy, x.UpdatedDateTime });
                return Ok(new ResponseModel<List<JobDto>> { IsSuccess = true, Message = "Imported Permit information on release schedules", Value = releaseSchedules });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<JobDto>> { IsSuccess = false, Message = "Failed to import release schedules", Value = null });
            }
        }
    }
}
