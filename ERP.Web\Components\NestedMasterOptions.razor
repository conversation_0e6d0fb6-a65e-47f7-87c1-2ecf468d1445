﻿@inject AttributeService AttributeService
@using ERP.Data.Models

<!-- Bind SelectedOptionId (parameter in the MasterOptionsDDL components, to the Property of Attributes -->
<ERP.Web.Components.MasterOptionsDDL SelectedOptionId="@SelectedOptionId" SelectedOptionIdChanged="@MasterOptionChangedHandler"></ERP.Web.Components.MasterOptionsDDL>

@if (OptionsData == null)
{
    <p><em>Loading...</em></p>
}
else
{
    <TelerikGrid Data="@OptionsData"
                 Height="1000px"
                 RowHeight="40"
                 PageSize="40"
                 Pageable="true"
                 Class="spacer">
        <GridColumns>
            <GridColumn Field="OptionCode" Title="Code"></GridColumn>
            <GridColumn Field="OptionDesc" Title="Description"></GridColumn>
        </GridColumns>
        <DetailTemplate>
            @{
                MasterOptionDto optionData = context as MasterOptionDto;
                <ERP.Web.Components.NestedGroup MasterOptionId="@optionData.OptionId"></ERP.Web.Components.NestedGroup>
            }
        </DetailTemplate>
    </TelerikGrid>
}

@code {
    /// <summary>
    /// Properties
    /// </summary>
    public List<MasterOptionDto>? OptionsData { get; set; }
    [Parameter] public int AttributeGroupAssignmentId { get; set; }

    // Component parameter for in-sync purpose
    private int SelectedOptionId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    protected override async Task OnParametersSetAsync()
    {
        var data = await AttributeService.GetMasterOptionsFromAttributeGroupAssignmentAsync(AttributeGroupAssignmentId);
        OptionsData = data.Value;
    }

    /// <summary>
    /// Changing the dropdown will send handler event to the child component (MasterOptionsDDL)
    /// </summary>
    /// <param name="selectedValue"></param>
    /// <returns></returns>
    async Task MasterOptionChangedHandler(int selectedValue)
    {
        // Options
        SelectedOptionId = selectedValue;
        var optionsData = await AttributeService.GetOptionAttributeGroupItemsAsync(SelectedOptionId);
        OptionsData = optionsData.Value;
    }
}
