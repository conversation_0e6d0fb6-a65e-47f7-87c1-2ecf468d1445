﻿@inject SubdivisionService SubdivisionService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="300px"
               Height="400px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Update Jobs
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@JobToUpdate" OnValidSubmit="@HandleValidUpdateSubmit">
            <div class="mb-3">
                <label class="form-label">Phase</label>
                <TelerikDropDownList Data="@AllPhases"
                                     Filterable="true"
                                     FilterOperator="@StringFilterOperator.Contains"
                                     @bind-Value="JobToUpdate.Phase">
                </TelerikDropDownList>
                @if (JobToUpdate.Phase == "Add New")
                {
                    <br />
                    <TelerikTextBox @bind-Value="NewPhase" Placeholder="New Phase" />
                }
            </div>

            <div class="mb-3">
                <label class="form-label">Building Num</label>
                <TelerikDropDownList Data="@AllBuildingNums"
                                     Filterable="true"
                                     FilterOperator="@StringFilterOperator.Contains"
                                     @bind-Value="JobToUpdate.BuildingNum">
                </TelerikDropDownList>
                @if (JobToUpdate.BuildingNum == "Add New")
                {
                    <br />
                    <TelerikTextBox @bind-Value="NewBuildingNum" Placeholder="New Building Num" />
                }
            </div>

            <div class="mb-3">
                <label class="form-label">Stick</label>
                <TelerikDropDownList Data="@AllSticks"
                                     Filterable="true"
                                     FilterOperator="@StringFilterOperator.Contains"
                                     @bind-Value="JobToUpdate.StickBuilingNum">
                </TelerikDropDownList>
                @if (JobToUpdate.StickBuilingNum == "Add New")
                {
                    <br />
                    <TelerikTextBox @bind-Value="NewStick" Placeholder="New Stick Name"></TelerikTextBox>
                }
            </div>
            <br />
            <br />
            <button type="submit" class="btn btn-primary">Update</button>
            <button type="button" @onclick="CancelUpdate" class="btn btn-secondary">Cancel</button>
            <div style=@submittingStyle>Updating jobs. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public JobDto JobToUpdate { get; set; } = new JobDto();
    private string submittingStyle = "display:none";
    public List<string> AllPhases { get; set; } = new List<string> { "Add New" };
    public List<string> AllBuildingNums { get; set; } = new List<string> { "Add New" };
    public List<string> AllSticks { get; set; } = new List<string> { "Add New" };
    public string NewPhase { get; set; }
    public string NewBuildingNum { get; set; }
    public string NewStick { get; set; }
    [Parameter]
    public int? SubdivisionId { get; set; }

    [Parameter]
    public IEnumerable<JobDto> SelectedJobs { get; set; }

    [Parameter]
    public EventCallback<ResponseModel<UpdateJob>> HandleUpdateSubmit { get; set; }

    public async Task Show()
    {
        IsModalVisible = true;
        NewPhase = null;
        NewStick = null;
        NewBuildingNum = null;
        JobToUpdate.BuildingNum = null;
        JobToUpdate.Phase = null;
        JobToUpdate.StickBuilingNum = null;
        StateHasChanged();
    }
    protected override async Task OnParametersSetAsync()
    {
        LoadData();
    }
    private async Task LoadData()
    {
        if(SubdivisionId != null)
        {
            var jobsTask = SubdivisionService.GetJobsAsync((int)SubdivisionId);
            await Task.WhenAll(new Task[] { jobsTask });
            var jobs = jobsTask.Result.Value;
            AllPhases = jobs.Where(x => !string.IsNullOrWhiteSpace(x.Phase)).Select(x => x.Phase).Distinct().ToList();
            AllBuildingNums = jobs.Where(x => !string.IsNullOrWhiteSpace(x.BuildingNum)).Select(x => x.BuildingNum).Distinct().ToList();
            AllSticks = jobs.Where(x => !string.IsNullOrWhiteSpace(x.StickBuilingNum)).Select(x => x.StickBuilingNum).Distinct().ToList();
            AllPhases.Insert(0, "Add New");
            AllBuildingNums.Insert(0, "Add New");
            AllSticks.Insert(0, "Add New");
        }        
    }
    private async void HandleValidUpdateSubmit()
    {
        var jobsList = SelectedJobs.Select(x => x.JobNumber).ToList();
        UpdateJob updateJob = new UpdateJob()
            {
                JobNumber = "",
                Phase = JobToUpdate.Phase != null && JobToUpdate.Phase != "" ? (JobToUpdate.Phase == "Add New" ? NewPhase : JobToUpdate.Phase) : null,
                BuildingNum = JobToUpdate.BuildingNum != null && JobToUpdate.BuildingNum != "" ? (JobToUpdate.BuildingNum == "Add New" ? NewBuildingNum: JobToUpdate.BuildingNum) : null,
                Stick = JobToUpdate.StickBuilingNum != null && JobToUpdate.StickBuilingNum != "" ? (JobToUpdate.StickBuilingNum == "Add New" ? NewStick : JobToUpdate.StickBuilingNum) : null,
                JobsToUpdate = jobsList
            };
        var updateResponse = await SubdivisionService.UpdateFieldsInLotsAsync(updateJob);
        await HandleUpdateSubmit.InvokeAsync(updateResponse);
    }
    async void CancelUpdate()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;
    }
}

