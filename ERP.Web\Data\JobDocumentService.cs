﻿using Azure.Core;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.ExtendedProperties;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using NLog;
using System.Text;
using Telerik.Blazor.Components.FileSelect;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class JobDocumentService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public JobDocumentService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }


        public async Task<ResponseModel<string>> GetTokenAsync()
        {
            var scopes = _configuration.GetSection("DownstreamApi:ApiScopes").Value;
            var scopesArray = new string[] { scopes};
            var token = await _tokenAcquisition.GetAccessTokenForUserAsync(scopesArray, tokenAcquisitionOptions: new TokenAcquisitionOptions { ForceRefresh = true});
            //ForceRefresh = true ignores the token cache
            //this is wrong, but seems to fix errors about multiple tokens in the cache
            return new ResponseModel<string>() { Value = token, IsSuccess = true };
        }
        public async Task<ResponseModel<string>> GetScopeAsync()
        {
            var scopes = _configuration.GetSection("DownstreamApi:ApiScopes").Value;
           
            return new ResponseModel<string>() { Value = scopes, IsSuccess = true };
        }
        public async Task<ResponseModel<List<DoctypeDto>>> GetDocumentTypesAsync()
        {
            var documents = new ResponseModel<List<DoctypeDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/jobdocument/GetDocumentTypes");
                var responseString = await response.Content.ReadAsStringAsync();
                documents = JsonConvert.DeserializeObject<ResponseModel<List<DoctypeDto>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return documents;
        }
        public string GetMimeType(string fileName)
        {
            try
            {
                var provider = new FileExtensionContentTypeProvider();
                string DefaultContentType = "application/octet-stream";
                if (!provider.TryGetContentType(fileName, out string contentType))
                {
                    contentType = DefaultContentType;
                }
                return contentType;
            }          
            catch (Exception ex)
            {
                // var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                _logger.Error(ex);
#endif
            }

            return null;
        }
        public async Task<ResponseModel<List<JobAttachmentDto>>> GetJobDocumentsAsync(string jobNumber)
        {
            var documents = new ResponseModel<List<JobAttachmentDto>>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
                   "DownstreamApi",
                   options => options.RelativePath = $"api/jobdocument/ListJobDocuments/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                documents = JsonConvert.DeserializeObject<ResponseModel<List<JobAttachmentDto>>>(responseString);

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return documents;
        }

        public async Task<ResponseModel<JobAttachmentDto>> AddJobDocumentAsync(JobAttachmentDto document)
        {
            var returnDocument = new ResponseModel<JobAttachmentDto>();
            try
            {

                returnDocument = await _downstreamAPI.PostForUserAsync<JobAttachmentDto, ResponseModel<JobAttachmentDto>>(
                            "DownstreamApi", document,
                             options =>
                             {
                                 options.RelativePath = "api/jobdocument/addjobdocument/";
                             });


            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return returnDocument;
        }
        public async Task<ResponseModel<JobAttachmentDto>> UpdateJobDocumentAsync(JobAttachmentDto document)
        {
            var returnDocument = new ResponseModel<JobAttachmentDto>();
            try
            {

                returnDocument = await _downstreamAPI.PutForUserAsync<JobAttachmentDto, ResponseModel<JobAttachmentDto>>(
                            "DownstreamApi", document,
                             options =>
                             {
                                 options.RelativePath = "api/jobdocument/updatejobdocument/";
                             });


            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return returnDocument;
        }
        public async Task<ResponseModel<JobAttachmentDto>> DeleteJobDocumentAsync(JobAttachmentDto document)
        {
            var returnDocument = new ResponseModel<JobAttachmentDto>();
            try
            {

                returnDocument = await _downstreamAPI.PutForUserAsync<JobAttachmentDto, ResponseModel<JobAttachmentDto>>(
                            "DownstreamApi", document,
                             options =>
                             {
                                 options.RelativePath = "api/jobdocument/deletejobdocument/";
                             });


            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return returnDocument;
        }

        public async Task<ResponseModel<byte[]>> DownloadDocumentAsync(int jobAttachmentId)
        {
            try
            {

                var response = await _downstreamAPI.PostForUserAsync<int, ResponseModel<byte[]>>(
                           "DownstreamApi", jobAttachmentId,
                            options => {
                                options.RelativePath = "api/jobdocument/DownloadDocument/";
                            });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<byte[]>() {  Value = new byte[0], IsSuccess = true };

        }

    }
}
