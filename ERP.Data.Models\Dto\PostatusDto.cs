﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class PostatusDto : IMapFrom<Postatus>
{
    public int PostatusId { get; set; }

    public string? Postatus1 { get; set; } 

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

   // public byte[] RecordTimeStamp { get; set; } = null!;

  //  public virtual ICollection<Poheader> Poheaders { get; set; } = new List<Poheader>();
}
