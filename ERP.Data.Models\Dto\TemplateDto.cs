﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class TemplateDto : IMapFrom<Template>
{
    public int TemplateId { get; set; }

    public string? TemplateName { get; set; }

    public string? Description { get; set; }

    public string? UserCreated { get; set; }

    public DateTime? DateCreated { get; set; }

    public int? DivId { get; set; }

    public DateTime Createddatetime { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public string? UpdatedBy { get; set; }

    //public byte[] RecordTimeStamp { get; set; } = null!;

    //public virtual ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();

    //public virtual ICollection<TemplateMilestone> TemplateMilestones { get; set; } = new List<TemplateMilestone>();

    //public virtual ICollection<TemplateSactivity> TemplateSactivities { get; set; } = new List<TemplateSactivity>();
}
