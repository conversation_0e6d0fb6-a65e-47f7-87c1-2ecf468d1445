﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ScheduleMasterSchedule
{
    public int ScheduleMasterScheduleId { get; set; }

    public int SchedulePhaseId { get; set; }

    public int ScheduleId { get; set; }

    public int ScheduleMasterScheduleSeq { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual Schedule Schedule { get; set; } = null!;

    public virtual SchedulePhase SchedulePhase { get; set; } = null!;
}
