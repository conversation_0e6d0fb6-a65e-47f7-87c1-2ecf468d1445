﻿@page "/scheduletemplates"
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ConstructionDirector, ConstructionManager")]
@inject ScheduleService ScheduleService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JSRuntimeService

@using ERP.Data.Models.Dto
@using ERP.Data.Models
@using Telerik.DataSource.Extensions
@using System.Collections.ObjectModel

<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }
    /*.mytreeclass .k-drag-cell {
            display:none;
        }*/
/*    .mytreeclass .k-table-th.k-header.k-drag-cell{
        visibility: collapse;
    }
    .mytreeclass .k-drag-cell.k-touch-action-none{
        visibility: collapse;

    }*/
    .mytreeclass .k-drag-col{
        visibility: collapse;
    }
    .mytreeclass .k-header {
        min-width: 200px;
    }

    .mytreeclass  {
       overflow-x: auto;
    }
    .k-drag-clue {
        color: white;
    }

    .k-icon.k-icon-with-modifier {
        display: none;
    }

    .SubdivisionTable .k-hierarchy-cell {
        display: none;
    }

    .k-splitbar {
        width: 15px;
        color: black;
        padding-left:5px;
        padding-right:5px;
    }

    .pane {
        padding: 8px;
        overflow-x: auto;
    }

    .k-splitter .k-pane {
        overflow-x: auto !important;
    }

    .k-splitter-horizontal{
        overflow-x: auto !important;
    }
    .page-wrapper .page-content{     
       /* padding-right: 100px !important;
        margin-right: 100px !important;*/ 
/*        width: 95% !important;*/
    }
    .page-wrapper{
       /* padding-right: 100px !important;
        margin-right: 100px !important;
            width: 80% !important;*/
    }

    .k-table-td { /*these settings are to prevent the 0 width columns from affecting the column height*/
        padding: 4px !important;
        font-size: 11px;
        height: 30px !important;
        white-space: nowrap !important; 
        overflow: visible;
    }

    .column-text-wrapping {
        white-space: normal;
        word-wrap: break-word;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>

<div class="card" style="background-color:#2e5771">
    <div class="card-body" style="padding:0.5rem">
        <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Schedule Templates</h7>
    </div>
</div>
<TelerikLoaderContainer Visible="@IsLoading" Text="Please wait..." />
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<TelerikTooltip TargetSelector=".tooltip-target" />
<div class="row">
    <TelerikSplitter Width="100%" Height="100%" Orientation="@SplitterOrientation.Horizontal">
        <SplitterPanes>
            <SplitterPane Size="30%" Min="15%" Max="25%" Collapsible="true">
                <div class="pane">
                    <h4 class="card-header">Activites/Milestones</h4>
                    @if (ActivitiesMilestoneData == null)
                    {
                        <p><em>Loading...</em></p>
                    }
                    else
                    {
                        <TelerikTreeList Data="@ActivitiesMilestoneData"
                        SelectionMode="@TreeListSelectionMode.Multiple"
                        Pageable="true"
                        PageSize="50"
                        Height="1200px"
                        IdField="Id"
                        ItemsField="Children"
                        @ref="@ActivitiesMilestonesTree"
                        OnStateInit="((TreeListStateEventArgs<ScheduleTemplateTreeModel> args) => OnStateInitHandler(args))"
                        RowDraggable="true"
                        OnRowDrop="@((TreeListRowDropEventArgs<ScheduleTemplateTreeModel> args) => OnMilestoneActivityRowDropHandler(args))"
                        Width="100%">
                            <TreeListColumns>
                                <TreeListCheckboxColumn Width="40px" SelectChildren="true">
                                </TreeListCheckboxColumn>
                                <TreeListColumn Field="Id" Title="Milestone/Activity" Expandable="true">
                                    <Template>
                                        @{
                                            var item = context as ScheduleTemplateTreeModel;
                                            if (item.FolderName != null)
                                            {
                                                @($"{item.FolderName}")
                                            }
                                            else if (item.Sactivity != null)
                                            {
                                                @($"{item.Sactivity.ActivityName}")
                                            }
                                            else if (item.Milestone != null)
                                            {
                                                @($"{item.Milestone.MilestoneName}")
                                            }
                                        }
                                    </Template>
                                </TreeListColumn>
                                <TreeListColumn Field="MilestoneName" Visible="true" Width="0px"></TreeListColumn>
                                <TreeListColumn Field="ActivityName" Visible="true" Width="0px"></TreeListColumn>
                            </TreeListColumns>
                            <TreeListSettings>
                                <TreeListRowDraggableSettings DragClueField="DragClue"></TreeListRowDraggableSettings>
                            </TreeListSettings>
                            <TreeListToolBarTemplate>
                                <TreeListSearchBox />
                            </TreeListToolBarTemplate>
                        </TelerikTreeList>

                    }
                </div>
            </SplitterPane>
            <SplitterPane Collapsible="false">
                <div class="pane">
                    <h4 class="card-header mb-3">Template/Schedules Created from Template</h4>
                    @if (AvailableTemplates == null)
                    {
                        <p><em>Loading...</em></p>
                    }
                    else
                    {
                        <TelerikDropDownList Data="@AvailableTemplates"
                        @bind-Value="@SelectedTemplate.TemplateId"
                        TextField="TemplateName"
                        ValueField="TemplateId"
                        DefaultText="Select Template"
                        Width="400px"
                        PageSize="40"
                        Filterable="true"
                        FilterOperator="StringFilterOperator.Contains"
                        ItemHeight="30"
                        OnChange="@OnTemplateSelectedHandler"
                        ScrollMode="@DropDownScrollMode.Virtual">
                            <DropDownListSettings>
                                <DropDownListPopupSettings Height="600px"></DropDownListPopupSettings>
                            </DropDownListSettings>

                        </TelerikDropDownList>
                    }
                    <br />
                    <br />
                    @if (SelectedTemplate?.TemplateName == null)
                    {
                        <div style="width:200px">
                            <TelerikButton OnClick="AddTemplate" Icon="@FontIcon.Plus" Class="k-button-add">New Template</TelerikButton>
                        </div>
                    }
                    else
                    {
                        @if (SelectedTemplateData == null)
                        {
                            <p><em>Select a template to see details</em></p>
                        }
                        else
                        {
                            <div>
                                <TelerikTabStrip>
                                    <TabStripTab Title="Template">
                                        <div>
                                            <TelerikTreeList Data="@SelectedTemplateData"
                                            SelectionMode="@TreeListSelectionMode.Multiple"
                                            OnStateInit="@((TreeListStateEventArgs<ScheduleTemplateTreeModel> args) => OnStateInitHandler(args))"
                                            IdField="Id"
                                            Id="WorksheetTree"
                                            EditMode="@TreeListEditMode.Incell"
                                            ItemsField="Children"
                                            Resizable="true"
                                            Width="100%"
                                            Height="1000px"
                                            RowDraggable="true"
                                            OnUpdate="@UpdateTemplate"
                                            OnDelete="@DeleteActivity"
                                            OnRowDrop="@((TreeListRowDropEventArgs<ScheduleTemplateTreeModel> args) => OnTemplateRowDropHandler(args))"
                                            @ref="@TemplateTreeList">
                                                <TreeListColumns>
                                                    <TreeListColumn Field="SearchTags" Width="0px" />
                                                    <TreeListColumn Field="Id" Title="Milestone/Activity" Expandable="true" Editable="false" Width="250px">
                                                        <Template>
                                                            @{
                                                                var item = context as ScheduleTemplateTreeModel;
                                                                if (item.FolderName != null)
                                                                {
                                                                    @($"{item.FolderName}")
                                                                }
                                                                else if (item.Sactivity != null)
                                                                {
                                                                    @($"{item.Sactivity.ActivityName}")
                                                                }
                                                                else if (item.Milestone != null)
                                                                {
                                                                    @($"{item.Milestone.MilestoneName}")
                                                                }
                                                            }
                                                        </Template>
                                                    </TreeListColumn>
                                                    <TreeListColumn Field="TemplateSactivity.Seq" Title="Seq" Editable="false" Width="50px" />
                                                    <TreeListColumn Field="Predecessors" Title="Predecessor">
                                                        <EditorTemplate>
                                                            @{
                                                                ItemToEdit = context as ScheduleTemplateTreeModel;
                                                                var priorActivities = SelectedTemplateData.SelectMany(x => x.Children.Where(x => x.TemplateSactivity != null /* && x.TemplateSactivity.Seq < ItemToEdit.TemplateSactivity.Seq */)).Select(x => x.Sactivity);
                                                                if (ItemToEdit.TemplateSactivity != null)
                                                                {
                                                                    <TelerikMultiSelect Data="@priorActivities"
                                                                    TextField="ActivityName"
                                                                    ValueField="SactivityId"
                                                                    Filterable="true"
                                                                    FilterOperator="StringFilterOperator.Contains"
                                                                    @bind-Value="ItemToEdit.PredecessorIds">
                                                                    </TelerikMultiSelect>
                                                                }
                                                            }

                                                        </EditorTemplate>
                                                    </TreeListColumn>
                                                    <TreeListColumn Field="TemplateSactivity.Duration" Title="Duration" />
                                                    <TreeListColumn Field="TemplateSactivity.LagTime" Title="Lag Time" />
                                                    <TreeListColumn Field="PurchasingActivities" Title="Purchasing Activities" Width="250px">
                                                        <Template>
                                                            @{
                                                                var item = context as ScheduleTemplateTreeModel;
                                                                <div class="column-text-wrapping">
                                                                    @item.PurchasingActivities
                                                                </div>
                                                            }
                                                        </Template>
                                                    </TreeListColumn>
                                                    <TreeListCommandColumn>
                                                        <TreeListCommandButton Title="Delete" Command="Delete" Class="tooltip-target k-button-danger" Icon="@FontIcon.Trash"></TreeListCommandButton>
                                                    </TreeListCommandColumn>
                                                </TreeListColumns>
                                                <TreeListSettings>
                                                    <TreeListRowDraggableSettings DragClueField="Sactivity.ActivityName"></TreeListRowDraggableSettings>
                                                </TreeListSettings>
                                                <TreeListToolBarTemplate>
                                                    <TreeListSearchBox DebounceDelay="200"> </TreeListSearchBox>
                                                    <TreeListCommandButton Command="AddTemplate" OnClick="@AddTemplate" Title="Add New Template" Icon="@FontIcon.Plus" Class="tooltip-target k-button-add"></TreeListCommandButton>
                                                    <TreeListCommandButton Command="SaveAsTemplate" OnClick="@SaveAsTemplate" Title="Save As Template" Icon="@FontIcon.Save" Class="tooltip-target"></TreeListCommandButton>
                                                    <TreeListCommandButton Command="DeleteTemplate" OnClick="@DeleteTemplate" Title="Delete Template" Icon="@FontIcon.Trash" Class=" tooltip-target k-button-danger"></TreeListCommandButton>
                                                    <TreeListCommandButton Command="CreateSchedule" OnClick="@NewScheduleFromTemplate" Title="New schedule from template" Icon="@FontIcon.Plus" Class=" tooltip-target k-button-add"></TreeListCommandButton>
                                                    <TreeListCommandButton Title="Export To Excel" Command="ExcelExport" OnClick="@ExportScheduleTemplate" Icon="@FontIcon.FileExcel" Class="tooltip-target k-button-info"></TreeListCommandButton>
                                                </TreeListToolBarTemplate>
                                            </TelerikTreeList>
                                        </div>
                                    </TabStripTab>
                                    <TabStripTab Title="Schedules Created from Template">
                                        <div>
                                            <TelerikGrid Data="SchedulesForTemplateData"
                                            @bind-SelectedItems="@SelectedJobsToUpdate"
                                            SelectionMode="GridSelectionMode.Multiple"
                                            FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                            FilterMenuType="@FilterMenuType.CheckBoxList"
                                            ConfirmDelete="true"
                                            OnDelete="DeleteSchedule"
                                            Sortable="true">
                                                <GridColumns>
                                                    <GridCheckboxColumn SelectAllMode="GridSelectAllMode.All" />
                                                    <GridColumn Field="JobNumberNavigation.Subdivision.SubdivisionName" Title="Subdivision" />
                                                    <GridColumn Field="JobNumber" Title="Job Number" />
                                                    <GridCommandColumn>
                                                        @{
                                                            var item = context as ScheduleDto;
                                                            if (item.IniSchApproved != true || item.JobNumber.Contains("TEST"))//no delete if approved, except test job
                                                            {
                                                                <GridCommandButton Title="Delete" Command="Delete" Icon="FontIcon.Trash"></GridCommandButton>
                                                            }
                                                        }

                                                    </GridCommandColumn>
                                                </GridColumns>
                                                <GridToolBarTemplate>
                                                    <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                                    <GridCommandButton Command="UpdateSchedulesFromTemplate" OnClick="@UpdateFromTemplate" Title="Update Schedules From Template" Icon="@FontIcon.ArrowRotateCw" Class=" tooltip-target k-button-success"></GridCommandButton>
                                                </GridToolBarTemplate>
                                            </TelerikGrid>
                                        </div>
                                    </TabStripTab>
                                </TelerikTabStrip>
                            </div>
                        }
                    }
                </div>
            </SplitterPane>
        </SplitterPanes>
    </TelerikSplitter>

</div>
<ERP.Web.Components.CreateTemplate @ref="AddTemplateModal" SaveAsTemplate="@SelectedTemplate" SaveAs="SaveAs" HandleAddSubmit="@HandleValidAddTemplateSubmit"></ERP.Web.Components.CreateTemplate>
<ERP.Web.Components.CreateScheduleFromTemplate @ref="AddScheduleFromTemplateModal" SelectedTemplate="@SelectedTemplate.TemplateId" HandleAddSubmit="@HandleValidAddScheduleFromTemplateSubmit"></ERP.Web.Components.CreateScheduleFromTemplate>
@code {

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    private TelerikTreeList<ScheduleTemplateTreeModel>? TemplateTreeList { get; set; }
    private TelerikTreeList<ScheduleTemplateTreeModel>? ActivitiesMilestonesTree { get; set; }
    public List<ScheduleTemplateTreeModel>? SelectedTemplateData { get; set; }
    public List<ScheduleTemplateTreeModel>? ActivitiesMilestoneData { get; set; }    
    public List<TemplateDto>? AvailableTemplates { get; set; }
    public List<SactivityDto>? AllSactivities { get; set; }
    public ScheduleTemplateTreeModel? ItemToEdit { get; set; }
    public TemplateDto? SelectedTemplate { get; set; } = new TemplateDto();
    public List<ScheduleDto>? SchedulesForTemplateData { get; set; }
    public IEnumerable<ScheduleDto>? SelectedJobsToUpdate { get; set; } = Enumerable.Empty<ScheduleDto>();
    public int? PreviousSelectedTemplateId { get; set; }
    protected ERP.Web.Components.CreateTemplate? AddTemplateModal { get; set; }
    protected ERP.Web.Components.CreateScheduleFromTemplate? AddScheduleFromTemplateModal { get; set; }
    public bool SaveAs { get; set; }

    private bool IsLoading { get; set; } = false;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var availableTemplatesTask = ScheduleService.GetTemplatesAsync();
        var milestonesTask = ScheduleService.GetMilestonesAsync();
        var activitiesTask = ScheduleService.GetSactivitiesAsync();
        await Task.WhenAll(new Task []{availableTemplatesTask, milestonesTask, activitiesTask});
        AvailableTemplates = availableTemplatesTask.Result.Value;
        var allMilestones = milestonesTask.Result.Value;
        AllSactivities = activitiesTask.Result.Value;
        ActivitiesMilestoneData = new List<ScheduleTemplateTreeModel>()
        {
            new ScheduleTemplateTreeModel()
            {
                Id = Guid.NewGuid(),
                FolderName = "Activities",
                HasChildren = true,
                Children = AllSactivities.Select(x => new ScheduleTemplateTreeModel()
                {
                    Id = Guid.NewGuid(),
                    HasChildren = false,
                    Sactivity = x,
                    ActivityName = x.ActivityName,
                    DragClue = x.ActivityName,
                }).ToList()
            },
            new ScheduleTemplateTreeModel()
            {                
                Id = Guid.NewGuid(),
                FolderName = "Milestones",
                HasChildren = true,
                Children = allMilestones.Select(x => new ScheduleTemplateTreeModel()
                {
                    Id = Guid.NewGuid(),
                    HasChildren = false,
                    Milestone = x,
                    MilestoneName = x.MilestoneName,
                    DragClue = x.MilestoneName
                }).ToList()
            },
        };
    }

    async Task OnStateInitHandler(TreeListStateEventArgs<ScheduleTemplateTreeModel> args)
    {
        var collapsedItemsState = new TreeListState<ScheduleTemplateTreeModel>()
        {
            //collapse all items in the TreeList upon initialization of the state
                ExpandedItems = new List<ScheduleTemplateTreeModel>()
        };
        args.TreeListState = collapsedItemsState;
    }

    protected async Task OnTemplateSelectedHandler(object theUserInput)
    {        
        int currValue = (int)theUserInput;
        if(currValue != 0 && currValue != PreviousSelectedTemplateId)
        {
            //TODO: loading indicator
            //TODO: something goes wrong here sometimes
            PreviousSelectedTemplateId = currValue;
            SelectedTemplate.TemplateId = currValue;
            SelectedTemplate.TemplateName = AvailableTemplates.FirstOrDefault(x => x.TemplateId == currValue).TemplateName;
            SelectedTemplateData = (await ScheduleService.GetTemplateAsync(currValue)).Value;
            var schedulesFromTemplateResponse = await ScheduleService.GetSchedulesForTemplateAsync(currValue);
            SchedulesForTemplateData = schedulesFromTemplateResponse.Value;

            if (schedulesFromTemplateResponse.IsSuccess == false)
            {
                ShowSuccessOrErrorNotification(schedulesFromTemplateResponse.Message, false);
            }

            StateHasChanged();
        }
    }
    private async Task OnMilestoneActivityRowDrop(List<ScheduleTemplateTreeModel> items, TreeListRowDropEventArgs<ScheduleTemplateTreeModel> args)
    {
        // foreach (var item in args.Items)
        // {
        //     RemoveChildRecursive(items, item);
        // }
        //TODO: what if multiple rows dropped?
        var destinationData = args.DestinationTreeList == ActivitiesMilestonesTree ? ActivitiesMilestoneData : SelectedTemplateData;

        if(args.DestinationTreeList == TemplateTreeList)
        {
            foreach (var item in args.Items)
            {
                var getTreeItem = item as ScheduleTemplateTreeModel;
                if(getTreeItem.Milestone != null)//item being dropped is a milestone
                {
                    var destinationItemIndex = SelectedTemplateData.IndexOf(args.DestinationItem);
                    if (args.DropPosition == TreeListRowDropPosition.After ||args.DropPosition == TreeListRowDropPosition.Over|| args.DestinationItem == null)
                    {
                        destinationItemIndex++;                   
                    }
                    //TODO: this will break if the destination item is an activity, then need to find its parent
                    getTreeItem.TemplateMilestone = new TemplateMilestoneDto() { TemplateId = SelectedTemplate.TemplateId, Seq = destinationItemIndex + 1 };
                    var response = await ScheduleService.AddMilestoneToTemplateAsync(getTreeItem);
                    if (response.IsSuccess)
                    {
                        var responseMilestone = response.Value;
                        responseMilestone.MilestoneName = getTreeItem.MilestoneName;
                        SelectedTemplateData.Insert(destinationItemIndex, responseMilestone);
                        //adust the seq numbers of everything
                        foreach (var milestoneItem in SelectedTemplateData)
                        {
                            var findSeq = SelectedTemplateData.IndexOf(milestoneItem);
                            milestoneItem.TemplateMilestone.Seq = findSeq + 1; //adding one because list is 0 index
                        }
                        var milestoneDataToUpdate = SelectedTemplateData.Select(x => x.TemplateMilestone).ToList();
                        await ScheduleService.UpdateTemplateMilestonesSeqAsync(milestoneDataToUpdate);
                        StateHasChanged();

                        TemplateTreeList.Rebind();
                    }
                    ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
                }
                if (getTreeItem.Sactivity != null)//item being dropped is an activity
                {
                    var destItem = args.DestinationItem as ScheduleTemplateTreeModel;
                    if(destItem.TemplateMilestone != null && destItem.TemplateSactivity == null)//dropped onto a milestone
                    {
                        getTreeItem.TemplateMilestone = new TemplateMilestoneDto() { TemplateId = SelectedTemplate.TemplateId, TemplateMid = destItem.TemplateMilestone.TemplateMid };
                        getTreeItem.TemplateSactivity = new TemplateSactivityDto()
                            {
                                PredIds = new List<int>() {  }
                            };

                        var response = await ScheduleService.AddActivityToTemplateAsync(getTreeItem);
                        if (response.IsSuccess)
                        {
                            var responseActivity = response.Value;
                            responseActivity.ActivityName = getTreeItem.ActivityName;
                            destItem.HasChildren = true;
                            if (destItem.Children == null)
                            {
                                destItem.Children = new List<ScheduleTemplateTreeModel>();
                            }
                            destItem.Children.Add(responseActivity);
                            //adust the seq numbers of everything
                            foreach (var activityItem in SelectedTemplateData.SelectMany(x => x.Children).ToList())
                            {
                                var findSeq = SelectedTemplateData.SelectMany(x => x.Children).IndexOf(activityItem);
                                activityItem.TemplateSactivity.Seq = findSeq + 1; //adding one because list is 0 index
                            }
                            var activityDataToUpdate = SelectedTemplateData.SelectMany(x => x.Children).Select(x => x.TemplateSactivity).ToList();
                            await ScheduleService.UpdateTemplateActivitiesSeqAsync(activityDataToUpdate);
                            StateHasChanged();
                            TemplateTreeList.Rebind();
                        }
                        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
                    }
                    else if(destItem.TemplateSactivity != null)//dropped onto an activity
                    {
                        var findParent = SelectedTemplateData.SingleOrDefault(x => x.Children != null && x.Children.Select(x => x.Id).Contains(destItem.Id));
                        if(findParent != null)
                        {
                            var destinationItemIndex = findParent.Children.IndexOf(args.DestinationItem);
                            if (args.DropPosition == TreeListRowDropPosition.After || args.DropPosition == TreeListRowDropPosition.Over)
                            {
                                destinationItemIndex++;
                                getTreeItem.Sactivity.Seq = destItem.TemplateSactivity.Seq + 1;
                                getTreeItem.TemplateSactivity = new TemplateSactivityDto()
                                {
                                    PredIds = new List<int>() { destItem.TemplateSactivity.SactivityId }
                                };
                            }
                            else if(args.DropPosition == TreeListRowDropPosition.Before)
                            {
                                getTreeItem.Sactivity.Seq = destItem.TemplateSactivity.Seq - 1;
                                var findPrev = findParent.Children.FirstOrDefault(x => x.TemplateSactivity.Seq == getTreeItem.Sactivity.Seq);
                                getTreeItem.TemplateSactivity = new TemplateSactivityDto()
                                    {
                                        PredIds = findPrev != null ? new List<int>() { findPrev.TemplateSactivity.SactivityId } : new List<int>() { }
                                    };
                            }

                            getTreeItem.TemplateMilestone = findParent.TemplateMilestone;

                            var response = await ScheduleService.AddActivityToTemplateAsync(getTreeItem);
                            if (response.IsSuccess)
                            {
                                var responseActivity = response.Value;
                                responseActivity.ActivityName = getTreeItem.ActivityName;

                                findParent.Children.Insert(destinationItemIndex, responseActivity);

                                //adust the seq numbers of everything
                                foreach (var activityItem in SelectedTemplateData.SelectMany(x => x.Children).ToList())
                                {
                                    var findSeq = SelectedTemplateData.SelectMany(x => x.Children).IndexOf(activityItem);
                                    activityItem.TemplateSactivity.Seq = findSeq + 1; //adding one because list is 0 index
                                }
                                var activityDataToUpdate = SelectedTemplateData.SelectMany(x => x.Children).Select(x => x.TemplateSactivity).ToList();
                                await ScheduleService.UpdateTemplateActivitiesSeqAsync(activityDataToUpdate);
                                StateHasChanged();
                                TemplateTreeList.Rebind();
                            }
                            ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
                        }
                    }
                }
            }
        }
    }
    private async Task OnRowDropFromTemplate(List<ScheduleTemplateTreeModel> items, TreeListRowDropEventArgs<ScheduleTemplateTreeModel> args)
    {
        //This is for re-ordering the activities or milestones
        //TODO: error or warning if moving item before its predecessor
        var item = args.Item as ScheduleTemplateTreeModel;
        if(item.TemplateMilestone != null && item.TemplateSactivity == null)
        {
            //milestone
            //if destination item is a activity, drop below that activities parent
            var destinatationItem = args.DestinationItem as ScheduleTemplateTreeModel;
            if(destinatationItem.TemplateSactivity != null)
            {
                SelectedTemplateData.Remove(args.Item);
                var findDesinationParent = SelectedTemplateData.FirstOrDefault(x => x.Children.Contains(args.DestinationItem));
                var findIndex = SelectedTemplateData.IndexOf(findDesinationParent);
                SelectedTemplateData.Insert(findIndex + 1, args.Item);
                foreach (var milestone in SelectedTemplateData.ToList())
                {
                    var findSeq = SelectedTemplateData.IndexOf(milestone);
                    milestone.TemplateMilestone.Seq = findSeq + 1; //adding one because list is 0 index
                }
                var milestoneDataToUpdate = SelectedTemplateData.Select(x => x.TemplateMilestone).ToList();
                await ScheduleService.UpdateTemplateMilestonesSeqAsync(milestoneDataToUpdate);               
            }
            else
            {
                SelectedTemplateData.Remove(args.Item);
                var destinationItemIndex = SelectedTemplateData.IndexOf(args.DestinationItem);
                if (args.DropPosition == TreeListRowDropPosition.After)
                {
                    destinationItemIndex++;
                }
                SelectedTemplateData.Insert(destinationItemIndex, args.Item);
                foreach (var milestone in SelectedTemplateData.ToList())
                {
                    var findSeq = SelectedTemplateData.IndexOf(milestone);
                    milestone.TemplateMilestone.Seq = findSeq + 1; //adding one because list is 0 index
                }
                var milestoneDataToUpdate = SelectedTemplateData.Select(x => x.TemplateMilestone).ToList();
                await ScheduleService.UpdateTemplateMilestonesSeqAsync(milestoneDataToUpdate);
            }
            foreach (var child in SelectedTemplateData.SelectMany(x => x.Children).ToList())
            {
                var findSeq = SelectedTemplateData.SelectMany(x => x.Children).IndexOf(child);
                child.TemplateSactivity.Seq = findSeq + 1; //adding one because list is 0 index
            }
            var activityDataToUpdate = SelectedTemplateData.SelectMany(x => x.Children).Select(x => x.TemplateSactivity).ToList();
            await ScheduleService.UpdateTemplateActivitiesSeqAsync(activityDataToUpdate);
        }
        else if(item.TemplateSactivity != null)
        {
            var initialItemIndex1 = SelectedTemplateData.IndexOf(args.Item);
            var initialItemIndex = SelectedTemplateData.SelectMany(x => x.Children).IndexOf(args.Item);
            // SelectedTemplateData.Remove(args.Item);
            var findSourceParent = SelectedTemplateData.FirstOrDefault(x => x.Children.Contains(args.Item));
            findSourceParent.Children.Remove(args.Item);

            var destinatationItem = args.DestinationItem as ScheduleTemplateTreeModel;
            if(destinatationItem.TemplateSactivity != null)
            {
                var findDesinationParent = SelectedTemplateData.FirstOrDefault(x => x.Children.Contains(args.DestinationItem));
                //var destinationItemIndex = SelectedTemplateData.SelectMany(x => x.Children).IndexOf(args.DestinationItem);
                var destinationItemIndex = findDesinationParent.Children.IndexOf(args.DestinationItem);
                if (args.DropPosition == TreeListRowDropPosition.After)
                {
                    destinationItemIndex++;
                }
                if (findDesinationParent != null)
                {
                    findDesinationParent.Children.Insert(destinationItemIndex, args.Item);
                }
                foreach (var child in SelectedTemplateData.SelectMany(x => x.Children).ToList())
                {
                    var findSeq = SelectedTemplateData.SelectMany(x => x.Children).IndexOf(child);
                    child.TemplateSactivity.Seq = findSeq + 1; //adding one because list is 0 index
                }
                if (findSourceParent != findDesinationParent)
                {
                    //if sourceparent now has no children, make it not expandable
                    if (!findSourceParent.Children.Any())
                    {
                        findSourceParent.HasChildren = false;
                    }
                    args.Item.TemplateSactivity.TemplateMid = findDesinationParent.TemplateMilestone.TemplateMid;//Item moved to different milestone, update the milestoneid
                }
                var activityDataToUpdate = SelectedTemplateData.SelectMany(x => x.Children).Select(x => x.TemplateSactivity).ToList();
                await ScheduleService.UpdateTemplateActivitiesSeqAsync(activityDataToUpdate);
            }
            else
            {
                if (!findSourceParent.Children.Any())
                {
                    findSourceParent.HasChildren = false;
                }
                destinatationItem.Children.Add(args.Item);
                destinatationItem.HasChildren = true;
                args.Item.TemplateSactivity.TemplateMid = destinatationItem.TemplateMilestone.TemplateMid;//Item moved to different milestone, update the milestoneid
                foreach (var child in SelectedTemplateData.SelectMany(x => x.Children).ToList())
                {
                    var findSeq = SelectedTemplateData.SelectMany(x => x.Children).IndexOf(child);
                    child.TemplateSactivity.Seq = findSeq + 1; //adding one because list is 0 index
                }
                var activityDataToUpdate = SelectedTemplateData.SelectMany(x => x.Children).Select(x => x.TemplateSactivity).ToList();
                await ScheduleService.UpdateTemplateActivitiesSeqAsync(activityDataToUpdate);
            }            
        }
        else
        {
            //there was nothing in the tree? 
            //TODO: fix so can add a milestone but not an activity if nothing in the tree
            foreach (var child in SelectedTemplateData.SelectMany(x => x.Children).ToList())
            {
                var findSeq = SelectedTemplateData.SelectMany(x => x.Children).IndexOf(child);
                child.TemplateSactivity.Seq = findSeq + 1; //adding one because list is 0 index
            }
            var activityDataToUpdate = SelectedTemplateData.SelectMany(x => x.Children).Select(x => x.TemplateSactivity).ToList();
            await ScheduleService.UpdateTemplateActivitiesSeqAsync(activityDataToUpdate);
        }
    }
    private void OnMilestoneActivityRowDropHandler(TreeListRowDropEventArgs<ScheduleTemplateTreeModel> args)
    {     
        //this one should take items in the milestone/activity list and put them in the template
        OnMilestoneActivityRowDrop(SelectedTemplateData, args);
    }
    private void OnTemplateRowDropHandler(TreeListRowDropEventArgs<ScheduleTemplateTreeModel> args)
    {
        //this one needs to change to reorder the template
        OnRowDropFromTemplate(SelectedTemplateData, args);
    }
    private void AddTemplate()
    {
        SaveAs = false;
        AddTemplateModal.Show();
        StateHasChanged();
    }
    private async void SaveAsTemplate()
    {
        SaveAs = true;
        AddTemplateModal.Show();
        StateHasChanged();
    }
    private void NewScheduleFromTemplate()
    {
        AddScheduleFromTemplateModal.Show();
        StateHasChanged();
    }
    private async void HandleValidAddScheduleFromTemplateSubmit(ScheduleDto responseSchedule)
    {
        var schedulesFromTemplateResponse = await ScheduleService.GetSchedulesForTemplateAsync(SelectedTemplate.TemplateId);
        SchedulesForTemplateData = schedulesFromTemplateResponse.Value;
        AddScheduleFromTemplateModal.Hide();
        StateHasChanged();
    }
    async Task DeleteTemplate()
    {
        var confirm = await Dialogs.ConfirmAsync("Are you sure you want to delete this template?");
        if (confirm)
        {
            var templateToDelete = SelectedTemplate;
            if(templateToDelete != null)
            {
                var response = await ScheduleService.DeleteTemplateAsync(templateToDelete);
                SelectedTemplateData = null; //clear
                SelectedTemplate = new TemplateDto();
                AvailableTemplates = (await ScheduleService.GetTemplatesAsync()).Value;//reset the list of available
                ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
            }           
        }
    }
    private async void HandleValidAddTemplateSubmit(ResponseModel<TemplateDto> responseTemplate)
    {
        AvailableTemplates = (await ScheduleService.GetTemplatesAsync()).Value;
        SelectedTemplate = AvailableTemplates.Where(x => x.TemplateId == responseTemplate.Value.TemplateId).FirstOrDefault();
        SelectedTemplateData = (await ScheduleService.GetTemplateAsync(SelectedTemplate.TemplateId)).Value;
        await AddTemplateModal.Hide();
        ShowSuccessOrErrorNotification(responseTemplate.Message, responseTemplate.IsSuccess);
        StateHasChanged();
    }
    private async void UpdateTemplate(TreeListCommandEventArgs args)
    {
        //TODO: bug if click in milestone line
        var itemToEdit = args.Item as ScheduleTemplateTreeModel;
        if (itemToEdit != null && itemToEdit.TemplateSactivity != null)
        {
            //TODO: doesn't have pred ids if it was updating another field, need to bind differently
            if(ItemToEdit != null)
            {//TODO: there is no way to select no predecessor if it had one to start?
                itemToEdit.TemplateSactivity.PredIds = ItemToEdit.PredecessorIds;
            }

            var response = await ScheduleService.UpdateTemplateActivityAsync(itemToEdit.TemplateSactivity);
            if (response.IsSuccess)
            {
                //TODO: update the item, update all the fields, update if it's a milestone not an activity
                var findItem = SelectedTemplateData.SelectMany(x => x.Children).SingleOrDefault(x => x.Id == itemToEdit.Id);
                findItem.TemplateSactivity = response.Value;
                findItem.Predecessors = response.Value.Predecessors;
                findItem.PredecessorIds = response.Value.PredIds;
                TemplateTreeList.Rebind();
            }
            ItemToEdit = null;
            ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);           
        } 
    }
    private async void DeleteActivity(TreeListCommandEventArgs args)
    {        
        var itemToEdit = args.Item as ScheduleTemplateTreeModel;
        if (itemToEdit != null && itemToEdit.TemplateSactivity != null)
        {
            var confirm = await Dialogs.ConfirmAsync("Confirm delete activity from template?");
            if (confirm)
            {
                var response = await ScheduleService.DeleteTemplateActivityAsync(itemToEdit.TemplateSactivity);
                var findParent = SelectedTemplateData.Where(x => x.Children.Select(x => x.Id).Contains(itemToEdit.Id)).FirstOrDefault();
                findParent.Children.Remove(itemToEdit);
                //TODO: remove any predecessors tied to this, refresh the list of available preds? 
                //adust the seq numbers of everything            
                foreach (var activityItem in SelectedTemplateData.SelectMany(x => x.Children).ToList())            
                {                
                    var findSeq = SelectedTemplateData.SelectMany(x => x.Children).IndexOf(activityItem);                
                    activityItem.TemplateSactivity.Seq = findSeq + 1; //adding one because list is 0 index
                    if (activityItem.PredecessorIds != null && activityItem.PredecessorIds.Contains(itemToEdit.Sactivity.SactivityId))
                    {
                        activityItem.PredecessorIds.Remove(itemToEdit.Sactivity.SactivityId);
                    }                   
                    if (activityItem.Predecessors.Contains(itemToEdit.Sactivity.ActivityName))
                    {
                        var index = activityItem.Predecessors.IndexOf(itemToEdit.Sactivity.ActivityName);
                        activityItem.Predecessors = activityItem.Predecessors.Remove(index, itemToEdit.Sactivity.ActivityName.Length);
                        if(activityItem.Predecessors.StartsWith(", "))
                        {
                            activityItem.Predecessors = activityItem.Predecessors.Remove(0, 2);
                        }
                        if (activityItem.Predecessors.EndsWith(","))
                        {
                            activityItem.Predecessors = activityItem.Predecessors.Remove(activityItem.Predecessors.Length - 1, 1);
                        }
                        if (activityItem.Predecessors.Contains(", ,"))
                        {
                            activityItem.Predecessors = activityItem.Predecessors.Remove(activityItem.Predecessors.IndexOf((", ,")), 2);
                        }
                    }
                }            
                var activityDataToUpdate = SelectedTemplateData.SelectMany(x => x.Children).Select(x => x.TemplateSactivity).ToList();            
                await ScheduleService.UpdateTemplateActivitiesSeqAsync(activityDataToUpdate);
                StateHasChanged();
                TemplateTreeList.Rebind();                
            }            
        }
        if (itemToEdit != null && itemToEdit.TemplateMilestone != null && itemToEdit.TemplateSactivity == null)
        {
            var confirm = await Dialogs.ConfirmAsync("Deleting this milestone will delete activities in it. Do you want to proceed?");
            if (confirm)
            {
                var response = await ScheduleService.DeleteTemplateMilestoneAsync(itemToEdit.TemplateMilestone);
                if (response.IsSuccess)
                {
                    //TODO: if it removes all the activities in the milestone, it needs to remove preds that referred to those deleted activities
                    SelectedTemplateData.Remove(itemToEdit);
                    //adust the seq numbers of the activities                   
                    foreach (var activityItem in SelectedTemplateData.SelectMany(x => x.Children).ToList())
                    {
                        var findSeq = SelectedTemplateData.SelectMany(x => x.Children).IndexOf(activityItem);
                        activityItem.TemplateSactivity.Seq = findSeq + 1; //adding one because list is 0 index
                    }
                    var activityDataToUpdate = SelectedTemplateData.SelectMany(x => x.Children).Select(x => x.TemplateSactivity).ToList();
                    await ScheduleService.UpdateTemplateActivitiesSeqAsync(activityDataToUpdate);
                    TemplateTreeList.Rebind();
                }
                ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
            }           
        }
    }

    private async Task ExportScheduleTemplate()
    {
        if (SelectedTemplateData == null)
        {
            return;
        }
        if (await JSRuntimeService.InvokeAsync<bool>("confirm", $"Do you want to Export?"))
        {
            StateHasChanged();
            var responseBytes = await ScheduleService.DownloadExcelScheduleTemplatesAsync(SelectedTemplateData);
            var fileName = $"{SelectedTemplate.TemplateName}-{DateTime.Now.ToString("yyyy-MM-dd", System.Globalization.CultureInfo.InvariantCulture)}.xlsx";
            await JSRuntimeService.InvokeAsync<object>("saveAsFile", fileName, Convert.ToBase64String(responseBytes.Value));

        }
    }

    public async Task UpdateFromTemplate()
    {
        //TODO: need to also select to only update certain milestones

        if(SelectedJobsToUpdate != null && SelectedJobsToUpdate.Any())
        {
            IsLoading = true;
            var response = await ScheduleService.UpdateSchedulesFromTemplateAsync(SelectedJobsToUpdate.ToList());
            IsLoading = false;
            ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        }     
    }
    public async Task DeleteSchedule(GridCommandEventArgs args)
    {
        var job = args.Item as ScheduleDto;
        if(job != null)
        {
            IsLoading = true;
            var response = await ScheduleService.DeleteScheduleAsync(job);
            var schedulesFromTemplateResponse = await ScheduleService.GetSchedulesForTemplateAsync(SelectedTemplate.TemplateId);
            SchedulesForTemplateData = schedulesFromTemplateResponse.Value;
            IsLoading = false;
            ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
        }
       
    }
    private void ShowSuccessOrErrorNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
