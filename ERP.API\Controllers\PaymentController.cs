﻿
using AutoMapper;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using System.Data;
using System.Diagnostics;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class PaymentController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        private readonly IWebHostEnvironment _env;
        public PaymentController(IConfiguration configuration, ErpDevContext context, IMapper mapper, IWebHostEnvironment env)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _env = env;
        }
//        /// <summary>
//        /// create or update from bc
//        /// </summary>
//        /// <param name="payment"></param>
//        /// <returns></returns>
//        [HttpPost]
//        public async Task<IActionResult> PaymentAsync([FromBody] CheckPaymentDto payment)
//        {
//            try
//            {
//                //BC wont' have supplier Id, finding by invoice nubmer, check number and bank account
//                var findPayment = _context.CheckPayments.SingleOrDefault(x => x.InvNumber == payment.InvNumber && x.CheckNumber == payment.CheckNumber && x.BankAccount == payment.BankAccount);
//                if (findPayment != null)
//                {
//                    //payment exists, update it
//                    //subnumber from bc should be short name, look up by that, and/or po number, is that available/uniquae

//                    var findSupplier = _context.Suppliers.FirstOrDefault(x => x.ShortName == payment.SupplierShortName);
//                    if (findSupplier == null)
//                    {
//                        return StatusCode(500, new ResponseModel<CheckPaymentDto> { IsSuccess = false, Message = $"Failed to create/update payment. Could not find supplier: {payment.SupplierShortName}", Value = null });
//                    }
//                    var findPoHeader = _context.Poheaders.FirstOrDefault(x => x.Ponumber == payment.PoNumber);
//                    if (findPoHeader == null)
//                    {
//                        return StatusCode(500, new ResponseModel<CheckPaymentDto> { IsSuccess = false, Message = $"Failed to create/update payment. Could not find PO Number: {payment.PoNumber}", Value = null });
//                    }
//                    findPayment.BankAccount = payment.BankAccount ?? findPayment.BankAccount;
//                    findPayment.CheckNumber = payment.CheckNumber ?? findPayment.CheckNumber;
//                    findPayment.CheckDate = payment.CheckDate ?? findPayment.CheckDate;
//                    findPayment.SubNumber = findSupplier.SubNumber;//probably shouldn't change
//                    findPayment.InvNumber = payment.InvNumber ?? findPayment.InvNumber;
//                    findPayment.InvAmountPaid = payment.InvAmountPaid ?? findPayment.InvAmountPaid;
//                    findPayment.PoheaderId = findPoHeader.PoheaderId;//probably shouldn't be changed
//                    findPayment.PoAmountPaid = payment.PoAmountPaid ?? findPayment.PoAmountPaid;
//                    findPayment.VoidedAmount = payment.VoidedAmount ?? findPayment.VoidedAmount;
//                    findPayment.VoidedPayment = payment.VoidedPayment ?? findPayment.VoidedPayment;
//                    findPayment.VoidedDate = payment.VoidedDate ?? findPayment.VoidedDate;
//                    findPayment.AcntDbId = payment.AcntDbId ?? findPayment.AcntDbId;
//                    findPayment.FoundDuringUpdate = payment.FoundDuringUpdate ?? findPayment.FoundDuringUpdate;
//                    findPayment.PaymentId = payment.PaymentId ?? findPayment.PaymentId;

//                    findPayment.UpdatedBy = "BC integration";
//                    findPayment.UpdatedDateTime = DateTime.Now;
//                    _context.CheckPayments.Update(findPayment);
//                    await _context.SaveChangesAsync();
//                    var returnPayment = _mapper.Map<CheckPaymentDto>(findPayment);
//                    return new OkObjectResult(new ResponseModel<CheckPaymentDto> { IsSuccess = true, Message = "Updated payment successfully", Value = returnPayment });

//                }
//                else
//                {
//                    //new payment, insert it
//                    var findSupplier = _context.Suppliers.FirstOrDefault(x => x.ShortName == payment.SupplierShortName);
//                    if (findSupplier == null)
//                    {
//                        return StatusCode(500, new ResponseModel<CheckPaymentDto> { IsSuccess = false, Message = $"Failed to create/update payment. Could not find supplier: {payment.SupplierShortName}", Value = null });
//                    }
//                    var findPoHeader = _context.Poheaders.FirstOrDefault(x => x.Ponumber == payment.PoNumber);
//                    if (findPoHeader == null)
//                    {
//                        return StatusCode(500, new ResponseModel<CheckPaymentDto> { IsSuccess = false, Message = $"Failed to create/update payment. Could not find PO Number: {payment.PoNumber}", Value = null });
//                    }
//                    var addPayment = _mapper.Map<CheckPayment>(payment);
//                    addPayment.PoheaderId = findPoHeader.PoheaderId;
//                    addPayment.SubNumber = findPoHeader.SubNumber;
//                    addPayment.CreatedBy = "BC integration";
//                    _context.CheckPayments.Add(addPayment);
//                    await _context.SaveChangesAsync();
//                    var returnPayment = _mapper.Map<CheckPaymentDto>(addPayment);
//                    return new OkObjectResult(new ResponseModel<CheckPaymentDto> { IsSuccess = true, Message = "Created payment successfully", Value = returnPayment });
//                }
               
//            }
//            catch (Exception ex)
//            {
//#if DEBUG
//                _logger.Debug(ex);
//#else
//                _logger.Error(ex);
//#endif
//                return StatusCode(500, new ResponseModel<CheckPaymentDto> { IsSuccess = false, Message = $"Failed to create/update payment Error: {ex.Message} {ex.InnerException?.Message}", Value = null });
//            }
//        }
        /// <summary>
        /// create or update from bc
        /// </summary>
        /// <param name="payment"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> PaymentAsync([FromBody] BCCheckPayments payment)
        {
            try
            {
                //BC wont' have supplier Id, finding by invoice nubmer, check number and bank account
                var findSupplier = _context.Suppliers.FirstOrDefault(x => x.ShortName == payment.SupplierShortName);
                if (findSupplier == null)
                {
                    return StatusCode(500, new ResponseModel<CheckPaymentDto> { IsSuccess = false, Message = $"Failed to create/update payment. Could not find supplier: {payment.SupplierShortName}", Value = null });
                }
                List<CheckPayment> addPayments = new List<CheckPayment>();
                foreach (var poHeader in payment.Invoices)
                {
                    var findPoHeader = _context.Poheaders.FirstOrDefault(x => x.Ponumber == poHeader.PoNumber);
                    if (findPoHeader == null)
                    {
                        return StatusCode(500, new ResponseModel<CheckPaymentDto> { IsSuccess = false, Message = $"Failed to create/update payment. Could not find PO Number: {poHeader.PoNumber}", Value = null });
                    }
                    //find if the checkpayment line exists and if so, update it, else add
                    var findPayment = _context.CheckPayments.FirstOrDefault(x => x.InvNumber == poHeader.InvNumber && x.CheckNumber == payment.CheckNumber && x.BankAccount == payment.BankAccount);
                    if (findPayment == null)
                    {
                        var addPayment = new CheckPayment();
                        addPayment.BankAccount = payment.BankAccount;
                        addPayment.CheckNumber = payment.CheckNumber;
                        addPayment.CheckDate = payment.CheckDate;
                        addPayment.CheckAmount = payment.CheckAmount;
                        addPayment.InvAmountPaid = poHeader.InvAmountPaid;
                        addPayment.InvNumber = poHeader.InvNumber;
                        addPayment.PoAmountPaid = poHeader.PoAmountPaid;
                        addPayment.VoidedPayment = payment.VoidedPayment;
                        addPayment.VoidedDate = payment.VoidedDate;//it wouldn't be voided if it's new 
                        addPayment.VoidedAmount = payment.VoidedAmount;
                        addPayment.PoheaderId = findPoHeader.PoheaderId;
                        addPayment.SubNumber = findPoHeader.SubNumber;
                        addPayment.PaymentType = payment.PaymentType;
                        addPayment.CreatedBy = payment.CreatedBy;
                        addPayment.InvAmountPaid = poHeader.InvAmountPaid;
                        addPayment.PoAmountPaid = poHeader.PoAmountPaid;
                        addPayments.Add(addPayment);
                    }
                    else
                    {
                        //update
                        findPayment.PaymentType = payment.PaymentType ?? findPayment.PaymentType;
                        findPayment.VoidedPayment = payment.VoidedPayment ?? findPayment.VoidedPayment;
                        findPayment.CheckAmount = payment.CheckAmount ?? findPayment.CheckAmount;
                        findPayment.InvAmountPaid = poHeader.InvAmountPaid ?? findPayment.InvAmountPaid;
                        findPayment.PoAmountPaid = poHeader.PoAmountPaid ?? findPayment.PoAmountPaid; //it will send update if it's voided
                        findPayment.VoidedPayment = payment.VoidedPayment ?? findPayment.VoidedPayment;// will send update if it's voided
                        findPayment.VoidedAmount = payment.VoidedAmount ?? findPayment.VoidedAmount;
                        findPayment.VoidedDate = payment.VoidedDate ?? findPayment.VoidedDate;
                        findPayment.UpdatedBy = payment.UpdatedBy;
                        findPayment.UpdatedDateTime = DateTime.Now;
                        _context.CheckPayments.Update(findPayment);
                        await _context.SaveChangesAsync();
                    }
                }
                _context.CheckPayments.AddRange(addPayments);
                await _context.SaveChangesAsync();
                var returnPayments = _mapper.Map<List<CheckPaymentDto>>(addPayments);
                //TODO: return object has too much stuff in it
                return new OkObjectResult(new ResponseModel<List<CheckPaymentDto>> { IsSuccess = true, Message = "Created payment successfully", Value = returnPayments });

            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                //log to bc api log
                string baseRequestURL = _env.IsDevelopment() ? "https://vmerpservices-dev.azurewebsites.net/api/" : "https://vmerpservices.azurewebsites.net/api/";
                var jsonItem = JsonConvert.SerializeObject(payment);
                var newLog = new ErpBcApiLog()
                {
                    Method = "POST",
                    Type = "Payment",
                    RequestUrl = $"{baseRequestURL}payment/payment",
                    RequestBody = jsonItem,
                    ResponseBody = $"Failed to add or update Payment. Error: {ex.Message} {ex.InnerException?.Message}",
                    ResponseCode = "500",
                    CreatedBy = payment.CreatedBy,
                    CreatedDateTime = DateTime.Now,
                };
                _context.ErpBcApiLogs.Add(newLog);
                await _context.SaveChangesAsync();

                return StatusCode(500, new ResponseModel<CheckPaymentDto> { IsSuccess = false, Message = $"Failed to create/update payment Error: {ex.Message} {ex.InnerException?.Message}", Value = null });
            }
        }
//        /// <summary>
//        /// create or update from bc
//        /// </summary>
//        /// <param name="payment"></param>
//        /// <returns></returns>
//        [HttpPost]
//        public async Task<IActionResult> PaymentsAsync([FromBody] List<BCCheckPayments> payments)
//        {
//            try
//            {
//                //TODO: fix list loop
//                var returnPayments = new List<CheckPaymentDto>();
//                foreach (var payment in payments)
//                {
//                    //BC wont' have supplier Id, finding by invoice nubmer, check number and bank account
//                    var findSupplier = _context.Suppliers.FirstOrDefault(x => x.ShortName == payment.SupplierShortName);
//                    if (findSupplier == null)
//                    {
//                        return StatusCode(500, new ResponseModel<CheckPaymentDto> { IsSuccess = false, Message = $"Failed to create/update payment. Could not find supplier: {payment.SupplierShortName}", Value = null });
//                    }
//                    List<CheckPayment> addPayments = new List<CheckPayment>();
//                    foreach (var poHeader in payment.Invoices)
//                    {
//                        var findPoHeader = _context.Poheaders.FirstOrDefault(x => x.Ponumber == poHeader.PoNumber);
//                        if (findPoHeader == null)
//                        {
//                            return StatusCode(500, new ResponseModel<CheckPaymentDto> { IsSuccess = false, Message = $"Failed to create/update payment. Could not find PO Number: {poHeader.PoNumber}", Value = null });
//                        }
//                        //find if the checkpayment line exists and if so, update it, else add
//                        var findPayment = _context.CheckPayments.FirstOrDefault(x => x.InvNumber == poHeader.InvNumber && x.CheckNumber == payment.CheckNumber && x.BankAccount == payment.BankAccount);
//                        if (findPayment == null)
//                        {
//                            var addPayment = new CheckPayment();
//                            addPayment.BankAccount = payment.BankAccount;
//                            addPayment.CheckNumber = payment.CheckNumber;
//                            addPayment.CheckDate = payment.CheckDate;
//                            addPayment.CheckAmount = payment.CheckAmount;
//                            addPayment.InvAmountPaid = poHeader.InvAmountPaid;
//                            addPayment.InvNumber = poHeader.InvNumber;
//                            addPayment.PoAmountPaid = poHeader.PoAmountPaid;
//                            addPayment.VoidedPayment = payment.VoidedPayment;
//                            addPayment.VoidedDate = payment.VoidedDate;//it wouldn't be voided if it's new 
//                            addPayment.VoidedAmount = payment.VoidedAmount;
//                            addPayment.PoheaderId = findPoHeader.PoheaderId;
//                            addPayment.SubNumber = findPoHeader.SubNumber;
//                            addPayment.PaymentType = payment.PaymentType;
//                            addPayment.CreatedBy = payment.CreatedBy;
//                            addPayment.InvAmountPaid = poHeader.InvAmountPaid;
//                            addPayment.PoAmountPaid = poHeader.PoAmountPaid;
//                            addPayments.Add(addPayment);
//                        }
//                        else
//                        {
//                            //update
//                            findPayment.PaymentType = payment.PaymentType ?? findPayment.PaymentType;
//                            findPayment.VoidedPayment = payment.VoidedPayment ?? findPayment.VoidedPayment;
//                            findPayment.CheckAmount = payment.CheckAmount ?? findPayment.CheckAmount;
//                            findPayment.InvAmountPaid = poHeader.InvAmountPaid ?? findPayment.InvAmountPaid;
//                            findPayment.PoAmountPaid = poHeader.PoAmountPaid ?? findPayment.PoAmountPaid; //it will send update if it's voided
//                            findPayment.VoidedPayment = payment.VoidedPayment ?? findPayment.VoidedPayment;// will send update if it's voided
//                            findPayment.VoidedAmount = payment.VoidedAmount ?? findPayment.VoidedAmount;
//                            findPayment.VoidedDate = payment.VoidedDate ?? findPayment.VoidedDate;
//                            findPayment.UpdatedBy = payment.UpdatedBy;
//                            findPayment.UpdatedDateTime = DateTime.Now;
//                            _context.CheckPayments.Update(findPayment);
//                            await _context.SaveChangesAsync();
//                        }
//                    }
//                    _context.CheckPayments.AddRange(addPayments);
//                    await _context.SaveChangesAsync();
//                    var returnPayment = _mapper.Map<List<CheckPaymentDto>>(addPayments);
//                    returnPayments.AddRange(returnPayment);
//                    //_mapper.Map<List<CheckPaymentDto>>(addPayments);
//                }
                
//                //TODO: return object has too much stuff in it
//                return new OkObjectResult(new ResponseModel<List<CheckPaymentDto>> { IsSuccess = true, Message = "Created payment successfully", Value = returnPayments });

//            }
//            catch (Exception ex)
//            {
//#if DEBUG
//                _logger.Debug(ex);
//#else
//                _logger.Error(ex);
//#endif
//                return StatusCode(500, new ResponseModel<CheckPaymentDto> { IsSuccess = false, Message = $"Failed to create/update payment Error: {ex.Message} {ex.InnerException?.Message}", Value = null });
//            }
//        }

        /// <summary>
        /// create or update from bc
        /// </summary>
        /// <param name="payment"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> PaymentsAsync([FromBody] List<BCPayment> payments)
        {
            try
            {
                //TODO: return which one fails, if some succeed

                //It could be existing if they're voiding a payment
                //would they need to repush if something fails
                //if it's a vpo, need to get only one line representing the final approve
                //if bc sends ones that are from base or something that doesn't match an erp invoice, it will return success just not find anything

                //temporary log to test if it's sending same request twice
                var jsonItem = JsonConvert.SerializeObject(payments);
                //var stringBody = new StringContent(jsonItem);
                var newLog = new ErpBcApiLog()
                {
                    Method = "POST",
                    Type = "Payment",
                    RequestUrl = "https://vmerpservices.azurewebsites.net/api/payment/payments",
                    RequestBody = jsonItem,
                    CreatedBy = "testing from BC",
                    CreatedDateTime = DateTime.Now,
                };
                _context.ErpBcApiLogs.Add(newLog);
                await _context.SaveChangesAsync();


                var returnPayments = new List<CheckPaymentDto>();
                var subApNums = payments.Select(x => x.SupplierShortName).ToList();
                var getSuppliers = _context.Suppliers.Where(x => x.IsActive1 == true && subApNums.Contains(x.ShortName)).ToList();
                var invoiceNums = payments.Select(x => x.InvNumber).ToList();               
                var getInvoices = _context.Poapprovals.Include(x => x.VpoApproval).Where(x => x.IsActive == true && invoiceNums.Contains(x.Invnumber) && (x.VpoApprovalId == null || (x.VpoApproval.LevelCode == "Payment"))).ToList();
                var getInvoices2 = _context.Poapprovals.Include(x => x.VpoApproval).Where(x => x.IsActive == true && invoiceNums.Contains(x.Invnumber)).ToList();
                var checkNumbers = payments.Select(x => x.CheckNumber).ToList();
                var paymentsExisting = _context.CheckPayments.Where(x => x.IsActive == true && checkNumbers.Contains(x.CheckNumber)).ToList();
                var paymentExists = (from a in payments
                                     join b in paymentsExisting on new {  a.CheckNumber, a.InvNumber } equals new { b.CheckNumber, b.InvNumber }
                                     select new CheckPayment()
                                     {
                                         CheckPaymentsId = b.CheckPaymentsId,
                                         BankAccount = a.BankAccount,
                                         CheckNumber = a.CheckNumber,
                                         CheckDate = a.CheckDate,
                                         CheckAmount = a.CheckAmount,
                                         InvAmountPaid = a.InvAmountPaid,
                                         InvNumber = a.InvNumber,
                                         PoAmountPaid = a.PoAmountPaid,
                                         VoidedPayment = a.VoidedPayment,
                                         VoidedDate = a.VoidedDate,
                                         VoidedAmount = a.VoidedAmount,
                                         PaymentType = a.PaymentType,
                                         UpdatedBy = a.UpdatedBy,
                                         UpdatedDateTime = DateTime.Now,
                                     }).ToList();
                await _context.CheckPayments.BulkUpdateAsync(paymentExists, options => options.ColumnInputExpression = x => new { x.InvAmountPaid, x.UpdatedDateTime, x.UpdatedBy, x.PaymentType, x.VoidedAmount, x.VoidedPayment, x.VoidedDate, x.CheckAmount });
                //update the existing.. 
                //then only insert the rest of them
                var notExists = payments.Where(x => !paymentExists.Any(y => y.BankAccount == x.BankAccount && y.InvNumber == x.InvNumber && y.CheckNumber == x.CheckNumber)).ToList();

                //Level code = Payment is the final one
                //var poNums = payments.Select(x => x.PoNumber).ToList();//todo might have to get from inv
                //var getHeaders = _context.Poheaders.Where(x => x.IsActive == true && poNums.Contains(x.Ponumber)).ToList();
                var paymentData = (from a in notExists
                                   from c in getInvoices.Where(x => x.Invnumber == a.InvNumber).DefaultIfEmpty()
                                  from b in getSuppliers.Where(x => x.ShortName?.ToLower() == a.SupplierShortName?.ToLower()).DefaultIfEmpty()
                                   select new CheckPayment()
                                  {
                                      BankAccount = a.BankAccount,
                                      CheckNumber = a.CheckNumber,
                                      CheckDate = a.CheckDate,
                                      CheckAmount = a.CheckAmount,
                                      InvAmountPaid = a.InvAmountPaid,
                                      InvNumber = a.InvNumber,
                                      PoAmountPaid = a.PoAmountPaid,
                                      VoidedPayment = a.VoidedPayment,
                                      VoidedDate = a.VoidedDate,//it wouldn't be voided if it's new 
                                      VoidedAmount = a.VoidedAmount,
                                      PoheaderId = c != null ? c.PoheaderId : 0,
                                      SubNumber = b != null ? b.SubNumber : 0,
                                      PaymentType = a.PaymentType,
                                      CreatedBy = a.CreatedBy
                                  }).ToList();

                //if(getAllData.Any(x => x.SubNumber == null))
                //{
                //    return StatusCode(500, new ResponseModel<CheckPaymentDto> { IsSuccess = false, Message = $"Failed to create/update payments. Suppliers not found in ERP", Value = null });
                //}
                var paymentsSuppliersExists = paymentData.Where(x => x.SubNumber != 0 && x.PoheaderId != 0);
                var missingSuppliers = paymentData.Where(x => x.SubNumber == 0);
                var missingInvoice = paymentData.Where(x => x.PoheaderId == 0);
                await _context.CheckPayments.BulkInsertAsync(paymentsSuppliersExists);

                var getReturnPayments = _context.CheckPayments.Include(x => x.SubNumberNavigation).Where(x => paymentsSuppliersExists.Select(y=>y.CheckPaymentsId).Contains(x.CheckPaymentsId) || paymentExists.Select(y => y.CheckPaymentsId).Contains(x.CheckPaymentsId)).ToList();
                var returnPaymentDto = _mapper.Map<List<CheckPaymentDto>>(getReturnPayments);
                foreach(var item in returnPaymentDto)
                {
                    item.SupplierShortName = item.SubNumberNavigation.ShortName;
                    item.SubNumberNavigation = null;//To avoid returning the whole thing
                }
                var message = missingSuppliers.Any() || missingInvoice.Any() ? $"Some payments were not saved because invoice numbers or suppliers did not match ERP" : "Success";
                return new OkObjectResult(new ResponseModel<List<CheckPaymentDto>> { IsSuccess = true, Message = message, Value = returnPaymentDto });

            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                //log to bc api log
                string baseRequestURL = _env.IsDevelopment() ? "https://vmerpservices-dev.azurewebsites.net/api/" : "https://vmerpservices.azurewebsites.net/api/";
                var jsonItem = JsonConvert.SerializeObject(payments);
                var newLog = new ErpBcApiLog()
                {
                    Method = "POST",
                    Type = "Payment",
                    RequestUrl = $"{baseRequestURL}payment/payments",
                    RequestBody = jsonItem,
                    ResponseBody = $"Failed to add or update Payment. Error: {ex.Message} {ex.InnerException?.Message}",
                    ResponseCode = "500",
                    CreatedBy = payments?.FirstOrDefault()?.CreatedBy,
                    CreatedDateTime = DateTime.Now,
                };
                _context.ErpBcApiLogs.Add(newLog);
                await _context.SaveChangesAsync();
                return StatusCode(500, new ResponseModel<CheckPaymentDto> { IsSuccess = false, Message = $"Failed to create/update payment Error: {ex.Message} {ex.InnerException?.Message}", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> CheckPaymentsAsync()
        {
            try
            {
                var payments = await _context.CheckPayments.Include(x => x.SubNumberNavigation).Include(x => x.Poheader).Where(x => x.IsActive == true).OrderBy(x => x.InvNumber).ToListAsync();
                var paymentsDto = _mapper.Map<List<CheckPaymentDto>>(payments);
                return Ok(new ResponseModel<List<CheckPaymentDto>>() { Value = paymentsDto, IsSuccess = true, Message = "Got pyments data" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CheckPaymentDto>> { IsSuccess = false, Message = "Failed to get data", Value = null });

            }
        }
        [HttpGet("{subNumber}")]
        public async Task<IActionResult> CheckPaymentsBySupplierAsync(int subNumber)
        {
            try
            {
                var payments = await _context.CheckPayments.AsNoTracking().Include(x => x.SubNumberNavigation).Include(x => x.Poheader).Where(x => x.IsActive == true && x.SubNumber == subNumber).OrderByDescending(x => x.CheckDate).ToListAsync();
                var paymentsDto = _mapper.Map<List<CheckPaymentDto>>(payments);
                return Ok(new ResponseModel<List<CheckPaymentDto>>() { Value = paymentsDto, IsSuccess = true, Message = "Got pyments data" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CheckPaymentDto>> { IsSuccess = false, Message = "Failed to get data", Value = null });

            }
        }
        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> CheckPaymentsByJobAsync(string jobNumber)
        {
            try
            {
                var payments = await _context.CheckPayments.Include(x => x.SubNumberNavigation).Include(x => x.Poheader).Where(x => x.IsActive == true && x.Poheader.Pojobnumber == jobNumber).OrderByDescending(x => x.CheckDate).ToListAsync();
                var paymentsDto = _mapper.Map<List<CheckPaymentDto>>(payments);
                return Ok(new ResponseModel<List<CheckPaymentDto>>() { Value = paymentsDto, IsSuccess = true, Message = "Got pyments data" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CheckPaymentDto>> { IsSuccess = false, Message = "Failed to get data", Value = null });

            }
        }
    }
}
