﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Addendum
{
    public int AddendaId { get; set; }

    public int? SubNumber { get; set; }

    public string? FilePath { get; set; }

    public Guid? DocusignEnvelopeId { get; set; }

    public string? DocusignStatus { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<AddendaPhasePlan> AddendaPhasePlans { get; set; } = new List<AddendaPhasePlan>();

    public virtual Supplier? SubNumberNavigation { get; set; }
}
