﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public record CombinedPOBudgetTreeModel
    {
        //TODO: make an interface instead, 
        public bool? IsPendingCustomEstimate { get; set; }
       // public CombinedPOBudgetTreeModel? Parent { get; set; }
        public List<CombinedPOBudgetTreeModel>? Children { get; set; }
        public bool HasChildren { get; set; }
        //header
        public Guid Id { get; set; }
        public bool? IsShowing { get; set; }
        public bool? ToBeIssued { get; set; }
        public bool? CanCancel { get; set; }
        public EstheaderDto? Estheader { get; set; }
        //public int? EstheaderId { get; set; }

        public string? JobNumber { get; set; }
        public string? BomClass { get; set; }
        public string? ExtraNumber { get; set; }//seems to show option code | option name in WMS
        //public string? Releasecode { get; set; }
        public int? EstimateNumber { get; set; }

        //option
        public EstoptionDto? Estoption { get; set; }
        public double? TotalCost { get; set; }//sum from item costs

        //activity
        public EstactivityDto? Estactivity { get; set; }
        public double? ActivityTotal { get; set; }
        public double? TaxTotal { get; set; }//it looks like there are actually no tax on any item
        //EstDetail
        public EstdetailDto? Estdetail { get; set; }

        public Guid? ParentId { get; set; }       
        public bool? IsActive { get; set; }

        public decimal? TotalAmount { get; set; }
        public decimal? IssuedAmount { get; set; }
        //TODO: make an interface instead
        public string? PoNumber { get; set; }
        public string? ReleaseCode { get; set; }
        public string? Activity { get; set; }
        public bool IsIssued { get; set; }
        public bool IssueEnabled { get; set; } = true;
        public bool Indeterminate { get; set; } = false;
        public bool PoIsIssued { get; set; }
        public bool PoIssueEnabled { get; set; } = true;
        public bool PoIndeterminate { get; set; } = false;
        public PoheaderDto? Poheader { get; set; }
        public PodetailoptionDto? Podetailoption { get; set; }
        public PodetailDto? Podetail { get; set; }
        public PoapprovalDto? Poapproval { get; set; }
        public string? SearchTags { get; set; } // to make search work for children in TreeList

    }
}
