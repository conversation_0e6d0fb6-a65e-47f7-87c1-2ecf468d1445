﻿
@inject SubdivisionService SubdivisionService


<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add New Contact
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@ContactToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <div class="mb-3">
                <label class="form-label">Select User</label><br />
                <TelerikDropDownList Context="dropdowncontext"
                    Data="@AllUsers"
                                     @bind-Value="@ContactToAdd.UserId"
                                     ValueField="UserId"
                                     TextField="FullName"
                                     DefaultText="Select User"
                                     Width="100%"
                                     FilterOperator="@StringFilterOperator.Contains"
                                     Filterable = "true">
                    <DropDownListSettings>
                        <DropDownListPopupSettings Height="200px"></DropDownListPopupSettings>
                    </DropDownListSettings>
                </TelerikDropDownList>
            </div>
            <div class="mb-3">
                <TelerikCheckBox @bind-Value="@ContactToAdd.IsSiteContact1"></TelerikCheckBox>
                <label class="form-label">Site Contact 1</label><br />
                <TelerikCheckBox @bind-Value="@ContactToAdd.IsSiteContact2"></TelerikCheckBox>
                <label class="form-label">Site Contact 2</label><br />
                <TelerikCheckBox @bind-Value="@ContactToAdd.IsSiteContact3"></TelerikCheckBox>
                <label class="form-label">Site Contact 3</label><br />
                <TelerikCheckBox @bind-Value="@ContactToAdd.IsSiteContact4"></TelerikCheckBox>
                <label class="form-label">Site Contact 4</label><br />
                <TelerikCheckBox @bind-Value="@ContactToAdd.IsSiteContact5"></TelerikCheckBox>
                <label class="form-label">Site Contact 5</label><br />
                <TelerikCheckBox @bind-Value="@ContactToAdd.IsSiteContact6"></TelerikCheckBox>
                <label class="form-label">Site Contact 6</label><br />
                
            </div>
            <button type="submit" class="btn btn-primary">Add</button>
            <button type="button" @onclick="CancelAddContact" class="btn btn-secondary">Cancel</button>
            <div style=@submittingStyle>Adding contact. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public SubdivisionContactModel ContactToAdd { get; set; } = new SubdivisionContactModel();

    [Parameter]
    public int SubdivisionId { get; set; }

    public List<UserDto>? AllUsers;


    private string submittingStyle = "display:none";


    [Parameter]
    public EventCallback<ResponseModel<SubdivisionContactModel>> HandleAddSubmit { get; set; }
    
    public async Task Show()
    {
        //So first pick pactivity, then pick item, then addd.
        IsModalVisible = true;
        AllUsers = (await SubdivisionService.GetUserContactsAsync()).Value;
        StateHasChanged();
    }   

    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        var addItem = new SubdivisionContactModel()
            {
                UserId = ContactToAdd.UserId,
                SubdivisionId = SubdivisionId,
                IsSiteContact1 = ContactToAdd.IsSiteContact1,
                IsSiteContact2 = ContactToAdd.IsSiteContact2,
                IsSiteContact3 = ContactToAdd.IsSiteContact3,
                IsSiteContact4 = ContactToAdd.IsSiteContact4,
                IsSiteContact5 = ContactToAdd.IsSiteContact5,
                IsSiteContact6 = ContactToAdd.IsSiteContact6
            };

        var responseItem = await SubdivisionService.AddSubdivisionContactAsync(addItem);
        submittingStyle = "display:none";
        await HandleAddSubmit.InvokeAsync(responseItem);
    }
    async void CancelAddContact()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;
    }
}
