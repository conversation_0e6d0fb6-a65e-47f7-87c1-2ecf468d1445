﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class PojccdetailDto : IMapFrom<Pojccdetail>
{
    public int PojccdetailId { get; set; }

    public int? SessionId { get; set; }

    public int PoheaderId { get; set; }

    public int? JccItemnumber { get; set; }

    public string? JccItemDesc { get; set; }

    public string? Poextra { get; set; }

    public string? Pocostcode { get; set; }

    public string? Pocategory { get; set; }

    public double? JccAmountIncTax { get; set; }

    public double? JccTaxAmount { get; set; }

    public string? JccTaxGroup { get; set; }

    public double? JccUnits { get; set; }

    public double? JccUnitCost { get; set; }

    public double? AtdAmountIncTax { get; set; }

    public double? AtdTaxAmount { get; set; }

    public double? AtdUnits { get; set; }

    public double? PendAmountIncTax { get; set; }

    public double? PendTaxAmount { get; set; }

    public double? PendDiscount { get; set; }

    public double? PendUnits { get; set; }

    public string? JccItemClosed { get; set; }

    public string? JccItemExported { get; set; }

    public string? LastDraw { get; set; }

    public string? PendDraw { get; set; }

    public string? UserStamp { get; set; }

    public string? JccUnitDesc { get; set; }

    public string? PoPendingSend { get; set; }

    public string? InvPendingSend { get; set; }

    public DateTime? JccItemDatetimeExported { get; set; }

    public string? JccItemUserExported { get; set; }

    public DateTime? JccItemDatetimeClosed { get; set; }

    public string? JccItemUserClosed { get; set; }

    public string? PoPendingSendStatus { get; set; }

    public string? InvPendingSendStatus { get; set; }

    public double? JccCostedTaxAmount { get; set; }

    public double? JccNoncostedTaxAmount { get; set; }

    public int? InvSessionId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public bool? ExportBc { get; set; }
    //public byte[]? RecordTimeStamp { get; set; }

    // public virtual ICollection<Podetail> Podetails { get; set; } = new List<Podetail>();

    public virtual PoheaderDto? Poheader { get; set; } 
}
