﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Worksheet
{
    public int WorksheetId { get; set; }

    public int? DivId { get; set; }

    public string? WorksheetName { get; set; }

    public string? WorksheetDesc { get; set; }

    public string? IsTemporary { get; set; }

    public string? LockedBy { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? WorksheetCreatedBy { get; set; }

    public DateTime? WorksheetCreatedOn { get; set; }

    public string? WorksheetCategory { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<WorksheetPlan> WorksheetPlans { get; set; } = new List<WorksheetPlan>();
}
