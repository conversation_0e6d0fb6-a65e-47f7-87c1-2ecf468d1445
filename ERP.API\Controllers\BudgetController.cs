﻿using AutoMapper;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using DocumentFormat.OpenXml.InkML;
using ERP.API.Data;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Options;
using NLog;
using NLog.Fluent;
using System.Data;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using ERP.Data.Models.ExtensionMethods;
using System.Linq.Expressions;
using Microsoft.Graph.Models;
using Newtonsoft.Json;
using Microsoft.Identity.Abstractions;
using System.Xml.Serialization;
using System.Xml;
using System.Reflection.PortableExecutable;
using DocumentFormat.OpenXml.EMMA;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class BudgetController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly IWebHostEnvironment _env;
        public BudgetController(IConfiguration configuration, ErpDevContext context, IMapper mapper, IDownstreamApi downstreamApi, IWebHostEnvironment env)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
            _downstreamAPI = downstreamApi;
            _env = env;
        }

        /// <summary>
        /// update or create from bc
        /// </summary>
        /// <param name="jobTask"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> JobTaskAsync([FromBody] JobTaskDto jobTask)
        {
            try
            {
                var findJobTask = _context.JobTasks.FirstOrDefault(x => x.JobTaskNo == jobTask.JobTaskNo && x.JobNumber == jobTask.JobNumber);
                if (findJobTask != null)
                {
                    findJobTask.JobPostingGroup = jobTask.JobPostingGroup ?? findJobTask.JobPostingGroup;//G/L account
                    findJobTask.JobNumber = jobTask.JobNumber ?? findJobTask.JobNumber;
                    findJobTask.JobTaskType = jobTask.JobTaskType != 0 ? jobTask.JobTaskType : findJobTask.JobTaskType;
                    findJobTask.JobTaskNo = jobTask.JobTaskNo ?? findJobTask.JobTaskNo;
                    //findJobTask.CostCode = jobTask.JobTaskNo != null && jobTask.JobTaskNo.Length >= 5 ? jobTask.JobTaskNo[^5..] : findJobTask.CostCode;
                    findJobTask.Description = jobTask.Description ?? findJobTask.Description;
                    findJobTask.UpdatedBy = jobTask.UpdatedBy;
                    findJobTask.UpdatedDateTime = DateTime.Now;
                    findJobTask.IsActive = jobTask.IsActive ?? true;//BC will send only if it's deleted
                    _context.JobTasks.Update(findJobTask);
                    await _context.SaveChangesAsync();
                    //any new cost code gets inserted to jccost code as well
                    var findCostCode = _context.Jccostcodes.FirstOrDefault(x => x.CostCode == findJobTask.JobTaskNo);
                    if (findCostCode == null)
                    {
                        var addCostCode = new Jccostcode()
                        {
                            CostCode = jobTask.CostCode,
                            CcDescription = jobTask.Description,
                            CreatedBy = jobTask.CreatedBy
                        };
                        _context.Jccostcodes.Add(addCostCode);
                        await _context.SaveChangesAsync();
                    }
                    else
                    {
                        findCostCode.CcDescription = jobTask.Description ?? findCostCode.CcDescription;
                        findCostCode.UpdatedBy = findCostCode.UpdatedBy;
                        findCostCode.UpdatedDateTime = DateTime.Now;
                        _context.Jccostcodes.Update(findCostCode);
                        await _context.SaveChangesAsync();
                    }
                    var returnJobTask = _mapper.Map<JobTaskDto>(findJobTask);
                    return new OkObjectResult(new ResponseModel<JobTaskDto> { IsSuccess = true, Message = "Created Job Task successfully", Value = returnJobTask });
                }
                else
                {
                    var addJobTask = _mapper.Map<JobTask>(jobTask);
                    addJobTask.IsActive = addJobTask.IsActive ?? true;
                    //addJobTask.CostCode = jobTask.JobTaskNo != null && jobTask.JobTaskNo.Length >= 5 ? jobTask.JobTaskNo[^5..] : "";
                    addJobTask.CreatedBy = jobTask.CreatedBy;
                    _context.JobTasks.Add(addJobTask);
                    await _context.SaveChangesAsync();
                    var findCostCode = _context.Jccostcodes.FirstOrDefault(x => x.CostCode == addJobTask.JobTaskNo);
                    if(findCostCode == null)
                    {
                        var addCostCode = new Jccostcode()
                        {
                            CostCode = addJobTask.JobTaskNo,
                            CcDescription = jobTask.Description,
                            CreatedBy = jobTask.CreatedBy
                        };
                        _context.Jccostcodes.Add(addCostCode);
                        await _context.SaveChangesAsync();
                    }
                    else
                    {
                        findCostCode.CcDescription = jobTask.Description ?? findCostCode.CcDescription;
                        findCostCode.UpdatedBy = jobTask.UpdatedBy;
                        findCostCode.UpdatedDateTime = DateTime.Now;
                        _context.Jccostcodes.Update(findCostCode);
                        await _context.SaveChangesAsync();
                    }
                    var returnJobTask = _mapper.Map<JobTaskDto>(addJobTask);
                    return new OkObjectResult(new ResponseModel<JobTaskDto> { IsSuccess = true, Message = "Created/Updated Job task successfully", Value = returnJobTask });
                }
                
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                //log to bc api log
                string baseRequestURL = _env.IsDevelopment() ? "https://vmerpservices-dev.azurewebsites.net/api/" : "https://vmerpservices.azurewebsites.net/api/";
                var jsonItem = JsonConvert.SerializeObject(jobTask);
                var newLog = new ErpBcApiLog()
                {
                    Method = "POST",
                    Type = "Job Task",
                    RequestUrl = $"{baseRequestURL}budget/jobtask",
                    RequestBody = jsonItem,
                    ResponseBody = $"Failed to add or update Job. Error: {ex.Message} {ex.InnerException?.Message}",
                    ResponseCode = "500",
                    CreatedBy = jobTask.CreatedBy,
                    CreatedDateTime = DateTime.Now,
                };
                _context.ErpBcApiLogs.Add(newLog);
                await _context.SaveChangesAsync();

                return StatusCode(500, new ResponseModel<JobDto> { IsSuccess = false, Message = $"Failed to create or update Job Task. Error: {ex.Message} {ex.InnerException?.Message}", Error = $"{ex.Message} {ex.InnerException?.Message}", Value = null });
            }
        }
        /// <summary>
        /// update or create from bc
        /// </summary>
        /// <param name="jobTask"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> JobTasksAsync([FromBody] List<JobTaskDto> jobTasks)
        {
            try
            {
                var returnTasks = new List<JobTaskDto>();
                var jobTasksSelectedJobs = jobTasks.Select(x => x.JobNumber).Distinct();
                var findJobTasks = _context.JobTasks.Where(x => jobTasksSelectedJobs.Contains(x.JobNumber)).ToList();
                var updateJobTasks = (from a in jobTasks
                                      join b in findJobTasks on new { t = a.JobTaskNo ?? string.Empty, j = a.JobNumber ?? string.Empty } equals new { t = b.JobTaskNo, j = b.JobNumber }
                                      select new JobTask()
                                      {
                                          JobNumber = b.JobNumber,
                                          JobTaskNo = b.JobTaskNo,
                                          JobTaskType = a.JobTaskType,
                                          JobPostingGroup = a.JobPostingGroup,
                                          Description = a.Description,
                                          UpdatedBy = a.UpdatedBy,
                                          UpdatedDateTime = DateTime.Now,
                                          IsActive = a.IsActive
                                      }).ToList();
                await _context.JobTasks.BulkUpdateAsync(updateJobTasks, options => options.ColumnInputExpression = x => new { x.JobTaskType, x.JobPostingGroup, x.Description, x.UpdatedBy, x.IsActive, x.UpdatedDateTime });
                var returnUpdateTasks = _mapper.Map<List<JobTaskDto>>(updateJobTasks);
                returnTasks.AddRange(returnUpdateTasks);

                var tasksToAdd = jobTasks.Where(x => !updateJobTasks.Any(y => y.JobNumber == x.JobNumber && y.JobTaskNo == x.JobTaskNo)).ToList();
                var costCodes = tasksToAdd.Select(x => x.JobTaskNo).ToList();
                var costCodesExist = _context.Jccostcodes.Where(x => costCodes.Contains(x.CostCode)).Select(x => x.CostCode).ToList();
                var costCodesNotExist = costCodes.Where(x => !costCodesExist.Contains(x)).ToList();
                var codesToAdd = tasksToAdd.Where(x => !costCodesExist.Contains(x.JobTaskNo)).Select(x => new Jccostcode()
                {
                    CcDescription = x.Description,
                    CostCode = x.JobTaskNo,
                    CreatedBy = x.CreatedBy,                    
                }).ToList();
                await _context.Jccostcodes.BulkInsertAsync(codesToAdd);

                var insertTasks = tasksToAdd.Select(x => new JobTask()
                {
                    JobNumber = x.JobNumber,
                    JobTaskNo = x.JobTaskNo,
                    JobTaskType = x.JobTaskType,
                    JobPostingGroup = x.JobPostingGroup,
                    Description = x.Description,
                    CreatedBy = x.UpdatedBy,
                    IsActive = x.IsActive
                }).ToList();
                await _context.JobTasks.BulkInsertAsync(insertTasks);
                var returnInsertTasks = _mapper.Map<List<JobTaskDto>>(insertTasks);
                returnTasks.AddRange(returnInsertTasks);

                return new OkObjectResult(new ResponseModel<List<JobTaskDto>> { IsSuccess = true, Message = "Created/updated Job tasks successfully", Value = returnTasks });                

            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                //log to bc api log
                string baseRequestURL = _env.IsDevelopment() ? "https://vmerpservices-dev.azurewebsites.net/api/" : "https://vmerpservices.azurewebsites.net/api/";
                var jsonItem = JsonConvert.SerializeObject(jobTasks);
                var newLog = new ErpBcApiLog()
                {
                    Method = "POST",
                    Type = "Job Task",
                    RequestUrl = $"{baseRequestURL}budget/jobtasks",
                    RequestBody = jsonItem,
                    ResponseBody = $"Failed to add or update Job. Error: {ex.Message} {ex.InnerException?.Message}",
                    ResponseCode = "500",
                    CreatedBy = jobTasks?.FirstOrDefault()?.CreatedBy,
                    CreatedDateTime = DateTime.Now,
                };
                _context.ErpBcApiLogs.Add(newLog);
                await _context.SaveChangesAsync();
                return StatusCode(500, new ResponseModel<JobDto> { IsSuccess = false, Message = $"Failed to create or update Job Tasks. Error: {ex.Message} {ex.InnerException?.Message}", Error = $"{ex.Message} {ex.InnerException?.Message}", Value = null });
            }
        }

        [HttpGet("{estHeaderId}")]
        public async Task<IActionResult> GetEstHeaderAsync(int estHeaderId)
        {
            try
            {
                var getHeader = await _context.Estheaders.AsNoTracking().Include("ReferenceTypeNavigation").SingleOrDefaultAsync(x => x.EstheaderId == estHeaderId);
                var getHeaderDto = _mapper.Map<EstheaderDto>(getHeader);
                return Ok(new ResponseModel<EstheaderDto> { Value = getHeaderDto, IsSuccess = true});
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstheaderDto> { IsSuccess = false, Message = "failed to get est header", Value = null });
            }
        }
        [HttpGet("{estOptionId}")]
        public async Task<IActionResult> GetEstOptionAsync(int estOptionId)
        {
            try
            {
                var getOption = await _context.Estoptions.AsNoTracking().SingleOrDefaultAsync(x => x.EstoptionId == estOptionId);
                var getOptionDto = _mapper.Map<EstoptionDto>(getOption);
                return Ok(new ResponseModel<EstoptionDto> { Value = getOptionDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstoptionDto> { IsSuccess = false, Message = "failed to get est option", Value = null });
            }
        }
        [HttpGet("{estActivityId}")]
        public async Task<IActionResult> GetEstActivityAsync(int estActivityId)
        {
            try
            {
                var getActivity = await _context.Estactivities.AsNoTracking().SingleOrDefaultAsync(x => x.EstactivityId == estActivityId);
                var getActivityDto = _mapper.Map<EstactivityDto>(getActivity);
                getActivityDto.BoolUseWbsSort = getActivityDto.UseWbsSort == "T";
                getActivityDto.BoolUseLocation = getActivityDto.UseLocation == "T";
                getActivityDto.BoolTaxable = getActivityDto.Taxable == "T";
                return Ok(new ResponseModel<EstactivityDto> { IsSuccess = true, Value = getActivityDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstactivityDto> { IsSuccess = false, Message = "failed to get Est Activity", Value = null });
            }
        }
        [HttpGet("{estDetailId}")]
        public async Task<IActionResult> GetEstDetailAsync(int estDetailId)
        {
            try
            {
                var getDetail = await _context.Estdetails.AsNoTracking().Include("MasterItems").Include("Podetail.Poheader.PostatusNavigation").SingleOrDefaultAsync(x => x.EstdetailId == estDetailId);
                var getDetailDto = _mapper.Map<EstdetailDto>(getDetail);
                getDetailDto.BoolTaxable = getDetailDto.Taxable == "T";
                getDetailDto.BoolLump = getDetailDto.Lump == "T";
                getDetailDto.BoolUseEstPrice = getDetailDto.UseEstPrice == "T";
                getDetailDto.DisplayWarnings = !string.IsNullOrWhiteSpace(getDetailDto.Warnings) ? string.Join(", ", getDetailDto.Warnings.Split('|').Select(x => EnumExtension.GetDescriptionFromEnum((BudgetWarnings)int.Parse(x))).ToList()) : null;
                getDetailDto.DisplayErrors = !string.IsNullOrWhiteSpace(getDetailDto.Errors) ? string.Join(", ", getDetailDto.Errors.Split('|').Select(x => EnumExtension.GetDescriptionFromEnum((BudgetErrors)int.Parse(x))).ToList()) : null;
                getDetailDto.Podetail = getDetailDto.Podetail ?? new PodetailDto() { Poheader = new PoheaderDto() { PostatusNavigation = new PostatusDto() } };
                return Ok(new ResponseModel<EstdetailDto> { IsSuccess = true, Value = getDetailDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstdetailDto> { IsSuccess = false, Message = "failed to get est detail", Value = null });
            }
        }
           
        [HttpGet("{estActivityId}")]
        public async Task<IActionResult> GetEstDetailsForActivityAsync(int estActivityId)
        {
            var getDetails = await _context.Estdetails.AsNoTracking().Include("Podetail.Poheader.PostatusNavigation").Where(x => x.EstactivityId == estActivityId).ToListAsync();
            var getDetailDto = _mapper.Map<List<EstdetailDto>>(getDetails);
            foreach(var detail in  getDetailDto)
            {
                detail.Podetail = detail.Podetail ?? new PodetailDto() { Poheader = new PoheaderDto() { PostatusNavigation = new PostatusDto() } };
            }
            return Ok(getDetailDto);
        }
        [HttpGet("{poHeaderId}")]
        public async Task<IActionResult> GetPODetailsByPoNumberAsync(int poHeaderId)
        {
            try
            {
                var getPODetails = await _context.Podetails.AsNoTracking().Where(x => x.PoheaderId == poHeaderId).ToListAsync();
                var detailsDto = _mapper.Map<List<PodetailDto>>(getPODetails);
                return new OkObjectResult(new ResponseModel<List<PodetailDto>> { Value = detailsDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<PodetailDto>> { IsSuccess = false, Message = "Failed to get EstDetail by PO Number", Value = new List<PodetailDto>() });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetReferenceTypesAsync()
        {
            try
            {
                var getRefTypes = await _context.ReferenceTypes.AsNoTracking().Where(x => x.IsActive == true).ToListAsync();
                var refTypesDto = _mapper.Map<List<ReferenceTypeDto>>(getRefTypes);
                return Ok(new ResponseModel<List<ReferenceTypeDto>> { IsSuccess = true, Value = refTypesDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<ReferenceTypeDto>> { IsSuccess = false, Message = "failed to get reference types", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetEstimateSourcesAsync()
        {
            try
            {
                var estSources = await _context.EstimateSources.AsNoTracking().Where(x => x.IsActive == true).ToListAsync();
                var estSourcesDto = _mapper.Map<List<EstimateSourceDto>>(estSources);
                return Ok(new ResponseModel<List<EstimateSourceDto>> { IsSuccess = true, Value = estSourcesDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstimateSourceDto>> { IsSuccess = false, Message = "failed to get estimate sources", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetVarianceCategoriesAsync()
        {
            try
            {
                var varianceJCCategories = await _context.Estdetails.AsNoTrackingWithIdentityResolution().Where(x => !string.IsNullOrWhiteSpace(x.VarianceJcCategory)).Select(x => x.VarianceJcCategory).Distinct().ToListAsync();//TODO: this is not right, there could be other possible codes
                return Ok(new ResponseModel<List<string>> { IsSuccess = true, Value = varianceJCCategories });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<string>> { IsSuccess = false, Message = "failed to get variance categories", Value = null });
            }
        }
        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> GetBudgetByJobRelesaseSortAsync(string jobNumber)
        {
            try
            {
                var listPending = new List<CombinedPOBudgetTreeModel>();
                var getBudgets = _context.Estdetails.AsNoTrackingWithIdentityResolution().AsSplitQuery().Include("Estoption.Estheader.ReferenceTypeNavigation").Include("Estactivity").Include("Podetail.Poheader.PostatusNavigation").Include("Estjcedetail").Where(x => x.IsActive == true && x.Estoption.Estheader.JobNumber == jobNumber && x.Estoption.IsActive == true && x.Estoption.Estheader.IsActive == true).ToList().GroupBy(x => new { ActivityId = x.EstactivityId, Estactivity = x.Estactivity, ReleaseCode = x.Estactivity.Releasecode, JobNumber = x.Estoption.Estheader.JobNumber, Estheader = x.Estoption.Estheader }).ToList().GroupBy(x => new {
                    ReleaseCode = x.Key.ReleaseCode,
                    JobNumber = x.Key.JobNumber,
                    Estheader = x.Key.Estheader
                }).ToList().GroupBy(x => new { Estheader = x.Key.Estheader, JobNumber = x.Key.JobNumber }).OrderBy(x => x.Key.Estheader.EstimateNumber).ToList();
                foreach (var job in getBudgets)
                {
                    var addJob = new CombinedPOBudgetTreeModel()
                    {
                        Id = Guid.NewGuid(),
                        JobNumber = job.Key.JobNumber,
                        EstimateNumber = job.Key.Estheader.EstimateNumber,
                        IsPendingCustomEstimate = job.Key.Estheader.EstimateDescPe == "Pending/Custom Estimate",
                        Estheader = _mapper.Map<EstheaderDto>(job.Key.Estheader),
                        IsIssued = job.All(x => x.All(y => y.All(z => z.EstjcedetailId != null))),
                        IssueEnabled = job.Any(x => x.Any(y => y.Any(z => z.EstjcedetailId == null && z.Podetail?.Poheader.Postatus != 5))),
                        Indeterminate = job.Any(x => x.Any(y => y.Any(z => z.EstjcedetailId != null))) && job.Any(x => x.Any(y => y.Any(z => z.EstjcedetailId == null))),
                        PoIsIssued = job.All(x => x.All(y => y.All(z => z.PodetailId != null))),
                        PoIssueEnabled = job.Any(x => x.Any(y => y.Any(z => z.PodetailId == null))),
                        PoIndeterminate = job.Any(x => x.Any(y => y.Any(z => z.PodetailId != null))) && job.Any(x => x.Any(y => y.Any(z => z.PodetailId == null))),
                        Children = new List<CombinedPOBudgetTreeModel>(),
                        HasChildren = true
                    };
                    foreach (var releaseCode in job.OrderBy(x => x.Key.ReleaseCode))
                    {
                        var addRelease = new CombinedPOBudgetTreeModel()
                        {
                            Id = Guid.NewGuid(),
                            ParentId = addJob.Id,
                            IsPendingCustomEstimate = addJob.IsPendingCustomEstimate,
                            EstimateNumber = job.Key.Estheader.EstimateNumber,
                            ReleaseCode = releaseCode.Key.ReleaseCode,
                            //Estoption = _mapper.Map<EstoptionDto>(releaseCode.Key.Estoption),
                            TotalCost = releaseCode.Sum(x => x.Sum(y => y.Amount)),
                            IsIssued = releaseCode.All(x => x.All(y => y.EstjcedetailId != null)),
                            IssueEnabled = releaseCode.Any(x => x.Any(y => y.EstjcedetailId == null && y.Podetail?.Poheader.Postatus != 5)),
                            Indeterminate = releaseCode.Any(x => x.Any(y => y.EstjcedetailId != null)) && releaseCode.Any(x => x.Any(y => y.EstjcedetailId == null)),
                            PoIsIssued = releaseCode.All(x => x.All(y => y.PodetailId != null)),
                            PoIssueEnabled = releaseCode.Any(x => x.Any(y => y.PodetailId == null)),
                            PoIndeterminate = releaseCode.Any(x => x.Any(y => y.PodetailId != null)) && releaseCode.Any(x => x.Any(y => y.PodetailId == null)),
                            HasChildren = true,
                            Children = new List<CombinedPOBudgetTreeModel>(),
                            // ExtraNumber = $"{releaseCode.Key.Estoption.OptionNumber} | {releaseCode.Key.Estoption.OptionDesc}",
                        };
                        foreach (var activity in releaseCode.OrderBy(x => x.Key.Estactivity.BomClass))
                        {
                            var addActivity = new CombinedPOBudgetTreeModel()
                            {
                                Id = Guid.NewGuid(),
                                ParentId = addRelease.Id,
                                IsPendingCustomEstimate = addJob.IsPendingCustomEstimate,
                                Estactivity = _mapper.Map<EstactivityDto>(activity.Key.Estactivity),
                                EstimateNumber = job.Key.Estheader.EstimateNumber,
                                HasChildren = true,
                                ActivityTotal = activity.Sum(x => x.Amount),
                                TotalAmount = (decimal?)activity.Sum(x => x.Amount),
                                IssuedAmount = (decimal?)activity.Where(x => x.EstjcedetailId != null).Sum(x => x.Amount),
                                ExtraNumber = addRelease.ExtraNumber,
                                IsIssued = activity.All(x => x.EstjcedetailId != null) ? true : false,
                                IssueEnabled = activity.Any(x => x.EstjcedetailId == null && x.Podetail?.Poheader.Postatus != 5),
                                Indeterminate = activity.Any(x => x.EstjcedetailId != null) && activity.Any(x => x.EstjcedetailId == null) ? true : false,
                                PoIsIssued = activity.All(x => x.PodetailId != null) ? true : false,
                                PoIssueEnabled = activity.Any(x => x.PodetailId == null),
                                PoIndeterminate = activity.Any(x => x.PodetailId != null) && activity.Any(x => x.PodetailId == null) ? true : false,
                                Children = activity.Select(x => new CombinedPOBudgetTreeModel()
                                {
                                    Id = Guid.NewGuid(),
                                    EstimateNumber = job.Key.Estheader.EstimateNumber,
                                    IsPendingCustomEstimate = addJob.IsPendingCustomEstimate,
                                    Estdetail = _mapper.Map<EstdetailDto>(x),
                                    Estactivity = _mapper.Map<EstactivityDto>(activity.Key.Estactivity),
                                    ExtraNumber = addRelease.ExtraNumber,
                                    IsIssued = x.EstjcedetailId != null,
                                    IssueEnabled = x.EstjcedetailId == null && x.Podetail?.Poheader.Postatus != 5,
                                    TotalAmount = (decimal?)x.Amount,
                                    IssuedAmount = x.EstjcedetailId != null && x.Estjcedetail?.IsCancelled != "T" ? (decimal?)x.Amount : 0,
                                    PoIsIssued = x.PodetailId != null,
                                    PoIssueEnabled = x.PodetailId == null,
                                    PoIndeterminate = false,
                                    Indeterminate = false,
                                    HasChildren = false
                                }).ToList()
                            };
                            addActivity.Estactivity.BoolTaxable = addActivity.Estactivity.Taxable == "T";
                            addActivity.Estactivity.BoolUseLocation = addActivity.Estactivity.UseLocation == "T";
                            addActivity.Estactivity.BoolUseWbsSort = addActivity.Estactivity.UseWbsSort == "T";
                            addRelease.Children.Add(addActivity);
                            foreach (var estdetail in addActivity.Children)
                            {
                                estdetail.Estdetail.Podetail = estdetail.Estdetail.Podetail ?? new PodetailDto() { Poheader = new PoheaderDto() { PostatusNavigation = new PostatusDto() } };
                                estdetail.Estdetail.Podetail = estdetail.Estdetail.Podetail ?? new PodetailDto() { Poheader = new PoheaderDto() { PostatusNavigation = new PostatusDto() } };
                                estdetail.Estdetail.Podetail.Poheader = estdetail.Estdetail.Podetail.Poheader ?? new PoheaderDto() { PostatusNavigation = new PostatusDto() };//Why are some of them null? 
                                estdetail.Estdetail.Podetail.Poheader.PostatusNavigation = estdetail.Estdetail.Podetail.Poheader.PostatusNavigation ?? new PostatusDto();
                                estdetail.ParentId = addActivity.Id;
                                estdetail.Estdetail.BoolLump = estdetail.Estdetail.Lump == "T";
                                estdetail.Estdetail.BoolTaxable = estdetail.Estdetail.Taxable == "T";
                                estdetail.Estdetail.BoolUseEstPrice = estdetail.Estdetail.UseEstPrice == "T";
                                estdetail.Estactivity.BoolTaxable = estdetail.Estactivity.Taxable == "T";
                                estdetail.Estactivity.BoolUseLocation = estdetail.Estactivity.UseLocation == "T";
                                estdetail.Estactivity.BoolUseWbsSort = estdetail.Estactivity.UseWbsSort == "T";
                                estdetail.SearchTags = $"{addJob.JobNumber}|{addJob.Estheader.EstimateDescPe}|{addJob.Estheader.ReferenceNumber}|{addJob.Estheader.ReferenceDesc}|{addRelease.ReleaseCode}|{addActivity.Estactivity.BomClass}|{estdetail.Estdetail.ItemNumber}|{estdetail.Estdetail.ItemDesc}";
                                addActivity.SearchTags = estdetail.SearchTags;
                                addRelease.SearchTags = estdetail.SearchTags;
                                addJob.SearchTags = estdetail.SearchTags;
                                if (!string.IsNullOrWhiteSpace(estdetail.Estdetail.Errors))
                                {
                                    estdetail.Estdetail.DisplayErrors = string.Join(", ", estdetail.Estdetail.Errors.Split('|').Select(x => ERP.Data.Models.ExtensionMethods.EnumExtension.GetDescriptionFromEnum((BudgetErrors)int.Parse(x))).ToList());
                                }
                                if (!string.IsNullOrWhiteSpace(estdetail.Estdetail.Warnings))
                                {
                                    estdetail.Estdetail.DisplayWarnings = string.Join(", ", estdetail.Estdetail.Warnings.Split('|').Select(x => ERP.Data.Models.ExtensionMethods.EnumExtension.GetDescriptionFromEnum((BudgetWarnings)int.Parse(x))).ToList());
                                }
                            }
                        }
                        addRelease.TotalAmount = addRelease.Children.Sum(x => x.TotalAmount);
                        addRelease.IssuedAmount = addRelease.Children.Sum(x => x.IssuedAmount);
                        addJob.Children.Add(addRelease);
                    }
                    addJob.TotalAmount = addJob.Children.Sum(x => x.TotalAmount);
                    addJob.IssuedAmount = addJob.Children.Sum(x => x.IssuedAmount);
                    listPending.Add(addJob);
                }
                return Ok(new ResponseModel<List<CombinedPOBudgetTreeModel>> { IsSuccess = true, Value = listPending });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CombinedPOBudgetTreeModel>> { IsSuccess = false, Message = "failed to get budget by job release sort", Value = null });

            }                                 
        }
        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> GetBudgetByJobAsync(string jobNumber)
        {
            try
            {
                var listPending = new List<CombinedPOBudgetTreeModel>();
                var getBudgets = _context.Estdetails.AsNoTrackingWithIdentityResolution().AsSplitQuery().Include("Estoption.Estheader.ReferenceTypeNavigation").Include("Estactivity").Include("Podetail.Poheader.PostatusNavigation").Include("Estjcedetail").Where(x => x.IsActive == true && x.Estoption.Estheader.JobNumber == jobNumber && x.Estoption.IsActive == true && x.Estoption.Estheader.IsActive == true && (x.Podetail == null || x.Podetail.IsActive == true) && (x.Podetail == null || x.Podetail.Poheader.IsActive == true)).ToList().GroupBy(x => new { ActivityId = x.EstactivityId, OptionId = x.EstoptionId, Estactivity = x.Estactivity, Estoption = x.Estoption, JobNumber = x.Estoption.Estheader.JobNumber, Estheader = x.Estoption.Estheader }).ToList().GroupBy(x => new
                {
                    OptionId = x.Key.OptionId,
                    Estoption = x.Key.Estoption,
                    JobNumber = x.Key.Estoption.Estheader.JobNumber,
                    Estheader
                 = x.Key.Estheader
                }).ToList().GroupBy(x => new { Estheader = x.Key.Estheader, JobNumber = x.Key.JobNumber }).OrderBy(x => x.Key.Estheader.EstimateNumber).ToList();
                var findBlockedVendors = _context.Suppliers.AsNoTrackingWithIdentityResolution().Where(x => x.Blocked == true).Select(x => x.SubNumber).Distinct().ToList();
                var findInsuranceExpiredVendors = _context.SupplierInsurances.AsNoTrackingWithIdentityResolution().Where(x => x.InsuranceRequired == 1 && x.PolicyExpirationDate.Date < DateTime.Now.Date && x.IsActive == true).Select(x => x.SubNumber).Distinct().ToList();
                foreach (var job in getBudgets)
                {
                    var addJob = new CombinedPOBudgetTreeModel()
                    {
                        Id = Guid.NewGuid(),
                        JobNumber = job.Key.JobNumber,
                        EstimateNumber = job.Key.Estheader.EstimateNumber,
                        IsPendingCustomEstimate = job.Key.Estheader.EstimateDescPe == "Pending/Custom Estimate",
                        Estheader = _mapper.Map<EstheaderDto>(job.Key.Estheader),
                        IsIssued = job.All(x => x.All(y => y.All(z => z.EstjcedetailId != null))),
                        IssueEnabled = job.Any(x => x.Any(y => y.Any(z => z.EstjcedetailId == null && z.Podetail?.Poheader.Postatus != 5))),
                        Indeterminate = job.Any(x => x.Any(y => y.Any(z => z.EstjcedetailId != null))) && job.Any(x => x.Any(y => y.Any(z => z.EstjcedetailId == null))),
                        PoIsIssued = job.All(x => x.All(y => y.All(z => z.PodetailId != null))),
                        PoIssueEnabled = job.Any(x => x.Any(y => y.Any(z => z.PodetailId == null))),
                        PoIndeterminate = job.Any(x => x.Any(y => y.Any(z => z.PodetailId != null))) && job.Any(x => x.Any(y => y.Any(z => z.PodetailId == null))),
                        Children = new List<CombinedPOBudgetTreeModel>(),
                        HasChildren = true
                    };
                    foreach (var option in job)
                    {
                        var addOption = new CombinedPOBudgetTreeModel()
                        {
                            Id = Guid.NewGuid(),
                            ParentId = addJob.Id,
                            IsPendingCustomEstimate = addJob.IsPendingCustomEstimate,
                            EstimateNumber = job.Key.Estheader.EstimateNumber,
                            Estoption = _mapper.Map<EstoptionDto>(option.Key.Estoption),
                            TotalCost = option.Sum(x => x.Sum(y => y.Amount)),
                            IsIssued = option.All(x => x.All(y => y.EstjcedetailId != null)),
                            IssueEnabled = option.Any(x => x.Any(y => y.EstjcedetailId == null && y.Podetail?.Poheader.Postatus != 5)),
                            Indeterminate = option.Any(x => x.Any(y => y.EstjcedetailId != null)) && option.Any(x => x.Any(y => y.EstjcedetailId == null)),
                            IssuedAmount = (decimal?)option.Sum(x => x.Where(y => y.Estjcedetail != null).DistinctBy(x=> x.EstjcedetailId).Sum(y => y.Estjcedetail?.EstimateAmount)),
                            PoIsIssued = option.All(x => x.All(y => y.PodetailId != null)),
                            PoIssueEnabled = option.Any(x => x.Any(y => y.PodetailId == null)),
                            PoIndeterminate = option.Any(x => x.Any(y => y.PodetailId != null)) && option.Any(x => x.Any(y => y.PodetailId == null)),
                            HasChildren = true,
                            Children = new List<CombinedPOBudgetTreeModel>(),
                            ExtraNumber = $"{option.Key.Estoption.OptionNumber} | {option.Key.Estoption.OptionDesc}",
                        };
                        foreach (var activity in option)
                        {
                            var addActivity = new CombinedPOBudgetTreeModel()
                            {
                                Id = Guid.NewGuid(),
                                ParentId = addOption.Id,
                                IsPendingCustomEstimate = addJob.IsPendingCustomEstimate,
                                Estoption = _mapper.Map<EstoptionDto>(option.Key.Estoption),
                                Estactivity = _mapper.Map<EstactivityDto>(activity.Key.Estactivity) ?? new EstactivityDto(),//Activity should not be null, this is bad
                                EstimateNumber = job.Key.Estheader.EstimateNumber,
                                HasChildren = true,
                                ActivityTotal = activity.Sum(x => x.Amount),
                                ExtraNumber = addOption.ExtraNumber,
                                //TotalAmount = (decimal?)activity.Sum(x => x.Amount),
                                //IssuedAmount = (decimal?)activity.Where(x => x.Estjcedetail != null).DistinctBy(x => x.EstjcedetailId).Sum(x => x.Estjcedetail?.EstimateAmount),
                                IsIssued = activity.All(x => x.EstjcedetailId != null) ? true : false,
                                IssueEnabled = activity.Any(x => x.EstjcedetailId == null),
                                Indeterminate = activity.Any(x => x.EstjcedetailId != null) && activity.Any(x => x.EstjcedetailId == null) ? true : false,
                                PoIsIssued = activity.All(x => x.PodetailId != null) ? true : false,
                                PoIssueEnabled = activity.Any(x => x.PodetailId == null),
                                PoIndeterminate = activity.Any(x => x.PodetailId != null) && activity.Any(x => x.PodetailId == null) ? true : false,
                                Children = activity.Select(x => new CombinedPOBudgetTreeModel()
                                {
                                    Id = Guid.NewGuid(),
                                    EstimateNumber = job.Key.Estheader.EstimateNumber,
                                    IsPendingCustomEstimate = addJob.IsPendingCustomEstimate,
                                    Estdetail = _mapper.Map<EstdetailDto>(x),
                                    TotalAmount = x.Estjcedetail?.IsCancelled == "T" || x.Podetail?.Poheader.Postatus == 5 ? 0: (decimal?)x.Amount,//3/20/25 don't include in total if it was cancelled
                                    //TotalAmount =  (decimal?)x.Amount,//4/11/25 include anywy
                                    IssuedAmount = x.EstjcedetailId != null && x.Estjcedetail?.IsCancelled != "T" ? x.EstimateExportAmt != null ? (decimal?)x.EstimateExportAmt : (decimal?)x.Amount : 0,//4/11/25 estimate export amoutn seems to be the amount sent to estjce, but ERP hasn't been filling it that way
                                    Estactivity = _mapper.Map<EstactivityDto>(activity.Key.Estactivity) ?? new EstactivityDto(),
                                    ExtraNumber = addOption.ExtraNumber,
                                    IsIssued = x.EstjcedetailId != null,
                                    IssueEnabled = x.EstjcedetailId == null && x.Podetail?.Poheader.Postatus != 5,//can be issued if not already issued and no cancelled po
                                    PoIsIssued = x.PodetailId != null,
                                    PoIssueEnabled = x.PodetailId == null,
                                    PoIndeterminate = false,
                                    Indeterminate = false,
                                    HasChildren = false
                                }).ToList()
                            };
                            if (addActivity.Estactivity.SelectedVendor != null && findBlockedVendors.Contains((int)addActivity.Estactivity.SelectedVendor))
                            {
                                addActivity.Estactivity.VendorBlocked = true;
                            }
                            if (addActivity.Estactivity.SelectedVendor != null && findInsuranceExpiredVendors.Contains((int)addActivity.Estactivity.SelectedVendor))
                            {
                                addActivity.Estactivity.VendorNoInsurance = true;
                            }
                            //addActivity.TotalAmount = addActivity.Children.Sum(x => x.TotalAmount);
                            //addActivity.IssuedAmount = addActivity.Children.Sum(x => x.IssuedAmount);
                            addActivity.Estactivity.BoolTaxable = addActivity.Estactivity.Taxable == "T";
                            addActivity.Estactivity.BoolUseLocation = addActivity.Estactivity.UseLocation == "T";
                            addActivity.Estactivity.BoolUseWbsSort = addActivity.Estactivity.UseWbsSort == "T";
                            addOption.Children.Add(addActivity);
                            foreach (var estdetail in addActivity.Children)
                            {
                                if(addActivity.Estactivity.VendorBlocked == true|| addActivity.Estactivity.VendorNoInsurance == true)
                                {
                                    estdetail.PoIssueEnabled = false;
                                }
                                estdetail.Estdetail.OrigCommittedAmount = estdetail.Estdetail.OrigCommittedAmount ?? estdetail.Estdetail.Amount;//For PO, so the amount can change for the po but not change the budget
                                estdetail.Estdetail.OrigCommittedQty = estdetail.Estdetail.OrigCommittedQty ?? estdetail.Estdetail.OrderQty;
                                estdetail.Estdetail.OrgPrice = estdetail.Estdetail.OrgPrice ?? estdetail.Estdetail.Price;
                                estdetail.Estdetail.Podetail = estdetail.Estdetail.Podetail ?? new PodetailDto() { Poheader = new PoheaderDto() { PostatusNavigation = new PostatusDto() } };
                                estdetail.Estdetail.Podetail.Poheader = estdetail.Estdetail.Podetail.Poheader ?? new PoheaderDto() { PostatusNavigation = new PostatusDto() };//Why are some of them null? 
                                estdetail.Estdetail.Podetail.Poheader.PostatusNavigation = estdetail.Estdetail.Podetail.Poheader.PostatusNavigation ?? new PostatusDto();
                                estdetail.ParentId = addActivity.Id;
                                estdetail.Estdetail.BoolLump = estdetail.Estdetail.Lump == "T";
                                estdetail.Estdetail.BoolTaxable = estdetail.Estdetail.Taxable == "T";
                                estdetail.Estdetail.BoolUseEstPrice = estdetail.Estdetail.UseEstPrice == "T";
                                estdetail.Estactivity.BoolTaxable = estdetail.Estactivity.Taxable == "T";
                                estdetail.Estactivity.BoolUseLocation = estdetail.Estactivity.UseLocation == "T";
                                estdetail.Estactivity.BoolUseWbsSort = estdetail.Estactivity.UseWbsSort == "T";
                                estdetail.SearchTags = $"{addJob.JobNumber}|{addJob.Estheader.EstimateDescPe}|{addJob.Estheader.ReferenceNumber}|{addJob.Estheader.ReferenceDesc}|{addOption.Estoption.OptionNumber}|{addOption.Estoption.OptionDesc}|{addActivity.Estactivity.BomClass}|{estdetail.Estdetail.ItemNumber}|{estdetail.Estdetail.ItemDesc}";
                                addActivity.SearchTags = estdetail.SearchTags;
                                addOption.SearchTags = estdetail.SearchTags;
                                addJob.SearchTags = estdetail.SearchTags;
                                if (!string.IsNullOrWhiteSpace(estdetail.Estdetail.Errors))
                                {
                                    var errors = estdetail.Estdetail.Errors.Split('|').ToList();
                                    var stringErrors = new List<string>();
                                    foreach (var error in errors)
                                    {
                                        int intError;
                                        if (int.TryParse(error, out intError))
                                        {
                                            //TODO: also check it's in the range of the enum; there must be cleaner way.
                                            stringErrors.Add(EnumExtension.GetDescriptionFromEnum((BudgetErrors)intError));
                                        }
                                    }
                                    estdetail.Estdetail.DisplayErrors = string.Join(", ", stringErrors);
                                }
                                if (!string.IsNullOrWhiteSpace(estdetail.Estdetail.Warnings))
                                {
                                    var warnings = estdetail.Estdetail.Warnings.Split('|').ToList();
                                    var stringWarnings = new List<string>();
                                    foreach (var warning in warnings)
                                    {
                                        int intWarning;
                                        if (int.TryParse(warning, out intWarning))
                                        {
                                            //TODO: also check it's in the range of the enum; there must be cleaner way.
                                            stringWarnings.Add(EnumExtension.GetDescriptionFromEnum((BudgetWarnings)intWarning));
                                        }
                                    }
                                    estdetail.Estdetail.DisplayWarnings = string.Join(", ", stringWarnings);
                                }
                            }
                            addActivity.TotalAmount = addActivity.Children.Sum(x => x.TotalAmount);
                            addActivity.IssuedAmount = addActivity.Children.Sum(x => x.IssuedAmount);
                        }
                        addOption.TotalAmount = addOption.Children.Sum(x => x.TotalAmount);
                        addOption.IssuedAmount = addOption.Children.Sum(x => x.IssuedAmount);
                        addOption.Children = addOption.Children.OrderBy(x => x.Estactivity?.BomClass).ToList();
                        addJob.Children.Add(addOption);
                    }
                    addJob.TotalAmount = addJob.Estheader.ReferenceType != 2 ? addJob.Children.Sum(x => x.TotalAmount) : 0;
                    addJob.IssuedAmount = addJob.Estheader.ReferenceType != 2 ? addJob.Children.Sum(x => x.IssuedAmount) : 0;
                    addJob.Children = addJob.Children.OrderBy(x => x.Estoption?.OptionNumber).ToList();
                    listPending.Add(addJob);
                }
                
                return Ok(new ResponseModel<List<CombinedPOBudgetTreeModel>>() { IsSuccess = true, Message = "retrieved budget", Value = listPending });
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CombinedPOBudgetTreeModel>> { IsSuccess = false, Message = "failed to get budget by job", Value = null });
            }                          
        }

        //not used
        [HttpGet]
        public async Task<IActionResult> GetSendToAccountingEFAsync()
        {
            var listPending = new List<CombinedJCETreeModel>();
            try
            {

                //11/14 NOTE: added check for WMS Source load to prevent sending to NAV items that were sent from WMS. Need to revisit this
                //11/18 remove above check for Wms load so there's some test data

                //for integrate with NAV, show ones that were exported in past 2 months so they can mark it and send again if needed
                //var getPending2 = _context.Estjcedetails.AsNoTrackingWithIdentityResolution().Include("Estoption.Estheader").Where(x => x.IsActive == true && x.EstimateAmount != 0 && x.EstjcedetailId != null && x.UpdatedBy != "WMSSourceLoad1" && x.CreatedBy != null && x.EstimateDate > DateTime.Now.AddMonths(-2)).GroupBy(x => new { OptionId = x.EstoptionId, JobNumber = x.Estoption.Estheader.JobNumber }).ToList().GroupBy(x => x.Key.JobNumber).ToList();
                var getPending = _context.Estjcedetails.AsNoTrackingWithIdentityResolution().Include("Estoption.Estheader").Where(x => x.IsActive == true && x.EstimateAmount != 0 && x.EstjcedetailId != null  && x.EstimateDate > DateTime.Now.AddMonths(-2)).GroupBy(x => new { OptionId = x.EstoptionId, JobNumber = x.Estoption.Estheader.JobNumber }).ToList().GroupBy(x => x.Key.JobNumber).ToList();
                //var getPending = _context.Estjcedetails.Include("Estoption.Estheader").Where(x => x.IsActive == true && x.EstimateExported != "T" && x.ExportBc != true && x.EstjcedetailId != null).GroupBy(x => new { OptionId = x.EstoptionId, JobNumber = x.Estoption.Estheader.JobNumber }).ToList().GroupBy(x => x.Key.JobNumber).ToList();
                var findBlockedVendors = _context.Suppliers.AsNoTrackingWithIdentityResolution().Where(x => x.Blocked == true).Select(x => x.SubNumber).Distinct().ToList();
                var findInsuranceExpiredVendors = _context.SupplierInsurances.AsNoTrackingWithIdentityResolution().Where(x => x.InsuranceRequired == 1 && x.PolicyExpirationDate.Date < DateTime.Now.Date && x.IsActive == true).Select(x => x.SubNumber).Distinct().ToList();
                foreach (var job in getPending)
                {
                    var addJob = new CombinedJCETreeModel()
                    {
                        Id = Guid.NewGuid(),
                        JobNumber = job.Key,                        
                        Children = new List<CombinedJCETreeModel>(),
                        HasChildren = true,
                        SearchTags = job.Key
                    };
                    foreach (var option in job)
                    {
                        var addOption = new CombinedJCETreeModel()
                        {
                            Id = Guid.NewGuid(),
                            Estoption = _mapper.Map<EstoptionDto>(option.FirstOrDefault().Estoption),
                            HasChildren = true,
                            Amount = (decimal?)option.Sum(x => x.EstimateAmount),
                            Children = option.Select(x => new CombinedJCETreeModel()
                            {
                                Id = Guid.NewGuid(),
                                Estjcedetail = _mapper.Map<EstjcedetailDto>(x),
                            }).ToList()
                        };
                        addOption.SearchTags = $"{job.Key}|{addOption.Estoption.OptionDesc}|{addOption.Estoption.OptionNumber}";
                        addJob.SearchTags += $"|{addOption.Estoption.OptionNumber}|{addOption.Estoption.OptionDesc}";
                        foreach (var estdetail in addOption.Children) 
                        {
                            estdetail.Estjcedetail.BoolEstimateExported = estdetail.Estjcedetail.EstimateExported == "T";
                            estdetail.Amount = (decimal?)estdetail.Estjcedetail.EstimateAmount;
                            estdetail.BoolExported = estdetail.Estjcedetail.EstimateExported == "T";
                            addJob.SearchTags += $"|{estdetail.Estjcedetail.Costcode}";
                            addOption.SearchTags += $"|{estdetail.Estjcedetail.Costcode}";
                            estdetail.SearchTags = $"{job.Key}|{addOption.Estoption.OptionDesc}|{addOption.Estoption.OptionNumber}|{estdetail.Estjcedetail.Costcode}";
                        }
                        addOption.BoolExported = addOption.Children.All(x => x.BoolExported == true) ? true : false;
                        addJob.Children.Add(addOption);
                    }
                    addJob.BoolExported = addJob.Children.All(x => x.BoolExported == true) ? true : false;
                    addJob.Amount = addJob.Children.Sum(x => x.Amount);
                    listPending.Add(addJob);
                }
            }

            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CombinedJCETreeModel>> { IsSuccess = false, Message = "failed to get send to accounting", Value = null });
            }
            
            return Ok(new ResponseModel<List<CombinedJCETreeModel>> { IsSuccess = true, Value = listPending });
        }

        [HttpGet]
        public async Task<IActionResult> GetSendToAccountingAsync()
        {
            var listPending = new List<CombinedJCETreeModel>();
            try
            {
                //var getPending = _context.Estjcedetails.AsNoTrackingWithIdentityResolution().Include("Estoption.Estheader").Where(x => x.IsActive == true && x.EstimateAmount != 0 && x.EstjcedetailId != null && x.EstimateDate > DateTime.Now.AddMonths(-2)).GroupBy(x => new { OptionId = x.EstoptionId, JobNumber = x.Estoption.Estheader.JobNumber }).ToList().GroupBy(x => x.Key.JobNumber).ToList();

                var getPending2 = new List<EstjcedetailDto>();
                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT jc.ESTJCEDETAIL_ID, jc.ESTOPTION_ID, jc.COSTCODE, jc.CATEGORY, jc.ESTIMATE_DATE, jc.ESTIMATE_UNITS, JC.UNIT_DESC, JC.ESTIMATE_AMOUNT, JC.ESTIMATE_EXPORTED, jc.EXPORT_BC, eo.ESTOPTION_ID, eo.ESTHEADER_ID, eh.JOB_NUMBER, eh.ESTIMATE_NUMBER, eh.ESTIMATE_DESC_PE, eo.OPTION_NUMBER, eo.OPTION_DESC, jt.Job_Task_No  FROM dbo.ESTJCEDETAIL jc JOIN dbo.ESTOPTION eo ON jc.ESTOPTION_ID = eo.ESTOPTION_ID JOIN dbo.ESTHEADER eh ON eo.ESTHEADER_ID = eh.ESTHEADER_ID LEFT JOIN dbo.JOB_TASK jt ON  jt.JOB_NUMBER = eh.JOB_NUMBER AND jt.Job_Task_No = jc.COSTCODE  WHERE  jc.ESTIMATE_AMOUNT != 0 AND jc.ESTIMATE_DATE >= DATEADD(day, CAST(-20.0E0 AS int), GETDATE()) and (jc.EXPORT_BC IS NULL or jc.EXPORT_BC = 0)";//3/20/25 added condition for only not exported ones, since for BC dont need option to send again 
                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        bool missingJobTask = reader.GetValue(17) == DBNull.Value ? true : false;
                        var addjceDetail = new EstjcedetailDto()
                        {
                            EstjcedetailId = reader.GetValue(0) != DBNull.Value ? (int)reader.GetValue(0) : 0,
                            EstoptionId = reader.GetValue(1) != DBNull.Value ? (int)reader.GetValue(1) : 0,
                            Costcode = reader.GetValue(2) != DBNull.Value ? (string)reader.GetValue(2) : null,
                            Category = reader.GetValue(3) != DBNull.Value ? (string)reader.GetValue(3) : null,
                            EstimateDate = reader.GetValue(4) != DBNull.Value ? (DateTime?)reader.GetValue(4) : null,
                            EstimateUnits = reader.GetValue(5) != DBNull.Value ? (double?)reader.GetValue(5) : null,
                            UnitDesc = reader.GetValue(6) != DBNull.Value ? (string)reader.GetValue(6) : null,
                            EstimateAmount = reader.GetValue(7) != DBNull.Value ? (double?)reader.GetValue(7) : null,
                            EstimateExported = reader.GetValue(8) != DBNull.Value ? (string)reader.GetValue(8) : null,
                            ExportBc = reader.GetValue(9) != DBNull.Value ? (bool?)reader.GetValue(9) : null,
                            MissingJobTask = missingJobTask,
                            Estoption = new EstoptionDto()
                            {
                                Estheader = new EstheaderDto()
                                {
                                    EstheaderId = reader.GetValue(11) != DBNull.Value ? (int)reader.GetValue(11) : 0,
                                    JobNumber = reader.GetValue(12) != DBNull.Value ? (string)reader.GetValue(12) : "",
                                    EstimateNumber = reader.GetValue(13) != DBNull.Value ? (int)reader.GetValue(13) : null,
                                },
                                EstoptionId = reader.GetValue(10) != DBNull.Value ? (int)reader.GetValue(10) : 0,
                                EstheaderId = reader.GetValue(11) != DBNull.Value ? (int)reader.GetValue(11) : 0,
                                OptionNumber = reader.GetValue(15) != DBNull.Value ? (string)reader.GetValue(15) : null,
                                OptionDesc = reader.GetValue(16) != DBNull.Value ? (string)reader.GetValue(16) : null,
                            }
                        };
                        getPending2.Add(addjceDetail);
                    }
                }


                var grouped = getPending2.GroupBy(x => new { OptionId = x.EstoptionId, JobNumber = x.Estoption.Estheader.JobNumber }).ToList().GroupBy(x => x.Key.JobNumber).OrderBy(x => x.Key).ToList();

                //var findBlockedVendors = _context.Suppliers.AsNoTrackingWithIdentityResolution().Where(x => x.Blocked == true).Select(x => x.SubNumber).Distinct().ToList();
                //var findInsuranceExpiredVendors = _context.SupplierInsurances.AsNoTrackingWithIdentityResolution().Where(x => x.InsuranceRequired == 1 && x.PolicyExpirationDate.Date < DateTime.Now.Date).Select(x => x.SubNumber).Distinct().ToList();
                foreach (var job in grouped)
                {
                    var addJob = new CombinedJCETreeModel()
                    {
                        Id = Guid.NewGuid(),
                        JobNumber = job.Key,
                        Children = new List<CombinedJCETreeModel>(),
                        HasChildren = true,
                        SearchTags = job.Key
                    };
                    foreach (var option in job)
                    {
                        var addOption = new CombinedJCETreeModel()
                        {
                            Id = Guid.NewGuid(),
                            Estoption = option.FirstOrDefault().Estoption,
                            HasChildren = true,
                            //Amount = (decimal?)option.Sum(x => x.EstimateAmount),
                            Children = option.Select(x => new CombinedJCETreeModel()
                            {
                                Id = Guid.NewGuid(),
                                Estjcedetail = x,
                            }).ToList()
                        };
                        addOption.SearchTags = $"{job.Key}|{addOption.Estoption.OptionDesc}|{addOption.Estoption.OptionNumber}";
                        addJob.SearchTags += $"|{addOption.Estoption.OptionNumber}|{addOption.Estoption.OptionDesc}";
                        foreach (var estdetail in addOption.Children)
                        {
                            estdetail.Estjcedetail.BoolEstimateExported = estdetail.Estjcedetail.EstimateExported == "T";
                            estdetail.Amount = (decimal?)estdetail.Estjcedetail.EstimateAmount;
                            estdetail.BoolExported = estdetail.Estjcedetail.EstimateExported == "T";
                            addJob.SearchTags += $"|{estdetail.Estjcedetail.Costcode}";
                            addOption.SearchTags += $"|{estdetail.Estjcedetail.Costcode}";
                            estdetail.SearchTags = $"{job.Key}|{addOption.Estoption.OptionDesc}|{addOption.Estoption.OptionNumber}|{estdetail.Estjcedetail.Costcode}";
                        }
                        addOption.BoolExported = addOption.Children.All(x => x.BoolExported == true) ? true : false;
                        addOption.Amount = addOption.Children.Sum(x => x.Amount);
                        addJob.Children.Add(addOption);
                    }
                    addJob.BoolExported = addJob.Children.All(x => x.BoolExported == true) ? true : false;
                    addJob.Amount = addJob.Children.Sum(x => x.Amount);
                    listPending.Add(addJob);
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CombinedJCETreeModel>> { IsSuccess = false, Message = "failed to get send to accounting", Value = null });
            }

            return Ok(new ResponseModel<List<CombinedJCETreeModel>> { IsSuccess = true, Value = listPending });
        }
        [HttpGet]
        public async Task<IActionResult> GetPendingJCEDetailsAsync()
        {
            try
            {
                var getPendingJce = await _context.Estjcedetails.AsNoTracking().Include("Estoption.Estheader").Where(x => x.IsActive == true && x.EstimateExported != "T").ToListAsync();
                var pendingJceDto = _mapper.Map<List<EstjcedetailDto>>(getPendingJce);               
                return Ok(new ResponseModel<List<EstjcedetailDto>> { IsSuccess = true, Value = pendingJceDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstjcedetailDto>> { IsSuccess = false, Message = "failed to get pending JCE details", Value = null });
            }
        }
        [HttpGet("{estOptionId}")]
        public async Task<IActionResult> GetPendingJCEDetailsByOptionAsync(int estOptionId)
        {
            try
            {
                var getPendingJce = await _context.Estdetails.AsNoTracking().Include("Estjcedetail.Estoption.Estheader").Where(x => x.IsActive == true && x.EstimateExported != "T" && x.EstoptionId == estOptionId && x.EstjcedetailId != null).Select(x => x.Estjcedetail).ToListAsync();
                var pendingJceDto = _mapper.Map<List<EstjcedetailDto>>(getPendingJce);
                return Ok(new ResponseModel<List<EstjcedetailDto>> { IsSuccess = true, Value = pendingJceDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstjcedetailDto>> { IsSuccess = false, Message = "failed to get pending JCE details by option", Value = null });
            }
        }
        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> GetPendingJCEDetailOptionsByJobAsync(string jobNumber)
        {
            try
            {
                var getPendingJce = await _context.Estdetails.AsNoTrackingWithIdentityResolution().Include("Estjcedetail.Estoption.Estheader").Where(x => x.IsActive == true && x.EstimateExported != "T" && x.Estoption.Estheader.JobNumber == jobNumber && x.EstjcedetailId != null).Select(x => x.Estjcedetail.Estoption).Distinct().ToListAsync();
                var pendingJceDto = _mapper.Map<List<EstoptionDto>>(getPendingJce);
                return Ok(new ResponseModel<List<EstoptionDto>> { IsSuccess = true, Value = pendingJceDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstoptionDto>> { IsSuccess = false, Message = "failed to get pending JCE details by job", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetPendingJCEDetailsJobsAsync()
        {
            try
            {
                var getPendingJceJobNumber = await _context.Estdetails.AsNoTrackingWithIdentityResolution().Include("Estjcedetail.Estoption.Estheader").Where(x => x.IsActive == true && x.EstimateExported != "T" && x.EstjcedetailId != null).Select(x => x.Estoption.Estheader.JobNumber).Distinct().OrderBy(x => x).ToListAsync();
                return Ok(new ResponseModel<List<string>> { IsSuccess = true, Value = getPendingJceJobNumber });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<string>> { IsSuccess = false, Message = "failed to get pending JCE details jobs", Value = null });
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetCustomEstimatesAsync()
        {
            try
            {
                var getCustEstimates = await _context.Estcustoptions.AsNoTracking().Include("Estheader").Include("Subdivision").Where(x => x.IsActive == true).ToListAsync();
                var custEstDto = _mapper.Map<List<EstcustoptionDto>>(getCustEstimates);
                foreach (var est in custEstDto)
                {
                    est.IsBuilderapproved = est.Builderapproved == 1 ? true : false;
                    est.IsCustomerapproved = est.Customerapproved == 1 ? true : false;
                    est.IsEstimateCompleted = est.EstimateCompleted == "T" ? true : false;
                }
                return Ok(new ResponseModel<List<EstcustoptionDto>> { IsSuccess = true, Value = custEstDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstcustoptionDto>> { IsSuccess = false, Message = "failed to get custom estimates", Value = null });
            }
        }
        [HttpGet("{custEstId}")]
        public async Task<IActionResult> GetCustomEstimateAsync(int custEstId)
        {
            try
            {
                var getCustOption = await _context.Estcustoptions.AsNoTracking().Include("Subdivision").Include("Estheader").SingleOrDefaultAsync(x => x.IsActive == true && x.EstcustoptionId == custEstId);
                var custEstOptionDto = _mapper.Map<EstcustoptionDto>(getCustOption);
                return Ok(new ResponseModel<EstcustoptionDto> { IsSuccess = true, Value = custEstOptionDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstcustoptionDto> { IsSuccess = false, Message = "failed to get custom estimate", Value = null });
            }
        }
        [HttpGet("{custEstId}")]
        public async Task<IActionResult> GetCustomEstimateDetailsAsync(int custEstId)
        {
            try
            {
                var getCustOption = await _context.Estcustoptions.AsNoTracking().SingleOrDefaultAsync(x => x.IsActive == true && x.EstcustoptionId == custEstId);
                var getCustEstimates = await _context.Estheaders.AsNoTracking().Include("ReferenceTypeNavigation").Where(x => x.IsActive == true && x.EstheaderId == getCustOption.EstheaderId).ToListAsync();
                var custEstHeadersDto = _mapper.Map<List<EstheaderDto>>(getCustEstimates);
                return Ok(new ResponseModel<List<EstheaderDto>> { IsSuccess = true, Value = custEstHeadersDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstheaderDto>> { IsSuccess = false, Message = "failed to get custom estimate details", Value = null });
            }
        }
        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> GetEstimatesForJobAsync(string jobNumber)
        {
            try
            {
                var getCustEstimates = await _context.Estheaders.AsNoTracking().Include("ReferenceTypeNavigation").Where(x => x.IsActive == true && x.JobNumber == jobNumber && x.EstimateDescPe == "Pending/Custom Estimate").ToListAsync();
                var custEstHeadersDto = _mapper.Map<List<EstheaderDto>>(getCustEstimates);
                return Ok(new ResponseModel<List<EstheaderDto>> { IsSuccess = true, Value = custEstHeadersDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstheaderDto>> { IsSuccess = false, Message = "failed to get estimates for job", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> AttachEstimateToOptionAsync([FromBody] AttachEstimateModel attachEstimateModel)
        {
            //TODO: belongs in salesconfig controller?
            try
            {
                if (attachEstimateModel.SalesConfigCoOptionsId != null)
                {
                    var findOption = _context.Salesconfigcooptions.SingleOrDefault(x => x.SalesconfigcooptionsId == attachEstimateModel.SalesConfigCoOptionsId);
                    var findHeader = _context.Estheaders.SingleOrDefault(x => x.EstheaderId == attachEstimateModel.EstHeaderId);
                    findOption.AssociatedEstimate = $"{findHeader.EstheaderId}-{findHeader.ReferenceNumber}-{findHeader.ReferenceDesc}";
                    findOption.EstheaderId = findHeader.EstheaderId;
                    _context.Salesconfigcooptions.Update(findOption);
                    await _context.SaveChangesAsync();
                    attachEstimateModel.AttachEstimateName = findOption.AssociatedEstimate;
                    attachEstimateModel.EstimateCost = _context.Estdetails.Where(x => x.Estoption.EstheaderId == findHeader.EstheaderId).Sum(x => x.Amount);
                }
                else if (attachEstimateModel.SalesConfigOptionsId != null)
                {
                    var findOption = _context.Salesconfigoptions.SingleOrDefault(x => x.SalesconfigoptionsId == attachEstimateModel.SalesConfigOptionsId);
                    var findHeader = _context.Estheaders.SingleOrDefault(x => x.EstheaderId == attachEstimateModel.EstHeaderId);
                    findOption.AssociatedEstimate = $"{findHeader.EstheaderId}-{findHeader.ReferenceNumber}-{findHeader.ReferenceDesc}";
                    findOption.EstheaderId = findHeader.EstheaderId;
                    _context.Salesconfigoptions.Update(findOption);
                    await _context.SaveChangesAsync();
                    attachEstimateModel.AttachEstimateName = findOption.AssociatedEstimate;//in order to return the new attached name
                    attachEstimateModel.EstimateCost = _context.Estdetails.Where(x => x.Estoption.EstheaderId == findHeader.EstheaderId).Sum(x => x.Amount);
                }

                return Ok(new ResponseModel<AttachEstimateModel> { IsSuccess = true, Value = attachEstimateModel });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<AttachEstimateModel> { IsSuccess = false, Message = "failed to attach estimate to option", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeleteCustomEstimateAsync([FromBody] EstcustoptionDto custOptionToDelete)
        {
            try
            {
                var findCustOption = await _context.Estcustoptions.SingleOrDefaultAsync(x => x.EstcustoptionId == custOptionToDelete.EstcustoptionId);
                findCustOption.IsActive = false;
                findCustOption.UpdatedDateTime = DateTime.Now;
                //findCustOption.updateby
                _context.Estcustoptions.Update(findCustOption);
                await _context.SaveChangesAsync();
                var findEst = await _context.Estheaders.SingleOrDefaultAsync(x => x.EstheaderId == findCustOption.EstheaderId);
                findEst.IsActive = false;
                findEst.UpdatedDateTime = DateTime.Now;
                findEst.UpdatedBy = User.Identity.Name.Split('@')[0];
                _context.Estheaders.Update(findEst);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<EstcustoptionDto> { IsSuccess = true, Value = custOptionToDelete });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstcustoptionDto> { IsSuccess = false, Message = "failed to delete custom estimate", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdateCustomEstimateAsync([FromBody] EstcustoptionDto custOptionToUpdate)
        {
            try
            {
                var findCustOption = await _context.Estcustoptions.SingleOrDefaultAsync(x => x.EstcustoptionId == custOptionToUpdate.EstcustoptionId);
                findCustOption.Optiondesc = custOptionToUpdate.Optiondesc;
                findCustOption.Optioncode = custOptionToUpdate.Optioncode;
                findCustOption.EstimateCompleted = custOptionToUpdate.IsEstimateCompleted ? "T" : "F";
                findCustOption.UpdatedDateTime = DateTime.Now;
                //findCustOption.updateby
                _context.Estcustoptions.Update(findCustOption);
                await _context.SaveChangesAsync();

                return Ok(new ResponseModel<EstcustoptionDto> { IsSuccess = true, Value = custOptionToUpdate });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstcustoptionDto> { IsSuccess = false, Message = "failed to update custom estimate", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> AddCustomEstimateAsync([FromBody] AddCustomEstimateModel addCustomEstimate)
        {
            try
            {
                var addHeader = new Estheader()
                {
                    JobNumber = "CUSTEST00001",
                    ReferenceType = 99,//default
                    ReferenceDesc = addCustomEstimate.Description,
                    //ReferenceNumber = ??
                    EstimateDescPe = "Pending/Custom Estimate",
                    Estimator = addCustomEstimate.EstimatorName,
                    EstimateSource = 3,//This seems to be default for pending custom option estimate
                    CreatedBy = User.Identity.Name.Split('@')[0]
                };
                _context.Estheaders.Add(addHeader);
                await _context.SaveChangesAsync();
                var addEstimate = new Estcustoption()
                {
                    Optioncode = addCustomEstimate.OptionCode,
                    Optiondesc = addCustomEstimate.Description,
                    JobNumber = addCustomEstimate.JobNumber,
                    EstheaderId = addHeader.EstheaderId,
                    SubdivisionId = addCustomEstimate.SubdivisionId,
                    EstimatorEmail = addCustomEstimate.EsimatorEmail,
                    Price = addCustomEstimate.SalesPrice,
                    Customerdesc = addCustomEstimate.CustomerInfoNotes,
                    EstimatorNotes = addCustomEstimate.Notes,
                };
                _context.Estcustoptions.Add(addEstimate);
                await _context.SaveChangesAsync();

                var addOption = new Estoption()
                {
                    OptionNumber = addCustomEstimate.OptionCode,
                    OptionDesc = addCustomEstimate.Description,
                    EstheaderId = addHeader.EstheaderId,
                    CreatedBy = User.Identity.Name.Split('@')[0]
                };
                _context.Estoptions.Add(addOption);
                await _context.SaveChangesAsync();
                var addOptionDto = _mapper.Map<EstoptionDto>(addOption);
                //var addEstimateDto = _mapper.Map<EstheaderDto>(addHeader);
                return Ok(new ResponseModel<EstoptionDto> { IsSuccess = true, Value = addOptionDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstoptionDto> { IsSuccess = false, Message = "failed to add custom estimate", Value = null });
            }
            
        }
        [HttpPost]
        public async Task<IActionResult> AddPendingEstimateAsync([FromBody] EstheaderDto addCustomEstimate)
        {
            //TODO: why is this a separate method from add estimate (below)?
            try
            {
                var getMaxEstNumber = _context.Estheaders.Where(x => x.JobNumber == addCustomEstimate.JobNumber).Max(x => x.EstimateNumber);
                var newEstNumber = getMaxEstNumber != null ? getMaxEstNumber + 1 : 1;
                var addHeader = new Estheader()
                {
                    JobNumber = addCustomEstimate.JobNumber,
                    ReferenceNumber = addCustomEstimate.ReferenceNumber,
                    EstimateNumber = newEstNumber,
                    ReferenceType = 99,//default
                    ReferenceDesc = addCustomEstimate.ReferenceDesc,
                    EstimateDescPe = addCustomEstimate.EstimateDescPe,//TODO: Default this to "Pending/Custom Estimate"
                    Estimator = User.Identity.Name.Split('@')[0],
                    EstimateSource = 3,//This seems to be default for pending custom option estimate 
                    CreatedBy = User.Identity.Name.Split('@')[0]
                };
                _context.Estheaders.Add(addHeader);
                await _context.SaveChangesAsync();
                var addOption = new Estoption()
                {
                    OptionNumber = "Base",
                    OptionDesc = "Base house",                    
                    EstheaderId = addHeader.EstheaderId,
                    OptionQty = 1,//default to this for now, but they might need to pick it
                    //SubdivisionId = addCustomEstimate.SubdivisionId,
                    //EstimatorEmail = addCustomEstimate.EsimatorEmail
                };
                _context.Estoptions.Add(addOption);
                await _context.SaveChangesAsync();
                var addOptionDto = _mapper.Map<EstoptionDto>(addOption);
                var getOption = _context.Estoptions.Include("Estheader.ReferenceTypeNavigation").SingleOrDefault(x => x.EstoptionId == addOption.EstoptionId);
                var getOptionDto = _mapper.Map<EstoptionDto>(getOption);
                return Ok(new ResponseModel<EstoptionDto> { IsSuccess = true, Value = getOptionDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstoptionDto> { IsSuccess = false, Message = "failed to add pending estimate", Value = null });
            }

        }
        
        [HttpPost]
        public async Task<IActionResult> AddEstheaderAsync([FromBody] EstheaderDto addEstimate)
        {
            try
            {
                var getMaxEstNumber = _context.Estheaders.Where(x => x.JobNumber == addEstimate.JobNumber).Max(x => x.EstimateNumber);
                var newEstNumber = getMaxEstNumber != null ? getMaxEstNumber + 1 : 1;
                var addHeader = new Estheader()
                {
                    JobNumber = addEstimate.JobNumber,
                    ReferenceNumber = addEstimate.ReferenceNumber,
                    EstimateNumber = newEstNumber,
                    ReferenceType = addEstimate.ReferenceType,
                    ReferenceDesc = addEstimate.ReferenceDesc,
                    EstimateDescPe = addEstimate.EstimateDescPe,
                    Estimator = User.Identity.Name.Split('@')[0],
                    EstimateSource = 3,//TODO: this will come from either radio box custom estimate, item takeoof, or option takeoof, I think it should pick "Issue variance of option 
                    CreatedBy = User.Identity.Name.Split('@')[0]
                };
                _context.Estheaders.Add(addHeader);
                await _context.SaveChangesAsync();
                var getEstimate = _context.Estheaders.Include(x => x.ReferenceTypeNavigation).SingleOrDefault(x => x.EstheaderId == addHeader.EstheaderId);
                var addEstimateDto = _mapper.Map<EstheaderDto>(getEstimate);
                return Ok(new ResponseModel<EstheaderDto>() { Value = addEstimateDto, IsSuccess = true, Message = "Added estimate" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstheaderDto>() { Value = null, IsSuccess = false, Message = "Failed to add estimate" });
            }
        }
        [HttpPost]
        public async Task<IActionResult> AddEstoptionAsync([FromBody] EstoptionDto addEstOption)
        {
            try
            {
                var addOption = new Estoption()
                {
                    EstheaderId = addEstOption.EstheaderId,
                    OptionDesc = addEstOption.OptionDesc,
                    OptionNumber = addEstOption.OptionNumber,
                    OptionQty = 1,
                    CreatedBy = User.Identity.Name.Split('@')[0]
                };
                _context.Estoptions.Add(addOption);
                await _context.SaveChangesAsync();
                var addOptionDto = _mapper.Map<EstoptionDto>(addOption);
                return Ok(new ResponseModel<EstoptionDto>() { Value = addOptionDto, IsSuccess = true, Message = "Added option" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstoptionDto>() { Value = null, IsSuccess = false, Message = "Failed to add option" });
            }
        }
        [HttpPost]
        public async Task<IActionResult> AddEstoptionsAsync([FromBody] List<EstoptionDto> addEstOptions)
        {
            try
            {
                var addOptions = addEstOptions.Select(x => new Estoption()
                {
                    EstheaderId = x.EstheaderId,
                    OptionDesc = x.OptionDesc,
                    OptionNumber = x.OptionNumber,
                    OptionQty = 1,
                    CreatedBy = User.Identity.Name.Split('@')[0],
                }).ToList();
                await _context.Estoptions.BulkInsertAsync(addOptions);

                var addOptionsDto = _mapper.Map<List<EstoptionDto>>(addOptions);
                foreach(var option in addOptionsDto)
                {
                    option.AsmHeaderId = addEstOptions.Where(x => x.OptionDesc == option.OptionDesc && x.OptionNumber == option.OptionNumber).FirstOrDefault().AsmHeaderId;
                }
                return Ok(new ResponseModel<List<EstoptionDto>>() { Value = addOptionsDto, IsSuccess = true, Message = "Added option" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstoptionDto>>() { Value = null, IsSuccess = false, Message = "Failed to add option" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddEstdetailAsync([FromBody] EstdetailDto addEstDetail)
        {
            try
            {
                var findItem = await _context.MasterItems.Include("MasterItemPhase").SingleOrDefaultAsync(x => x.MasterItemId == addEstDetail.MasterItemsId);
                var findActivity = await _context.Pactivities.Include("Jccategory").Include("Jccostcode").SingleOrDefaultAsync(x => x.BomClassId == findItem.BomClassId && x.DivId == 1);
                //get the job number, subdivision, vendor, costs
                var findHeader = await _context.Estheaders.SingleOrDefaultAsync(x => x.EstheaderId == addEstDetail.Estoption.EstheaderId);
                var findSubdivisionId = _context.Jobs.SingleOrDefault(x => x.JobNumber == findHeader.JobNumber)?.SubdivisionId;
                var findVendor = _context.PactivityAreaSuppliers.SingleOrDefault(x => x.PactivityId == findActivity.PactivityId && x.SubdivisionId == findSubdivisionId);
                if (findVendor == null)
                {
                    //TODO: should they be allowed add it anyway?
                    return Ok(new ResponseModel<EstdetailDto>() { IsSuccess = false, Message = $"Missing Vendor for activity {findActivity.Activity}", Value = null });
                }

                var findCost = _context.Costs.Where(x => x.MasterItemId == findItem.MasterItemId && x.SubNumber == findVendor.SubNumber && x.SubdivisionId == findSubdivisionId).FirstOrDefault() ?? _context.Costs.Where(x => x.MasterItemId == findItem.MasterItemId && x.SubNumber == findVendor.SubNumber && x.SubdivisionId == 1).FirstOrDefault();

                //TODO: below findEstActivity should it also check by option??
                var findEstActivity = _context.Estactivities.Where(x => x.BomClass == findActivity.Activity && x.JobNumber == findHeader.JobNumber).FirstOrDefault();
                if (findEstActivity == null)
                {
                    //add the activity if it wasn't already in the estactivities for this job
                    findEstActivity = new Estactivity()
                    {
                        BomClass = findActivity.Activity,
                        JobNumber = findHeader.JobNumber,
                        TradeId = findActivity.TradeId,
                        Releasecode = findActivity.Releasecode,
                        SactivityId = findActivity.SactivityId,
                        SubNumber = findVendor.SubNumber,
                        SelectedVendor = findVendor.SubNumber,
                        DefaultVendor = findVendor.SubNumber,                        
                        CreatedBy = User.Identity.Name.Split('@')[0]
                    };
                    _context.Estactivities.Add(findEstActivity);
                    await _context.SaveChangesAsync();

                    //add schedule links if schedule activity exist
                    var findScheduleActivities = _context.ScheduleSactivities.Include(x => x.ScheduleM.Schedule).Include(x => x.ScheduleSactivityLinks).Where(x => x.ScheduleM.Schedule.JobNumber == findHeader.JobNumber && x.SactivityId == findActivity.SactivityId).ToList();
                    if (findScheduleActivities.Any() && findActivity.SactivityId != null) 
                    {
                        var newlinks = findScheduleActivities.Select(x => new ScheduleSactivityLink()
                        {
                            EstactivityId = findEstActivity.EstactivityId,
                            ScheduleAid = findScheduleActivities.FirstOrDefault(y => y.SactivityId == findActivity.SactivityId)?.ScheduleAid ?? 0,
                        }).ToList();
                        newlinks.RemoveAll(x => x.ScheduleAid == 0);
                        _context.ScheduleSactivityLinks.AddRange(newlinks);
                        await _context.SaveChangesAsync();
                    }                    
                }
                var newItem = new Estdetail()
                {
                    EstoptionId = addEstDetail.EstoptionId,
                    EstactivityId = findEstActivity.EstactivityId,
                    MasterItemsId = findItem.MasterItemId,
                    PhaseCode = findItem.MasterItemPhase.PhaseCode,
                    ItemNumber = findItem.ItemNumber,
                    CostsId = findCost?.CostsId,
                    //Category = "CO",//it seems to need to change the cost category?? this seems o be the only option? - "S" means sales? ? "CO" means change order???
                    //Instance = findItem.Instance,//???
                    //SeqNumber = findItem.SeqNumber,//???
                    //Location = findItem.Location,
                    //SortLocation = findItem.SortLocation,
                    //SortWbs = findItem.SortWbs,
                    ItemDesc = findItem.ItemDesc,
                    ItemNotes = findItem.ItemNotes,
                    // TakeoffQuantity = findItem.TakeoffQuantity,//?? get new from master item??? //ths would be asmdetail maybe factor?? but that only works if it's item in option, 
                    TakeoffUnit = findItem.TakeoffUnit,
                    VendorNumber = findVendor.SubNumber,//?? get new
                    CnvFctr = findItem.CnvFctr,
                    Multdiv = findItem.Multdiv,
                    OrdrUnit = findItem.OrderUnit,
                    OrderQty = 1, //TODO: needs to be picked when adding, let's default to 1
                    Price = findCost?.UnitCost,
                    OrgPrice = findCost?.UnitCost,
                    Amount = 1 * (findCost?.UnitCost ?? 0),//TODO: allow pick qty
                    JcCategory = findActivity.Jccategory?.Category,
                    VarianceJcCategory = addEstDetail.VarianceJcCategory,
                    JcPhase = findActivity.Jccostcode?.CostCode,
                    CreatedBy = User.Identity.Name.Split('@')[0],
                    Errors = ErrorString(findCost == null ? null : "not null", findActivity.Jccostcode?.CostCode, findActivity.Jccategory?.Category)
                };
                
                _context.Estdetails.Add(newItem);
                await _context.SaveChangesAsync();
                var getEstDetailWithOptandHeader = await _context.Estdetails.Include(x => x.Estoption.Estheader.ReferenceTypeNavigation).Include(x => x.Estactivity).SingleOrDefaultAsync(x => x.EstdetailId == newItem.EstdetailId);
                var addDetailDto = _mapper.Map<EstdetailDto>(getEstDetailWithOptandHeader);
                addDetailDto.Podetail = new PodetailDto() { Poheader = new PoheaderDto() { PostatusNavigation = new PostatusDto()} };
                return Ok(new ResponseModel<EstdetailDto>() { Value = addDetailDto, IsSuccess = true, Message = "Added item" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstdetailDto>() { Value = null, IsSuccess = false, Message = "Failed to add option" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddEstdetailsAsync([FromBody] List<EstdetailDto> addEstDetails)
        {
            try
            {
                //this is for when user picks by trade/activity/item
                //all added items should go in same option
                //if estoptionid is null 0 or 0, add the base option, and pick that
                var findEstoptId = addEstDetails.First().EstoptionId;
                if(findEstoptId == null || findEstoptId == 0)
                {
                    var findBaseHouseOpt = _context.Estoptions.FirstOrDefault(x => x.EstheaderId == addEstDetails.First().EstitemheaderId && x.OptionNumber.StartsWith("Base"));
                    if(findBaseHouseOpt == null)
                    {
                        var addOption = new Estoption()
                        {
                            EstheaderId = addEstDetails.First().EstitemheaderId,
                            OptionNumber = "Base",//??
                            OptionDesc = "Base house",//??                    
                            OptionQty = 1,//default to this for now, but they might need to pick it
                        };
                        _context.Estoptions.Add(addOption);
                        await _context.SaveChangesAsync();
                        findEstoptId = addOption.EstoptionId;
                    }
                    else
                    {
                        findEstoptId = findBaseHouseOpt.EstoptionId;
                    }
                }

                var findHeader = _context.Estoptions.Include(x => x.Estheader).Where(x => x.EstoptionId == findEstoptId).FirstOrDefault().Estheader;
                var findEstDetailsActivitiesThisHeader = _context.Estdetails.Where(x => x.Estoption.EstheaderId == findHeader.EstheaderId).Select(x => x.EstactivityId).ToList();
                var findActivitiesThisHeader = _context.Estactivities.Where(x => findEstDetailsActivitiesThisHeader.Contains(x.EstactivityId) && x.IsActive == true);//should be empty if adding new header
                //var findActivitesThisHeader = _context.Estdetails.Where(x => x.Estoption != null && x.Estoption.EstheaderId == findHeader.EstheaderId && x.IsActive == true && x.Estactivity != null && x.Estactivity.IsActive == true).Include(x => x.Estactivity).Select(x => x.Estactivity).ToList();//should be empty if adding new header
                var findHeaderId = findHeader.EstheaderId;
                var jobNum = findHeader.JobNumber;
                var findSubdivisionId = _context.Jobs.SingleOrDefault(x => x.JobNumber == jobNum)?.SubdivisionId;
                var addedDetails = new List<EstdetailDto>();
                var userName = User.Identity.Name.Split('@')[0];   

                //var findActivities = (from b in _context.MasterItems.Where(x => x.IsActive == true && addEstDetails.Select(y => y.MasterItemsId).Contains(x.MasterItemId))
                //                             join c in _context.Pactivities.Where(x => x.DivId == 1 && x.IsActive == true) on b.BomClassId equals c.BomClassId
                //                             from f in _context.PactivityAreaSuppliers.Where(x => x.IsActive == true && x.SubdivisionId == findSubdivisionId && x.PactivityId == c.PactivityId).DefaultIfEmpty()
                //                             from h in findActivitesThisHeader.Where(x => x.BomClass == c.Activity).DefaultIfEmpty()//12/9 Chi says there could be multiple suppliers on same activity in est, hence need multiple on the job but one per estimate, I guess
                //                      //from h in _context.Estactivities.Where(x => x.BomClass == c.Activity && x.JobNumber == jobNum && x.IsActive == true).Take(1).DefaultIfEmpty()//TODO: should there be only one-- this is finding multiple, probably due to previous bad data, adding Take(1) to fix for now
                //                      select new Estactivity
                //                             {
                //                                 EstactivityId = h != null && h.EstactivityId != 0 ? h.EstactivityId : 0,
                //                                 BomClass = c.Activity,
                //                                 JobNumber = jobNum,
                //                                 TradeId = c.TradeId,
                //                                 Releasecode = c.Releasecode,
                //                                 SactivityId = c.SactivityId,
                //                                 SubNumber = (f != null && f.PactivityId != 0) ? f.SubNumber : null,//O?
                //                                 SelectedVendor = (f != null && f.PactivityId != 0) ? f.SubNumber : null,//TODO: no supplier error
                //                                 DefaultVendor = (f != null && f.PactivityId != 0) ? f.SubNumber : null,
                //                                 CreatedBy = userName
                //                             }).ToList();
                //var newActivities = findActivities.Where(x => x.EstactivityId == 0).GroupBy(x => x.BomClass).ToList();
                //var insertActvites = newActivities.Select(x => new Estactivity()
                //{
                //    BomClass = x.Key,
                //    JobNumber = x.First().JobNumber,
                //    TradeId = x.First().TradeId,
                //    Releasecode = x.First().Releasecode,
                //    SactivityId = x.First().SactivityId,
                //    SubNumber = x.First().SubNumber,
                //    SelectedVendor = x.First().SelectedVendor,
                //    DefaultVendor = x.First().DefaultVendor,
                //    CreatedBy = userName,                    
                //}).ToList();
                //await _context.Estactivities.BulkInsertAsync(insertActvites);

                //Now all the activities should be found below, but this is inefficient
                var findItems = (from b in _context.MasterItems.Where(x => x.IsActive == true && addEstDetails.Select(y => y.MasterItemsId).Contains(x.MasterItemId))
                                join e in _context.MasterItemPhases.Where(x => x.IsActive == true) on b.MasterItemPhaseId equals e.MasterItemPhaseId
                                 join c in _context.Pactivities.Where(x => x.DivId == 1 && x.IsActive == true) on b.BomClassId equals c.BomClassId
                                 join d in _context.Jccostcodes.Where(x => x.IsActive == true) on c.JccostcodeId equals d.JccostcodeId
                                 from j in _context.Jccategories.Where(x => x.IsActive == true && x.JccategoryId == c.JccategoryId).DefaultIfEmpty()
                                 from f in _context.PactivityAreaSuppliers.Where(x => x.IsActive == true && x.SubdivisionId == findSubdivisionId && x.PactivityId == c.PactivityId).DefaultIfEmpty()
                                 from g in _context.Costs.Where(x => x.SubNumber == (f != null ? f.SubNumber : 0) && x.MasterItemId == b.MasterItemId && x.SubdivisionId == findSubdivisionId && x.IsActive == true).DefaultIfEmpty()
                                 from i in _context.Costs.Where(x => x.SubNumber == (f != null ? f.SubNumber : 0) && x.MasterItemId == b.MasterItemId && x.SubdivisionId == 1 && x.IsActive == true).DefaultIfEmpty()//To get division default if no cost for the subdivision

                                 from h in findActivitiesThisHeader.Where(x => x.BomClass == c.Activity).Take(1).DefaultIfEmpty()//12/9 Chi says there could be multiple suppliers on same activity in est, hence need multiple on the job but one per estimate, I guess

                                  //  from h in _context.Estactivities.Where(x => x.BomClass == c.Activity && x.JobNumber == jobNum && x.IsActive == true).Take(1).DefaultIfEmpty()//TODO: should there be only one-- this is finding multiple, probably due to previous bad data, adding Take(1) to fix for now
                                 select new Estdetail
                                {
                                     EstoptionId = findEstoptId,
                                     EstactivityId = h != null && h.EstactivityId != 0 ? h.EstactivityId : null,
                                    MasterItemsId = b.MasterItemId,
                                    CostsId = g != null && g.CostsId != 0 ? g.CostsId : i != null && i.CostsId != 0 ? i.CostsId : null,
                                     PhaseCode = e.PhaseCode,
                                     ItemNumber = b.ItemNumber,
                                     Estactivity = (h != null && h.EstactivityId != 0) ? null : new Estactivity()
                                     {
                                         BomClass = c.Activity,
                                         JobNumber = jobNum,
                                         TradeId = c.TradeId,
                                         Releasecode = c.Releasecode,
                                         SactivityId = c.SactivityId,
                                         SubNumber = (f != null && f.PactivityId != 0) ? f.SubNumber : null,//O?
                                         SelectedVendor = (f != null && f.PactivityId != 0) ? f.SubNumber : null,//TODO: no supplier error
                                         DefaultVendor = (f != null && f.PactivityId != 0) ? f.SubNumber : null,
                                         CreatedBy = userName
                                     },
                                     ItemDesc = b.ItemDesc,
                                     ItemNotes = b.ItemNotes,
                                     TakeoffUnit = b.TakeoffUnit,
                                     VendorNumber = (f != null && f.PactivityId != 0) ? f.SubNumber : null,//?? get new, //TODO: no supplier error
                                     CnvFctr = b.CnvFctr,
                                     Multdiv = b.Multdiv,
                                     OrdrUnit = b.OrderUnit,
                                     OrderQty = 1, //TODO: needs to be picked when adding, let's default to 1
                                     Price = (g != null && g.CostsId != 0) ? g.UnitCost : (i != null && i.CostsId != 0) ? i.UnitCost : null,//TODO: null or 0?
                                     OrgPrice = (g != null && g.CostsId != 0) ? g.UnitCost : (i != null && i.CostsId != 0) ? i.UnitCost : null,//TODO: null or 0?
                                     Amount = 1 * ((g != null && g.CostsId != 0) ? g.UnitCost ?? 0 : (i != null && i.CostsId != 0) ? i.UnitCost ?? 0 : 0),
                                     JcPhase = d.CostCode,
                                     JcCategory = j.Category,
                                     CreatedBy = userName,
                                     Errors = ErrorString((g == null && i == null ? null : "not null"), d.CostCode, j.Category),
                                     VarianceJcCategory = addEstDetails.First().VarianceJcCategory,//TODO: is this the one the user picked?                  
                                 }).ToList();
                //activity existed in the estimte, just add items
                var insertNewItems = findItems.Where(x => x.Estactivity == null).ToList();
                await _context.Estdetails.BulkInsertAsync(insertNewItems);

                //need to add activity and items
                var findNewDetailsGrouped = findItems.Where(x => x.Estactivity != null).GroupBy(x => x.Estactivity.BomClass).ToList();
                var addActivities = new List<Estactivity>();
                foreach(var activity in findNewDetailsGrouped)
                {
                    var newActivity = activity.First().Estactivity;
                    newActivity.Estdetails = activity.Select(x => x).ToList();
                    addActivities.Add(newActivity);
                }
                await _context.Estactivities.BulkInsertAsync(addActivities, options => options.IncludeGraph = true);//insert new activities with items


                //await _context.Estdetails.BulkInsertAsync(itemsToInsert, options => options.IncludeGraph = true);//include graph should not be needed now since not inserting activities here
                // await _context.Estdetails.BulkInsertAsync(findItems);
                //add schedule links
                var newEstactivityIds = addActivities.Where(x => x.SactivityId != null).Select(x => x.EstactivityId).ToList();
                var newActivitiesSactivityIds = addActivities.Where(x => x.SactivityId != null).Select(x => x.SactivityId).ToList();
                var findScheduleActivities = _context.ScheduleSactivities.Include(x => x.ScheduleM.Schedule).Include(x => x.ScheduleSactivityLinks).Where(x => x.ScheduleM.Schedule.JobNumber == jobNum && newActivitiesSactivityIds.Contains(x.SactivityId)).ToList();
                var alreadyThereEstActivities = findScheduleActivities.SelectMany(x => x.ScheduleSactivityLinks).Where(x => newEstactivityIds.Contains(x.EstactivityId)).Select(x => x.EstactivityId).ToList();
                var newlinks = addActivities.Where(x => !alreadyThereEstActivities.Contains(x.EstactivityId) && x.SactivityId != null).Select(x => new ScheduleSactivityLink()
                {
                    EstactivityId = x.EstactivityId,
                    ScheduleAid = findScheduleActivities.FirstOrDefault(y => y.SactivityId == x.SactivityId)?.ScheduleAid ?? 0,
                }).ToList();
                newlinks.RemoveAll(x => x.ScheduleAid == 0);
                await _context.ScheduleSactivityLinks.BulkInsertAsync(newlinks);


                var getReturnDetails = await _context.Estdetails.Include(x => x.Estoption.Estheader.ReferenceTypeNavigation).Include(x => x.Estactivity).Where(x => findItems.Select(y => y.EstdetailId).Contains(x.EstdetailId)).ToListAsync();
                var getReturnDetailsDto = _mapper.Map<List<EstdetailDto>>(getReturnDetails);
                foreach (var detail in getReturnDetailsDto)
                {
                    detail.Podetail = new PodetailDto() { Poheader = new PoheaderDto() { PostatusNavigation = new PostatusDto() } };
                    detail.DisplayErrors = !string.IsNullOrWhiteSpace(detail.Errors) ? string.Join(", ", detail.Errors.Split('|').Select(x => EnumExtension.GetDescriptionFromEnum((BudgetErrors)int.Parse(x))).ToList()): null;
                }
                return Ok(new ResponseModel<List<EstdetailDto>>() { Value = getReturnDetailsDto, IsSuccess = true, Message = "Added items" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstdetailDto>>() { Value = null, IsSuccess = false, Message = "Failed to add items" });
            }
        }
        [HttpPost]
        public async Task<IActionResult> AddEstoptionsAndDetailsAsync([FromBody] List<EstdetailDto> addEstDetails)
        {
            try
            {
                // first insert the ones with existing option, then group the rest 
                //Estoption.AsmheaderId will exist, and will be the asmheader to add or add to
                //masteritemId will be the master item to add 
                //this is for when they use option/item takeoff. if the option is already in the estimate, add the items to it, if not, add the option
                var userName = User.Identity.Name.Split('@')[0];
                var varianceJcCategorySelected = addEstDetails.First().VarianceJcCategory;//the one they selected. not sure if this is used
                var findHeaderId = addEstDetails.First().EstitemheaderId;
                var findHeader = _context.Estheaders.FirstOrDefault(x => x.EstheaderId == findHeaderId);
                var jobNum = findHeader.JobNumber;
                var findSubdivisionId = _context.Jobs.SingleOrDefault(x => x.JobNumber == jobNum)?.SubdivisionId;


                //add new activites needed
                var findEstDetailsActivitiesThisHeader = _context.Estdetails.Where(x => x.Estoption.EstheaderId == findHeader.EstheaderId).Select(x => x.EstactivityId).ToList();
                var findActivitiesThisHeader = _context.Estactivities.Where(x => findEstDetailsActivitiesThisHeader.Contains(x.EstactivityId) && x.IsActive == true);//should be empty if adding new header



                //find and insert any needed activities first
                //var findActivities = (from b in _context.MasterItems.Where(x => x.IsActive == true && addEstDetails.Select(y => y.MasterItemsId).Contains(x.MasterItemId))
                //                      join c in _context.Pactivities.Where(x => x.DivId == 1 && x.IsActive == true) on b.BomClassId equals c.BomClassId
                //                      from f in _context.PactivityAreaSuppliers.Where(x => x.IsActive == true && x.SubdivisionId == findSubdivisionId && x.PactivityId == c.PactivityId).DefaultIfEmpty()
                //                      from h in _context.Estactivities.Where(x => x.BomClass == c.Activity && x.JobNumber == jobNum && x.IsActive == true).Take(1).DefaultIfEmpty()//TODO: should there be only one-- this is finding multiple, probably due to previous bad data, adding Take(1) to fix for now
                //                      select new Estactivity
                //                      {
                //                          EstactivityId = h != null && h.EstactivityId != 0 ? h.EstactivityId : 0,
                //                          BomClass = c.Activity,
                //                          JobNumber = jobNum,
                //                          TradeId = c.TradeId,
                //                          Releasecode = c.Releasecode,
                //                          SactivityId = c.SactivityId,
                //                          SubNumber = (f != null && f.PactivityId != 0) ? f.SubNumber : null,//O?
                //                          SelectedVendor = (f != null && f.PactivityId != 0) ? f.SubNumber : null,//TODO: no supplier error
                //                          DefaultVendor = (f != null && f.PactivityId != 0) ? f.SubNumber : null,
                //                          CreatedBy = userName
                //                      }).ToList();
                //var newActivities = findActivities.Where(x => x.EstactivityId == 0).GroupBy(x => x.BomClass).ToList();
                //var insertActvites = newActivities.Select(x => new Estactivity()
                //{
                //    BomClass = x.Key,
                //    JobNumber = x.First().JobNumber,
                //    TradeId = x.First().TradeId,
                //    Releasecode = x.First().Releasecode,
                //    SactivityId = x.First().SactivityId,
                //    SubNumber = x.First().SubNumber,
                //    SelectedVendor = x.First().SelectedVendor,
                //    DefaultVendor = x.First().DefaultVendor,
                //    CreatedBy = userName
                //}).ToList();
                //await _context.Estactivities.BulkInsertAsync(insertActvites);

                var findItems = (from b in _context.MasterItems.Where(x => x.IsActive == true && addEstDetails.Select(y => y.MasterItemsId).Contains(x.MasterItemId))
                                 join l in _context.AsmDetails.Where(x => x.IsActive == true) on b.MasterItemId equals l.MasterItemId
                                 join m in _context.AsmHeaders.Where(x => x.IsActive == true && addEstDetails.Select(y => y.Estoption.AsmHeaderId).Contains(x.AsmHeaderId)) on l.AsmHeaderId equals m.AsmHeaderId
                                 join e in _context.MasterItemPhases.Where(x => x.IsActive == true) on b.MasterItemPhaseId equals e.MasterItemPhaseId
                                 join c in _context.Pactivities.Where(x => x.DivId == 1 && x.IsActive == true) on b.BomClassId equals c.BomClassId
                                 join d in _context.Jccostcodes.Where(x => x.IsActive == true) on c.JccostcodeId equals d.JccostcodeId
                                 join p in _context.Jccategories.Where(x => x.IsActive == true) on c.JccategoryId equals p.JccategoryId
                                 from f in _context.PactivityAreaSuppliers.Where(x => x.IsActive == true && x.SubdivisionId == findSubdivisionId && x.PactivityId == c.PactivityId).DefaultIfEmpty()
                                 from g in _context.Costs.Where(x => x.SubNumber == (f != null ? f.SubNumber : 0) && x.MasterItemId == b.MasterItemId && x.SubdivisionId == findSubdivisionId && x.IsActive == true).DefaultIfEmpty()
                                 from i in _context.Costs.Where(x => x.SubNumber == (f != null ? f.SubNumber : 0) && x.MasterItemId == b.MasterItemId && x.SubdivisionId == 1 && x.IsActive == true).DefaultIfEmpty()//To get division default if no cost for the subdivision
                                 from h in findActivitiesThisHeader.Where(x => x.BomClass == c.Activity).Take(1).DefaultIfEmpty()//12/9 Chi says there could be multiple suppliers on same activity in est, hence need multiple on the job but one per estimate, I guess
                                 //from h in _context.Estactivities.Where(x => x.BomClass == c.Activity && x.JobNumber == jobNum && x.IsActive == true).Take(1).DefaultIfEmpty()//TODO: should there be only one-- this is finding multiple, probably due to previous bad data, adding Take(1) to fix for now
                                 from k in _context.Estoptions.Where(x => x.OptionNumber == m.AssemblyCode && x.EstheaderId == findHeaderId && x.IsActive == true).Take(1).DefaultIfEmpty()
                                 select new Estdetail
                                 {
                                     
                                     MasterItemsId = b.MasterItemId,
                                     PhaseCode = e.PhaseCode,
                                     ItemNumber = b.ItemNumber,
                                     EstactivityId = h != null && h.EstactivityId != 0 ? h.EstactivityId : null,
                                     Estactivity = (h != null && h.EstactivityId != 0) ? null : new Estactivity()
                                     {
                                         BomClass = c.Activity,
                                         JobNumber = jobNum,
                                         TradeId = c.TradeId,
                                         Releasecode = c.Releasecode,
                                         SactivityId = c.SactivityId,
                                         SubNumber = (f != null && f.PactivityId != 0) ? f.SubNumber : null,//O?
                                         SelectedVendor = (f != null && f.PactivityId != 0) ? f.SubNumber : null,//TODO: no supplier error
                                         DefaultVendor = (f != null && f.PactivityId != 0) ? f.SubNumber : null,
                                         CreatedBy = userName
                                     },
                                     Estoption = (k != null && k.EstoptionId != 0) ? null : new Estoption()
                                     {
                                         EstheaderId = findHeaderId,
                                         OptionDesc = m.AssemblyDesc,
                                         OptionNumber = m.AssemblyCode,
                                         OptionQty = 1,//TODO: allow pick
                                         CreatedBy = userName,
                                         //TODO: other fields needed?
                                     },
                                     EstoptionId = k != null && k.EstoptionId != 0 ? k.EstoptionId : null,
                                     ItemDesc = b.ItemDesc,
                                     ItemNotes = b.ItemNotes,
                                     TakeoffUnit = b.TakeoffUnit,
                                     VendorNumber = (f != null && f.PactivityId != 0) ? f.SubNumber : null,//?? get new, //TODO: no supplier error
                                     CnvFctr = b.CnvFctr,
                                     Multdiv = b.Multdiv,
                                     OrdrUnit = b.OrderUnit,
                                     OrderQty = l.Factor, //from asm detail
                                    // OrderQty = 1, //TODO: needs to be picked when adding, let's default to 1, or should it be however many are in the asmdetail?
                                     Price = (g != null && g.CostsId != 0) ? g.UnitCost : (i != null && i.CostsId != 0) ? i.UnitCost : null,//TODO: null or 0?
                                     OrgPrice = (g != null && g.CostsId != 0) ? g.UnitCost : (i != null && i.CostsId != 0) ? i.UnitCost : null,//TODO: null or 0?
                                    // Amount = 1 * ((g != null && g.CostsId != 0) ? g.UnitCost ?? 0 : (i != null && i.CostsId != 0) ? i.UnitCost ?? 0 : 0),
                                     Amount = l.Factor * ((g != null && g.CostsId != 0) ? g.UnitCost ?? 0 : (i != null && i.CostsId != 0) ? i.UnitCost ?? 0 : 0),
                                     JcPhase = d.CostCode,
                                     JcCategory = p.Category,
                                     VarianceJcCategory = varianceJcCategorySelected,//TODO: is this the one the user picked?
                                     CreatedBy = userName,
                                     Errors = ((g == null && i == null)) ? "4" : null//TODO: also need no supplier error 
                                 }).ToList();


                var optionExists = findItems.Where(x => x.EstoptionId != null);
                await _context.Estdetails.BulkInsertAsync(optionExists, options => options.IncludeGraph = true);

                var optionNotExists = findItems.Where(x => x.EstoptionId == null);
                var optionNotExistsGrouped = optionNotExists.GroupBy(x => x.Estoption.OptionNumber).ToList();
                var insertNotExistGrouped = optionNotExistsGrouped.Select(x => new Estoption()
                {
                    OptionNumber = x.Key,
                    OptionDesc = x.First().Estoption.OptionDesc,
                    EstheaderId = findHeaderId,
                    OptionQty = 1,//TODO: allow pick
                    CreatedBy = userName,
                    Estdetails = x.Select(y => new Estdetail()
                    {
                        MasterItemsId = y.MasterItemsId,
                        PhaseCode = y.PhaseCode,
                        ItemNumber = y.ItemNumber,
                        EstactivityId = y.EstactivityId,
                        //Estactivity = y.Estactivity,                        
                        ItemDesc = y.ItemDesc,
                        ItemNotes = y.ItemNotes,
                        TakeoffUnit = y.TakeoffUnit,
                        VendorNumber = y.VendorNumber,
                        CnvFctr = y.CnvFctr,
                        Multdiv = y.Multdiv,
                        OrdrUnit = y.OrdrUnit,
                        OrderQty = y.OrderQty,
                        Price = y.Price,
                        OrgPrice = y.OrgPrice,
                        Amount = y.Amount,
                        JcPhase = y.JcPhase,
                        CreatedBy = userName,
                        Errors = y.Errors,
                        JcCategory = y.JcCategory,//TODO: should it be findactivity.jccategory.category
                        VarianceJcCategory = y.VarianceJcCategory
                    }).ToList(),
                }).ToList();
                await _context.Estoptions.BulkInsertAsync(insertNotExistGrouped, options => options.IncludeGraph = true);



                //add activities 
                var itemsNeedActivity = findItems.Where(x => x.Estactivity != null).ToList();
                var itemsNeedActivityIds = findItems.Where(x => x.Estactivity != null).Select(x => x.MasterItemsId).ToList();
                var findBomClasses = _context.MasterItems.Where(x => itemsNeedActivityIds.Contains(x.MasterItemId)).Select(x => x.BomClass).Distinct().ToList();
                var insertActiviies = itemsNeedActivity.GroupBy(x => x.Estactivity.BomClass).Select(x => new Estactivity()
                {
                    BomClass = x.Key,
                    JobNumber = jobNum,
                    TradeId = x.First().Estactivity.TradeId,
                    Releasecode = x.First().Estactivity.Releasecode,
                    SactivityId = x.First().Estactivity.SactivityId,
                    SubNumber = x.First().Estactivity.SubNumber,
                    SelectedVendor = x.First().Estactivity.SelectedVendor,
                    DefaultVendor = x.First().Estactivity.DefaultVendor,
                    CreatedBy = userName
                });
                //await _context.Estactivities.BulkInsertAsync(insertActiviies, options => options.IncludeGraph = false);
                //TODO: above doesn't track the ids, so not working below
                var newActivities = new List<Estactivity>();
                foreach(var activity in insertActiviies)
                {
                    _context.Estactivities.Add(activity);
                    _context.SaveChanges();
                    newActivities.Add(activity);
                    
                }//TODO: slow?
                 //_context.Estactivities.AddRange(insertActiviies);
                 //await _context.SaveChangesAsync();

                //add schedule links
                var newEstactivityIds = newActivities.Where(x => x.SactivityId != null).Select(x => x.EstactivityId).ToList();
                var newActivitiesSactivityIds = newActivities.Where(x => x.SactivityId != null).Select(x => x.SactivityId).ToList();
                var findScheduleActivities = _context.ScheduleSactivities.Include(x => x.ScheduleM.Schedule).Include(x => x.ScheduleSactivityLinks).Where(x => x.ScheduleM.Schedule.JobNumber == jobNum && newActivitiesSactivityIds.Contains(x.SactivityId)).ToList();
                var alreadyThereEstActivities = findScheduleActivities.SelectMany(x => x.ScheduleSactivityLinks).Where(x => newEstactivityIds.Contains(x.EstactivityId)).Select(x => x.EstactivityId).ToList();
                var newlinks = newActivities.Where(x => !alreadyThereEstActivities.Contains(x.EstactivityId) && x.SactivityId != null).Select(x => new ScheduleSactivityLink()
                {
                    EstactivityId = x.EstactivityId,
                    ScheduleAid = findScheduleActivities.FirstOrDefault(y => y.SactivityId == x.SactivityId)?.ScheduleAid ?? 0,
                }).ToList();
                newlinks.RemoveAll(x => x.ScheduleAid == 0);
                await _context.ScheduleSactivityLinks.BulkInsertAsync(newlinks);


                //link estdetails with activites if didn't have one
                var findToUpdate = new List<Estdetail>();
                var findOptExist = await _context.Estdetails.Include(x => x.MasterItems.BomClass).Include(x => x.Estactivity).Where(x => optionExists.Select(y => y.EstdetailId).Contains(x.EstdetailId)).ToListAsync();
                findToUpdate.AddRange(findOptExist);
                var newDetails = insertNotExistGrouped.SelectMany(y => y.Estdetails).ToList();
                var newDetailIds = newDetails.Select(z => z.EstdetailId).ToList();
                var findNotOptExist = await _context.Estdetails.Include(x => x.MasterItems.BomClass).Include(x => x.Estactivity).Where(x => newDetailIds.Contains(x.EstdetailId)).ToListAsync();
                findToUpdate.AddRange(findNotOptExist);

                foreach (var estdetail in findToUpdate.Where(x => x.EstactivityId == null || x.EstactivityId == 0))
                {
                    estdetail.EstactivityId = newActivities.Where(x => x.BomClass == estdetail.MasterItems.BomClass.BomClass1).FirstOrDefault()?.EstactivityId;
                }
                await _context.Estdetails.BulkUpdateAsync(findToUpdate, options => options.ColumnInputExpression = x => new { x.EstactivityId });

                var returnDetails = new List<EstdetailDto>();
                var getReturnDetailsOptExist = await _context.Estdetails.Include(x => x.Estoption.Estheader.ReferenceTypeNavigation).Include(x => x.Estactivity).Where(x => optionExists.Select(y => y.EstdetailId).Contains(x.EstdetailId)).ToListAsync();
      
                var getReturnDetailsOptNotExist = await _context.Estdetails.Include(x => x.Estoption.Estheader.ReferenceTypeNavigation).Include(x => x.Estactivity).Where(x => newDetailIds.Contains(x.EstdetailId)).ToListAsync();
                //TODO: why didn't the estactivity get includeed above? the estactivity id was not null
                var getRetrudetailsOptNotExistActivityIds = getReturnDetailsOptNotExist.Select(x => x.EstactivityId).ToList();
                var findActivities = _context.Estactivities.Where(x => getRetrudetailsOptNotExistActivityIds.Contains(x.EstactivityId)).ToList();
                foreach(var detail in getReturnDetailsOptNotExist)
                {
                    detail.Estactivity = findActivities.Where(x => x.EstactivityId ==  detail.EstactivityId).FirstOrDefault();
                }

                var getReturnDetailsOptExistedDto = _mapper.Map<List<EstdetailDto>>(getReturnDetailsOptExist);
                              
                var getReturnDetailsNotExistedDto = _mapper.Map<List<EstdetailDto>>(getReturnDetailsOptNotExist);
               
                returnDetails.AddRange(getReturnDetailsOptExistedDto);
                returnDetails.AddRange(getReturnDetailsNotExistedDto);
                foreach (var detail in returnDetails)
                {
                    detail.Podetail = new PodetailDto() { Poheader = new PoheaderDto() { PostatusNavigation = new PostatusDto() } };
                    detail.DisplayErrors = detail.Errors != null ? string.Join(", ", detail.Errors.Split('|').Select(x => EnumExtension.GetDescriptionFromEnum((BudgetErrors)int.Parse(x))).ToList()) : null;
                }
                return Ok(new ResponseModel<List<EstdetailDto>>() { Value = returnDetails, IsSuccess = true, Message = "Added items" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstdetailDto>>() { Value = null, IsSuccess = false, Message = "Failed to add items" });
            }
        }
        [HttpPost]
        public async Task<IActionResult> AddEstimateAsync([FromBody] EstheaderDto addCustomEstimate)
        {
            try
            {
                var getMaxEstNumber = _context.Estheaders.Where(x => x.JobNumber == addCustomEstimate.JobNumber).Max(x => x.EstimateNumber);
                var newEstNumber = getMaxEstNumber != null ? getMaxEstNumber + 1 : 1;
                var addHeader = new Estheader()
                {
                    JobNumber = addCustomEstimate.JobNumber,
                    ReferenceNumber = addCustomEstimate.ReferenceNumber,
                    EstimateNumber = newEstNumber,
                    ReferenceType = addCustomEstimate.ReferenceType,
                    ReferenceDesc = addCustomEstimate.ReferenceDesc,
                    EstimateDescPe = addCustomEstimate.EstimateDescPe,
                    Estimator = User.Identity.Name.Split('@')[0],
                    EstimateSource = 3,//TODO: this will come from either radio box custom estimate, item takeoof, or option takeoof, I think it should pick "Issue variance of option 
                    CreatedBy = User.Identity.Name.Split('@')[0]
                };
                _context.Estheaders.Add(addHeader);
                await _context.SaveChangesAsync();
                //TODO: don't add this base house if they are picking an option in takeoff by option
                var addOption = new Estoption()
                {
                    OptionNumber = "Base",//??
                    OptionDesc = "Base house",//??                    
                    EstheaderId = addHeader.EstheaderId,
                    OptionQty = 1,//default to this for now, but they might need to pick it
                    //SubdivisionId = addCustomEstimate.SubdivisionId,
                    //EstimatorEmail = addCustomEstimate.EsimatorEmail
                };
                _context.Estoptions.Add(addOption);
                await _context.SaveChangesAsync();
                var addOptionDto = _mapper.Map<EstoptionDto>(addOption);
                var getOption = _context.Estoptions.Include("Estheader.ReferenceTypeNavigation").SingleOrDefault(x => x.EstoptionId == addOption.EstoptionId);
                var getOptionDto = _mapper.Map<EstoptionDto>(getOption);
                //var addEstimateDto = _mapper.Map<EstheaderDto>(addHeader);
                return Ok(new ResponseModel<EstoptionDto> { IsSuccess = true, Value = getOptionDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstoptionDto> { IsSuccess = false, Message = "Failed to add estimate", Value = null });
            }

        }
        [HttpGet("{jobNumber}")]
        public async Task<IActionResult> GetBudgetHeadersByJobAsync(string jobNumber)
        {
            try
            {
                var getBudgets = await _context.Estheaders.AsNoTracking().Include("ReferenceTypeNavigation").Where(x => x.JobNumber == jobNumber && x.IsActive == true).OrderBy(x => x.EstimateNumber).ToListAsync();
                var budgetsDto = _mapper.Map<List<EstheaderDto>>(getBudgets);
                //below gets whether the items in the budget have been issued or not
                foreach (var budget in budgetsDto)
                {
                    var getDetails = (from b in _context.Estoptions.Where(x => x.IsActive == true && x.EstheaderId == budget.EstheaderId)
                                      join c in _context.Estdetails.Where(x => x.IsActive == true) on b.EstoptionId equals c.EstoptionId
                                      select new
                                      {
                                          IsIssued = c.EstjcedetailId != null
                                      }).ToList();
                    budget.IsIssued = !getDetails.Any(x => x.IsIssued == true) ? false : !getDetails.Any(x => x.IsIssued == false) ? true : null;
                }
                return Ok(new ResponseModel<List<EstheaderDto>> { IsSuccess = true, Value = budgetsDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstheaderDto>> { IsSuccess = false, Message = "failed to get budget headers by job", Value = null });
            }
        }
        [HttpGet("{estHeaderId}")]
        public async Task<IActionResult> GetOptionsForBudgetAsync(int estHeaderId)
        {
            try
            {
                var getOptions = await _context.Estoptions.AsNoTracking().Where(x => x.EstheaderId == estHeaderId && x.IsActive == true).ToListAsync();
                var optionsDto = _mapper.Map<List<EstoptionDto>>(getOptions);
                foreach (var option in optionsDto)
                {
                    var getDetails = (from c in _context.Estdetails.Where(x => x.IsActive == true && x.EstoptionId == option.EstoptionId)
                                      select new
                                      {
                                          IsIssued = c.EstjcedetailId != null
                                      }).ToList();
                    option.IsIssued = !getDetails.Any(x => x.IsIssued == true) ? false : !getDetails.Any(x => x.IsIssued == false) ? true : null;
                }
                return Ok(new ResponseModel<List<EstoptionDto>> { IsSuccess = true, Value = optionsDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstoptionDto>> { IsSuccess = false, Message = "failed to get options for budget", Value = null });
            }
        }
        [HttpGet("{estOptionId}")]
        public async Task<IActionResult> GetOptionCostTotalAsync(int estOptionId)
        {
            try
            {
                var getDetails = _context.Estdetails.AsNoTracking().Where(x => x.EstoptionId == estOptionId && x.IsActive == true);
                var TotalCost = getDetails.Sum(x => x.Amount);
                return Ok(new ResponseModel<double?> { IsSuccess = true, Value = TotalCost });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<double?> { IsSuccess = false, Message = "Failed to get option cost total" });
            }
        }
        
        [HttpGet("{estOptionId}")]
        public async Task<IActionResult> GetActivitiesForOptionAsync(int estOptionId)
        {
            try
            {
                var getActivities = await _context.Estdetails.AsNoTrackingWithIdentityResolution().Include("Estactivity").Where(x => x.EstoptionId == estOptionId && x.IsActive == true).Select(x => x.Estactivity).Distinct().ToListAsync();
                var activitiesDto = _mapper.Map<List<EstactivityDto>>(getActivities);
                foreach(var activity in activitiesDto)
                {
                    var getDetails = _context.Estdetails.Where(x => x.EstoptionId == estOptionId && x.EstactivityId == activity.EstactivityId);
                    activity.ActivityTotal = getDetails.Sum(x => x.Amount);
                    activity.IsIssued = getDetails.All(x => x.EstjcedetailId != null)  ? true : getDetails.Any(x => x.EstjcedetailId != null) ? null: false;
                }
                return Ok(new ResponseModel<List<EstactivityDto>> { IsSuccess = true, Value = activitiesDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstactivityDto>> { IsSuccess = false, Message = "failed to get activities for option", Value = null });
            }
        }
        [HttpGet("{estOptionId}/{activityId}")]
        public async Task<IActionResult> GetDetailsForActivitiesAsync(int estOptionId, int activityId)
        {
            try
            {
                var getDetails = await _context.Estdetails.AsNoTracking().Include("Podetail.Poheader").Where(x => x.EstoptionId == estOptionId && x.EstactivityId == activityId && x.IsActive == true).ToListAsync();
                var detailsDto = _mapper.Map<List<EstdetailDto>>(getDetails);
                foreach (var detail in detailsDto)
                {
                    if (!string.IsNullOrWhiteSpace(detail.Errors))
                    {
                        detail.DisplayErrors = string.Join(", ", detail.Errors.Split('|').Select(x => EnumExtension.GetDescriptionFromEnum((BudgetErrors)int.Parse(x))).ToList());
                    }
                    if (!string.IsNullOrWhiteSpace(detail.Warnings))
                    {
                        detail.DisplayWarnings = string.Join(", ", detail.Warnings.Split('|').Select(x => EnumExtension.GetDescriptionFromEnum((BudgetWarnings)int.Parse(x))).ToList());
                    }
                }
                return Ok(new ResponseModel<List<EstdetailDto>> { IsSuccess = true, Value = detailsDto });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstdetailDto>> { IsSuccess = false, Message = "Failed to get details for activities", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> IssueBudgetItemAsync([FromBody] EstdetailDto estdetail)
        {
            try
            {
                //Budget "issued" save estjcedetailid into the estdetail
                var updateDetails = await _context.Estdetails.Include("Estjcedetail").SingleOrDefaultAsync(x => x.EstdetailId == estdetail.EstdetailId);
                if (updateDetails?.Podetail?.Poheader?.Postatus == 5)
                {
                    return BadRequest("Cannot issue budget if PO is cancelled.");
                }
                var newEstJceDetail = new Estjcedetail()
                {
                    //TODO: fill the right fields
                    EstoptionId = (int)updateDetails.EstoptionId,
                    Costcode = updateDetails.JcPhase,
                    Category = updateDetails.Category,
                    EstimateAmount = updateDetails.Amount,
                    EstimateUnits = updateDetails.OrderQty,
                    UnitDesc = updateDetails.OrdrUnit,//
                    EstimateDate = DateTime.Now,
                    CreatedBy = User.Identity.Name.Split('@')[0],

                };
                _context.Estjcedetails.Add(newEstJceDetail);
                await _context.SaveChangesAsync();
                updateDetails.EstjcedetailId = newEstJceDetail.EstjcedetailId;
                updateDetails.UpdatedBy = User.Identity.Name.Split('@')[0];
                updateDetails.UpdatedDateTim = DateTime.Now;
                updateDetails.EstimateExportAmt = updateDetails.Amount;
                _context.Estdetails.Update(updateDetails);
                await _context.SaveChangesAsync();
                var returnItem = _mapper.Map<EstdetailDto>(updateDetails);
                return Ok(new ResponseModel<EstdetailDto> { IsSuccess = true, Value = returnItem, Message = "Issued budget." });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstdetailDto> { IsSuccess = false, Message = "Failed to issue budget item", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> IssueBudgetsAsync([FromBody] List<CombinedPOBudgetTreeModel> budgets)
        {
            //Budget "issued" saves the Jcedetail, grouped by option, costcode, and category, and "session"
            try
            {
                //4/14/25 Checkboxes only sending estdetails, not headers or option level, so those are commented out for now

                //TODO: figure out logic on reissue ones, also on ones that are done at the same session but click separately 
                //don't issue ones with errors
                string updateBy = User.Identity.Name.Split('@')[0];
                //foreach(var row in budgets.Where(x => x.Estheader != null && x.Estoption == null))
                //{
                //   //process the selected headers as a group
                //   var findDetails = _context.Estdetails.Include("Estoption").Where(x => string.IsNullOrWhiteSpace(x.Errors) && x.Estoption.EstheaderId == row.Estheader.EstheaderId && x.EstjcedetailId == null && x.IsActive == true && (x.Podetail == null || x.Podetail.Poheader.Postatus != 5)).GroupBy(y => new { y.EstoptionId, y.JcCategory, y.JcPhase }).ToList();
                //    foreach (var group in findDetails)
                //    {
                //        var newEstJceDetail = new Estjcedetail()
                //        {
                //            //TODO: fill the right fields
                //            EstoptionId = (int)group.Key.EstoptionId,
                //            Costcode = group.Key.JcPhase,
                //            Category = group.Key.JcCategory,//What is category vs jc category?
                //            EstimateAmount = group.Sum(x => x.Amount),
                //            EstimateUnits = group.Sum(x => x.OrderQty),
                //            UnitDesc = group.First().OrdrUnit,//Seems wrong, but it looks like it's what's done
                //            EstimateDate = DateTime.Now,
                //            CreatedBy = updateBy
                //        };
                //        _context.Estjcedetails.Add(newEstJceDetail);
                //        await _context.SaveChangesAsync();
                //        //update the estdetails with the new jcedetailid
                //        var estDetailIds = group.Select(x => x.EstdetailId).ToList();
                //        var updateEstDetails = _context.Estdetails.Where(x => estDetailIds.Contains(x.EstdetailId)).ToList();
                //        foreach(var estDetail in updateEstDetails)
                //        {
                //            estDetail.EstjcedetailId = newEstJceDetail.EstjcedetailId;
                //            estDetail.UpdatedBy = updateBy;
                //            estDetail.UpdatedDateTim = DateTime.Now;
                //            estDetail.EstimateExportAmt = estDetail.Amount;
                            
                //        }
                //        _context.Estdetails.UpdateRange(updateEstDetails);
                //        await _context.SaveChangesAsync();
                //        //await _context.Estdetails.BulkUpdateAsync(updateEstDetails, options => options.ColumnInputExpression = x => new { x.EstjcedetailId, x.UpdatedBy, x.UpdatedDateTim, x.EstimateExportAmt });
                //    }
                //}
                //foreach (var row in budgets.Where(x => x.Estoption != null && x.Estactivity == null))
                //{
                //    //process the selected options as a group, should only process details not processed above already
                //    //get details not already issued, grouped by cost code and category
                //    var findGroupDetailsInOption = _context.Estdetails.Where(x => string.IsNullOrWhiteSpace(x.Errors) && x.EstoptionId == row.Estoption.EstoptionId && x.EstjcedetailId == null).GroupBy(y => new { y.EstoptionId, y.JcCategory, y.JcPhase }).ToList();
                //    foreach(var group in findGroupDetailsInOption)
                //    {
                //        var newEstJceDetail = new Estjcedetail()
                //        {
                //            //TODO: fill the right fields
                //            EstoptionId = (int)group.Key.EstoptionId,
                //            Costcode = group.Key.JcPhase,
                //            Category = group.Key.JcCategory,//What is category vs jc category?
                //            EstimateAmount = group.Sum(x => x.Amount),
                //            EstimateUnits = group.Sum(x => x.OrderQty),
                //            UnitDesc = group.First().OrdrUnit,//Seems wrong, but it looks like it's what's done
                //            EstimateDate = DateTime.Now,
                //            CreatedBy = updateBy
                //        };
                //        _context.Estjcedetails.Add(newEstJceDetail);
                //        _context.SaveChanges();
                //        //update the estdetails with the new jcedetailid
                //        var estDetailIds = group.Select(x => x.EstdetailId).ToList();
                //        var updateEstDetails = _context.Estdetails.Where(x => estDetailIds.Contains(x.EstdetailId));
                //        foreach (var estDetail in updateEstDetails)
                //        {
                //            estDetail.EstjcedetailId = newEstJceDetail.EstjcedetailId;
                //            estDetail.UpdatedBy = updateBy;
                //            estDetail.UpdatedDateTim = DateTime.Now;
                //            estDetail.EstimateExportAmt = estDetail.Amount;
                //        }
                //        _context.Estdetails.UpdateRange(updateEstDetails);
                //        await _context.SaveChangesAsync();
                //       // await _context.Estdetails.BulkUpdateAsync(updateEstDetails, options => options.ColumnInputExpression = x => new { x.EstjcedetailId, x.UpdatedBy, x.UpdatedDateTim, x.EstimateExportAmt });
                //    }
                //}
                //foreach (var row in budgets.Where(x => x.Estactivity != null && x.Estdetail == null))
                //{
                //    var findDetails = new List<Estdetail>();
                //    if(row.Estoption == null)//in release sort, option is not there
                //    {
                //        findDetails = _context.Estdetails.Where(x => string.IsNullOrWhiteSpace(x.Errors) && x.EstactivityId == row.Estactivity.EstactivityId && x.EstjcedetailId == null && x.IsActive == true).ToList();
                //    }
                //    else
                //    {
                //        findDetails = _context.Estdetails.Where(x => string.IsNullOrWhiteSpace(x.Errors) && x.EstoptionId == row.Estoption.EstoptionId && x.EstactivityId == row.Estactivity.EstactivityId && x.EstjcedetailId == null && x.IsActive == true).ToList();
                //    }
                //    var groupedDetails = findDetails.GroupBy(y => new { y.EstoptionId, y.JcCategory, y.JcPhase });
                //    foreach (var group in groupedDetails)
                //    {
                //        var newEstJceDetail = new Estjcedetail()
                //        {
                //            //TODO: fill the right fields
                //            EstoptionId = (int)group.Key.EstoptionId,
                //            Costcode = group.Key.JcPhase,
                //            Category = group.Key.JcCategory,//What is category vs jc category?
                //            EstimateAmount = group.Sum(x => x.Amount),
                //            EstimateUnits = group.Sum(x => x.OrderQty),
                //            UnitDesc = group.First().OrdrUnit,//Seems wrong, but it looks like it's what's done
                //            EstimateDate = DateTime.Now,
                //            CreatedBy = updateBy
                //        };
                //        _context.Estjcedetails.Add(newEstJceDetail);
                //        await _context.SaveChangesAsync();
                //        //update the estdetails with the new jcedetailid
                //        var estDetailIds = group.Select(x => x.EstdetailId).ToList();
                //        var updateEstDetails = _context.Estdetails.Where(x =>estDetailIds.Contains(x.EstdetailId));
                //        foreach (var estDetail in updateEstDetails)
                //        {
                //            estDetail.EstjcedetailId = newEstJceDetail.EstjcedetailId;
                //            estDetail.UpdatedBy = updateBy;
                //            estDetail.UpdatedDateTim = DateTime.Now;
                //            estDetail.EstimateExportAmt = estDetail.Amount;
                //        }
                //        _context.Estdetails.UpdateRange(updateEstDetails);
                //        await _context.SaveChangesAsync();
                //       // await _context.Estdetails.BulkUpdateAsync(updateEstDetails, options => options.ColumnInputExpression = x => new { x.EstjcedetailId, x.UpdatedBy, x.UpdatedDateTim, x.EstimateExportAmt });
                //    }
                //}
                var remainingDetailsIds = budgets.Where(x => x.Estdetail != null).Select(x => x.Estdetail.EstdetailId).ToList();
                var findRemainingDetailsGrouped = _context.Estdetails.Where(x => string.IsNullOrWhiteSpace(x.Errors) && remainingDetailsIds.Contains(x.EstdetailId) && x.EstjcedetailId == null && x.IsActive == true).GroupBy(y => new { y.EstoptionId, y.JcCategory, y.JcPhase }).ToList();
                foreach (var group in findRemainingDetailsGrouped)
                {
                    var newEstJceDetail = new Estjcedetail()
                    {
                        //TODO: fill the right fields
                        EstoptionId = (int)group.Key.EstoptionId,
                        Costcode = group.Key.JcPhase,
                        Category = group.Key.JcCategory,//What is category vs jc category?
                        EstimateAmount = group.Sum(x => x.Amount),
                        EstimateUnits = group.Sum(x => x.OrderQty),
                        UnitDesc = group.First().OrdrUnit,//Seems wrong, but it looks like it's what's done
                        EstimateDate = DateTime.Now,
                        CreatedBy = updateBy
                    };
                    _context.Estjcedetails.Add(newEstJceDetail);
                    await _context.SaveChangesAsync();
                    //update the estdetails with the new jcedetailid
                    var estDetailIds = group.Select(x => x.EstdetailId).ToList();
                    var updateEstDetails = _context.Estdetails.Where(x => estDetailIds.Contains(x.EstdetailId)).ToList();
                    foreach (var estDetail in updateEstDetails)
                    {
                        estDetail.EstjcedetailId = newEstJceDetail.EstjcedetailId;
                        estDetail.UpdatedBy = updateBy;
                        estDetail.UpdatedDateTim = DateTime.Now;
                        estDetail.EstimateExportAmt = estDetail.Amount;
                    }
                    _context.Estdetails.UpdateRange(updateEstDetails);
                    await _context.SaveChangesAsync();
                   // await _context.Estdetails.BulkUpdateAsync(updateEstDetails, options => options.ColumnInputExpression = x => new { x.EstjcedetailId, x.UpdatedBy, x.UpdatedDateTim, x.EstimateExportAmt });
                }
            
                return Ok(new ResponseModel<List<CombinedPOBudgetTreeModel>> { IsSuccess = true, Value = budgets, Message = "Issued budgets" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CombinedPOBudgetTreeModel>> { IsSuccess = false, Message = "Some budgets failed to issue.", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> CancelBudgetItemAsync([FromBody] EstdetailDto estdetail)
        {
            //TODO: prevent re-cancel
            //cancel issued budget
            try
            {
               
                var updateDetails = await _context.Estdetails.Include(x => x.Podetail.Poheader).SingleOrDefaultAsync(x => x.EstdetailId == estdetail.EstdetailId);
                if(updateDetails.Podetail != null && updateDetails.Podetail.Poheader.Postatus != 5)//status 5 is cancelled
                {
                    return Ok(new ResponseModel<EstdetailDto> { IsSuccess = false, Message = "There is an active PO. PO must be cancelled before budget can be cancelled" });
                }
                var findEstJceDetail = await _context.Estjcedetails.SingleOrDefaultAsync(x => x.EstjcedetailId == updateDetails.EstjcedetailId);
                if (findEstJceDetail.EstimateExported == "T")//already sent to accounting
                {
                    //generate offset
                    var offsetJce = new Estjcedetail()
                    {
                        EstoptionId = (int)estdetail.EstoptionId,
                        IsCancelled = "T",
                        Costcode = findEstJceDetail.Costcode,
                        Category = findEstJceDetail.Category,
                        EstimateUnits = estdetail.OrderQty,
                        UnitDesc = estdetail.OrdrUnit,
                        EstimateAmount = -estdetail.Amount,
                        EstimateDate = DateTime.Now,
                        CreatedBy = User.Identity.Name.Split('@')[0],
                    };
                    _context.Estjcedetails.Add(offsetJce);
                    await _context.SaveChangesAsync();

                    //associate the detail with the cancel so it needs to be sent to accounting then issued again 
                    updateDetails.EstjcedetailId = offsetJce.EstjcedetailId;
                    updateDetails.UpdatedBy = User.Identity.Name.Split('@')[0];
                    updateDetails.UpdatedDateTim = DateTime.Now;
                    //3/17/25 amounts should not be zeroed out. - why not??? the total is now 0?
                    //updateDetails.OrgPrice = updateDetails.Price;
                    //updateDetails.OrigCommittedAmount = updateDetails.Amount;
                    //updateDetails.Amount = 0;
                    //updateDetails.Price = 0;
                    _context.Estdetails.Update(updateDetails);
                    await _context.SaveChangesAsync();

                    var returnItem = _mapper.Map<EstdetailDto>(updateDetails);
                    return Ok(new ResponseModel<EstdetailDto> { IsSuccess = true, Value = returnItem, Message = "Cancelled Budget" });
                }
                else
                {
                    //jce detail not already sent to accounting
                    //if it was a combined jcedetail, but not sent to account, subtract out the amount from this estdetai, because other estdetails may share the same estjcedetail 
                    //findEstJceDetail.IsCancelled = "T";// no, because it is just canceling one estdetail from this, but then it shouldn't be 
                    var findDetailsThisJce = _context.Estdetails.Where(x => x.EstjcedetailId == findEstJceDetail.EstjcedetailId);
                    findEstJceDetail.IsActive = findDetailsThisJce.Count() <= 1 ? false : true;//deactivate if not associated with any other estdetail 
                    findEstJceDetail.EstimateAmount -= estdetail.Amount;
                    findEstJceDetail.EstimateUnits -= estdetail.OrderQty;
                    findEstJceDetail.UpdatedBy = User.Identity.Name.Split('@')[0];
                    findEstJceDetail.UpdatedDateTime = DateTime.Now;
                    _context.Estjcedetails.Update(findEstJceDetail);
                    await _context.SaveChangesAsync();

                    //updateDetails.IsActive = false;//deactivate the detail if it was cancelled and it wasn't sent //4/8/25 Julie says cancel should not delete the item                    
                    updateDetails.EstjcedetailId = null;
                    updateDetails.UpdatedBy = User.Identity.Name.Split('@')[0];
                    updateDetails.UpdatedDateTim = DateTime.Now;
                    updateDetails.EstimateExportAmt = 0;
                    _context.Estdetails.Update(updateDetails);
                    await _context.SaveChangesAsync();
                    var returnItem = _mapper.Map<EstdetailDto>(updateDetails);
                    return Ok(new ResponseModel<EstdetailDto> { IsSuccess = true, Value = returnItem, Message = "Cancelled budget" });
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstdetailDto> { IsSuccess = false, Message = "Failed to cancel budget item", Value = null });
            }
        }
        
        [HttpPut]
        public async Task<IActionResult> CancelBudgetsAsync([FromBody] List<CombinedPOBudgetTreeModel> estdetails)
        {
            //Not used???
            try
            {
                foreach (var estDetail in estdetails)
                {
                    //TODO: each jcedetail might be grouped for several items, cancelling one item should projably not cancel the whole jcedetail
                    //TODO: what happens here probably depends on whehter it was sent to accounting
                    //If it was sent to accounting, generate a new estdetail to offset the one that was cancelled. If not sent to accounting, just adjust the total, I guess
                    var updateDetails = await _context.Estdetails.SingleOrDefaultAsync(x => x.EstdetailId == estDetail.Estdetail.EstdetailId);
                    var findEstJceDetail = await _context.Estjcedetails.SingleOrDefaultAsync(x => x.EstjcedetailId == updateDetails.EstjcedetailId);
                    if (findEstJceDetail.EstimateExported == "T")
                    {
                        var offsetJce = new Estjcedetail()
                        {
                            EstoptionId = (int)estDetail.Estdetail.EstoptionId,
                            IsCancelled = "T",
                            Costcode = findEstJceDetail.Costcode,
                            Category = findEstJceDetail.Category,//TODO: does it change for cancel?
                            EstimateUnits = estDetail.Estdetail.OrderQty,
                            UnitDesc = estDetail.Estdetail.OrdrUnit,
                            EstimateAmount = -estDetail.Estdetail.Amount,//TODO: check
                            CreatedBy = User.Identity.Name.Split('@')[0],
                        };
                        _context.Estjcedetails.Add(offsetJce);
                        await _context.SaveChangesAsync();
                        //associate the detail with the cancel so it needs to be sent to accounting then issued again 
                        //TODO: In WMS it looks like it sets the cost to 0 (and the using lump to true) in the estdetail when cancel. Why? Is that needed?
                        updateDetails.EstjcedetailId = offsetJce.EstjcedetailId;
                        _context.Estdetails.Update(updateDetails);
                        await _context.SaveChangesAsync();
                        //TODO: need to associate the estdetail with the new offset entry, so can't reissue until the offset entry is sent to accounting
                        //so issue enabled would be true if jcedtail is null, or if jcedetail is not null, but it is jcedetai is a reissue and it is sent to accounting, or maybe once send the reissue jce to accounting, can set the original estdetail jce  back to null? ? ? ? 

                    }
                    else
                    {
                        //jce detail not already sent to accounting
                        //if it was a combined jcedetail, but not sent to account, subtract out the amount from this estdetai, because other estdetails may share the same estjcedetail 
                        //findEstJceDetail.IsCancelled = "T";// no, because it is just canceling one estdetail from this, , but then it shouldn't be available in the send to accounting screen if it was the last one, but it won't be, because those get from job, estheader, then estoption, then estdetail, right??? no., if it's the last one it still shows. how to fix that?? - 
                        //how about this - check for other estdetails pointing to this item, and if none, mark it inactive?
                        var findDetailsThisJce = _context.Estdetails.Where(x => x.EstjcedetailId == findEstJceDetail.EstjcedetailId);
                        findEstJceDetail.IsActive = findDetailsThisJce.Count() <= 1 ? false : true;//deactivate if not associated with any other estdetail 
                        findEstJceDetail.EstimateAmount -= estDetail.Estdetail.Amount;
                        findEstJceDetail.EstimateUnits -= estDetail.Estdetail.OrderQty;
                        findEstJceDetail.UpdatedBy = User.Identity.Name.Split('@')[0];
                        findEstJceDetail.UpdatedDateTime = DateTime.Now;
                        _context.Estjcedetails.Update(findEstJceDetail);
                        await _context.SaveChangesAsync();

                        updateDetails.EstjcedetailId = null;
                        updateDetails.UpdatedBy = User.Identity.Name.Split('@')[0];
                        updateDetails.UpdatedDateTim = DateTime.Now;
                        _context.Estdetails.Update(updateDetails);
                        await _context.SaveChangesAsync();
                    }

                }
                return Ok(new ResponseModel<List<CombinedPOBudgetTreeModel>> { IsSuccess = true, Value = estdetails });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<CombinedPOBudgetTreeModel>> { IsSuccess = false, Message = "Failed to cancel budgets", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeleteJceDetailAsync([FromBody] EstjcedetailDto estJceDetail)
        {
            //if it was not already sent to Nav, mark it inactive, and update all the estdetails that point to it. But what if it's a cancel? then it can't just be deleted, because the offset entry would need to be sent before reissue can happen 
            try
            {
                var findEstJceDetail = await _context.Estjcedetails.SingleOrDefaultAsync(x => x.EstjcedetailId == estJceDetail.EstjcedetailId);

                findEstJceDetail.IsActive = false;
                findEstJceDetail.UpdatedBy = User.Identity.Name.Split('@')[0];
                findEstJceDetail.UpdatedDateTime = DateTime.Now;
                _context.Estjcedetails.Update(findEstJceDetail);
                await _context.SaveChangesAsync();

                //TODO: if it was a cancel offset item, need to set something in the original estdetail so it knows it can now be reissued
                //for now, let's set the jce detailid to null, but this loses tracking of the cancel and reissue

                //maybe if it is deleting cancel, the corresponding estdetail goes back to issued status -- which would mean it would need to put back the previous estjcedetail id, which it doesn't have,   and can be needs to be "recancelled" or else maybe you can't delete the cancel??? 
                if (findEstJceDetail.IsCancelled != "T")
                {
                    var findEstDetail = _context.Estdetails.Where(x => x.EstjcedetailId == findEstJceDetail.EstjcedetailId);
                    await findEstDetail.ExecuteUpdateAsync(s => s.SetProperty(b => b.EstjcedetailId, (int?)null));
                    //Does it need to fill the estdetail estimate exported columns?
                }

                return Ok(new ResponseModel<EstjcedetailDto> { IsSuccess = true, Value = estJceDetail });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstjcedetailDto> { IsSuccess = false, Message = "failed to delete Jce Detail", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeleteJceDetailsAsync([FromBody] List<EstjcedetailDto> estJceDetails)
        {
            try
            {
                //if it was not already sent to Nav, mark it inactive, and update all the estdetails that point to it. But what if it's a cancel? then it can't just be deleted, because the offset entry would need to be sent before reissue can happen 
                foreach (var estJceDetail in estJceDetails)
                {
                    var findEstJceDetail = await _context.Estjcedetails.SingleOrDefaultAsync(x => x.EstjcedetailId == estJceDetail.EstjcedetailId);

                    findEstJceDetail.IsActive = false;
                    findEstJceDetail.UpdatedBy = User.Identity.Name.Split('@')[0];
                    findEstJceDetail.UpdatedDateTime = DateTime.Now;
                    _context.Estjcedetails.Update(findEstJceDetail);
                    await _context.SaveChangesAsync();

                    //TODO: if it was a cancel offset item, need to set something in the original estdetail so it knows it can now be reissued
                    //for now, let's set the jce detailid to null, but this loses tracking of the cancel and reissue

                    //maybe if it is deleting cancel, the corresponding estdetail goes back to issued status -- which would mean it would need to put back the previous estjcedetail id, which it doesn't have,   and can be needs to be "recancelled" or else maybe you can't delete the cancel??? 
                    if (findEstJceDetail.IsCancelled != "T")
                    {
                        var findEstDetail = _context.Estdetails.Where(x => x.EstjcedetailId == findEstJceDetail.EstjcedetailId);
                        await findEstDetail.ExecuteUpdateAsync(s => s.SetProperty(b => b.EstjcedetailId, (int?)null));
                        //Does it need to fill the estdetail estimate exported columns?
                    }
                }
                return Ok(new ResponseModel<List<EstjcedetailDto>> { IsSuccess = true, Value = estJceDetails });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstjcedetailDto>> { IsSuccess = false, Message = "failed to delete Jce details", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdateJceDetailsExportedAsync([FromBody] List<EstjcedetailDto> estJceDetails)
        {
            try
            {
                //update exported column, to track which ones sent to nav
                foreach(var item in estJceDetails)
                {
                    item.UpdatedDateTime = DateTime.Now;
                    item.UpdatedBy = User.Identity.Name.Split('@')[0];
                    item.EstimateExported = item.BoolEstimateExported ? "T" : "F";
                    item.Estoption = null;//to make the mapping work
                }
                var updateItems = _mapper.Map<List<Estjcedetail>>(estJceDetails);
                await _context.Estjcedetails.BulkUpdateAsync(updateItems, options => options.ColumnInputExpression = x => new { x.EstimateExported, x.UpdatedBy, x.UpdatedDateTime });                
                return Ok(new ResponseModel<List<EstjcedetailDto>> { IsSuccess = true, Value = estJceDetails });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstjcedetailDto>> { IsSuccess = false, Message = "failed to delete Jce details", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdateEstDetailAsync([FromBody] EstdetailDto estdetail)
        {
            try
            {
                var updateDetails = await _context.Estdetails.Include(x => x.Podetail).Include(x => x.Estoption.Estheader).Include(x => x.Estactivity).SingleOrDefaultAsync(x => x.EstdetailId == estdetail.EstdetailId);
                updateDetails.ItemDesc = estdetail.ItemDesc;
                updateDetails.ItemNotes = estdetail.ItemNotes;
                // updateDetails.ItemNumber = estdetail.ItemNumber;
                //updateDetails.PhaseCode = estdetail.PhaseCode;
                updateDetails.OrdrUnit = estdetail.OrdrUnit;
                updateDetails.Amount = estdetail.Amount;
                updateDetails.OrderQty = estdetail.OrderQty;
                updateDetails.Price = estdetail.Price;
                updateDetails.OrigCommittedAmount = estdetail.OrigCommittedAmount;
                updateDetails.OrigCommittedQty = estdetail.OrigCommittedQty;
                updateDetails.OrgPrice = estdetail.OrgPrice;
                updateDetails.Lump = estdetail.Lump;
                updateDetails.UseEstPrice = estdetail.UseEstPrice;
                updateDetails.Taxable = estdetail.Taxable;
                updateDetails.Warnings = estdetail.Warnings;
                updateDetails.JcCategory = estdetail.JcCategory;
                updateDetails.JcPhase = estdetail.JcPhase;
                //TODO: how does tracking variance work?
                if (estdetail.Amount != updateDetails.OrigCommittedAmount)
                {
                    //updateDetails.OrgPrice = updateDetails.OrgPrice ?? updateDetails.Price;//set the original price to the previous price, unless this is being changed again
                    updateDetails.VarianceJcCategory = "CO";//NOTE: In WMS there is a picker, but Julie says CO is always what they pick
                }
                var noCostErrorString = updateDetails.Lump == "T" || updateDetails.CostsId != null ? "not null" : null;
                updateDetails.Errors = ErrorString(noCostErrorString, estdetail.JcPhase, estdetail.JcCategory);
                updateDetails.UpdatedBy = User.Identity.Name.Split('@')[0];
                updateDetails.UpdatedDateTim = DateTime.Now;
                _context.Estdetails.Update(updateDetails);
                await _context.SaveChangesAsync();
                var getDetailDto = _mapper.Map<EstdetailDto>(updateDetails);
                getDetailDto.BoolTaxable = getDetailDto.Taxable == "T";
                getDetailDto.BoolLump = getDetailDto.Lump == "T";
                getDetailDto.BoolUseEstPrice = getDetailDto.UseEstPrice == "T";
                getDetailDto.DisplayWarnings = !string.IsNullOrWhiteSpace(getDetailDto.Warnings) ? string.Join(", ", getDetailDto.Warnings.Split('|').Select(x => EnumExtension.GetDescriptionFromEnum((BudgetWarnings)int.Parse(x))).ToList()) : null;
                getDetailDto.DisplayErrors = !string.IsNullOrWhiteSpace(getDetailDto.Errors) ? string.Join(", ", getDetailDto.Errors.Split('|').Select(x => EnumExtension.GetDescriptionFromEnum((BudgetErrors)int.Parse(x))).ToList()) : null;
                //getDetailDto.Podetail = getDetailDto.Podetail ?? new PodetailDto() { Poheader = new PoheaderDto() { PostatusNavigation = new PostatusDto() } };
                return Ok(new ResponseModel<EstdetailDto> { IsSuccess = true, Value = getDetailDto, Message = "Updated estimate details" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstdetailDto> { IsSuccess = false, Message = "Failed to update est detail", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdateEstActivityAsync([FromBody] EstactivityDto estactivity)
        {
            try
            {
                //TODO: don't change supplier if PO already issued
                var findBlockedVendors = _context.Suppliers.AsNoTrackingWithIdentityResolution().Where(x => x.Blocked == true).Select(x => x.SubNumber).Distinct().ToList();
                var findInsuranceExpiredVendors = _context.SupplierInsurances.AsNoTrackingWithIdentityResolution().Where(x => x.InsuranceRequired == 1 && x.PolicyExpirationDate.Date < DateTime.Now.Date && x.IsActive == true).Select(x => x.SubNumber).Distinct().ToList();
                var updateActivity = await _context.Estactivities.SingleOrDefaultAsync(x => x.EstactivityId == estactivity.EstactivityId);
                updateActivity.Taxable = estactivity.Taxable;
                updateActivity.UseWbsSort = estactivity.UseWbsSort;
                updateActivity.UseLocation = estactivity.UseLocation;
                updateActivity.SelectedVendor = estactivity.SelectedVendor;
                updateActivity.UpdatedBy = User.Identity.Name.Split('@')[0];
                updateActivity.UpdatedDateTime = DateTime.Now;
                _context.Estactivities.Update(updateActivity);
                await _context.SaveChangesAsync();
                estactivity.VendorNoInsurance = findInsuranceExpiredVendors.Contains(estactivity.SelectedVendor);
                estactivity.VendorBlocked = findBlockedVendors.Contains((int)estactivity.SelectedVendor);//TODO: fix to return the new one, not the old one
                return Ok(new ResponseModel<EstactivityDto> { IsSuccess = true, Value = estactivity, Message = "Updated Activity" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstactivityDto> { IsSuccess = false, Message = "Failed to update activity", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdateEstOptionAsync([FromBody] EstoptionDto estOption)
        {
            try
            {
                var updateOption = await _context.Estoptions.SingleOrDefaultAsync(x => x.EstoptionId == estOption.EstoptionId);
                updateOption.OptionDesc = estOption.OptionDesc;
                updateOption.OptionSalesPrice = estOption.OptionSalesPrice;
                updateOption.OptionQty = estOption.OptionQty;
                updateOption.OptionNotes = estOption.OptionNotes;
                //updateOption.OptionSelections = estOption.OptionSelections;
                updateOption.UpdatedBy = User.Identity.Name.Split('@')[0];
                updateOption.UpdatedDateTime = DateTime.Now;
                _context.Estoptions.Update(updateOption);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<EstoptionDto> { IsSuccess = true, Value = estOption, Message = "Updated Estimate option" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstoptionDto> { IsSuccess = false, Message = "Failed to update estimate option", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdateEstHeaderAsync([FromBody] EstheaderDto estHeader)
        {
            try
            {
                var updateHeader = await _context.Estheaders.SingleOrDefaultAsync(x => x.EstheaderId == estHeader.EstheaderId);
                updateHeader.EstimateDescPe = estHeader.EstimateDescPe;
                updateHeader.EstimateSalesPrice = estHeader.EstimateSalesPrice;
                updateHeader.Estimator = estHeader.Estimator;
                updateHeader.ReferenceNumber = estHeader.ReferenceNumber;
                updateHeader.ReferenceDesc = estHeader.ReferenceDesc;
                updateHeader.ReferenceType = estHeader.ReferenceType;//TODO: seems to be only editable if it s not original estimate
                updateHeader.Heading1 = estHeader.Heading1;
                updateHeader.Heading2 = estHeader.Heading2;
                updateHeader.Heading3 = estHeader.Heading3;
                updateHeader.JobSize = estHeader.JobSize;
                updateHeader.JobUnit = estHeader.JobUnit;
                updateHeader.UpdatedBy = User.Identity.Name.Split('@')[0];
                updateHeader.UpdatedDateTime = DateTime.Now;
                _context.Estheaders.Update(updateHeader);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<EstheaderDto> { IsSuccess = true, Value = estHeader, Message = "Update saved successfully." });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstheaderDto> { IsSuccess = false, Message = "Failed to update est header", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> CopyItemAsync([FromBody] EstdetailDto itemToCopy)
        {            
            try
            {
               
                var findItem = await _context.Estdetails.Include("Estoption.Estheader").Include("Estactivity").SingleOrDefaultAsync(x => x.EstdetailId == itemToCopy.EstdetailId);

                var copyItem = new Estdetail()
                {
                    EstoptionId = findItem.EstoptionId,
                    EstactivityId = findItem.EstactivityId,
                    MasterItemsId = findItem.MasterItemsId,
                    PhaseCode = findItem.PhaseCode,
                    ItemNumber = findItem.ItemNumber,
                    Category = "C",//TODO: it seems to need to change the cost category?? this seems to be the only option? - "S" means sales? ? "CO" means change order??? -- broken now, now it says "M" -it used to be CO, no the field is only varchar1 - no seems they pick it when they copy
                    Instance = findItem.Instance,//???
                    SeqNumber = findItem.SeqNumber,//???
                    Lump = findItem.Lump,
                    Location = findItem.Location,
                    SortLocation = findItem.SortLocation,
                    SortWbs = findItem.SortWbs,
                    ItemDesc = findItem.ItemDesc,
                    ItemNotes = findItem.ItemNotes,
                    TakeoffQuantity = findItem.TakeoffQuantity,
                    TakeoffUnit = findItem.TakeoffUnit,
                    Waste = findItem.Waste,
                    VendorNumber = findItem.VendorNumber,
                    LengthWbs = findItem.LengthWbs,
                    NoOfLengths = findItem.NoOfLengths,
                    ThicknessWbs = findItem.ThicknessWbs,
                    WidthWbs = findItem.WidthWbs,
                    ApplyWaste = findItem.ApplyWaste,
                    CnvFctr = findItem.CnvFctr,
                    Multdiv = findItem.Multdiv,
                    OrdrUnit = findItem.OrdrUnit,
                    OrderQty = findItem.OrderQty,
                    Price = findItem.Price,
                    Amount = findItem.Amount,
                    OrgPrice = findItem.OrgPrice,
                    OrigCommittedQty = findItem.OrigCommittedQty,
                    OrigCommittedAmount = findItem.OrigCommittedAmount,
                    JcCategory = findItem.JcCategory,
                    JcPhase = findItem.JcPhase,                   
                    VarianceJcCategory = findItem.VarianceJcCategory,
                    Warnings = findItem.Warnings,
                    Errors = findItem.Errors,
                    CreatedBy = User.Identity.Name.Split('@')[0]
                };

                _context.Estdetails.Add(copyItem);
                await _context.SaveChangesAsync();
                var itemToReturn = _context.Estdetails.SingleOrDefault(x => x.EstdetailId == copyItem.EstdetailId);
                var returnItem = _mapper.Map<EstdetailDto>(itemToReturn);
                returnItem.BoolLump = returnItem.Lump == "T";
                returnItem.BoolTaxable = returnItem.Taxable == "T";
                returnItem.BoolUseEstPrice = returnItem.UseEstPrice == "T";
                if (!string.IsNullOrWhiteSpace(returnItem.Errors))
                {
                    var errors = returnItem.Errors.Split('|').ToList();
                    var stringErrors = new List<string>();
                    foreach (var error in errors)
                    {
                        int intError;
                        if (int.TryParse(error, out intError))
                        {
                            //TODO: also check it's in the range of the enum; there must be cleaner way.
                            stringErrors.Add(EnumExtension.GetDescriptionFromEnum((BudgetErrors)intError));
                        }
                    }
                    returnItem.DisplayErrors = string.Join(", ", stringErrors);
                }
                if (!string.IsNullOrWhiteSpace(returnItem.Warnings))
                {
                    var warnings = returnItem.Warnings.Split('|').ToList();
                    var stringWarnings = new List<string>();
                    foreach (var warning in warnings)
                    {
                        int intWarning;
                        if (int.TryParse(warning, out intWarning))
                        {
                            //TODO: also check it's in the range of the enum; there must be cleaner way.
                            stringWarnings.Add(EnumExtension.GetDescriptionFromEnum((BudgetWarnings)intWarning));
                        }
                    }
                    returnItem.DisplayWarnings = string.Join(", ", stringWarnings);
                }
                return Ok(new ResponseModel<EstdetailDto> { IsSuccess = true, Value = returnItem , Message = "Copied Item" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstdetailDto> { IsSuccess = false, Message = "Failed to copy item", Value = null  });
            }
        }
        [HttpPost]
        public async Task<IActionResult> ReissueItemAsync([FromBody] EstdetailDto itemToCopy)
        {
            try
            {
                //When cancel PO, copy the item to make available to reissue, 
                // on old one set cost to 0. 
                //TODO:should it look up new cost instead of copying the one from the estdetail??? 
                var findItem = await _context.Estdetails.Include("Estoption.Estheader").Include("Estactivity").SingleOrDefaultAsync(x => x.EstdetailId == itemToCopy.EstdetailId);              
                var copyItem = new Estdetail()
                {
                    EstoptionId = findItem.EstoptionId,
                    EstactivityId = findItem.EstactivityId,
                    MasterItemsId = findItem.MasterItemsId,
                    PhaseCode = findItem.PhaseCode,
                    ItemNumber = findItem.ItemNumber,
                    Category = "C",//TODO: it seems to need to change the cost category?? this seems to be the only option? - "S" means sales? ? "CO" means change order??? -- broken now, now it says "M" -it used to be CO, no the field is only varchar1 - no seems they pick it when they copy
                    Instance = findItem.Instance,//???
                    SeqNumber = findItem.SeqNumber,//???
                    Lump = findItem.Lump,
                    Location = findItem.Location,
                    SortLocation = findItem.SortLocation,
                    SortWbs = findItem.SortWbs,
                    ItemDesc = findItem.ItemDesc,
                    ItemNotes = findItem.ItemNotes,
                    TakeoffQuantity = findItem.TakeoffQuantity,//?? get new from master item???
                    TakeoffUnit = findItem.TakeoffUnit,
                    Waste = findItem.Waste,
                    VendorNumber = findItem.VendorNumber,//?? get new
                    LengthWbs = findItem.LengthWbs,
                    NoOfLengths = findItem.NoOfLengths,
                    ThicknessWbs = findItem.ThicknessWbs,
                    WidthWbs = findItem.WidthWbs,
                    ApplyWaste = findItem.ApplyWaste,
                    CnvFctr = findItem.CnvFctr,
                    Multdiv = findItem.Multdiv,
                    OrdrUnit = findItem.OrdrUnit,
                    OrderQty = findItem.OrderQty,
                    Price = findItem.Price,
                    Amount = findItem.Amount,
                    OrgPrice = findItem.OrgPrice,
                    JcCategory = findItem.JcCategory,
                    JcPhase = findItem.JcPhase,
                    VarianceJcCategory = findItem.VarianceJcCategory,
                                                                     //tracking variance gets set to true, but I don't have that field
                    CreatedBy = User.Identity.Name.Split('@')[0]
                };
                //TODO: resum to the activity level???
                _context.Estdetails.Add(copyItem);
                await _context.SaveChangesAsync();
                var itemToReturn = _context.Estdetails.SingleOrDefault(x => x.EstdetailId == copyItem.EstdetailId);
                var returnItem = _mapper.Map<EstdetailDto>(itemToReturn);
                returnItem.BoolLump = returnItem.Lump == "T";
                returnItem.BoolTaxable = returnItem.Taxable == "T";
                returnItem.BoolUseEstPrice = returnItem.UseEstPrice == "T";

                ////update the original to have cost set to 0 - removed 5/7/25
                //findItem.Amount = 0;
                //findItem.OrderQty = 0;
                //findItem.Price = 0;
                //findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                //findItem.UpdatedDateTim = DateTime.Now;
                //_context.Estdetails.Update(findItem);
                //await _context.SaveChangesAsync();

                //TODO: update the jcedetail? Would that have to be pushed to accounting?
                //TODO: generate new jcedetail for the copies?

                return Ok(new ResponseModel<EstdetailDto> { IsSuccess = true, Value = returnItem });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstdetailDto> { IsSuccess = false, Message = "failed to reissue item", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeleteItemAsync([FromBody] EstdetailDto itemToDelete)
        {
            try
            {
                var findItem = await _context.Estdetails.SingleOrDefaultAsync(x => x.EstdetailId == itemToDelete.EstdetailId);
                findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                findItem.UpdatedDateTim = DateTime.Now;
                findItem.IsActive = false;
                _context.Estdetails.Update(findItem);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<EstdetailDto>() { Value = itemToDelete, Message = "Item deleted", IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstdetailDto> { IsSuccess = false, Message = "failed to delete item", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeleteActivityAsync([FromBody] CombinedPOBudgetTreeModel itemToDelete)
        {
            try
            {
                //TODO: in release sort, want to only delete the items in the activity in same header
                //in activity sort, ok to delete all
                var updateBy = User.Identity.Name.Split('@')[0];
                var findActivity = await _context.Estactivities.SingleOrDefaultAsync(x => x.EstactivityId == itemToDelete.Estactivity.EstactivityId);
                var findEstDetails =  _context.Estdetails.Where(x => x.EstactivityId == itemToDelete.Estactivity.EstactivityId && x.EstjcedetailId == null);//estjcedetail id null means the item is not already issued
                await findEstDetails.ExecuteUpdateAsync(s => s.SetProperty(x => x.UpdatedBy, updateBy).SetProperty(x => x.UpdatedDateTim, DateTime.Now).SetProperty(x => x.IsActive, false));
                findActivity.UpdatedBy = User.Identity.Name.Split('@')[0];
                findActivity.UpdatedDateTime = DateTime.Now;
                findActivity.IsActive = false;
                _context.Estactivities.Update(findActivity);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<CombinedPOBudgetTreeModel> { IsSuccess = true, Value = itemToDelete , Message = "Deleted Activity" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<CombinedPOBudgetTreeModel> { IsSuccess = false, Message = "failed to delete activity", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeleteActivityOptionSortAsync([FromBody] CombinedPOBudgetTreeModel itemToDelete)
        {
            try
            {
                //in option sort, want to only delete the items in the activity in same option
                var updateBy = User.Identity.Name.Split('@')[0];
                var findActivity = await _context.Estactivities.SingleOrDefaultAsync(x => x.EstactivityId == itemToDelete.Estactivity.EstactivityId);
                var findEstDetails = _context.Estdetails.Where(x => x.EstactivityId == itemToDelete.Estactivity.EstactivityId && x.EstoptionId == itemToDelete.Estoption.EstoptionId && x.EstjcedetailId == null);//estjcedetail null means the item is not yet issued, don't delete already issued ones
                await findEstDetails.ExecuteUpdateAsync(s => s.SetProperty(x => x.UpdatedBy, updateBy).SetProperty(x => x.UpdatedDateTim, DateTime.Now).SetProperty(x => x.IsActive, false));
                findActivity.UpdatedBy = User.Identity.Name.Split('@')[0];
                findActivity.UpdatedDateTime = DateTime.Now;
                findActivity.IsActive = false;
                _context.Estactivities.Update(findActivity);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<CombinedPOBudgetTreeModel> { IsSuccess = true, Value = itemToDelete, Message = "Deleted Activity" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<CombinedPOBudgetTreeModel> { IsSuccess = false, Message = "failed to delete activity", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeleteOptionAsync([FromBody] EstoptionDto itemToDelete)
        {
            //TODO: if some items issued and some not, need to handle this case
            try
            {
                var findItem = await _context.Estoptions.SingleOrDefaultAsync(x => x.EstoptionId == itemToDelete.EstoptionId);
                findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                findItem.UpdatedDateTime = DateTime.Now;
                findItem.IsActive = false;
                _context.Estoptions.Update(findItem);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<EstoptionDto> { IsSuccess = true, Value = itemToDelete, Message = "Deleted option" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstoptionDto> { IsSuccess = false, Message = "Failed to delete option", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> DeleteHeaderAsync([FromBody] EstheaderDto itemToDelete)
        {
            //TODO: should it deactivate options and items in the header?
            //TODO: don't delete if some items already issued. In that case need to cancel those items first (if not sent to nav, just delete the estdetail, if sent, add an offset entry, etc...)
            try
            {
                var findItem = await _context.Estheaders.SingleOrDefaultAsync(x => x.EstheaderId == itemToDelete.EstheaderId);
                findItem.UpdatedBy = User.Identity.Name.Split('@')[0];
                findItem.UpdatedDateTime = DateTime.Now;
                findItem.IsActive = false;
                _context.Estheaders.Update(findItem);
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<EstheaderDto> { IsSuccess = true, Value = itemToDelete, Message="Deleted Estheader" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<CombinedPOBudgetTreeModel> { IsSuccess = false, Message = "Failed to delete header", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> RefreshCostsAsync(RefreshSupplierAndCostsModel costsModel)
        {
            try
            {
                costsModel.SelectedDate = costsModel.SelectedDate != null ? costsModel.SelectedDate.Value.Date : DateTime.Now.Date;//only the date portion for comparing
                int masterItemId = 0;
                //TODO: actually should it refresh if budget is issued, but track variance in original issued amount?
                List<Estdetail> estDetails = new List<Estdetail>();

                estDetails.AddRange(await _context.Estdetails.Include(x => x.Estoption.Estheader.JobNumberNavigation).Where(x => costsModel.EstOptionIds.Contains((int)x.EstoptionId) && x.IsActive == true && x.Lump != "T" && x.EstjcedetailId == null && x.PodetailId == null).ToListAsync());
                estDetails.AddRange(await _context.Estdetails.Include(x => x.Estoption.Estheader.JobNumberNavigation).Where(x => costsModel.EstDetailIds.Contains((int)x.EstdetailId) && x.IsActive == true && x.Lump != "T" && x.EstjcedetailId == null && x.PodetailId == null).ToListAsync()); // when Refresh Cost is clicked on the EstDetail row, don't update ones marked "Lump Sum", don't update ones already issued, don't update ones with issued pos
                estDetails.AddRange(await _context.Estdetails.Include(x => x.Estoption.Estheader.JobNumberNavigation).Where(x => costsModel.EstActivityIds.Contains((int)x.EstactivityId) && x.IsActive == true && x.Lump != "T" && x.EstjcedetailId == null && x.PodetailId == null).ToListAsync()); // when Refresh Cost is clicked on the EstActivity row
                
                foreach (var estdetail in estDetails)
                {
                    var estActivity = await _context.Estactivities.SingleOrDefaultAsync(x => x.EstactivityId == estdetail.EstactivityId);
                    var supplier = estActivity?.SelectedVendor;

                    if (estdetail.MasterItemsId == null)
                    {
                        //TODO: could item be found by combination of item number, item desc, and item phase?
                        var masterItem = _context.MasterItems.Where(x => x.ItemNumber != null && x.ItemNumber.Trim() == estdetail.ItemNumber && x.ItemDesc == estdetail.ItemDesc).ToList();
                        masterItemId = masterItem.Count == 1 ? masterItem.First().MasterItemId : -1; //setting to -1 when estdetail.MasterItemsId is null
                    }
                    else
                    {
                        masterItemId = (int)estdetail.MasterItemsId;
                    }
                    var subdivisionId = estdetail.Estoption.Estheader.JobNumberNavigation.SubdivisionId;
                    var costs = _context.Costs.Include(x => x.CostsHistories).Where(x => x.MasterItemId == masterItemId && x.SubdivisionId == subdivisionId && x.SubNumber == supplier && x.IsActive == true).FirstOrDefault();
                    var costsHistory = new CostsHistory();
                    if(costs != null)
                    {
                         costsHistory = costs.CostsHistories.Where(x => x.CostExpired != null && costsModel.SelectedDate < x.CostExpired.Value.Date).OrderByDescending(x => x.CostExpired).FirstOrDefault();
                    }
                    
                    if (costs == null) //if no costs found for the given subdivision,checking costs for the default subdivision
                    {
                        costs = _context.Costs.Include(x => x.CostsHistories).Where(x => x.MasterItemId == masterItemId && x.SubdivisionId == 1 && x.SubNumber == supplier && x.IsActive == true).FirstOrDefault();
                        if (costs == null)
                        {       
                            //no cost found for subdivision or for division default, show error
                            estdetail.Price = 0d;
                            estdetail.Amount = 0d;
                            estdetail.Errors =  ErrorString(null, estdetail.JcPhase, estdetail.JcCategory);//errors for no cost, no cost code, no category
                                                                                                           ////todo is jc phase and jc category being passed in?
                        }
                        else
                        {
                            costsHistory = costs.CostsHistories.Where(x => x.CostExpired != null && costsModel.SelectedDate < x.CostExpired.Value.Date).OrderByDescending(x => x.CostExpired).FirstOrDefault();
                        }
                    }
                    if (costs != null)
                    {
                        //TODO: would they ever want to pull in a future cost if they pick a date in the future?
                        switch (costsModel.SelectedDate) // getting costs based on date selected by the user
                        {
                            case DateTime dt when costs.LastCostExpired == null || dt >= costs.LastCostExpired.Value.Date:
                                estdetail.Price = costs.UnitCost;
                                break;
                            case DateTime dt when costs.LastCostExpired != null && dt < costs.LastCostExpired.Value.Date && (costs.LastCost2Expired == null || dt >= costs.LastCost2Expired.Value.Date):
                                estdetail.Price = costs.LastCost1;
                                break;
                            case DateTime dt when costs.LastCost2Expired != null && dt < costs.LastCost2Expired.Value.Date && (costs.LastCost3Expired == null || dt >= costs.LastCost3Expired.Value.Date):
                                estdetail.Price = costs.LastCost2;
                                break;
                            case DateTime dt when costs.LastCost3Expired != null && dt < costs.LastCost3Expired.Value.Date && (costsHistory == null || (costsHistory.CostExpired != null && dt >= costsHistory.CostExpired.Value.Date)):
                                estdetail.Price = costs.LastCost3;
                                break;
                            case DateTime dt when costsHistory != null && dt < costsHistory.CostExpired:
                                estdetail.Price = costsHistory.OldCost;
                                break;
                            default:
                                break;
                        }
                        estdetail.CostsId = costs.CostsId;
                        estdetail.Amount = estdetail.Price * estdetail.OrderQty;
                        estdetail.Errors = ErrorString("not null", estdetail.JcPhase, estdetail.JcCategory);
                        estdetail.UpdatedBy = User.Identity.Name.Split('@')[0];
                        estdetail.UpdatedDateTim = DateTime.Now;
                    }
                    //TODO: need to update the errors on no cost, no supplier, etc
                    _context.Estdetails.Update(estdetail);
                }
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<RefreshSupplierAndCostsModel> { IsSuccess = true, Value = costsModel, Message = "Refreshed costs" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<RefreshSupplierAndCostsModel> { IsSuccess = false, Message = "failed to refresh costs", Value = null });
            }
        }
        [HttpPut]
        public async Task<IActionResult> RefreshCostsPOAsync(RefreshSupplierAndCostsModel costsModel)
        {
            try
            {
                //refreshs costs in estdetail, allows updte costs if estdetail is issued
                //for updating costs from PO, it needs to save in orig committed amount, so as not to affect the budget
                //do not update if po is issued (po details exist)
                costsModel.SelectedDate = costsModel.SelectedDate != null ? costsModel.SelectedDate.Value.Date : DateTime.Now.Date;//only the date portion for comparing
                int masterItemId = 0;

                List<Estdetail> estDetails = new List<Estdetail>();

                estDetails.AddRange(await _context.Estdetails.Include(x => x.Estoption.Estheader.JobNumberNavigation).Where(x => costsModel.EstOptionIds.Contains((int)x.EstoptionId) && x.IsActive == true && x.Lump != "T" && x.PodetailId == null).ToListAsync());
                estDetails.AddRange(await _context.Estdetails.Include(x => x.Estoption.Estheader.JobNumberNavigation).Where(x => costsModel.EstDetailIds.Contains((int)x.EstdetailId) && x.IsActive == true && x.Lump != "T" && x.PodetailId == null).ToListAsync()); // when Refresh Cost is clicked on the EstDetail row, don't update ones marked "Lump Sum", don't update ones already issued, don't update ones with issued pos
                estDetails.AddRange(await _context.Estdetails.Include(x => x.Estoption.Estheader.JobNumberNavigation).Where(x => costsModel.EstActivityIds.Contains((int)x.EstactivityId) && x.IsActive == true && x.Lump != "T" && x.PodetailId == null).ToListAsync()); // when Refresh Cost is clicked on the EstActivity row

                foreach (var estdetail in estDetails)
                {
                    var estActivity = await _context.Estactivities.SingleOrDefaultAsync(x => x.EstactivityId == estdetail.EstactivityId);
                    var supplier = estActivity?.SelectedVendor;
                    
                    if (estdetail.MasterItemsId == null)
                    {
                        //TODO: could item be found by combination of item number, item desc, and item phase?
                        var masterItem = _context.MasterItems.Where(x => x.ItemNumber != null && x.ItemNumber.Trim() == estdetail.ItemNumber && x.ItemDesc == estdetail.ItemDesc).ToList();
                        masterItemId = masterItem.Count == 1 ? masterItem.First().MasterItemId : -1; //setting to -1 when estdetail.MasterItemsId is null
                    }
                    else
                    {
                        masterItemId = (int)estdetail.MasterItemsId;
                    }
                    var subdivisionId = estdetail.Estoption.Estheader.JobNumberNavigation.SubdivisionId;
                    var costs = _context.Costs.Include(x => x.CostsHistories).Where(x => x.MasterItemId == masterItemId && x.SubdivisionId == subdivisionId && x.SubNumber == supplier).FirstOrDefault();
                    var costsHistory = new CostsHistory();
                    if (costs != null)
                    {
                        costsHistory = costs.CostsHistories.Where(x => x.CostExpired != null && costsModel.SelectedDate < x.CostExpired.Value.Date).OrderByDescending(x => x.CostExpired).FirstOrDefault();
                    }

                    if (costs == null) //if no costs found for the given subdivision,checking costs for the default subdivision
                    {
                        costs = _context.Costs.Include(x => x.CostsHistories).Where(x => x.MasterItemId == masterItemId && x.SubdivisionId == 1 && x.SubNumber == supplier).FirstOrDefault();
                        if (costs == null)
                        {
                            //no cost found for subdivision or for division default, show error
                            estdetail.OrgPrice = 0d; //save in org rather than Price so as not to affect budget amounts, only po amounts
                            estdetail.OrigCommittedAmount = 0d;
                            estdetail.OrigCommittedQty = estdetail.OrigCommittedQty ?? estdetail.OrderQty;
                            estdetail.Errors = ErrorString(null, estdetail.JcPhase, estdetail.JcCategory);//errors for no cost, no cost code, no category
                                                                                                          ////todo is jc phase and jc category being passed in?
                        }
                        else
                        {
                            costsHistory = costs.CostsHistories.Where(x => x.CostExpired != null && costsModel.SelectedDate < x.CostExpired.Value.Date).OrderByDescending(x => x.CostExpired).FirstOrDefault();
                        }
                    }
                    if (costs != null)
                    {
                        //2/27/25 saving OrgPrice instead of Price, when refresh costs for PO so it doesn't affect budget amount
                        //TODO: would they ever want to pull in a future cost if they pick a date in the future?
                        switch (costsModel.SelectedDate) // getting costs based on date selected by the user
                        {
                            case DateTime dt when costs.LastCostExpired == null || dt >= costs.LastCostExpired.Value.Date:
                                estdetail.OrgPrice = costs.UnitCost;
                                break;
                            case DateTime dt when costs.LastCostExpired != null && dt < costs.LastCostExpired.Value.Date && (costs.LastCost2Expired == null || dt >= costs.LastCost2Expired.Value.Date):
                                estdetail.OrgPrice = costs.LastCost1;
                                break;
                            case DateTime dt when costs.LastCost2Expired != null && dt < costs.LastCost2Expired.Value.Date && (costs.LastCost3Expired == null || dt >= costs.LastCost3Expired.Value.Date):
                                estdetail.OrgPrice = costs.LastCost2;
                                break;
                            case DateTime dt when costs.LastCost3Expired != null && dt < costs.LastCost3Expired.Value.Date && (costsHistory == null || (costsHistory.CostExpired != null && dt >= costsHistory.CostExpired.Value.Date)):
                                estdetail.OrgPrice = costs.LastCost3;
                                break;
                            case DateTime dt when costsHistory != null && dt < costsHistory.CostExpired:
                                estdetail.OrgPrice = costsHistory.OldCost;
                                break;
                            default:
                                break;
                        }

                        estdetail.CostsId = costs.CostsId;
                        estdetail.OrigCommittedQty = estdetail.OrigCommittedQty ?? estdetail.OrderQty;
                        estdetail.OrigCommittedAmount = estdetail.OrgPrice * estdetail.OrigCommittedQty;
                        estdetail.Errors = ErrorString("not null", estdetail.JcPhase, estdetail.JcCategory);
                    }
                    //TODO: need to update the errors no supplier 
                    _context.Estdetails.Update(estdetail);
                }
                await _context.SaveChangesAsync();
                return Ok(new ResponseModel<RefreshSupplierAndCostsModel> { IsSuccess = true, Value = costsModel, Message = "Refreshed costs" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<RefreshSupplierAndCostsModel> { IsSuccess = false, Message = "failed to refresh costs", Value = null });
            }
        }
        private static string ErrorString(string? costs, string? costCode, string? category)
        {
            string error = "";
            if (string.IsNullOrWhiteSpace(costs))
            {
                error += "4";
                if (string.IsNullOrWhiteSpace(costCode))
                {
                    error += "|6";
                }
                if (string.IsNullOrWhiteSpace(category))
                {
                    error += "|7";
                }
            }
            else
            {
                if (string.IsNullOrWhiteSpace(costCode))
                {
                    error += "6";
                    if (category == null)
                    {
                        error += "|7";
                    }

                }
                else if (string.IsNullOrWhiteSpace(category))
                {
                    error += "7";
                }
            }
            return error;
        }
        [HttpPost]
        public async Task<IActionResult> SendBudgetToBCAsync([FromBody] EstjcedetailDto estjcedetail)
        {
            try
            {
                //Question: send entry if total is 0? 
                //NOTE: ESTJCEDetail combines (adds) multiple estdetails, with same cost code and estoption
                var updatedBy = User.Identity.Name.Split('@')[0];
                //var access_token = await GetBCTokenAsync();
                //var client = new HttpClient();
                //client.DefaultRequestHeaders.Add("Authorization", $"Bearer {access_token}");
                var baseUrl = _configuration.GetSection("BusinessCentral:BaseUrl").Value;
                var companyId = _configuration.GetSection("BusinessCentral:companyId").Value;
                var tenantId = _configuration.GetSection("BusinessCentral:tenantId").Value;
                //Post the budget line
                var findAccount = _context.JobTasks.FirstOrDefault(x => x.JobTaskNo == estjcedetail.Costcode && x.JobNumber == estjcedetail.Estoption.Estheader.JobNumber);
                if(findAccount == null)
                {
                    return Ok(new ResponseModel<EstjcedetailDto>() { Value = estjcedetail, IsSuccess = false, Message = "Missing Job Task. Failed to send to BC." });
                }
                var itemToPost = new BCJobPlanningLine()
                {
                    Job_No = estjcedetail.Estoption.Estheader.JobNumber,
                    Job_Task_No = estjcedetail.Costcode,//cost code
                    Line_Type = "Budget",
                    Type = "G/L Account",
                    No = findAccount.JobPostingGroup,//G/L account number, from job task table
                    Quantity = estjcedetail.EstimateUnits,
                    //Total_Cost = estjcedetail.EstimateAmount, //read only, it calculates it
                    Unit_Cost = estjcedetail.EstimateAmount / estjcedetail.EstimateUnits,
                    //Planned_Delivery_Date = DateTime.Now.ToString("yyyy-MM-dd"),//not used
                    Planning_Date = DateTime.Now.ToString("yyyy-MM-dd"),
                    Link_With_ERP = true,
                    Description = findAccount.Description,
                };
              
                string baseRequestUrl = _configuration.GetSection("BusinessCentral:BudgetBaseUrl").Value;

                var jsonItem = JsonConvert.SerializeObject(itemToPost);
                var stringBody = new StringContent(jsonItem);
                var response = await _downstreamAPI.CallApiForAppAsync("BusinessCentral",
                   options =>
                   {

                       options.HttpMethod = "POST";
                       options.RelativePath = $"companies({companyId})/JobPlanningLinesApi";
                       options.BaseUrl = baseRequestUrl;                      
                       options.CustomizeHttpRequestMessage = message =>
                       {
                           message.Headers.Add("Accept", "*/*");
                           message.Content.Headers.Remove("Content-Type");//TODO: better way to do this so don't have to remove the header and re-add
                           message.Content.Headers.Add("Content-Type", "application/json");
                       };
                   }, stringBody);
                var responseContentString = await response.Content.ReadAsStringAsync();
                var apiLog = new ErpBcApiLog()
                {
                    Method = "POST",
                    Type = "Budget",
                    RequestBody = jsonItem,
                    RequestUrl = $"{baseRequestUrl}companies({companyId})/JobPlanningLinesApi')",
                    ResponseBody = responseContentString,
                    ResponseCode = response.StatusCode.ToString(),
                    CreatedBy = updatedBy
                };
                _context.ErpBcApiLogs.Add(apiLog);
                await _context.SaveChangesAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    //mark exported in the table
                    var findEstJceDetail = _context.Estjcedetails.Where(x => x.EstjcedetailId == estjcedetail.EstjcedetailId);
                    await findEstJceDetail.ExecuteUpdateAsync(s => s.SetProperty(x => x.UpdatedBy, updatedBy).SetProperty(x => x.UpdatedDateTime, DateTime.Now).SetProperty(x => x.EstimateExported, "T").SetProperty(x => x.ExportBc, true));                 
                    return Ok(new ResponseModel<EstjcedetailDto>() { Value = estjcedetail, IsSuccess = true, Message = "Sent Items to BC" });
                }
                else
                {
                    //TODO: test this deserialize, it might be failing
                    var responseErrorMessage = JsonConvert.DeserializeObject<dynamic>(responseContentString);
                    return Ok(new ResponseModel<EstjcedetailDto> { IsSuccess = false, Message = "Send To BC Failed", Error= responseErrorMessage?.message, Value = null });
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<EstjcedetailDto> { IsSuccess = false, Message = "Failed to send to BC", Value = null });
            }
            
        }
        [HttpPost]
        public async Task<IActionResult> SendBudgetToNAVAsync([FromBody] EstjcedetailDto estjcedetail)
        {
            try
            {
                //Question: send entry if total is 0? 
                //NOTE: ESTJCEDetail combines (adds) multiple estdetails, with same cost code and estoption
                var updatedBy = User.Identity.Name.Split('@')[0];

                //Post the budget line
                //TODO: fails if job task not found
                var findAccount = _context.JobTasks.FirstOrDefault(x => x.JobTaskNo == estjcedetail.Costcode && x.JobNumber == estjcedetail.Estoption.Estheader.JobNumber);
                var item = new JobPlanningLine()
                {
                    JobNo = estjcedetail.Estoption.Estheader.JobNumber,
                    JobTaskNo = estjcedetail.Costcode,//cost code
                    LineType = "Budget",
                    Type = "G/L Account",
                    No = findAccount.JobPostingGroup,//G/L account number, from job task table
                    Quantity = (decimal?)estjcedetail.EstimateUnits,
                    TotalCost = (decimal?)estjcedetail.EstimateAmount, 
                    UnitCost = estjcedetail.EstimateUnits != 0 ? (decimal?)(estjcedetail.EstimateAmount / estjcedetail.EstimateUnits) : 0,
                    UnitofMeasureCode = estjcedetail.UnitDesc,
                    //Planned_Delivery_Date = DateTime.Now.ToString("yyyy-MM-dd"),//not used
                    PlanningDate = DateTime.Now.ToString("yyyy-MM-dd"),
                    Description = findAccount.Description,
                };

                byte[] renderedBytes = null;

                using (MemoryStream ms = new MemoryStream())
                {

                    var writer = XmlWriter.Create(ms);
                    writer.WriteStartElement("DataList");

                    writer.WriteStartElement("JobPlanningLineList");
                    writer.WriteElementString("TableID", "1003");
                    writer.WriteElementString("PackageCode", "JOB PLANNING LINES 2");
                    writer.WriteElementString("LineNo", "10000");
                    writer.WriteStartElement("JobPlanningLine");
                    writer.WriteElementString("JobNo", item.JobNo);
                    writer.WriteElementString("JobTaskNo", item.JobTaskNo);
                    writer.WriteElementString("PlanningDate", item.No);
                    writer.WriteElementString("Type", item.Type);
                    writer.WriteElementString("No", item.No);
                    writer.WriteElementString("Description", item.Description);
                    writer.WriteElementString("Quantity", item.Quantity.ToString());
                    writer.WriteElementString("UnitCost", item.UnitCost.ToString());
                    writer.WriteElementString("TotalCost", item.TotalCost.ToString());
                    writer.WriteElementString("LineType", item.LineType);
                    writer.WriteElementString("UnitofMeasureCode", item.UnitofMeasureCode);
                    writer.WriteEndElement();//close planning line
                    writer.WriteEndElement();//close JobPlanningline list
                    writer.WriteEndElement();//close data list

                    writer.Flush();//does this close it

                    renderedBytes = ms.ToArray();
                }

                //mark exported in the table
                var findEstJceDetail = _context.Estjcedetails.Where(x => x.EstjcedetailId == estjcedetail.EstjcedetailId);
                await findEstJceDetail.ExecuteUpdateAsync(s => s.SetProperty(x => x.UpdatedBy, updatedBy).SetProperty(x => x.UpdatedDateTime, DateTime.Now).SetProperty(x => x.EstimateExported, "T").SetProperty(x => x.ExportBc, true));

                return Ok(new ResponseModel<byte[]>() { Value = renderedBytes, IsSuccess = true, Message = "Exported XML" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<byte[]> { IsSuccess = false, Message = "Failed to export XML", Value = null });
            }
        }
        
        [HttpPost]
        public async Task<IActionResult> SendBudgetsToBCAsync([FromBody] List<EstjcedetailDto> estjcedetails)
        {
            try
            {
                string errorMessage = string.Empty;
                var updatedBy = User.Identity.Name.Split('@')[0];
                var access_token = await GetBCTokenAsync();
                //var client = new HttpClient();
                //client.DefaultRequestHeaders.Add("Authorization", $"Bearer {access_token}");
                //var baseUrl = _configuration.GetSection("BusinessCentral:BaseUrl").Value;
                var companyId = _configuration.GetSection("BusinessCentral:companyId").Value;
                var tenantId = _configuration.GetSection("BusinessCentral:tenantId").Value;
                // var bcJobPlanningLines = new List<BCJobPlanningLine>();
                var newPlanningLines = (from a in estjcedetails
                                        join b in _context.JobTasks.Where(x => estjcedetails.Select(y => y.Estoption.Estheader.JobNumber).Contains(x.JobNumber)) on new { jobNum = a.Estoption.Estheader.JobNumber, costCode = a.Costcode } equals new { jobNum = b.JobNumber, costCode = b.JobTaskNo }
                                        select new BCJobPlanningLineModel()
                                        {
                                            Id = a.EstjcedetailId,
                                            PlanningLine = new BCJobPlanningLine()
                                            {
                                                Job_No = a.Estoption.Estheader.JobNumber,
                                                Job_Task_No = a.Costcode,//cost code
                                                Line_Type = "Budget",
                                                Type = "G/L Account",
                                                No = b.JobPostingGroup,//G/L account number, from job task table
                                                Quantity = a.EstimateUnits,//??
                                                Unit_Cost = a.EstimateUnits != 0 ? a.EstimateAmount / a.EstimateUnits : 0,
                                                // Total_Cost = estjcedetail.EstimateAmount, //total cost is read only, need unit cost
                                                Planning_Date = DateTime.Now.ToString("yyyy-MM-dd"),
                                                // Planned_Delivery_Date = DateTime.Now.ToString("yyyy-MM-dd"),
                                                Link_With_ERP = true,
                                                Description = b.Description
                                            }   
                                        }).ToList();
                //TODO: if any missing, send error message
                if (newPlanningLines.Count < estjcedetails.Count)
                {
                    errorMessage = "Some budget items failed to send due to missing job tasks";
                }
                if(newPlanningLines.Count > 0)
                {
                    int sent = 0;
                    int remaining = newPlanningLines.Count;
                    int failedCount = 0;
                    string baseRequestUrl = _configuration.GetSection("BusinessCentral:BudgetBaseUrl").Value;

                    while (remaining > 0)
                    {
                        var linesToSend = newPlanningLines.Skip(sent).Take(100).ToList();
                        var batchRequest = new BCBatchRequest<BCJobPlanningLine>()
                        {
                            requests = linesToSend.Select(x => new Request<BCJobPlanningLine>()
                            {
                                method = "POST",
                                //id = Guid.NewGuid().ToString(),//TODO: use this to match the success ones with the estjcedetail, what about just use estjcedetailid here?
                                id = x.Id.ToString(),//estjcedetailid
                                url = $"companies({companyId})/JobPlanningLinesApi",
                                headers = new ERP.Data.Models.Dto.Headers() { ContentType = "application/json" },
                                body = x.PlanningLine
                            }).ToArray()
                        };
                        //var RequestUri = new Uri($"{baseRequestUrl}$batch");
                        //var res = await client.PostAsJsonAsync($"{baseRequestUrl}$batch", batchRequest);
                        //var stringResp = await res.Content.ReadAsStringAsync();
                        //var response = await res.Content.ReadFromJsonAsync<BCBatchResponseRoot<BCResponsePlanningLine2>>();

                        //var response1 = await _downstreamAPI.PostForAppAsync<BCBatchRequest<BCJobPlanningLine>, BCBatchResponseRoot<BCResponsePlanningLine2>>("BusinessCentral", batchRequest, options => { options.BaseUrl = baseRequestUrl; options.RelativePath = "$batch"; });


                        var jsonItem = JsonConvert.SerializeObject(batchRequest);
                        var stringBody = new StringContent(jsonItem);
                        var response = await _downstreamAPI.CallApiForAppAsync("BusinessCentral",
                           options =>
                           {
                               options.HttpMethod = "POST";
                               options.RelativePath = "$batch";
                               options.BaseUrl = baseRequestUrl;
                               options.CustomizeHttpRequestMessage = message =>
                               {
                                   message.Headers.Add("Accept", "*/*");
                                   message.Headers.Add("Prefer", "odata.continue-on-error");//allows the rest of the requests to be processed if one is error
                                                                                            //message.Headers.Add("OData-Isolation", "snapshot");//this would treat the batch as a transaction, and roll back if any one is error, combined with above it would tell you which one is error
                                   message.Content.Headers.Remove("Content-Type");//TODO: better way to do this so don't have to remove the header and re-add
                                   message.Content.Headers.Add("Content-Type", "application/json");
                               };
                           }, stringBody);
                        var responseString = await response.Content.ReadAsStringAsync();
                        var apiLog = new ErpBcApiLog()
                        {
                            Method = "POST",
                            Type = "Budget",
                            RequestBody = jsonItem,
                            RequestUrl = $"{baseRequestUrl}/JobPlanningLinesApi')",
                            ResponseBody = responseString,
                            ResponseCode = response.StatusCode.ToString(),
                            CreatedBy = updatedBy
                        };
                        var addApiLogs = new List<ErpBcApiLog>() {apiLog};

                        var responseJson = JsonConvert.DeserializeObject<BCBatchResponseRoot<dynamic>>(responseString);
                        
                        foreach (var res in responseJson.responses)
                        {
                            var addLog = new ErpBcApiLog()
                            {
                                Method = "POST",
                                Type = "Budget",
                                RequestBody = JsonConvert.SerializeObject(batchRequest.requests.FirstOrDefault(x => x.id == res.id)?.body),
                                RequestUrl = $"{baseRequestUrl}/JobPlanningLinesApi')",
                                ResponseBody = JsonConvert.SerializeObject(res.body),//This won't work if it's fail response
                                ResponseCode = response.StatusCode.ToString(),
                                CreatedBy = updatedBy
                            };
                            addApiLogs.Add(addLog);
                        }
                        await _context.ErpBcApiLogs.BulkInsertAsync(addApiLogs);

                        //mark exported in the table for the ones that were successful
                        var successResponses = responseJson.responses.Where(x => x.status == 201).Select(x => x.id).ToList();//TODO: check it's 201 being sent for success
                        var findSuccessfulEstJCEDetails = _context.Estjcedetails.Where(x => successResponses.Contains(x.EstjcedetailId.ToString()));
                        await findSuccessfulEstJCEDetails.ExecuteUpdateAsync(s => s.SetProperty(x => x.UpdatedBy, updatedBy).SetProperty(x => x.UpdatedDateTime, DateTime.Now).SetProperty(x => x.EstimateExported, "T").SetProperty(x => x.ExportBc, true));

                        sent += linesToSend.Count;
                        remaining = newPlanningLines.Count - sent;
                        failedCount += (linesToSend.Count - successResponses.Count);

                    }

                    if (failedCount > 0)
                    {
                        errorMessage += $"{failedCount} items failed to send.";
                        return Ok(new ResponseModel<List<EstjcedetailDto>>() { Value = estjcedetails, IsSuccess = false, Message = $"Sent Items to BC.{errorMessage}" });
                    }
                    return Ok(new ResponseModel<List<EstjcedetailDto>>() { Value = estjcedetails, IsSuccess = true, Message = $"Sent Items to BC.{errorMessage}" });
                }
                return Ok(new ResponseModel<List<EstjcedetailDto>>() { Value = estjcedetails, IsSuccess = false, Message = $"{errorMessage} Nothing was sent" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<EstjcedetailDto>> { IsSuccess = false, Message = "Failed to send to BC", Value = null });
            }
        }
        [HttpPost]
        public async Task<IActionResult> SendBudgetsToNAVAsync([FromBody] List<EstjcedetailDto> estjcedetails)
        {
            try
            {
                var updatedBy = User.Identity.Name.Split('@')[0];
                var xmlData = new DataList()
                {
                    PackageCode = "JOB PLANNING LINES 2",
                    TableID = 1004
                };
                
                var newPlanningLines = (from a in estjcedetails
                                        join b in _context.JobTasks.Where(x => estjcedetails.Select(y => y.Estoption.Estheader.JobNumber).Contains(x.JobNumber)) on new { jobNum = a.Estoption.Estheader.JobNumber, costCode = a.Costcode } equals new { jobNum = b.JobNumber, costCode = b.JobTaskNo }
                                        select new JobPlanningLine()
                                        {
                                            JobNo = a.Estoption.Estheader.JobNumber,
                                            JobTaskNo = a.Costcode,//cost code
                                            LineType = "Budget",
                                            //LineType = 0,//0 = Budget
                                            Type = "G/L Account",
                                            No = b.JobPostingGroup,//G/L account number, from job task table
                                            Quantity = (decimal)a.EstimateUnits,//??
                                            UnitCost = a.EstimateUnits != 0 ? (decimal)(a.EstimateAmount / a.EstimateUnits) : 0,
                                            UnitofMeasureCode = a.UnitDesc,
                                            TotalCost = (decimal?)a.EstimateAmount, 
                                            PlanningDate = DateTime.Now.ToString("yyyy-MM-dd"),
                                            Description = b.Description
                                        }).ToList();
                xmlData.JobPlanningLineList = newPlanningLines;
                byte[] renderedBytes = null;
                using (MemoryStream ms = new MemoryStream())
                {

                    var writer = XmlWriter.Create(ms);
                    writer.WriteStartElement("DataList");

                    writer.WriteStartElement("JobPlanningLineList");
                    writer.WriteElementString("TableID", "1003");
                    writer.WriteElementString("PackageCode", "JOB PLANNING LINES 2");
                    int i = 0;
                    foreach(var item in newPlanningLines)
                    {
                        writer.WriteStartElement("JobPlanningLine");
                        writer.WriteElementString("LineNo", (i += 10000).ToString());
                        writer.WriteElementString("JobNo", item.JobNo);
                        writer.WriteElementString("JobTaskNo", item.JobTaskNo);
                        writer.WriteElementString("PlanningDate", item.PlanningDate);
                        writer.WriteElementString("Type", item.Type);
                        writer.WriteElementString("No", item.No);
                        writer.WriteElementString("Description", item.Description);
                        writer.WriteElementString("Quantity", item.Quantity.ToString());
                        writer.WriteElementString("UnitCost", item.UnitCost.ToString());
                        writer.WriteElementString("TotalCost", item.TotalCost.ToString());
                        writer.WriteElementString("UnitofMeasureCode", item.UnitofMeasureCode);
                        writer.WriteElementString("LineType", item.LineType);
                        writer.WriteEndElement();//close planning line
                    }
                    writer.WriteEndElement();//close JobPlanningline list
                    writer.WriteEndElement();//close data list

                    writer.Flush();//does this close it

                    renderedBytes = ms.ToArray();
                }



                //mark exported in the table 
                var estjcedetailIds = estjcedetails.Select(x => x.EstjcedetailId).ToList();
                var findEstJceDetails = _context.Estjcedetails.Where(x => estjcedetailIds.Contains(x.EstjcedetailId));
                await findEstJceDetails.ExecuteUpdateAsync(s => s.SetProperty(x => x.UpdatedBy, updatedBy).SetProperty(x => x.UpdatedDateTime, DateTime.Now).SetProperty(x => x.EstimateExported, "T").SetProperty(x => x.ExportBc, true));

                return Ok(new ResponseModel<byte[]>() { Value = renderedBytes, IsSuccess = true, Message = "Exported XML" });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<byte[]> { IsSuccess = false, Message = "Failed to export XML", Value = null });
            }
        }
        private async Task<string> GetBCTokenAsync()
        {

            var clientId = _configuration.GetSection("BusinessCentral:clientId").Value;
            var clientSecret = _configuration.GetSection("BusinessCentral:clientSecret").Value;
            var tenantId = _configuration.GetSection("BusinessCentral:tenantId").Value;
            var token_url = "https://login.microsoftonline.com/" + tenantId + "/oauth2/v2.0/token";

            var client = new HttpClient();

            var content = new StringContent(
                "grant_type=client_credentials" +
                "&scope=https://api.businesscentral.dynamics.com/.default" +
                "&client_id=" + System.Web.HttpUtility.UrlEncode(clientId) +
                "&client_secret=" + System.Web.HttpUtility.UrlEncode(clientSecret));

            content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");

            var response = await client.PostAsync(token_url, content);
            var tokenresponse = await response.Content.ReadAsStringAsync();
            var access_token_Object = JsonConvert.DeserializeObject<dynamic>(tokenresponse);
            var access_token = access_token_Object.access_token;

            return access_token;
        }

        [HttpGet]
        public async Task<IActionResult> GetEstimateBudgetAsync()
        {
            try
            {
                var returnData = new TileDto();

                var conn = _configuration.GetConnectionString("ERPConnection");

                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT CAST(ISNULL(SUM(d.ESTIMATE_EXPORT_AMT), 0) AS INT) FROM dbo.ESTDETAIL d JOIN dbo.ESTOPTION o ON d.ESTOPTION_ID = o.ESTOPTION_ID JOIN dbo.ESTHEADER h ON o.ESTHEADER_ID = h.ESTHEADER_ID WHERE CAST(d.CreatedDateTime AS DATE) >= CAST(DATEADD(wk, DATEDIFF(wk, 30, GETDATE()), 0) as date) AND d.CreatedDateTime < CAST(DATEADD(wk, DATEDIFF(wk, 30, GETDATE()), 30) AS DATE)";
                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        var totalEstimateBudget = Convert.ToInt32(reader.GetValue(0).ToString());

                        if (totalEstimateBudget >= 10000)
                        {
                            returnData.TotalEstimateBudget = (totalEstimateBudget / 1000D).ToString("0.#") + "K";
                        }
                        else
                        {
                            returnData.TotalEstimateBudget = (totalEstimateBudget / 1000) + "K";
                        }
                    }
                }

                return new OkObjectResult(new ResponseModel<TileDto> { Value = returnData, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Estimate Budget Total"));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetIssuedBudgetAsync()
        {
            try
            {
                var returnData = new TileDto();

                var conn = _configuration.GetConnectionString("ERPConnection");

                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT CAST(ISNULL(SUM(d.AMOUNT), 0) AS INT) FROM dbo.ESTDETAIL d JOIN dbo.ESTOPTION o ON d.ESTOPTION_ID = o.ESTOPTION_ID JOIN dbo.ESTHEADER h ON o.ESTHEADER_ID = h.ESTHEADER_ID WHERE CAST(d.CreatedDateTime AS DATE) >= CAST(DATEADD(wk, DATEDIFF(wk, 30, GETDATE()), 0) as date) AND d.CreatedDateTime < CAST(DATEADD(wk, DATEDIFF(wk, 30, GETDATE()), 30) AS DATE)";
                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        var totalIssuedBudget = Convert.ToInt32(reader.GetValue(0).ToString());

                        if (totalIssuedBudget >= 10000)
                        {
                            returnData.TotalIssuedBudget = (totalIssuedBudget / 1000D).ToString("0.#") + "K";
                        }
                        else
                        {
                            returnData.TotalIssuedBudget = (totalIssuedBudget / 1000) + "K";
                        }
                    }
                }

                return new OkObjectResult(new ResponseModel<TileDto> { Value = returnData, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Estimate Budget Total"));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetPricingAssumptionCostAsync()
        {
            try
            {
                var returnData = new TileDto();
                returnData.HouseCostTiles = new List<CommunityTileDto>();

                var conn = _configuration.GetConnectionString("ERPConnection");

                using (var connection = new SqlConnection(conn))
                {
                    await connection.OpenAsync();

                    var query = "SELECT DISTINCT\r\n\t   x.SubdivisionName,\r\n       x.HousePlanNumber,\r\n\t   x.Building,\r\n       CAST(x.HouseBaseCost AS INT)\r\nFROM\r\n(\r\n    SELECT TOP 10\r\n\t\t   SubdivisionName,\r\n           HousePlanNumber,\r\n           HouseBaseCost,\r\n\t\t   Building\r\n    FROM [dbo].[PricingAssumption]\r\n    WHERE IsDeleted = 0\r\n          AND Backlog = 0\r\n          AND Settled = 0\r\n          AND HousePlanNumber IS NOT NULL\r\n          AND HousePlanNumber <> ''\r\n    ORDER BY CreateDate DESC\r\n) x;\r\n\r\n";
                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        var communityDto = new CommunityTileDto
                        {
                            Community = reader.GetValue(0).ToString(),
                            HousePlan = reader.GetValue(1).ToString(),
                            Building = reader.GetValue(2).ToString()
                        };

                        var totalHoustCost = Convert.ToInt32(reader.GetValue(3).ToString());

                        if (totalHoustCost >= 10000)
                        {
                            communityDto.TotalHouseCost = (totalHoustCost / 1000D).ToString("0.#") + "K";
                        }
                        else
                        {
                            communityDto.TotalHouseCost = (totalHoustCost / 1000) + "K";
                        }

                        returnData.HouseCostTiles.Add(communityDto);
                    }
                }

                return new OkObjectResult(new ResponseModel<TileDto> { Value = returnData, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                throw (new Exception("Error in getting the Estimate Budget Total"));
            }
        }
    }    
}
