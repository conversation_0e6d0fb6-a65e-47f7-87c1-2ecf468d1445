﻿namespace ERP.Data.Models.Dto
{
    public class EmailModel
    {
        public string? Subject { get; set; }

        public string? Body { get; set; }

        public string? FromEmail { get; set; }

        public List<string>? Emails { get; set; }

        public List<FileModel>? Files { get; set; }

        public int? SupplierNumber { get; set; }

        public string? SupplierName { get; set; }

        public string? JobNumber { get; set; }

        public List<ContactDto>? Contacts { get; set; }
    }
}
