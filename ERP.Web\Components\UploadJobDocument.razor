﻿@using ERP.Data.Models;
@*@using Telerik.Documents.SpreadsheetStreaming;
@using DocumentProcessing*@
@inject JobDocumentService DocumentService
@inject SubdivisionService SubdivisionService
@inject NavigationManager NavigationManager
@inject IConfiguration Configuration
@inject IJSRuntime JsInterop

@using ERP.Data.Models.Dto;

<style type="text/css">
    .k-dropzone-hint, .k-form-hint {
    font-family: "Roboto",sans-serif;
    font-size: .688rem;
    }
</style>

<TelerikWindow Modal="true"
Visible="@IsModalVisible"
Width="600px"
Height="450px"
VisibleChanged="@HandleVisibleChanged"
CloseOnOverlayClick="true">
    <WindowTitle>
        <h7 class="page-title" style="font-weight:bold">Add Document</h7>
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@ItemToAdd" OnValidSubmit="@HandleValidAddSubmit">
           @*  <div class="k-form-field">
                <label class="k-label k-form-label">File Name:</label>
                <div class="k-form-field-wrap">
                    <TelerikTextBox @bind-Value="@ItemToAdd.FileName" Id="name" />
                </div>
            </div> *@
            <br />
            @* <br />
            token: @myToken
            <br/>
            scope: @myScope
            <br />
            time : @DateTime.Now
            <br/>
            Save URL: @SaveUrl *@
           @*  <div class="k-form-field">
                <label class="k-label k-form-label">Document Type:</label>
                <div class="k-form-field-wrap">
                    <TelerikDropDownList Data="@AllDocumentTypes"
                    Filterable = "true"
                    FilterOperator="StringFilterOperator.Contains"
                    @bind-Value="@ItemToAdd.DocTypeId"
                    TextField="Doctype1"
                    ValueField="DoctypeId"
                    DefaultText="Select Type"
                    Width="100%">
                        <DropDownListSettings>
                            <DropDownListPopupSettings Height="auto"></DropDownListPopupSettings>
                        </DropDownListSettings>
                    </TelerikDropDownList>
                </div>
            </div><br /> *@
            <div class="k-form-field">
                <label class="k-label k-form-label">Upload:</label>
                <div class="k-form-field-wrap">
                    <TelerikUpload SaveUrl="@SaveUrl"
                    SaveField="files"
                    RemoveUrl="@RemoveUrl"
                    MinFileSize="1"
                    AllowedExtensions="@( new List<string>() { ".pdf", ".docx", ".jpg", ".png" } )"
                    OnSelect="@OnSelect"
                    OnUpload="@OnUpload"
                    OnSuccess="@OnSuccess"
                    OnError="@OnError"
                    OnCancel="@OnCancelHandler"
                    OnRemove="@OnRemoveHandler"

                    AutoUpload="false">
                    </TelerikUpload>
                    <div class="k-form-hint">Accepted files:&nbsp;&nbsp;<strong>PDF, DOCX, JPG, PNG</strong></div>
                </div>
            </div><br />
            <div class="k-form-field">
                <label class="k-label k-form-label">Send Email Notification</label>
                <div class="k-form-field-wrap">
                    <TelerikMultiSelect Data="UsersData"
                    Filterable="true"
                    FilterOperator="StringFilterOperator.Contains"
                    @bind-Value=@SelectedUserEmails
                    TextField="FullName"
                    ValueField="EmailAddress"
                    Placeholder="Select Users to notify"
                    AutoClose="false"
                    Width="100%" />
                </div>
            </div>
            <br />
            <button type="button" @onclick="CancelAddItem" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-solid-base">
                <span class="telerik-blazor k-button-icon k-icon k-svg-icon k-svg-i-cancel" aria-hidden="true">
                    <!--!-->
                    <svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <!--!-->
                        <!--!-->
                        <path d="M256 32c-50.3 0-96.8 16.6-134.1 44.6-17.2 12.8-32.4 28.1-45.3 45.3C48.6 159.2 32 205.7 32 256c0 123.7 100.3 224 224 224 50.3 0 96.8-16.6 134.1-44.6 17.2-12.8 32.4-28.1 45.3-45.3 28-37.4 44.6-83.8 44.6-134.1 0-123.7-100.3-224-224-224zm0 384c-88.2 0-160-71.8-160-160 0-32.6 9.8-62.9 26.6-88.2l221.6 221.6C318.9 406.2 288.6 416 256 416zm133.4-71.8L167.8 122.6C193.1 105.8 223.4 96 256 96c88.2 0 160 71.8 160 160 0 32.6-9.8 62.9-26.6 88.2z"></path><!--!-->
                    </svg><!--!-->
                    <!--!-->
                </span> Cancel
            </button>
            <div style=@submittingStyle>Adding item. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {
    public bool IsModalVisible { get; set; }
    public DocumentUploadModel ItemToAdd { get; set; } = new DocumentUploadModel();

    [Parameter]
    public string? JobNumber { get; set; }

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    public List<DoctypeDto>? AllDocumentTypes;
    public string? BaseUrl { get; set; }
    public string? SaveUrl { get; set; }
    public List<UserDto>? UsersData { get; set; }
    public List<string>? SelectedUserEmails { get; set; }
    public string RemoveUrl = "api/jobdocument/remove";
    private string submittingStyle = "display:none";

    public string? myToken { get; set; }
    public string? myScope { get; set; }
    [Parameter]
    public EventCallback<ResponseModel> HandleAddSubmit { get; set; }

    Dictionary<string, bool> FilesValidationInfo { get; set; } = new Dictionary<string, bool>();

    public async Task Show()
    {
        BaseUrl = Configuration.GetSection("DownstreamApi").GetSection("BaseUrl").Value;
        SaveUrl = BaseUrl + "api/JobDocument/UploadJobDocument";
        IsModalVisible = true;
        ItemToAdd.JobNumber = JobNumber;
        AllDocumentTypes = (await DocumentService.GetDocumentTypesAsync()).Value;//
        UsersData = (await SubdivisionService.GetUserContactsAsync()).Value;
        // myToken = (await DocumentService.GetTokenAsync()).Value;
        // myScope = (await DocumentService.GetScopeAsync()).Value;
        StateHasChanged();
    }

    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        submittingStyle = "display:none";
    }

    async void CancelAddItem()
    {
        IsModalVisible = false;
        FilesValidationInfo.Clear();
        await HandleAddSubmit.InvokeAsync(new ResponseModel { IsSuccess = false, Message = "Nothing to upload" });
    }

    public async Task Hide()
    {
        IsModalVisible = false;
    }

    public async Task HandleVisibleChanged()
    {
        IsModalVisible = !IsModalVisible;

        if (IsModalVisible == false)
        {
            FilesValidationInfo.Clear();
            await HandleAddSubmit.InvokeAsync(new ResponseModel { IsSuccess = false, Message = "Nothing to upload" });//update the documents list if they close the window
        }
    }

    void OnCancelHandler(UploadCancelEventArgs e)
    {
        RemoveFailedFilesFromList(e.Files);
    }

    void OnRemoveHandler(UploadEventArgs e)
    {
        RemoveFailedFilesFromList(e.Files);
    }

    public void OnSelect(UploadSelectEventArgs args)
    {
        foreach (var file in args.Files)
        {
            if(file.Size == 0)
            {
                //file size 0 can happen on drag from sharepoint, or on drag from inside zip folder
                Dialogs.AlertAsync("The file you are uploading has no content.");
            }
            if (!FilesValidationInfo.Keys.Contains(file.Id))
            {
                // nothing is assumed to be valid until the server returns an OK
                FilesValidationInfo.Add(file.Id, false);
            }
        }
    }

    public async Task OnUpload(UploadEventArgs args)
    {
        var token = await DocumentService.GetTokenAsync();
        //  var scope = await DocumentService.GetScopeAsync();
        // myScope = scope.Value;
        // myToken = token.Value;
        StateHasChanged();
        //args.RequestData.Add("FileName", args.Files[0].Name);//this was wrong if multiple files. getting the file name from the files instead
        args.RequestData.Add("DocTypeId", ItemToAdd.DocTypeId);//not actually used here now, input removed 2/19 
        args.RequestData.Add("JobNumber", ItemToAdd.JobNumber);
        args.RequestData.Add("EmailNotification", SelectedUserEmails != null && SelectedUserEmails.Count() != 0);
        if (SelectedUserEmails != null && SelectedUserEmails.Count() != 0)
        {
            args.RequestData.Add("EmailRecipients", SelectedUserEmails);
            args.RequestData.Add("EmailSubject", "Document added to Job Notification");
            args.RequestData.Add("EmailBody", $"A document of type was uploaded to Jobnumber: {JobNumber}");
        }
        var tokenObj = new
        {
            Bearer = $"{token}"
        };
        var tokenList = new List<string>() { $"bearer {token}" };
        args.RequestHeaders.Add("Authorization", $"bearer {token.Value}");
        //TODO: request size for large files
    }

    public async void OnSuccess(UploadSuccessEventArgs args)
    {
        if (args.Operation == UploadOperationType.Upload)
        {
            if (FilesValidationInfo.Keys.Contains(args.Files[0].Id))
            {
                // only when the server got the file, saved it and confirmed it is OK do we update client validation
                FilesValidationInfo[args.Files[0].Id] = true;
            }
            await HandleAddSubmit.InvokeAsync(new ResponseModel { IsSuccess = true, Message = "Upload is successful" });
        }
        else
        {
            RemoveFailedFilesFromList(args.Files);
            await HandleAddSubmit.InvokeAsync(new ResponseModel { IsSuccess = false, Message = "Failed to upload. If problem persists, <NAME_EMAIL>" });
        }

        // if (UpdateValidationModel() == true)
        // {
            
        // }
        // else
        // {
           
        // }
        //FilesValidationInfo.Clear();

    }

    public async void OnError(UploadErrorEventArgs args)
    {
        RemoveFailedFilesFromList(args.Files);
        await HandleAddSubmit.InvokeAsync(new ResponseModel { IsSuccess = false, Message = $"{args.Request.ResponseText}-{args.Request.StatusText}-{args.Request.ResponseType.ToString()}" });
    }

    void RemoveFailedFilesFromList(List<UploadFileInfo> files)
    {
        foreach (var file in files)
        {
            if (FilesValidationInfo.Keys.Contains(file.Id))
            {
                FilesValidationInfo.Remove(file.Id);
            }
        }
    }

    // bool UpdateValidationModel()
    // {
    //     bool areAllUploadedFilesValid = false;

    //     if (FilesValidationInfo.Keys.Count > 0 &&
    //         !FilesValidationInfo.Values.Contains(false))
    //     {
    //         areAllUploadedFilesValid = true;
    //     }

    //     return areAllUploadedFilesValid;
    // }
}
