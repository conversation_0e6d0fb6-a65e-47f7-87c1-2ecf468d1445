﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class SupplierCommunicationAttachDto : IMapFrom<SupplierCommunicationAttach>
{
    public int SubAttachId { get; set; }

    public int SubCommId { get; set; }

    public string? AttachName { get; set; }

    public string? AttachDriveLink { get; set; }

    public string? Notes { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool IsActive { get; set; }

    // public byte[]? RecordTimeStamp { get; set; }

    // public SupplierCommunicationDto? SubComm { get; set; } 
}
