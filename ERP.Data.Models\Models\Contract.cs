﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Contract
{
    public int ContractId { get; set; }

    public string? SelectedLotId { get; set; }

    public int? SelectedFloorplanId { get; set; }

    public int? TitleCompanyId { get; set; }

    public int? ResidencyTypeId { get; set; }

    public int? FinancingContingencyTypeId { get; set; }

    public int? OriginalLotStatusId { get; set; }

    public int? DesignCenterDays { get; set; }

    public int? FinancingContingencyDays { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? DesignCenterDate { get; set; }

    public DateTime? DesignCenterRatifiedDate { get; set; }

    public DateTime? SettlementDate { get; set; }

    public DateTime? ExpectedSettlementDate { get; set; }

    public decimal? FinalBasePrice { get; set; }

    public decimal ClosingCostWalker { get; set; }

    public decimal ClosingCostIcm { get; set; }

    public decimal ClosingCostOther { get; set; }

    public string Name { get; set; } = null!;

    public string InternalNotes { get; set; } = null!;

    public string TitleCompanyName { get; set; } = null!;

    public string TitleCompanyRepresentativeName { get; set; } = null!;

    public string TitleAddress { get; set; } = null!;

    public string TitleCity { get; set; } = null!;

    public string TitleState { get; set; } = null!;

    public string TitleZip { get; set; } = null!;

    public string TitleEmail { get; set; } = null!;

    public string TitlePhone { get; set; } = null!;

    public string? TitleOfficePhone { get; set; }

    public virtual ICollection<Buyer> Buyers { get; set; } = new List<Buyer>();

    public virtual Cancellation? Cancellation { get; set; }

    public virtual ContractAdmin? ContractAdmin { get; set; }

    public virtual ICollection<Envelope> Envelopes { get; set; } = new List<Envelope>();

    public virtual LotStatus? OriginalLotStatus { get; set; }

    public virtual PhasePlan? SelectedFloorplan { get; set; }

    public virtual Job? SelectedLot { get; set; }

    public virtual ICollection<TbBuiltOption> BuiltOptions { get; set; } = new List<TbBuiltOption>();
}
