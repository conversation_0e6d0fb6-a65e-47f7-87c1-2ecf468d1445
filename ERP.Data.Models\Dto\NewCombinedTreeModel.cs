﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public record NewCombinedTreeModel
    {
        //TODO: make an interface instead
        public bool? IsDummyForSearch { get; set; }//trying hack to keep treelevels expandable on search
        public Guid Id { get; set; }
        public NewCombinedTreeModel? Parent { get; set; }
        public List<NewCombinedTreeModel>? Children { get; set; }
        public SubdivisionDto? Subdivision { get; set; }
        public PhasePlanDto? PhasePlan { get; set; }
        public AvailablePlanOptionDto? AvailablePlanOption { get; set; }
        public bool? IsShowing { get; set; }
        public int DisplayRowIndex { get; set; }
        public string? DragClue { get; set; }
        public int? PlanId { get; set; }
        public int? WorksheetPlanId { get; set; }
        public int? PhasePlanId { get; set; }
        public int? SubdivisionId { get; set; }
        public int? OptionId { get; set; }
        public bool? IsBaseHouse { get; set; }
        public int? AvailablePlanOptionId { get; set; }
        public int? ActivityId { get; set; }
        public string? OptionName { get; set; }
        public string? OptionCode { get; set; }
        public string? PlanName { get; set; }
        public string? PlanNumber { get; set; }
        public string? SubdivisionName { get; set; }
        public string? ActivityName { get; set; }
        public double? PercentOfTotal { get; set; }
        public Guid? ParentId { get; set; }
        public bool HasChildren { get; set; }
        public string? Vendor { get; set; }
        public decimal? SellPrice { get; set; }
        public decimal? Cost { get; set; }
        public int? Errors { get; set; }
        public string? ErrorReason { get; set; }
        public int? Warnings { get; set; }
        public string? WarningReason { get; set; }
        public bool LumpSum { get; set; }
        public decimal? Markup { get; set; }
        public int? MarkupType { get; set; }
        public decimal? MarketValue { get; set; }
        public double? MarginPercent { get; set; }
        public double? MarkupPercent { get; set; }
        public double? Margin { get; set; }
        public DateTime? PriceDate { get; set; }
        public bool? IsActive { get; set; }
    }
}
