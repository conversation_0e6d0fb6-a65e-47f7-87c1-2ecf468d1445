﻿@page "/suppliermessages"
@using ERP.Data.Models.Dto
@using ERP.Web.Components
@using ERP.Data.Models
@attribute [Authorize(Roles = "Admin, Purchasing")]
@inject SupplierMessagesService SupplierMessagesService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider

<style>
    /* Gutter */
    .row > * {
        padding-right: calc(var(--bs-gutter-x) * .10);
        padding-left: calc(var(--bs-gutter-x) * .10);
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
    }
</style>

<PageTitle>Suppliers &amp; Messages | Setup</PageTitle>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Supplier Messages</h7>
                </div>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item"><a href="/suppliers">Suppliers</a></li>
            <li class="breadcrumb-item active">Supplier Messages</li>
        </ol>

        <div class="col-lg-3">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Suppliers</h7>
                </div>
            </div>
            @if (Suppliers == null)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingSuppliers />
            }

            else
            {
                <TelerikGrid Data=@Suppliers
                         ScrollMode="@GridScrollMode.Virtual"
                         FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                         Height="1000px" RowHeight="60" PageSize="20"
                         Sortable="true"
                         Resizable="true"
                         Groupable="false"
                         OnRowClick="@OnSupplierRowClickHandler"
                         SelectionMode="GridSelectionMode.Single"
                         EditMode="@GridEditMode.Popup"
                         ConfirmDelete="true"
                         @ref="@SupplierGridRef">                  
                    <GridColumns>
                        <GridColumn Field="SubName" Title="Supplier Name" />
                        <GridColumn Field="ShortName" Title="Supplier Short Name" />
                    </GridColumns>
                    <GridSettings>
                        <GridPopupEditSettings Width="400px"
                                           Height="350px"
                                           Title="Suppliers">
                        </GridPopupEditSettings>
                    </GridSettings>
                    <GridToolBarTemplate>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                </TelerikGrid>

            }
        </div>
        <div class="col-lg-6">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Supplier Messages for: @SelectedSupplier?.SubName</h7>
                </div>
            </div>
            @if (SupplierMessages == null)
            {
                <p><em>Select a supplier to see supplier messages</em></p>
                <div style=@loadingSupplierMessagesStyle>Loading...</div>
                 <TelerikLoader Size="@ThemeConstants.Loader.Size.Large" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingSupplier />
            }
            else
            {
                <TelerikGrid Data=@SupplierMessages
                         FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                         Height="1000px" RowHeight="60" PageSize="20"
                         Sortable="true"
                         Resizable="true"
                         Groupable="false"
                         SelectionMode="GridSelectionMode.Single"
                         EditMode="@GridEditMode.Inline"
                         ConfirmDelete="true"
                         @ref="@SupplierMessagesGridRef"
                         OnRowDoubleClick="@OnRowDoubleClickHandler">
                <GridColumns>
                        <GridColumn Field="MessageSubject" Title="Subject" Editable="false" Groupable="true" Width="150px" FilterMenuType="FilterMenuType.CheckBoxList" />
                        <GridColumn Field="Message" Title="Message" Editable="false" Groupable="true" Width="150px" FilterMenuType="FilterMenuType.CheckBoxList" OnCellRender="@OnCellRenderHandler">
                            <Template>
                                @{
                                    var item = context as SupplierCommunicationDto;
                                    if (item?.Message != null)
                                    {
                                        @((MarkupString)item.Message)
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Field="SendFrom" Title="Send From" Editable="false" Groupable="true" Width="150px" FilterMenuType="FilterMenuType.CheckBoxList" />
                        <GridColumn Field="SendTo" Title="Send To" Editable="false" Groupable="true" Width="150px" FilterMenuType="FilterMenuType.CheckBoxList" OnCellRender="@OnCellRenderHandler" />
                        <GridColumn Field="JobNumber" Title="Job Number" Editable="false" Groupable="true" Width="150px" FilterMenuType="FilterMenuType.CheckBoxList" />
                </GridColumns>
                    <GridToolBarTemplate>
                        <GridCommandButton Command="MyToolbarCommand" Icon="@FontIcon.Plus" OnClick="@ShowAddMessage" Class="k-button-add">Add Message</GridCommandButton>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
            </TelerikGrid>                
            }
            
        </div>
    </div>
</div>

<style>
    /* template */
    div.custom-ellipsis,
    /* OnCellRender */
    .k-grid td.custom-ellipsis,
    /* OnRowRender */
    .k-grid tr.custom-ellipsis .k-table-td {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>

<TelerikWindow @bind-Visible="@WindowIsVisible" Modal="true">
    <WindowTitle>
        <strong>Details for @SupplierCommunication.MessageSubject</strong>
    </WindowTitle>
    <WindowContent>
        @SupplierCommunication.Message
        <br />
        <br />
        Sent to: @SupplierCommunication.SendTo
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Minimize"></WindowAction>
        <WindowAction Name="Maximize"></WindowAction>
        <WindowAction Name="Close"></WindowAction>
    </WindowActions>
</TelerikWindow>

<AddMessage @ref="AddMessageModal" EmailMessage="@EmailMessage" HandleSubmit="@HandleAddMessageSubmit"></AddMessage>

@code {
    private bool WindowIsVisible { get; set; }
    private List<SupplierDto>? Suppliers;
    private TelerikGrid<SupplierDto> SupplierGridRef { get; set; }
    private TelerikGrid<SupplierCommunicationDto> SupplierMessagesGridRef { get; set; }
    public List<SubdivisionDto>? SubdivisionData { get; set; }
    public List<SupplierCommunicationDto>? SupplierMessages { get; set; }
    public SubdivisionDto? SelectedSubdivision { get; set; }
    public SupplierDto SelectedSupplier { get; set; }
    protected SupplierDetailsComponent? SupplierDetailsModal { get; set; }
    public int SelectedSupplierNum { get; set; } = 0;
    private string loadingSubdivisionStyle = "display:none";
    private string loadingSupplierMessagesStyle = "display:none";
    public bool IsLoadingSuppliers { get; set; } = false;
    public bool IsLoadingSubivision { get; set; } = false;
    public bool IsLoadingSupplier { get; set; } = false;
    private SupplierCommunicationDto SupplierCommunication { get; set; } = new SupplierCommunicationDto();
    public EmailModel? EmailMessage { get; set; } = new EmailModel();
    public Components.AddMessage? AddMessageModal { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        IsLoadingSuppliers = true;
        Suppliers = (await SupplierMessagesService.GetSuppliersAsync()).Value;
        IsLoadingSuppliers = false;
        loadingSubdivisionStyle = "";
        IsLoadingSubivision = true;
        loadingSubdivisionStyle = "display:none";
        IsLoadingSubivision = false;
    }


    protected async Task OnSupplierRowClickHandler(GridRowClickEventArgs args)
    {
        var supplier = args.Item as SupplierDto;
        SelectedSupplier = supplier;
        SupplierMessages = null;
        loadingSupplierMessagesStyle = "";
        IsLoadingSupplier = true;
        if (SelectedSupplier != null && SelectedSupplier.SubNumber != null)
        {
            SupplierMessages = (await SupplierMessagesService.GetSupplilerMessagesAsync((int)SelectedSupplier.SubNumber)).Value;
        }
        loadingSupplierMessagesStyle = "display:none";
        IsLoadingSupplier = false;
    }

    private void ShowSuccessOrErrorNotification(string message, bool isSuccess)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = isSuccess ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }

    // apply ellipsis to specific columns
    private void OnCellRenderHandler(GridCellRenderEventArgs args)
    {
        args.Class = "custom-ellipsis";
    }

    private void OnRowDoubleClickHandler(GridRowClickEventArgs args)
    {
        SupplierCommunication = args.Item as SupplierCommunicationDto;

        WindowIsVisible = !WindowIsVisible;
    }

    public async Task ShowAddMessage()
    {
        if (EmailMessage != null)
        {
            var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            EmailMessage.FromEmail = user.User.Identity.Name;
            EmailMessage.SupplierNumber = SelectedSupplier.SubNumber;
            var contacts = await SupplierMessagesService.GetSupplilerContactsAsync(SelectedSupplier.SubNumber);
            EmailMessage.Contacts = new List<ContactDto>();
            foreach (var contact in contacts.Value)
            {
                EmailMessage.Contacts.Add(contact.Contact);
            }
            
            if (AddMessageModal != null)
            {
                await AddMessageModal.Show();
            }
        }

        await AddMessageModal.Show();
    }

    public async Task HandleAddMessageSubmit(ResponseModel<EmailModel> response)
    {
        await AddMessageModal.Hide(); 

        //refresh the messages
        var getMessages = await SupplierMessagesService.GetSupplilerMessagesAsync((int)SelectedSupplier.SubNumber);
        SupplierMessages = getMessages.Value;
    }
}
