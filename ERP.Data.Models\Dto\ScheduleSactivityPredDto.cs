﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace ERP.Data.Models;

public class ScheduleSactivityPredDto : IMapFrom<ScheduleSactivityPred>
{
    public int ScheduleAid { get; set; }

    public int PredSactivityId { get; set; }

    public int? PredScheduleAid { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public SactivityDto? PredSactivity { get; set; }

    public ScheduleSactivityDto? ScheduleA { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<ScheduleSactivityPredDto, ScheduleSactivityPred>().ReverseMap();
    }
}
