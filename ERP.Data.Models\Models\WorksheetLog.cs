﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class WorksheetLog
{
    public int WorksheetLogId { get; set; }

    public int WorksheetId { get; set; }

    public string? LogType { get; set; }

    public int? PlanOptId { get; set; }

    public string? Activity { get; set; }

    public string? PhaseCode { get; set; }

    public string? ItemNumber { get; set; }

    public string? ItemDesc { get; set; }

    public int? ErrorCode { get; set; }

    public string? ErrorDesc { get; set; }

    public int? WarningCode { get; set; }

    public string? WarningDesc { get; set; }

    public string? UserStamp { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;
}
