﻿using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using NLog;
using System.Diagnostics;
using System.Text;
using Telerik.Blazor.Components.FileSelect;
using Microsoft.Identity.Abstractions;
using ERP.Data.Models.ExtensionMethods;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class ScheduleStartPackageService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public ScheduleStartPackageService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }
        
        public async Task<ResponseModel<List<ScheStartPackageDto>>> GetPackageNamesBySubdivisionIdAsync(int subdivisionId)
        {
            var packages = new List<ScheStartPackageDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedulestartpackage/packagenamesbysubdivisionid/{subdivisionId}");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<ScheStartPackageDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<ScheStartPackageDto>>() { Value = packages, IsSuccess = false, Message = "Failed to get Package Names by Subdivision " };
        }
        public async Task<ResponseModel<ScheStartPackageDto>> AddPackageNameAsync(ScheStartPackageDto packageNameToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<ScheStartPackageDto, ResponseModel<ScheStartPackageDto>>(
                    "DownstreamApi", packageNameToAdd,
                    options => {
                        options.RelativePath = "api/schedulestartpackage/addpackagename/";                        
                    });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<ScheStartPackageDto>() { Value = null, IsSuccess = false, Message = "Failed to Add Package Name" };
        }
        public async Task<ResponseModel<ScheStartPackageDto>> UpdatePackageNameAsync(ScheStartPackageDto packageNameToUpdate)
        {
            try
            {
                var response = await _downstreamAPI.PutForUserAsync<ScheStartPackageDto, ResponseModel<ScheStartPackageDto>>(
                    "DownstreamApi", packageNameToUpdate,
                    options => {
                        options.RelativePath = "api/schedulestartpackage/updatepackagename/";
                    });
                return new ResponseModel<ScheStartPackageDto>() { Value = packageNameToUpdate, IsSuccess = true, Message = "Successfully updated Package Name" };

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<ScheStartPackageDto>() { Value = null, IsSuccess = false, Message = "Failed to update Package Name" };
        }
        public async Task<ResponseModel<ScheStartPackageDto>> DeletePackageNameAsync(ScheStartPackageDto packageNameToDelete)
        {

            try
            {
                var response = await _downstreamAPI.PutForUserAsync<ScheStartPackageDto, ResponseModel<ScheStartPackageDto>>(
                    "DownstreamApi", packageNameToDelete,
                    options => {
                        options.RelativePath = "api/schedulestartpackage/deletepackagename/";
                    });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<ScheStartPackageDto>() { Value = null, IsSuccess = false, Message = "Failed to delete Package Name" };
        }
        public async Task<ResponseModel<List<SchePackageItemDto>>> GetPackageItemsAsync(int packageId)
        {
            var items = new List<SchePackageItemDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedulestartpackage/packageitems/{packageId}");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<SchePackageItemDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<SchePackageItemDto>>() { Value = items, IsSuccess = false, Message = "Failed to get Package Items" };
        }
        public async Task<ResponseModel<SchePackageItemDto>> AddPackageItemAsync(SchePackageItemDto packageItemToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<SchePackageItemDto, ResponseModel<SchePackageItemDto>>(
                    "DownstreamApi", packageItemToAdd,
                    options => {
                        options.RelativePath = "api/schedulestartpackage/addpackageitem/";
                    });
                return response;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<SchePackageItemDto>() { Value = null, IsSuccess = false, Message = "Failed to Add Package Item" };
        }
        public async Task<ResponseModel<SchePackageItemDto>> UpdatePackageItemAsync(SchePackageItemDto packageItemToUpdate)
        {
            var responsePlan = new SchePackageItemDto();
            try
            {
                responsePlan = await _downstreamAPI.PutForUserAsync<SchePackageItemDto, SchePackageItemDto>(
                    "DownstreamApi", packageItemToUpdate,
                    options => {
                        options.RelativePath = "api/schedulestartpackage/updatepackageitem/";

                    });
                return new ResponseModel<SchePackageItemDto>() { Value = packageItemToUpdate, IsSuccess = true, Message = "Successfully updated Package Item" };

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<SchePackageItemDto>() { Value = null, IsSuccess = false, Message = "Failed to update Package Item" };
        }
        public async Task<ResponseModel<SchePackageItemDto>> DeletePackageItemAsync(SchePackageItemDto packageItemToDelete)
        {

            try
            {
                var response = await _downstreamAPI.PutForUserAsync<SchePackageItemDto, ResponseModel<SchePackageItemDto>>(
                    "DownstreamApi", packageItemToDelete,
                    options => {
                        options.RelativePath = "api/schedulestartpackage/deletepackageitem/";
                    });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<SchePackageItemDto>() { Value = null, IsSuccess = false, Message = "Failed to delete Package Item" };
        }
        public async Task<ResponseModel<List<SchePackageAssignmentDto>>> GetPackageItemAssignmentAsync(int packageItemId)
        {
            var itemAssignment = new List<SchePackageAssignmentDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedulestartpackage/packageitemassignment/{packageItemId}");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<SchePackageAssignmentDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<SchePackageAssignmentDto>>() { Value = itemAssignment, IsSuccess = false, Message = "Failed to get Package Item Assignment" };
        }
        public async Task<ResponseModel<List<SchePackageAssignmentDto>>> GetAllPackageItemAssignmentAsync()
        {
            var itemAssignment = new List<SchePackageAssignmentDto>();
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/schedulestartpackage/allpackageitemassignment/");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<SchePackageAssignmentDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<List<SchePackageAssignmentDto>>() { Value = itemAssignment, IsSuccess = false, Message = "Failed to get All Package Item Assignment" };
        }

       
    }
}
