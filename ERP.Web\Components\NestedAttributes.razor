﻿@inject AttributeService AttributeService
@using ERP.Data.Models
@using Telerik.DataSource.Extensions
@inject AttributeItemPickService AttributeItemPickService

<style>
    .k-list-item {
        border-bottom: 1px solid #cccccc;
    }
</style>

<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

@if (AttributeItemsData == null)
{
    <p><em>Loading...</em></p>
    <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingOptions />
}
else
{
    <TelerikGrid Data="@AttributeItemsData"
                 SelectionMode="GridSelectionMode.Single"
                 EditMode="@GridEditMode.Popup"
                 @ref="@AttributeItemDataRef"
                 OnDelete="@OnDeleteGroupHandler">
        <GridToolBarTemplate>
            <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Item</GridCommandButton>
            <GridSearchBox DebounceDelay="200"></GridSearchBox>
        </GridToolBarTemplate>
        <GridColumns>
            <GridColumn Field="Description" Editable="true" Width="400px" />
            <GridColumn Field="IsActive" Title="Is Active?" Editable="false" Width="25" />
            <GridColumn Field="AttributeGroupId" Visible="false"></GridColumn>
            <GridCommandColumn>
                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true">Update</GridCommandButton>
                <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                <GridCommandButton Command="Reactivate" Icon="@FontIcon.Link" ShowInEdit="false" OnClick="@ReactiveClickHandler" Title="Reactivate"></GridCommandButton>
                <GridCommandButton Command="Relink" Icon="@FontIcon.Hand" ShowInEdit="false" OnClick="@RelinkClickHandler" Title="Relink"></GridCommandButton>
            </GridCommandColumn>
        </GridColumns>
        <GridSettings>
            <GridPopupEditSettings Width="500px"></GridPopupEditSettings>
            <GridPopupEditFormSettings Context="FormContext">
                <FormTemplate>
                    @{
                        EditItem = FormContext.Item as MasterAttributeItemDto;

                        <TelerikForm Model="@EditItem"
                                     OnValidSubmit="@OnValidSubmit">
                            <FormItems>
                                <FormItem>
                                    <Template>
                                        @{
                                            if (EditItem.AttributeItemId == 0)
                                            {
                                                <label for="position">Description</label>
                                                <TelerikComboBox Data="@ComboList"
                                                                 TextField="@nameof(MasterAttributeItemDto.Description)"
                                                                 ValueField="@nameof(MasterAttributeItemDto.Description)"
                                                                 @bind-Value="@SelectedValue"
                                                                 AllowCustom="true"
                                                                 Filterable="true"
                                                                 FilterOperator="@StringFilterOperator.Contains"
                                                                 Placeholder="Select an item or type your own"
                                                                 OnChange="@((object value) => AddAttributeOnTheFly(value))">
                                                    <ComboBoxSettings>
                                                        <ComboBoxPopupSettings Width="400px" />
                                                    </ComboBoxSettings>
                                                </TelerikComboBox>
                                            }
                                            else
                                            {
                                                <FormItem Field="Description" LabelText="Description"></FormItem>
                                            }
                                        }
                                    </Template>
                                </FormItem>
                            </FormItems>
                            <FormButtons>
                                <TelerikButton Icon="@nameof(FontIcon.Save)" Class="k-button-success">Save</TelerikButton>
                                <TelerikButton Icon="@nameof(FontIcon.Cancel)" Class="k-button-danger" ButtonType="@ButtonType.Button" OnClick="@OnCancel">Cancel</TelerikButton>
                            </FormButtons>
                        </TelerikForm>
                    }
                </FormTemplate>
            </GridPopupEditFormSettings>
        </GridSettings>
    </TelerikGrid>
}


@code {

    /// <summary>
    /// Properties
    /// </summary>
    [Parameter] public int AttributeGroupId { get; set; }
    public List<MasterAttributeItemDto>? AttributeItemsData { get; set; }
    public bool IsLoadingOptions { get; set; } = false;
    private TelerikGrid<MasterAttributeItemDto> AttributeItemDataRef { get; set; }

    /// <summary>
    /// Set and pass data as parameter and event
    /// </summary>
    [Parameter] public int AttributeGroupAssignmentId { get; set; }
    [Parameter] public EventCallback<int> AttributeGroupAssignmentIdChanged { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    // Combo
    public string SelectedValue { get; set; }
    MasterAttributeItemDto SelectedAttributeItem { get; set; }
    List<MasterAttributeItemDto> ComboList = new List<MasterAttributeItemDto>();

    // Form
    private MasterAttributeItemDto EditItem { get; set; }

    /// <summary>
    /// Receiving parameter from clicked parent Grid
    /// </summary>
    /// <returns></returns>
    protected override async Task OnParametersSetAsync()
    {
        var data = await AttributeService.GetAttributeAssignmentByGroupIdAsync(AttributeGroupId);
        AttributeItemsData = data.Value;

        var result = await AttributeService.GetMasterAttributeItemsAsync();
        ComboList = result.Value;
    }

    private async void LoadData(MasterAttributeItemDto? result)
    {
        // Reload
        var data = await AttributeService.GetAttributeAssignmentByGroupIdAsync(AttributeGroupId);
        AttributeItemsData = data.Value;

        var comboResult = await AttributeService.GetMasterAttributeItemsAsync();
        ComboList = comboResult.Value;

        // Alert
        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Success!",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        // Refresh all components
        StateHasChanged();
    }

    private async Task OnValidSubmit()
    {
        // Update
        if (EditItem.AttributeItemId != 0 && EditItem.AttributeGroupAssignmentId != 0 && EditItem.AttributeGroupId != 0)
        {
            var result = await AttributeService.UpdateAttributeItemAsync(EditItem);

            LoadData(result);
        }
        // Add new on the fly
        else if (SelectedAttributeItem != null)
        {
            var result = await AttributeService.AddAttributeItemAsync(SelectedAttributeItem);

            LoadData(result);
        }
        // Add new from selection
        else
        {
            var buildAttributeItem = new MasterAttributeItemDto
                {
                    AttributeGroupId = AttributeGroupId,
                    Description = SelectedValue
                };

            var result = await AttributeService.AddAttributeItemAsync(buildAttributeItem);

            LoadData(result);
        }

        await ExitEditAsync();
    }

    private async Task OnDeleteGroupHandler(GridCommandEventArgs args)
    {
        var result = await AttributeService.DeleteAttributeItemAsync((MasterAttributeItemDto)args.Item);

        // Reload
        var data = await AttributeService.GetAttributeAssignmentByGroupIdAsync(AttributeGroupId);
        AttributeItemsData = data.Value;

        // Alert
        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Successfully deactivate item attribute",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        // Refresh all components
        StateHasChanged();

        // Notify something has changed
        this.AttributeItemPickService.IsChanged = true;
        this.AttributeItemPickService.ShowNotification = false;
    }

    /// <summary>
    /// Reactivate
    /// </summary>
    /// <param name="args"></param>
    /// <returns></returns>
    private async Task ReactiveClickHandler(GridCommandEventArgs args)
    {
        var result = await AttributeService.ReactivateAttributeItemAsync((MasterAttributeItemDto)args.Item);

        // Reload
        var data = await AttributeService.GetAttributeAssignmentByGroupIdAsync(AttributeGroupId);
        AttributeItemsData = data.Value;

        // Alert
        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Successfully reactivate item attribute",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        // Refresh all components
        StateHasChanged();

        // Notify something has changed
        this.AttributeItemPickService.IsChanged = true;
        this.AttributeItemPickService.ShowNotification = false;
    }

    private async Task RelinkClickHandler(GridCommandEventArgs args)
    {
        var result = await AttributeService.RelinkAttributeItemAsync((MasterAttributeItemDto)args.Item);

        // Reload
        var data = await AttributeService.GetAttributeAssignmentByGroupIdAsync(AttributeGroupId);
        AttributeItemsData = data.Value;

        // Alert
        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Error in relinking item attribute",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Successfully relink item attribute",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        // Refresh all components
        StateHasChanged();

        // Notify something has changed
        this.AttributeItemPickService.IsChanged = true;
        this.AttributeItemPickService.ShowNotification = false;
    }

    /// <summary>
    /// Get distinct Attribute Item
    /// </summary>
    /// <param name="args"></param>
    /// <returns></returns>
    async Task GetMasterAttributeItemData(ComboBoxReadEventArgs args)
    {
        var result = await AttributeService.GetMasterAttributeItemsAsync();
        ComboList = result.Value;
        var comboResult = ComboList.ToDataSourceResultAsync(args.Request);

        // set the Data and the TotalItems to the current page of data and total number of items
        args.Data = comboResult.Result.Data;
        args.Total = comboResult.Result.Total;
    }

    /// <summary>
    /// Add Attribute Item on the fly
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    private void AddAttributeOnTheFly(object value)
    {
        if (value != null)
        {
            if (ComboList.FirstOrDefault(x => x.Description == value.ToString()) == null)
            {
                SelectedAttributeItem = new MasterAttributeItemDto
                    {
                        AttributeGroupId = AttributeGroupId,
                        Description = value.ToString()
                    };
            }
        }
    }

    private async Task OnCancel()
    {
        await ExitEditAsync();
    }

    private async Task ExitEditAsync()
    {
        var state = AttributeItemDataRef?.GetState();
        state.OriginalEditItem = null;
        state.EditItem = null;
        state.InsertedItem = null;

        await AttributeItemDataRef?.SetStateAsync(state);
    }
}
