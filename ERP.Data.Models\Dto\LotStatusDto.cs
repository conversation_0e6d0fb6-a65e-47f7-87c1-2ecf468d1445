﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class LotStatusDto : IMapFrom<LotStatus>
{
    public int Id { get; set; }

    public string? StatusName { get; set; } 

    public string? SystemName { get; set; }

    public int? LastUser { get; set; }

    public DateTime? LastChanged { get; set; }

    public DateTime Createddatetime { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? Dwuserid { get; set; }

    public bool? IsActive { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    //public byte[]? RecordTimeStamp { get; set; }

   // public virtual ICollection<Contract> Contracts { get; set; } = new List<Contract>();

  //  public virtual ICollection<Job> Jobs { get; set; } = new List<Job>();
}
