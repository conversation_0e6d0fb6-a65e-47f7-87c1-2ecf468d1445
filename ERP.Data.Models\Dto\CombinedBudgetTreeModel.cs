﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public record CombinedBudgetTreeModel
    {
        //TODO: make an interface instead, 
        public bool? IsPendingCustomEstimate { get; set; }
        public CombinedBudgetTreeModel? Parent { get; set; }
        public List<CombinedBudgetTreeModel>? Children { get; set; }
        public bool HasChildren { get; set; }
        //header
        public Guid Id { get; set; }
        public bool? IsShowing { get; set; }
        public bool? IsIssued { get; set; }
        public bool IssueEnabled { get; set; }
        public bool? ToBeIssued { get; set; }
        public EstheaderDto? Estheader { get; set; }
        //public int? EstheaderId { get; set; }

        public string? JobNumber { get; set; }
        public string? BomClass { get; set; }
        public string? ExtraNumber { get; set; }//seems to show option code | option name in WMS
        public string? Releasecode { get; set; }
        public int? EstimateNumber { get; set; }

        //option
        public EstoptionDto? Estoption { get; set; }
        public double? TotalCost { get; set; }//sum from item costs

        //activity
        public EstactivityDto? Estactivity { get; set; }
        public double? ActivityTotal { get; set; }

        //EstDetail
        public EstdetailDto? Estdetail { get; set; }

        public Guid? ParentId { get; set; }       
        public bool? IsActive { get; set; }
    }
}
