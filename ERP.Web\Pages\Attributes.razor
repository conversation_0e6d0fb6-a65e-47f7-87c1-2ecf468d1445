﻿@page "/attributes"
@inject AttributeService AttributeService
@inject NavigationManager NavManager
@using ERP.Data.Models
@using Microsoft.Graph
@using System.Security.Cryptography
@inject AttributeItemPickService AttributeItemPickService
@inject AuthenticationStateProvider AuthenticationStateProvider
@implements IDisposable
@attribute [Authorize(Roles = "Admin, DesignCenter")]
<style>
    /* Gutter */
    .row > * {
        padding-right: calc(var(--bs-gutter-x) * .10);
        padding-left: calc(var(--bs-gutter-x) * .10);
    }

    .spacer {
        margin-top: 6px;
    }

    /**
                A CSS rule that matches the OnRowRender handler to
                conditionally hide hierarchy expand buttons and
                stops the pointer events of the cells with no
                expand buttons.
                You may want to add this to your site-wide stylesheet.
            **/
    .k-grid tr.no-children td.k-hierarchy-cell * {
        display: none;
    }

    .k-grid tr.no-children td.k-hierarchy-cell {
        pointer-events: none;
    }

    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto",sans-serif;
        font-size: .75rem;
    }

</style>
<PageTitle>Manage Master Items Attributes</PageTitle>

<div class="container-fluid flex">
    <div class="row d-flex">
        <div class="col-lg-12">
            <div class="card" style="background-color:#2e5771">
                <div class="card-body" style="padding:0.5rem">
                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Manage Attributes</h7>
                </div>
            </div>
        </div>

        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item active">Manage Attributes</li>
        </ol>

        <TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

        <div class="col-lg-12">
            <div class="card-header" style="padding:4px; margin-bottom:4px">
                <div style="text-align:center">
                    <h7 class="page-title" style="font-weight:bold">Attributes</h7>
                </div>
            </div>
            @if (AttributeGroupItemsData == null)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=@IsLoadingOptions />
            }
            else
            {
                <TelerikGrid Data=@AttributeGroupItemsData
                             PageSize="40"
                             Height="800px"
                             RowHeight="40"
                             ConfirmDelete="true"
                             SelectionMode="GridSelectionMode.Single"
                             EditMode="@GridEditMode.Inline"
                             Pageable="true"
                             OnCreate="@OnCreateGroupHandler"
                             RowDraggable="true"
                             OnRowDrop="@((GridRowDropEventArgs<MasterAttributeGroupDto> args) => OnGroupAssignmentDrop(args))"
                             OnUpdate="@OnUpdateGroupHandler"
                             OnDelete="@OnDeleteGroupHandler">
                    <GridToolBarTemplate>
                        <GridCommandButton Command="Add" Icon="@FontIcon.Plus" Class="k-button-add">Add Group</GridCommandButton>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <GridColumns>
                        <GridColumn Field="Description" Title="Group" Editable="true" Groupable="false" />
                        <GridColumn Field="IsActive" Title="Is Active?" Editable="false" Groupable="false" />
                        <GridCommandColumn>
                            <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true" Class="k-button-success">Update</GridCommandButton>
                            <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                            <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                            <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true" Class="k-button-danger">Cancel</GridCommandButton>
                            <GridCommandButton Command="Reactive" Icon="@FontIcon.Link" ShowInEdit="false" OnClick="@ReactiveClickHandler"></GridCommandButton>
                        </GridCommandColumn>
                    </GridColumns>
                    <DetailTemplate>
                        @{
                            MasterAttributeGroupDto attributeGroup = context as MasterAttributeGroupDto;
                            <ERP.Web.Components.NestedAttributes AttributeGroupId="@attributeGroup.AttributeGroupId" AttributeGroupAssignmentIdChanged="@AttributeItemClickedHandler"></ERP.Web.Components.NestedAttributes>
                        }
                    </DetailTemplate>
                </TelerikGrid>
            }
        </div>
    </div>
</div>

@code {
    /// <summary>
    /// Properties
    /// </summary>
    public List<MasterAttributeGroupDto>? AttributeGroupItemsData { get; set; }
    private StringFilterOperator FilterOperator { get; set; } = StringFilterOperator.StartsWith;

    /// <summary>
    /// Component parameter for accessing child components
    /// </summary>
    //private int SelectedOptionId { get; set; }
    private int AttributeGroupAssignmentId { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    private string loadingActivityStyle = "display:none";
    private string loadingItemStyle = "display:none";
    public bool IsLoadingOptions { get; set; } = false;
    public bool IsLoadingActivity { get; set; } = false;
    public bool IsLoadingItem { get; set; } = false;
    private bool AllowEdit { get; set; } = true;

    [Inject]
    public GraphServiceClient? Client { get; set; }
    public string? PhotoBytes { get; set; }

    /// <summary>
    /// Load init
    /// </summary>
    /// <returns></returns>
    protected override async Task OnInitializedAsync()
    {
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        IsLoadingItem = true;

        var attributeGroupItemsData = await AttributeService.GetMasterAttributeGroupsAsync();
        AttributeGroupItemsData = attributeGroupItemsData.Value;

        IsLoadingItem = false;
    }

    /// <summary>
    /// Pass event to child component
    /// </summary>
    async Task AttributeItemClickedHandler(int selectedValue)
    {
        AttributeGroupAssignmentId = selectedValue;
    }

    /// <summary>
    /// Create
    /// </summary>
    private async Task OnCreateGroupHandler(GridCommandEventArgs args)
    {
        var attributeGroup = (MasterAttributeGroupDto)args.Item;

        var result = await AttributeService.AddAttributeGroupAsync(attributeGroup);

        // Reload
        var attributeGroupItemsData = await AttributeService.GetMasterAttributeGroupsAsync();
        AttributeGroupItemsData = attributeGroupItemsData.Value;

        // Alert
        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Successfully added group attribute",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        // Refresh all components
        StateHasChanged();
    }

    /// <summary>
    /// Update
    /// </summary>
    /// <returns></returns>
    private async Task OnUpdateGroupHandler(GridCommandEventArgs args)
    {
        var attributeGroup = (MasterAttributeGroupDto)args.Item;

        var result = await AttributeService.UpdateAttributeGroupAsync(attributeGroup);

        // Reload
        var attributeGroupItemsData = await AttributeService.GetMasterAttributeGroupsAsync();
        AttributeGroupItemsData = attributeGroupItemsData.Value;

        // Alert
        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Successfully updated group attribute",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        StateHasChanged();
    }

    /// <summary>
    /// Delete
    /// </summary>
    /// <returns></returns>
    private async Task OnDeleteGroupHandler(GridCommandEventArgs args)
    {
        var attributeGroup = (MasterAttributeGroupDto)args.Item;

        var result = await AttributeService.DeleteAttributeGroupAsync(attributeGroup);

        // Reload
        var attributeGroupItemsData = await AttributeService.GetMasterAttributeGroupsAsync();
        AttributeGroupItemsData = attributeGroupItemsData.Value;

        // Alert
        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Successfully deleted group attribute",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        StateHasChanged();
    }

    /// <summary>
    ///  Reactivate
    /// </summary>
    /// <param name="args"></param>
    /// <returns></returns>
    private async Task ReactiveClickHandler(GridCommandEventArgs args)
    {
        var attributeGroup = (MasterAttributeGroupDto)args.Item;

        var result = await AttributeService.ReactivateAttributeGroupAsync(attributeGroup);

        // Reload
        var attributeGroupItemsData = await AttributeService.GetMasterAttributeGroupsAsync();
        AttributeGroupItemsData = attributeGroupItemsData.Value;

        // Alert
        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Successfully reactivate group attribute",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        StateHasChanged();
    }

    /// <summary>
    /// Init delegate
    /// </summary>
    protected override void OnInitialized()
    {
        this.AttributeItemPickService.OnDataChanged += GetMasterOption;
    }

    async Task GetMasterOption()
    {
        await this.InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        this.AttributeItemPickService.OnDataChanged -= GetMasterOption;
    }

    private async Task OnGroupAssignmentDrop(GridRowDropEventArgs<MasterAttributeGroupDto> args)
    {
        if (args.DestinationItem != null)
        {
            var model = new MasterAttributeGroupDto
                {
                    MasterOptionId = args.DestinationItem.MasterOptionId,
                    AttributeGroupId = args.Item.AttributeGroupId,
                    IsActive = args.Item.IsActive
                };

            var result = await AttributeService.AddGroupToOption(model);

            if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
            {
                this.AttributeItemPickService.ErrorMessage = result.ErrorMessage;
            }

            // Notify something has changed
            this.AttributeItemPickService.IsChanged = true;
        }
    }
}