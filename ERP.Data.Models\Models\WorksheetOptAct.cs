﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class WorksheetOptAct
{
    public int WorksheetOptActId { get; set; }

    public int WorksheetOptId { get; set; }

    public string? Activity { get; set; }

    public double? Costprice { get; set; }

    public string? Lumpsum { get; set; }

    public int? Errors { get; set; }

    public int? Warnings { get; set; }

    public int? SubNumber { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual Supplier? SubNumberNavigation { get; set; }

    public virtual WorksheetOpt WorksheetOpt { get; set; } = null!;
}
