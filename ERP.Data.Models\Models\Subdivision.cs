﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Subdivision
{
    public int SubdivisionId { get; set; }

    public string? SubdivisionNum { get; set; }

    public string? SubdivisionName { get; set; }

    public string? Address1 { get; set; }

    public string? Address2 { get; set; }

    public string? City { get; set; }

    public string? State { get; set; }

    public string? Zip { get; set; }

    public string? MarketingName { get; set; }

    public string? EntityName { get; set; }

    public string? EntityNum { get; set; }

    public string? EntityName2 { get; set; }

    public string? EntitySignatureBlock { get; set; }

    public DateTime? AcquireDate { get; set; }

    public decimal? Acreage { get; set; }

    public string? Contracted { get; set; }

    public string? Country { get; set; }

    public int? CountryId { get; set; }

    public string? County { get; set; }

    public string? DocsFolder { get; set; }

    public string? ElectricName { get; set; }

    public string? Email { get; set; }

    public string? ElectricPhone { get; set; }

    public string? EmailPosToSuperviser { get; set; }

    public string? LegalName { get; set; }

    public string? Fax { get; set; }

    public string? GasName { get; set; }

    public string? GasPhone { get; set; }

    public decimal? HoaFees { get; set; }

    public string? HoaName { get; set; }

    public string? HoaPhone { get; set; }

    public bool? HoaType { get; set; }

    public DateTime? LastChanged { get; set; }

    public int? LastUser { get; set; }

    public string? MarketingDesc { get; set; }

    public bool? MaxIncentiveType { get; set; }

    public decimal? MaxIncentiveValue { get; set; }

    public decimal? MaxLoanAmt { get; set; }

    public decimal? MiscellaneousFee { get; set; }

    public string? Phone { get; set; }

    public decimal? SetupFee { get; set; }

    public string? SewerName { get; set; }

    public string? SewerPhone { get; set; }

    public bool? ShouldSupressRulesForUnavailableOptions { get; set; }

    public string? StandardSubdivisionName { get; set; }

    public int? StateId { get; set; }

    public string? SubdivisionClass { get; set; }

    public string? SubdivisionStatus { get; set; }

    public int? TargetUnits { get; set; }

    public string? TelephoneName { get; set; }

    public string? TelephonePhone { get; set; }

    public int? TotalLots { get; set; }

    public bool? UseElectronicDocStorage { get; set; }

    public bool? UseElectronicSignature { get; set; }

    public string? VmhVmb { get; set; }

    public string? WarrantyEnabled { get; set; }

    public string? WarrantyShortName { get; set; }

    public string? WaterName { get; set; }

    public string? WaterPhone { get; set; }

    public string? Workdays { get; set; }

    public int? WorkZoneId { get; set; }

    public string? Zoning { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStampe { get; set; } = null!;

    public bool? IsActiveAdult { get; set; }

    public string? CommunityLabel { get; set; }

    public bool? Blocked { get; set; }

    public bool? LinkWithErp { get; set; }

    public string? CommunityStatus { get; set; }

    public string? SellerManager { get; set; }

    public int? DivId { get; set; }

    public virtual ICollection<Cost> Costs { get; set; } = new List<Cost>();

    public virtual ICollection<DocuSignSignatureTemplate> DocuSignSignatureTemplates { get; set; } = new List<DocuSignSignatureTemplate>();

    public virtual ICollection<Estcustoption> Estcustoptions { get; set; } = new List<Estcustoption>();

    public virtual ICollection<HoaAssessment> HoaAssessments { get; set; } = new List<HoaAssessment>();

    public virtual ICollection<Job> Jobs { get; set; } = new List<Job>();

    public virtual ICollection<PactivityAreaSupplier> PactivityAreaSuppliers { get; set; } = new List<PactivityAreaSupplier>();

    public virtual ICollection<PhasePlan> PhasePlans { get; set; } = new List<PhasePlan>();

    public virtual ICollection<ScheduleArea> ScheduleAreas { get; set; } = new List<ScheduleArea>();

    public virtual ICollection<SubdivisionContact> SubdivisionContacts { get; set; } = new List<SubdivisionContact>();

    public virtual ICollection<TradeSupplier> TradeSuppliers { get; set; } = new List<TradeSupplier>();
}
