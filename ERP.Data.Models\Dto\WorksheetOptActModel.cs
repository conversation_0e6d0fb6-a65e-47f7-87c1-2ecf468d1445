﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class WorksheetOptActModel
{
    public int WorksheetOptActId { get; set; }

    public int WorksheetOptId { get; set; }

    public string? Activity { get; set; }

    public double? Costprice { get; set; }

    public string? Lumpsum { get; set; }
    public int? ErrorCount { get; set; }
    public int? ErrorCode { get; set; }//error code - 4 = no cost
    public string? ErrorReason { get; set; }
    public int? Warnings { get; set; }
    public int? WarningCount { get; set; }
    public string? WarningReason { get; set; }

    public int? SubNumber { get; set; }
    public string? SupplierName { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public Supplier? SubNumberNavigation { get; set; } 

    public WorksheetOpt? WorksheetOpt { get; set; } 
    public string? PhaseCode { get; set; }
    public string? ItemNumber { get; set; }
    public string? ItemDesc { get; set; }
    public double? OldCost { get; set; }//from history
    public DateTime? OldCostEndDate { get; set; }
    public double? UnitCost { get; set; }//from costs unit cost
    public double? NextCost { get; set; }//from costs next cost
    public DateTime? NextCostSartDate { get; set; }
    public double? LastCost1 { get; set; }//from costs last cost
    public DateTime? LastCostEndDate { get; set; }
    public double? LastCost2 { get; set; }//from costs last cost
    public DateTime? LastCost2EndDate { get; set; }
    public double? LastCost3 { get; set; }//from costs last cost
    public DateTime? LastCost3EndDate { get; set; }
    public int? MasterItemId { get; set; }
    public double? ItemQuantity { get; set; }
}
