﻿@page "/vpoapprovals"
@using ERP.Data.Models
@using ERP.Web.DocumentProcessing
@using Telerik.DataSource.Extensions
@using Telerik.SvgIcons
@inject PoService PoService
@inject BudgetService BudgetService
@inject SubdivisionService SubdivisionService
@inject VPOItemPickService VPOItemPickService
@inject AuthenticationStateProvider _authenticationStateProvider
@inject IJSRuntime JS
<style type="text/css">
    /* Gutter */
    .row > * {
    padding-right: calc(var(--bs-gutter-x) * .10);
    padding-left: calc(var(--bs-gutter-x) * .10);
    }

    /* Checkbox */
    .k-grid .header-select-all .k-checkbox-wrap {
    vertical-align: middle;
    }

    .k-grid .header-select-all,
    .k-grid td:first-child {
    text-align: center;
    }

    .hideCustomRowFormatting .k-grid-checkbox {
    display: none;
    }

    .showCustomRowFormatting .k-grid-checkbox {
    display: inline;
    }

    .button-container {
    display: flex;
    flex-direction: column;
    gap: 6px;
    justify-content: center;
    align-items: stretch;
    }

</style>

<PageTitle>VPO Approvals</PageTitle>

<div class="container-fluid flex">
    <div class="col-lg-12">
        <div class="card" style="background-color: #2e5771">
            <div class="card-body" style="padding:0.5rem">
                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">VPO Approvals</h7>
            </div>
        </div>
    </div>

    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item active">VPO Approvals</li>
    </ol>

    <TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

    <div class="row d-flex">
        <div class="col-lg-12">
            @if (IsLoading)
            {
                <p><em>Loading...</em></p>
                <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />
            }
            else
            {
                <TelerikGrid @ref="@PoApprovalGridRef"
                EditMode="@GridEditMode.None"
                OnRead="@ReadItems"
                TItem="@PoapprovalDto"
                Sortable="true"
                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                SelectionMode="GridSelectionMode.Multiple"
                SelectedItemsChanged="@((IEnumerable<PoapprovalDto> approvalList) => OnSelect(approvalList))"
                SelectedItems="@SelectedApprovals"
                OnRowRender="@OnRowRenderHandler"
                OnStateInit="@( (GridStateEventArgs<PoapprovalDto> args) => OnGridStateInit(args) )"
                Groupable="true"
                Pageable="true"
                PageSize="20"
                ScrollMode="@GridScrollMode.Scrollable">
                    <GridToolBarTemplate>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                        <GridCommandButton Command="ApproveAllCommand" OnClick="@ApproveAllOnClick" Icon="@SvgIcon.CheckCircle" Class="k-button-add">Approve All Selected</GridCommandButton>
                        <TelerikToggleButton @bind-Selected="@ToggleShowAll" OnClick="@ToggleNeedApprovalOnly">All/Need My Approval: <strong>@ShowHideNeedApprove</strong></TelerikToggleButton>
                        <label for="SubdivisionFilter">Subdivision:</label>
                        <TelerikDropDownList Id="SubdivisionFilter"
                        Data="@AllSubdivisions"
                        TextField="SubdivisionName"
                        ValueField="SubdivisionName"
                        Filterable="true"
                        FilterOperator="StringFilterOperator.Contains"
                        DefaultText="All"
                        @bind-Value="@SelectedSubdivision" OnChange="OnSubdivisionChanged" Width="200px" />
                        <label for="JobByFilter">Job:</label>
                        <TelerikDropDownList Id="JobFilter"
                        Data="@Jobs"
                        TextField="JobNumber"
                        ValueField="JobNumber"
                        Filterable="true"
                        FilterOperator="StringFilterOperator.Contains"
                        DefaultText="All"
                        @bind-Value="@SelectedJob" Width="200px" OnChange="ApplyFilters" />
                        <label for="CreatedByFilter">Created by:</label>
                        <TelerikDropDownList Id="UserFilter"
                        Data="@Users"
                        TextField="."
                        ValueField="."
                        Filterable="true"
                        FilterOperator="StringFilterOperator.Contains"
                        DefaultText="All"
                        @bind-Value="@SelectedUser" Width="200px" OnChange="ApplyFilters" />
                        <label for="StatusFilter">Status:</label>
                        <TelerikDropDownList Id="status"
                        Data="@Status"
                        TextField="."
                        ValueField="."
                        Filterable="true"
                        FilterOperator="StringFilterOperator.Contains"
                        DefaultText="All"
                        @bind-Value="@SelectedStatus" Width="200px" OnChange="ApplyFilters" />
                    </GridToolBarTemplate>
                    <GridColumns>

                        <GridCheckboxColumn SelectAllMode="GridSelectAllMode.Current" Title="Select" Width="35px" Locked="true" />
                        <GridColumn Field="Invnumber" Title="Invoice Number" Editable="false" Width="100px" Locked="true" />
                        <GridColumn Field="Poheader.VpoNumberSearch" Title="VPO Number" Editable="false" Width="100px" Locked="true" />
                        <GridColumn Field="Poheader.SubdivisionName" Title="Subdivision" Editable="false" Width="100px" />
                        <GridColumn Field="Poheader.Pojobnumber" Title="Job Number" Editable="false" Width="100px" />
                        <GridColumn Field="PurchasingActivityName" Title="Activity" Editable="false" Width="100px" Locked="true" />
                        <GridColumn Field="Vendor" Title="Vendor" Editable="false" Width="100px" />
                        <GridColumn Field="Invdescription" Title="Description" Editable="false" Width="100px" />
                        <GridColumn Field="Poheader.Pototal" Title="Amount" DisplayFormat="{0:C2}" Editable="false" Width="100px" />
                        <GridColumn Field="Invdate" Title="Invoice Date" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Width="100px" />
                        <GridColumn Field="Approvedby" Title="Approved By" Editable="false" Width="100px" />
                        <GridColumn Field="ApprovedDate" Title="Approved Date" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Width="100px" />
                        <GridColumn Field="CreatedDateTime" Title="Created Date" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Width="100px" />
                        <GridColumn Field="Poheader.CreatedBy" Title="Created By" Editable="false" Width="100px" />
                        @* <GridColumn Field="UpdatedDateTime" Title="Updated" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" />
                    <GridColumn Field="UpdatedBy" Title="Updated By" Editable="false" /> *@
                        <GridColumn Field="NextLevelApprover" Title="Next Approver" Editable="false" Width="100px" />
                        <GridColumn Field="PoStatus" Title="Status" Editable="false" Width="100px" />
                        <GridCommandColumn Context="commandContext" Width="100px">
                            @{
                                var roleStatus = commandContext as PoapprovalDto;

                                if (roleStatus.ShowApproveButton == true)
                                {
                                    <div class="button-container">
                                        <GridCommandButton Command="ApproveCommand" Icon="@SvgIcon.CheckCircle" ShowInEdit="false" OnClick="@ApproveOnClickHandler" Class="k-button-add">Approve</GridCommandButton>
                                        <GridCommandButton Command="RejectCommand" Icon="@SvgIcon.CancelOutline" ShowInEdit="false" OnClick="@RejectVPO" Class="k-button-danger">Reject</GridCommandButton>
                                    </div>
                                }
                            }
                        </GridCommandColumn>
                        <GridCommandColumn Context="commandContext" Width="50px">
                            <div class="button-container">
                                <GridCommandButton Title="Download" Command="Download" OnClick="@Download" Icon="@FontIcon.Download" Class="k-button-success"></GridCommandButton>
                                @*  <GridCommandButton Command="Restart" Icon="@FontIcon.ArrowRotateCwSmall" ShowInEdit="false" OnClick="@RestartClickHandler"></GridCommandButton> *@
                                @{
                                    var poApproval = commandContext as PoapprovalDto;
                                    if (poApproval.PoAttachments.Any())
                                    {
                                        <GridCommandButton Title="View Attachments" Command="View" OnClick="@ViewAttachments" Icon="@FontIcon.Paperclip"></GridCommandButton>
                                    }

                                    if (string.IsNullOrEmpty(poApproval.Approvedby))
                                    {
                                        if (CanResetVPO())
                                        {
                                            <GridCommandButton Title="Send Approval Email" Command="SendApprovalCommand" OnClick="@StartApprovalFlow" Icon="@FontIcon.Envelope"></GridCommandButton>
                                        }
                                    }
                                }
                            </div>

                        </GridCommandColumn>
                    </GridColumns>
                </TelerikGrid>
            }
        </div>
    </div>
</div>

<RejectVPO @ref="RejectVPOModalRef" VPOToReject="EditPoApprovalDto" HandleRejectSubmit="HandleVPORejectSubmit"></RejectVPO>

@code {
    public List<PoapprovalDto>? PoApprovalData { get; set; }
    private TelerikGrid<PoapprovalDto>? PoApprovalGridRef { get; set; }
    private PoapprovalDto EditPoApprovalDto { get; set; }
    public IEnumerable<PoapprovalDto> SelectedApprovals { get; set; } = Enumerable.Empty<PoapprovalDto>();
    public RejectVPO RejectVPOModalRef { get; set; }

    private bool ShowAll { get; set; } = false;
    private bool RetrievedAllVpoApprovals = false;
    private bool ToggleShowAll { get; set; } = true;
    private string? ShowHideNeedApprove { get; set; } = "My Pending VPOs";
    private BrowserInfo? BrowserInfo { get; set; }
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    private string SelectedSubdivision { get; set; }
    public List<SubdivisionDto>? AllSubdivisions { get; set; }
    public List<string> Users { get; set; }
    public List<string> Status = new List<string>()
    {
        "Approved",
        "Pending Approval",
        "Pending Payment Approval",
        "Payment Approved"
    };

    public List<JobDto> Jobs { get; set; }
    public List<JobDto> AllJobs { get; set; }
    private string? SelectedUser { get; set; }
    private string SelectedJob { get; set; }
    private string SelectedStatus { get; set; }

    // Loading
    public bool IsLoading { get; set; }

    async Task<string> getUserName()
    {
        var user = (await _authenticationStateProvider.GetAuthenticationStateAsync()).User;
        var userName = user.Identity.Name.Split('@')[0];
        return userName;
    }

    private bool CanResetVPO()
    {
        var authState = _authenticationStateProvider.GetAuthenticationStateAsync().Result;
        var user = authState.User;
        return user.IsInRole("VPOReset");
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
        BrowserInfo = await JS.InvokeAsync<BrowserInfo>("browserUtils.getBrowserInfo", null);
    }

    private async Task OnCancel()
    {
        await ExitEditAsync();
    }

    private async Task OnValidSubmit()
    {
        await ExitEditAsync();
    }

    protected async Task ReadItems(GridReadEventArgs args)
    {
        var dataSourceResult = PoApprovalData.ToDataSourceResult(args.Request);
        args.Data = dataSourceResult.Data;
        args.Total = dataSourceResult.Total;
    }

    private async Task LoadData()
    {
        IsLoading = true;

        Task<ResponseModel<List<PoapprovalDto>>> poApprovalsTask;

        if (ShowAll && !RetrievedAllVpoApprovals)
        {
            poApprovalsTask = PoService.GetVPOApprovals();
            RetrievedAllVpoApprovals = true;
        }
        else
        {
            var username = await getUserName();
            poApprovalsTask = PoService.GetVPOApprovals(username);
        }

        var subdivisionsTask = SubdivisionService.GetSubdivisionsAsync();
        var jobsTask = SubdivisionService.GetAllJobsAsync();
        await Task.WhenAll(poApprovalsTask, subdivisionsTask, jobsTask);

        PoApprovalData = poApprovalsTask.Result.Value;
        AllSubdivisions = subdivisionsTask.Result.Value;
        Users = PoApprovalData.Select(x => x.Poheader.CreatedBy).Distinct().ToList();
        AllJobs = jobsTask.Result.Value;
        Jobs = jobsTask.Result.Value;
        IsLoading = false;
    }

    private async Task ExitEditAsync()
    {
        var state = PoApprovalGridRef?.GetState();
        state.OriginalEditItem = null;
        state.EditItem = null;
        state.InsertedItem = null;

        await PoApprovalGridRef?.SetStateAsync(state);
    }

    private async Task OnGridStateInit(GridStateEventArgs<PoapprovalDto> args)
    {

        var filters = new CompositeFilterDescriptor()
            {
                FilterDescriptors = new FilterDescriptorCollection() {
                     new FilterDescriptor()
                     {
                        Member = nameof(PoapprovalDto.ShowApproveButton),
                        MemberType = typeof(bool),
                        Operator = FilterOperator.IsEqualTo,
                        Value =true
                     }
                }
            };
        args.GridState.FilterDescriptors.Add(filters);
        args.GridState.GroupDescriptors.Add(new GroupDescriptor
            {
                Member = "Invnumber",
                MemberType = typeof(string)
            });
    }

    private async Task ApproveOnClickHandler(GridCommandEventArgs args)
    {
        var item = args.Item as PoapprovalDto;

        var result = await PoService.ApproveVPO(item);

        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.SuccessMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        ResponseModel<List<PoapprovalDto>> poApprovalsReponse;
        RetrievedAllVpoApprovals = false;

        if (ShowAll)
        {
            poApprovalsReponse = await PoService.GetVPOApprovals();
            PoApprovalData = poApprovalsReponse.Value;
            RetrievedAllVpoApprovals = true;
        }
        else
        {
            var username = await getUserName();
            poApprovalsReponse = await PoService.GetVPOApprovals(username);
            PoApprovalData = poApprovalsReponse.Value;
        }

        await ExitEditAsync();
    }

    private async Task RejectVPO(GridCommandEventArgs args)
    {
        var item = args.Item as PoapprovalDto;
        EditPoApprovalDto = item;

        RejectVPOModalRef.Show();
    }

    private async Task StartApprovalFlow(GridCommandEventArgs args)
    {
        var item = args.Item as PoapprovalDto;
        if (item != null)
        {
            var result = await PoService.StartPowerAutomateApprovalFlowAsync(item.PoapprovalId);
            var response = result.Value;
            if (response == "Accepted")
            {
                ShowNotification("Successfully started the approval flow", true);
            }
            else
            {
                ShowNotification("Failed to start the approval flow", false);
            }
        }
    }

    public async void ViewAttachments(GridCommandEventArgs args)
    {
        var item = args.Item as PoapprovalDto;
        var poHeaderData = await PoService.GetPoHeaderAsync(item.PoheaderId);
        var poHeader = poHeaderData.Value;
        if (poHeader != null)
        {
            poHeader.PoAttachments = item.PoAttachments;
            var blobFilesDetail = await PoService.GetBlobFilesDetailAsync(poHeader);
            var files = blobFilesDetail.Value;
            if (files != null)
            {
                foreach (var file in files)
                {
                    await OpenFileInNewTab(file);
                    if (file.Content != null)
                    {
                        DemoFileExporter.Save(JS, file.Content, file.ContentType, file.DownloadName);
                    }
                }
            }
        }
    }

    private async Task OpenFileInNewTab(BlobFileDetails file)
    {
        string base64File = string.Empty;
        string fileName = string.Empty;
        string contentType = string.Empty;
        if (ImageConverter.IsHeicFile(file.DownloadName) && BrowserInfo?.Browser != "Safari")
        {
            var jpegContent = ImageConverter.ConvertHeicBytesToJpeg(file.Content);
            base64File = Convert.ToBase64String(jpegContent);
            fileName = Path.ChangeExtension(file.DownloadName, ".jpg");
            contentType = "image/jpeg";
        }
        else
        {
            base64File = Convert.ToBase64String(file.Content);
            fileName = file.DownloadName;
            contentType = file.ContentType;
        }

        // Invoke JavaScript function to open a new tab and display the file
        await JS.InvokeVoidAsync("viewMyFile", base64File, contentType, fileName);
    }

    private async Task HandleVPORejectSubmit(ResponseModel<PoapprovalDto> rejectResponse)
    {
        RejectVPOModalRef.Hide();

        ShowNotification(rejectResponse.Message, rejectResponse.IsSuccess);

        await LoadData();
    }

    protected void OnSelect(IEnumerable<PoapprovalDto> approvalList)
    {
        SelectedApprovals = approvalList;
    }

    private async Task ApproveAllOnClick(GridCommandEventArgs args)
    {
        var result = await PoService.BulkApproveVPO(SelectedApprovals.ToList());

        if (!string.IsNullOrWhiteSpace(result.ErrorMessage))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.ErrorMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.SuccessMessage,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        ResponseModel<List<PoapprovalDto>> poApprovalsReponse;
        RetrievedAllVpoApprovals = false;

        if (ShowAll)
        {
            poApprovalsReponse = await PoService.GetVPOApprovals();
            PoApprovalData = poApprovalsReponse.Value;
            RetrievedAllVpoApprovals = true;
        }
        else
        {
            var username = await getUserName();
            poApprovalsReponse = await PoService.GetVPOApprovals(username);
            PoApprovalData = poApprovalsReponse.Value;
        }

        await ExitEditAsync();
    }

    protected void OnRowRenderHandler(GridRowRenderEventArgs args)
    {
        var item = args.Item as PoapprovalDto;

        if (item.VpoApprovalId == 1 && item.CreatedBy.ToLower() == getUserName().Result.ToLower() && item.ApprovedDate == null)
        {
            args.Class = "showCustomRowFormatting";
        }
        else if (item.JobContactId != null && item.ApprovedDate == null)
        {
            args.Class = "showCustomRowFormatting";
        }
        else
        {
            args.Class = "hideCustomRowFormatting";
        }
    }
    public async void Download(GridCommandEventArgs args)
    {
        var poApproveItem = args.Item as PoapprovalDto;
        if (poApproveItem != null)
        {
            var getPoHeaderdata = await PoService.GetPoHeaderAsync(poApproveItem.PoheaderId);
            var getPoHeader = getPoHeaderdata.Value;
            var estDetailsResponse = await BudgetService.GetPoDetailsByPONumberAsync(poApproveItem.PoheaderId);
            if (estDetailsResponse.IsSuccess)
            {
                GenerateDocumentAndDownload(getPoHeader, estDetailsResponse.Value);
            }
            else
            {
                ShowNotification(estDetailsResponse.Message, false);
            }
        }
    }
    async Task GenerateDocumentAndDownload(PoheaderDto poHeader, List<PodetailDto> poDetails)
    {
        byte[] fileData = ERP.Web.DocumentProcessing.GeneratePO.GenerateFile(poHeader, poDetails);
        // await PoService.UploadPoAsync(itemToIssue.Poheader, fileData);
        DemoFileExporter.Save(JS, fileData, "application/pdf", $"PO-{poHeader.Ponumber}.pdf");
    }
    async void ShowNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
    private async Task RestartClickHandler(GridCommandEventArgs args)
    {
        var poApprovalData = (PoapprovalDto)args.Item;

        var result = await PoService.RestartPowerAutomateAsync(poApprovalData.PoapprovalId);

        // Alert
        if (!string.IsNullOrWhiteSpace(result.Error))
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = result.Message,
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Error
                });
        }
        else
        {
            NotificationReference.Show(new NotificationModel()
                {
                    Text = "Successfully restart the approval workflow",
                    ThemeColor = ThemeConstants.Notification.ThemeColor.Success
                });
        }

        StateHasChanged();
    }

    async Task ApplyFilters()
    {
        var state = PoApprovalGridRef.GetState();

        state.Skip = 0;

        var compositeFilter = new CompositeFilterDescriptor
            {
                LogicalOperator = FilterCompositionLogicalOperator.And,
                FilterDescriptors = new FilterDescriptorCollection()
            };

        if (!ShowAll)
        {
            compositeFilter.FilterDescriptors.Add(new FilterDescriptor
                {
                    Member = nameof(PoapprovalDto.ShowApproveButton),
                    MemberType = typeof(bool),
                    Operator = FilterOperator.IsEqualTo,
                    Value = true
                });
        }

        if (!string.IsNullOrWhiteSpace(SelectedSubdivision) && SelectedSubdivision != "All")
        {
            compositeFilter.FilterDescriptors.Add(new FilterDescriptor
                {
                    Member = nameof(PoapprovalDto.SubdivisionName),
                    MemberType = typeof(string),
                    Operator = FilterOperator.IsEqualTo,
                    Value = SelectedSubdivision
                });
        }

        if (!string.IsNullOrWhiteSpace(SelectedJob) && SelectedJob != "All")
        {
            compositeFilter.FilterDescriptors.Add(new FilterDescriptor
                {
                    Member = nameof(PoapprovalDto.Pojobnumber),
                    MemberType = typeof(string),
                    Operator = FilterOperator.IsEqualTo,
                    Value = SelectedJob
                });
        }

        if (!string.IsNullOrWhiteSpace(SelectedUser) && SelectedUser != "All")
        {
            compositeFilter.FilterDescriptors.Add(new FilterDescriptor
                {
                    Member = nameof(PoapprovalDto.CreatedBy),
                    MemberType = typeof(string),
                    Operator = FilterOperator.IsEqualTo,
                    Value = SelectedUser
                });
        }

        if (!string.IsNullOrWhiteSpace(SelectedStatus) && SelectedStatus != "All")
        {
            compositeFilter.FilterDescriptors.Add(new FilterDescriptor
                {
                    Member = nameof(PoapprovalDto.PoStatus),
                    MemberType = typeof(string),
                    Operator = Telerik.DataSource.FilterOperator.IsEqualTo,
                    Value = SelectedStatus
                });
        }

        var newState = new GridState<PoapprovalDto>
            {
                Skip = 0,
                FilterDescriptors = compositeFilter.FilterDescriptors.Any()
                                ? new List<IFilterDescriptor> { compositeFilter }
                                : new List<IFilterDescriptor>(),
                GroupDescriptors = new List<GroupDescriptor>
                {
                    new GroupDescriptor
                    {
                        Member = "Invnumber",
                        MemberType = typeof(string)
                    }
                }
            };

        await PoApprovalGridRef.SetStateAsync(newState);
    }

    async Task OnSubdivisionChanged()
    {
        // Reset the selected job
        SelectedJob = "All";

        // Re-filter the job list based on selected subdivision
        if (!string.IsNullOrWhiteSpace(SelectedSubdivision) && SelectedSubdivision != "All")
        {
            int subdivisionId = AllSubdivisions
                .FirstOrDefault(x => x.SubdivisionName == SelectedSubdivision)?.SubdivisionId ?? 0;

            Jobs = AllJobs
                .Where(x => x.SubdivisionId == subdivisionId)
                .ToList();
        }
        else
        {
            Jobs = AllJobs.ToList();
        }

        // Re-apply filters with updated job list
        await ApplyFilters();
    }

    async Task ToggleNeedApprovalOnly()
    {
        ShowAll = ToggleShowAll;
        ShowHideNeedApprove = !ShowAll ? "My Pending Only" : "All";

        ResponseModel<List<PoapprovalDto>> poApprovalsReponse;
        if (ShowAll && !RetrievedAllVpoApprovals)
        {
            IsLoading = true;
            poApprovalsReponse = await PoService.GetVPOApprovals();
            PoApprovalData = poApprovalsReponse.Value;
            RetrievedAllVpoApprovals = true;
            IsLoading = false;
            StateHasChanged();
        }

        await ApplyFilters();
    }
}
