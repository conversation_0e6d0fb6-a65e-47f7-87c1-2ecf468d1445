﻿@using ERP.Data.Models;
@inject SubdivisionService SubdivisionService
@inject BudgetService BudgetService
@using ERP.Data.Models.Dto;
<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="450px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Attach Estimate
    </WindowTitle>
    <WindowContent>
        <label>Use Master Custom Estimates</label>
        <TelerikRadioGroup Data="@MasterOrJobSpecific" @bind-Value="@SelectedMasterOrJobSpecific" OnChange="@ChangeUseMasterOptionHandler"></TelerikRadioGroup>
        @if (SelectedMasterOrJobSpecific == "Use Job Specific Estimates")
        {
            <br />
            <label>Enter job number</label>
            <TelerikAutoComplete Data="AllJobsForAutoSelect"
                                 ScrollMode="@DropDownScrollMode.Virtual"
                                 ItemHeight="30"
                                 OnChange="@JobSelectedHandler"
            @bind-Value="@SelectedJob"
                                 Filterable="true"
                                 PageSize="10"
                                 Width="200px"
                                 FilterOperator="@StringFilterOperator.Contains">
                <AutoCompleteSettings>
                    <AutoCompletePopupSettings Height="300px" />
                </AutoCompleteSettings>
            </TelerikAutoComplete>
        }
        
        <br/>
        <br/>
        <TelerikGrid Data=@CustomEstimatesForSelectedJob
                     FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                     Height="500px" RowHeight="60" PageSize="20"
                     Sortable="true"
                     Resizable="true"
                     SelectionMode="GridSelectionMode.Single"
                     EditMode="@GridEditMode.Popup"
                     ConfirmDelete="true"
                     OnRowClick="@OnEstimateRowClickHandler"
                     >
            <GridColumns>
                <GridColumn Field="ReferenceDesc" Visible="true" Editable="false" Groupable="false" />
                <GridColumn Field="EstimateDescPe" Visible="true" Editable="false" Groupable="false" />
                <GridColumn Field="Estimator" Visible="true" Editable="false" Groupable="false" />
            </GridColumns>
        </TelerikGrid>
        <br/>
        <TelerikButton ButtonType="@ButtonType.Button" OnClick="@AttachSelectedEstimate">Attach</TelerikButton>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public bool UseMasterEstimates { get; set; } = true;
    private List<string>? MasterOrJobSpecific { get; set; } 
    private string? SelectedMasterOrJobSpecific { get; set; } = "Use Master Estimates";
    public AddCustomEstimateModel EstimateToAdd { get; set; } = new AddCustomEstimateModel();
    public List<SubdivisionDto>? AllSubdivisions { get; set; }
    private string submittingStyle = "display:none";
    public CombinedPOBudgetTreeModel? SelectedCustBudget { get; set; } = new CombinedPOBudgetTreeModel();
    private List<string>? AllJobsForAutoSelect { get; set; }
    private List<EstheaderDto>? CustomEstimatesForSelectedJob { get; set; }
    private string? SelectedJob { get; set; }
    private string? JobSelected { get; set; }
    public EstheaderDto? SelectedEstimate { get; set;  }
    [Parameter]
    public EventCallback<AttachEstimateModel> HandleAddSubmit { get; set; }
    [Parameter]
    public string? JobNumber { get; set; }
    [Parameter]
    public int? SalesConfigCoOptionId { get; set; }
    [Parameter]
    public int? SalesConfigOptionId { get; set; }
    public async Task Show()
    {
        IsModalVisible = true;
        JobSelected = "CUSTEST00001";
        SelectedJob = JobNumber;
        MasterOrJobSpecific = new List<string>() { "Use Master Estimates", "Use Job Specific Estimates" };
        //TODO: too slow to load
        var getJobsTask = SubdivisionService.GetAllJobsAsync();
        var getSubdivisionsTask = SubdivisionService.GetSubdivisionsAsync();
        var getEstimatesTask = BudgetService.GetEstimatesForJobAsync(JobSelected);
        await Task.WhenAll(getSubdivisionsTask, getJobsTask, getEstimatesTask);
        var AllJobs = getJobsTask.Result;
        AllJobsForAutoSelect = AllJobs.Value.Select(x => x.JobNumber).ToList();
        EstimateToAdd = new AddCustomEstimateModel();
        var data = getSubdivisionsTask.Result;
        AllSubdivisions = data.Value;
        CustomEstimatesForSelectedJob = getEstimatesTask.Result.Value;
        StateHasChanged();
    }
    protected override async Task OnParametersSetAsync()
    {
       // JobSelected = JobNumber;//Why are there two here??
        SelectedJob = JobNumber;
    }
    protected async Task ChangeUseMasterOptionHandler(object theUserChoice)
    {        
        JobSelected = SelectedMasterOrJobSpecific == "Use Master Estimates" ? "CUSTEST00001" : JobNumber;
        CustomEstimatesForSelectedJob = (await BudgetService.GetEstimatesForJobAsync(JobSelected)).Value;
        //StateHasChanged();
    }
    protected async Task JobSelectedHandler(object theUserChoice)
    {
        if (JobSelected != (string)theUserChoice)
        {
            JobSelected = (string)theUserChoice;
            CustomEstimatesForSelectedJob = (await BudgetService.GetEstimatesForJobAsync(JobSelected)).Value;
        }
    }
    private async void AttachSelectedEstimate()
    {
        submittingStyle = "";
        var attachEstimateModel = new AttachEstimateModel()
        {
              SalesConfigCoOptionsId = SalesConfigCoOptionId,
              SalesConfigOptionsId = SalesConfigOptionId,
              EstHeaderId = SelectedEstimate?.EstheaderId
        };
        var responseItem = await BudgetService.AttachEstimateAsync(attachEstimateModel);
        submittingStyle = "display:none";
        IsModalVisible = false;
        await HandleAddSubmit.InvokeAsync(responseItem.Value);
    }
    protected async Task OnEstimateRowClickHandler(GridRowClickEventArgs args)
    {
        var data = args.Item as EstheaderDto;
        if(data != null)
        {
            SelectedEstimate = data;
        }
    }
    async void CancelAddItem()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;
    }
   
}
