﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class DocumentUploadModel
    {
        public string? JobNumber { get; set; }
        public string? FolderName { get; set; }
        public string? AttachmentType { get; set; }
        public int DocTypeId { get; set; }
        public string? FileName { get; set; }
        public string? Path { get; set; }
        public IFormFile? files { get; set; }
        public bool EmailNotification { get; set; }
        public string? EmailSubject { get; set; }
        public string? EmailBody { get; set; }
        public List<string>? EmailRecipients { get; set; }

    }
}
