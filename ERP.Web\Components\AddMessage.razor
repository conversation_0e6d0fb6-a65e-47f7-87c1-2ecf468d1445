﻿@using ERP.Data.Models.Dto;
@inject SupplierMessagesService SupplierMessagesService

<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="600px"
               Height="600px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        New Message
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@Message" OnValidSubmit="@HandleValidAddSubmit">

            <div class="mb-3">
                <label class="form-label">To</label><br />
                <TelerikMultiSelect 
                    Data="@Contacts"
                    TextField="DropdownDisplay"
                    ValueField="Email"
                    Filterable="true"
                    FilterOperator="@StringFilterOperator.Contains"
                    @bind-Value="@Message.Emails">
                </TelerikMultiSelect>
            </div>
            <div class="mb-3">
                <label class="form-label">From</label><br />
                <TelerikTextBox Enabled = "false" @bind-Value="@Message.FromEmail"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Subject</label><br />
                <TelerikTextBox @bind-Value="@Message.Subject"></TelerikTextBox>
            </div>
            <div class="mb-3">
                <label class="form-label">Mesage</label><br />
                <TelerikTextArea @bind-Value="@Message.Body" Rows="4"></TelerikTextArea>
            </div>
            <div>
                <h4>Select Attachment</h4>
                <TelerikFileSelect AllowedExtensions="@AllowedExtensions"
                                   Multiple="true"
                                   MaxFileSize="@MaxSize"
                                   OnRemove="@OnRemoveHandler"
                                   OnSelect="@OnSelectHandler">
                </TelerikFileSelect>
                <br />
            </div>
            <button type="submit" class="btn btn-primary">Send</button>
            <button type="button" @onclick="CancelAddItem" class="btn btn-secondary">Cancel</button>
            <div style=@submittingStyle>Sending. Please wait...</div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {
    public bool IsModalVisible { get; set; }

    public EmailModel? Message { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    private string submittingStyle = "display:none";

    [Parameter]
    public EmailModel? EmailMessage { get; set; }

    [Parameter]
    public EventCallback<ResponseModel<EmailModel>> HandleSubmit { get; set; }

    public List<ContactDto>? Contacts { get; set; } = new List<ContactDto>();

    public CombinedPOBudgetTreeModel Model { get; set; }

    List<string> AllowedExtensions { get; set; } = new List<string>() { ".pdf", ".doc", ".docx", ".jpeg", ".jpg", ".png", ".xls", ".gif" };

    public int MaxSize { get; set; } =  1024 * 1024; //1 MB

    public async Task Show()
    {
        IsModalVisible = true;
        submittingStyle = "display:none";
        Message.Emails = EmailMessage.Emails;
        Message.FromEmail = EmailMessage.FromEmail;
        Message.Body = EmailMessage.Body;
        Message.Subject = EmailMessage.Subject;
        Message.JobNumber = EmailMessage.JobNumber;
        Message.SupplierNumber = EmailMessage.SupplierNumber;
        Message.SupplierName = EmailMessage.SupplierName;
        Contacts.Clear();
        foreach (var contact in EmailMessage.Contacts)
        {
            if (!string.IsNullOrEmpty(contact.Email))
            {
                Contacts.Add(contact);
            }
        }

        if (Contacts != null)
        {
            foreach (var contact in Contacts)
            {
                contact.DropdownDisplay = $"{contact.FirstName} {contact.LastName} ({contact.Email})";
            }
        }

        StateHasChanged();
    }
    protected override async Task OnParametersSetAsync()
    {
    }

    protected override async Task OnInitializedAsync()
    {
        Message = new EmailModel() { Files = new List<FileModel>() };
    }
    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";
        submittingStyle = "display:none";

        submittingStyle = "";
        var response = await SupplierMessagesService.SendEmail(Message);
        ShowNotification(response.Message, response.IsSuccess);
        submittingStyle = "display:none";
        await HandleSubmit.InvokeAsync(response);
    }

    async void CancelAddItem()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;
    }

    async Task OnSelectHandler(FileSelectEventArgs args)
    {
        foreach (var file in args.Files)
        {
            //add the file to the attachments
            var fileData = new byte[file.Stream.Length];
            await file.Stream.ReadAsync(fileData);
            Message.Files.Add(new FileModel()
                {
                    FileData = fileData,
                    FileName = file.Name
                });
        }
    }

    async Task OnRemoveHandler(FileSelectEventArgs args)
    {
        foreach (var file in args.Files)
        {
            //remove the file to the attachments
            Message.Files.RemoveAll(x => x.FileName == file.Name);
        }
    }

    async void ShowNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
