﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Poapproval
{
    public int PoapprovalId { get; set; }

    public int PoheaderId { get; set; }

    public int? Approvalseq { get; set; }

    public string? Invnumber { get; set; }

    public string? Invdescription { get; set; }

    public double? Invnetamount { get; set; }

    public double? Invtaxamount { get; set; }

    public double? Invretention { get; set; }

    public DateTime? Invdate { get; set; }

    public string? Approvedby { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public string? Notes { get; set; }

    public DateTime? ApproveDate { get; set; }

    public int? VpoApprovalId { get; set; }

    public int PartialPaymentSeq { get; set; }

    public Guid? BcId { get; set; }

    public bool? ExportBc { get; set; }

    public virtual Poheader Poheader { get; set; } = null!;

    public virtual VpoApprovalSeq? VpoApproval { get; set; }
}
