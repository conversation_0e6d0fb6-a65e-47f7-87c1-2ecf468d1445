﻿
@inject ScheduleService ScheduleService
@inject SubdivisionService SubdivisionService

<TelerikWindow Modal="true"
               MinHeight="400px"
               MinWidth="400px"
               @bind-Visible="@IsModalVisible"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <h4 class="page-title">Create a New Schedule from Selected Template</h4>
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@ScheduleToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />    
            <div>
                <label class="form-label">Select Template:  </label>
                <TelerikDropDownList Data="@AvailableTemplates"
                @bind-Value="@SelectedTemplate"
                                     TextField="TemplateName"
                                     ValueField="TemplateId"
                                     DefaultText="Select A Template"
                                     ScrollMode="@DropDownScrollMode.Virtual"
                                     PageSize="10"
                                     Filterable="true"
                                     ItemHeight="35"
                                     FilterOperator="@StringFilterOperator.Contains">

                </TelerikDropDownList>
               
            </div>
            <br />
            <div>
                <label class="form-label">Subdivision:  </label>
                <br />
                <TelerikDropDownList Data="AllSubdivisions"
                                     ScrollMode="@DropDownScrollMode.Virtual"
                                     ItemHeight="45"
                                     TextField="SubdivisionName"
                                     ValueField="SubdivisionId"
                                     OnChange="SubdivisionSelected"
                @bind-Value="@SelectedSubdivision"
                                     Filterable="true"
                                     PageSize="10"
                                     FilterOperator="@StringFilterOperator.Contains">
                </TelerikDropDownList>               
            </div>
            <br />
            <div>
                <label class="form-label">Jobs:  </label>
                <br />
                @* <style>
                    .selected-items-container {
                        max-height: 300px;
                        overflow-y: auto;
                    }
                </style>
                <TelerikMultiSelect Data="JobsForSelect"
                                    Class="selected-items-container"
                                     ScrollMode="@DropDownScrollMode.Virtual"
                                     ItemHeight="40"
                                    Context="multiSelectContext"
                                     @ref="MultiSelectRef"
                                     TextField="JobNumber"
                                     ValueField="JobNumber"
                                      @bind-Value="@SelectedJobs"
                                    TagMode="@MultiSelectTagMode.Multiple"
                                    MaxAllowedTags="5"
                                     Filterable="true"
                                     PageSize="10"
                                    AutoClose="false"
                                    Placeholder="Select Jobs"
                                     FilterOperator="@StringFilterOperator.Contains">
                                      <HeaderTemplate>
                        <label style="padding: 4px 8px;">
                            <TelerikCheckBox TValue="bool"
                                             Value="@IsAllSelected()"
                                             ValueChanged="@( (bool v) => ToggleSelectAll(v) )">
                            </TelerikCheckBox>
                            &nbsp;Select All
                        </label>
                    </HeaderTemplate>
                    <ItemTemplate>
                        <input type="checkbox"
                               class="k-checkbox k-checkbox-md"
                               checked="@GetChecked(multiSelectContext.JobNumber)">
                        @multiSelectContext.JobNumber
                    </ItemTemplate>
                </TelerikMultiSelect>   *@
                <br/>
                <TelerikGrid 
                    Height="300px"
                    RowHeight="40"
                    ScrollMode="GridScrollMode.Virtual"
                             @bind-SelectedItems="@SelectedJobsToSchedule"                           
                             FilterMode="@GridFilterMode.FilterMenu"
                             Data="JobsForSelect"
                             SelectionMode="GridSelectionMode.Multiple">
                    <GridColumns>
                        <GridCheckboxColumn SelectAllMode="GridSelectAllMode.All"></GridCheckboxColumn>
                        <GridColumn Field="JobNumber" Filterable="true"></GridColumn>
                        <GridColumn Field="JobPostingGroup" Filterable="true" FilterMenuType="@FilterMenuType.CheckBoxList"></GridColumn>
                        <GridColumn Field="LotWidth" Filterable="true" FilterMenuType="@FilterMenuType.CheckBoxList"></GridColumn>
                    </GridColumns>
                </TelerikGrid>
            </div>   
            <br />
            <div class="customSeparator">
                <TelerikButton Class="btn btn-primary" ButtonType="ButtonType.Submit" Enabled="@IsAddEnabled">Add</TelerikButton>
                <button type="button" @onclick="CancelAddSchedule" class="btn btn-secondary">Cancel</button>
                <div style=@ShowLoading>Submitting. Please wait...</div>
                <div style=@ShowError>Something went wrong.</div>
            </div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public ScheduleDto ScheduleToAdd { get; set; } = new ScheduleDto();
    public string ShowLoading{ get; set; } = "display:none";
    public string ShowError { get; set; } = "display:none";
    public List<TemplateDto>? AvailableTemplates { get; set; }
    public List<SubdivisionDto>? AllSubdivisions { get; set; } = new List<SubdivisionDto>();
    public List<JobDto>? AllJobs { get; set; } = new List<JobDto>();
    //public int? SelectedTemplate { get; set; }
    private string? SelectedJob { get; set; }
    private string? JobSelected { get; set; }
    public IEnumerable<JobDto>? SelectedJobsToSchedule { get; set; } = Enumerable.Empty<JobDto>();
    public bool IsAddEnabled => SelectedJobsToSchedule.Count() > 0;
    [Parameter]
    public int? SelectedSubdivision { get; set; } = -1;
    private int? CheckSubdivisionSelected { get; set; } = 1;
    public List<JobDto>? JobsForSelect { get; set; } = new List<JobDto>();
    [Parameter]
    public List<string>? SelectedJobs { get; set; } = new List<string>();
    private TelerikMultiSelect<JobDto, string>? MultiSelectRef;
    [Parameter]
    public EventCallback<ScheduleDto> HandleAddSubmit { get; set; }
    [Parameter]
    public int? SelectedTemplate { get; set; }
    protected override async Task OnInitializedAsync()
    {
        var templatesTask = ScheduleService.GetTemplatesAsync();
        var jobsTask = ScheduleService.GetJobsNoScheduleAsync();
        var subdivisionTask = SubdivisionService.GetSubdivisionsAsync();
        await Task.WhenAll(new Task[] { jobsTask, subdivisionTask, templatesTask });
        AvailableTemplates = templatesTask.Result.Value;
        AllJobs = jobsTask.Result.Value;
        AllSubdivisions = subdivisionTask.Result.Value;
        AllSubdivisions.Insert(0, new SubdivisionDto() { SubdivisionName = "All", SubdivisionId = -1 });
    }
    protected override async Task OnParametersSetAsync()
    {
        if(SelectedSubdivision != null)
        {
            if (SelectedSubdivision == -1)
            {
                JobsForSelect = AllJobs;//All subdvisions
            }
            else
            {
                JobsForSelect = AllJobs.Where(x => x.SubdivisionId == SelectedSubdivision).ToList();//filter jobs by subdivision
            }
        }
    }
    public void Show()
    {
        IsModalVisible = true;
    }      
    protected async Task SubdivisionSelected(object theUserChoice)
    {
        if (theUserChoice != null && SelectedSubdivision != CheckSubdivisionSelected)
        {
            CheckSubdivisionSelected = SelectedSubdivision;
            if (SelectedSubdivision == -1)
            {
                JobsForSelect = AllJobs;//All subdvisions
            }
            else
            {
                JobsForSelect = AllJobs.Where(x => x.SubdivisionId == SelectedSubdivision).ToList();//filter jobs by subdivision
            }
            SelectedJobs = new List<string>();//clear the selections if subdivision changed
        }
    }
    private async void HandleValidAddSubmit()
    {
        ShowLoading = "";
        var response = new ScheduleDto();
        ScheduleToAdd.TemplateId = SelectedTemplate;
        List<ScheduleDto> schedulesToAdd = new List<ScheduleDto>();
        foreach(var job in SelectedJobsToSchedule)
        {
            schedulesToAdd.Add(new ScheduleDto()
                {
                    JobNumber = job.JobNumber,
                    TemplateId = SelectedTemplate
                });
        }
        var response2 = await ScheduleService.GenerateSchedulesFromTemplateAsync(schedulesToAdd);
        if (response2.IsSuccess)
        {
            response = response2.Value.FirstOrDefault();//TODO: fix
                                                        // response = await ScheduleService.GenerateScheduleFromTemplateAsync(ScheduleToAdd);
            ShowLoading = "display:none";

            await HandleAddSubmit.InvokeAsync(response);
        }
        else
        {
            //TODO: show error message
        }
    }
    async void CancelAddSchedule()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;        
    }
    bool IsAllSelected()
    {
        return SelectedJobs.Count == JobsForSelect.Count;
    }

    // for the item checkboxes
    bool GetChecked(string selected)
    {
        return SelectedJobs.Contains(selected);
    }
    void ToggleSelectAll(bool selectAll)
    {
        SelectedJobs.Clear();

        if (selectAll)
        {
            SelectedJobs.AddRange(JobsForSelect.Select(x => x.JobNumber));
        }

        MultiSelectRef.Rebind();
    }
}
