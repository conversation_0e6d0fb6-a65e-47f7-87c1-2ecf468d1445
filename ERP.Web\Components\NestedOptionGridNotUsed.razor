﻿@using ERP.Data.Models;
@inject SalesPriceService SalesPriceService

<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }
    /*.mytreeclass .k-drag-cell {
            display:none
        }*/
    .k-drag-clue {
       /* background-color: rgb(244,97,71);*/
        color: white;
    }
</style>
<TelerikGrid Data="@OptionData"
             Width="100%"
             SelectionMode="@GridSelectionMode.Multiple"
             RowDraggable="true"
             Pageable="true"
             PageSize="10"
             OnRowDrop="@((GridRowDropEventArgs<WorksheetTreeModel> args) => OnGridRowDropHandler(args))"
                 >
    <GridColumns>
        <GridColumn Field="OptionId" Visible="false"></GridColumn>
        <GridColumn Field="OptionCode" Title="Code"></GridColumn>
        <GridColumn Field="OptionName" Title="Desc"></GridColumn>
    </GridColumns>
</TelerikGrid>


@code {

    [Parameter]
    public int? PhasePlanId { get; set; }

    public List<WorksheetTreeModel>? OptionData;
    
    [Parameter]
    public EventCallback<GridRowDropEventArgs<WorksheetTreeModel>> HandleDrop { get; set; }


    protected override async Task OnParametersSetAsync()
    {
        //get options in plan
        var getOptionData = await SalesPriceService.GetAvailablePlanOptionsByPlanAsync((int)PhasePlanId);
        OptionData = getOptionData.Value.Select(x => new WorksheetTreeModel()
            {
                Id = Guid.NewGuid(),
                PlanId = PhasePlanId,
                WorksheetOptionId = x.PlanOptionId,
                OptionName = x.ModifiedOptionDesc,
                OptionCode = x.OptionCode,
                HasChildren = false
            }).ToList();
    }   

    private async Task OnGridRowDropHandler(GridRowDropEventArgs<WorksheetTreeModel> args)
    {
        await HandleDrop.InvokeAsync(args);
    }
}
