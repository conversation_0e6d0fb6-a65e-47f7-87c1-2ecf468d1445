﻿@page "/operationsjob"
@inject SalesConfigService SalesConfigService
@inject SelectedOptionsService SelectedOptionsService
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject AuthenticationStateProvider AuthenticationStateProvider
@implements IDisposable
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, Operations")]
@using System.Collections.ObjectModel
@using Telerik.DataSource.Extensions
@using ERP.Data.Models

<style>
    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto", sans-serif;
        font-size: .75rem;
        position: relative;
        z-index: 10000 !important;
    }

    .k-notification-group{
        position: relative;
        z-index: 10000 !important;
    }

    .k-notification .telerik-blazor .k-notification-closable {
        position: relative;
        z-index: 10000 !important;
    }

    .k-notification-content {
        position: relative;
        z-index: 10000 !important;
    }

    .mostRecent .k-grid-checkbox {
       color: mediumpurple;
    }
    .structuralOption {
        background-color: lightblue !important;
    }

    .nonStructuralOption {
        background-color: lightyellow !important;
    }
    .mostRecent {
        color: mediumpurple;
    }
    .notRecent .k-grid-checkbox {
       display:none;
    }

    .tile-with-overflow .k-tilelayout-item-body {
        overflow: auto;
    }
    .k-card-header {
        padding-block: 0;
        padding-inline : 0;
    }

    .custom-dropdown {
        width: 250px;
    }

    .low-Z {
        position:relative;
        z-index:500 !important;
    }

}
</style>

<PageTitle>Operations</PageTitle>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>

<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="false" ShowJob="true" ShowSubdivision="true"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
<TelerikTooltip TargetSelector=".tooltip-target" />

<div class="container-fluid">
    <div>

        @if (JobSelected == null && !loading)
        {
            <p>Select a job to see data</p>
        }
        else if(loading)
        {
            <p><em>Loading...</em></p>
            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" />

        }        
        else
        {
            <TelerikTileLayout Columns="8"                
                               ColumnWidth="300px"
                               RowHeight="250px"
                               Reorderable="true"
                               Resizable="true"
                               OnResize="@ItemResize"
                               Class="low-Z">

                <TileLayoutItems>
                    <TileLayoutItem HeaderText="Selected Options" ColSpan="6" RowSpan="2" Class="tile-with-overflow">
                        <HeaderTemplate>
                            <div class="card" style="background-color:#2e5771">
                                <div class="card-body" style="padding:0.5rem">
                                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Selected Options</h7>
                                </div>
                            </div>
                        </HeaderTemplate>
                        <Content>
                            <ApproveSelectedOptions JobNumber="@JobSelected"></ApproveSelectedOptions>
                        </Content>
                    </TileLayoutItem>
                    <TileLayoutItem HeaderText="Customer" ColSpan="2" RowSpan="2" Class="tile-with-overflow">
                        <HeaderTemplate>
                            <div class="card" style="background-color:#2e5771">
                                <div class="card-body" style="padding:0.5rem">
                                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Customer</h7>
                                </div>
                            </div>
                        </HeaderTemplate>
                        <Content>
                            <CustomerData JobNumber="@JobSelected" HandlePushCustomerSubmit="HandlePushCustomerDataResponse" HandlePushCustomerNavSubmit="HandlePushCustomerDataNavResponse" />
                        </Content>
                    </TileLayoutItem>
                    <TileLayoutItem HeaderText="Lot Details" ColSpan="6" RowSpan="2" Class="tile-with-overflow">
                        <HeaderTemplate>
                            <div class="card" style="background-color:#2e5771">
                                <div class="card-body" style="padding:0.5rem">
                                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Lot Details</h7>
                                </div>
                            </div>
                        </HeaderTemplate>
                        <Content>
                            <JobDetails @ref="JobDetailsRef" JobNumber=@JobSelected></JobDetails>
                        </Content>
                    </TileLayoutItem>
                    <TileLayoutItem HeaderText="Release" ColSpan="2" RowSpan="2" Class="tile-with-overflow low-Z">
                        <HeaderTemplate>
                            <div class="card" style="background-color:#2e5771">
                                <div class="card-body" style="padding:0.5rem">
                                    <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Schedule Release</h7>
                                </div>
                            </div>
                        </HeaderTemplate>
                        <Content>
                            <ReleaseSchedule JobNumber="@JobSelected"></ReleaseSchedule>
                        </Content>
                    </TileLayoutItem>

                </TileLayoutItems>
            </TelerikTileLayout>

        }
    </div>
</div>

@code {


    private string? JobSelected { get; set; }
    private bool loading { get; set; } = true;

    public JobDetails? JobDetailsRef { get; set; }

    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        //TODO: if no job selected, show jobs with recent approved options
        SubdivisionJobPickService.OnChanged += JobChangedHandler;
        if (SubdivisionJobPickService.JobNumber != null)
        {
            JobSelected = SubdivisionJobPickService.JobNumber;
            StateHasChanged();
        }
        await Task.Delay(1); // to display loader until the UI components have been rendered
        loading = false;
    }
    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= JobChangedHandler;
    }
    async Task JobChangedHandler()
    {
        var selected = SubdivisionJobPickService.JobNumber;
        if (JobSelected != selected)//check if actual change, so not calling this multiple times
        {            
            JobSelected = selected;          
            StateHasChanged();
        }
    }

    async Task HandlePushCustomerDataResponse(ResponseModel<string> response)
    {
        if (response.IsSuccess)
        {
            //TODO: show indication if customer data already pushed
            if (JobSelected != null)
            {
                //need to tell the job compoenent to refresh here               
            }
            await JobDetailsRef.RefreshData();
        }
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
    }
    async Task HandlePushCustomerDataNavResponse(ResponseModel<byte[]> response)
    {
        if (response.IsSuccess)
        {
            //TODO: show indication if customer data already pushed
            if (JobSelected != null)
            {
                //need to tell the job compoenent to refresh here
            }
            await JobDetailsRef.RefreshData();
        }
        ShowSuccessOrErrorNotification(response.Message, response.IsSuccess);
    }
    private void ShowSuccessOrErrorNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
    void ItemResize()
    {
        //ConversionsRef.Refresh();
        //VisitorsRef.Refresh();
        //MostVisitedPagesRef.Refresh();
        //PageViewsRef.Refresh();
    }
    
}
