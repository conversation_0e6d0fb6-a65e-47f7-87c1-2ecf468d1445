﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public record CombinedJCETreeModel
    {
        //TODO: make an interface instead

        public Guid Id { get; set; }

        public string? JobNumber { get; set; }
        public EstactivityDto? Estactivity { get; set; } 
        public MasterItemDto? MasterItems { get; set; }
        public CostDto? Costs { get; set; }
        public EstjcedetailDto? Estjcedetail { get; set; }

        public EstheaderDto? Estheader { get; set; }
        public EstoptionDto? Estoption { get; set; }
        public PoheaderDto? Poheader { get; set; }
        public PodetailDto? Podetail { get; set; }
        public PojccdetailDto? Pojccdetail { get; set; }
        public PoapprovalDto? Poapproval { get; set; }
        public SupplierDto? Supplier { get; set; }
        public Guid? ParentId { get; set; }
        public CombinedJCETreeModel? Parent { get; set; }
        public List<CombinedJCETreeModel>? Children { get; set; }
        public bool HasChildren { get; set; }      
        public bool? IsActive { get; set; }
        public decimal? Amount { get; set; }
        public bool BoolExported { get; set; }
        public string? SearchTags { get; set; } // to make search work for children in TreeList
    }
}
