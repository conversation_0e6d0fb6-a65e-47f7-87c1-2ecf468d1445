﻿@page "/managecost"
@inject OptionService OptionService
@inject SubdivisionService SubdivisionService
@inject ItemService ItemService
@inject PlanService PlanService
@inject CostService CostService
@inject PoService POService
@inject NavigationManager NavManager
@inject TradeService TradeService
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JSRuntimeService
@implements IDisposable
@attribute [Authorize(Roles = "Admin, Purchasing, Scheduling, ReadOnly, Accounting")]
@using System.Collections.ObjectModel
@using ERP.Data.Models.Dto;
@using Telerik.Documents.SpreadsheetStreaming;
@using Telerik.Documents;
@using DocumentProcessing
<style type="text/css">
    .k-window-title {
        font-weight: bold;
    }

    /* Gutter */
    .row > * {
        padding-right: calc(var(--bs-gutter-x) * .10);
        padding-left: calc(var(--bs-gutter-x) * .10);
    }
    /*.mybutton{
        width: 50%;
    }*/
    .MyTelerikNotification .k-notification-container .k-notification {
        font-family: "Roboto",sans-serif;
        font-size: .75rem;
    }
</style>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<PageTitle>Purchasing | Costs</PageTitle>
<TelerikLoaderContainer Visible="@IsLoading" Text="Please wait..." />
<ERP.Web.Components.SubdivisionJobPickerMenuBar ShowSupplier="true" ShowJob="false" ShowSubdivision="false"></ERP.Web.Components.SubdivisionJobPickerMenuBar>
<TelerikTooltip TargetSelector=".tooltip-target" />

<div class="col-lg-12">
    <div class="card" style="background-color:#2e5771">
        <div class="card-body" style="padding:0.5rem">
            <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Manage Costs</h7>
        </div>
    </div>
</div>

<div class="container-fluid flex">
    <br />
    <div class="row d-flex">
        <TelerikTabStrip>
            <TabStripTab Title="Costs">
                @* <div class="row">
                    <div class="col-lg-5 mb-2">
                        <h4>Manage Costs</h4>
                        <TelerikButton ButtonType="ButtonType.Button" Class="mybutton k-button-success" OnClick="@RolloverCosts">Rollover All Costs</TelerikButton>                       
                    </div>                 
                </div> *@
                <div class="row">
                    <div class="col-2">
                        <div class="card-header" style="padding:4px; margin-bottom:4px">
                            <div style="text-align:center">
                                <h7 class="page-title" style="font-weight:bold">Purchasing Activity</h7>
                            </div>
                        </div>
                        @if (PurchasingActivityData == null)
                        {
                            <div style=@loadingItemStyle>Loading...</div>
                            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=true />
                        }
                        else
                        {
                            <TelerikGrid Data=@PurchasingActivityData
                            FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                            Height="1300px" RowHeight="60" PageSize="20" 
                            ScrollMode="@GridScrollMode.Virtual"
                            Sortable="true"
                            Resizable="true"
                            Reorderable="true"
                            Groupable="false"
                            OnRowClick="@OnActivitySelectedHandler"
                            SelectionMode="GridSelectionMode.Single"
                            ConfirmDelete="true">
                                <GridColumns>
                                    <GridColumn Field="Activity" Title="Activity" Editable="false" Groupable="true" />
                                    <GridColumn Field="PactivityId" Visible="false" />
                                </GridColumns>
                                <GridToolBarTemplate>
                                    <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                    @if(AllowEdit)
                                    {
                                        <TelerikButton ButtonType="ButtonType.Button" Class="mybutton k-button-success" OnClick="@RolloverCosts">Rollover All Costs</TelerikButton>
                                    }

                                </GridToolBarTemplate>
                            </TelerikGrid>
                        }
                    </div>
                    <div class="col-10">
                        <div class="row">
                            <div class="card-header" style="padding:4px; margin-bottom:4px">
                                <div style="text-align:center">
                                    <h7 class="page-title" style="font-weight:bold">Items for Activity: @SelectedActivity?.Activity</h7>
                                </div>
                            </div>
                            @if (SelectedActivity == null || SelectedActivity.PactivityId == 0)
                            {
                                <p><em>Select an activity to see items</em></p>

                            }
                            else if (ItemData == null)
                            {
                                <div style=@loadingItemStyle>Loading...</div>
                            }
                            else
                            {
                                <TelerikGrid Data=@ItemData
                                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                Height="300px" RowHeight="40" PageSize="20"
                                Pageable="true"
                                Sortable="true"
                                Resizable="true"
                                Reorderable="true"
                                Groupable="true"
                                SelectionMode="GridSelectionMode.Multiple"
                                SelectedItemsChanged="@((IEnumerable<ModelManagerItemModel> myItems) => ItemsSelectedHandler(myItems))"
                                EditMode="@GridEditMode.Inline"
                                ConfirmDelete="true"
                                @ref="@ItemGridRef">
                                    <GridColumns>
                                        <GridCheckboxColumn Width="35px" />
                                        <GridColumn Field="PhaseCode" Title="Phase Code" Editable="false" Groupable="true" />
                                        <GridColumn Field="ItemNumber" Title="Item Number" Editable="false" Groupable="false" />
                                        <GridColumn Field="ItemDesc" Title="Item" Editable="true" Groupable="false" />
                                        <GridColumn Field="PhaseDesc" Title="Description" Editable="false" Groupable="false" />
                                        <GridColumn Field="MasterItemId" Visible="false" />
                                        <GridColumn Field="TakeoffUnit" Title="Unit" Editable="false" Groupable="false" />
                                        <GridColumn Field="IsLumpSum" Title="Lump Sum" Editable="false" Groupable="false" />
                                        @*<GridColumn Field="IsPlanSpecific" Title="PlanSpecfic" Editable="false" Groupable="false" />*@
                                        @*                        <GridColumn Field="PhaseDesc" Title="Phase Desc" Editable="true" Groupable="false" />*@

                                    </GridColumns>
                                    <GridToolBarTemplate>
                                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                    </GridToolBarTemplate>
                                    <GridSettings>
                                        <GridPopupEditSettings Width="400px"
                                        Height="600px"
                                        Title="Option">
                                        </GridPopupEditSettings>
                                    </GridSettings>
                                </TelerikGrid>

                            }
                        </div>
                        <div class="row">
                            <div class="card-header" style="padding:4px; margin-bottom:4px">
                                <div style="text-align:center">
                                    <h7 class="page-title" style="font-weight:bold">Costs for Selected Items</h7>
                                </div>
                            </div>
                            @if (SelectedItems == null || SelectedItems.Count() == 0)
                            {
                                <p><em>Select an item to see costs</em></p>

                            }
                            else if (CostData == null)
                            {
                                <div style=@loadingItemStyle>Loading...</div>
                            }
                            else
                            {
                                <TelerikGrid Data=@CostData
                                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                                Height="1000px" RowHeight="60" PageSize="20"
                                Sortable="true"
                                Resizable="true"
                                Reorderable="true"
                                Groupable="true"
                                SelectionMode="GridSelectionMode.Multiple"
                                @bind-SelectedItems="SelectedCosts"
                                EditMode="@GridEditMode.Inline"
                                OnUpdate="@UpdateCostHandler"
                                OnEdit="@EditCostHandler"
                                OnDelete="@DeleteCostHandler"
                                OnCancel="@CancelCostHandler"
                                ConfirmDelete="true"
                                @ref="@CostGridRef">
                                    <GridExport>
                                        <GridExcelExport FileName="telerik-grid-export" AllPages="true" />
                                    </GridExport>
                                    <GridColumns>
                                        <GridCheckboxColumn />
                                        <GridColumn Field="MasterItemId" Title="Item Id" Editable="false" Groupable="false" Visible="false" />
                                        <GridColumn Field="ItemNumber" Title="Item Number" Editable="false" Groupable="false" />
                                        <GridColumn Field="ItemDesc" Title="Item Desc" Editable="false" Groupable="false" />
                                        <GridColumn Field="SupplierName" Title="Supplier Name" Editable="false" Groupable="true" />
                                        <GridColumn Field="SubdivisionId" Title="Subdivision Id" Visible="false" Editable="false" Groupable="false" />
                                        <GridColumn Field="SubdivisionName" Title="Subdivision" Editable="false" Groupable="true" />
                                        <GridColumn Field="Unit" Title="Unit" Editable="false" Groupable="false" />
                                        <GridColumn Field="EditType" Title="Edit Type" Editable="true" Groupable="false" Width="0">
                                            <EditorTemplate>
                                                @{
                                                    var SelectedCost = context as CostModel;
                                                    <TelerikDropDownList @bind-Value="@SelectedCost.EditType"
                                                    Data="@EditTypeOptions"
                                                    Width="100%">
                                                        <DropDownListSettings>
                                                            <DropDownListPopupSettings Height="auto"></DropDownListPopupSettings>
                                                        </DropDownListSettings>
                                                    </TelerikDropDownList>
                                                }
                                            </EditorTemplate>
                                        </GridColumn>
                                        <GridColumn Field="UnitCost" Title="Current Cost" DisplayFormat="{0:C2}" Editable="true" Groupable="false" />
                                        <GridColumn Field="LastCostExpired" Title="Cost Start Date" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Groupable="false" />
                                        <GridColumn Field="LastCost1" Title="Last Cost" DisplayFormat="{0:C2}" Editable="false" Groupable="false" />
                                        <GridColumn Field="LastCost2Expired" Title="Last Cost Start Date" DisplayFormat="{0:MM/dd/yyyy}" Editable="false" Groupable="false" />
                                        <GridColumn Field="NextCost" Title="Next Cost" DisplayFormat="{0:C2}" Editable="true" Groupable="false" />
                                        <GridColumn Field="NextCostDue" Title="Next Cost Start" DisplayFormat="{0:MM/dd/yyyy}" Editable="true" Groupable="false" />
                                        @*  <GridColumn Field="NextCost2" Title="Next Cost2" Editable="true" Groupable="false" />
                                    <GridColumn Field="NextCost2Due" Title="Next Cost2 Start" Editable="true" Groupable="false" />     *@
                                        <GridColumn Field="Warrantyitem" Title="Warranty Applies" Editable="true" Groupable="false" Width="0" />
                                        <GridColumn Field="Warrantytype" Title="Warranty Type" Editable="true" Groupable="false" Width="0" />
                                        <GridColumn Field="Warrantydays" Title="Warranty Period" Editable="true" Groupable="false" Width="0" />
                                        <GridColumn Field="ItemTaxGroup" Title="Item Tax Group" Editable="true" Groupable="false" Width="0" />
                                        <GridColumn Field="SupProductCode" Title="Supplier Product Code" Editable="true" Groupable="false" Width="0" />
                                        <GridCommandColumn>
                                            @if(AllowEdit)
                                            {
                                                var item = context as CostModel;

                                                <GridCommandButton Command="Save" Icon="@FontIcon.Save" ShowInEdit="true" Class="k-button-success">Update</GridCommandButton>
                                                //if (item.IsSupplierBlocked != true)
                                                if (item.IsSupplierActive != false)
                                                {
                                                    <GridCommandButton Command="Edit" Icon="@FontIcon.Pencil" Class="k-button-success"></GridCommandButton>
                                                }
                                                else
                                                {
                                                    <span title="Supplier is Inactive" class="tooltip-target">
                                                        <TelerikFontIcon Icon="@FontIcon.InfoCircle" />
                                                    </span>
                                                }
                                                <GridCommandButton Command="Delete" Icon="@FontIcon.Trash" Class="k-button-danger"></GridCommandButton>
                                                <GridCommandButton Command="Cancel" Icon="@FontIcon.Cancel" ShowInEdit="true">Cancel</GridCommandButton>
                                            }
                                        </GridCommandColumn>
                                    </GridColumns>
                                    <GridToolBarTemplate>
                                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                                        @if(AllowEdit)
                                        {
                                            <GridCommandButton Command="AddCostCommand" Icon="@FontIcon.Plus" OnClick="@AddCostFromToolbar" Class="k-button-add">Add Cost</GridCommandButton>
                                            <GridCommandButton Command="Delete Selected" Icon="@FontIcon.Trash" OnClick="@DeleteCostsHandler" Class="k-button-add">Delete Selected</GridCommandButton>
                                        }                                        
                                        @*<GridCommandButton Command="ExcelExport" Icon="@FontIcon.FileExcel">Export to Excel</GridCommandButton>
                                    <GridCommandButton Command="CsvExport" Icon="@FontIcon.FileCsv">Export to CSV</GridCommandButton>*@
                                    </GridToolBarTemplate>
                                    <GridSettings>
                                        <GridPopupEditSettings Width="400px"
                                        Height="600px"
                                        Title="Cost">
                                        </GridPopupEditSettings>
                                    </GridSettings>
                                </TelerikGrid>

                            }
                        </div>
                    </div>
                    @*  <div class="col-3">

                    </div>
                    <div class="col-lg-7">

                    </div> *@
                </div>
            </TabStripTab>
            @if(AllowEdit)
            {
                <TabStripTab Title="Import">
                    <div class="row">
                        <h4>Import Costs</h4>
                        <div class="card col-6">
                            <div class="card-body">
                                <h4>Select File</h4>
                                <TelerikFileSelect AllowedExtensions="@AllowedExtensions"
                                Multiple="false"
                                MaxFileSize="@MaxSize"
                                OnRemove="@OnRemoveHandler"
                                OnSelect="@OnSelectHandler">
                                    <SelectFilesButtonTemplate>
                                        <TelerikFontIcon Icon="@FontIcon.Upload" />
                                        Click to Select File for Upload
                                    </SelectFilesButtonTemplate>
                                </TelerikFileSelect>
                                <br />
                                <EditForm Model="@CostsToImport" OnValidSubmit="@HandleApplyCost">
                                    <DataAnnotationsValidator />
                                    <ValidationSummary />
                                    <h4>Vendor</h4>
                                    <TelerikDropDownList @bind-Value="@CostsToImport.SubNumber"
                                    Data="@SupplierData"
                                    ScrollMode="@DropDownScrollMode.Virtual"
                                    ItemHeight="40"
                                    PageSize="20"
                                    TextField="SubName"
                                    ValueField="SubNumber"
                                    DefaultText="Select Supplier"
                                    Filterable="true"
                                    FilterOperator="StringFilterOperator.Contains"
                                    Width="100%">
                                    </TelerikDropDownList>
                                    <br />
                                    <hr />
                                    @*<h4 class="page-title">Select File</h4>
                            <TelerikUpload AllowedExtensions="@AllowedExtensions"
                            Multiple="false"
                            MaxFileSize="@MaxSize"
                            SaveUrl="api/costs/ImportExcel" />
                            <br />*@
                                    <div class="row">
                                        <div class="col-3">
                                            <h4>Effective Date</h4>
                                            <TelerikRadioGroup Data="@EffectiveDateTypes"
                                            @bind-Value="@CostsToImport.CostEditType" OnChange="@ToggleDatePickVisible"></TelerikRadioGroup>
                                            <div style=@DatePickVisible>
                                                <TelerikDatePicker @bind-Value="@CostsToImport.EffectiveDate" Min="DateTime.Now"></TelerikDatePicker>
                                            </div>
                                        </div>
                                        <div class="col-3">
                                            <h4 class="page-title">Set as Division Default?</h4>
                                            <TelerikCheckBox @bind-Value="@ApplyAsDivisionDefault" OnChange="@TogglePickSubdivisionVisible"></TelerikCheckBox>
                                            <br />
                                        </div>
                                        <div class="col-4">
                                            <div style="@RemoveAreaCostsVisible">
                                                <h4 class="page-title">Remove Subdivision Costs?</h4>
                                                <TelerikCheckBox @bind-Value="@RemoveAreaCosts"></TelerikCheckBox>
                                                <br />
                                            </div>
                                            <div style=@PickSubdivisionVisible>
                                                <h4 class="page-title">Select Subdivision</h4>
                                                <TelerikMultiSelect @bind-Value="@SelectedSubdivisions"
                                                Data="@AllSubdivisions"
                                                TextField="SubdivisionName"
                                                ValueField="SubdivisionId"
                                                Placeholder="Select Subdivision"
                                                TagMode="@MultiSelectTagMode.Multiple"
                                                MaxAllowedTags="5"
                                                Filterable="true"
                                                FilterOperator="StringFilterOperator.Contains"
                                                @ref="MultiSelectRef"
                                                Width="100%">
                                                    <HeaderTemplate>
                                                        <label style="padding: 4px 8px;">
                                                            <TelerikCheckBox TValue="bool"
                                                            Value="@IsAllSelected()"
                                                            ValueChanged="@( (bool v) => ToggleSelectAll(v) )">
                                                            </TelerikCheckBox>
                                                            &nbsp;Select All
                                                        </label>
                                                    </HeaderTemplate>
                                                </TelerikMultiSelect>
                                            </div>
                                        </div>
                                    </div>
                                    <br />
                                    <br />
                                    <br />
                                    <div>@ApplyingCostsMessage</div>
                                    <button type="submit" class="telerik-blazor k-button k-button-solid k-rounded-md k-button-md k-button-success">Apply costs</button>
                                </EditForm>
                            </div>
                        </div>
                    </div>
                </TabStripTab>
            }            
            <TabStripTab Title="Export">
                <div class="row">
                    <h4>Export Costs</h4>
                    <div class="card col-6">
                        <div class="card-body">
                            @*                         <h4 class="page-title">Select Supplier</h4>
                        @if (SupplierData == null)
                        {
                            <p><em>Loading...</em></p>
                            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=true />
                        }
                        else
                        {
                            <TelerikDropDownList @bind-Value="@SelectedSupplierId"
                                                 Data="@SupplierData"
                                                 ScrollMode="@DropDownScrollMode.Virtual"
                                                 ItemHeight="40"
                                                 PageSize="20"
                                                 TextField="SubName"
                                                 ValueField="SubNumber"
                                                 DefaultText="Select Supplier"
                                                 Filterable="true"
                                                 FilterOperator="StringFilterOperator.Contains"
                                                 Width="100%">
                            </TelerikDropDownList>
                        }
                        <br />
                        <hr /> *@
                            <div class="row">
                                <div class="col-6">
                                    <h4 class="page-title">Select Purchasing Activities</h4>
                                    @if (PurchasingActivityData == null)
                                    {
                                        <p><em>Loading...</em></p>
                                        <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=true />
                                    }
                                    else
                                    {
                                        <TelerikMultiSelect @bind-Value="@SelectedActivityIds"
                                        Context="multiSelectContext"
                                        Data="@PurchasingActivityData"
                                        ScrollMode="@DropDownScrollMode.Virtual"
                                        ItemHeight="30"
                                        PageSize="30"
                                        TextField="Activity"
                                        ValueField="PactivityId"
                                        ClearButton="true"
                                        Filterable="true"
                                        FilterOperator="StringFilterOperator.Contains"
                                        AutoClose="false"
                                        @ref="MultiSelectExportRef"
                                        Width="100%">
                                            <HeaderTemplate>
                                                <label style="padding: 4px 8px;">
                                                    <TelerikCheckBox TValue="bool"
                                                    Value="@IsAllExportSelected()"
                                                    ValueChanged="@( (bool v) => ToggleSelectAllExport(v) )">
                                                    </TelerikCheckBox>
                                                    &nbsp;Select All
                                                </label>
                                            </HeaderTemplate>
                                            <ItemTemplate>
                                                <input type="checkbox"
                                                class="k-checkbox k-checkbox-md"
                                                checked="@GetChecked(multiSelectContext)">
                                                @multiSelectContext.Activity
                                            </ItemTemplate>
                                        </TelerikMultiSelect>
                                    }
                                </div>
                                <br />
                                <div class="col-1"></div>
                                <div class="col-4">
                                    <h4 class="page-title">Select Effective Date</h4>
                                    <TelerikDatePicker @bind-Value="@SelectedDate"></TelerikDatePicker>
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="col-5">
                                    <h4 class="page-title">Division Default Costs</h4>
                                    <TelerikCheckBox @bind-Value="DivisionDefaultCosts" OnChange="@TogglePickSubdivisionExportVisible"></TelerikCheckBox>

                                    <br />

                                    <div style="@PickSubdivisionExportVisible">
                                        <h4 class="page-title">Select Subdivision</h4>
                                        @if (AllSubdivisions == null)
                                        {
                                            <p><em>Loading...</em></p>
                                            <TelerikLoader Size="@ThemeConstants.Loader.Size.Medium" Type="@LoaderType.ConvergingSpinner" Visible=true />
                                        }
                                        else
                                        {
                                            <TelerikDropDownList @bind-Value="@SelectedSubdivisionId"
                                            Data="@AllSubdivisions"
                                            TextField="SubdivisionName"
                                            ValueField="SubdivisionId"
                                            DefaultText="Select Subdivision"
                                            ScrollMode="@DropDownScrollMode.Virtual"
                                            ItemHeight="40"
                                            PageSize="20"
                                            Filterable="true"
                                            Width="100%">
                                            </TelerikDropDownList>
                                        }
                                        <br />
                                        <h4 class="page-title">Include Division Default Items Cost?<br /> (only if subdivision is picked)</h4>
                                        <TelerikCheckBox @bind-Value="IncludeDivisionDefaultCosts"></TelerikCheckBox>
                                        <br />
                                    </div>
                                </div>                        
                                <div class="col-3">
                                    <h4 class="page-title">Include Items with no Cost?</h4>
                                    <TelerikCheckBox @bind-Value="IncludeNoCostItems"></TelerikCheckBox>
                                    <br />
                                </div>
                                <div class="col-3">
                                    <h4 class="page-title">Only Items with no Cost?</h4>
                                    <TelerikCheckBox @bind-Value="OnlyItemsWithoutCosts"></TelerikCheckBox>
                                </div>
                            </div>
                            <br />
                            <br />
                            <p>
                                <TelerikButton Icon="@FontIcon.FileExcel" OnClick="@DownloadExcelFile" Class="k-button-success">Export to Excel</TelerikButton>
                            </p>
                        </div>
                    </div>
                </div>
            </TabStripTab>
        </TelerikTabStrip>            
    </div>   
</div>
<ERP.Web.Components.AddItemCost @ref="AddCostModal"  Items="@SelectedItems?.ToList()" SupplierNumber=@SelectedSupplierNumber HandleAddSubmit="HandleValidAddCostSubmit"></ERP.Web.Components.AddItemCost>
@* <ERP.Web.Components.CopyCostsToSubdivision @ref="CopyCostModal"  HandleAddSubmit="HandleValidCopyCostSubmit"></ERP.Web.Components.CopyCostsToSubdivision> *@
@code {
    private TelerikGrid<PactivityModel>? ActivityGridRef { get; set; }
    private TelerikGrid<ModelManagerItemModel>? ItemGridRef { get; set; }
    private TelerikGrid<CostModel>? CostGridRef { get; set; }
    public List<PactivityModel>? PurchasingActivityData { get; set; }
    // public List<ModelManagerItemModel>? ItemData { get; set; }
    private ObservableCollection<ModelManagerItemModel>? ItemData { get; set; } = new ObservableCollection<ModelManagerItemModel>();
    public ObservableCollection<CostModel>? CostData { get; set; } = new ObservableCollection<CostModel>();
    public PactivityModel? SelectedActivity { get; set; } = new PactivityModel();
    public ModelManagerItemModel? SelectedItem { get; set; }
    public IEnumerable<ModelManagerItemModel>? SelectedItems { get; set; } = Enumerable.Empty<ModelManagerItemModel>();
    public List<SupplierDto>? SupplierData { get; set; }
    public int? SelectedSupplierNumber { get; set; }
    public int SelectedActivityId { get; set; }
    private string loadingItemStyle = "display:none";
    private string loadingCostsStyle = "display:none";
    public bool IsLoadingItem { get; set; } = false;
    public bool IsLoadingCost { get; set; } = false;
    public bool IsLoadingActivity { get; set; } = false;
    public bool IsLoading { get; set; } = false;
    protected ERP.Web.Components.AddItemCost? AddCostModal { get; set; }
    // protected ERP.Web.Components.CopyCostsToSubdivision? CopyCostModal { get; set; }
    public string? ErrorMessage;
    public bool? ShowError;
    public List<CostEditType> EditTypeOptions = new List<CostEditType>();
    List<string> AllowedExtensions { get; set; } = new List<string>() { ".xlsx" };
    public int MaxSize { get; set; } = 10 * 1024 * 1024; //10 MB
    public List<int>? SelectedSubdivisions;
    public List<SubdivisionDto>? AllSubdivisions { get; set; }
    public bool ApplyAsDivisionDefault { get; set; } = true;
    public bool RemoveAreaCosts { get; set; } = false;
    public List<CostEditType> EffectiveDateTypes = new List<CostEditType>();
    public CostEditType? SelectedEffectiveDateType { get; set; } = CostEditType.Current;
    public DateTime? SelectedDate { get; set; } = DateTime.Now;
    public List<CostModel>? ImportedCosts { get; set; } = new List<CostModel>();
    private string DatePickVisible { get; set; } = "display:none";
    private string PickSubdivisionVisible { get; set; } = "display:none";
    private string PickSubdivisionExportVisible { get; set; } = "display:none";
    private string RemoveAreaCostsVisible { get; set; } = "";
    private TelerikMultiSelect<SubdivisionDto, int>? MultiSelectRef;
    private TelerikMultiSelect<PactivityModel, int>? MultiSelectExportRef;

    private string ApplyingCostsMessage { get; set; } = "";
    public int? SelectedSupplierId { get; set; }
    public ImportCostModel? CostsToImport { get; set; } = new ImportCostModel() { EffectiveDate = DateTime.Now, CostEditType = CostEditType.Current };

    private bool AllowEdit { get; set; } = true;

    public SubdivisionDto? SelectedSubdivision { get; set; } = new SubdivisionDto();
    public List<int>? SelectedActivityIds { get; set; }
    private List<int>? LastSelectedActivityIds { get; set; }
    public bool IncludeNoCostItems { get; set; } = true;
    public bool DivisionDefaultCosts { get; set; } = true;
    public bool IncludeDivisionDefaultCosts { get; set; } = false;
    public bool OnlyItemsWithoutCosts { get; set; } = false;
    public int? SelectedSubdivisionId { get; set; }
    public int? LastSelectedSupplierId { get; set; }
    private string loadingCostStyle = "display:none";
    public IEnumerable<CostModel>? SelectedCosts { get; set; } = Enumerable.Empty<CostModel>();

    IEnumerable<ModelManagerItemModel>? myItems { get; set; } = Enumerable.Empty<ModelManagerItemModel>();

    public TelerikNotification NotificationReference { get; set; }

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    protected override async Task OnInitializedAsync()
    {
        SubdivisionJobPickService.OnChanged += SupplierChangedHandler;
        //  await RolloverCosts(); //TODO: find out if they want rollover to happen automatically without button click or not
        SelectedActivity = new PactivityModel();
        IsLoadingActivity = true;
        PurchasingActivityData = (await CostService.GetPurchasingActivitiesAsync()).Value;
        SupplierData = (await POService.GetSuppliersAsync()).Value;
        EditTypeOptions = new List<CostEditType>()
        {
            CostEditType.Current,
            CostEditType.Next,
           // CostEditType.Future
        };
        //ShowError = getGroups.IsSuccess;
        //ErrorMessage = getGroups.Message;
        //OptionGroupOptions = getGroups.Value;
        IsLoadingActivity = false;

        SelectedSubdivisions = new List<int>();
        // await CostService.RolloverAllCostsAsync();
        var response = await SubdivisionService.GetSubdivisionsAsync();
        AllSubdivisions = response.Value.Where(x => x.SubdivisionName != null && !x.SubdivisionName.Contains("Default")).ToList();//Remove "Division Default" since that is picked separately through checkbox
        EffectiveDateTypes = new List<CostEditType>()
        {
            CostEditType.Current,
            CostEditType.Next,
            //CostEditType.Future
        };

        SelectedActivityIds = new List<int>();

        StateHasChanged();
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userRoleAdmin = user.User.IsInRole("Admin");
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
        if(AllowEdit)
        {
            var isAnyRolloverDueCosts = await CostService.CheckRolloverDueCostsAsync();
            if (isAnyRolloverDueCosts.Value)
            {
                bool isConfirmed = await Dialogs.ConfirmAsync("There are costs that need rollover. Click OK to roll over or Cancel to continue");
                if (isConfirmed)
                {
                    IsLoading = true;
                    StateHasChanged();
                    await RolloverCosts();
                    IsLoading = false;
                }
            }
        }


    }
    async Task SupplierChangedHandler()
    {
        SelectedSupplierNumber = SubdivisionJobPickService.SupplierNumber;
    }
    private async Task RolloverCosts()
    {       
        var result = await CostService.RolloverAllCostsAsync();
        var count = result.Value.Count();//number of costs that were actually rolled over
        var message = $"{count} Costs were Rolled over";
        ShowMessage(true, message);
    }
    // private async Task CopyCosts()
    // {
    //     CopyCostModal.Show();
    // }
    private void AddCostFromToolbar(GridCommandEventArgs args)
    { 
        AddCostModal.Show();
        StateHasChanged();
    }
    private async void HandleValidAddCostSubmit(ResponseModel<CostModel> responseItem)
    {
        ShowMessage(responseItem.IsSuccess, responseItem.Message);
        if (SelectedItems != null && SelectedItems.Count() != 0)
        {
            CostData = new ObservableCollection<CostModel>();
            var getData = (await CostService.GetCostsForItemsAsync(SelectedItems.Select(x => x.MasterItemId).ToList())).Value;
            CostData = new ObservableCollection<CostModel>(getData);
        }
        AddCostModal.Hide();
        StateHasChanged();
    }
    // private async void HandleValidCopyCostSubmit(ResponseModel<List<CostDto>> responseItem)
    // {
    //     if (SelectedItems != null && SelectedItems.Count() != 0)
    //     {
    //         CostData = new ObservableCollection<CostModel>();
    //         var getData = (await CostService.GetCostsForItemsAsync(SelectedItems.Select(x => x.MasterItemId).ToList())).Value;
    //         CostData = new ObservableCollection<CostModel>(getData);
    //     }
    //     await CopyCostModal.Hide();

    //     if (responseItem.IsSuccess)
    //     {
    //         var responseCount = responseItem.Value.Count();

    //         ShowMessage(true, "Successfully copied costs");
    //     }
    //     else
    //     {
    //         ShowMessage(false, "There's an error copying costs. If problem persists, <NAME_EMAIL>");
    //     }
    //     StateHasChanged();
    // }
    //protected async Task OnActivityRowClickHandler(GridRowClickEventArgs args)
    //{
    //    loadingItemStyle = "";
    //    IsLoadingItem = true;
    //    SelectedActivity = args.Item as PactivityModel;
    //    ItemData = await CostService.GetItemsInActivityAsync(SelectedActivity.PactivityId);
    //    loadingItemStyle = "display:none";
    //    IsLoadingItem = false;
    //}
    protected async Task OnActivitySelectedHandler(GridRowClickEventArgs args)
    {
        loadingItemStyle = "";
        IsLoadingItem = true;
        SelectedActivity = args.Item as PactivityModel;
        //ItemData = new ObservableCollection<ModelManagerItemModel>();
        CostData.Clear();
        ItemData.Clear();//Clearing the item data is needed to get the items selected to clear. Also needs to be observable collection
                         // SelectedActivity = PurchasingActivityData.Where(x => x.PactivityId == SelectedActivityId).FirstOrDefault();
        if(SelectedActivity != null)
        {
            var getItemData = (await CostService.GetItemsInActivityAsync(SelectedActivity.PactivityId)).Value;
            ItemData = new ObservableCollection<ModelManagerItemModel>(getItemData);
        }
        //CostData = null;
        SelectedItems = new List<ModelManagerItemModel>();//clear selected items        
        loadingItemStyle = "display:none";
        IsLoadingItem = false;
    }
    protected async Task ItemsSelectedHandlerAsync(IEnumerable<ModelManagerItemModel> items)
    {
        //TODO: according to telerik documentation, async does not work with changeselected handler, and that is why this doesn't work on deselect
        //solution may be to use the row click handler, which does allow async
        IsLoadingCost = true;
        //TODO: add or take away from selected Items collection, cost data get data for all selected
        //TODO: doesn't work right on take items out of the collection, also not right on checkbox select all
        SelectedItems = items;
        SelectedItem = items.FirstOrDefault();
        CostData = new ObservableCollection<CostModel>();
        var getData = (await CostService.GetCostsForItemsAsync(SelectedItems.Select(x => x.MasterItemId).ToList())).Value;
        CostData = new ObservableCollection<CostModel>(getData);
        // foreach (var item in SelectedItems)
        // {
        //     var getData = await CostService.GetCostsForItemAsync(item.MasterItemId);
        //     foreach(var cost in getData)
        //     {
        //         CostData.Aggregate(cost);
        //     }
        //     CostData.AddRange(getData);
        // }       
        IsLoadingCost = false;
        CostGridRef.Rebind();
        StateHasChanged();
    }
    protected void ItemsSelectedHandler(IEnumerable<ModelManagerItemModel> items)
    {
        //This is a synchronous version of above because telerik change selected items handler doesn't work with async.
        //TODO: instead of this synchronous version, use the row click handler. that will require manual logic for select all
        //still, async loading the data would be far better
        IsLoadingCost = true;
        SelectedItems = items;
        SelectedItem = items.FirstOrDefault();
        CostData = new ObservableCollection<CostModel>();
        var getCostData = new List<CostModel>();
        foreach (var item in SelectedItems)
        {
            var getData = CostService.GetCostsForItem(item.MasterItemId).Result.Value;
            getCostData.AddRange(getData);
        }
        CostData = new ObservableCollection<CostModel>(getCostData);
        IsLoadingCost = false;
        // CostGridRef.Rebind();
        StateHasChanged();
    }
    protected async Task OnItemRowClickHandlerOld(GridRowClickEventArgs args)
    {
        IsLoadingCost = true;
        SelectedItem = args.Item as ModelManagerItemModel;
        //TODO: add or take away from selected Items collection, cost data get data for all selected
        // foreach( var item in SelectedItems)
        // {
        //     var getData = await CostService.GetCostsForItemAsync((int)SelectedItem.MasterItemId);
        //     CostData.AddRange(getData);
        // }
        // CostData = await CostService.GetCostsForItemAsync((int)SelectedItem.MasterItemId);
        // IsLoadingCost = false;
    }

    void EditCostHandler(GridCommandEventArgs args)
    {
        CostModel item = (CostModel)args.Item;
    }

    async Task UpdateCostHandler(GridCommandEventArgs args)
    {
        CostModel item = (CostModel)args.Item;
        var response = await CostService.UpdateCostAsync(item);
        var getData = await CostService.GetCostsForItemsAsync(SelectedItems.Select(x => x.MasterItemId).ToList());
        CostData = new ObservableCollection<CostModel>(getData.Value);
        ShowMessage(response.IsSuccess, response.Message); 
    }

    async Task DeleteCostHandler(GridCommandEventArgs args)
    {
        CostModel item = (CostModel)args.Item;
        var response = await CostService.DeleteCostAsync(item);
        var getData = (await CostService.GetCostsForItemsAsync(SelectedItems.Select(x => x.MasterItemId).ToList())).Value;
        CostData = new ObservableCollection<CostModel>(getData);
        ShowMessage(response.IsSuccess, response.Message);        
    }
    async Task DeleteCostsHandler()
    {
        var confirm = await Dialogs.ConfirmAsync("Are you sure you want to delete selected costs?");
        if (confirm)
        {
            var response = await CostService.DeleteCostsAsync(SelectedCosts.ToList());
            CostData = new ObservableCollection<CostModel>();
            var getData = await CostService.GetCostsForItemsAsync(SelectedItems.Select(x => x.MasterItemId).ToList());
            CostData = new ObservableCollection<CostModel>(getData.Value);
            ShowMessage(response.IsSuccess, response.Message);
        }

    }
    async Task CancelCostHandler(GridCommandEventArgs args)
    {
        CostModel item = (CostModel)args.Item;
    }

    private void TogglePickSubdivisionVisible()
    {
        PickSubdivisionVisible = ApplyAsDivisionDefault ? "display:none" : "";
        RemoveAreaCostsVisible = ApplyAsDivisionDefault ? "" : "display:none";
    }
    private void ToggleDatePickVisible()
    {
        DatePickVisible = CostsToImport.CostEditType == CostEditType.Next || SelectedEffectiveDateType == CostEditType.Future ? "" : "display:none";
    }
    private async void HandleApplyCost()
    {
        //TODO: if not selected division default, make sure a subdivision is picked
        ApplyingCostsMessage = "Applying Costs. Please wait...";

        if (!ApplyAsDivisionDefault)
        {
            foreach (var subdiv in SelectedSubdivisions)
            {
                foreach (var cost in ImportedCosts)
                {
                    //TODO: should it use SelectedSupplier from the subdivisionjob pick menu instead?
                    cost.SubNumber = CostsToImport.SubNumber ?? cost.SubNumber;//This will use the one picked instead of the one in the file
                    cost.CostEffectiveDate = CostsToImport.EffectiveDate;//TODO: fix the model so it doesn't have to be saved with individual record, also ensure date is in future
                    cost.SubdivisionId = subdiv;
                    cost.EditType = CostsToImport.CostEditType ?? CostEditType.Current;
                }
                var response1 = await CostService.ApplyCostsAsync(ImportedCosts);
                ShowMessage(response1.IsSuccess, response1.Message);
            }
        }
        else
        {

            foreach (var cost in ImportedCosts)
            {
                cost.SubNumber = CostsToImport.SubNumber ?? cost.SubNumber;//This will use the one picked instead of the one in the file
                cost.CostEffectiveDate = SelectedDate;//TODO: fix the model so it doesn't have to be saved with individual record, also ensure date is in future
                cost.SubdivisionId = 1;
                cost.EditType = SelectedEffectiveDateType ?? CostEditType.Current;
            }
            var response = await CostService.ApplyCostsAsync(ImportedCosts);
            ShowMessage(response.IsSuccess, response.Message);
            if (RemoveAreaCosts)
            {
                var response2 = await CostService.RemoveAreaCostsAsync(ImportedCosts);
            }
        }
        ImportedCosts = new List<CostModel>();//clear
        ApplyingCostsMessage = "";
        StateHasChanged();
    }
    async Task OnSelectHandler(FileSelectEventArgs args)
    {
        foreach (var file in args.Files)
        {
            var costsToAdd = await CostService.ImportExcel(file);
            ImportedCosts.AddRange(costsToAdd.Value);
        }
    }
    async Task OnRemoveHandler(FileSelectEventArgs args)
    {
        ImportedCosts = new List<CostModel>();//clear
    }
    void ToggleSelectAll(bool selectAll)
    {
        SelectedSubdivisions.Clear();
        if (selectAll)
        {
            SelectedSubdivisions.AddRange(AllSubdivisions.Select(x => x.SubdivisionId));
        }
        MultiSelectRef.Rebind();
    }
    bool IsAllSelected()
    {
        return SelectedSubdivisions?.Count == AllSubdivisions?.Count;
    }
    public int IsDownloadStarted { get; set; } = 0;
    protected async Task DownloadExcelFile()
    {
        if(SelectedSupplierNumber == null)
        {
            ShowMessage(false, "No Supplier Selected");
            return;
        }
        if (await JSRuntimeService.InvokeAsync<bool>("confirm", $"Do you want to Export?"))
        {
            IsDownloadStarted = 1;
            StateHasChanged();
            var model = new SupplierandActivitiesListSelectModel()
                {
                   // SubNumber = (int)SelectedSupplierId,
                    SubNumber = (int)SelectedSupplierNumber,
                    SelectedActivities = SelectedActivityIds,
                    CostAsOfDate = SelectedDate,
                    SubdivisionId = SelectedSubdivisionId,
                    DivisionDefaults = DivisionDefaultCosts,
                    IncludeItemsWithoutCosts = IncludeNoCostItems,
                    OnlyItemsWithoutCosts = OnlyItemsWithoutCosts,
                    IncludeDivisionDefaultCosts = IncludeDivisionDefaultCosts
                };
            var responseBytes = await CostService.DownloadExcelCostsAsync(model);
            var getSupplierName = await TradeService.GetSupplierAsync((int)SelectedSupplierNumber);
            var fileName = $"{getSupplierName.Value.SubName}-{DateTime.Now.ToString("yyyy-MM-dd", System.Globalization.CultureInfo.InvariantCulture)}.xlsx";
            await JSRuntimeService.InvokeAsync<object>("saveAsFile", fileName, Convert.ToBase64String(responseBytes.Value));

            IsDownloadStarted = 2;
            ShowMessage(true, "Costs Downloaded");
        }
    }    
    private void TogglePickSubdivisionExportVisible()
    {
        PickSubdivisionExportVisible = DivisionDefaultCosts ? "display:none" : "";
    }
    void ToggleSelectAllExport(bool selectAll)
    {
        SelectedActivityIds.Clear();
        if (selectAll)
        {
            SelectedActivityIds.AddRange(PurchasingActivityData.Select(x => x.PactivityId));
        }

        MultiSelectExportRef.Rebind();
    }
    bool IsAllExportSelected()
    {
        return SelectedActivityIds.Count == PurchasingActivityData.Count;
    }
    // for the item checkboxes
    bool GetChecked(PactivityModel item)
    {
        return SelectedActivityIds.Contains(item.PactivityId);
    }
    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= SupplierChangedHandler;        
    }
    async void ShowMessage(bool success, string message)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}
