﻿using ERP.Data.Models.Abstract;
using ERP.Data.Models.Dto;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public class TbBuiltOptionDto : IMapFrom<TbBuiltOption>
{
    public int Id { get; set; }

    public int LotId { get; set; }

    public int? AvailableOptionIdNotused { get; set; }

    public int? PlanOptionId { get; set; }

    public int OptionTypeId { get; set; }

    public int? OptionGroupId { get; set; }

    public int? InternalRefId { get; set; }

    public decimal Qty { get; set; }

    public decimal? QtyDelta { get; set; }

    public decimal Price { get; set; }

    public decimal? PriceDelta { get; set; }

    public bool Conflict { get; set; }

    public string OptionDesc { get; set; } = null!;

    public string? CustomerDesc { get; set; }

    public bool Elevation { get; set; }

    public DateTime? PrintDate { get; set; }

    public DateTime? ChangeDate { get; set; }

    public bool MostRecent { get; set; }

    public bool Removed { get; set; }

    public string? ExternalCode { get; set; }

    public int? LastUser { get; set; }

    public DateTime? LastChanged { get; set; }

    public string? Crgroup { get; set; }

    public short? BuilderApproved { get; set; }
    public bool BoolBuilderApproved { get; set; }
    public bool NeedsApproval { get; set; }

    public short? CustomerApproved { get; set; }

    public int? RoomPlanId { get; set; }

    public bool Exported { get; set; }

    public bool FastcustomRatified { get; set; }

    public decimal? UnitCost { get; set; }

    public int? ChangeOrderNum { get; set; }

    public int? OptionNum { get; set; }

    public bool QuoteComplete { get; set; }

    public int? OrigUser { get; set; }

    public string? OptionLongDesc { get; set; }

    public string? Restrictions { get; set; }

    public short? AddendumNum { get; set; }

    public bool HasAttachment { get; set; }

    public int? SelectedUserId { get; set; }

    public int? Envision { get; set; }

    public long? UniqueOptionId { get; set; }

    public bool EstimateRequired { get; set; }

    public long? OrigUniqueOptionId { get; set; }

    public int FromSpec { get; set; }
    public bool? BoolFromSpec { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public bool? IsActive { get; set; }

    public string? CreatedBy { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public string? JobNumber { get; set; }

    public int? OpAttrGroupItemId { get; set; }

    public string? OptionCode { get; set; }

    public string? PlanCode { get; set; }
    public string? SelectionNotes { get; set; }//using to concatenate attributes to string
    public bool? CancelledOption { get; set; } //track cancelled contract option 
    public OptionGroupDto? OptionGroup { get; set; }

    public OptionTypeDto? OptionType { get; set; } 
    public List<BuildAttributeItemDto>? BuildAttributeItems { get; set; }
    public AvailablePlanOptionDto? PlanOption { get; set; }
}
