﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class SalesconfigoptionsAttribute
{
    public int Id { get; set; }

    public int SalesconfigoptionsId { get; set; }

    public int? OpAttrGroupItemId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public int? AttrGroupAssignmentId { get; set; }

    public virtual AttrGroupAssignment? AttrGroupAssignment { get; set; }

    public virtual OptionAttributeGroupItem? OpAttrGroupItem { get; set; }

    public virtual Salesconfigoption Salesconfigoptions { get; set; } = null!;
}
