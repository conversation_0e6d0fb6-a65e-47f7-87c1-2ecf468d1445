﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Salesconfigco
{
    public int SalesconfigcoId { get; set; }

    public int SalesconfigId { get; set; }

    public int? SsChangeorderid { get; set; }

    public int? CoNumber { get; set; }

    public string? CoStatus { get; set; }

    public DateTime? CoStatusdate { get; set; }

    public string? SentToPurchasing { get; set; }

    public double? LotPremium { get; set; }

    public string? IsApproved { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTim { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<Estheader> Estheaders { get; set; } = new List<Estheader>();

    public virtual Salesconfig Salesconfig { get; set; } = null!;

    public virtual ICollection<Salesconfigcooption> Salesconfigcooptions { get; set; } = new List<Salesconfigcooption>();
}
