﻿using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using DocuSign.eSign.Client;
using Microsoft.AspNetCore.DataProtection;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;

namespace ERP.Web.DocusignServices
{
    public interface IDocusignAuthenticator
    {
        void Authenticate(DocuSignClient apiClient);
    }
    public class DocusignAuthenticator : IDocusignAuthenticator
    {
        private IConfiguration Configuration { get; }
        private static string AccessToken { get; set; }
        private static DateTime ExpiresIn;

        public DocusignAuthenticator(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public void Authenticate(DocuSignClient apiClient)
        {
            ConfigureApiClientAuth(apiClient);
        }

        private void ConfigureApiClientAuth(DocuSignClient apiClient)
        {
            CheckToken(apiClient);
            apiClient.Configuration.AddDefaultHeader("Authorization", $"Bearer {AccessToken}");
        }

        private void CheckToken(DocuSignClient apiClient)
        {
            if (AccessToken != null && DateTime.UtcNow <= ExpiresIn)
                return;

            UpdateToken(apiClient);
        }

        private void UpdateToken(DocuSignClient apiClient)
        {
            var clientId = Configuration["DocuSign:ClientId"];
            var impersonatedUserGuid = Configuration["DocuSign:ImpersonatedUserId"];
            var authServer = Configuration["DocuSign:AuthServer"];
            //var privateKey = System.IO.File.ReadAllBytes(Configuration["DocuSign:PrivateKeyFile"]);

            var identityClientId = Configuration.GetSection("ManagedIdentityId").Value;
            var credentialOptions = new DefaultAzureCredentialOptions
            {
                ManagedIdentityClientId = identityClientId,
                ExcludeEnvironmentCredential = true, //did not work
                ExcludeWorkloadIdentityCredential = true,// did not work
            };
            // var newCredential = new ManagedIdentityCredential(clientId, credentialOptions);
            //var newCredential = new ManagedIdentityCredential(clientId);
            //var credential = new DefaultAzureCredential();
            var credential = new DefaultAzureCredential(credentialOptions);
            var client = new SecretClient(
                new Uri("https://erpkeyvault.vault.azure.net/"),
                credential);
            KeyVaultSecret keyVaultSecret = client.GetSecret("NewDocusignPrivateKey");
            string secretValue = keyVaultSecret.Value;
            var privateKeyBytes = Encoding.UTF8.GetBytes(secretValue);

            try
            {
                var authToken = apiClient.RequestJWTUserToken(
                    clientId,
                    impersonatedUserGuid,
                    authServer,
                    privateKeyBytes,
                    1);

                AccessToken = authToken.access_token;
                if (authToken.expires_in != null)
                    ExpiresIn = DateTime.UtcNow.AddSeconds(authToken.expires_in.Value);
            }
            catch (ApiException apiExp)
            {
                // Consent for impersonation must be obtained to use JWT Grant
                if (apiExp.Message.Contains("consent_required"))
                {
                    // Caret needed for escaping & in windows URL
                    string caret = "";
                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                    {
                        caret = "^";
                    }

                    // Build a URL to provide consent for this Integration Key and this userId
                   // string url = "https://account-d.docusign.com/oauth/auth?response_type=code" + caret + "&scope=impersonation%20signature" + caret + "&client_id=6a4d2035-f5cb-470f-8bbb-daa6e24f0afd" + caret + "&redirect_uri=" + "https://localhost:7218/";
                    string url = "https://account.docusign.com/oauth/auth?response_type=code" + caret + "&scope=impersonation%20signature" + caret + "&client_id=6a4d2035-f5cb-470f-8bbb-daa6e24f0afd" + caret + "&redirect_uri=" + "https://localhost:7218/";
                    Console.WriteLine($"Consent is required - launching browser (URL is {url.Replace(caret, "")})");

                    // Start new browser window for login and consent to this app by DocuSign user
                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                    {
                        Process.Start(new ProcessStartInfo("cmd", $"/c start {url}") { CreateNoWindow = false });
                    }
                    else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                    {
                        Process.Start("xdg-open", url);
                    }
                    else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                    {
                        Process.Start("open", url);
                    }

                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine("Unable to get Token; Exiting. Please rerun the console app once consent was provided");
                    Console.ForegroundColor = ConsoleColor.White;
                    Environment.Exit(-1);
                }
            }
        }
    }
}
