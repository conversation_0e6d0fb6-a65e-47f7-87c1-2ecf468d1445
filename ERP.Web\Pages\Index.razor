﻿@page "/"
@inject SubdivisionService SubdivisionService
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@using ERP.Web.Components
@inject IHostEnvironment Env


@if (Env.IsDevelopment())
{
    <PageTitle>VM ERP Dev | Home Dashboard</PageTitle>
}
@if (Env.IsProduction())
{
    <PageTitle>VM ERP | Home Dashboard</PageTitle>
}
<!-- Sample Data -->
<div class="row">
    <div class="col-lg-9">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-3">
                <div class="card report-card">
                    <ERP.Web.Components.Tile_1></ERP.Web.Components.Tile_1>
                    <!--end card-body-->
                </div><!--end card-->
            </div> <!--end col-->
            <div class="col-md-6 col-lg-3">
                <div class="card report-card">
                    <ERP.Web.Components.Tile_2></ERP.Web.Components.Tile_2>
                    <!--end card-body-->
                </div><!--end card-->
            </div> <!--end col-->
            <div class="col-md-6 col-lg-3">
                <div class="card report-card">
                    <ERP.Web.Components.Tile_3></ERP.Web.Components.Tile_3>
                    <!--end card-body-->
                </div><!--end card-->
            </div> <!--end col-->
            <div class="col-md-6 col-lg-3">
                <div class="card report-card">
                    <ERP.Web.Components.Tile_4></ERP.Web.Components.Tile_4>
                    <!--end card-body-->
                </div><!--end card-->
            </div> <!--end col-->
        </div><!--end row-->
        <div class="card">
            <ERP.Web.Components.Tile_5 OnIncompleteSubdivisionActivitiesObtained="SetTotalIncompleteActivities"
            OnSeriesClicked="SetIncompleteActivitiesForSubdivision">
            </ERP.Web.Components.Tile_5><!--end card-body-->
        </div><!--end card-->
    </div><!--end col-->
    @if (ConstructionUser)
    {
        <div class="col-lg-3">
            <div class="card">
                <ERP.Web.Components.ShowIncompleteActivities TotalIncompleteActivities="TotalIncompleteActivities"
                IncompleteSubdivisionActivities="IncompleteSubdivisionActivities">
                </ERP.Web.Components.ShowIncompleteActivities><!--end card-body-->
            </div><!--end card-->
        </div> <!--end col-->
    }
    else
    {
        <div class="col-lg-3">
            <div class="card">
                <ERP.Web.Components.Tile_6></ERP.Web.Components.Tile_6><!--end card-body-->
            </div><!--end card-->
        </div> <!--end col-->
    }
    
    
</div><!--end row-->
@* <p>Secret TEST: @secretValue</p> *@

@code {
    private string? secretValue { get; set; }
    public int TotalIncompleteActivities { get; set; }
    public IncompleteSubdivisionActivitiesDto? IncompleteSubdivisionActivities { get; set; }
    public bool ConstructionUser = false;
    protected override async Task OnInitializedAsync()
    {
        var response = await SubdivisionService.DoNothingAsync();//Login and do nothing, just testing
        secretValue = response.Message;//Testing get secret
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var IsContstructionUser = user.User.IsInRole("ConstructionDirector") || user.User.IsInRole("ConstructionManager");
        if (IsContstructionUser)
        {
            ConstructionUser = true;
            //NavManager.NavigateTo("/constructiondashboard");//Ernie 11/25: Construction dashboard is slow to load, using the main page for now
        }
        var isOperations = user.User.IsInRole("Operations");
        if (isOperations)
        {
            NavManager.NavigateTo("/operationsjob");
        }
    }

    private void SetTotalIncompleteActivities(int totalIncompleteActivities)
    {
        TotalIncompleteActivities = totalIncompleteActivities;
        StateHasChanged();
    }

    private void SetIncompleteActivitiesForSubdivision(IncompleteSubdivisionActivitiesDto incompleteActivities)
    {
        IncompleteSubdivisionActivities = incompleteActivities;
        StateHasChanged();
    }
}