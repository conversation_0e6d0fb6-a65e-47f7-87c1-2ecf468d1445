﻿
using AutoMapper;
using DocumentFormat.OpenXml.Office2016.Excel;
using DocumentFormat.OpenXml.Spreadsheet;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NLog;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Numerics;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace ERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Authorize]
    public class PlanController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        readonly ErpDevContext _context;
        private static Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly IMapper _mapper;

        public PlanController(IConfiguration configuration, ErpDevContext context, IMapper mapper)
        {
            this._configuration = configuration;
            _context = context;
            _mapper = mapper;
        }

        [HttpGet]
        public async Task<IActionResult> GetPlanTypesAsync()
        {
            try
            {
                //Plan Types are Condo, Single-Family, Town
                var types = await _context.PlanTypes.Where(x => x.DivId == 1 && x.IsActive == true).ToListAsync();
                var planTypes = _mapper.Map<List<PlanTypeDto>>(types);
                return new OkObjectResult(new ResponseModel<List<PlanTypeDto>> { Value = planTypes, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<PlanTypeDto>> { IsSuccess = false, Message = "Failed to get Plan Types", Value = null });
            }
        }

        [HttpGet("{subdivisionId}")]
        public async Task<IActionResult> GetPhasePlansAsync(int subdivisionId)
        {
            try
            {
                var plans = await _context.PhasePlans.Where(x => x.SubdivisionId == subdivisionId && x.IsActive == true).Select(x => new PhasePlanModel()
                {
                    PhasePlanId = x.PhasePlanId,
                    Phase = x.Phase,
                    MasterPlan = x.MasterPlan,
                    PlanTypeId = x.MasterPlan.PlanTypeId,
                    PlanTypeDescription = _context.PlanTypes.Where(p => p.PlanTypeId == x.MasterPlan.PlanTypeId).Select(p => p.Description).FirstOrDefault(),
                    PlanName = x.MasterPlan.PlanName,
                    PlanNumber = x.MasterPlan.PlanNum
                }).ToListAsync();

                return new OkObjectResult(new ResponseModel<List<PhasePlanModel>> { Value = plans, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<PhasePlanModel>> { IsSuccess = false, Message = "Failed to get Phase Plans", Value = new List<PhasePlanModel>() });
            }
        }
        [HttpGet("{subdivisionId}")]
        public async Task<IActionResult> GetPhasePlansInSubdivisionAsync(int subdivisionId)
        {
            try
            {
                var plans = await _context.PhasePlans.AsNoTracking().Include(x => x.MasterPlan).Include(x => x.Subdivision).Where(x => x.SubdivisionId == subdivisionId && x.IsActive == true).ToListAsync();
                var plansDto = _mapper.Map<List<PhasePlanDto>>(plans);
                foreach (var planDto in plansDto)
                {
                    planDto.PlanNum = planDto.MasterPlan.PlanNum;
                    planDto.PlanName = planDto.MasterPlan.PlanName;
                    planDto.SubdivisionPlanNamePlanNumberDisplay = $"{planDto.Subdivision.SubdivisionName} - {planDto.MasterPlan.PlanName} - {planDto.MasterPlan.PlanNum}";
                }
                return new OkObjectResult(new ResponseModel<List<PhasePlanDto>> { Value = plansDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<PhasePlanDto>> { IsSuccess = false, Message = "Failed to get Phase Plans", Value = new List<PhasePlanDto>() });
            }
        }
        [HttpGet("{phasePlanId}")]
        public async Task<IActionResult> GetElevationsInPlanAsync(int phasePlanId)
        {
            try
            {
                var getElevations = _context.AvailablePlanOptions.Where(x => x.PhasePlanId == phasePlanId && x.IsElevation == "T" && x.IsActive == true).ToList();
                var elvationsDto = _mapper.Map<List<AvailablePlanOptionDto>>(getElevations);

                return new OkObjectResult(new ResponseModel<List<AvailablePlanOptionDto>> { Value = elvationsDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<AvailablePlanOptionDto>> { IsSuccess = false, Message = "Failed to get Phase Plans", Value = new List<AvailablePlanOptionDto>() });
            }
        }
        [HttpPut]
        public async Task<IActionResult> UpdatePhasePlanAsync([FromBody] PhasePlanModel planToUpdate)
        {
            try
            {
                var getPlan = await _context.PhasePlans.SingleOrDefaultAsync(x => x.PhasePlanId == planToUpdate.PhasePlanId).ConfigureAwait(false);
                getPlan.IsActive = true;
                getPlan.Phase = planToUpdate.Phase;
                getPlan.UpdatedBy = User.Identity.Name.Split('@')[0];
                getPlan.UpdatedDateTime = DateTime.Now;
                _context.PhasePlans.Update(getPlan);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<PhasePlanModel> { Value = planToUpdate, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<PhasePlanModel> { IsSuccess = false, Message = "Failed to Update Plan", Value = null });
            }
        }

        [HttpGet("{planId}")]
        public async Task<IActionResult> DeletePlanFromSubdivisionAsync(int? planId)
        {
            try
            {
                var updatedBy = User.Identity.Name.Split('@')[0];
                var getPlan = await _context.PhasePlans.SingleOrDefaultAsync(x => x.PhasePlanId == planId).ConfigureAwait(false);
                getPlan.IsActive = false;
                getPlan.UpdatedBy = updatedBy;
                getPlan.UpdatedDateTime = DateTime.Now;
                _context.PhasePlans.Update(getPlan);
                await _context.SaveChangesAsync();

                //deactivate available plan options too
                var getPlanOptions = _context.AvailablePlanOptions.Where(x => x.PhasePlanId == planId);
                await getPlanOptions.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedDateTime, DateTime.Now).SetProperty(b => b.UpdatedBy, updatedBy));

                return new OkObjectResult(new ResponseModel<int?> { Value = planId, IsSuccess = true });


            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<int?> { IsSuccess = false, Message = "Failed to Delete Plan from Subdivision", Value = null });
            }
        }


        [HttpPost]
        public async Task<ResponseModel> AddPhasePlanToSubdivisionAsync([FromBody] AddPlanToSubdivisionPhaseModel addPlan)
        {
            try
            {
                //This will copy all the options from one phase plan to new phase plan 
                string updateBy = User.Identity.Name.Split('@')[0];
                var getPlan = await _context.PhasePlans.SingleOrDefaultAsync(x => x.PhasePlanId == addPlan.PlanId).ConfigureAwait(false);
                var checkActivePlanExists = await _context.PhasePlans.Where(x => x.MasterPlanId == getPlan.MasterPlanId && x.SubdivisionId == addPlan.SubdivisionId && x.Phase == addPlan.PhaseName && x.IsActive == true).ToListAsync();
                var checkDeactivatedPlanExists = await _context.PhasePlans.Where(x => x.MasterPlanId == getPlan.MasterPlanId && x.SubdivisionId == addPlan.SubdivisionId && x.Phase == addPlan.PhaseName && x.IsActive == false).ToListAsync();
                var newPhasePlan = new PhasePlan();
                if (checkActivePlanExists.Any())
                {
                    return new ResponseModel() { IsSuccess = false, Message = "Could not add plan. This plan already exists in this subdivision." };
                }
                else if (checkDeactivatedPlanExists.Any())
                {

                    var reactivatePlan = checkDeactivatedPlanExists.First();
                    reactivatePlan.IsActive = true;
                    reactivatePlan.UpdatedBy = updateBy;
                    _context.PhasePlans.Update(reactivatePlan);
                    await _context.SaveChangesAsync();
                    //deactivate any options associated with the plan, then readd as below
                    var findPlanOptions = _context.AvailablePlanOptions.Where(x => x.PhasePlanId == reactivatePlan.PhasePlanId);
                    if (findPlanOptions.Any())
                    {
                        await findPlanOptions.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));
                    }
                    newPhasePlan = reactivatePlan;
                }
                else
                {
                    newPhasePlan = new PhasePlan()
                    {
                        MasterPlanId = getPlan.MasterPlanId,
                        SubdivisionId = (int)addPlan.SubdivisionId,
                        Phase = addPlan.PhaseName,
                        CreatedBy = User.Identity.Name.Split('@')[0]
                    };
                    await _context.PhasePlans.AddAsync(newPhasePlan);
                    await _context.SaveChangesAsync();
                }


                var optionsToInsert = await _context.AvailablePlanOptions.Where(x => x.PhasePlanId == addPlan.PlanId && x.IsActive == true).Select(x => new AvailablePlanOption()
                {
                    SubdivisionId = (int)addPlan.SubdivisionId,
                    OptionCode = x.MasterOption.OptionCode,
                    MasterPlanId = x.MasterPlanId,
                    OptionGroupId = x.OptionGroupId,
                    ModifiedOptionDesc = x.ModifiedOptionDesc,
                    OptionLongDesc = x.OptionLongDesc,
                    Phase = newPhasePlan.Phase,
                    MasterOptionId = x.MasterOptionId,
                    HomeAreaId = x.HomeAreaId,
                    OptionTypeId = x.OptionTypeId,
                    UnitPrice = x.UnitPrice,
                    UnitCost = x.UnitCost,
                    UnitQty = x.UnitQty,
                    IsActive = true,
                    OptionSelectionType = x.MasterOption.OptionSelectionType ?? "S",
                    PhasePlanId = newPhasePlan.PhasePlanId,
                    CreatedDateTime = DateTime.Now,
                    AgentModQty = x.AgentModQty ?? false,
                    AgentModType = x.AgentModType ?? false,
                    AgentModPrice = x.AgentModPrice ?? false,
                    CustChoiceReq = x.CustChoiceReq ?? false,
                    Elevation = x.Elevation ?? false,
                    ActiveDepositSched = x.ActiveDepositSched ?? false,
                    SubNumber = x.SubNumber,
                    //MarginLumpSum = x.MarginLumpSum,
                    //MarginMarketValue = x.MarginMarketValue,
                    //MarginPercent = x.MarginPercent,
                    //MarginType = x.MarginType,
                    //LastCost = x.LastCost,
                    //LastDate = x.LastDate,
                    //LastMargin = x.LastMargin,
                    //LastSelling = x.LastSelling,
                    //CurrCost = x.CurrCost,
                    //CurrDate = x.CurrDate,
                    //CurrMargin = x.CurrMargin,
                    //CurrSelling = x.CurrSelling,
                    //NextCost = x.NextCost,
                    //NextDate = x.NextDate,
                    //NextMargin = x.NextMargin,
                    //NextSelling = x.NextSelling,
                    AreaOptionSource = x.AreaOptionSource,
                    AddPriceToBase = x.AddPriceToBase,
                    AssociatedEstimate = x.AssociatedEstimate,
                    WarningCount = x.WarningCount,
                    IsElevation = x.IsElevation,
                    IncludeInBase = x.IncludeInBase,
                    IsStandard = x.IsStandard,
                    HasEstimate = x.HasEstimate,
                }).ToListAsync();
                await _context.AvailablePlanOptions.BulkInsertAsync(optionsToInsert);

            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return new ResponseModel() { IsSuccess = false, Message = "Failed to add plan." };
            }

            return new ResponseModel() { IsSuccess = true, Message = "Plan has been added." };
        }

        [HttpPost]
        public async Task<ResponseModel> AddPhasePlansToSubdivisionAsync([FromBody] AddPlanToSubdivisionPhaseModel addPlan)
        {
            try
            {
                //TODO: this will go wrong if the user selects the same plan from multiple subdivisions and copies both
                string updateBy = User.Identity.Name.Split('@')[0];
                var getPlans = _context.PhasePlans.Include(x => x.AvailablePlanOptions.Where(x => x.IsActive == true)).ThenInclude(x => x.MasterOption).Where(x => addPlan.PlanToAddIds.Contains(x.PhasePlanId)).ToList();
                var checkPlanExists = await _context.PhasePlans.Where(x => getPlans.Select(y => y.MasterPlanId).Contains(x.MasterPlanId) && x.SubdivisionId == addPlan.SubdivisionId && x.Phase == addPlan.PhaseName).ToListAsync();
                var existingMasterPlanIds = new List<int>();
                if (checkPlanExists.Any(x => x.IsActive == true))
                {
                    return new ResponseModel() { IsSuccess = false, Message = "A plan you are adding already exists in this subdivision." };
                }
                if(checkPlanExists.Any(x => x.IsActive == false))
                {                    
                    foreach (var plan in checkPlanExists.Where(x => x.IsActive == false))
                    {
                        existingMasterPlanIds.Add(plan.MasterPlanId);
                        //reactivate the plan
                        plan.IsActive = true;
                        plan.UpdatedBy = updateBy;
                        _context.PhasePlans.Update(plan);
                        await _context.SaveChangesAsync();

                        //deactivate any options in it so it can be readded
                        var findPlanOptions = _context.AvailablePlanOptions.Where(x => x.PhasePlanId == plan.PhasePlanId);
                        if (findPlanOptions.Any())
                        {
                            await findPlanOptions.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));
                        }
                    }
                }
                //add new phase plans with their options (existing inactive ones have been reactivated, any existing active ones caused return fail response)
                var newPhasePlans = getPlans.Where(x => !existingMasterPlanIds.Contains(x.MasterPlanId)).Select(x => new PhasePlan()
                {
                    MasterPlanId = x.MasterPlanId,
                    SubdivisionId = (int)addPlan.SubdivisionId,
                    Phase = addPlan.PhaseName,
                    CreatedBy = User.Identity.Name.Split('@')[0],
                    CreatedDateTime = DateTime.Now,
                    AvailablePlanOptions = x.AvailablePlanOptions.Select(y => new AvailablePlanOption()
                    {
                        SubdivisionId = (int)addPlan.SubdivisionId,//fix null
                        OptionCode = y.MasterOption.OptionCode,
                        MasterPlanId = y.MasterPlanId,
                        OptionGroupId = y.OptionGroupId,
                        ModifiedOptionDesc = y.ModifiedOptionDesc,
                        Phase = addPlan.PhaseName,
                        MasterOptionId = y.MasterOptionId,
                        HomeAreaId = y.HomeAreaId,
                        OptionTypeId = 7, //This is the ID for "Standard" as the default
                        UnitPrice = y.UnitPrice,
                        UnitCost = y.UnitCost,
                        UnitQty = y.UnitQty,
                        IsActive = true,
                        OptionSelectionType = y.MasterOption.OptionSelectionType ?? "S",
                        //PhasePlanId = newPhasePlan.PhasePlanId,
                        CreatedDateTime = DateTime.Now,
                        AgentModQty = y.AgentModQty ?? false,
                        AgentModType = y.AgentModType ?? false,
                        AgentModPrice = y.AgentModPrice ?? false,
                        CustChoiceReq = y.CustChoiceReq ?? false,
                        Elevation = y.Elevation ?? false,
                        ActiveDepositSched = y.ActiveDepositSched ?? false,
                        SubNumber = y.SubNumber,
                        //MarginLumpSum = x.MarginLumpSum,
                        //MarginMarketValue = x.MarginMarketValue,
                        //MarginPercent = x.MarginPercent,
                        //MarginType = x.MarginType,
                        //LastCost = x.LastCost,
                        //LastDate = x.LastDate,
                        //LastMargin = x.LastMargin,
                        //LastSelling = x.LastSelling,
                        //CurrCost = x.CurrCost,
                        //CurrDate = x.CurrDate,
                        //CurrMargin = x.CurrMargin,
                        //CurrSelling = x.CurrSelling,
                        //NextCost = x.NextCost,
                        //NextDate = x.NextDate,
                        //NextMargin = x.NextMargin,
                        //NextSelling = x.NextSelling,
                        AreaOptionSource = y.AreaOptionSource,
                        AddPriceToBase = y.AddPriceToBase,
                        AssociatedEstimate = y.AssociatedEstimate,
                        WarningCount = y.WarningCount,
                        IsElevation = y.IsElevation,
                        IncludeInBase = y.IncludeInBase,
                        IsStandard = y.IsStandard,
                        HasEstimate = y.HasEstimate,
                    }).ToList(),
                }).ToList();
                await _context.PhasePlans.BulkInsertAsync(newPhasePlans, options => options.IncludeGraph = true);

                //if phaseplan existed (and was reactivated), add new options
                var newPlanOptions = getPlans.Where(x => existingMasterPlanIds.Contains(x.MasterPlanId)).SelectMany(x => x.AvailablePlanOptions).Select(y => new AvailablePlanOption()
                {
                    SubdivisionId = (int)addPlan.SubdivisionId,
                    OptionCode = y.MasterOption.OptionCode,
                    MasterPlanId = y.MasterPlanId,
                    OptionGroupId = y.OptionGroupId,
                    ModifiedOptionDesc = y.ModifiedOptionDesc,
                    Phase = addPlan.PhaseName,
                    MasterOptionId = y.MasterOptionId,
                    HomeAreaId = y.HomeAreaId,
                    OptionTypeId = 7, //This is the ID for "Standard" as the default
                    UnitPrice = y.UnitPrice,
                    UnitCost = y.UnitCost,
                    UnitQty = y.UnitQty,
                    IsActive = true,
                    OptionSelectionType = y.MasterOption.OptionSelectionType ?? "S",
                    PhasePlanId = checkPlanExists.Where(z => z.MasterPlanId == y.MasterPlanId).FirstOrDefault().PhasePlanId,
                    CreatedDateTime = DateTime.Now,
                    AgentModQty = y.AgentModQty ?? false,
                    AgentModType = y.AgentModType ?? false,
                    AgentModPrice = y.AgentModPrice ?? false,
                    CustChoiceReq = y.CustChoiceReq ?? false,
                    Elevation = y.Elevation ?? false,
                    ActiveDepositSched = y.ActiveDepositSched ?? false,
                    SubNumber = y.SubNumber,
                    //MarginLumpSum = x.MarginLumpSum,
                    //MarginMarketValue = x.MarginMarketValue,
                    //MarginPercent = x.MarginPercent,
                    //MarginType = x.MarginType,
                    //LastCost = x.LastCost,
                    //LastDate = x.LastDate,
                    //LastMargin = x.LastMargin,
                    //LastSelling = x.LastSelling,
                    //CurrCost = x.CurrCost,
                    //CurrDate = x.CurrDate,
                    //CurrMargin = x.CurrMargin,
                    //CurrSelling = x.CurrSelling,
                    //NextCost = x.NextCost,
                    //NextDate = x.NextDate,
                    //NextMargin = x.NextMargin,
                    //NextSelling = x.NextSelling,
                    AreaOptionSource = y.AreaOptionSource,
                    AddPriceToBase = y.AddPriceToBase,
                    AssociatedEstimate = y.AssociatedEstimate,
                    WarningCount = y.WarningCount,
                    IsElevation = y.IsElevation,
                    IncludeInBase = y.IncludeInBase,
                    IsStandard = y.IsStandard,
                    HasEstimate = y.HasEstimate,
                }).ToList();
                await _context.AvailablePlanOptions.BulkInsertAsync(newPlanOptions);

                

            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return new ResponseModel() { IsSuccess = false, Message = "Failed to add plan." };
            }

            return new ResponseModel() { IsSuccess = true, Message = "Plan has been added." };

        }

        [HttpPost]
        public async Task<ResponseModel> AddMasterPlanToSubdivisionAsync([FromBody] AddPlanToSubdivisionPhaseModel addPlan)
        {
            try
            {
                string updateBy = User.Identity.Name.Split('@')[0];
                var getPlan = await _context.MasterPlans.FirstOrDefaultAsync(x => x.MasterPlanId == addPlan.PlanId);
                var checkActivePlanExists = await _context.PhasePlans.Where(x => x.MasterPlanId == addPlan.PlanId && x.SubdivisionId == addPlan.SubdivisionId && x.Phase == addPlan.PhaseName && x.IsActive == true).ToListAsync();
                var checkDeactivatedPlanExists = await _context.PhasePlans.Where(x => x.MasterPlanId == addPlan.PlanId && x.SubdivisionId == addPlan.SubdivisionId && x.Phase == addPlan.PhaseName && x.IsActive == false).ToListAsync();
                var newPhasePlan = new PhasePlan();
                if (checkActivePlanExists.Any())
                {
                    return new ResponseModel() { IsSuccess = false, Message = "Could not add plan. This plan may already exist in this subdivision." };
                }
                else if (checkDeactivatedPlanExists.Any())
                {

                    var reactivatePlan = checkDeactivatedPlanExists.First();
                    reactivatePlan.IsActive = true;
                    reactivatePlan.UpdatedBy = updateBy;
                    _context.PhasePlans.Update(reactivatePlan);
                    await _context.SaveChangesAsync();
                    //deactivate any options associated with the plan, then readd as below
                    var findPlanOptions = _context.AvailablePlanOptions.Where(x => x.PhasePlanId == reactivatePlan.PhasePlanId);
                    if (findPlanOptions.Any())
                    {
                        await findPlanOptions.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));
                    }
                    newPhasePlan = reactivatePlan;
                }
                else
                {
                    newPhasePlan = new PhasePlan()
                    {
                        MasterPlanId = (int)addPlan.PlanId,
                        SubdivisionId = (int)addPlan.SubdivisionId,
                        Phase = addPlan.PhaseName,
                        CreatedBy = User.Identity.Name.Split('@')[0],
                        CreatedDateTime = DateTime.Now
                    };
                    await _context.PhasePlans.AddAsync(newPhasePlan);
                    await _context.SaveChangesAsync();
                }
                //add the options, don't add the base house, or it will be there twice, because Base house doesn't go in available plan option, that is phase plan    
                var userName = User.Identity.Name.Split('@')[0];
                var newOptionsToAdd = await _context.AsmHeaders.Where(x => x.MasterPlanId == addPlan.PlanId && x.MasterOption != null && x.IsBaseHouse != "T" && x.IsActive == true).Select(x => new AvailablePlanOption()
                {
                    SubdivisionId = (int)addPlan.SubdivisionId,
                    OptionCode = x.MasterOption.OptionCode,//PlanNum gets appended only on frontend view//$"{getPlan.PlanNum}{x.MasterOption.OptionCode}",
                    MasterPlanId = x.MasterPlanId,
                    OptionGroupId = x.MasterOption.OptionGroupId,
                    ModifiedOptionDesc = x.MasterOption.OptionDesc,//They can change it later, but if it's just copy for the whole plan, need to have something
                    Phase = newPhasePlan.Phase,
                    MasterOptionId = x.MasterOptionId,
                    HomeAreaId = x.HomeAreaId,
                    OptionTypeId = 6, //6 is "finance" 7 is the ID for "Standard" as the default -- per Julie 9/20, default to "Finance"
                    UnitPrice = x.MasterOption.UnitPrice,
                    UnitCost = x.MasterOption.UnitCost,
                    UnitQty = x.MasterOption.UnitQty,
                    IsActive = true,
                    OptionSelectionType = x.MasterOption.OptionSelectionType ?? "S",
                    PhasePlanId = newPhasePlan.PhasePlanId,
                    CreatedDateTime = DateTime.Now,
                    CreatedBy = userName,
                    AgentModQty = x.MasterOption.AgentModQty ?? false,
                    AgentModType = x.MasterOption.AgentModType ?? false,
                    AgentModPrice = x.MasterOption.AgentModPrice ?? false,
                    CustChoiceReq = x.MasterOption.CustChoiceReq ?? false,
                    Elevation = x.MasterOption.Elevation ?? false,//TODO: x.iselevation?
                    IsElevation = x.IsElevation,
                    //IsStandard = false,//default to false
                    ActiveDepositSched = x.MasterOption.ActiveDepositSched ?? false,
                    DollarAmt = x.MasterOption.DollarAmt ?? false,
                }).ToListAsync();

                DataTable OptionsToAddDataTable = new DataTable("AvailablePlanOption");
                DataColumn SubdivisionId = new DataColumn("SubdivisionId", typeof(int));
                OptionsToAddDataTable.Columns.Add(SubdivisionId);
                DataColumn OptionCode = new DataColumn("OptionCode", typeof(string));
                OptionsToAddDataTable.Columns.Add(OptionCode);
                DataColumn ModifiedOptionDesc = new DataColumn("ModifiedOptionDesc", typeof(string));
                OptionsToAddDataTable.Columns.Add(ModifiedOptionDesc);
                DataColumn MasterPlanId = new DataColumn("MasterPlanId", typeof(int));
                OptionsToAddDataTable.Columns.Add(MasterPlanId);
                DataColumn OptionGroupId = new DataColumn("OptionGroupId", typeof(int));
                OptionsToAddDataTable.Columns.Add(OptionGroupId);
                DataColumn Phase = new DataColumn("Phase", typeof(string));
                OptionsToAddDataTable.Columns.Add(Phase);
                DataColumn MasterOptionId = new DataColumn("MasterOptionId", typeof(int));
                OptionsToAddDataTable.Columns.Add(MasterOptionId);
                DataColumn HomeAreaId = new DataColumn("HomeAreaId", typeof(int));
                OptionsToAddDataTable.Columns.Add(HomeAreaId);
                DataColumn OptionTypeId = new DataColumn("OptionTypeId", typeof(int));
                OptionsToAddDataTable.Columns.Add(OptionTypeId);
                DataColumn UnitPrice = new DataColumn("UnitPrice", typeof(decimal));
                OptionsToAddDataTable.Columns.Add(UnitPrice);
                DataColumn UnitCost = new DataColumn("UnitCost", typeof(decimal));
                OptionsToAddDataTable.Columns.Add(UnitCost);
                DataColumn IsActive = new DataColumn("IsActive", typeof(bool));
                OptionsToAddDataTable.Columns.Add(IsActive);
                DataColumn UnitQty = new DataColumn("UnitQty", typeof(decimal));
                OptionsToAddDataTable.Columns.Add(UnitQty);
                DataColumn OptionSelectionType = new DataColumn("OptionSelectionType", typeof(string));
                OptionsToAddDataTable.Columns.Add(OptionSelectionType);
                DataColumn PhasePlanId = new DataColumn("PhasePlanId", typeof(int));
                OptionsToAddDataTable.Columns.Add(PhasePlanId);
                DataColumn CreatedDateTime = new DataColumn("CreatedDateTime", typeof(DateTime));
                OptionsToAddDataTable.Columns.Add(CreatedDateTime);
                DataColumn CreatedBy = new DataColumn("CreatedBy", typeof(string));
                OptionsToAddDataTable.Columns.Add(CreatedBy);
                DataColumn CustChoiceReq = new DataColumn("CustChoiceReq", typeof(bool));
                OptionsToAddDataTable.Columns.Add(CustChoiceReq);
                DataColumn AgentModType = new DataColumn("AgentModType", typeof(bool));
                OptionsToAddDataTable.Columns.Add(AgentModType);
                DataColumn AgentModPrice = new DataColumn("AgentModPrice", typeof(bool));
                OptionsToAddDataTable.Columns.Add(AgentModPrice);
                DataColumn AgentModQty = new DataColumn("AgentModQty", typeof(bool));
                OptionsToAddDataTable.Columns.Add(AgentModQty);
                DataColumn Elevation = new DataColumn("Elevation", typeof(bool));
                OptionsToAddDataTable.Columns.Add(Elevation);
                DataColumn IsElevation = new DataColumn("IsElevation", typeof(string));
                OptionsToAddDataTable.Columns.Add(IsElevation);
                DataColumn DollarAmt = new DataColumn("DollarAmt", typeof(bool));
                OptionsToAddDataTable.Columns.Add(DollarAmt);
                DataColumn ActiveDepositSched = new DataColumn("ActiveDepositSched", typeof(bool));
                OptionsToAddDataTable.Columns.Add(ActiveDepositSched);
                var createBy = User.Identity.Name.Split('@')[0];
                foreach (var optionToAdd in newOptionsToAdd)
                {
                    OptionsToAddDataTable.Rows.Add(optionToAdd.SubdivisionId, optionToAdd.OptionCode, optionToAdd.ModifiedOptionDesc, optionToAdd.MasterPlanId, optionToAdd.OptionGroupId, optionToAdd.Phase, optionToAdd.MasterOptionId, optionToAdd.HomeAreaId, optionToAdd.OptionTypeId, optionToAdd.UnitPrice, optionToAdd.UnitCost, optionToAdd.IsActive, optionToAdd.UnitQty, optionToAdd.OptionSelectionType, optionToAdd.PhasePlanId, optionToAdd.CreatedDateTime, createBy, optionToAdd.CustChoiceReq, optionToAdd.AgentModType, optionToAdd.AgentModPrice, optionToAdd.AgentModQty, optionToAdd.Elevation, optionToAdd.IsElevation, optionToAdd.DollarAmt, optionToAdd.ActiveDepositSched);
                }

                var conn = _configuration.GetConnectionString("ERPConnection");
                using (var connection = new SqlConnection(conn))
                {
                    connection.Open();
                    using (var bulkCopy = new SqlBulkCopy(connection))
                    {
                        bulkCopy.DestinationTableName = "AVAILABLE_PLAN_OPTION";
                        bulkCopy.ColumnMappings.Add("PhasePlanId", "PHASE_PLAN_ID");
                        bulkCopy.ColumnMappings.Add("MasterPlanId", "MASTER_PLAN_ID");
                        bulkCopy.ColumnMappings.Add("ModifiedOptionDesc", "MODIFIED_OPTION_DESC");
                        bulkCopy.ColumnMappings.Add("HomeAreaId", "HOME_AREA_ID");
                        bulkCopy.ColumnMappings.Add("Phase", "PHASE");
                        bulkCopy.ColumnMappings.Add("SubdivisionId", "SUBDIVISION_ID");
                        bulkCopy.ColumnMappings.Add("OptionTypeId", "OPTION_TYPE_ID");
                        bulkCopy.ColumnMappings.Add("OptionCode", "OPTION_CODE");
                        bulkCopy.ColumnMappings.Add("OptionGroupId", "OPTION_GROUP_ID");
                        bulkCopy.ColumnMappings.Add("MasterOptionId", "MASTER_OPTION_ID");
                        bulkCopy.ColumnMappings.Add("UnitPrice", "UNIT_PRICE");
                        bulkCopy.ColumnMappings.Add("UnitCost", "UNIT_COST");
                        bulkCopy.ColumnMappings.Add("UnitQty", "UNIT_QTY");
                        bulkCopy.ColumnMappings.Add("CustChoiceReq", "CUST_CHOICE_REQ");
                        bulkCopy.ColumnMappings.Add("AgentModType", "AGENT_MOD_TYPE");
                        bulkCopy.ColumnMappings.Add("AgentModPrice", "AGENT_MOD_QTY");
                        bulkCopy.ColumnMappings.Add("AgentModQty", "AGENT_MOD_PRICE");
                        bulkCopy.ColumnMappings.Add("Elevation", "ELEVATION");
                        bulkCopy.ColumnMappings.Add("IsElevation", "IS_ELEVATION");
                        bulkCopy.ColumnMappings.Add("OptionSelectionType", "OPTION_SELECTION_TYPE");
                        bulkCopy.ColumnMappings.Add("DollarAmt", "DOLLAR_AMT");
                        bulkCopy.ColumnMappings.Add("ActiveDepositSched", "ACTIVE_DEPOSIT_SCHED");
                        bulkCopy.ColumnMappings.Add("IsActive", "IsActive");
                        bulkCopy.ColumnMappings.Add("CreatedDateTime", "CreatedDateTime");
                        bulkCopy.ColumnMappings.Add("CreatedBy", "CreatedBy");
                        try
                        {
                            bulkCopy.WriteToServer(OptionsToAddDataTable);
                        }
                        catch (Exception ex)
                        {
                            var execption = ex.Message;
                            return new ResponseModel() { IsSuccess = false, Message = "Error adding options in plan." };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return new ResponseModel() { IsSuccess = false, Message = "Could not add plan." };
            }
            return new ResponseModel() { IsSuccess = true, Message = "Plan has been added." };
        }
        [HttpPost]
        public async Task<ResponseModel> AddMasterPlansToSubdivisionAsync([FromBody] AddPlanToSubdivisionPhaseModel addPlan)
        {
            try
            {
                string updateBy = User.Identity.Name.Split('@')[0];
                var getPlans = _context.MasterPlans.Where(x => addPlan.PlanToAddIds.Contains(x.MasterPlanId)).ToList();
                var checkActivePlanExists = await _context.PhasePlans.Where(x => getPlans.Select(y => y.MasterPlanId).Contains(x.MasterPlanId) && x.SubdivisionId == addPlan.SubdivisionId && x.Phase == addPlan.PhaseName).ToListAsync();
                if (checkActivePlanExists.Any(x => x.IsActive == true))
                {
                    return new ResponseModel() { IsSuccess = false, Message = "A plan you are adding already exists in this subdivision." };
                }

                var newPhasePlans = getPlans.Select(x => new PhasePlan()
                {
                    MasterPlanId = x.MasterPlanId,
                    SubdivisionId = (int)addPlan.SubdivisionId,
                    Phase = addPlan.PhaseName,
                    CreatedBy = User.Identity.Name.Split('@')[0],
                    CreatedDateTime = DateTime.Now
                }).ToList();
                _context.PhasePlans.AddRange(newPhasePlans);
                await _context.SaveChangesAsync();

                var newOptionsToAdd = _context.AsmHeaders.Include(x => x.MasterOption).Where(x => x.MasterPlanId != null && addPlan.PlanToAddIds.Contains((int)x.MasterPlanId) && x.MasterOption != null).ToList();

                var addOptions = (from a in newPhasePlans
                                  join b in newOptionsToAdd on a.MasterPlanId equals b.MasterPlanId
                                  select new AvailablePlanOption()
                                  {
                                      SubdivisionId = (int)addPlan.SubdivisionId,
                                      OptionCode = b.MasterOption.OptionCode,//PlanNum gets appended only on frontend view//$"{getPlan.PlanNum}{x.MasterOption.OptionCode}",
                                      MasterPlanId = b.MasterPlanId,
                                      OptionGroupId = b.MasterOption.OptionGroupId,
                                      ModifiedOptionDesc = b.MasterOption.OptionDesc,//They can change it later, but if it's just copy for the whole plan, need to have something
                                      Phase = a.Phase,
                                      MasterOptionId = b.MasterOptionId,
                                      HomeAreaId = b.HomeAreaId,
                                      OptionTypeId = 7, //This is the ID for "Standard" as the default
                                      UnitPrice = b.MasterOption.UnitPrice,
                                      UnitCost = b.MasterOption.UnitCost,
                                      UnitQty = b.MasterOption.UnitQty,
                                      IsActive = true,
                                      OptionSelectionType = b.MasterOption.OptionSelectionType ?? "S",
                                      PhasePlanId = a.PhasePlanId,
                                      CreatedDateTime = DateTime.Now,
                                      CreatedBy = updateBy,
                                      AgentModQty = b.MasterOption.AgentModQty ?? false,
                                      AgentModType = b.MasterOption.AgentModType ?? false,
                                      AgentModPrice = b.MasterOption.AgentModPrice ?? false,
                                      CustChoiceReq = b.MasterOption.CustChoiceReq ?? false,
                                      Elevation = b.MasterOption.Elevation ?? false,
                                      ActiveDepositSched = b.MasterOption.ActiveDepositSched ?? false,
                                      DollarAmt = b.MasterOption.DollarAmt ?? false,
                                  }).ToList();
                await _context.AvailablePlanOptions.BulkInsertAsync(addOptions, options => options.IncludeGraph = true);

                //foreach (var planId in addPlan.PlanToAddIds)
                //{
                //    string updateBy = User.Identity.Name.Split('@')[0];
                //    var getPlan = await _context.MasterPlans.FirstOrDefaultAsync(x => x.MasterPlanId == planId);
                //   // var checkActivePlanExists = await _context.PhasePlans.Where(x => x.MasterPlanId == planId && x.SubdivisionId == addPlan.SubdivisionId && x.Phase == addPlan.PhaseName).ToListAsync();
                //    var newPhasePlan = new PhasePlan();
                //    if (checkActivePlanExists.Any(x => x.IsActive == true))
                //    {
                //        return new ResponseModel() { IsSuccess = false, Message = "Could not add plan. This plan may already exist in this subdivision." };
                //    }
                //    else if (checkActivePlanExists.Any(x => x.IsActive == false))
                //    {

                //        var reactivatePlan = checkActivePlanExists.Where(x => x.IsActive == false).First();
                //        reactivatePlan.IsActive = true;
                //        reactivatePlan.UpdatedBy = updateBy;
                //        _context.PhasePlans.Update(reactivatePlan);
                //        await _context.SaveChangesAsync();
                //        //deactivate any options associated with the plan, then readd as below
                //        //TODO: reactivate seems like a bad idea
                //        var findPlanOptions = _context.AvailablePlanOptions.Where(x => x.PhasePlanId == reactivatePlan.PhasePlanId);
                //        if (findPlanOptions.Any())
                //        {
                //            await findPlanOptions.ExecuteUpdateAsync(s => s.SetProperty(b => b.IsActive, false).SetProperty(b => b.UpdatedBy, updateBy).SetProperty(b => b.UpdatedDateTime, DateTime.Now));
                //        }
                //        newPhasePlan = reactivatePlan;
                //    }
                //    else
                //    {
                //        newPhasePlan = new PhasePlan()
                //        {
                //            MasterPlanId = planId,
                //            SubdivisionId = (int)addPlan.SubdivisionId,
                //            Phase = addPlan.PhaseName,
                //            CreatedBy = User.Identity.Name.Split('@')[0],
                //            CreatedDateTime = DateTime.Now
                //        };
                //        await _context.PhasePlans.AddAsync(newPhasePlan);
                //        await _context.SaveChangesAsync();
                //    }
                //    //add the options
                //    var userName = User.Identity.Name.Split('@')[0];
                //    var newOptionsToAdd = await _context.AsmHeaders.Where(x => x.MasterPlanId == planId && x.MasterOption != null).Select(x => new AvailablePlanOption()
                //    {
                //        SubdivisionId = (int)addPlan.SubdivisionId,
                //        OptionCode = x.MasterOption.OptionCode,//PlanNum gets appended only on frontend view//$"{getPlan.PlanNum}{x.MasterOption.OptionCode}",
                //        MasterPlanId = x.MasterPlanId,
                //        OptionGroupId = x.MasterOption.OptionGroupId,
                //        ModifiedOptionDesc = x.MasterOption.OptionDesc,//They can change it later, but if it's just copy for the whole plan, need to have something
                //        Phase = newPhasePlan.Phase,
                //        MasterOptionId = x.MasterOptionId,
                //        HomeAreaId = x.HomeAreaId,
                //        OptionTypeId = 7, //This is the ID for "Standard" as the default
                //        UnitPrice = x.MasterOption.UnitPrice,
                //        UnitCost = x.MasterOption.UnitCost,
                //        UnitQty = x.MasterOption.UnitQty,
                //        IsActive = true,
                //        OptionSelectionType = x.MasterOption.OptionSelectionType ?? "S",
                //        PhasePlanId = newPhasePlan.PhasePlanId,
                //        CreatedDateTime = DateTime.Now,
                //        CreatedBy = userName,
                //        AgentModQty = x.MasterOption.AgentModQty ?? false,
                //        AgentModType = x.MasterOption.AgentModType ?? false,
                //        AgentModPrice = x.MasterOption.AgentModPrice ?? false,
                //        CustChoiceReq = x.MasterOption.CustChoiceReq ?? false,
                //        Elevation = x.MasterOption.Elevation ?? false,
                //        ActiveDepositSched = x.MasterOption.ActiveDepositSched ?? false,
                //        DollarAmt = x.MasterOption.DollarAmt ?? false,
                //    }).ToListAsync();

                //    DataTable OptionsToAddDataTable = new DataTable("AvailablePlanOption");
                //    DataColumn SubdivisionId = new DataColumn("SubdivisionId", typeof(int));
                //    OptionsToAddDataTable.Columns.Add(SubdivisionId);
                //    DataColumn OptionCode = new DataColumn("OptionCode", typeof(string));
                //    OptionsToAddDataTable.Columns.Add(OptionCode);
                //    DataColumn ModifiedOptionDesc = new DataColumn("ModifiedOptionDesc", typeof(string));
                //    OptionsToAddDataTable.Columns.Add(ModifiedOptionDesc);
                //    DataColumn MasterPlanId = new DataColumn("MasterPlanId", typeof(int));
                //    OptionsToAddDataTable.Columns.Add(MasterPlanId);
                //    DataColumn OptionGroupId = new DataColumn("OptionGroupId", typeof(int));
                //    OptionsToAddDataTable.Columns.Add(OptionGroupId);
                //    DataColumn Phase = new DataColumn("Phase", typeof(string));
                //    OptionsToAddDataTable.Columns.Add(Phase);
                //    DataColumn MasterOptionId = new DataColumn("MasterOptionId", typeof(int));
                //    OptionsToAddDataTable.Columns.Add(MasterOptionId);
                //    DataColumn HomeAreaId = new DataColumn("HomeAreaId", typeof(int));
                //    OptionsToAddDataTable.Columns.Add(HomeAreaId);
                //    DataColumn OptionTypeId = new DataColumn("OptionTypeId", typeof(int));
                //    OptionsToAddDataTable.Columns.Add(OptionTypeId);
                //    DataColumn UnitPrice = new DataColumn("UnitPrice", typeof(decimal));
                //    OptionsToAddDataTable.Columns.Add(UnitPrice);
                //    DataColumn UnitCost = new DataColumn("UnitCost", typeof(decimal));
                //    OptionsToAddDataTable.Columns.Add(UnitCost);
                //    DataColumn IsActive = new DataColumn("IsActive", typeof(bool));
                //    OptionsToAddDataTable.Columns.Add(IsActive);
                //    DataColumn UnitQty = new DataColumn("UnitQty", typeof(decimal));
                //    OptionsToAddDataTable.Columns.Add(UnitQty);
                //    DataColumn OptionSelectionType = new DataColumn("OptionSelectionType", typeof(string));
                //    OptionsToAddDataTable.Columns.Add(OptionSelectionType);
                //    DataColumn PhasePlanId = new DataColumn("PhasePlanId", typeof(int));
                //    OptionsToAddDataTable.Columns.Add(PhasePlanId);
                //    DataColumn CreatedDateTime = new DataColumn("CreatedDateTime", typeof(DateTime));
                //    OptionsToAddDataTable.Columns.Add(CreatedDateTime);
                //    DataColumn CreatedBy = new DataColumn("CreatedBy", typeof(string));
                //    OptionsToAddDataTable.Columns.Add(CreatedBy);
                //    DataColumn CustChoiceReq = new DataColumn("CustChoiceReq", typeof(bool));
                //    OptionsToAddDataTable.Columns.Add(CustChoiceReq);
                //    DataColumn AgentModType = new DataColumn("AgentModType", typeof(bool));
                //    OptionsToAddDataTable.Columns.Add(AgentModType);
                //    DataColumn AgentModPrice = new DataColumn("AgentModPrice", typeof(bool));
                //    OptionsToAddDataTable.Columns.Add(AgentModPrice);
                //    DataColumn AgentModQty = new DataColumn("AgentModQty", typeof(bool));
                //    OptionsToAddDataTable.Columns.Add(AgentModQty);
                //    DataColumn Elevation = new DataColumn("Elevation", typeof(bool));
                //    OptionsToAddDataTable.Columns.Add(Elevation);
                //    DataColumn DollarAmt = new DataColumn("DollarAmt", typeof(bool));
                //    OptionsToAddDataTable.Columns.Add(DollarAmt);
                //    DataColumn ActiveDepositSched = new DataColumn("ActiveDepositSched", typeof(bool));
                //    OptionsToAddDataTable.Columns.Add(ActiveDepositSched);
                //    var createBy = User.Identity.Name.Split('@')[0];
                //    foreach (var optionToAdd in newOptionsToAdd)
                //    {
                //        OptionsToAddDataTable.Rows.Add(optionToAdd.SubdivisionId, optionToAdd.OptionCode, optionToAdd.ModifiedOptionDesc, optionToAdd.MasterPlanId, optionToAdd.OptionGroupId, optionToAdd.Phase, optionToAdd.MasterOptionId, optionToAdd.HomeAreaId, optionToAdd.OptionTypeId, optionToAdd.UnitPrice, optionToAdd.UnitCost, optionToAdd.IsActive, optionToAdd.UnitQty, optionToAdd.OptionSelectionType, optionToAdd.PhasePlanId, optionToAdd.CreatedDateTime, createBy, optionToAdd.CustChoiceReq, optionToAdd.AgentModType, optionToAdd.AgentModPrice, optionToAdd.AgentModQty, optionToAdd.Elevation, optionToAdd.DollarAmt, optionToAdd.ActiveDepositSched);
                //    }

                //    var conn = _configuration.GetConnectionString("ERPConnection");
                //    using (var connection = new SqlConnection(conn))
                //    {
                //        connection.Open();
                //        using (var bulkCopy = new SqlBulkCopy(connection))
                //        {
                //            bulkCopy.DestinationTableName = "AVAILABLE_PLAN_OPTION";
                //            bulkCopy.ColumnMappings.Add("PhasePlanId", "PHASE_PLAN_ID");
                //            bulkCopy.ColumnMappings.Add("MasterPlanId", "MASTER_PLAN_ID");
                //            bulkCopy.ColumnMappings.Add("ModifiedOptionDesc", "MODIFIED_OPTION_DESC");
                //            bulkCopy.ColumnMappings.Add("HomeAreaId", "HOME_AREA_ID");
                //            bulkCopy.ColumnMappings.Add("Phase", "PHASE");
                //            bulkCopy.ColumnMappings.Add("SubdivisionId", "SUBDIVISION_ID");
                //            bulkCopy.ColumnMappings.Add("OptionTypeId", "OPTION_TYPE_ID");
                //            bulkCopy.ColumnMappings.Add("OptionCode", "OPTION_CODE");
                //            bulkCopy.ColumnMappings.Add("OptionGroupId", "OPTION_GROUP_ID");
                //            bulkCopy.ColumnMappings.Add("MasterOptionId", "MASTER_OPTION_ID");
                //            bulkCopy.ColumnMappings.Add("UnitPrice", "UNIT_PRICE");
                //            bulkCopy.ColumnMappings.Add("UnitCost", "UNIT_COST");
                //            bulkCopy.ColumnMappings.Add("UnitQty", "UNIT_QTY");
                //            bulkCopy.ColumnMappings.Add("CustChoiceReq", "CUST_CHOICE_REQ");
                //            bulkCopy.ColumnMappings.Add("AgentModType", "AGENT_MOD_TYPE");
                //            bulkCopy.ColumnMappings.Add("AgentModPrice", "AGENT_MOD_QTY");
                //            bulkCopy.ColumnMappings.Add("AgentModQty", "AGENT_MOD_PRICE");
                //            bulkCopy.ColumnMappings.Add("Elevation", "ELEVATION");
                //            bulkCopy.ColumnMappings.Add("OptionSelectionType", "OPTION_SELECTION_TYPE");
                //            bulkCopy.ColumnMappings.Add("DollarAmt", "DOLLAR_AMT");
                //            bulkCopy.ColumnMappings.Add("ActiveDepositSched", "ACTIVE_DEPOSIT_SCHED");
                //            bulkCopy.ColumnMappings.Add("IsActive", "IsActive");
                //            bulkCopy.ColumnMappings.Add("CreatedDateTime", "CreatedDateTime");
                //            bulkCopy.ColumnMappings.Add("CreatedBy", "CreatedBy");
                //            try
                //            {
                //                bulkCopy.WriteToServer(OptionsToAddDataTable);
                //            }
                //            catch (Exception ex)
                //            {
                //                var execption = ex.Message;
                //                return new ResponseModel() { IsSuccess = false, Message = "Error adding options in plan." };
                //            }
                //        }
                //    }
                //}

            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return new ResponseModel() { IsSuccess = false, Message = "Could not add plan." };
            }
            return new ResponseModel() { IsSuccess = true, Message = "Plan has been added." };
        }

        [HttpGet]
        public async Task<IActionResult> GetMasterPlansAsync()
        {
            try
            {
                var plans = await _context.MasterPlans.Where(x => !string.IsNullOrWhiteSpace(x.PlanName) && x.IsActive == true).OrderBy(x => x.PlanNum).ToListAsync();
                var plansDto = _mapper.Map<List<MasterPlanDto>>(plans);

                foreach (var plan in plansDto)
                {
                    plan.DisplayName = $"{plan.PlanNum} - {plan.PlanName} ";
                    plan.MasterPlanId = plan.MasterPlanId;
                    plan.PlanName = plan.PlanName;
                    plan.PlanNum = plan.PlanNum;
                }

                return new OkObjectResult(new ResponseModel<List<MasterPlanDto>> { Value = plansDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MasterPlanDto>> { IsSuccess = false, Message = "Failed to get Master Plans", Value = null });
            }
        }

        [HttpGet("{masterPlanId}")]
        public async Task<IActionResult> GetMasterPlansExceptAsync(int masterPlanId)
        {
            try
            {
                var plans = await _context.MasterPlans.Where(x => x.MasterPlanId != masterPlanId && x.IsActive == true).OrderBy(x => x.PlanNum).ToListAsync();

                var plansDto = _mapper.Map<List<MasterPlanDto>>(plans);

                foreach (var plan in plansDto)
                {
                    plan.DisplayName = $"{plan.PlanNum} - {plan.PlanName} ";
                    plan.MasterPlanId = plan.MasterPlanId;
                    plan.PlanName = plan.PlanName;
                    plan.PlanNum = plan.PlanNum;
                }

                return new OkObjectResult(new ResponseModel<List<MasterPlanDto>> { Value = plansDto, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MasterPlanDto>> { IsSuccess = false, Message = "Failed to get Master Plans", Value = null });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetPhasePlansAsync()
        {

            try
            {
                var plans = await _context.PhasePlans.Where(x => x.IsActive == true).Select(x => new SubdivisionPlanModel()
                {
                    PhasePlanId = x.PhasePlanId,
                    SubdivisionName = x.Subdivision.SubdivisionName,
                    SubdivisionId = x.Subdivision.SubdivisionId,
                    PlanName = x.MasterPlan.PlanName,
                    PlanNum = x.MasterPlan.PlanNum,
                    DisplaySubdivisionPlanName = (!string.IsNullOrWhiteSpace(x.Subdivision.SubdivisionName)) ? $"{x.Subdivision.SubdivisionName} - {x.MasterPlan.PlanNum} - {x.MasterPlan.PlanName}" : $"{x.MasterPlan.PlanNum} - {x.MasterPlan.PlanName}",
                    Phase = x.Phase,
                    MasterPlanId = x.MasterPlanId,
                }).OrderBy(x => x.SubdivisionName).ToListAsync();
                return new OkObjectResult(new ResponseModel<List<SubdivisionPlanModel>> { Value = plans, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<SubdivisionPlanModel>> { IsSuccess = false, Message = "Failed to get Phase Plan Types", Value = null });
            }
        }

        [HttpGet("{planId}")]
        public async Task<MasterPlanDto> GetMasterPlanAsync(int? planId)
        {
            var plan = await _context.MasterPlans.SingleOrDefaultAsync(x => x.MasterPlanId == planId).ConfigureAwait(false);
            var planDto = _mapper.Map<MasterPlanDto>(plan);
            return planDto;
        }

        [HttpPost]
        public async Task<IActionResult> AddMasterPlanAsync([FromBody] MasterPlanDto Plan)
        {
            try
            {
                var createdBy = User.Identity.Name.Split('@')[0];
                // Check if Plan with the same Plan Number exist already
                var checkExistingPlan = await _context.MasterPlans.FirstOrDefaultAsync(x => x.PlanNum == Plan.PlanNum).ConfigureAwait(false);
                if (checkExistingPlan == null)
                {
                    var addPlan = _mapper.Map<MasterPlan>(Plan);
                    addPlan.CreatedBy = createdBy;
                    Plan.IsActive = true;
                    await _context.MasterPlans.AddAsync(addPlan);
                    await _context.SaveChangesAsync();
                    //add the new base house option  
                    var baseHouseOption = new AsmHeader
                    {
                        MasterPlanId = addPlan.MasterPlanId,
                        MasterOptionId = 1,//this is the base house option
                        IsBaseHouse = "T",
                        AssemblyDesc = $"{addPlan.PlanName} BASE HOUSE",
                        AssemblyCode = $"{addPlan.PlanNum}A000000",
                        AssemblyNotes = "BASE HOUSE",
                        CreatedBy = createdBy,
                    };
                    await _context.AsmHeaders.AddAsync(baseHouseOption);
                    await _context.SaveChangesAsync();
                    return new OkObjectResult(new ResponseModel<MasterPlanDto> { Value = Plan, IsSuccess = true, Message = "Plan added successfully" });
                }
                else if (checkExistingPlan.IsActive == false)
                {
                    //reactivate if exists but inactive
                    checkExistingPlan.IsActive = true;
                    checkExistingPlan.UpdatedBy = createdBy;
                    checkExistingPlan.UpdatedDateTime = DateTime.Now;
                    _context.MasterPlans.Update(checkExistingPlan);
                    await _context.SaveChangesAsync();
                    return new OkObjectResult(new ResponseModel<MasterPlanDto> { Value = Plan, IsSuccess = true, Message = "The plan number you added alredy exists but was inactive. The plan has been reactivated" });

                }
                else
                {
                    return new OkObjectResult(new ResponseModel<MasterPlanDto> { Value = Plan, IsSuccess = false, Message = "The plan number you added alredy exists." });
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MasterPlanDto> { IsSuccess = false, Message = "Failed to Add Master Plan", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateMasterPlanAsync([FromBody] MasterPlanDto Plan)
        {
            try
            {
                var findPlan = _context.MasterPlans.SingleOrDefault(x => x.MasterPlanId == Plan.MasterPlanId);
                findPlan.IsActive = true;
                findPlan.PlanName = Plan.PlanName;
                findPlan.PlanNum = Plan.PlanNum;
                findPlan.SquareFeet = Plan.SquareFeet;
                findPlan.PlanTypeId = Plan.PlanTypeId;
                findPlan.PlanModelNum = Plan.PlanModelNum;
                findPlan.UpdatedBy = User.Identity.Name.Split('@')[0];
                findPlan.UpdatedDateTime = DateTime.Now;
                _context.MasterPlans.Update(findPlan);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<MasterPlanDto> { Value = Plan, IsSuccess = true, Message = "Updated master plan." });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MasterPlanDto> { IsSuccess = false, Message = "Failed to Update Master Plan", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteMasterPlanAsync([FromBody] MasterPlanDto Plan)
        {
            try
            {
                var findPlan = _context.MasterPlans.SingleOrDefault(x => x.MasterPlanId == Plan.MasterPlanId);
                findPlan.IsActive = false;
                findPlan.UpdatedBy = User.Identity.Name.Split('@')[0];
                findPlan.UpdatedDateTime = DateTime.Now;
                _context.MasterPlans.Update(findPlan);
                await _context.SaveChangesAsync();
                return new OkObjectResult(new ResponseModel<MasterPlanDto> { Value = Plan, IsSuccess = true, Message = "Deleted Master Plan." });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MasterPlanDto> { IsSuccess = false, Message = "Failed to Delete Master Plan", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> CopyMasterPlanAsync([FromBody] MasterPlanDto Plan)
        {

            try
            {
                var checkExists = _context.MasterPlans.Where(x => x.PlanNum == Plan.PlanNum).Any();
                if (checkExists)
                {
                    var planExists = _context.MasterPlans.Where(x => x.PlanNum == Plan.PlanNum).FirstOrDefault();
                    if(planExists?.IsActive == true)
                    {
                        return Ok(new ResponseModel<MasterPlanDto> { IsSuccess = false, Message = "Plan number exists and is active", Value = Plan });
                    }
                    if(planExists?.IsActive == false)
                    {
                        planExists.IsActive = true;
                        planExists.UpdatedBy = User.Identity.Name.Split('@')[0];
                        planExists.UpdatedDateTime = DateTime.Now;
                        _context.MasterPlans.Update(planExists);
                        await _context.SaveChangesAsync();
                        return Ok(new ResponseModel<MasterPlanDto> { IsSuccess = true, Message = "Plan number exists and was inactive. Plan has been reactivated.", Value = Plan });
                    }
                    
                }
                var planToAdd = new MasterPlan()
                {
                    PlanName = Plan.PlanName,//This should be the new name they type in
                    PlanNum = Plan.PlanNum,//New plan num they enter
                    PlanSize = Plan.PlanSize,//Everything else should copy
                    PlanTypeId = Plan.PlanTypeId,
                    IsActive = true,
                    UpdatedBy = User.Identity.Name.Split('@')[0]
                };
                await _context.MasterPlans.AddAsync(planToAdd);
                await _context.SaveChangesAsync();

                //copy all the options from Plan.PlanId to plantoAdd.id
                var headersFromCopyPlan = _context.AsmHeaders.Where(x => x.MasterPlanId == Plan.MasterPlanId && x.IsActive == true);
                var createdBy = User.Identity.Name.Split('@')[0];
                var copyHeaders = headersFromCopyPlan.Select(x => new CopyHeaderModel()
                {
                    CopyFromHeader = x,
                    CopyToHeader = new AsmHeader()
                    {
                        MasterPlanId = planToAdd.MasterPlanId,
                        AsmGroupId = x.AsmGroupId,
                        MasterOptionId = x.MasterOptionId,
                        HomeAreaId = x.HomeAreaId,
                        PeHeader = x.PeHeader,
                        AssemblyCode = $"{Plan.PlanNum}{x.MasterOption.OptionCode}",//assembly code is composed of new plan num and option code
                        AssemblyDesc = x.AssemblyDesc,//TODO: if it was the base house it probably needs new name
                        AssemblyNotes = x.AssemblyNotes,
                        AssemblyUnit = x.AssemblyUnit,
                        AssemblySize = x.AssemblySize,
                        DeletedFromPe = x.DeletedFromPe,
                        Formula = x.Formula,
                        Calculation = x.Calculation,
                        EstDbOwner = x.EstDbOwner,
                        TlpeOptionCategoryId = x.TlpeOptionCategoryId,
                        OptionScope = x.OptionScope,
                        IsElevation = x.IsElevation,
                        IsBaseHouse = x.IsBaseHouse,
                        IsActive = x.IsActive,
                        CreatedBy = createdBy,
                    }
                }).ToList();
                await _context.AsmHeaders.BulkInsertAsync(copyHeaders.Select(x => x.CopyToHeader), options => options.IncludeGraph = true);

                //_context.AsmHeaders.AddRange(copyHeaders.Select(x => x.CopyToHeader));
                // _context.SaveChanges();//May be slow on large plan

                //the copyToHeaders should now have new ids
                // var getItems = _context.AsmDetails.Where(x => (copyHeaders.Select(x => x.CopyFromHeader.AsmHeaderId)).Contains(x.AsmHeaderId)).ToList();

                var getItems = _context.AsmDetails.Include(x => x.AsmHeader.MasterOption).Include(x => x.MasterItem).Where(x => copyHeaders.Select(x => x.CopyFromHeader.AsmHeaderId).Contains(x.AsmHeaderId) && x.IsActive == true).ToList();
                var getLumpSumItems = getItems.Where(x => x.MasterItem.PlanSpecific == "T").ToList();
                var getNonLumpItems = getItems.Where(x => x.MasterItem.PlanSpecific == "F").ToList();
                //var getLumpSumItems = _context.AsmDetails.Include(x => x.AsmHeader.MasterOption).Include(x => x.MasterItem).Where(x => copyHeaders.Select(x => x.CopyFromHeader.AsmHeaderId).Contains(x.AsmHeaderId) && x.MasterItem.PlanSpecific == "T").ToList();
                //var getNonLumpItems = _context.AsmDetails.Include(x => x.AsmHeader.MasterOption).Include(x => x.MasterItem).Where(x => copyHeaders.Select(x => x.CopyFromHeader.AsmHeaderId).Contains(x.AsmHeaderId) && x.MasterItem.PlanSpecific == "F").ToList();

                //lump sum (plan specific) -- need to make copies of the master items, new master item phases, and new asm details 
                //TODO: check master item doesn't exist
                List<AsmDetail> lumpSumItemsWhereMasterItemExists = new List<AsmDetail>();
                foreach (var item in getLumpSumItems)
                {
                    //check if the master item exists, so don't add it again

                    //somehow have to find options that have the same (plan-specific) item in multiple options to only add that one once
                    //var findExists = _context.MasterItems.Where(x => x.MasterItemPhase.PhaseCode == $"{Plan.PlanNum}{item.AsmHeader.MasterOption.OptionCode}" && x.ItemNumber == item.MasterItem.ItemNumber && x.ItemDesc == item.MasterItem.ItemDesc).Any();//Should be none here since this is adding a new plan, no, not true, since adding multiple options, and doing in bulk, not loop, so ??
                    //if (findExists)
                    //{
                    //    getLumpSumItems.Remove(item);
                    //    lumpSumItemsWhereMasterItemExists.Add(item);
                    //}

                }
                var distinctHeaderLumpItems = getLumpSumItems.Select(x => x.AsmHeader).Distinct().ToList();
                var newMasterItemPhases = (from x in copyHeaders
                                           join y in distinctHeaderLumpItems on x.CopyFromHeader.AsmHeaderId equals y.AsmHeaderId
                                           select new MasterItemPhasis()
                                           {
                                               PhaseCode = $"{Plan.PlanNum}{y.MasterOption.OptionCode}",//These  different now because of the new plan//TODO: not sure if this is all included
                                               PhaseDesc = y.AssemblyDesc,
                                               PhaseNotes = y.AssemblyNotes,
                                               AsmHeaderId = x.CopyToHeader.AsmHeaderId,
                                               CreatedBy = User.Identity.Name.Split('@')[0],
                                           }).ToList();
                //_context.MasterItemPhases.AddRange(newMasterItemPhases);
                // await _context.SaveChangesAsync();
                await _context.MasterItemPhases.BulkInsertAsync(newMasterItemPhases, options => options.IncludeGraph = true);

                var joinedLumpData = (from x in copyHeaders
                                      join y in getLumpSumItems on x.CopyFromHeader.AsmHeaderId equals y.AsmHeaderId
                                      join z in newMasterItemPhases on x.CopyToHeader.AsmHeaderId equals z.AsmHeaderId
                                      select new AsmDetail()
                                      {
                                          AsmHeaderId = x.CopyToHeader.AsmHeaderId,
                                          BomClassId = y.BomClassId,
                                          //MasterItemId = y.MasterItemId,
                                          Ordinality = y.Ordinality,
                                          Factor = y.Factor,
                                          Calculation = y.Calculation,
                                          CalculationCode = y.CalculationCode,
                                          Formula = y.Formula,
                                          UseItemFormula = y.UseItemFormula,
                                          CreatedBy = User.Identity.Name.Split('@')[0],
                                          IsActive = true,
                                          MasterItem = new MasterItem()
                                          {
                                              ItemNumber = y.MasterItem.ItemNumber,//TODO: New item number??? from phase code plan code stuf
                                              ItemDesc = y.MasterItem.ItemDesc,//1/3/25 changed to keep the item desc rather than use the option desc
                                              BomClassId = y.MasterItem.BomClassId,//everything else should copy from original master item
                                              PlanSpecific = "T",
                                              PeCategoryCode = y.MasterItem.PeCategoryCode,
                                              PeUnitPrice = y.MasterItem.PeUnitPrice,
                                              PeUnitPriceDtCg = y.MasterItem.PeUnitPriceDtCg,
                                              CalcPercent = y.MasterItem.CalcPercent,
                                              DeletedFromPe = y.MasterItem.DeletedFromPe,
                                              ExcludeFromPo = y.MasterItem.ExcludeFromPo,
                                              OrderUnit = y.MasterItem.OrderUnit,
                                              TakeoffUnit = y.MasterItem.TakeoffUnit,
                                              Taxable = y.MasterItem.Taxable,
                                              CalcBasis = y.MasterItem.CalcBasis,
                                              CnvFctr = y.MasterItem.CnvFctr,
                                              CreatedBy = User.Identity.Name.Split('@')[0],
                                              Formula = y.MasterItem.Formula,
                                              ItemNotes = y.MasterItem.ItemNotes,
                                              JcCategory = y.MasterItem.JcCategory,
                                              JcPhase = y.MasterItem.JcPhase,
                                              Multdiv = y.MasterItem.Multdiv,
                                              MasterItemPhaseId = z.MasterItemPhaseId,
                                              //MasterItemPhase = new MasterItemPhasis()
                                              //{
                                              //    PhaseCode = $"{Plan.PlanNum}{y.AsmHeader.MasterOption.OptionCode}",//These  different now because of the new plan//TODO: not sure if this is all included
                                              //    PhaseDesc = y.AsmHeader.AssemblyDesc,
                                              //    PhaseNotes = y.AsmHeader.AssemblyNotes,
                                              //    AsmHeaderId = x.CopyToHeader.AsmHeaderId,
                                              //    CreatedBy = User.Identity.Name.Split('@')[0],
                                              //    //TODO: multiple master items could actually share the same phase if it's from same option - so this is wrong -- need to first insert a phase for each option, or find the phase for each opiton
                                              //},
                                          },

                                      }).ToList();


                //_context.AsmDetails.AddRange(joinedLumpData);//slow
                await _context.AsmDetails.BulkInsertAsync(joinedLumpData, options => options.IncludeGraph = true);
                // await _context.SaveChangesAsync();

                //lump where master item already exists - just add asmdetail 
                string createBy = User.Identity.Name.Split("@")[0];
                //var joinedDataLumpItemExists = from x in copyHeaders
                //                               join y in lumpSumItemsWhereMasterItemExists on x.CopyFromHeader.AsmHeaderId equals y.AsmHeaderId
                //                               select new AsmDetail()
                //                               {
                //                                   AsmHeaderId = x.CopyToHeader.AsmHeaderId,
                //                                   BomClassId = y.BomClassId,
                //                                   MasterItemId = y.MasterItemId,
                //                                   Ordinality = y.Ordinality,
                //                                   Calculation = y.Calculation,
                //                                   CalculationCode = y.CalculationCode,
                //                                   Formula = y.Formula,
                //                                   Factor = y.Factor,
                //                                   UseItemFormula = y.UseItemFormula,
                //                                   CreatedBy = createBy,
                //                                   IsActive = true,
                //                                   CreatedDateTime = DateTime.Now
                //                               };

                //non-lump
                var joinedData = from x in copyHeaders
                                 join y in getNonLumpItems on x.CopyFromHeader.AsmHeaderId equals y.AsmHeaderId
                                 select new AsmDetail()
                                 {
                                     AsmHeaderId = x.CopyToHeader.AsmHeaderId,
                                     BomClassId = y.BomClassId,
                                     MasterItemId = y.MasterItemId,
                                     Ordinality = y.Ordinality,
                                     Calculation = y.Calculation,
                                     CalculationCode = y.CalculationCode,
                                     Factor = y.Factor,
                                     Formula = y.Formula,
                                     UseItemFormula = y.UseItemFormula,
                                     CreatedBy = createdBy,
                                     IsActive = true,
                                     CreatedDateTime = DateTime.Now
                                 };

                await _context.AsmDetails.BulkInsertAsync(joinedData);


                ////JoinedData should now be the data to insert into AsmDetails,
                //                DataTable OptionsToAddDataTable = new DataTable("AsmDetails");
                //                DataColumn AsmHeaderId = new DataColumn("AsmHeaderId", typeof(int));
                //                OptionsToAddDataTable.Columns.Add(AsmHeaderId);
                //                DataColumn MasterItemId = new DataColumn("MasterItemId", typeof(int));
                //                OptionsToAddDataTable.Columns.Add(MasterItemId);
                //                DataColumn BomClassId = new DataColumn("BomClassId", typeof(int));
                //                OptionsToAddDataTable.Columns.Add(BomClassId);
                //                DataColumn Ordinality = new DataColumn("Ordinality", typeof(int));
                //                OptionsToAddDataTable.Columns.Add(Ordinality);
                //                DataColumn Calculation = new DataColumn("Calculation", typeof(string));
                //                OptionsToAddDataTable.Columns.Add(Calculation);
                //                DataColumn CalculationCode = new DataColumn("CalculationCode", typeof(int));
                //                OptionsToAddDataTable.Columns.Add(CalculationCode);
                //                DataColumn Formula = new DataColumn("Formula", typeof(string));
                //                OptionsToAddDataTable.Columns.Add(Formula);
                //                DataColumn Factor = new DataColumn("Factor", typeof(double));
                //                OptionsToAddDataTable.Columns.Add(Factor);
                //                DataColumn UseItemFormula = new DataColumn("UseItemFormula", typeof(string));
                //                OptionsToAddDataTable.Columns.Add(UseItemFormula);
                //                DataColumn CreatedBy = new DataColumn("CreatedBy");
                //                OptionsToAddDataTable.Columns.Add(CreatedBy);


                //                foreach (var itemToAdd in joinedData)
                //                {
                //                    OptionsToAddDataTable.Rows.Add(itemToAdd.AsmHeaderId, itemToAdd.MasterItemId, itemToAdd.BomClassId, itemToAdd.Ordinality, itemToAdd.Calculation, itemToAdd.CalculationCode, itemToAdd.Formula, itemToAdd.Factor, itemToAdd.UseItemFormula, createBy);
                //                }
                //                foreach (var itemToAdd in joinedDataLumpItemExists)
                //                {
                //                    OptionsToAddDataTable.Rows.Add(itemToAdd.AsmHeaderId, itemToAdd.MasterItemId, itemToAdd.BomClassId, itemToAdd.Ordinality, itemToAdd.Calculation, itemToAdd.CalculationCode, itemToAdd.Formula, itemToAdd.Factor, itemToAdd.UseItemFormula, createBy);
                //                }
                //                //foreach (var itemToAdd in joinedLumpData)
                //                //{
                //                //    OptionsToAddDataTable.Rows.Add(itemToAdd.AsmHeaderId, itemToAdd.MasterItemId, itemToAdd.BomClassId, itemToAdd.Ordinality, itemToAdd.Calculation, itemToAdd.CalculationCode, itemToAdd.Formula, itemToAdd.UseItemFormula, createBy);
                //                //}
                //                var conn = _configuration.GetConnectionString("ERPConnection");
                //                using (var connection = new SqlConnection(conn))
                //                {
                //                    connection.Open();
                //                    using (var bulkCopy = new SqlBulkCopy(connection))
                //                    {
                //                        bulkCopy.DestinationTableName = "ASM_DETAIL";
                //                        bulkCopy.ColumnMappings.Add("AsmHeaderId", "ASM_HEADER_ID");
                //                        bulkCopy.ColumnMappings.Add("MasterItemId", "MASTER_ITEM_ID");
                //                        bulkCopy.ColumnMappings.Add("BomClassId", "BOM_CLASS_ID");
                //                        bulkCopy.ColumnMappings.Add("Ordinality", "ORDINALITY");
                //                        bulkCopy.ColumnMappings.Add("Calculation", "CALCULATION");
                //                        bulkCopy.ColumnMappings.Add("CalculationCode", "CALCULATION_CODE");
                //                        bulkCopy.ColumnMappings.Add("Formula", "FORMULA");
                //                        bulkCopy.ColumnMappings.Add("Factor", "FACTOR");
                //                        bulkCopy.ColumnMappings.Add("UseItemFormula", "USE_ITEM_FORMULA");
                //                        bulkCopy.ColumnMappings.Add("CreatedBy", "CreatedBy");
                //                        try
                //                        {
                //                            bulkCopy.WriteToServer(OptionsToAddDataTable);
                //                        }
                //                        catch (Exception ex)
                //                        {
                //                            var username = User.Identity?.Name?.Split('@')[0];
                //#if DEBUG
                //                            _logger.Debug(ex);
                //#else
                //                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
                //#endif
                //                            throw;
                //                        }
                //                    }
                //                }

                return new OkObjectResult(new ResponseModel<MasterPlanDto> { Value = Plan, IsSuccess = true, Message = "Plan copied." });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<MasterPlanDto> { IsSuccess = false, Message = "Failed to Copy Master Plan", Value = null });
            }
        }

        [HttpGet("{planId}")]
        public async Task<IActionResult> GetAvailablePlanOptionInSubdivisionAsync(int planId)
        {
            try
            {
                var subdivisions = new List<SubdivisionDto>();

                var connString = _configuration.GetConnectionString("ERPConnection");

                using (var connection = new SqlConnection(connString))
                {
                    await connection.OpenAsync();

                    var query = "SELECT DISTINCT SUBDIVISION_ID FROM dbo.AVAILABLE_PLAN_OPTION WHERE MASTER_PLAN_ID = @planId AND IsActive = 1";

                    var command = new SqlCommand(query, connection);
                    command.Parameters.AddWithValue("@planId", planId);

                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        if (reader.GetValue(0) != DBNull.Value)
                        {
                            var getSubdivisionId = Convert.ToInt32(reader.GetValue(0).ToString());
                            var getSubdivision = await _context.Subdivisions.Where(x => x.SubdivisionId == getSubdivisionId).Select(x => x).FirstOrDefaultAsync();

                            if (getSubdivision != null)
                            {
                                subdivisions.Add(new SubdivisionDto
                                {
                                    SubdivisionId = getSubdivisionId,
                                    SubdivisionName = getSubdivision.SubdivisionName,
                                    MarketingName = getSubdivision.MarketingName,
                                });
                            }
                        }
                    }
                }

                return Ok(new ResponseModel<List<SubdivisionDto>>() { Value = subdivisions, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<AvailablePlanOptionDto>>() { IsSuccess = false, Message = "failed to get subdivisions", Value = null });
            }
        }

        [HttpGet("{planId}/{subdivisionId}")]
        public async Task<IActionResult> GetAvailableOptionByPlanAndSubdivisionAsync(int planId, int subdivisionId)
        {
            try
            {
                var options = new List<AvailablePlanOptionDto>();

                var connString = _configuration.GetConnectionString("ERPConnection");

                using (var connection = new SqlConnection(connString))
                {
                    await connection.OpenAsync();

                    var query = "SELECT PLAN_OPTION_ID, OPTION_CODE, MODIFIED_OPTION_DESC, MASTER_OPTION_ID FROM dbo.AVAILABLE_PLAN_OPTION WHERE SUBDIVISION_ID = @subdivisionId and MASTER_PLAN_ID = @planId AND IsActive = 1";

                    var command = new SqlCommand(query, connection);
                    command.Parameters.AddWithValue("@subdivisionId", subdivisionId);
                    command.Parameters.AddWithValue("@planId", planId);

                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        if (reader.GetValue(0) != DBNull.Value)
                        {
                            options.Add(new AvailablePlanOptionDto
                            {
                                PlanOptionId = Convert.ToInt32(reader.GetValue(0).ToString()),
                                OptionCode = reader.GetValue(1).ToString(),
                                ModifiedOptionDesc = reader.GetValue(2).ToString(),
                                MasterOptionId = Convert.ToInt32(reader.GetValue(3).ToString())
                            });
                        }
                    }
                }

                return Ok(new ResponseModel<List<AvailablePlanOptionDto>>() { Value = options, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<AvailablePlanOptionDto>>() { IsSuccess = false, Message = "failed to get subdivisions", Value = null });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddOptionAttributeGroupItemAsync([FromBody] List<OptionAttributeGroupItemDto> items)
        {
            try
            {
                if (items.Any())
                {
                    foreach (var group in items)
                    {
                        var getAttributeAssignments = await _context.AttrGroupAssignments.Where(x => x.AttributeGroupId == group.AttributeGroupId && x.IsActive == true).ToListAsync();

                        if (getAttributeAssignments.Any())
                        {
                            foreach (var getAttributeAssignment in getAttributeAssignments)
                            {
                                var checkExist = await _context.OptionAttributeGroupItems.Where(x => x.AttrGroupAssignmentId == getAttributeAssignment.AttrGroupAssignmentId && x.MasterOptionId == group.MasterOptionId && x.PlanOptionId == group.PlanOptionId && x.PlanNum == group.PlanNum && x.MasterPlanId == group.MasterPlanId).FirstOrDefaultAsync();

                                if (checkExist == null)
                                {
                                    _context.OptionAttributeGroupItems.Add(new OptionAttributeGroupItem
                                    {
                                        MasterOptionId = group.MasterOptionId,
                                        AttrGroupAssignmentId = getAttributeAssignment.AttrGroupAssignmentId,
                                        PlanOptionId = group.PlanOptionId,
                                        PlanNum = group.PlanNum,
                                        MasterPlanId = group.MasterPlanId,
                                        IsActive = true,
                                        CreatedBy = User.Identity.Name.Split('@')[0],
                                        CreatedDateTime = DateTime.Now
                                    });

                                    await _context.SaveChangesAsync();
                                }
                                else if (checkExist != null && checkExist.IsActive == false)
                                {
                                    // Reactivate
                                    checkExist.IsActive = true;

                                    _context.OptionAttributeGroupItems.Update(checkExist);

                                    await _context.SaveChangesAsync();
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MasterOptionAttributeItemDto>>() { IsSuccess = false, Message = "failed to add groups", Value = null });
            }

            return Ok(new ResponseModel<List<OptionAttributeGroupItemDto>>() { Value = items, IsSuccess = true, Message = "Groups added" });
        }

        /// <summary>
        /// Option with Groups attached to it
        /// </summary>
        /// <param name="optionId"></param>
        /// <returns></returns>
        [HttpGet("{planOptionId}")]
        public async Task<IActionResult> GetPlanWithGroupsAsync(int planOptionId)
        {
            try
            {
                var options = new List<AttrGroupAssignmentDto>();
                var groups = new List<OptionAttributeGroupItemDto>();
                var masterOptionIds = new List<int>();

                var connString = _configuration.GetConnectionString("ERPConnection");

                // Option Attribute Group Item
                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    var query = "SELECT a.[ATTR_GROUP_ASSIGNMENT_ID] FROM [dbo].[OPTION_ATTRIBUTE_GROUP_ITEM] a WHERE a.PLAN_OPTION_ID = @planOptionId AND a.IsActive = 1";

                    var command = new SqlCommand(query, connection);
                    command.Parameters.AddWithValue("@planOptionId", planOptionId);

                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        if (reader.GetValue(0) != DBNull.Value)
                        {
                            options.Add(new AttrGroupAssignmentDto
                            {
                                AttrGroupAssignmentId = Convert.ToInt32(reader.GetValue(0).ToString())
                            });
                        }
                    }
                }

                #region not used. This combining with MasterOptionAttributeItem
                // Combine with Master Option Attribute (when user clicks on the option that has an attribute group attach to the master, we want to show that, too)
                // No more combine, as of 01/10/2025.
                //using (var connection = new SqlConnection(connString))
                //{
                //    connection.Open();

                //    var query = "SELECT a.[MASTER_OPTION_ID] FROM [dbo].[OPTION_ATTRIBUTE_GROUP_ITEM] a WHERE a.PLAN_OPTION_ID = @planOptionId AND a.IsActive = 1";

                //    var command = new SqlCommand(query, connection);
                //    command.Parameters.AddWithValue("@planOptionId", planOptionId);

                //    var reader = command.ExecuteReader();

                //    while (reader.Read())
                //    {
                //        if (reader.GetValue(0) != DBNull.Value)
                //        {
                //            var getId = Convert.ToInt32(reader.GetValue(0).ToString());

                //            var checkExists = masterOptionIds.Exists(x => x == getId);

                //            if (!checkExists)
                //            {
                //                masterOptionIds.Add(Convert.ToInt32(reader.GetValue(0).ToString()));
                //            }
                //        }
                //    }
                //}

                //if (masterOptionIds.Any())
                //{
                //    foreach (var masterOptionId in masterOptionIds)
                //    {
                //        var getAttributeGroupAssignmentIds = await _context.MasterOptionAttributeItems.Where(x => x.MasterOptionId == masterOptionId && x.IsActive == true).Select(x => x.AttrGroupAssignmentId).ToListAsync();

                //        if (getAttributeGroupAssignmentIds.Any())
                //        {
                //            foreach (var attributeGroupAssignmentId in getAttributeGroupAssignmentIds)
                //            {
                //                var getGroupId = await _context.AttrGroupAssignments.Where(x => x.AttrGroupAssignmentId == attributeGroupAssignmentId && x.IsActive == true).Select(x => x.AttributeGroupId).FirstOrDefaultAsync();

                //                if (getGroupId != 0)
                //                {
                //                    var getGroup = await _context.MasterAttributeGroups.Where(x => x.AttributeGroupId == getGroupId).FirstOrDefaultAsync();

                //                    if (getGroup != null)
                //                    {
                //                        var checkExists = groups.Exists(x => x.AttributeGroupId == getGroup.AttributeGroupId);

                //                        if (!checkExists)
                //                        {
                //                            groups.Add(new MasterAttributeGroupDto
                //                            {
                //                                AttributeGroupId = getGroup.AttributeGroupId,
                //                                Description = getGroup.Description,
                //                                IsActive = getGroup.IsActive,
                //                                AttributeGroupAssignmentId = attributeGroupAssignmentId,
                //                                PlanOptionId = planOptionId,
                //                                IsMaster = true
                //                            });
                //                        }
                //                    }
                //                }
                //            }
                //        }
                //    }
                //}
                #endregion

                // Get the group based on OPTION_ATTRIBUTE_GROUP_ITEM
                if (options.Any())
                {
                    foreach (var option in options)
                    {
                        var getGroupId = await _context.AttrGroupAssignments.Where(x => x.AttrGroupAssignmentId == option.AttrGroupAssignmentId && x.IsActive == true).Select(x => x.AttributeGroupId).FirstOrDefaultAsync();

                        if (getGroupId != null)
                        {
                            //var getGroup = await _context.OptionAttributeGroupItems.Where(x => x.AttributeGroupId == getGroupId && x.IsActive == true).FirstOrDefaultAsync();

                            var getGroup = new OptionAttributeGroupItemDto();

                            // Expect just 1
                            using (var connection = new SqlConnection(connString))
                            {
                                connection.Open();

                                var query = "SELECT oagi.OP_ATTR_GROUP_ITEM_ID, oagi.MASTER_OPTION_ID, oagi.ATTR_GROUP_ASSIGNMENT_ID, aga.ATTRIBUTE_GROUP_ID, mag.DESCRIPTION, oagi.PLAN_OPTION_ID FROM [dbo].[OPTION_ATTRIBUTE_GROUP_ITEM] oagi JOIN dbo.ATTR_GROUP_ASSIGNMENT aga ON oagi.ATTR_GROUP_ASSIGNMENT_ID = aga.ATTR_GROUP_ASSIGNMENT_ID JOIN dbo.MASTER_ATTRIBUTE_GROUP mag ON aga.ATTRIBUTE_GROUP_ID = mag.ATTRIBUTE_GROUP_ID WHERE oagi.PLAN_OPTION_ID = @planOptionId AND oagi.ATTR_GROUP_ASSIGNMENT_ID = @attrGroupAssignmentId AND aga.ATTRIBUTE_GROUP_ID = @attributeGroupId AND oagi.IsActive = 1 AND aga.IsActive = 1 AND mag.IsActive = 1";

                                var command = new SqlCommand(query, connection);
                                command.Parameters.Add("@planOptionId", SqlDbType.Int).Value = planOptionId;
                                command.Parameters.Add("@attrGroupAssignmentId", SqlDbType.Int).Value = option.AttrGroupAssignmentId;
                                command.Parameters.Add("@attributeGroupId", SqlDbType.Int).Value = getGroupId;

                                var reader = command.ExecuteReader();

                                while (reader.Read())
                                {
                                    getGroup.OpAttrGroupItemId = Convert.ToInt32(reader.GetValue(0).ToString());
                                    getGroup.MasterOptionId = Convert.ToInt32(reader.GetValue(1).ToString());
                                    getGroup.AttrGroupAssignmentId = Convert.ToInt32(reader.GetValue(2).ToString());
                                    getGroup.AttributeGroupId = Convert.ToInt32(reader.GetValue(3).ToString());
                                    getGroup.AttrGroupAssignment = new AttrGroupAssignmentDto
                                    {
                                        AttributeGroup = new MasterAttributeGroupDto
                                        {
                                            Description = reader.GetValue(4).ToString()
                                        }
                                    };
                                    getGroup.PlanOptionId = Convert.ToInt32(reader.GetValue(5).ToString());
                                }
                            }

                            if (getGroup != null)
                            {
                                // Becareful that if Attribute Group is assigned to master and option, the master will always win
                                // No more on the above comment as of 01/10/2025
                                var checkExists = groups.Exists(x => x.AttributeGroupId == getGroup.AttributeGroupId);

                                if (!checkExists)
                                {
                                    if (getGroup.AttrGroupAssignment != null)
                                    {
                                        if (getGroup.AttrGroupAssignment.AttributeGroup != null)
                                        {
                                            groups.Add(new OptionAttributeGroupItemDto
                                            {
                                                OpAttrGroupItemId = getGroup.OpAttrGroupItemId,
                                                MasterOptionId = getGroup.MasterOptionId,
                                                AttrGroupAssignmentId = getGroup.AttrGroupAssignmentId,
                                                AttributeGroupId = getGroup.AttributeGroupId,
                                                AttrGroupAssignment = new AttrGroupAssignmentDto
                                                {
                                                    AttributeGroup = new MasterAttributeGroupDto
                                                    {
                                                        Description = getGroup.AttrGroupAssignment.AttributeGroup.Description
                                                    }
                                                },
                                                PlanOptionId = getGroup.PlanOptionId
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                return Ok(new ResponseModel<List<OptionAttributeGroupItemDto>>() { Value = groups, IsSuccess = true });
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<List<MasterAttributeGroupDto>>() { IsSuccess = false, Message = "failed to get master options by group", Value = null });
            }
        }

        [HttpPut]
        public async Task<IActionResult> DeleteOptionAttributeGroupItemAsync([FromBody] OptionAttributeGroupItemDto model)
        {
            try
            {
                // Find ATTR_ASSIGNMENT_GROUP_ID
                var findAttrAssignmentGroupIds = await _context.AttrGroupAssignments.Where(x => x.AttributeGroupId == model.AttributeGroupId && x.IsActive == true).ToListAsync();

                var attrGroupAssignmentIds = new List<int>();

                if (findAttrAssignmentGroupIds.Any())
                {
                    foreach (var findAttrAssignmentGroupId in findAttrAssignmentGroupIds)
                    {
                        attrGroupAssignmentIds.Add(findAttrAssignmentGroupId.AttrGroupAssignmentId);
                    }
                }

                // Loop anything available
                var getAllOptionAttributeGroupItems = await _context.OptionAttributeGroupItems.Where(x => x.PlanOptionId == model.PlanOptionId && x.IsActive == true).ToListAsync();

                if (getAllOptionAttributeGroupItems.Any())
                {
                    foreach (var getAllOptionAttributeGroupItem in getAllOptionAttributeGroupItems)
                    {
                        // Match with attrGroupAssignmentIds
                        if (attrGroupAssignmentIds.Contains(getAllOptionAttributeGroupItem.AttrGroupAssignmentId))
                        {
                            var findOptionAttributeGroupItem = await _context.OptionAttributeGroupItems.Where(x => x.AttrGroupAssignmentId == getAllOptionAttributeGroupItem.AttrGroupAssignmentId && x.PlanOptionId == model.PlanOptionId && x.IsActive == true).FirstOrDefaultAsync();

                            if (findOptionAttributeGroupItem != null)
                            {
                                findOptionAttributeGroupItem.IsActive = false;
                                findOptionAttributeGroupItem.UpdatedDateTime = DateTime.Now;
                                findOptionAttributeGroupItem.Updatedby = User.Identity.Name.Split('@')[0];

                                _context.OptionAttributeGroupItems.Update(findOptionAttributeGroupItem);

                                await _context.SaveChangesAsync();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var username = User.Identity?.Name?.Split('@')[0];
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", username).Exception(ex).Log();
#endif
                return StatusCode(500, new ResponseModel<OptionAttributeGroupItemDto>() { IsSuccess = false, Message = "failed to deactivate group", Value = null });
            }

            return Ok(new ResponseModel<OptionAttributeGroupItemDto>() { Value = model, IsSuccess = true, Message = "Group is deactivated" });
        }
    }
}
