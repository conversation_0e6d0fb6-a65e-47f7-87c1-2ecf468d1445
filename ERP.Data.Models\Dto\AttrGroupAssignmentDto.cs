﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class AttrGroupAssignmentDto : IMapFrom<AttrGroupAssignment>
    {
        public int AttrGroupAssignmentId { get; set; }

        public int? AttributeGroupId { get; set; }

        public int? AttributeItemId { get; set; }

        public DateTime CreatedDateTime { get; set; }

        public DateTime? UpdatedDateTime { get; set; }

        public string? UpdatedBy { get; set; }

        public string? CreatedBy { get; set; }

        public bool? IsActive { get; set; }
        public MasterAttributeGroupDto? AttributeGroup { get; set; }

        public MasterAttributeItemDto? AttributeItem { get; set; }
    }
}
