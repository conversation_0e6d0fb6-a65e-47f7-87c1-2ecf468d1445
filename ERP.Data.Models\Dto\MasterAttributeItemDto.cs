﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class MasterAttributeItemDto : IMapFrom<MasterAttributeItem>
    {
        public int AttributeItemId { get; set; }

        public string? Description { get; set; } = null!;

        public bool? IsActive { get; set; }

        public int? Seq { get; set; }

        public DateTime CreatedDateTime { get; set; }

        public string? CreatedBy { get; set; }

        public DateTime? UpdatedDateTime { get; set; }

        public string? UpdatedBy { get; set; }

        public int AttributeGroupAssignmentId { get; set; }

        public int AttributeGroupId { get; set; }

        public string? ErrorMessage { get; set; }

        public int MasterOptionId { get; set; }

        public int OptionAttributeGroupItemId { get; set; }

        public string? PlanNum { get; set; }

        public string? Subdivision { get; set; }

        public int? PlanOptionId { get; set; }

        public int? MasterPlanId { get; set; }

        public int? TotalItems { get; set; }
    }
}
