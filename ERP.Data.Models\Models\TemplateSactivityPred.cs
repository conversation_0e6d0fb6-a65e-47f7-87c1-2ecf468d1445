﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class TemplateSactivityPred
{
    public int TemplateAid { get; set; }

    public int PredSactivityId { get; set; }

    public DateTime Createddatetime { get; set; }

    public DateTime? Updateddatetime { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public string? UpdatedBy { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual Sactivity PredSactivity { get; set; } = null!;

    public virtual TemplateSactivity TemplateA { get; set; } = null!;
}
