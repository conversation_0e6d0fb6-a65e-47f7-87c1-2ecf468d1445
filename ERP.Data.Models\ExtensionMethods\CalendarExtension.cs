﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.ExtensionMethods
{
    public static class CalendarExtension
    {
        public static DateTime AddWorkingDays(DateTime startDate, int days, List<DateTime>? holidays = null)
        {
            var date = startDate;
            if(days >0)
            {
                for(int i = 0; i < days; i++)
                {
                    date = date.AddDays(1);
                    if(holidays != null)
                    {
                        while (date.DayOfWeek == DayOfWeek.Sunday || date.DayOfWeek == DayOfWeek.Saturday || (holidays.Select(x => x.Date).Contains(date.Date)))
                        {
                            date = date.AddDays(1);
                        }
                    }
                    else
                    {
                        while (date.DayOfWeek == DayOfWeek.Sunday || date.DayOfWeek == DayOfWeek.Saturday)
                        {
                            date = date.AddDays(1);
                        }
                    }                              
                }
            }
            else if (days == 0)
            {
                if (holidays != null)
                {
                    while (date.DayOfWeek == DayOfWeek.Sunday || date.DayOfWeek == DayOfWeek.Saturday || (holidays.Select(x => x.Date).Contains(date.Date)))
                    {
                        date = date.AddDays(1);
                    }
                }
                else
                {
                    while (date.DayOfWeek == DayOfWeek.Sunday || date.DayOfWeek == DayOfWeek.Saturday)
                    {
                        date = date.AddDays(1);
                    }
                }
            }
            else if (days < 0)
            {
                for (int i = 0; i > days; i--)
                {
                    date = date.AddDays(-1);
                    if (holidays != null)
                    {
                        while (date.DayOfWeek == DayOfWeek.Sunday || date.DayOfWeek == DayOfWeek.Saturday || (holidays.Select(x => x.Date).Contains(date.Date)))
                        {
                            date = date.AddDays(-1);
                        }
                    }
                    else
                    {
                        while (date.DayOfWeek == DayOfWeek.Sunday || date.DayOfWeek == DayOfWeek.Saturday)
                        {
                            date = date.AddDays(-1);
                        }
                    }
                }
            }
            return date;

        }
        public static DateTime AddNonSundaysDays(DateTime startDate, int days)
        {
            var date = startDate;
            if (days > 0)
            {
                for (int i = 0; i < days; i++)
                {
                    date = date.AddDays(1);
                    while (date.DayOfWeek == DayOfWeek.Sunday)
                    {
                        date = date.AddDays(1);
                    }
                }
            }
            else if (days < 0)
            {
                for (int i = 0; i > days; i--)
                {
                    date = date.AddDays(-1);
                    while (date.DayOfWeek == DayOfWeek.Sunday)
                    {
                        date = date.AddDays(-1);
                    }
                }
            }
            return date;

        }
        public static int WorkingDaysDuration(DateTime startDate, DateTime endDate, List<DateTime>? holidays = null)
        {
            int duration = (endDate - startDate).Days;
            var start = startDate <= endDate ? startDate : endDate;
            var end = startDate <= endDate ? endDate : startDate;
            int count = 0;
            for (DateTime date = start.Date; date <= end.Date; date = date.AddDays(1))
            {
                if(holidays != null)
                {
                    if (date.DayOfWeek != DayOfWeek.Sunday && date.DayOfWeek != DayOfWeek.Saturday && !(holidays.Select(x => x.Date).Contains(date.Date)))                                    
                    {
                        count++;
                    }
                }
                else
                {
                    if (date.DayOfWeek != DayOfWeek.Sunday && date.DayOfWeek != DayOfWeek.Saturday)
                    {
                        count++;
                    }
                }
                
            };
            return startDate <= endDate ? count : -count;
        }
        public static int PlusMinusDaysDuration(DateTime startDate, DateTime endDate, List<DateTime>? holidays = null)
        {
            int duration = (endDate - startDate).Days;
            var start = startDate <= endDate ? startDate : endDate;
            var end = startDate <= endDate ? endDate : startDate;
            int count = 0;
            for (DateTime date = start.Date; date < end.Date; date = date.AddDays(1))
            {
                if (holidays != null)
                {
                    if (date.DayOfWeek != DayOfWeek.Sunday && date.DayOfWeek != DayOfWeek.Saturday && !(holidays.Select(x => x.Date).Contains(date.Date)))
                    {
                        count++;
                    }
                }
                else
                {
                    if (date.DayOfWeek != DayOfWeek.Sunday && date.DayOfWeek != DayOfWeek.Saturday)
                    {
                        count++;
                    }
                }

            };

            return startDate <= endDate ? count : -count;
        }

        public static List<DateTime> GetSundays(DateTime startDate, DateTime endDate)
        {
            List<DateTime> weekendList = new List<DateTime>();
            for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
            {
                if (date.DayOfWeek == DayOfWeek.Sunday)
                    weekendList.Add(date);
            }
            return weekendList;
        }
    }
}
