﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Web.Data
{
    public class AttributeItemPickService
    {
        private bool? _isChanged;
        private int _masterOptionId;
        private string? _errorMessage;
        private bool? _showNotification;

        public bool? IsChanged
        {
            get
            {
                return _isChanged;
            }
            set
            {
                _isChanged = value;
                NotifyChanged();
            }
        }

        public int MasterOptionId
        {
            get
            {
                return _masterOptionId;
            }
            set
            {
                _masterOptionId = value;
                NotifyChanged();
            }
        }

        public string? ErrorMessage
        {
            get
            {
                return _errorMessage;
            }
            set
            {
                _errorMessage = value;
                NotifyChanged();
            }
        }

        public bool? ShowNotification
        {
            get
            {
                return _showNotification;
            }
            set
            {
                _showNotification = value;
                NotifyChanged();
            }
        }

        public Func<Task>? OnDataChanged { get; set; }

        private async Task NotifyChanged() => await OnDataChanged?.Invoke();
    }
}
