﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Podetailoption
{
    public int PodetailoptionsId { get; set; }

    public int PoheaderId { get; set; }

    public string? OptionNumber { get; set; }

    public string? OptionDesc { get; set; }

    public string? OptionLongDesc { get; set; }

    public string? OptionExtendedDesc { get; set; }

    public string? OptionNotes { get; set; }

    public string? OptionSelections { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<Podetail> Podetails { get; set; } = new List<Podetail>();

    public virtual Poheader Poheader { get; set; } = null!;
}
