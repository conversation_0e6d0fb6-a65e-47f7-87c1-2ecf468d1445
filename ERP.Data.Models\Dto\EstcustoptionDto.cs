﻿using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public partial class EstcustoptionDto : IMapFrom<Estcustoption> 
{
    public int EstcustoptionId { get; set; }

    public int EstheaderId { get; set; }

    public int Builtoptionid { get; set; }

    public int? LotId { get; set; }

    public string? JobNumber { get; set; }

    public string? Optioncode { get; set; }

    public string? Optiontype { get; set; }

    public string? Optiongroupname { get; set; }

    public double? Qty { get; set; }

    public double? Qtydelta { get; set; }

    public double? Price { get; set; }

    public double? Pricedelta { get; set; }

    public string Conflict { get; set; } = null!;

    public string? Optiondesc { get; set; }

    public string? Customerdesc { get; set; }

    public string Elevation { get; set; } = null!;

    public DateTime? Printdate { get; set; }

    public DateTime? Changedate { get; set; }

    public string Removed { get; set; } = null!;

    public string? Externalcode { get; set; }

    public string? Crgroup { get; set; }

    public int? Builderapproved { get; set; }
    public bool IsBuilderapproved { get; set; }

    public int? Customerapproved { get; set; }
    public bool IsCustomerapproved { get; set; }
    public double? Unitcost { get; set; }

    public int? Optionnum { get; set; }

    public string Quotecomplete { get; set; } = null!;

    public string? Optionlongdesc { get; set; }

    public string? Restrictions { get; set; }

    public int? Addendumnum { get; set; }

    public string? Description { get; set; }

    public int? Lastuser { get; set; }

    public DateTime? Lastchanged { get; set; }

    public string EstimateRequired { get; set; } = null!;
    public bool IsEstimateRequired { get; set; }
    public double? EstimateCost { get; set; }

    public string EstimateCompleted { get; set; } = null!;
    public bool IsEstimateCompleted { get; set; }

    public string? EstimatorNotes { get; set; }

    public string? EstimatorEmail { get; set; }

    public int? SubdivisionId { get; set; }

    public string? DocsFolder { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual EstheaderDto Estheader { get; set; } = null!;
    public virtual SubdivisionDto? Subdivision { get; set; }
}
