﻿@inject PlanService PlanService
@inject OptionService OptionService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               Width="500px"
               Height="300px"
               CloseOnOverlayClick="true">
    <WindowTitle>
        Add Group(s) to Plan
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@GroupToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <p>
                <label>Select Multiple Groups to Add To Plan:</label>
                <TelerikMultiSelect Data="@AllMasterAttributeGroup"
                                    TextField="Description"
                                    ValueField="AttributeGroupId"
                                    Placeholder="Select Master Attribute Group"
                                    Filterable="true"
                                    FilterOperator="StringFilterOperator.Contains"
                                    @bind-Value="@SelectedMasterAttributeGroups"
                                    Width="100%">
                </TelerikMultiSelect>
            </p>
            <button type="submit" class="btn btn-primary">Add</button>
            <button type="button" @onclick="CancelAddItem" class="btn btn-secondary">Cancel</button>
            <div style=@submittingStyle>Adding groups. Please wait...</div>
        </EditForm>
    </WindowContent>
</TelerikWindow>

@code {
    public bool IsModalVisible { get; set; }
    public MasterAttributeGroupDto GroupToAdd { get; set; } = new MasterAttributeGroupDto();
    public List<MasterAttributeGroupDto>? AllMasterAttributeGroup { get; set; }
    public List<int>? SelectedMasterAttributeGroups { get; set; }
    private List<OptionAttributeGroupItemDto> GroupToAssign { get; set; }

    private string submittingStyle = "display:none";

    [Parameter]
    public EventCallback<ResponseModel> HandleAddSubmit { get; set; }
    [Parameter]
    public MasterPlanDto Plan { get; set; }
    [Parameter]
    public AvailablePlanOptionDto PlanOption { get; set; }
    [Parameter]
    public string PlanNum { get; set; }

    public async Task Show()
    {
        SelectedMasterAttributeGroups = null;
        IsModalVisible = true;
        await LoadMasterAttributeGroupsAsync();
        StateHasChanged();
    }

    private async Task LoadMasterAttributeGroupsAsync()
    {
        var getMasterAttributeGroups = await OptionService.GetMasterAttributeGroupsAsync();
        AllMasterAttributeGroup = getMasterAttributeGroups.Value;
    }

    private async void HandleValidAddSubmit()
    {
        submittingStyle = "";

        SelectedMasterAttributeGroups.Add(PlanOption.MasterOptionId);

        if (SelectedMasterAttributeGroups.Any())
        {
            GroupToAssign = new List<OptionAttributeGroupItemDto>();

            foreach (var item in SelectedMasterAttributeGroups)
            {
                GroupToAssign.Add(new OptionAttributeGroupItemDto
                    {
                        AttributeGroupId = item,
                        MasterOptionId = PlanOption.MasterOptionId,
                        PlanOptionId = PlanOption.PlanOptionId,
                        MasterPlanId = Plan.MasterPlanId,
                        PlanNum = PlanNum
                    });
            }

            var responseItem = await PlanService.AddOptionAttributeGroupItemAsync(GroupToAssign);
            var responseModel = new ResponseModel() { IsSuccess = responseItem.IsSuccess, Message = responseItem.Message };
            await HandleAddSubmit.InvokeAsync(responseModel);
        }

        submittingStyle = "display:none";
    }

    async void CancelAddItem()
    {
        IsModalVisible = false;
    }

    public async Task Hide()
    {
        IsModalVisible = false;
    }
}
