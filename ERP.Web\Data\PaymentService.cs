﻿using Azure.Core;
using DocumentFormat.OpenXml.Spreadsheet;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Pages;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using NLog;
using Microsoft.Identity.Abstractions;
using Microsoft.AspNetCore.Components.Authorization;

namespace ERP.Web.Data
{
    public class PaymentService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public PaymentService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<ResponseModel<List<CheckPaymentDto>>> GetPaymentsAsync()
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/payment/checkpayments");
                var responseString = await response.Content.ReadAsStringAsync();
                var payments = JsonConvert.DeserializeObject<ResponseModel<List<CheckPaymentDto>>>(responseString);
                return payments;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<CheckPaymentDto>>() { IsSuccess = false, Value = new List<CheckPaymentDto>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<CheckPaymentDto>>> GetPaymentsAsync(int subNumber)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/payment/checkpaymentsbysupplier/{subNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var payments = JsonConvert.DeserializeObject<ResponseModel<List<CheckPaymentDto>>>(responseString);
                return payments;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<CheckPaymentDto>>() { IsSuccess = false, Value = new List<CheckPaymentDto>(), Message = "Something went wrong" };
        }
        public async Task<ResponseModel<List<CheckPaymentDto>>> GetPaymentsAsync(string jobNumber)
        {
            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync("DownstreamApi", options => options.RelativePath = $"api/payment/checkpaymentsbyjob/{jobNumber}");
                var responseString = await response.Content.ReadAsStringAsync();
                var payments = JsonConvert.DeserializeObject<ResponseModel<List<CheckPaymentDto>>>(responseString);
                return payments;
            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }
            return new ResponseModel<List<CheckPaymentDto>>() { IsSuccess = false, Value = new List<CheckPaymentDto>(), Message = "Something went wrong" };
        }
    }
}
