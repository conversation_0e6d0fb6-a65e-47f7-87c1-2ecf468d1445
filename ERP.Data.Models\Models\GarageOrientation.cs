﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class GarageOrientation
{
    public int GarageOrientationId { get; set; }

    public short ListOrder { get; set; }

    public string Name { get; set; } = null!;

    public bool? IsActive { get; set; }

    public string? Note { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public byte[] RecordTimeStampe { get; set; } = null!;

    public virtual ICollection<Job> Jobs { get; set; } = new List<Job>();
}
