﻿
<TelerikWindow Modal="true"
@bind-Visible="@IsModalVisible"
               Top="150px"
               Width="200px"
               Height="200px">
    <WindowTitle>
        @MessageToUser
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@MessageToUser" OnValidSubmit="SelectedDateSubmit">
            <p>
                <TelerikDatePicker @ref="@DatePickerRef"
                @bind-Value="SelectedDate"
                                   AdaptiveMode="AdaptiveMode.Auto">
                </TelerikDatePicker>
            </p>
            <br />
            <button type="submit" class="btn btn-primary">Update</button>
            <button type="button" @onclick="Cancel" class="btn btn-secondary">Cancel</button>
        </EditForm>
    </WindowContent>
</TelerikWindow>


@code {

    public bool IsModalVisible { get; set; }
    [Parameter]
    public string? MessageToUser { get; set; }
    [Parameter]
    public EventCallback<DateTime> HandleDateSelected { get; set; }
    public TelerikDatePicker<DateTime> DatePickerRef { get; set; }
    public DateTime SelectedDate { get; set; } = DateTime.Now;

    public async Task Show()
    {
        IsModalVisible = true;
        StateHasChanged();
        await Task.Delay(2);
        DatePickerRef?.Open();
    }

    public async Task Hide()
    {
        IsModalVisible = false;
    }

    private async void Cancel()
    {
        IsModalVisible = false;
    }

    private async Task SelectedDateSubmit()
    {
        await HandleDateSelected.InvokeAsync(SelectedDate);
    }
}
