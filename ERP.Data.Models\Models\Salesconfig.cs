﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Salesconfig
{
    public int SalesconfigId { get; set; }

    public int? SsConfigurationid { get; set; }

    public string? SsAction { get; set; }

    public DateTime? Saledate { get; set; }

    public DateTime? Ratificationdate { get; set; }

    public DateTime? Estimatedsettlementdate { get; set; }

    public DateTime? Closingdate { get; set; }

    public DateTime? Canceldate { get; set; }

    public string? Status { get; set; }

    public string? SalesContact { get; set; }

    public string JobNumber { get; set; } = null!;

    public int? PhasePlanId { get; set; }

    public double? Baseprice { get; set; }

    public string? LotSwing { get; set; }

    public string? Ownername { get; set; }

    public string? Owneraddress1 { get; set; }

    public string? Owneraddress2 { get; set; }

    public string? Ownersuburb { get; set; }

    public string? Ownerstate { get; set; }

    public string? Ownerpostcode { get; set; }

    public string? Ownerphone1 { get; set; }

    public string? Ownerphone2 { get; set; }

    public string? Ownerfax { get; set; }

    public string? Ownermobile { get; set; }

    public string? Owneremail { get; set; }

    public string? UserContact1 { get; set; }

    public string? UserContact2 { get; set; }

    public string? SentToPurchasing { get; set; }

    public int? PeeHeaderId { get; set; }

    public double? LotPremium { get; set; }

    public string? OwnerNamePrefix { get; set; }

    public string? OwnerFirstName { get; set; }

    public string? OwnerMiddleName { get; set; }

    public string? OwnerLastName { get; set; }

    public string? OwnerNameSuffix { get; set; }

    public string? UserContact1NamePrefix { get; set; }

    public string? UserContact1FirstName { get; set; }

    public string? UserContact1MiddleName { get; set; }

    public string? UserContact1LastName { get; set; }

    public string? UserContact1NameSuffix { get; set; }

    public string? UserContact2NamePrefix { get; set; }

    public string? UserContact2FirstName { get; set; }

    public string? UserContact2MiddleName { get; set; }

    public string? UserContact2LastName { get; set; }

    public string? UserContact2NameSuffix { get; set; }

    public string? IsDeleted { get; set; }

    public string? ConfigSource { get; set; }

    public double? SalesIncentives { get; set; }

    public string? IsApproved { get; set; }

    public string? Prevstatus { get; set; }

    public double? PriceIncentive { get; set; }

    public double? OptionIncentive { get; set; }

    public double? UpfrontIncentive { get; set; }

    public double? LotPremiumIncentive { get; set; }

    public int? SsClientId { get; set; }

    public int? SsSalesagentId { get; set; }

    public string? SalesagentName { get; set; }

    public string? SalesagentEmail { get; set; }

    public string? SalesagentPhone { get; set; }

    public int? SsLotid { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTim { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual ICollection<Estheader> Estheaders { get; set; } = new List<Estheader>();

    public virtual Job JobNumberNavigation { get; set; } = null!;

    public virtual PhasePlan? PhasePlan { get; set; }

    public virtual ICollection<Salesconfigco> Salesconfigcos { get; set; } = new List<Salesconfigco>();

    public virtual ICollection<Salesconfigoption> Salesconfigoptions { get; set; } = new List<Salesconfigoption>();
}
