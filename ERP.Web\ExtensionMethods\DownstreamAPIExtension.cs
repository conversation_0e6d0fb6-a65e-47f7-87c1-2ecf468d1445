﻿using Microsoft.Identity.Web;

namespace ERP.Web.ExtensionMethods
{
    public static class DownstreamAPIExtension
    {
        public static MicrosoftIdentityAppCallsWebApiAuthenticationBuilder UseHttpClientTimeout(this MicrosoftIdentityAppCallsWebApiAuthenticationBuilder builder, string serviceName, TimeSpan timeout)
        {
            builder.Services.AddHttpClient(serviceName, client =>
            {
                client.Timeout = timeout;
            });
            return builder;
        }
    }
}
