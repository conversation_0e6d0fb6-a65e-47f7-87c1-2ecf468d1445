﻿@using ERP.Data.Models
@inject SalesPriceService SalesPriceService

<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <h4 class="page-title">Reprice Worksheet</h4>
    </WindowTitle>
    <WindowContent>
        <p>Reprice Worksheet @RepriceWorksheet1?.WorksheetName</p>
        <EditForm Model="@WorksheetToReprice" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />            
            <label class="form-label">Reprice Date</label>
            <TelerikDatePicker @bind-Value="@WorksheetToReprice.RepriceDate"></TelerikDatePicker>
            <br />
            <div class="customSeparator">
                <button type="submit" class="btn btn-primary">Reprice</button>
                <button type="button" @onclick="CancelAddWorksheet" class="btn btn-secondary">Cancel</button>
                <div style=@ShowLoading>Submitting. Please wait...</div>
                <div style=@ShowError>Something went wrong.</div>
            </div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public RepriceWorksheetModel WorksheetToReprice { get; set; } = new RepriceWorksheetModel();
    public string ShowLoading{ get; set; } = "display:none";
    public string ShowError { get; set; } = "display:none";

    [Parameter]
    public WorksheetDto? RepriceWorksheet1 { get; set; }

    [Parameter]
    public EventCallback<ResponseModel<RepriceWorksheetModel>> HandleAddSubmit { get; set; }

    public void Show()
    {
        IsModalVisible = true;
        WorksheetToReprice.WorksheetId = RepriceWorksheet1.WorksheetId;
    }      

    private async void HandleValidAddSubmit()
    {
        ShowLoading = "";
        WorksheetToReprice.RepriceDate = WorksheetToReprice.RepriceDate ?? DateTime.Now; //Fill in current date as default
        var response = await SalesPriceService.RepriceWorksheetAsync(WorksheetToReprice);
        ShowLoading = "display:none";       
        await HandleAddSubmit.InvokeAsync(response);
    }
    async void CancelAddWorksheet()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;        
    }
}
