﻿@page "/approvepos"
@inject BudgetService BudgetService
@inject PoService PoService
@inject TradeService TradeService
@inject SubdivisionService SubdivisionService
@inject SubdivisionJobPickService SubdivisionJobPickService
@inject AuthenticationStateProvider AuthenticationStateProvider
@implements IDisposable
@attribute [Authorize(Roles = "Admin, Purchasing, ReadOnly, Accounting")]
@using ERP.Data.Models
@using System.Collections.ObjectModel
@using Telerik.DataSource.Extensions

<style>
    .partial-payment-info {
        background-color: transparent;
        border: none;
        padding: 0;
        cursor: pointer;
    }
</style>

<PageTitle>Purchasing | Approve POs</PageTitle>
<TelerikNotification @ref="@NotificationReference" Class="MyTelerikNotification"></TelerikNotification>
<TelerikTooltip TargetSelector=".tooltip-target" />
<ERP.Web.Components.SubdivisionJobPickerMenuBar></ERP.Web.Components.SubdivisionJobPickerMenuBar>

<div class="container-fluid flex">
    <div class="col-lg-12">
        <div class="card" style="background-color: #2e5771">
            <div class="card-body" style="padding:0.5rem">
                <h7 class="page-title" style="font-weight:bold; font-size:0.9rem; color:#fff">Approve Payment of Purchase Orders</h7>
            </div>
        </div>
    </div>

    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/issuepo">Issue Purchase Orders</a></li>
        <li class="breadcrumb-item active">Approve Purchase Orders</li>
    </ol>
    @if (displayLoadingSpinner)
    {
        <p><em>Loading...</em></p>
        <TelerikLoader></TelerikLoader>
    }
    else
    {
        <div class="row d-flex">
            <div class="col-lg-12">
                <TelerikGrid Data=@PoData
                Size="@ThemeConstants.Grid.Size.Small"
                ScrollMode="@GridScrollMode.Virtual"
                FilterMode="Telerik.Blazor.GridFilterMode.FilterMenu"
                PageSize="40"
                Height="1000px"
                RowHeight="40"
                Sortable="true"
                Resizable="true"
                Groupable="false"
                ConfirmDelete="true"
                @ref="@PoGridRef">
                    <GridToolBarTemplate>
                        <GridSearchBox DebounceDelay="200"></GridSearchBox>
                    </GridToolBarTemplate>
                    <GridColumns>
                        <GridColumn Field="Releasecode" Title="Release Code" Editable="false" Groupable="false" Width="40px"/>
                        <GridColumn Field="Ponumber" Title="PO Number" Editable="true" Groupable="false" Width="100px" />
                        <GridColumn Field="VpoNumber" Title="VPO Number" Editable="true" Groupable="false" Width="80px" />
                        <GridColumn Field="PendInvNumber" Title="Invoice Number" Editable="true" Groupable="false" Width="100px" />
                        <GridColumn Field="Podescription" Title="Description" Editable="true" Groupable="false" Width="150px" />
                        <GridColumn Field="SubNumberNavigation.SubName" Title="Supplier Name" Editable="false" Groupable="false" Width="150px" />
                        <GridColumn Field="PostatusNavigation.Postatus1" Title="Postatus" Editable="true" Groupable="false" Width="100px" />
                        <GridColumn Field="Approvedby" Title="Approved By" Editable="true" Groupable="false" Width="100px" />
                        <GridColumn Field="Podateapproved" Title="Date Approved" DisplayFormat="{0:MM/dd/yyy}" Editable="true" Groupable="false" Width="100px" />
                        <GridColumn Field="TaskCompleteBy" Title="Completed By" Editable="true" Groupable="false" Width="100px" />
                        <GridColumn Field="TaskCompleteDate" Title="Date Complete" DisplayFormat="{0:MM/dd/yyy}" Editable="true" Groupable="false" Width="100px" />
                        <GridColumn Field="Podateissued" Title="Date Issued" DisplayFormat="{0:MM/dd/yyy}" Editable="true" Groupable="false" Width="100px" />
                        <GridColumn Field="ApproveTillDate" Title="Approved To Date" DisplayFormat="{0:C2}" Editable="false" Groupable="false" Width="100px">
                            <Template>
                                @{
                                    var item = (PoheaderDto)context;
                                    if (item.ApproveTillDate > 0)
                                    {
                                        <label>@($"{item.ApproveTillDate:C2}")  &nbsp;</label>
                                        <TelerikPopover @ref="@PartialPaymentInfoPopoverRef"
                                        AnimationType="@AnimationType.Fade"
                                        AnchorSelector=".partial-payment-info"
                                        Collision="@PopoverCollision.Flip"
                                        Offset="20"
                                        Width="500px"
                                        Position="@PopoverPosition.Left"
                                        ShowOn="@PopoverShowOn.Click">
                                            <PopoverContent>
                                                <TelerikGrid Data=@PoApprovalData
                                                Size="@ThemeConstants.Grid.Size.Small"
                                                Sortable="true"
                                                Resizable="true"
                                                RowHeight="40">
                                                    <GridColumns>
                                                        <GridColumn Field="Invnumber" Title="Invoice Number" />
                                                        <GridColumn Field="Invdescription" Title="Description" />
                                                        <GridColumn Field="Invdate" Title="Date Approved" DisplayFormat="{0:MM/dd/yyy}" />
                                                        <GridColumn Field="Invnetamount" Title="Amount" DisplayFormat="{0:C2}" />
                                                        <GridColumn Field="Approvedby" Title="Approved By" />
                                                    </GridColumns>
                                                </TelerikGrid>
                                            </PopoverContent>
                                            <PopoverActions>
                                                <TelerikButton Icon="@FontIcon.XCircle" OnClick="@ClosePartialPaymentInfoPopover">Close</TelerikButton>
                                            </PopoverActions>
                                        </TelerikPopover>
                                        <button class="partial-payment-info" onclick="@( () => BeforePartialPaymentInfoPopoverOpen(item) )"><TelerikFontIcon Icon="@FontIcon.InfoCircle"></TelerikFontIcon></button>
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Field="RemainingTotal" Title="Remaining Amount" DisplayFormat="{0:C2}" Editable="false" Groupable="false" Width="100px" />
                        <GridColumn Field="Pototal" Title="Amount" DisplayFormat="{0:C2}" Editable="true" Groupable="false" Width="100px" />
                        <GridColumn Field="IsSchedulePayPoint" Title="Sch Link" Editable="false" Groupable="false" Width="50px">
                            <Template>
                                @{
                                    var item = (PoheaderDto)context;
                                    if (item.IsSchedulePayPoint == true)
                                    {
                                        <span class="tooltip-target" title="Schedule Linked"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Success" Icon="@FontIcon.CheckCircle" /></span>
                                    }
                                    else
                                    {
                                        <span class="tooltip-target" title="No Schedule Activity Link"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Error" Icon="@FontIcon.XCircle" /></span>
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridCommandColumn Width="150px">
                            @{
                                var approvalPo = context as PoheaderDto;
                                //Per Kevin 11/14 allow approve for payment even if insurance expired. New PO should not be issued if no insurance
                                //if (approvalPo.RemainingTotal != 0 && approvalPo.SupplierBlocked != true && approvalPo.SupplierNoInsurance != true && AllowEdit)
                                // if(approvalPo.RemainingTotal != 0  && AllowEdit)
                                // {
                                //     // <GridCommandButton Class="tooltip-target" OnClick="@ApprovePartPO" Title="Part Payment" Command="PartPayment">Part Payment</GridCommandButton>
                                //     <GridCommandButton Class="tooltip-target" OnClick="@ApprovePO" Title="Complete" Command="CompletePO">Complete</GridCommandButton>
                                // }
                                if (approvalPo.Postatus != 4 && AllowEdit)
                                {
                                    // <GridCommandButton Class="tooltip-target" OnClick="@ApprovePartPO" Title="Part Payment" Command="PartPayment">Part Payment</GridCommandButton>
                                    <GridCommandButton Class="tooltip-target" OnClick="@ApprovePO" Title="Complete" Command="CompletePO">Complete</GridCommandButton>
                                }
                                if (approvalPo.SupplierBlocked == true)
                                {
                                    <span class="tooltip-target" title="Supplier Blocked"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Error" Icon="@FontIcon.WarningCircle" /></span>
                                }
                                if (approvalPo.SupplierNoInsurance == true)
                                {
                                    <span class="tooltip-target" title="Supplier Insurance Expired"><TelerikFontIcon ThemeColor="@ThemeConstants.FontIcon.ThemeColor.Error" Icon="@FontIcon.WarningCircle" /></span>
                                }
                                if (approvalPo.Postatus == 4 && AllowEdit)
                                {
                                    <GridCommandButton Class="tooltip-target" OnClick="@UnApprovePO" Title="Mark Incomplete" Command="CompletePO">Undo Complete</GridCommandButton>
                                }
                                // if (approvalPo.RemainingTotal == 0 && AllowEdit)
                                // {
                                //     <GridCommandButton Class="tooltip-target" OnClick="@UnApprovePO" Title="Mark Incomplete" Command="CompletePO">Undo Complete</GridCommandButton>
                                // }
                            }
                        </GridCommandColumn>
                    </GridColumns>
                </TelerikGrid>
            </div>
        </div>
    }

</div>

<ERP.Web.Components.ApprovePartPaymentPO @ref="PartPaymentModal" PoHeader="PoHeader" HandlePartPayment="HandlePartPaymentSubmit" />

@code {
    private bool displayLoadingSpinner { get; set; } = false;
    private string? SelectedJob { get; set; }
    private string? JobSelected { get; set; }
    public List<PoheaderDto>? PoData { get; set; }
    public List<PoapprovalDto>? PoApprovalData { get; set; }
    private TelerikGrid<PoheaderDto>? PoGridRef { get; set; }
    private ApprovePartPaymentPO? PartPaymentModal { get; set; }
    public PoheaderDto? PoHeader { get; set; }
    private TelerikPopover PartialPaymentInfoPopoverRef { get; set; } = null!;
    private bool AllowEdit { get; set; } = true;
    // Success/Error
    public TelerikNotification NotificationReference { get; set; }

    protected override async Task OnInitializedAsync()
    {
        SubdivisionJobPickService.OnChanged += JobChangedHandler;
        if (SubdivisionJobPickService.JobNumber != null)
        {
            displayLoadingSpinner = true;
            StateHasChanged();
            JobSelected = SubdivisionJobPickService.JobNumber;
            var getData = await PoService.GetPOApprovalByJobAsync(JobSelected);
            PoData = getData.Value;
            displayLoadingSpinner = false;
            StateHasChanged();
        }
        var user = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userName = user.User.Identity.Name.Split('@')[0];
        var userReadOnly = user.User.IsInRole("ReadOnly");
        AllowEdit = !userReadOnly;
    }
    public void Dispose()
    {
        SubdivisionJobPickService.OnChanged -= JobChangedHandler;
    }
    async Task JobChangedHandler()
    {
        var selected = SubdivisionJobPickService.JobNumber;
        if (JobSelected != selected)//check if actual change
        {   
            displayLoadingSpinner = true;
            StateHasChanged();
            JobSelected = selected;
            var getData = await PoService.GetPOApprovalByJobAsync(JobSelected);
            PoData = getData.Value;
            displayLoadingSpinner = false;
            StateHasChanged();
        }
    }
    protected async Task ApprovePO(GridCommandEventArgs args)
    {
        var approveItem = args.Item as PoheaderDto;
        if (approveItem != null)
        {
            approveItem.PartPaymentAmt = null; // to override previous partial payment value
            var responseItem = await PoService.ApprovePo(approveItem);
            approveItem.RemainingTotal = 0d;
            approveItem.ApproveTillDate = responseItem.Value.Pototal;
            approveItem.Approvedby = responseItem.Value.Approvedby;
            approveItem.Postatus = responseItem.Value.Postatus;
            approveItem.PostatusNavigation = responseItem.Value.PostatusNavigation;
            approveItem.Podateapproved = responseItem.Value.Podateapproved;
            approveItem.TaskCompleteBy = responseItem.Value.TaskCompleteBy;
            approveItem.TaskCompleteDate = responseItem.Value.TaskCompleteDate;
            approveItem.Postatus = responseItem.Value.Postatus;
            approveItem.PendInvNumber = responseItem.Value.PendInvNumber;
        }
        PoGridRef?.Rebind();
    }
    protected async Task UnApprovePO(GridCommandEventArgs args)
    {
        var approveItem = args.Item as PoheaderDto;
        if (approveItem != null)
        {
            approveItem.PartPaymentAmt = null; // to override previous partial payment value
            var responseItem = await PoService.UnApprovePo(approveItem);
            approveItem.RemainingTotal = approveItem.Pototal;
            approveItem.ApproveTillDate = responseItem.Value.Pototal;
            approveItem.Approvedby = responseItem.Value.Approvedby;
            approveItem.Postatus = responseItem.Value.Postatus;
            approveItem.PostatusNavigation = responseItem.Value.PostatusNavigation;
            approveItem.Podateapproved = responseItem.Value.Podateapproved;
            approveItem.TaskCompleteBy = responseItem.Value.TaskCompleteBy;
            approveItem.TaskCompleteDate = responseItem.Value.TaskCompleteDate;
            approveItem.Postatus = responseItem.Value.Postatus;
            approveItem.PendInvNumber = responseItem.Value.PendInvNumber;
        }
        PoGridRef?.Rebind();
    }
    protected async Task ApprovePartPO(GridCommandEventArgs args)
    {
        var approveItem = args.Item as PoheaderDto;
        if (approveItem != null)
        {
            PoHeader = approveItem;
            PartPaymentModal?.Show();
            StateHasChanged();
        }
    }

    private void HandlePartPaymentSubmit(ResponseModel<PoheaderDto> response)
    {
        if (response.IsSuccess)
        {
            var poHeader = PoData.SingleOrDefault(x => x.PoheaderId == response.Value.PoheaderId);
            if (poHeader != null)
            {
                poHeader.ApproveTillDate += response.Value.PartPaymentAmt;
                poHeader.RemainingTotal = poHeader.RemainingTotal - response.Value.PartPaymentAmt;
                poHeader.Approvedby = response.Value.Approvedby;
                poHeader.Postatus = response.Value.Postatus;
                poHeader.PostatusNavigation = response.Value.PostatusNavigation;
                poHeader.Podateapproved = response.Value.Podateapproved;
                poHeader.TaskCompleteBy = response.Value.TaskCompleteBy;
                poHeader.TaskCompleteDate = response.Value.TaskCompleteDate;
                poHeader.Postatus = response.Value.Postatus;
                poHeader.PendInvNumber = response.Value.PendInvNumber;
            }

            PartPaymentModal?.Hide();
            ShowSuccessOrErrorNotification("Approved partial payment", true);
            PoGridRef?.Rebind();
        }
        else
        {
            ShowSuccessOrErrorNotification("Error while approving partial payment", false);
        }
    }

    private async void BeforePartialPaymentInfoPopoverOpen(PoheaderDto poheader)
    {
        PoApprovalData = (await PoService.GetPoApprovalByPoHeaderIdAsync(poheader.PoheaderId)).Value;
        PartialPaymentInfoPopoverRef.Refresh();
    }

    private void ClosePartialPaymentInfoPopover()
    {
        PartialPaymentInfoPopoverRef.Hide();
    }

    private void ShowSuccessOrErrorNotification(string message, bool success)
    {
        NotificationReference.Show(new NotificationModel()
            {
                Text = message,
                ThemeColor = success ? ThemeConstants.Notification.ThemeColor.Success : ThemeConstants.Notification.ThemeColor.Error
            });
    }
}

