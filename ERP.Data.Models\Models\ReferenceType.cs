﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ReferenceType
{
    public int ReferenceTypeId { get; set; }

    public string? ReferenceType1 { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public virtual ICollection<Estheader> Estheaders { get; set; } = new List<Estheader>();
}
