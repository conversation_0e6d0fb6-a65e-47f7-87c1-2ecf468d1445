﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class ScheduleChain
{
    public int ScheduleChainId { get; set; }

    public int ScheduleMasterId { get; set; }

    public int SactivityId { get; set; }

    public int SubNumber { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public virtual Sactivity Sactivity { get; set; } = null!;

    public virtual ICollection<ScheduleChainSchedAid> ScheduleChainSchedAids { get; set; } = new List<ScheduleChainSchedAid>();

    public virtual ScheduleMaster ScheduleMaster { get; set; } = null!;
}
