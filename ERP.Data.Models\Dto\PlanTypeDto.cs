﻿using AutoMapper;
using ERP.Data.Models.Abstract;
using System;
using System.Collections.Generic;

namespace ERP.Data.Models.Dto;

public class PlanTypeDto : IMapFrom<PlanType>
{
    public int PlanTypeId { get; set; }

    public string? PlanTypeCode { get; set; }

    public string? Description { get; set; }

    public int? DivId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }
    public void Mapping(Profile profile)
    {
        profile.CreateMap<PlanTypeDto, PlanType>().ReverseMap();
    }

}
