﻿using ERP.Data.Models.Dto;
using Telerik.Windows.Documents.Spreadsheet.FormatProviders;
using Telerik.Windows.Documents.Spreadsheet.FormatProviders.OpenXml.Xlsx;
using Telerik.Windows.Documents.Spreadsheet.Model;
using Telerik.Windows.Documents.Spreadsheet.Model.DataValidation;
using Telerik.Windows.Documents.Spreadsheet.Model.ConditionalFormattings;
using Telerik.Documents.Media;
using System.Diagnostics;
using Telerik.DataSource.Extensions;
using System.Collections.ObjectModel;
using Telerik.Windows.Documents.Spreadsheet.Formatting;

namespace ERP.Web.DocumentProcessing
{
    public static class SalesWorksheetExcelHelper
    {
       // public const int SubdivisionOptionActivityCol = 0;
        public const int SubdivisionCol = 0;
        public const int OptionCodeCol = 1;
        //public const int LumpSumCol = 2;
        //public const int ErrorsCol = 3;
        //public const int PercentageOfTotalCol = 4;
        //public const int CostCol = 5;
        //public const int SellPriceCol = 6;
        //public const int MarkupCol = 7;
        //public const int PriceDateCol = 8;
        //public const int MarkupTypeCol = 9;
        //public const int MarketValueCol = 10;
        //public const int MarkupPercentageCol = 11;
        //public const int MarginCol = 12;
        //public const int MarginPercentageCol = 13;
        //public const int IDCol = 14;
        //public const int PlanOrOptionOrActivityCol = 15; //to determine whether its a plan, option or activity while import
        public const int OptionCol = 2;
        public const int ActivityCol = 3;
        public const int VenderCol = 4;
        public const int LumpSumCol = 5;
        public const int ErrorsCol = 6;
        public const int PercentageOfTotalCol = 7;
        public const int CostCol = 8;
        public const int SellPriceCol = 9;
        public const int MarkupCol = 10;
        public const int PriceDateCol = 11;
        public const int MarkupTypeCol = 12;
        public const int MarketValueCol = 13;
        public const int MarkupPercentageCol = 14;
        public const int MarginCol = 15;
        public const int MarginPercentageCol = 16;
        public const int IDCol = 17;
        public const int PlanOrOptionOrActivityCol = 18; //to determine whether its a plan, option or activity while import
        public const int IsBaseHouseCol = 19; 
        public static byte[] ExportToExcel(List<WorksheetTreeModel> worksheetData)
        {
            Workbook workbook = new Workbook();
            workbook.Sheets.Add(SheetType.Worksheet);
            //Telerik.Windows.Documents.Spreadsheet.Model.Worksheet worksheet = workbook.ActiveWorksheet;
            Worksheet worksheet = workbook.ActiveWorksheet;
            worksheet.GroupingProperties.SummaryRowIsBelow = false;

            SetColumnTitles(worksheet);

            Dictionary<Guid, Tuple<int, int>> planRowMap = new Dictionary<Guid, Tuple<int, int>>();
            Dictionary<Guid, int> optionRowMap = new Dictionary<Guid, int>();
            List<Guid> optionIdsList = new List<Guid>();
            int currentPlanRowIndex = 1;

            foreach (var worksheetPlan in worksheetData)
            {
                planRowMap.Add(worksheetPlan.Id, new Tuple<int, int>(currentPlanRowIndex, -1));
                int currentOptionRowIndex = currentPlanRowIndex + 1;

                foreach (var optionRow in worksheetPlan.Children)
                {
                    optionIdsList.Add(optionRow.Id);
                    optionRowMap.Add(optionRow.Id, currentOptionRowIndex);
                    currentOptionRowIndex += optionRow.Children.Count;
                    currentOptionRowIndex += 1;
                }

                currentPlanRowIndex += worksheetPlan.Children.SelectRecursive(x => x.Children).ToList().Count;
                var currentPlanValue = planRowMap[worksheetPlan.Id];
                planRowMap[worksheetPlan.Id] = new Tuple<int, int>(currentPlanValue.Item1, currentPlanRowIndex);
                currentPlanRowIndex += 1;
            }


            Parallel.ForEach(worksheetData, worksheetPlan =>
            {
                Tuple<int, int> currentPlanRowIndexRange;

                if (planRowMap.TryGetValue(worksheetPlan.Id, out currentPlanRowIndexRange))
                {
                    int currentPlanRowIndex = currentPlanRowIndexRange.Item1;
                    int currentPlanRowLastIndex = currentPlanRowIndexRange.Item2;
                    lock (worksheet)
                    {
                        //worksheet.Cells[currentPlanRowIndex, SubdivisionOptionActivityCol].SetValue($"{worksheetPlan.SubdivisionName}|{worksheetPlan.PlanName}|{worksheetPlan.PlanNumber}");
                        for (int index = currentPlanRowIndex; index <= currentPlanRowLastIndex; index++)
                        {
                            worksheet.Cells[index, SubdivisionCol].SetFormat(new CellValueFormat("@"));
                            worksheet.Cells[index, SubdivisionCol].SetValue(worksheetPlan.SubdivisionName);
                        }
                        worksheet.Cells[currentPlanRowIndex, ErrorsCol].SetValue(worksheetPlan.ErrorCount != null ? (double)worksheetPlan.ErrorCount : 0);
                        worksheet.Cells[currentPlanRowIndex, CostCol].SetValue(worksheetPlan.Cost != null ? (double) worksheetPlan.Cost : 0);
                        worksheet.Cells[currentPlanRowIndex, SellPriceCol].SetValue(worksheetPlan.SellPrice != null ? (double) worksheetPlan.SellPrice : 0);
                        worksheet.Cells[currentPlanRowIndex, IDCol].SetValue(worksheetPlan.WorksheetPlanId != null ? (double)worksheetPlan.WorksheetPlanId : 0);
                        worksheet.Cells[currentPlanRowIndex, PlanOrOptionOrActivityCol].SetFormat(new CellValueFormat("@"));
                        worksheet.Cells[currentPlanRowIndex, PlanOrOptionOrActivityCol].SetValue("Plan");
                        //worksheet.Rows[currentPlanRowIndex + 1, currentPlanRowLastIndex].Group();
                    }
                }
            });
            var optionsList = worksheetData.SelectRecursive(x => x.Children).Where(x => optionIdsList.Contains(x.Id)).ToList();  
            Parallel.ForEach(optionsList, worksheetOption =>
            {
                int currentOptionRowIndex;
                if (optionRowMap.TryGetValue(worksheetOption.Id, out currentOptionRowIndex))
                {
                    lock (worksheet)
                    {

                        worksheet.Cells[currentOptionRowIndex, OptionCol].SetFormat(new CellValueFormat("@"));//sets the format to string so it doesn't try to parse the string as other types first, which is slow
                        worksheet.Cells[currentOptionRowIndex, OptionCodeCol].SetFormat(new CellValueFormat("@"));
                        //worksheet.Cells[currentOptionRowIndex, SubdivisionOptionActivityCol].SetValue($"{worksheetOption.OptionCode}|{worksheetOption.OptionName}");
                        worksheet.Cells[currentOptionRowIndex, OptionCol].SetValue(worksheetOption.OptionName);
                        worksheet.Cells[currentOptionRowIndex, OptionCodeCol].SetValue(worksheetOption.OptionCode);
                        worksheet.Cells[currentOptionRowIndex, LumpSumCol].SetValue(worksheetOption.LumpSum);
                        worksheet.Cells[currentOptionRowIndex, ErrorsCol].SetValue(worksheetOption.ErrorCount != null ? (double)worksheetOption.ErrorCount : 0);
                        //worksheet.Cells[currentOptionRowIndex, SellPriceCol].SetValue($"=IF(J{currentOptionRowIndex + 1}=\"Markup %\", CEILING(F{currentOptionRowIndex + 1}+H{currentOptionRowIndex + 1},10), IF(J{currentOptionRowIndex + 1}=\"Market Value\", K{currentOptionRowIndex + 1}, IF(J{currentOptionRowIndex + 1}=\"Margin %\", CEILING(F{currentOptionRowIndex + 1}+M{currentOptionRowIndex + 1},10), \"Select a valid Markup type\")))");
                        //worksheet.Cells[currentOptionRowIndex, MarkupCol].SetValue($"=L{currentOptionRowIndex + 1}/100*F{currentOptionRowIndex + 1}");
                        worksheet.Cells[currentOptionRowIndex, SellPriceCol].SetValueAsFormula($"=IF(M{currentOptionRowIndex + 1}=\"Markup %\", CEILING(I{currentOptionRowIndex + 1}+K{currentOptionRowIndex + 1},10), IF(M{currentOptionRowIndex + 1}=\"Market Value\", N{currentOptionRowIndex + 1}, IF(M{currentOptionRowIndex + 1}=\"Margin %\", CEILING(I{currentOptionRowIndex + 1}+P{currentOptionRowIndex + 1},10), \"Select a valid Markup type\")))");
                        worksheet.Cells[currentOptionRowIndex, MarkupCol].SetValueAsFormula($"=O{currentOptionRowIndex + 1}/100*I{currentOptionRowIndex + 1}");
                        //worksheet.Cells[currentOptionRowIndex, MarkupCol].SetIsLocked(true);
                        worksheet.Cells[currentOptionRowIndex, MarkupCol].SetFill(new PatternFill(PatternType.Solid, Colors.LightGray, Colors.Transparent));
                        worksheet.Cells[currentOptionRowIndex, PriceDateCol].SetValue(worksheetOption.PriceDate?.ToString("MM/dd/yyyy") ?? "");
                        var markupTypeString = worksheetOption.MarkupType == 0 ? "Markup %" : worksheetOption.MarkupType == 1 ? "Market Value" : "Margin %";
                        worksheet.Cells[currentOptionRowIndex, MarkupTypeCol].SetFormat(new CellValueFormat("@"));
                        worksheet.Cells[currentOptionRowIndex, MarkupTypeCol].SetValue(markupTypeString);
                        worksheet.Cells[currentOptionRowIndex, MarketValueCol].SetValue(worksheetOption.MarketValue != null ? (double)worksheetOption.MarketValue : 0);
                        worksheet.Cells[currentOptionRowIndex, MarkupPercentageCol].SetValue(worksheetOption.MarkupPercent != null ? (double)worksheetOption.MarkupPercent : 0);
                        //worksheet.Cells[currentOptionRowIndex, MarginCol].SetValue($"=N{currentOptionRowIndex + 1}*F{currentOptionRowIndex + 1}/(100-N{currentOptionRowIndex + 1})");
                        worksheet.Cells[currentOptionRowIndex, MarginCol].SetValueAsFormula($"=Q{currentOptionRowIndex + 1}*I{currentOptionRowIndex + 1}/(100-Q{currentOptionRowIndex + 1})");
                        //worksheet.Cells[currentOptionRowIndex, MarginCol].SetIsLocked(true);
                        worksheet.Cells[currentOptionRowIndex, MarginCol].SetFill(new PatternFill(PatternType.Solid, Colors.LightGray, Colors.Transparent));
                        worksheet.Cells[currentOptionRowIndex, MarginPercentageCol].SetValue(worksheetOption.MarginPercent != null ? (double)worksheetOption.MarginPercent : 0);
                        worksheet.Cells[currentOptionRowIndex, IDCol].SetValue(worksheetOption.IsBaseHouse != true ? (worksheetOption.WorksheetOptionId != null ? (double)worksheetOption.WorksheetOptionId : 0) : (double)worksheetOption.WorksheetPlanId); // set plan id in the idcol when base house
                        worksheet.Cells[currentOptionRowIndex, PlanOrOptionOrActivityCol].SetFormat(new CellValueFormat("@"));
                        worksheet.Cells[currentOptionRowIndex, PlanOrOptionOrActivityCol].SetValue("Option");
                        worksheet.Cells[currentOptionRowIndex, IsBaseHouseCol].SetValue(worksheetOption.IsBaseHouse.ToString());

                        CreateDropdownForMarketType(worksheet, currentOptionRowIndex);

                        SetConditionalFormattingForMarketValuePercentage(worksheet, currentOptionRowIndex); // sets the Market Value cell to gray when Margin % and Markup % are selected
                        SetConditionalFormattingForMarginPercentage(worksheet, currentOptionRowIndex); // sets the Margin % cell to gray when Market Value and Markup % are selected
                        SetConditionalFormattingForMarkupPercentage(worksheet, currentOptionRowIndex); // sets the Markup % cell to gray when Market Value and Margin % are selected

                        int currentActivityRowIndex = currentOptionRowIndex + 1;
                        foreach (var worksheetActivity in worksheetOption.Children)
                        {
                            //worksheet.Cells[currentActivityRowIndex, SubdivisionOptionActivityCol].SetValue($"{worksheetActivity.ActivityName}|{worksheetActivity.Vendor}");
                            //worksheet.Cells[currentActivityRowIndex, OptionCodeCol].SetValue($"{worksheetActivity.OptionCode}");
                            // var percentTotal = 
                            worksheet.Cells[currentActivityRowIndex, OptionCol].SetFormat(new CellValueFormat("@"));//sets the format to string so it doesn't try to parse the string as other types first, which is slow
                            worksheet.Cells[currentActivityRowIndex, OptionCodeCol].SetFormat(new CellValueFormat("@"));
                            worksheet.Cells[currentActivityRowIndex, VenderCol].SetFormat(new CellValueFormat("@"));
                            worksheet.Cells[currentActivityRowIndex, ActivityCol].SetFormat(new CellValueFormat("@"));
                            worksheet.Cells[currentActivityRowIndex, PlanOrOptionOrActivityCol].SetFormat(new CellValueFormat("@"));
                            worksheet.Cells[currentActivityRowIndex, OptionCol].SetValue(worksheetOption.OptionName);
                            worksheet.Cells[currentActivityRowIndex, OptionCodeCol].SetValue(worksheetOption.OptionCode);
                            worksheet.Cells[currentActivityRowIndex, ActivityCol].SetValue(worksheetActivity.ActivityName);
                            worksheet.Cells[currentActivityRowIndex, VenderCol].SetValue(worksheetActivity.Vendor);
                            worksheet.Cells[currentActivityRowIndex, LumpSumCol].SetValue(worksheetActivity.LumpSum);
                            worksheet.Cells[currentActivityRowIndex, ErrorsCol].SetValue(worksheetActivity.ErrorCount != null ? (double)worksheetActivity.ErrorCount : 0);//parse to double first so it looks for numeric data type rather than try to parse
                            worksheet.Cells[currentActivityRowIndex, CostCol].SetValue(worksheetActivity.Cost != null ? (double)worksheetActivity.Cost : 0);
                            //worksheet.Cells[currentActivityRowIndex, PercentageOfTotalCol].SetValue($"=F{currentActivityRowIndex + 1}/F{currentOptionRowIndex + 1}*100");
                            worksheet.Cells[currentActivityRowIndex, PercentageOfTotalCol].SetValueAsFormula($"=I{currentActivityRowIndex + 1}/I{currentOptionRowIndex + 1}*100");
                            worksheet.Cells[currentActivityRowIndex, PercentageOfTotalCol].SetValueAsFormula($"=I{currentActivityRowIndex + 1}/I{currentOptionRowIndex + 1}*100");
                            worksheet.Cells[currentActivityRowIndex, IDCol].SetValue(worksheetActivity.WorksheetActivityId != null ? (double)worksheetActivity.WorksheetActivityId : 0);
                            worksheet.Cells[currentActivityRowIndex, PlanOrOptionOrActivityCol].SetValue("Activity");

                            currentActivityRowIndex += 1;
                        }
                        //worksheet.Rows[currentOptionRowIndex + 1, currentActivityRowIndex - 1].Group();

                        //worksheet.Cells[currentOptionRowIndex, CostCol].SetValue($"=SUM(F{currentOptionRowIndex + 2}:F{currentActivityRowIndex})"); // cost cell for options
                        worksheet.Cells[currentOptionRowIndex, CostCol].SetValueAsFormula($"=SUM(I{currentOptionRowIndex + 2}:I{currentActivityRowIndex})"); // cost cell for options
                    }
                }
            });
            worksheet.Columns[IDCol].SetHidden(true);
            worksheet.Columns[PlanOrOptionOrActivityCol].SetHidden(true);
            worksheet.Columns[IsBaseHouseCol].SetHidden(true);
            //ColumnSelection columnSelection = worksheet.Columns[0, 13];
            ColumnSelection columnSelection = worksheet.Columns[SubdivisionCol, MarginPercentageCol];
            columnSelection.AutoFitWidth();

            IWorkbookFormatProvider formatProvider = new XlsxFormatProvider();
            using (MemoryStream memoryStream = new MemoryStream())
            {
                formatProvider.Export(workbook, memoryStream);
                return memoryStream.ToArray();
            }

        }

        //Data Validation to make Margin and Margin Perc fields disabled when Margin is not selected as Markup Type
        private static void SetDataValidationForMarginAndMarginPerc(Worksheet worksheet, int currentOptionRowIndex)
        {
            CellIndex dataValidationRuleMarginCellIndex = new CellIndex(currentOptionRowIndex + 1, MarginCol);
            CellIndex dataValidationRuleMarginPercCellIndex = new CellIndex(currentOptionRowIndex + 1, MarginPercentageCol);

            SingleArgumentDataValidationRuleContext MarginColContext = new SingleArgumentDataValidationRuleContext(worksheet, dataValidationRuleMarginCellIndex);
            MarginColContext.InputMessageTitle = "Enabled only when MarkupType = Margin %";
            MarginColContext.ErrorStyle = ErrorStyle.Stop;
            MarginColContext.ErrorAlertTitle = "Disabled";
            MarginColContext.ErrorAlertContent = "Disabled when MarkupType not equal to Margin %";
            MarginColContext.Argument1 = $"=J{currentOptionRowIndex + 1}=\"Margin %\"";

            CustomDataValidationRule rule = new CustomDataValidationRule(MarginColContext);
            worksheet.Cells[dataValidationRuleMarginCellIndex].SetDataValidationRule(rule);

            SingleArgumentDataValidationRuleContext MarginPercContext = new SingleArgumentDataValidationRuleContext(worksheet, dataValidationRuleMarginPercCellIndex);
            MarginPercContext.InputMessageTitle = "Enabled only when MarkupType = Margin %";
            MarginPercContext.ErrorStyle = ErrorStyle.Stop;
            MarginPercContext.ErrorAlertTitle = "Disabled";
            MarginPercContext.ErrorAlertContent = "Disable when MarkupType not equal to Margin %";
            MarginPercContext.Argument1 = $"=J{currentOptionRowIndex + 1}=\"Margin %\"";

            CustomDataValidationRule marginPercRule = new CustomDataValidationRule(MarginPercContext);
            worksheet.Cells[dataValidationRuleMarginPercCellIndex].SetDataValidationRule(marginPercRule);
        }
        //Data Validation to make Markup and Markup Perc fields disabled when Margin is not selected as Markup Type
        private static void SetDataValidationForMarkupAndMarkupPerc(Worksheet worksheet, int currentOptionRowIndex)
        {
            CellIndex dataValidationRuleMarkupCellIndex = new CellIndex(currentOptionRowIndex + 1, MarkupCol);
            CellIndex dataValidationRuleMarkypPercCellIndex = new CellIndex(currentOptionRowIndex + 1, MarkupPercentageCol);

            SingleArgumentDataValidationRuleContext MarkupColContext = new SingleArgumentDataValidationRuleContext(worksheet, dataValidationRuleMarkupCellIndex);
            MarkupColContext.InputMessageTitle = "Enabled only when MarkupType = Markup %";
            MarkupColContext.ErrorStyle = ErrorStyle.Stop;
            MarkupColContext.ErrorAlertTitle = "Disabled";
            MarkupColContext.ErrorAlertContent = "Disabled when MarkupType not equal to Markup %";
            MarkupColContext.Argument1 = $"=J{currentOptionRowIndex + 1}=\"Markup %\"";

            CustomDataValidationRule rule = new CustomDataValidationRule(MarkupColContext);
            worksheet.Cells[dataValidationRuleMarkupCellIndex].SetDataValidationRule(rule);

            SingleArgumentDataValidationRuleContext MarkupPercContext = new SingleArgumentDataValidationRuleContext(worksheet, dataValidationRuleMarkypPercCellIndex);
            MarkupPercContext.InputMessageTitle = "Enabled only when MarkupType = Markup %";
            MarkupPercContext.ErrorStyle = ErrorStyle.Stop;
            MarkupPercContext.ErrorAlertTitle = "Disabled";
            MarkupPercContext.ErrorAlertContent = "Disable when MarkupType not equal to Markup %";
            MarkupPercContext.Argument1 = $"=J{currentOptionRowIndex + 1}=\"Markup %\"";

            CustomDataValidationRule markupPercRule = new CustomDataValidationRule(MarkupPercContext);
            worksheet.Cells[dataValidationRuleMarkypPercCellIndex].SetDataValidationRule(markupPercRule);
        }
        //Data Validation to make MarketValue field disabled when Market Value is not selected as Markup Type
        private static void SetDataValidationForMarketValue(Worksheet worksheet, int currentOptionRowIndex)
        {
            CellIndex dataValidationRuleMaretValueCellIndex = new CellIndex(currentOptionRowIndex + 1, MarketValueCol);

            SingleArgumentDataValidationRuleContext MarketValueContext = new SingleArgumentDataValidationRuleContext(worksheet, dataValidationRuleMaretValueCellIndex);
            MarketValueContext.InputMessageTitle = "Enabled only when MarkupType = Market Value";
            MarketValueContext.ErrorStyle = ErrorStyle.Stop;
            MarketValueContext.ErrorAlertTitle = "Disabled";
            MarketValueContext.ErrorAlertContent = "Disabled when MarkupType not equal to Market Value";
            MarketValueContext.Argument1 = $"=J{currentOptionRowIndex + 1}=\"Market Value\"";

            CustomDataValidationRule rule = new CustomDataValidationRule(MarketValueContext);
            worksheet.Cells[dataValidationRuleMaretValueCellIndex].SetDataValidationRule(rule);
        }

        private static void SetConditionalFormattingForMarginPercentage(Worksheet worksheet, int currentOptionRowIndex)
        {
            FormulaRule rule1 = new($"=J{currentOptionRowIndex + 1}=\"Market Value\"", new DifferentialFormatting());
            FormulaRule rule2 = new($"=J{currentOptionRowIndex + 1}=\"Markup %\"", new DifferentialFormatting());

            DifferentialFormatting formatting = new DifferentialFormatting();
            formatting.Fill = new PatternFill(PatternType.Solid, Colors.LightGray, Colors.Transparent);
            rule1.Formatting = formatting;
            rule2.Formatting = formatting;

            ConditionalFormatting conditionalFormatting1 = new ConditionalFormatting(rule1);
            ConditionalFormatting conditionalFormatting2 = new ConditionalFormatting(rule2);
            worksheet.Cells[currentOptionRowIndex, MarginPercentageCol].AddConditionalFormatting(conditionalFormatting1);
            worksheet.Cells[currentOptionRowIndex, MarginPercentageCol].AddConditionalFormatting(conditionalFormatting2);
        }

        private static void SetConditionalFormattingForMarkupPercentage(Worksheet worksheet, int currentOptionRowIndex)
        {
            FormulaRule rule1 = new($"=J{currentOptionRowIndex + 1}=\"Market Value\"", new DifferentialFormatting());
            FormulaRule rule2 = new($"=J{currentOptionRowIndex + 1}=\"Margin %\"", new DifferentialFormatting());

            DifferentialFormatting formatting = new DifferentialFormatting();
            formatting.Fill = new PatternFill(PatternType.Solid, Colors.LightGray, Colors.Transparent);
            rule1.Formatting = formatting;
            rule2.Formatting = formatting;

            ConditionalFormatting conditionalFormatting1 = new ConditionalFormatting(rule1);
            ConditionalFormatting conditionalFormatting2 = new ConditionalFormatting(rule2);
            worksheet.Cells[currentOptionRowIndex, MarkupPercentageCol].AddConditionalFormatting(conditionalFormatting1);
            worksheet.Cells[currentOptionRowIndex, MarkupPercentageCol].AddConditionalFormatting(conditionalFormatting2);
        }

        private static void SetConditionalFormattingForMarketValuePercentage(Worksheet worksheet, int currentOptionRowIndex)
        {
            FormulaRule rule1 = new($"=J{currentOptionRowIndex + 1}=\"Markup %\"", new DifferentialFormatting());
            FormulaRule rule2 = new($"=J{currentOptionRowIndex + 1}=\"Margin %\"", new DifferentialFormatting());

            DifferentialFormatting formatting = new DifferentialFormatting();
            formatting.Fill = new PatternFill(PatternType.Solid, Colors.LightGray, Colors.Transparent);
            rule1.Formatting = formatting;
            rule2.Formatting = formatting;

            ConditionalFormatting conditionalFormatting1 = new ConditionalFormatting(rule1);
            ConditionalFormatting conditionalFormatting2 = new ConditionalFormatting(rule2);
            worksheet.Cells[currentOptionRowIndex, MarketValueCol].AddConditionalFormatting(conditionalFormatting1);
            worksheet.Cells[currentOptionRowIndex, MarketValueCol].AddConditionalFormatting(conditionalFormatting2);
        }

        private static void SetColumnTitles(Worksheet worksheet)
        {
            worksheet.Cells[0, SubdivisionCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, OptionCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, ActivityCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, VenderCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, OptionCodeCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, LumpSumCol].SetFormat(new CellValueFormat("@")); 
            worksheet.Cells[0, ErrorsCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, PercentageOfTotalCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, CostCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, SellPriceCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, MarkupCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, PriceDateCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, MarkupTypeCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, MarketValueCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, MarkupPercentageCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, MarginCol].SetFormat(new CellValueFormat("@"));
            worksheet.Cells[0, MarginPercentageCol].SetFormat(new CellValueFormat("@"));
            //worksheet.Cells[0, SubdivisionOptionActivityCol].SetValue("Subdivision/Option/Activity");
            worksheet.Cells[0, SubdivisionCol].SetValue("Subdivision");
            worksheet.Cells[0, OptionCol].SetValue("Option");
            worksheet.Cells[0, ActivityCol].SetValue("Activity");
            worksheet.Cells[0, VenderCol].SetValue("Vendor");
            worksheet.Cells[0, OptionCodeCol].SetValue("Option Code");
            worksheet.Cells[0, LumpSumCol].SetValue("Lump Sum");
            worksheet.Cells[0, ErrorsCol].SetValue("Errors");
            worksheet.Cells[0, PercentageOfTotalCol].SetValue("% of Total");
            worksheet.Cells[0, CostCol].SetValue("Cost");
            worksheet.Cells[0, SellPriceCol].SetValue("Sell Price");
            worksheet.Cells[0, MarkupCol].SetValue("Markup");
            worksheet.Cells[0, PriceDateCol].SetValue("Price Date");
            worksheet.Cells[0, MarkupTypeCol].SetValue("Markup Type");
            worksheet.Cells[0, MarketValueCol].SetValue("Market Value");
            worksheet.Cells[0, MarkupPercentageCol].SetValue("Markup Percentage");
            worksheet.Cells[0, MarginCol].SetValue("Margin");
            worksheet.Cells[0, MarginPercentageCol].SetValue("Margin Percentage");
        }

        private static void CreateDropdownForMarketType(Worksheet worksheet, int currentOptionRowIndex)
        {
            CellIndex dataValidationRuleCellIndex = new CellIndex(currentOptionRowIndex, MarkupTypeCol);

            ListDataValidationRuleContext context = new ListDataValidationRuleContext(worksheet, dataValidationRuleCellIndex);
            context.InputMessageTitle = "Restricted input";
            context.InputMessageContent = "The input is restricted to the Markup Types.";
            context.ErrorStyle = ErrorStyle.Stop;
            context.ErrorAlertTitle = "Wrong value";
            context.ErrorAlertContent = "The entered value is not valid. Allowed values are Markup %, Market value, Margin %";
            context.InCellDropdown = true;
            context.Argument1 = "Markup %, Market value, Margin %";

            ListDataValidationRule rule = new ListDataValidationRule(context);

            worksheet.Cells[dataValidationRuleCellIndex].SetDataValidationRule(rule);
        }
    
        public static ObservableCollection<WorksheetTreeModel> ImportWorksheetFromExcel(Workbook workbook)
        {
            var worksheet = workbook.Worksheets.FirstOrDefault();
            var worksheetData = new ObservableCollection<WorksheetTreeModel>();
            try
            {

                if (worksheet != null)
                {
                    var lastRowIndex = worksheet.UsedCellRange.ToIndex.RowIndex; // gets the last row
                    var planIndices = new List<int>();
                    var optionIndices = new List<int>();

                    for (int rowIndex = 1; rowIndex <= lastRowIndex; rowIndex++)
                    {
                        //var rowOutline = worksheet.Rows[rowIndex].GetOutlineLevel().Value;
                        var planOrOptionOrActivity = worksheet.Cells[rowIndex, PlanOrOptionOrActivityCol].GetValue().Value.RawValue;
                        if (planOrOptionOrActivity == "Plan")
                        {
                            planIndices.Add(rowIndex);
                        }
                        if (planOrOptionOrActivity == "Option")
                        {
                            optionIndices.Add(rowIndex);
                        }
                    }

                    var currentOption = new List<WorksheetTreeModel>();
                    Guid currentPlanId = Guid.NewGuid();
                    Guid currentOptionId = Guid.NewGuid();

                    for (int rowIndex = 1; rowIndex <= lastRowIndex; rowIndex++)
                    {
                        if (planIndices.Contains(rowIndex))
                        {
                            // var subdivisionPlanDetails = (worksheet.Cells[rowIndex, SubdivisionOptionActivityCol].GetValue().Value.RawValue).Split("|");
                            var subdivisionPlanDetails = (worksheet.Cells[rowIndex, SubdivisionCol].GetValue().Value.RawValue);
                            var cost = worksheet.Cells[rowIndex, CostCol].GetValue().Value.GetResultValueAsString(worksheet.Cells[rowIndex, CostCol].GetFormat().Value);
                            var sellPrice = worksheet.Cells[rowIndex, SellPriceCol].GetValue().Value.GetResultValueAsString(worksheet.Cells[rowIndex, SellPriceCol].GetFormat().Value);
                            WorksheetTreeModel planData = new WorksheetTreeModel()
                            {
                                Id = Guid.NewGuid(),
                                PlanId = Int32.Parse(worksheet.Cells[rowIndex, IDCol].GetValue().Value.RawValue),
                                //SubdivisionName = subdivisionPlanDetails[0],
                                //PlanName = subdivisionPlanDetails[1],
                                //PlanNumber = subdivisionPlanDetails[2],
                                SubdivisionName = subdivisionPlanDetails,
                                Cost = cost == "" ? 0 : Decimal.Parse(cost),
                                SellPrice = sellPrice == "" ? 0 : Decimal.Parse(sellPrice),
                                Children = new List<WorksheetTreeModel>(),
                                IsActive = true
                            };
                            currentPlanId = planData.Id;
                            worksheetData.Add(planData);
                        }
                        else if (optionIndices.Contains(rowIndex))
                        {
                            //var subdivisionPlanDetails = (worksheet.Cells[rowIndex, SubdivisionOptionActivityCol].GetValue().Value.RawValue).Split("|");
                            var optionDetails = (worksheet.Cells[rowIndex, OptionCol].GetValue().Value.RawValue);
                            var optionCodeDetails = (worksheet.Cells[rowIndex, OptionCodeCol].GetValue().Value.RawValue);
                            var markupTypeString = worksheet.Cells[rowIndex, MarkupTypeCol].GetValue().Value.RawValue;
                            var lumpsum = worksheet.Cells[rowIndex, LumpSumCol].GetValue().Value.RawValue;
                            var errors = worksheet.Cells[rowIndex, ErrorsCol].GetValue().Value.RawValue;
                            var cost = worksheet.Cells[rowIndex, CostCol].GetValue().Value.GetResultValueAsString(worksheet.Cells[rowIndex, CostCol].GetFormat().Value);
                            var sellPrice = worksheet.Cells[rowIndex, SellPriceCol].GetValue().Value.GetResultValueAsString(worksheet.Cells[rowIndex, SellPriceCol].GetFormat().Value);
                            var markup = worksheet.Cells[rowIndex, MarkupCol].GetValue().Value.GetResultValueAsString(worksheet.Cells[rowIndex, MarkupCol].GetFormat().Value);
                            //var priceDate = worksheet.Cells[rowIndex, PriceDateCol].GetValue().Value.RawValue;
                            ICellValue cellValue = worksheet.Cells[rowIndex, PriceDateCol].GetValue().Value;
                            CellValueFormat format = worksheet.Cells[rowIndex, PriceDateCol].GetFormat().Value;
                            string resultAsString = cellValue.GetValueAsString(format);
                            double rawValueAsNumber = Convert.ToDouble(cellValue.RawValue);
                            DateTime? priceDate = FormatHelper.ConvertDoubleToDateTime(rawValueAsNumber).Value.Date;

                            var marketValue = worksheet.Cells[rowIndex, MarketValueCol].GetValue().Value.RawValue;
                            var markupPercent = worksheet.Cells[rowIndex, MarkupPercentageCol].GetValue().Value.RawValue;
                            var margin = worksheet.Cells[rowIndex, MarginCol].GetValue().Value.GetResultValueAsString(worksheet.Cells[rowIndex, MarginCol].GetFormat().Value);
                            var marginPercent = worksheet.Cells[rowIndex, MarginPercentageCol].GetValue().Value.RawValue;
                            var isBaseHouse = worksheet.Cells[rowIndex, IsBaseHouseCol].GetValue().Value.RawValue;
                            WorksheetTreeModel optionData = new WorksheetTreeModel()
                            {
                                ParentId = currentPlanId,
                                Id = Guid.NewGuid(),
                                WorksheetOptionId = isBaseHouse == "True" ? 1 : Int32.Parse(worksheet.Cells[rowIndex, IDCol].GetValue().Value.RawValue),
                                WorksheetPlanId = isBaseHouse == "True" ? Int32.Parse(worksheet.Cells[rowIndex, IDCol].GetValue().Value.RawValue) : null,
                                //OptionCode = subdivisionPlanDetails[0],
                                //OptionName = subdivisionPlanDetails[1],
                                IsBaseHouse = isBaseHouse == "True" ? true : false, 
                                OptionCode = optionCodeDetails,
                                OptionName = optionDetails,
                                LumpSum = lumpsum == "" ? false : Boolean.Parse(lumpsum),
                                ErrorCode = errors == "" ? 0 : Int32.Parse(errors),
                                Cost = cost == "" ? 0 : Decimal.Parse(cost),
                                SellPrice = sellPrice == "" ? 0 : Decimal.Parse(sellPrice),
                                Markup = markup == "" ? 0 : Decimal.Parse(markup),
                                //PriceDate = priceDate == "" ? null : DateTime.Parse(priceDate),
                                PriceDate = priceDate,
                                MarkupType = markupTypeString == "Markup %" ? 0 : markupTypeString == "Market Value" ? 1 : 2,
                                MarketValue = marketValue == "" ? 0 : Decimal.Parse(marketValue),
                                MarkupPercent = markupPercent == "" ? 0 : Double.Parse(markupPercent),
                                Margin = margin == "" ? 0 : Double.Parse(margin),
                                MarginPercent = marginPercent == "" ? 0 : Double.Parse(marginPercent),
                                Children = new List<WorksheetTreeModel>(),
                                IsActive = true
                            };
                            currentOptionId = optionData.Id;
                            var parentPlan = worksheetData.Where(x => x.Id == currentPlanId).SingleOrDefault();
                            if (parentPlan != null)
                            {
                                parentPlan.HasChildren = true;
                                parentPlan.Children.Add(optionData);
                            }
                            //worksheetData.Add(optionData);
                        }
                        else
                        {
                            //var subdivisionPlanDetails = (worksheet.Cells[rowIndex, SubdivisionOptionActivityCol].GetValue().Value.RawValue).Split("|");
                            var activityDetails = (worksheet.Cells[rowIndex, ActivityCol].GetValue().Value.RawValue);
                            var vendorDetails = (worksheet.Cells[rowIndex, VenderCol].GetValue().Value.RawValue);
                            var lumpsum = worksheet.Cells[rowIndex, LumpSumCol].GetValue().Value.RawValue;
                            var errors = worksheet.Cells[rowIndex, ErrorsCol].GetValue().Value.RawValue;
                            var percentOfTotal = worksheet.Cells[rowIndex, PercentageOfTotalCol].GetValue().Value.GetResultValueAsString(worksheet.Cells[rowIndex, PercentageOfTotalCol].GetFormat().Value);
                            var cost = worksheet.Cells[rowIndex, CostCol].GetValue().Value.GetResultValueAsString(worksheet.Cells[rowIndex, CostCol].GetFormat().Value);

                            WorksheetTreeModel activityData = new WorksheetTreeModel()
                            {
                                ParentId = currentOptionId,
                                Id = Guid.NewGuid(),
                                WorksheetActivityId = Int32.Parse(worksheet.Cells[rowIndex, IDCol].GetValue().Value.RawValue),
                                //ActivityName = subdivisionPlanDetails[0],
                                //Vendor = subdivisionPlanDetails[1],
                                ActivityName = activityDetails,
                                Vendor = vendorDetails,
                                OptionCode = worksheet.Cells[rowIndex, OptionCodeCol].GetValue().Value.RawValue,
                                LumpSum = lumpsum == "" ? false : Boolean.Parse(lumpsum),
                                ErrorCode = errors == "" ? 0 : Int32.Parse(errors),
                                PercentOfTotal = percentOfTotal == "#DIV/0!" ? 0 : Double.Parse(percentOfTotal),
                                Cost = cost == "" ? 0 : Decimal.Parse(cost),
                                IsActive = true
                            };
                            var parentOption = worksheetData.SelectMany(x=>x.Children).Where(x => x.Id == currentOptionId).SingleOrDefault();
                            if (parentOption != null)
                            {
                                parentOption.HasChildren = true;
                                parentOption.Children.Add(activityData);
                            }
                            //worksheetData.Add(activityData);
                        }
                    }
                }

                return worksheetData;
            }
            catch (Exception ex)
            {
                var debug = ex.Message;
                return worksheetData;
            }
        }
    }
}
