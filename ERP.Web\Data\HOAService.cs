﻿using Azure.Core;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.ExtendedProperties;
using ERP.Data.Models;
using ERP.Data.Models.Dto;
using ERP.Web.Models;
using ERP.Web.Pages;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Abstractions;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Logging;
using Newtonsoft.Json;
using NLog;
using System.Diagnostics;
using System.Text;
using Telerik.Blazor.Components.FileSelect;

namespace ERP.Web.Data
{
    public class HOAService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly IDownstreamApi _downstreamAPI;
        private readonly MicrosoftIdentityConsentAndConditionalAccessHandler _consentHandler;
        private readonly ITokenAcquisition _tokenAcquisition;
        private static NLog.Logger _logger = LogManager.GetLogger("databaseLogger");
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public HOAService(IConfiguration configuration, HttpClient httpClient, IDownstreamApi downstreamWebApi, MicrosoftIdentityConsentAndConditionalAccessHandler consentHandler, ITokenAcquisition tokenAcquisition, AuthenticationStateProvider authenticationStateProvider)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _downstreamAPI = downstreamWebApi;
            _consentHandler = consentHandler;
            _tokenAcquisition = tokenAcquisition;
            _authenticationStateProvider = authenticationStateProvider;
        }
        public async Task<ResponseModel<List<HoaAssessmentDto>>> GetHOAAsync()
        {

            var assessments = new ResponseModel<List<HoaAssessmentDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/hoa/GetHOA");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<HoaAssessmentDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return assessments;
        }

        public async Task<ResponseModel<List<SubdivisionDto>>> GetSubdivisionsAsync()
        {

            var subdivisions = new ResponseModel<List<SubdivisionDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/hoa/GetSubdivisions");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<SubdivisionDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return subdivisions;
        }
        public async Task<ResponseModel<List<HoaDto>>> GetAssociationsAsync()
        {

            var associations = new ResponseModel<List<HoaDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/hoa/GetAssociations");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<HoaDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return associations;
        }
        public async Task<ResponseModel<List<JobDto>>> GetJobsAsync()
        {

            var jobs = new ResponseModel<List<JobDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
               options => options.RelativePath = $"api/hoa/GetJobs");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<JobDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return jobs;
        }
        public async Task<ResponseModel<List<HoaJobDto>>> GetHOAJobsAsync(HoaAssessmentDto assessment)
        {

            var jobs = new ResponseModel<List<HoaJobDto>>();

            try
            {
                var response = await _downstreamAPI.CallApiForUserAsync(
               "DownstreamApi",
                options => options.RelativePath = $"api/hoa/GetHOAJobs/{assessment.HoaAssessmentId}");
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ResponseModel<List<HoaJobDto>>>(responseString);
                    return result;
                }

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return jobs;
        }
        public async Task<ResponseModel<HoaAssessmentDto>> UpdateAssessmentAsync(HoaAssessmentDto entryToAdd)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<HoaAssessmentDto, ResponseModel<HoaAssessmentDto>>(
                            "DownstreamApi", entryToAdd,
                             options =>
                             {
                                 options.RelativePath = $"api/hoa/UpdateAssessment/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<HoaAssessmentDto>() { IsSuccess = false, Message = "Failed to update" };
        }
        public async Task<ResponseModel<HoaAssessmentDto>> DeleteAssessmentAsync(HoaAssessmentDto entryToDelete)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<HoaAssessmentDto, ResponseModel<HoaAssessmentDto>>(
                            "DownstreamApi", entryToDelete,
                             options =>
                             {
                                 options.RelativePath = $"api/hoa/DeleteAssessment/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<HoaAssessmentDto>() { IsSuccess = false, Message = "Failed to delete" };
        }

        public async Task<ResponseModel<HoaAssessmentDto>> AddAssessmentAsync(HoaAssessmentDto assessment)
        {
            var responseAssessment = new ResponseModel<HoaAssessmentDto>();

            try
            {
                responseAssessment = await _downstreamAPI.PostForUserAsync<HoaAssessmentDto, ResponseModel<HoaAssessmentDto>>(
                    "DownstreamApi", assessment,
                    options =>
                    {
                        options.RelativePath = $"api/hoa/AddAssessment/";
                    });

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseAssessment;
        }
        public async Task<ResponseModel<HoaJobDto>> DeleteJobAsync(HoaJobDto entryToDelete)
        {
            try
            {
                var response = await _downstreamAPI.PostForUserAsync<HoaJobDto, ResponseModel<HoaJobDto>>(
                            "DownstreamApi", entryToDelete,
                             options =>
                             {
                                 options.RelativePath = $"api/hoa/DeleteJob/";
                             });
                return response;

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return new ResponseModel<HoaJobDto>() { IsSuccess = false, Message = "Failed to delete" };
        }
        public async Task<ResponseModel<List<HoaJobDto>>> AddJobsAsync(List<HoaJobDto> jobsToAdd)
        {
            var responseJob = new ResponseModel<List<HoaJobDto>>();

            try
            {
                responseJob = await _downstreamAPI.PostForUserAsync<List<HoaJobDto>, ResponseModel<List<HoaJobDto>>>(
                    "DownstreamApi", jobsToAdd,
                    options =>
                    {
                        options.RelativePath = $"api/hoa/AddJobs/";
                    });

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return responseJob;

        }

        public async Task<ResponseModel<List<HoaJobDto>>> DeleteSelectedJobsForAssessmentAsync(List<HoaJobDto> deleteJobs)
        {
            var response = new ResponseModel<List<HoaJobDto>>();
            try
            {
                response = await _downstreamAPI.PostForUserAsync<List<HoaJobDto>, ResponseModel<List<HoaJobDto>>>(
               "DownstreamApi", deleteJobs,
                options => options.RelativePath = $"api/hoa/DeleteSelectedJobsForAssessment/");

            }
            catch (MicrosoftIdentityWebChallengeUserException ex)
            {
                _consentHandler.HandleException(ex);
            }
            catch (Exception ex)
            {
                var user = await _authenticationStateProvider.GetAuthenticationStateAsync();
#if DEBUG
                _logger.Debug(ex);
#else
                    _logger.ForErrorEvent().Property("username", user.User.Identity.Name.Split('@')[0]).Exception(ex).Log();
#endif
            }

            return response;
        }
    }
    }