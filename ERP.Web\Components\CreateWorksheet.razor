﻿
@inject SalesPriceService SalesPriceService


<TelerikWindow Modal="true"
               @bind-Visible="@IsModalVisible"
               CloseOnOverlayClick="true">
    <WindowTitle>
        <h4 class="page-title">Create a New Worksheet</h4>
    </WindowTitle>
    <WindowContent>
        <EditForm Model="@WorksheetToAdd" OnValidSubmit="@HandleValidAddSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />
            <label class="form-label">Worksheet Name </label>
            <TelerikTextBox @bind-Value="@WorksheetToAdd.WorksheetName"></TelerikTextBox>
            <br />
            <label class="form-label">Worksheet Description </label>
            <TelerikTextBox @bind-Value="@WorksheetToAdd.WorksheetDesc"></TelerikTextBox>
            <br />
            <div class="customSeparator">
                <button type="submit" class="btn btn-primary">Add</button>
                <button type="button" @onclick="CancelAddWorksheet" class="btn btn-secondary">Cancel</button>
                <div style=@ShowLoading>Submitting. Please wait...</div>
                <div style=@ShowError>Something went wrong.</div>
            </div>
        </EditForm>
    </WindowContent>
    <WindowActions>
        <WindowAction Name="Close" />
    </WindowActions>
</TelerikWindow>

@code {

    public bool IsModalVisible { get; set; }
    public WorksheetDto WorksheetToAdd { get; set; } = new WorksheetDto();
    public string ShowLoading{ get; set; } = "display:none";
    public string ShowError { get; set; } = "display:none";
    
    [Parameter]
    public EventCallback<ResponseModel<WorksheetDto>> HandleAddSubmit { get; set; }

    public void Show()
    {
        IsModalVisible = true;
        
    }      

    private async void HandleValidAddSubmit()
    {
        ShowLoading = "";      
        var createWorksheetResponse = await SalesPriceService.CreateWorksheetAsync(WorksheetToAdd);
        ShowLoading = "display:none";
        //TODO: show success/error
        //if (response.IsSuccess)
        //{
        //    ShowLoading = "display:none";
        //}
        //else
        //{
        //    ShowLoading = "display:none";
        //    ShowError = ""; //this won't actually show, since invoking the handle add hides the modal, so that function needs to display the error
        //}
        await HandleAddSubmit.InvokeAsync(createWorksheetResponse);
    }
    async void CancelAddWorksheet()
    {
        IsModalVisible = false;
    }
    public async Task Hide()
    {
        IsModalVisible = false;        
    }
}
