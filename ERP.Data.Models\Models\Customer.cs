﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class Customer
{
    public int CustomerId { get; set; }

    public string? AcntCustomerCode { get; set; }

    public string? CustomerKey { get; set; }

    public string? CustomerName { get; set; }

    public string? Address1 { get; set; }

    public string? Address2 { get; set; }

    public string? City { get; set; }

    public string? State { get; set; }

    public string? Country { get; set; }

    public string? Postcode { get; set; }

    public string? WorkPhone { get; set; }

    public string? HomePhone { get; set; }

    public string? MobilePhone { get; set; }

    public string? Fax { get; set; }

    public string? Email { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[]? RecordTimeStamp { get; set; }

    public bool? SentToNav { get; set; }

    public virtual ICollection<JobCustomer> JobCustomers { get; set; } = new List<JobCustomer>();
}
