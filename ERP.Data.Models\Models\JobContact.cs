﻿using System;
using System.Collections.Generic;

namespace ERP.Data.Models;

public partial class JobContact
{
    public int JobContactId { get; set; }

    public string JobNumber { get; set; } = null!;

    public string UserId { get; set; } = null!;

    public int RoleId { get; set; }

    public DateTime CreatedDateTime { get; set; }

    public DateTime? UpdatedDateTime { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public bool? IsActive { get; set; }

    public byte[] RecordTimeStamp { get; set; } = null!;

    public int? OldJobContractId { get; set; }

    public DateTime? StartDate { get; set; }

    public DateTime? EndDate { get; set; }

    public virtual Job JobNumberNavigation { get; set; } = null!;

    public virtual Role Role { get; set; } = null!;

    public virtual User User { get; set; } = null!;
}
