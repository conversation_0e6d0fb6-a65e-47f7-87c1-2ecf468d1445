﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ERP.Data.Models.Dto
{
    public class RefreshSupplierAndCostsModel
    {
        public string? BomClass { get; set; }
        public int? SubdivisionId { get; set; }
        public List<int>? EstOptionIds { get; set; }
        public List<int>? EstActivityIds { get; set; }
        public List<int>? EstDetailIds { get; set; }
        public int? SupplierNumber { get; set; }
        public bool? VendorBlocked { get; set; }
        public bool? VendorNoInsurance { get; set; }
        public DateTime? SelectedDate { get; set; }
    }
}
